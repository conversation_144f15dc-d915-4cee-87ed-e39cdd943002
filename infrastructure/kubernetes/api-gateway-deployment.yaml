# 🚢 Kubernetes Deployment for API Gateway
# 
# Theo knowledge base về:
# - Container Orchestration: Kubernetes best practices
# - High Availability: Multiple replicas, rolling updates
# - Security: Pod Security Standards, resource limits
# - Observability: Health checks, monitoring labels

apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: enterprise-platform
  labels:
    app: api-gateway
    component: gateway
    tier: api
    version: v1.0.0
    managed-by: kubernetes
  annotations:
    deployment.kubernetes.io/revision: "1"
    kubernetes.io/change-cause: "Initial deployment"
spec:
  # 📈 Scaling Configuration
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  
  selector:
    matchLabels:
      app: api-gateway
      component: gateway
  
  template:
    metadata:
      labels:
        app: api-gateway
        component: gateway
        tier: api
        version: v1.0.0
      annotations:
        # 📊 Prometheus monitoring
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
        # 🔄 Force pod restart on config changes
        kubectl.kubernetes.io/restartedAt: ""
    
    spec:
      # 🔒 Security Context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      
      # 🚀 Container Specification
      containers:
      - name: api-gateway
        image: api-gateway:1.0.0
        imagePullPolicy: IfNotPresent
        
        # 🔒 Container Security
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          capabilities:
            drop:
            - ALL
        
        # 🌐 Port Configuration
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        
        # 🌍 Environment Variables
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: TZ
          value: "UTC"
        
        # 🔐 Environment from ConfigMap & Secrets
        envFrom:
        - configMapRef:
            name: api-gateway-config
        - secretRef:
            name: api-gateway-secrets
        
        # 💾 Resource Limits & Requests
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        
        # 🏥 Health Checks
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        
        startupProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 30
        
        # 📁 Volume Mounts
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: logs
          mountPath: /app/logs
        - name: cache
          mountPath: /app/.cache
      
      # 📁 Volumes
      volumes:
      - name: tmp
        emptyDir: {}
      - name: logs
        emptyDir: {}
      - name: cache
        emptyDir: {}
      
      # 🔄 Restart Policy
      restartPolicy: Always
      
      # ⏱️ Termination Grace Period
      terminationGracePeriodSeconds: 30
      
      # 🏷️ Node Selection
      nodeSelector:
        kubernetes.io/os: linux
      
      # 🎯 Pod Anti-Affinity (High Availability)
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - api-gateway
              topologyKey: kubernetes.io/hostname
      
      # 🚫 Tolerations
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
# 🌐 Service Configuration
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-service
  namespace: enterprise-platform
  labels:
    app: api-gateway
    component: gateway
    tier: api
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
spec:
  type: LoadBalancer
  selector:
    app: api-gateway
    component: gateway
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  sessionAffinity: None

---
# 📊 Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: enterprise-platform
  labels:
    app: api-gateway
    component: gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# 🔧 ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-config
  namespace: enterprise-platform
  labels:
    app: api-gateway
    component: gateway
data:
  # Application Configuration
  LOG_LEVEL: "info"
  CORS_ORIGIN: "https://app.enterprise.com"
  API_PREFIX: "api/v1"
  
  # Rate Limiting
  RATE_LIMIT_WINDOW: "900000"  # 15 minutes
  RATE_LIMIT_MAX: "1000"       # requests per window
  
  # Cache Configuration
  CACHE_TTL: "300"             # 5 minutes
  CACHE_MAX_ITEMS: "1000"
  
  # Health Check Configuration
  HEALTH_CHECK_TIMEOUT: "5000"
  HEALTH_CHECK_INTERVAL: "30000"

---
# 🔐 Secret (Template - Values should be base64 encoded)
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-secrets
  namespace: enterprise-platform
  labels:
    app: api-gateway
    component: gateway
type: Opaque
data:
  # Database Connection
  DATABASE_URL: ""  # base64 encoded connection string
  
  # Redis Connection
  REDIS_URL: ""     # base64 encoded connection string
  
  # JWT Configuration
  JWT_SECRET: ""    # base64 encoded secret
  JWT_EXPIRES_IN: "MjRo"  # "24h" in base64
  
  # External API Keys
  EXTERNAL_API_KEY: ""    # base64 encoded API key

# ================================
# 🎯 Deployment Commands:
# ================================

# Apply all resources:
# kubectl apply -f api-gateway-deployment.yaml

# Check deployment status:
# kubectl get deployment api-gateway -n enterprise-platform

# View pods:
# kubectl get pods -l app=api-gateway -n enterprise-platform

# Check HPA status:
# kubectl get hpa api-gateway-hpa -n enterprise-platform

# View logs:
# kubectl logs -l app=api-gateway -n enterprise-platform --tail=100

# Scale manually:
# kubectl scale deployment api-gateway --replicas=5 -n enterprise-platform

# ================================
# 🔑 Key Features:
# ================================

# ✅ High Availability (3+ replicas, anti-affinity)
# ✅ Auto-scaling (HPA based on CPU/Memory)
# ✅ Rolling updates (zero-downtime deployments)
# ✅ Security (Pod Security Standards, non-root)
# ✅ Health checks (liveness, readiness, startup)
# ✅ Resource management (requests/limits)
# ✅ Configuration management (ConfigMap/Secret)
# ✅ Monitoring ready (Prometheus annotations)
# ✅ Load balancing (Service with LoadBalancer)
# ✅ Graceful shutdown (terminationGracePeriod)
