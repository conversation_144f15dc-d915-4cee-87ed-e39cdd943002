# 🐳 Multi-stage Dockerfile for API Gateway - Production Optimized
# 
# Theo knowledge base về:
# - Container Security: Non-root user, minimal attack surface
# - Performance: Multi-stage builds, layer caching
# - Best Practices: Explicit versions, health checks

# ================================
# 🏗️ Build Stage
# ================================
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./

# Install dependencies
RUN npm ci --only=production --silent && \
    npm cache clean --force

# Copy source code
COPY src/ ./src/

# Build application
RUN npm run build

# ================================
# 🚀 Production Stage
# ================================
FROM node:18-alpine AS production

# Metadata labels
LABEL maintainer="Enterprise Platform Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="Enterprise API Gateway - NestJS Application"

# Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./

# Create logs directory
RUN mkdir -p /app/logs && \
    chown -R nestjs:nodejs /app/logs

# Switch to non-root user
USER nestjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start application
CMD ["node", "dist/main.js"]

# ================================
# 🔧 Development Stage (Optional)
# ================================
FROM node:18-alpine AS development

WORKDIR /app

# Install development dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./

# Install all dependencies (including dev)
RUN npm ci --silent

# Copy source code
COPY src/ ./src/

# Expose port
EXPOSE 3000
EXPOSE 9229

# Development command with debugging
CMD ["npm", "run", "start:debug"]

# ================================
# 📊 Build Arguments & Environment
# ================================

# Build arguments
ARG NODE_ENV=production
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

# Environment variables
ENV NODE_ENV=${NODE_ENV}
ENV PORT=3000
ENV TZ=UTC

# Additional metadata
LABEL org.opencontainers.image.created=${BUILD_DATE}
LABEL org.opencontainers.image.revision=${VCS_REF}
LABEL org.opencontainers.image.version=${VERSION}
LABEL org.opencontainers.image.source="https://github.com/enterprise-platform/api-gateway"

# ================================
# 🎯 Usage Examples:
# ================================

# Build production image:
# docker build --target production -t api-gateway:latest .

# Build development image:
# docker build --target development -t api-gateway:dev .

# Run production container:
# docker run -p 3000:3000 \
#   -e NODE_ENV=production \
#   -e DATABASE_URL=postgresql://... \
#   -e REDIS_URL=redis://... \
#   api-gateway:latest

# Run development container with volume mount:
# docker run -p 3000:3000 -p 9229:9229 \
#   -v $(pwd)/src:/app/src \
#   -e NODE_ENV=development \
#   api-gateway:dev

# ================================
# 🔒 Security Features:
# ================================

# ✅ Non-root user execution
# ✅ Minimal base image (Alpine)
# ✅ Security updates installed
# ✅ No unnecessary packages
# ✅ Proper signal handling with dumb-init
# ✅ Health check endpoint
# ✅ Explicit port exposure
# ✅ Layer optimization for caching

# ================================
# 🚀 Performance Optimizations:
# ================================

# ✅ Multi-stage build (smaller final image)
# ✅ npm ci for faster, reliable installs
# ✅ Cache cleaning after installs
# ✅ Layer ordering for better caching
# ✅ Production-only dependencies in final stage
# ✅ Proper working directory structure

# ================================
# 📈 Monitoring & Observability:
# ================================

# ✅ Health check configuration
# ✅ Proper logging directory
# ✅ Signal handling for graceful shutdown
# ✅ Metadata labels for tracking
# ✅ Environment variable configuration
