"use strict";
/**
 * Digital Differential Analyzer (DDA) Line Drawing Algorithm
 * Calculates the points on a line between (x1, y1) and (x2, y2).
 * Returns an array of points [{x, y}].
 */
function ddaLine(x1, y1, x2, y2) {
  if ([x1, y1, x2, y2].some(c => typeof c !== 'number' || !Number.isFinite(c))) {
    throw new TypeError('All coordinates must be finite numbers.');
  }

  const dx = x2 - x1;
  const dy = y2 - y1;
  const steps = Math.abs(dx) > Math.abs(dy) ? Math.abs(dx) : Math.abs(dy);

  const xIncrement = dx / steps;
  const yIncrement = dy / steps;

  const points = [];
  let x = x1;
  let y = y1;

  for (let i = 0; i <= steps; i++) {
    points.push({ x: Math.round(x), y: Math.round(y) });
    x += xIncrement;
    y += yIncrement;
  }

  return points;
}

module.exports = ddaLine;

if (require.main === module) {
  const linePoints = ddaLine(2, 3, 8, 7);
  console.log('Points on the line from (2,3) to (8,7):');
  console.log(linePoints);
}

