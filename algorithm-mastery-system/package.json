{"name": "algorithm-mastery-system", "version": "1.0.0", "description": "Ultimate framework để master 196 bài thuật toán với triết lý tư duy tối thượng", "main": "tools/enhance_algorithm.js", "scripts": {"start": "node tools/enhance_algorithm.js", "enhance": "node tools/enhance_algorithm.js", "enhance:array": "node tools/enhance_algorithm.js --category=array", "enhance:string": "node tools/enhance_algorithm.js --category=string", "enhance:tree": "node tools/enhance_algorithm.js --category=tree", "enhance:dp": "node tools/enhance_algorithm.js --category=dp", "test": "node tools/test_enhancement.js", "test:performance": "node tools/performance_tests.js", "validate": "node tools/validate_enhancements.js", "progress": "node tools/progress_tracker.js", "report": "node tools/generate_report.js", "setup": "node tools/setup_environment.js", "clean": "rm -rf enhanced-algorithms && mkdir enhanced-algorithms", "demo": "node examples/demo_two_sum.js"}, "keywords": ["algorithm", "data-structure", "interview", "coding", "leetcode", "programming", "mastery", "framework", "analysis", "patterns"], "author": "Algorithm Mastery Team", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "commander": "^9.4.1", "fs-extra": "^10.1.0", "lodash": "^4.17.21", "moment": "^2.29.4"}, "devDependencies": {"jest": "^29.3.1", "eslint": "^8.28.0", "prettier": "^2.8.0"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/algorithm-mastery/196-bai-code-interview"}, "bugs": {"url": "https://github.com/algorithm-mastery/196-bai-code-interview/issues"}, "homepage": "https://github.com/algorithm-mastery/196-bai-code-interview#readme", "config": {"enhancement": {"outputDir": "enhanced-algorithms", "inputDir": "../196-bai-code-interview", "logLevel": "info", "generateSummary": true, "validateOutput": true}, "patterns": {"confidenceThreshold": 70, "maxPatternsPerProblem": 5, "enableAutoDetection": true}, "testing": {"enablePerformanceTests": true, "enableStressTests": false, "timeoutMs": 5000}}, "bin": {"algorithm-enhance": "./tools/enhance_algorithm.js", "algo-enhance": "./tools/enhance_algorithm.js"}}