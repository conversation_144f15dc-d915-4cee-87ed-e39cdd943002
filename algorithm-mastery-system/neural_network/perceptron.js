"use strict";
/**
 * Perceptron
 * A simple single-layer perceptron for binary classification.
 */
class Perceptron {
  constructor(learningRate = 0.1) {
    this.weights = [];
    this.bias = 0;
    this.learningRate = learningRate;
  }

  // Activation function (Heaviside step function)
  activate(sum) {
    return sum >= 0 ? 1 : 0;
  }

  // Predict the output for a given input
  predict(inputs) {
    if (this.weights.length === 0) {
      this.weights = new Array(inputs.length).fill(0).map(() => Math.random() * 2 - 1);
    }
    if (inputs.length !== this.weights.length) {
      throw new Error('Input size does not match weight size.');
    }
    const sum = inputs.reduce((acc, input, i) => acc + input * this.weights[i], this.bias);
    return this.activate(sum);
  }

  // Train the perceptron
  train(trainingData, epochs) {
    for (let epoch = 0; epoch < epochs; epoch++) {
      for (const data of trainingData) {
        const prediction = this.predict(data.inputs);
        const error = data.label - prediction;
        if (error !== 0) {
          for (let i = 0; i < this.weights.length; i++) {
            this.weights[i] += this.learningRate * error * data.inputs[i];
          }
          this.bias += this.learningRate * error;
        }
      }
    }
  }
}

module.exports = Perceptron;

if (require.main === module) {
  // Training data for AND gate
  const trainingData = [
    { inputs: [0, 0], label: 0 },
    { inputs: [0, 1], label: 0 },
    { inputs: [1, 0], label: 0 },
    { inputs: [1, 1], label: 1 },
  ];

  const p = new Perceptron(0.1);
  p.train(trainingData, 100);

  console.log('Testing Perceptron for AND gate:');
  console.log('0 AND 0:', p.predict([0, 0])); // Expected: 0
  console.log('0 AND 1:', p.predict([0, 1])); // Expected: 0
  console.log('1 AND 0:', p.predict([1, 0])); // Expected: 0
  console.log('1 AND 1:', p.predict([1, 1])); // Expected: 1
}

