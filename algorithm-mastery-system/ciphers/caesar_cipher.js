"use strict";
/**
 * Caesar Cipher (Shift Cipher)
 * Simple substitution cipher that shifts letters by k positions.
 * Only shifts A-Z and a-z; other characters are left unchanged.
 */
function encrypt(text, k) {
  if (typeof text !== "string") throw new TypeError("encrypt: text must be a string");
  if (!Number.isInteger(k)) throw new TypeError("encrypt: k must be an integer");
  return [...text].map(ch => shiftChar(ch, k)).join("");
}

function decrypt(text, k) {
  return encrypt(text, -k);
}

function shiftChar(ch, k) {
  const code = ch.charCodeAt(0);
  if (code >= 65 && code <= 90) { // A-Z
    const base = 65;
    return String.fromCharCode(((code - base + (k % 26) + 26) % 26) + base);
  }
  if (code >= 97 && code <= 122) { // a-z
    const base = 97;
    return String.fromCharCode(((code - base + (k % 26) + 26) % 26) + base);
  }
  return ch;
}

module.exports = { encrypt, decrypt };

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const ct = encrypt("Hello, World!", 3);
  assert(ct === "Khoor, Zruog!", ct);
  assert(decrypt(ct, 3) === "Hello, World!");
  console.log("caesar_cipher.js tests passed");
}

