Backtracking (JavaScript)

Representative backtracking algorithms that explore solution spaces by incrementally building candidates and abandoning those that fail constraints.

Included algorithms
- n_queens.js – Place N queens on an N×N chessboard so no two attack each other

Usage
- node n_queens.js
- const { solveNQueens } = require('./n_queens');

Notes
- Solutions are returned as arrays of strings where 'Q' marks a queen and '.' an empty square.
- Complexity is exponential in N; pruning improves performance.

