"use strict";
/**
 * N-Queens (Backtracking)
 * Place N queens on an N×N board so that no two queens attack each other.
 * Returns all solutions as arrays of strings with 'Q' and '.'.
 * Time: exponential; Space: O(N)
 */
function solveNQueens(n) {
  if (!Number.isInteger(n) || n <= 0) throw new TypeError("n must be a positive integer");
  const cols = new Set();
  const diag1 = new Set(); // r - c
  const diag2 = new Set(); // r + c
  const board = Array.from({ length: n }, () => new Array(n).fill('.'));
  const res = [];

  function backtrack(r) {
    if (r === n) {
      res.push(board.map(row => row.join('')));
      return;
    }
    for (let c = 0; c < n; c++) {
      const d1 = r - c, d2 = r + c;
      if (cols.has(c) || diag1.has(d1) || diag2.has(d2)) continue;
      cols.add(c); diag1.add(d1); diag2.add(d2);
      board[r][c] = 'Q';
      backtrack(r + 1);
      board[r][c] = '.';
      cols.delete(c); diag1.delete(d1); diag2.delete(d2);
    }
  }
  backtrack(0);
  return res;
}

module.exports = { solveNQueens };

if (require.main === module) {
  const sols4 = solveNQueens(4);
  console.log(`Solutions for N=4: ${sols4.length}`); // 2
  console.log(sols4.map(s => s.join('\n')).join('\n\n'));
}

