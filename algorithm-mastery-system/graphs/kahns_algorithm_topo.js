"use strict";
/**
 * <PERSON>'s Algorithm for Topological Sorting of a Directed Acyclic Graph (DAG)
 *
 * Input graph formats supported:
 * - Map<Node, Array<Node>> adjacency list
 * - Plain object: { node: [neighbors...] }
 *
 * Returns an array of nodes in topological order.
 * If the graph has a cycle, throws an Error.
 */
function kahnsTopologicalSort(graph) {
  const adj = toMap(graph);
  // initialize indegree for all nodes
  const indeg = new Map();
  for (const u of adj.keys()) indeg.set(u, 0);
  for (const [u, neighbors] of adj.entries()) {
    for (const v of neighbors || []) {
      if (!indeg.has(v)) indeg.set(v, 0);
      indeg.set(v, indeg.get(v) + 1);
    }
  }

  // queue of nodes with indegree 0
  const q = [];
  for (const [node, d] of indeg.entries()) if (d === 0) q.push(node);

  const order = [];
  while (q.length) {
    const u = q.shift();
    order.push(u);
    for (const v of adj.get(u) || []) {
      indeg.set(v, indeg.get(v) - 1);
      if (indeg.get(v) === 0) q.push(v);
    }
  }

  if (order.length !== indeg.size) throw new Error("Graph contains a cycle; topological order does not exist");
  return order;
}

function toMap(graph) {
  if (graph instanceof Map) return graph;
  if (typeof graph === "object" && graph !== null) {
    const m = new Map();
    for (const k of Object.keys(graph)) m.set(k, graph[k]);
    return m;
  }
  throw new TypeError("Graph must be Map or plain object");
}

module.exports = kahnsTopologicalSort;

// Inline example/tests
if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const g = {
    A: ["C"],
    B: ["C", "D"],
    C: ["E"],
    D: ["F"],
    E: ["H", "F"],
    F: ["G"],
    G: [],
    H: []
  };
  const order = kahnsTopologicalSort(g);
  // A or B can come first; validate constraints instead
  const pos = Object.fromEntries(order.map((x, i) => [x, i]));
  const edges = [
    ["A","C"],["B","C"],["B","D"],["C","E"],["D","F"],["E","H"],["E","F"],["F","G"]
  ];
  for (const [u,v] of edges) assert(pos[u] < pos[v], `${u} should come before ${v}`);
  console.log("kahns_algorithm_topo.js tests passed");
}

