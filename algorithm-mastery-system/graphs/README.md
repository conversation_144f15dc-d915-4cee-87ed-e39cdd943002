Graphs (JavaScript)

This folder mirrors TheAlgorithms/Python graphs category with JavaScript implementations. Each file exports a function and includes inline examples/tests.

Included algorithms (initial set)
- breadth_first_search.js – BFS traversal from a source (returns order and optionally distances)
- depth_first_search.js – DFS traversal recursive/iterative
- dijkstra.js – Single-source shortest paths on non-negative weighted graphs
- kahns_algorithm_topo.js – <PERSON>'s algorithm for topological sorting of DAGs

Graph representation
- We use adjacency lists: { nodeId: Array<{to, weight?}> } or Map equivalents.
- For unweighted graphs, omit weight or use 1.

Usage
- node breadth_first_search.js
- const dijkstra = require('./dijkstra');

Notes
- Implementations aim for clarity and include basic validation. No external dependencies.

