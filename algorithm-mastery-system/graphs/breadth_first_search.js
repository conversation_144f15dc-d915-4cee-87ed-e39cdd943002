"use strict";
/**
 * Breadth-First Search (BFS)
 * Traverses an unweighted graph from a source node. Returns traversal order and distances.
 * Graph representation: adjacency list as Map<any, any[]> or object {node: [neighbors...]}
 *
 * @param {Map<any, any[]>|Object<string, any[]>} graph
 * @param {any} source
 * @returns {{order:any[], distance: Map<any, number>}}
 */
function breadthFirstSearch(graph, source) {
  const adj = toMap(graph);
  if (!adj.has(source)) throw new Error("BFS: source not in graph");
  const visited = new Set([source]);
  const distance = new Map([[source, 0]]);
  const q = [source];
  const order = [];
  while (q.length) {
    const u = q.shift();
    order.push(u);
    for (const v of adj.get(u) || []) {
      if (!visited.has(v)) {
        visited.add(v);
        distance.set(v, distance.get(u) + 1);
        q.push(v);
      }
    }
  }
  return { order, distance };
}

function toMap(graph) {
  if (graph instanceof Map) return graph;
  if (typeof graph === "object" && graph !== null) {
    const m = new Map();
    for (const k of Object.keys(graph)) m.set(k, graph[k]);
    return m;
  }
  throw new TypeError("Graph must be Map or plain object");
}

module.exports = breadthFirstSearch;

if (require.main === module) {
  const g = { A: ["B", "C"], B: ["D"], C: ["D"], D: [] };
  const { order, distance } = breadthFirstSearch(g, "A");
  console.log(order); // [ 'A', 'B', 'C', 'D' ]
  console.log(distance.get("D")); // 2
}

