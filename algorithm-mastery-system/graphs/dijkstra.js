"use strict";
/**
 * <PERSON><PERSON><PERSON>'s algorithm (single-source shortest paths for non-negative weights)
 * Graph can be:
 * - Map<Node, Array<{to: Node, weight: number}>>
 * - Plain object: { node: Array<{to, weight}> }
 * - Or for unweighted, neighbors as Node[] (weight assumed 1)
 *
 * Returns { distance: Map<Node, number>, previous: Map<Node, Node|null> }
 */

function dijkstra(graph, source) {
  const adj = normalizeGraph(graph);
  if (!adj.has(source)) throw new Error("dijkstra: source not in graph");

  const dist = new Map();
  const prev = new Map();
  const pq = new MinHeap((a, b) => a.dist - b.dist); // elements: {node, dist}

  for (const node of adj.keys()) {
    dist.set(node, Infinity);
    prev.set(node, null);
  }
  dist.set(source, 0);
  pq.push({ node: source, dist: 0 });

  while (!pq.isEmpty()) {
    const { node: u, dist: d } = pq.pop();
    if (d !== dist.get(u)) continue; // stale entry
    for (const edge of adj.get(u)) {
      const v = edge.to;
      const w = edge.weight;
      if (w < 0) throw new Error("dijkstra: negative edge weight detected");
      const nd = d + w;
      if (nd < dist.get(v)) {
        dist.set(v, nd);
        prev.set(v, u);
        pq.push({ node: v, dist: nd });
      }
    }
  }

  return { distance: dist, previous: prev };
}

function normalizeGraph(graph) {
  // Convert supported graph formats into Map<Node, Array<{to, weight}>>
  if (graph instanceof Map) {
    const m = new Map();
    for (const [u, neighbors] of graph.entries()) {
      const list = [];
      for (const nb of neighbors || []) {
        if (typeof nb === "object" && nb && "to" in nb) list.push({ to: nb.to, weight: nb.weight ?? 1 });
        else list.push({ to: nb, weight: 1 });
      }
      m.set(u, list);
    }
    return m;
  }
  if (typeof graph === "object" && graph !== null) {
    const m = new Map();
    for (const k of Object.keys(graph)) {
      const neighbors = graph[k];
      const list = [];
      for (const nb of neighbors || []) {
        if (typeof nb === "object" && nb && "to" in nb) list.push({ to: nb.to, weight: nb.weight ?? 1 });
        else list.push({ to: nb, weight: 1 });
      }
      m.set(k, list);
    }
    return m;
  }
  throw new TypeError("Graph must be Map or plain object");
}

class MinHeap {
  constructor(cmp = (a, b) => a - b) { this._a = []; this._cmp = cmp; }
  size() { return this._a.length; }
  isEmpty() { return this.size() === 0; }
  push(x) { this._a.push(x); this._siftUp(this.size() - 1); }
  pop() {
    if (this.isEmpty()) throw new Error("heap underflow");
    const a = this._a; const top = a[0];
    const last = a.pop();
    if (a.length) { a[0] = last; this._siftDown(0); }
    return top;
  }
  _siftUp(i) {
    const a = this._a, cmp = this._cmp; let p;
    while (i > 0 && cmp(a[i], a[p = ((i - 1) >> 1)]) < 0) { [a[i], a[p]] = [a[p], a[i]]; i = p; }
  }
  _siftDown(i) {
    const a = this._a, cmp = this._cmp; const n = a.length;
    while (true) {
      let l = i * 2 + 1, r = l + 1, s = i;
      if (l < n && cmp(a[l], a[s]) < 0) s = l;
      if (r < n && cmp(a[r], a[s]) < 0) s = r;
      if (s === i) break; [a[i], a[s]] = [a[s], a[i]]; i = s;
    }
  }
}

module.exports = dijkstra;

if (require.main === module) {
  const g = {
    A: [{ to: "B", weight: 1 }, { to: "C", weight: 4 }],
    B: [{ to: "C", weight: 2 }, { to: "D", weight: 5 }],
    C: [{ to: "D", weight: 1 }],
    D: [],
  };
  const { distance, previous } = dijkstra(g, "A");
  console.log("dist to D:", distance.get("D")); // 1+2+1 = 4
  // reconstruct path A->B->C->D
  const path = [];
  for (let u = "D"; u != null; u = previous.get(u)) path.push(u);
  console.log(path.reverse());
}

