"use strict";
/**
 * Depth-First Search (DFS)
 * Traverses a graph from a source node. Returns traversal order.
 * Provides both recursive and iterative variants.
 * Graph representation: adjacency list as Map<any, any[]> or object {node: [neighbors...]}
 */

function depthFirstSearchRecursive(graph, source) {
  const adj = toMap(graph);
  if (!adj.has(source)) throw new Error("DFS: source not in graph");
  const visited = new Set();
  const order = [];
  (function dfs(u) {
    visited.add(u);
    order.push(u);
    for (const v of adj.get(u) || []) if (!visited.has(v)) dfs(v);
  })(source);
  return order;
}

function depthFirstSearchIterative(graph, source) {
  const adj = toMap(graph);
  if (!adj.has(source)) throw new Error("DFS: source not in graph");
  const visited = new Set();
  const order = [];
  const stack = [source];
  while (stack.length) {
    const u = stack.pop();
    if (visited.has(u)) continue;
    visited.add(u);
    order.push(u);
    const neighbors = adj.get(u) || [];
    for (let i = neighbors.length - 1; i >= 0; i--) {
      const v = neighbors[i];
      if (!visited.has(v)) stack.push(v);
    }
  }
  return order;
}

function toMap(graph) {
  if (graph instanceof Map) return graph;
  if (typeof graph === "object" && graph !== null) {
    const m = new Map();
    for (const k of Object.keys(graph)) m.set(k, graph[k]);
    return m;
  }
  throw new TypeError("Graph must be Map or plain object");
}

module.exports = { depthFirstSearchRecursive, depthFirstSearchIterative };

if (require.main === module) {
  const g = { A: ["B", "C"], B: ["D"], C: ["D"], D: [] };
  console.log(depthFirstSearchRecursive(g, "A")); // e.g., [ 'A', 'B', 'D', 'C' ]
  console.log(depthFirstSearchIterative(g, "A"));
}

