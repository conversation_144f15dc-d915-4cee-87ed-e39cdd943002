"use strict";
/**
 * Tower of Hanoi (Recursion)
 * Move n disks from source to target using auxiliary.
 * Returns array of moves as pairs [from, to].
 * Time: O(2^n)  Space: O(n)
 */
function towerOfHanoi(n, source, target, auxiliary) {
  if (!Number.isInteger(n) || n <= 0) throw new TypeError("n must be a positive integer");
  if (![source, target, auxiliary].every(x => typeof x === "string" && x.length > 0)) {
    throw new TypeError("pegs must be non-empty strings");
  }
  const moves = [];
  function move(k, s, t, a) {
    if (k === 1) { moves.push([s, t]); return; }
    move(k - 1, s, a, t);
    moves.push([s, t]);
    move(k - 1, a, t, s);
  }
  move(n, source, target, auxiliary);
  return moves;
}

module.exports = towerOfHanoi;

if (require.main === module) {
  const m = towerOfHanoi(3, 'A', 'C', 'B');
  console.log(m); // 7 moves
}

