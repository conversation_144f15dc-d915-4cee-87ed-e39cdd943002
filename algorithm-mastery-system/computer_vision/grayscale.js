"use strict";
/**
 * Converts an RGB pixel to grayscale using the luminosity method.
 * Grayscale = 0.299*R + 0.587*G + 0.114*B
 * Input: pixel object {r, g, b} with values in [0, 255]
 * Output: integer grayscale value in [0, 255]
 */
function toGrayscale(pixel) {
  if (!pixel || typeof pixel.r !== 'number' || typeof pixel.g !== 'number' || typeof pixel.b !== 'number') {
    throw new TypeError("Input must be an object {r, g, b} with number values.");
  }
  const { r, g, b } = pixel;
  if ([r, g, b].some(c => c < 0 || c > 255)) {
    throw new RangeError("RGB values must be between 0 and 255.");
  }
  return Math.round(0.299 * r + 0.587 * g + 0.114 * b);
}

module.exports = toGrayscale;

if (require.main === module) {
  const pixel = { r: 150, g: 100, b: 200 };
  const gray = toGrayscale(pixel);
  console.log(`Grayscale value for (${pixel.r},${pixel.g},${pixel.b}) is ${gray}`); // ~126
}

