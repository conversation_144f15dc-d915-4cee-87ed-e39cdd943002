Strings (JavaScript)

This folder mirrors TheAlgorithms/Python strings category with JavaScript implementations. Each file exports a function and includes inline examples/tests.

Included algorithms (initial set)
- knuth_morris_pratt.js – KMP substring search using prefix function
- rabin_karp.js – Rolling hash substring search

Usage
- node knuth_morris_pratt.js
- const rabinKarp = require('./rabin_karp');

Notes
- Both return the first index of the pattern in the text or -1 if not found; helper variants also return all matches.
- Implementations focus on clarity and correctness; hashing uses safe 32-bit arithmetic.

