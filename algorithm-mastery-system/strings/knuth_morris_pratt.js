"use strict";
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> (KMP) substring search
 * Returns index of first occurrence of pattern in text or -1 if not found.
 * Also exports prefixFunction and findAll for educational purposes.
 *
 * Time: O(n + m)
 * Space: O(m)
 */

function prefixFunction(pat) {
  if (typeof pat !== "string") throw new TypeError("prefixFunction: pattern must be string");
  const n = pat.length;
  const pi = new Array(n).fill(0);
  for (let i = 1; i < n; i++) {
    let j = pi[i - 1];
    while (j > 0 && pat[i] !== pat[j]) j = pi[j - 1];
    if (pat[i] === pat[j]) j++;
    pi[i] = j;
  }
  return pi;
}

function kmp(text, pattern) {
  if (typeof text !== "string" || typeof pattern !== "string") throw new TypeError("kmp: text/pattern must be strings");
  if (pattern.length === 0) return 0;
  const pi = prefixFunction(pattern);
  let j = 0;
  for (let i = 0; i < text.length; i++) {
    while (j > 0 && text[i] !== pattern[j]) j = pi[j - 1];
    if (text[i] === pattern[j]) j++;
    if (j === pattern.length) return i - j + 1;
  }
  return -1;
}

function findAll(text, pattern) {
  if (typeof text !== "string" || typeof pattern !== "string") throw new TypeError("findAll: text/pattern must be strings");
  const res = [];
  if (pattern.length === 0) { for (let i = 0; i <= text.length; i++) res.push(i); return res; }
  const pi = prefixFunction(pattern);
  let j = 0;
  for (let i = 0; i < text.length; i++) {
    while (j > 0 && text[i] !== pattern[j]) j = pi[j - 1];
    if (text[i] === pattern[j]) j++;
    if (j === pattern.length) { res.push(i - j + 1); j = pi[j - 1]; }
  }
  return res;
}

module.exports = Object.assign(kmp, { prefixFunction, findAll });

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(kmp("abxabcabcaby", "abcaby") === 6, "KMP basic");
  assert(JSON.stringify(findAll("aaaaa", "aa")) === JSON.stringify([0,1,2,3]), "findAll overlaps");
  console.log("knuth_morris_pratt.js tests passed");
}

