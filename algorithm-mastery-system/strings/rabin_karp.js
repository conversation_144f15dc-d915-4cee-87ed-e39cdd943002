"use strict";
/**
 * <PERSON>bin-<PERSON><PERSON> substring search using rolling hash
 * Returns index of first occurrence of pattern in text or -1. Also exports findAll.
 *
 * Uses base 256 and a large prime modulus to reduce collisions. For education.
 */

const MOD = 2_147_483_647; // a large prime (2^31-1)
const BASE = 256;

function rabinKarp(text, pattern) {
  if (typeof text !== "string" || typeof pattern !== "string") throw new TypeError("rabinKarp: text/pattern must be strings");
  const n = text.length, m = pattern.length;
  if (m === 0) return 0;
  if (m > n) return -1;

  let h = 1;
  for (let i = 0; i < m - 1; i++) h = (h * BASE) % MOD; // BASE^(m-1) % MOD

  let p = 0, t = 0;
  for (let i = 0; i < m; i++) {
    p = (p * BASE + pattern.charCodeAt(i)) % MOD;
    t = (t * BASE + text.charCodeAt(i)) % MOD;
  }

  for (let i = 0; i <= n - m; i++) {
    if (p === t) {
      // potential match, verify to avoid false positives due to collisions
      if (text.substr(i, m) === pattern) return i;
    }
    if (i < n - m) {
      t = (BASE * (t - text.charCodeAt(i) * h) + text.charCodeAt(i + 1)) % MOD;
      if (t < 0) t += MOD; // ensure positive
    }
  }
  return -1;
}

function findAll(text, pattern) {
  if (typeof text !== "string" || typeof pattern !== "string") throw new TypeError("findAll: text/pattern must be strings");
  const n = text.length, m = pattern.length;
  const res = [];
  if (m === 0) { for (let i = 0; i <= n; i++) res.push(i); return res; }
  if (m > n) return res;

  let h = 1;
  for (let i = 0; i < m - 1; i++) h = (h * BASE) % MOD;

  let p = 0, t = 0;
  for (let i = 0; i < m; i++) {
    p = (p * BASE + pattern.charCodeAt(i)) % MOD;
    t = (t * BASE + text.charCodeAt(i)) % MOD;
  }

  for (let i = 0; i <= n - m; i++) {
    if (p === t && text.substr(i, m) === pattern) res.push(i);
    if (i < n - m) {
      t = (BASE * (t - text.charCodeAt(i) * h) + text.charCodeAt(i + 1)) % MOD;
      if (t < 0) t += MOD;
    }
  }
  return res;
}

module.exports = Object.assign(rabinKarp, { findAll });

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(rabinKarp("hello world", "world") === 6, "basic match");
  assert(JSON.stringify(findAll("aaaaa", "aa")) === JSON.stringify([0,1,2,3]), "findAll overlaps");
  console.log("rabin_karp.js tests passed");
}

