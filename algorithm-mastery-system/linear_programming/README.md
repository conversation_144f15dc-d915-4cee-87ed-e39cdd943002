Linear Programming (JavaScript)

This category is for algorithms that solve linear programming problems, such as the Simplex method.

Implementing the Simplex algorithm from scratch is a significant undertaking. Below is a conceptual outline. For practical use, it is highly recommended to use a dedicated, well-tested library like `glpk.js` or `javascript-lp-solver`.

### Conceptual Outline of the Simplex Algorithm

1.  **Standard Form**: Convert the linear program into standard form:
    *   Maximize an objective function.
    *   All constraints are linear equations (not inequalities).
    *   All variables are non-negative.
    *   This involves adding slack and surplus variables.

2.  **Tableau Setup**: Create the initial Simplex tableau, which is a matrix representation of the linear system.

3.  **Pivoting (Iterative Process)**:
    *   **Select Pivot Column**: Find the most negative entry in the bottom row (objective function row). This column's variable will enter the basis.
    *   **Select Pivot Row**: For each row, calculate the ratio of the right-hand side value to the entry in the pivot column. Choose the row with the smallest non-negative ratio. This row's variable will leave the basis.
    *   **Pivot Operation**: Perform row operations to make the pivot element 1 and all other entries in the pivot column 0.

4.  **Termination**: Repeat the pivoting process until there are no negative entries in the bottom row. The tableau is now optimal, and the solution can be read from it.

