"use strict";
/**
 * Activity Selection Problem (Greedy)
 * Given start and finish times of activities, select the maximum number of activities
 * that can be performed by a single person, assuming a person can only work on a single
 * activity at a time. The greedy strategy is to select activities in ascending order of finish time.
 *
 * Input formats supported:
 * - Two arrays start[] and finish[] of equal length
 * - Or an array of objects: [{start: s, finish: f}, ...]
 *
 * Output:
 * - Indices (or items) of selected activities in order of execution
 */

function activitySelection(start, finish) {
  if (Array.isArray(start) && Array.isArray(finish)) {
    if (start.length !== finish.length) throw new Error("start and finish must have same length");
    const activities = start.map((s, i) => ({ index: i, start: s, finish: finish[i] }));
    return selectActivities(activities);
  }
  if (Array.isArray(start) && finish === undefined) {
    const activities = start.map((x, i) => {
      if (typeof x !== "object" || x == null || typeof x.start !== "number" || typeof x.finish !== "number") {
        throw new TypeError("Each activity must be an object with numeric start and finish");
      }
      return { index: i, start: x.start, finish: x.finish };
    });
    return selectActivities(activities, true);
  }
  throw new TypeError("Invalid inputs. Provide (start[], finish[]) or array of {start, finish}");
}

function selectActivities(activities, returnItems = false) {
  // sort by finish time ascending
  activities.sort((a, b) => a.finish - b.finish);
  const selected = [];
  let lastFinish = -Infinity;
  for (const act of activities) {
    if (act.start >= lastFinish) {
      selected.push(returnItems ? { start: act.start, finish: act.finish, originalIndex: act.index } : act.index);
      lastFinish = act.finish;
    }
  }
  return selected;
}

module.exports = activitySelection;

// Inline examples/tests
if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const s = [1, 3, 0, 5, 8, 5];
  const f = [2, 4, 6, 7, 9, 9];
  const sel = activitySelection(s, f);
  // One optimal solution selects activities with original indices [0, 1, 3, 4]
  assert(JSON.stringify(sel) === JSON.stringify([0,1,3,4]), "expected [0,1,3,4]");

  const items = [{start:1,finish:2},{start:3,finish:4},{start:0,finish:6},{start:5,finish:7},{start:8,finish:9},{start:5,finish:9}];
  const sel2 = activitySelection(items);
  assert(sel2.length === 4, "should select 4 activities");
  console.log("activity_selection.js tests passed");
}

