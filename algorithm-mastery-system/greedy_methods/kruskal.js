"use strict";
/**
 * <PERSON><PERSON><PERSON>'s Algorithm for Minimum Spanning Tree (MST)
 *
 * Input:
 * - nodes: array of node identifiers (any primitive or stringifiable)
 * - edges: array of { u, v, weight } representing undirected weighted edges
 *
 * Output:
 * - { mst: Array<{u,v,weight}>, totalWeight: number }
 *
 * Notes:
 * - Uses Disjoint Set Union (Union-Find) with path compression and union by rank.
 * - Throws if graph is disconnected (no spanning tree) or invalid input.
 */

function kruskal(nodes, edges) {
  if (!Array.isArray(nodes) || !Array.isArray(edges)) throw new TypeError("kruskal: nodes and edges must be arrays");
  const set = new DisjointSet(nodes);
  const sorted = edges.slice().sort((a, b) => a.weight - b.weight);
  const mst = [];
  let totalWeight = 0;
  for (const e of sorted) {
    validateEdge(e);
    if (!set.has(e.u) || !set.has(e.v)) throw new Error("Edge endpoints must be in nodes set");
    if (set.find(e.u) !== set.find(e.v)) {
      set.union(e.u, e.v);
      mst.push({ u: e.u, v: e.v, weight: e.weight });
      totalWeight += e.weight;
    }
  }
  if (mst.length !== nodes.length - 1) throw new Error("Graph is not connected; MST does not exist");
  return { mst, totalWeight };
}

class DisjointSet {
  constructor(elements = []) {
    this.parent = new Map();
    this.rank = new Map();
    for (const x of elements) {
      this.parent.set(x, x);
      this.rank.set(x, 0);
    }
  }
  has(x) { return this.parent.has(x); }
  find(x) {
    if (!this.parent.has(x)) throw new Error("find: element not found in DisjointSet");
    let p = this.parent.get(x);
    if (p !== x) p = this.find(p);
    this.parent.set(x, p);
    return p;
  }
  union(a, b) {
    let ra = this.find(a), rb = this.find(b);
    if (ra === rb) return false;
    const raRank = this.rank.get(ra), rbRank = this.rank.get(rb);
    if (raRank < rbRank) [ra, rb] = [rb, ra];
    this.parent.set(rb, ra);
    if (raRank === rbRank) this.rank.set(ra, raRank + 1);
    return true;
  }
}

function validateEdge(e) {
  if (typeof e !== "object" || e == null || !("u" in e) || !("v" in e) || !("weight" in e)) {
    throw new TypeError("Edge must be object {u, v, weight}");
  }
  if (typeof e.weight !== "number" || !Number.isFinite(e.weight)) throw new TypeError("Edge weight must be a finite number");
}

module.exports = Object.assign(kruskal, { DisjointSet });

// Inline example/tests
if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const nodes = ["A","B","C","D","E","F"];
  const edges = [
    { u: "A", v: "B", weight: 4 },
    { u: "A", v: "F", weight: 2 },
    { u: "F", v: "B", weight: 5 },
    { u: "B", v: "C", weight: 6 },
    { u: "F", v: "C", weight: 1 },
    { u: "C", v: "D", weight: 3 },
    { u: "E", v: "D", weight: 4 },
    { u: "C", v: "E", weight: 2 }
  ];
  const { mst, totalWeight } = kruskal(nodes, edges);
  // Expected MST edges (weights): 1 + 2 + 2 + 3 + 4 = 12
  assert(totalWeight === 12, `MST total weight should be 12, got ${totalWeight}`);
  assert(mst.length === nodes.length - 1, "MST should have n-1 edges");
  console.log("kruskal.js tests passed");
}

