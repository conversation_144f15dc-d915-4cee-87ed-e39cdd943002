Greedy Methods (JavaScript)

Representative greedy algorithms implemented in JavaScript. These mirror classic problems in TheAlgorithms/Python.

Included algorithms
- activity_selection.js – Select maximum number of non-overlapping activities by earliest finish time
- kruskal.js – Minimum Spanning Tree with Disjoint Set Union (Union-Find)

Usage
- node activity_selection.js
- node kruskal.js

Notes
- Inputs are validated and the algorithms include inline examples/tests using the `require.main === module` pattern.

