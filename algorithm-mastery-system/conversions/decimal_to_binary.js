"use strict";
/**
 * Decimal to Binary conversion (non-negative integers)
 * Returns binary string representation without leading zeros (except for 0 -> "0").
 */
function decimalToBinary(n) {
  if (!Number.isSafeInteger(n) || n < 0) throw new TypeError("decimalToBinary: n must be a non-negative safe integer");
  if (n === 0) return "0";
  let s = "";
  while (n > 0) { s = ((n & 1) ? "1" : "0") + s; n >>>= 1; }
  return s;
}

module.exports = decimalToBinary;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(decimalToBinary(0) === "0");
  assert(decimalToBinary(5) === "101");
  assert(decimalToBinary(1023) === "1111111111");
  console.log("decimal_to_binary.js tests passed");
}

