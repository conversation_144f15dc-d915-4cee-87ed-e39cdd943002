"use strict";
/**
 * Binary to Decimal conversion
 * Accepts a string of '0' and '1' characters and returns a non-negative integer.
 */
function binaryToDecimal(s) {
  if (typeof s !== "string" || !/^[01]+$/.test(s)) throw new TypeError("binaryToDecimal: input must be a binary string");
  let n = 0;
  for (let i = 0; i < s.length; i++) {
    n = (n << 1) | (s.charCodeAt(i) & 1);
  }
  return n;
}

module.exports = binaryToDecimal;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(binaryToDecimal("0") === 0);
  assert(binaryToDecimal("101") === 5);
  assert(binaryToDecimal("1111111111") === 1023);
  console.log("binary_to_decimal.js tests passed");
}

