"use strict";
/**
 * Mandelbrot Set (Text-based)
 * Generates a character-based representation of the Mandelbrot set.
 */
function generateMandelbrot(width, height, maxIter) {
  let output = '';
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const cRe = (x - width / 2.0) * 4.0 / width;
      const cIm = (y - height / 2.0) * 4.0 / width;
      let zRe = 0, zIm = 0;
      let iter = 0;
      while (zRe * zRe + zIm * zIm < 4 && iter < maxIter) {
        const zReNew = zRe * zRe - zIm * zIm + cRe;
        zIm = 2 * zRe * zIm + cIm;
        zRe = zReNew;
        iter++;
      }
      output += iter === maxIter ? '#' : '.';
    }
    output += '\n';
  }
  return output;
}

module.exports = generateMandelbrot;

if (require.main === module) {
  const width = 80;
  const height = 24;
  const maxIter = 1000;
  console.log(`Mandelbrot Set (${width}x${height}, max ${maxIter} iterations):`);
  console.log(generateMandelbrot(width, height, maxIter));
}

