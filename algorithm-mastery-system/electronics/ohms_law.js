"use strict";
/**
 * Ohm's Law (V = I * R)
 * Provides helpers to solve for voltage, current, or resistance when the other two are known.
 * Use null or undefined for the value to be calculated.
 */
function solve({ v, i, r }) {
  const known = [v, i, r].filter(x => typeof x === "number" && Number.isFinite(x));
  if (known.length !== 2) throw new Error("Exactly two of {v, i, r} must be provided as numbers");
  if (v == null) return { v: i * r, i, r };
  if (i == null) {
    if (r === 0) throw new Error("Cannot solve for current when resistance is zero");
    return { v, i: v / r, r };
  }
  if (r == null) {
    if (i === 0) throw new Error("Cannot solve for resistance when current is zero");
    return { v, i, r: v / i };
  }
  return {v,i,r}; // all known
}

module.exports = { solve };

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const res1 = solve({ v: null, i: 2, r: 6 });
  assert(res1.v === 12, `v=${res1.v}`);
  const res2 = solve({ v: 12, i: null, r: 6 });
  assert(res2.i === 2, `i=${res2.i}`);
  const res3 = solve({ v: 12, i: 2, r: null });
  assert(res3.r === 6, `r=${res3.r}`);
  console.log("ohms_law.js tests passed");
}

