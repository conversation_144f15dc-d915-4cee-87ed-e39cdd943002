"use strict";
/**
 * Basic Fuzzy Logic Operations
 * Fuzzy logic deals with values between 0 and 1, representing degrees of truth.
 */

// Fuzzy AND (min)
function fuzzyAnd(a, b) {
  if ([a, b].some(v => typeof v !== 'number' || v < 0 || v > 1)) {
    throw new TypeError('Inputs must be numbers between 0 and 1.');
  }
  return Math.min(a, b);
}

// Fuzzy OR (max)
function fuzzyOr(a, b) {
  if ([a, b].some(v => typeof v !== 'number' || v < 0 || v > 1)) {
    throw new TypeError('Inputs must be numbers between 0 and 1.');
  }
  return Math.max(a, b);
}

// Fuzzy NOT (1 - value)
function fuzzyNot(a) {
  if (typeof a !== 'number' || a < 0 || a > 1) {
    throw new TypeError('Input must be a number between 0 and 1.');
  }
  return 1 - a;
}

module.exports = { fuzzyAnd, fuzzyOr, fuzzyNot };

if (require.main === module) {
  const a = 0.8; // e.g., "very true"
  const b = 0.3; // e.g., "somewhat false"

  console.log(`Fuzzy AND(${a}, ${b}):`, fuzzyAnd(a, b));     // 0.3
  console.log(`Fuzzy OR(${a}, ${b}):`, fuzzyOr(a, b));      // 0.8
  console.log(`Fuzzy NOT(${a}):`, fuzzyNot(a).toFixed(1)); // 0.2
}

