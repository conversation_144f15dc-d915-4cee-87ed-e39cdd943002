Searches (JavaScript)

This folder mirrors TheAlgorithms/Python searches category with JavaScript implementations. Each file exports a function and includes inline example usage/tests under `if (require.main === module) { ... }`.

Included algorithms
- linear_search.js – O(n) sequential scan returning index or -1.
- binary_search.js – O(log n) on a sorted array; returns index or -1.

Usage
- Run any file directly with Node to see example output:
  node linear_search.js
- Or import in your code:
  const linearSearch = require('./linear_search');

Notes
- binary_search.js expects a sorted array based on the comparator used.
- All functions validate inputs and avoid mutating the provided arrays.

