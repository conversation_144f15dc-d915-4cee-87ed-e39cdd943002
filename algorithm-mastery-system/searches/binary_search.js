"use strict";
/**
 * Binary Search (O(log n))
 * Works on sorted arrays. Returns index of target or -1.
 *
 * @template T
 * @param {T[]} arr - Sorted array
 * @param {T} target
 * @param {(a:T,b:T)=>number} [cmp] - Comparator consistent with array ordering
 * @returns {number}
 */
function binarySearch(arr, target, cmp = defaultComparator) {
  if (!Array.isArray(arr)) throw new TypeError("binarySearch: arr must be an array");
  let lo = 0, hi = arr.length - 1;
  while (lo <= hi) {
    const mid = lo + ((hi - lo) >> 1);
    const c = cmp(arr[mid], target);
    if (c === 0) return mid;
    if (c < 0) lo = mid + 1; else hi = mid - 1;
  }
  return -1;
}

function defaultComparator(a, b) { return a === b ? 0 : a < b ? -1 : 1; }

module.exports = binarySearch;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const arr = [1, 3, 5, 7, 9, 11];
  assert(binarySearch(arr, 7) === 3, "should find 7 at index 3");
  assert(binarySearch(arr, 6) === -1, "6 is not present");
  console.log("binary_search.js tests passed");
}

