"use strict";
/**
 * Linear Search (O(n))
 * Scans the array sequentially and returns the index of the first match, or -1 if not found.
 *
 * @template T
 * @param {T[]} arr
 * @param {T} target
 * @param {(a:T, b:T)=>boolean} [equals] - Optional equality predicate
 * @returns {number}
 */
function linearSearch(arr, target, equals = (a, b) => a === b) {
  if (!Array.isArray(arr)) throw new TypeError("linearSearch: arr must be an array");
  for (let i = 0; i < arr.length; i++) {
    if (equals(arr[i], target)) return i;
  }
  return -1;
}

module.exports = linearSearch;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(linearSearch([1,2,3,4], 3) === 2, "should find index 2");
  assert(linearSearch([1,2,3,4], 5) === -1, "should not find 5");
  console.log("linear_search.js tests passed");
}

