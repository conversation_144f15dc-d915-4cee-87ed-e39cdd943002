File Transfer (JavaScript)

This category is for algorithms and protocols related to file transfer over a network.

Due to the complexity of setting up network sockets and I/O for a simple demonstration, this section contains a conceptual overview rather than runnable code. In a real-world Node.js application, one would use built-in modules like `net` (for TCP sockets), `dgram` (for UDP), or higher-level libraries like `express` (for HTTP-based transfers).

Conceptual Example (TCP Server/Client):

**Server**
```javascript
const net = require('net');
const fs = require('fs');

const server = net.createServer(socket => {
  const fileStream = fs.createReadStream('file_to_send.txt');
  fileStream.pipe(socket);
  socket.on('end', () => console.log('File sent.'));
});

server.listen(3000, () => console.log('Server listening on port 3000'));
```

**Client**
```javascript
const net = require('net');
const fs = require('fs');

const socket = net.connect(3000, 'localhost', () => {
  const fileStream = fs.createWriteStream('received_file.txt');
  socket.pipe(fileStream);
});

socket.on('end', () => console.log('File received.'));
```

