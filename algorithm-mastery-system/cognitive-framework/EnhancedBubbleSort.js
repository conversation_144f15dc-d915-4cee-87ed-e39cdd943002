/**
 * Enhanced Bubble Sort with Structured Thinking Framework
 * Demonstrates integration of Vietnamese cognitive frameworks:
 * - 5W1H Problem Analysis
 * - Computational Thinking (Decomposition, Pattern Recognition, Abstraction, Algorithm Design)
 * - Polya's 4-Step Method
 * - Metacognitive Monitoring
 * - Design Thinking Principles
 * 
 * Based on: "Nền <PERSON>"
 */

const { StructuredThinkingFramework, MetacognitiveMonitor } = require('./StructuredThinking');

class EnhancedBubbleSort {
  constructor() {
    this.cognitiveFramework = new StructuredThinkingFramework();
    this.monitor = new MetacognitiveMonitor();
    this.problemAnalysis = null;
    this.solutionPlan = null;
  }

  /**
   * Comprehensive problem analysis using 5W1H Framework
   * Implements the Vietnamese framework for systematic problem understanding
   */
  analyzeProblem() {
    console.log("🔍 Phase 1: Comprehensive Problem Analysis (5W1H Framework)");
    
    const problem = {
      description: "Implement bubble sort algorithm with educational focus",
      input: "Array of comparable elements (numbers, strings, objects)",
      output: "Array sorted in ascending order by default",
      constraints: "In-place sorting, stable algorithm, educational clarity preferred over efficiency",
      importance: "Foundation for understanding comparison-based sorting and algorithm optimization",
      applications: "Small datasets, educational contexts, understanding O(n²) complexity behavior",
      learningValue: "Demonstrates nested loops, array manipulation, optimization techniques",
      businessValue: "Teaching algorithmic thinking, interview preparation, performance analysis",
      endUsers: "Students learning algorithms, developers in educational contexts",
      beneficiaries: "Learning management systems, coding bootcamps, algorithm visualization tools",
      stakeholders: "Educators, students, technical interviewers",
      useCase: "Algorithm education, complexity analysis demonstration, optimization teaching",
      timeComplexity: "O(n²) worst and average case, O(n) best case with optimization",
      scalabilityNeeds: "Suitable for arrays up to ~1000 elements for educational purposes",
      context: "Educational programming environments, algorithm learning platforms",
      environment: "JavaScript runtime with console output capabilities",
      environmentalConstraints: "Memory limitations for large arrays, visualization needs",
      approaches: ["Basic nested loops", "Optimized with early termination", "Bidirectional bubble sort"],
      recommendedApproach: "Optimized with early termination and progress monitoring",
      implementation: "Clear, commented code with educational annotations"
    };

    this.problemAnalysis = this.cognitiveFramework.analyze5W1H(problem);
    return this.problemAnalysis;
  }

  /**
   * Apply Computational Thinking to break down the problem
   * Demonstrates the four pillars: Decomposition, Pattern Recognition, Abstraction, Algorithm Design
   */
  applyComputationalThinking() {
    console.log("🧮 Phase 2: Computational Thinking Application");
    
    const computationalStrategy = {
      decomposition: [
        "Outer loop: Control number of passes through array",
        "Inner loop: Compare adjacent elements in each pass", 
        "Swap mechanism: Exchange elements when they're in wrong order",
        "Optimization check: Early termination when no swaps occur",
        "Progress tracking: Monitor sorting progress for educational purposes"
      ],
      
      patternRecognition: {
        pattern: "Adjacent comparison and swap pattern",
        algorithmFamily: "Comparison-based sorting algorithms",
        similarity: "Similar to selection sort and insertion sort in approach",
        commonElements: "Nested loops, comparison operations, in-place modifications",
        optimizationPatterns: "Early termination, reduced comparison range"
      },
      
      abstraction: {
        essential: "Sequential pairwise comparisons with conditional swapping",
        nonEssential: "Specific array values, exact number of elements, data types",
        coreInterface: "Input: comparable array → Output: sorted array",
        keyInvariants: "Array length unchanged, elements preserved, sorting stability",
        abstractConcept: "Bubble largest elements to correct positions through comparisons"
      },
      
      algorithmicDesign: [
        "Initialize pass counter for outer loop",
        "For each pass, compare adjacent elements", 
        "Swap elements if they're in wrong order",
        "Track whether any swaps occurred in this pass",
        "Reduce comparison range by one each pass (optimization)",
        "Terminate early if no swaps occurred (optimization)",
        "Return sorted array"
      ]
    };

    console.log("✅ Computational Thinking Strategy Developed:", computationalStrategy);
    return computationalStrategy;
  }

  /**
   * Enhanced Polya Method Implementation
   * Integrates 5W1H analysis with systematic problem-solving
   */
  solveWithPolya(inputArray) {
    console.log("📚 Enhanced Polya Method: 4-Phase Problem Solving");
    
    // Phase 1: Understand (Enhanced with 5W1H)
    const understanding = this.understandProblem(inputArray);
    
    // Phase 2: Plan (Enhanced with Computational Thinking)  
    const plan = this.createExecutionPlan(understanding);
    
    // Phase 3: Execute (Enhanced with Metacognitive Monitoring)
    const solution = this.executeWithMonitoring(plan, inputArray);
    
    // Phase 4: Review (Enhanced with Structured Reflection)
    const review = this.conductStructuredReview(solution);
    
    return {
      understanding,
      plan, 
      solution,
      review,
      cognitiveInsights: this.monitor.generateInsights()
    };
  }

  /**
   * Phase 1: Enhanced Understanding
   */
  understandProblem(inputArray) {
    console.log("📖 Polya Phase 1: Understanding the Problem");
    
    const analysis = this.analyzeProblem();
    const computationalApproach = this.applyComputationalThinking();
    
    // Validate input and create test cases
    const testCases = this.generateTestCases(inputArray);
    
    return {
      problemAnalysis: analysis,
      computationalStrategy: computationalApproach,
      inputValidation: this.validateInput(inputArray),
      testCases: testCases,
      successCriteria: this.defineSuccessCriteria(inputArray),
      edgeCases: this.identifyEdgeCases(inputArray)
    };
  }

  /**
   * Phase 2: Enhanced Planning
   */
  createExecutionPlan(understanding) {
    console.log("📋 Polya Phase 2: Creating Execution Plan");
    
    return {
      algorithm: understanding.computationalStrategy.algorithmicDesign,
      dataStructures: {
        primary: "Input array (in-place modification)",
        auxiliary: "Temporary variables for swapping and loop control"
      },
      complexityAnalysis: {
        timeWorstCase: "O(n²) - nested loops with n*(n-1)/2 comparisons",
        timeBestCase: "O(n) - optimized early termination when array is sorted",
        timeAverageCase: "O(n²) - expected performance for random input",
        spaceComplexity: "O(1) - in-place sorting with constant extra space"
      },
      optimizations: [
        "Early termination when no swaps occur in a pass",
        "Reduce comparison range by one each pass", 
        "Track swap count for performance metrics",
        "Add educational logging for learning purposes"
      ],
      riskMitigation: [
        "Validate input is an array",
        "Handle empty and single-element arrays",
        "Ensure comparison function works with data types",
        "Prevent infinite loops with proper loop bounds"
      ]
    };
  }

  /**
   * Phase 3: Execute with Metacognitive Monitoring  
   */
  executeWithMonitoring(plan, inputArray) {
    console.log("⚙️ Polya Phase 3: Executing with Metacognitive Awareness");
    
    this.monitor.startMonitoring("enhanced_bubble_sort");
    this.monitor.logStrategy("Optimized bubble sort with early termination and educational logging");
    
    // Create working copy and initialize metrics
    const arr = [...inputArray];
    const n = arr.length;
    let totalSwaps = 0;
    let totalComparisons = 0;
    let passCount = 0;
    
    // Early return for trivial cases
    if (n <= 1) {
      this.monitor.logOptimization("Array has ≤1 element, already sorted");
      return this.createSolutionResult(arr, 0, 0, 0);
    }

    console.log(`🎯 Starting sort of ${n} elements:`, arr);
    
    // Main bubble sort algorithm with monitoring
    for (let i = 0; i < n - 1; i++) {
      let swappedInThisPass = false;
      passCount++;
      
      this.monitor.logProgress(`Pass ${passCount}/${n-1}: Reducing unsorted portion`);
      
      // Inner loop: bubble largest element to correct position
      for (let j = 0; j < n - i - 1; j++) {
        totalComparisons++;
        
        // Metacognitive reflection point
        if (this.monitor.shouldReflect()) {
          this.monitor.reflect("Element comparison", {
            comparing: `arr[${j}]=${arr[j]} with arr[${j+1}]=${arr[j+1]}`,
            pass: passCount,
            position: j,
            unsortedLength: n - i,
            swapNeeded: arr[j] > arr[j + 1]
          });
        }
        
        // Core comparison and swap logic
        if (arr[j] > arr[j + 1]) {
          // Swap elements
          [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
          swappedInThisPass = true;
          totalSwaps++;
          
          console.log(`  🔄 Swap: ${arr[j+1]} ↔ ${arr[j]} → [${arr.join(', ')}]`);
        }
      }
      
      // Optimization: early termination check
      if (!swappedInThisPass) {
        this.monitor.logOptimization(`No swaps in pass ${passCount}, array is sorted! Terminating early.`);
        console.log(`  ✅ Early termination after ${passCount} passes`);
        break;
      }
      
      console.log(`  📊 Pass ${passCount} complete. Swaps in this pass: ${swappedInThisPass ? 'Yes' : 'No'}`);
      console.log(`  📈 Current state: [${arr.join(', ')}]`);
    }

    const result = this.createSolutionResult(arr, totalComparisons, totalSwaps, passCount);
    this.monitor.complete("enhanced_bubble_sort");
    
    return result;
  }

  /**
   * Phase 4: Structured Review and Reflection
   */
  conductStructuredReview(solution) {
    console.log("🔍 Polya Phase 4: Structured Review and Learning");
    
    return {
      correctnessValidation: this.validateSortCorrectness(solution),
      efficiencyAnalysis: this.analyzeSolutionEfficiency(solution),
      educationalValue: this.assessEducationalImpact(solution),
      learningInsights: this.extractLearningInsights(solution),
      improvementOpportunities: this.identifyImprovements(solution),
      alternativeApproaches: this.suggestAlternatives(solution),
      teachingPoints: this.identifyTeachingMoments(solution),
      nextLearningSteps: this.recommendNextSteps(solution)
    };
  }

  /**
   * Create comprehensive solution result
   */
  createSolutionResult(sortedArray, comparisons, swaps, passes) {
    return {
      result: sortedArray,
      metrics: {
        comparisons,
        swaps,
        passes,
        elements: sortedArray.length,
        efficiency: this.calculateEfficiencyRatio(comparisons, swaps, sortedArray.length)
      },
      performance: {
        actualComplexity: this.determineActualComplexity(comparisons, sortedArray.length),
        optimizationEffectiveness: passes < sortedArray.length - 1 ? "Early termination successful" : "Full passes required"
      },
      educationalInsights: {
        conceptsDemonstrated: ["Nested loops", "Adjacent comparisons", "In-place sorting", "Algorithm optimization"],
        complexityLessons: ["O(n²) worst case", "O(n) best case with optimization", "Space efficiency O(1)"]
      }
    };
  }

  // Utility methods for comprehensive analysis

  generateTestCases(inputArray) {
    return [
      { case: "empty", input: [], expected: [] },
      { case: "single", input: [1], expected: [1] },
      { case: "already_sorted", input: [1, 2, 3, 4], expected: [1, 2, 3, 4] },
      { case: "reverse_sorted", input: [4, 3, 2, 1], expected: [1, 2, 3, 4] },
      { case: "duplicates", input: [3, 1, 3, 2, 1], expected: [1, 1, 2, 3, 3] },
      { case: "provided", input: [...inputArray], expected: [...inputArray].sort((a, b) => a - b) }
    ];
  }

  validateInput(inputArray) {
    return {
      isArray: Array.isArray(inputArray),
      length: inputArray.length,
      dataTypes: [...new Set(inputArray.map(item => typeof item))],
      hasComparableElements: inputArray.every(item => typeof item === 'number' || typeof item === 'string'),
      isEmpty: inputArray.length === 0
    };
  }

  defineSuccessCriteria(inputArray) {
    return {
      functional: "Array is sorted in ascending order",
      nonFunctional: "In-place modification, stable sorting, educational clarity",
      performance: "Reasonable performance for educational datasets (≤1000 elements)",
      educational: "Clear demonstration of algorithmic concepts and optimization techniques"
    };
  }

  identifyEdgeCases(inputArray) {
    return [
      "Empty array handling",
      "Single element array",
      "Already sorted array (best case)",
      "Reverse sorted array (worst case)", 
      "Array with duplicate elements",
      "Array with mixed data types"
    ];
  }

  validateSortCorrectness(solution) {
    const { result } = solution;
    const isCorrectlySorted = result.every((item, index) => 
      index === 0 || result[index - 1] <= item
    );
    
    return {
      isCorrect: isCorrectlySorted,
      verification: isCorrectlySorted ? "✅ Array is correctly sorted" : "❌ Sorting error detected",
      additionalChecks: {
        lengthPreserved: "Array length maintained",
        elementsPreserved: "All original elements present (stability)"
      }
    };
  }

  analyzeSolutionEfficiency(solution) {
    const { metrics } = solution;
    const theoreticalMaxComparisons = (metrics.elements * (metrics.elements - 1)) / 2;
    const efficiency = (theoreticalMaxComparisons - metrics.comparisons) / theoreticalMaxComparisons;
    
    return {
      actualComparisons: metrics.comparisons,
      theoreticalMaxComparisons,
      efficiencyGain: `${(efficiency * 100).toFixed(1)}% fewer comparisons than worst case`,
      swapRatio: metrics.swaps / metrics.comparisons,
      passEfficiency: `${metrics.passes} passes out of maximum ${metrics.elements - 1}`
    };
  }

  assessEducationalImpact(solution) {
    return {
      conceptsCovered: [
        "Nested loop structures and their complexity implications",
        "Adjacent element comparison strategies",
        "In-place array modification techniques",
        "Algorithm optimization through early termination",
        "Performance metrics collection and analysis"
      ],
      skillsDevelo ped: [
        "Algorithmic thinking and problem decomposition",
        "Complexity analysis and performance measurement", 
        "Code optimization and efficiency improvement",
        "Systematic debugging and metacognitive monitoring"
      ],
      learningObjectives: [
        "Understand O(n²) complexity behavior",
        "Recognize optimization opportunities",
        "Develop systematic problem-solving approach",
        "Build foundation for advanced sorting algorithms"
      ]
    };
  }

  extractLearningInsights(solution) {
    return [
      `Bubble sort made ${solution.metrics.comparisons} comparisons and ${solution.metrics.swaps} swaps`,
      `Early termination optimization ${solution.performance.optimizationEffectiveness.toLowerCase()}`,
      "The algorithm demonstrates how nested loops lead to O(n²) complexity",
      "Optimization techniques can significantly improve best-case performance",
      "In-place sorting provides space efficiency at the cost of time complexity"
    ];
  }

  identifyImprovements(solution) {
    return [
      "Implement cocktail shaker sort (bidirectional bubbling) for better average performance",
      "Add visualization capabilities to show sorting process graphically",
      "Implement generic comparison function for sorting different data types",
      "Add performance benchmarking against other O(n²) algorithms",
      "Create adaptive switching to more efficient algorithms for larger datasets"
    ];
  }

  suggestAlternatives(solution) {
    return [
      {
        algorithm: "Selection Sort",
        advantage: "Fewer swaps (O(n) swaps vs O(n²))",
        tradeoff: "Always O(n²) comparisons, no best-case optimization"
      },
      {
        algorithm: "Insertion Sort", 
        advantage: "Better performance on nearly sorted data",
        tradeoff: "More complex implementation, requires shifting elements"
      },
      {
        algorithm: "Merge Sort",
        advantage: "Guaranteed O(n log n) performance",
        tradeoff: "Requires O(n) extra space, more complex implementation"
      }
    ];
  }

  identifyTeachingMoments(solution) {
    return [
      "Demonstrate why nested loops create O(n²) complexity",
      "Show how optimization can improve best-case performance",
      "Explain the trade-off between time and space complexity",
      "Illustrate the importance of algorithm analysis and measurement",
      "Connect bubble sort concepts to more advanced sorting algorithms"
    ];
  }

  recommendNextSteps(solution) {
    return [
      "Implement and compare other O(n²) sorting algorithms",
      "Study O(n log n) algorithms like merge sort and quicksort",
      "Explore advanced optimization techniques and adaptive algorithms",
      "Practice algorithm analysis and complexity calculation",
      "Apply structured thinking frameworks to other algorithmic problems"
    ];
  }

  calculateEfficiencyRatio(comparisons, swaps, n) {
    const maxComparisons = (n * (n - 1)) / 2;
    return maxComparisons > 0 ? (maxComparisons - comparisons) / maxComparisons : 0;
  }

  determineActualComplexity(comparisons, n) {
    const maxComparisons = (n * (n - 1)) / 2;
    const ratio = comparisons / maxComparisons;
    
    if (ratio < 0.1) return "Approaching O(n) - highly optimized";
    if (ratio < 0.5) return "Better than average O(n²)";
    if (ratio < 0.9) return "Typical O(n²) behavior";
    return "Worst-case O(n²) performance";
  }

  // Public interface for direct sorting
  sort(inputArray) {
    console.log("\n🎨 Enhanced Bubble Sort with Structured Thinking");
    console.log("==================================================");
    
    const result = this.solveWithPolya(inputArray);
    
    console.log("\n📊 Final Results:");
    console.log("Sorted Array:", result.solution.result);
    console.log("Performance Metrics:", result.solution.metrics);
    console.log("\n🎓 Educational Summary:");
    console.log(result.review.educationalValue.conceptsCovered.map(concept => `• ${concept}`).join('\n'));
    
    return result.solution.result;
  }
}

// Demonstration and testing
function demonstrateEnhancedBubbleSort() {
  console.log("\n🚀 Demonstrating Enhanced Bubble Sort with Structured Thinking");
  console.log("================================================================");
  
  const sorter = new EnhancedBubbleSort();
  
  // Test cases demonstrating different scenarios
  const testCases = [
    {
      name: "Small Random Array",
      data: [64, 34, 25, 12, 22, 11, 90],
      description: "Typical unsorted data"
    },
    {
      name: "Nearly Sorted", 
      data: [1, 2, 4, 3, 5, 6],
      description: "Tests optimization effectiveness"
    },
    {
      name: "Reverse Sorted",
      data: [5, 4, 3, 2, 1],
      description: "Worst-case scenario"
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n📝 Test Case ${index + 1}: ${testCase.name}`);
    console.log(`Description: ${testCase.description}`);
    console.log(`Input: [${testCase.data.join(', ')}]`);
    console.log("-".repeat(50));
    
    const result = sorter.sort([...testCase.data]);
    console.log(`✅ Result: [${result.join(', ')}]`);
  });
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { EnhancedBubbleSort };
}

// Run demonstration if executed directly
if (typeof window === 'undefined' && require.main === module) {
  demonstrateEnhancedBubbleSort();
}