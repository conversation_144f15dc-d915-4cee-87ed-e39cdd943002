"use strict";
/**
 * Basic Genetic Algorithm to evolve a target string.
 * This is a simplified educational example.
 */

const TARGET = "HELLO WORLD";
const CHARSET = " ABCDEFGHIJKLMNOPQRSTUVWXYZ";
const POP_SIZE = 100;
const MUTATION_RATE = 0.05;

// Create a random individual (string)
function createIndividual() {
  let individual = '';
  for (let i = 0; i < TARGET.length; i++) {
    individual += CHARSET[Math.floor(Math.random() * CHARSET.length)];
  }
  return individual;
}

// Calculate fitness (higher is better)
function calculateFitness(individual) {
  let fitness = 0;
  for (let i = 0; i < TARGET.length; i++) {
    if (individual[i] === TARGET[i]) {
      fitness++;
    }
  }
  return fitness;
}

// Select parents from the population (tournament selection)
function selectParents(population) {
  const tournamentSize = 5;
  let best = null;
  for (let i = 0; i < tournamentSize; i++) {
    const randomIndividual = population[Math.floor(Math.random() * population.length)];
    if (best === null || randomIndividual.fitness > best.fitness) {
      best = randomIndividual;
    }
  }
  return best;
}

// Crossover (single-point)
function crossover(parent1, parent2) {
  const midpoint = Math.floor(Math.random() * TARGET.length);
  return parent1.slice(0, midpoint) + parent2.slice(midpoint);
}

// Mutate
function mutate(individual) {
  let mutated = '';
  for (let i = 0; i < individual.length; i++) {
    if (Math.random() < MUTATION_RATE) {
      mutated += CHARSET[Math.floor(Math.random() * CHARSET.length)];
    } else {
      mutated += individual[i];
    }
  }
  return mutated;
}

function runGA() {
  let population = Array.from({ length: POP_SIZE }, createIndividual).map(ind => ({ individual: ind, fitness: calculateFitness(ind) }));

  let generation = 0;
  let bestIndividual = population.sort((a, b) => b.fitness - a.fitness)[0];

  while (bestIndividual.fitness < TARGET.length) {
    const newPopulation = [];
    for (let i = 0; i < POP_SIZE; i++) {
      const parent1 = selectParents(population).individual;
      const parent2 = selectParents(population).individual;
      let child = crossover(parent1, parent2);
      child = mutate(child);
      newPopulation.push({ individual: child, fitness: calculateFitness(child) });
    }
    population = newPopulation;
    bestIndividual = population.sort((a, b) => b.fitness - a.fitness)[0];
    generation++;
    console.log(`Gen ${generation}: ${bestIndividual.individual} (Fitness: ${bestIndividual.fitness})`);
  }
  console.log(`\nTarget reached in ${generation} generations!`);
}

module.exports = { runGA };

if (require.main === module) {
  runGA();
}

