"use strict";
/**
 * Fractional Knapsack (Greedy)
 * Given arrays weights and values (non-negative), and capacity W, maximize total value
 * by taking fractional amounts of items. Returns { value, items } where items list
 * includes { index, taken, weight, value } with taken in [0,1].
 *
 * Time: O(n log n) due to sorting
 */
function greedyKnapsack(weights, values, W) {
  if (!Array.isArray(weights) || !Array.isArray(values) || weights.length !== values.length) {
    throw new TypeError("weights and values must be arrays of equal length");
  }
  if (typeof W !== "number" || !Number.isFinite(W) || W < 0) throw new TypeError("W must be a non-negative number");

  const items = weights.map((w, i) => {
    const v = values[i];
    if (!(typeof w === "number" && w >= 0) || !(typeof v === "number" && v >= 0)) {
      throw new TypeError("weights/values must be non-negative numbers");
    }
    return { index: i, w, v, ratio: w === 0 ? (v === 0 ? 0 : Infinity) : v / w };
  });
  items.sort((a, b) => b.ratio - a.ratio);

  let remaining = W;
  let total = 0;
  const taken = [];
  for (const it of items) {
    if (remaining <= 0) break;
    if (it.w <= remaining) {
      total += it.v;
      remaining -= it.w;
      taken.push({ index: it.index, taken: 1, weight: it.w, value: it.v });
    } else {
      const frac = remaining / it.w;
      total += it.v * frac;
      taken.push({ index: it.index, taken: frac, weight: it.w * frac, value: it.v * frac });
      remaining = 0;
    }
  }
  return { value: total, items: taken };
}

module.exports = greedyKnapsack;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const w = [10, 40, 20, 30];
  const v = [60, 40, 100, 120];
  const { value } = greedyKnapsack(w, v, 50);
  // Known optimal ~240
  assert(Math.abs(value - 240) < 1e-9, `Expected ~240, got ${value}`);
  console.log("greedy_knapsack.js tests passed");
}

