"use strict";
/**
 * Equated Monthly Installments (EMI)
 * EMI = P * r * (1+r)^n / ((1+r)^n - 1)
 * where P = principal, r = monthly interest rate (annualRate/12), n = number of months
 * Returns monthly payment amount.
 */
function emi(principal, annualRate, months) {
  if (![principal, annualRate].every(x => typeof x === "number" && Number.isFinite(x) && x >= 0)) {
    throw new TypeError("principal and annualRate must be non-negative numbers");
  }
  if (!Number.isInteger(months) || months <= 0) throw new TypeError("months must be a positive integer");
  const r = annualRate / 12;
  if (r === 0) return principal / months;
  const pow = Math.pow(1 + r, months);
  return (principal * r * pow) / (pow - 1);
}

module.exports = emi;

if (require.main === module) {
  const e = emi(100000, 0.12, 12);
  console.log(e.toFixed(2));
}

