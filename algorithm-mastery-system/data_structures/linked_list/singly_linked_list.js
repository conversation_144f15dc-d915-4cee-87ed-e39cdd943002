"use strict";
/**
 * Singly Linked List
 * Provides typical operations: pushFront, pushBack, popFront, popBack, insertAt, removeAt,
 * find (by predicate), size, toArray, [Symbol.iterator].
 *
 * For education: this list stores values as-is without type restrictions.
 */

class Node {
  constructor(value, next = null) {
    this.value = value;
    this.next = next;
  }
}

class SinglyLinkedList {
  constructor(iterable = []) {
    if (!isIterable(iterable)) throw new TypeError("SinglyLinkedList: iterable expected");
    this.head = null;
    this.tail = null;
    this._size = 0;
    for (const x of iterable) this.pushBack(x);
  }

  size() { return this._size; }
  isEmpty() { return this._size === 0; }

  pushFront(value) {
    const node = new Node(value, this.head);
    this.head = node;
    if (!this.tail) this.tail = node;
    this._size++;
  }

  pushBack(value) {
    const node = new Node(value, null);
    if (!this.head) { this.head = this.tail = node; }
    else { this.tail.next = node; this.tail = node; }
    this._size++;
  }

  popFront() {
    if (this.isEmpty()) throw new Error("popFront: list is empty");
    const val = this.head.value;
    this.head = this.head.next;
    if (!this.head) this.tail = null;
    this._size--;
    return val;
  }

  popBack() {
    if (this.isEmpty()) throw new Error("popBack: list is empty");
    if (this._size === 1) { const v = this.head.value; this.head = this.tail = null; this._size = 0; return v; }
    let prev = this.head;
    while (prev.next !== this.tail) prev = prev.next;
    const v = this.tail.value;
    prev.next = null;
    this.tail = prev;
    this._size--;
    return v;
  }

  insertAt(index, value) {
    if (!Number.isInteger(index) || index < 0 || index > this._size) throw new RangeError("insertAt: index out of range");
    if (index === 0) { this.pushFront(value); return; }
    if (index === this._size) { this.pushBack(value); return; }
    let prev = this.head; for (let i = 0; i < index - 1; i++) prev = prev.next;
    const node = new Node(value, prev.next);
    prev.next = node; this._size++;
  }

  removeAt(index) {
    if (!Number.isInteger(index) || index < 0 || index >= this._size) throw new RangeError("removeAt: index out of range");
    if (index === 0) return this.popFront();
    let prev = this.head; for (let i = 0; i < index - 1; i++) prev = prev.next;
    const removed = prev.next; prev.next = removed.next; if (removed === this.tail) this.tail = prev; this._size--;
    return removed.value;
  }

  find(predicate) {
    if (typeof predicate !== "function") throw new TypeError("find: predicate must be a function");
    let i = 0; for (let cur = this.head; cur; cur = cur.next, i++) {
      if (predicate(cur.value, i)) return cur.value;
    }
    return undefined;
  }

  toArray() {
    const out = []; for (let cur = this.head; cur; cur = cur.next) out.push(cur.value); return out;
  }

  clear() { this.head = this.tail = null; this._size = 0; }

  [Symbol.iterator]() {
    let cur = this.head;
    return {
      next() {
        if (!cur) return { done: true };
        const v = cur.value; cur = cur.next; return { value: v, done: false };
      }
    };
  }
}

function isIterable(obj) { return obj != null && typeof obj[Symbol.iterator] === "function"; }

module.exports = SinglyLinkedList;

// Inline examples/tests
if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const list = new SinglyLinkedList([1, 2, 3]);
  list.pushFront(0); // 0,1,2,3
  list.pushBack(4);  // 0,1,2,3,4
  assert(list.size() === 5, "size 5");
  assert(list.removeAt(2) === 2, "remove index 2 value 2"); // 0,1,3,4
  assert(list.popFront() === 0, "popFront 0"); // 1,3,4
  assert(list.popBack() === 4, "popBack 4"); // 1,3
  assert(JSON.stringify(list.toArray()) === JSON.stringify([1,3]), "toArray [1,3]");
  assert(list.find(x => x === 3) === 3, "find 3");
  console.log("singly_linked_list.js tests passed");
}

