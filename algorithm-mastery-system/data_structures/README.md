Data Structures (JavaScript)

This folder mirrors the Data Structures section from TheAlgorithms/Python. It is subdivided into subfolders to match the original structure (arrays/, binary_tree/, disjoint_set/, hashing/, heap/, kd_tree/, linked_list/, queues/, stacks/, suffix_tree/, trie/). This JS adaptation currently includes foundational examples and is intended to be extended over time.

Included (initial set)
- binary_tree/binary_search_tree.js – classic BST with insert, search, delete, traversals
- linked_list/singly_linked_list.js – singly linked list operations
- stacks/stack.js – array-backed stack
- queues/queue_by_list.js – array-backed queue

Usage
- node stacks/stack.js
- const BST = require('./binary_tree/binary_search_tree');

Notes
- Implementations focus on clarity, include inline tests/examples, and validate inputs.
- APIs are minimal but idiomatic for learning; feel free to extend.

