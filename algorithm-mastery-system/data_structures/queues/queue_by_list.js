"use strict";
/**
 * Queue (FIFO) using JavaScript array internally.
 * Operations: enqueue, dequeue, front, size, isEmpty, clear.
 */
class Queue {
  constructor(iterable = []) {
    if (!isIterable(iterable)) throw new TypeError("Queue: iterable expected");
    this._data = [];
    for (const x of iterable) this._data.push(x);
    this._head = 0; // optimize dequeue to O(1) amortized using head index
  }
  enqueue(x) { this._data.push(x); }
  dequeue() {
    if (this.isEmpty()) throw new Error("Queue underflow");
    const val = this._data[this._head++];
    // periodic compaction
    if (this._head > 64 && this._head * 2 > this._data.length) {
      this._data = this._data.slice(this._head);
      this._head = 0;
    }
    return val;
  }
  front() { return this.isEmpty() ? undefined : this._data[this._head]; }
  size() { return this._data.length - this._head; }
  isEmpty() { return this.size() === 0; }
  clear() { this._data.length = 0; this._head = 0; }
}

function isIterable(obj) { return obj != null && typeof obj[Symbol.iterator] === "function"; }

module.exports = Queue;

// Example usage and basic tests
if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const q = new Queue([1]);
  q.enqueue(2); q.enqueue(3);
  assert(q.front() === 1, "front is 1");
  assert(q.dequeue() === 1, "deq 1");
  assert(q.size() === 2, "size 2");
  assert(!q.isEmpty(), "not empty");
  q.clear();
  assert(q.isEmpty(), "now empty");
  console.log("queue_by_list.js tests passed");
}

