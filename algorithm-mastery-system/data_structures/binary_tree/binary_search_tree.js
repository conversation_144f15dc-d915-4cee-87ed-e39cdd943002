"use strict";
/**
 * Binary Search Tree (BST)
 *
 * Supports: insert, contains, find, min, max, remove (delete), inOrder, preOrder, postOrder.
 * Uses a comparator for generic values; defaults to numeric comparator.
 */

class Node {
  constructor(key, left = null, right = null) {
    this.key = key;
    this.left = left;
    this.right = right;
  }
}

class BinarySearchTree {
  constructor(comparator = defaultComparator) {
    if (typeof comparator !== "function") throw new TypeError("BST: comparator must be function");
    this._cmp = comparator;
    this.root = null;
    this._size = 0;
  }

  size() { return this._size; }
  isEmpty() { return this._size === 0; }

  insert(key) {
    this.root = this._insert(this.root, key);
    this._size++;
  }
  _insert(node, key) {
    if (!node) return new Node(key);
    const c = this._cmp(key, node.key);
    if (c < 0) node.left = this._insert(node.left, key);
    else node.right = this._insert(node.right, key);
    return node;
  }

  contains(key) { return this.find(key) !== null; }

  find(key) {
    let cur = this.root;
    while (cur) {
      const c = this._cmp(key, cur.key);
      if (c === 0) return cur.key;
      cur = c < 0 ? cur.left : cur.right;
    }
    return null;
  }

  min() { if (!this.root) return null; let n = this.root; while (n.left) n = n.left; return n.key; }
  max() { if (!this.root) return null; let n = this.root; while (n.right) n = n.right; return n.key; }

  remove(key) {
    const [newRoot, removed] = this._remove(this.root, key);
    if (removed) { this.root = newRoot; this._size--; }
    return removed;
  }
  _remove(node, key) {
    if (!node) return [null, false];
    const c = this._cmp(key, node.key);
    if (c < 0) { const [ln, r] = this._remove(node.left, key); node.left = ln; return [node, r]; }
    if (c > 0) { const [rn, r] = this._remove(node.right, key); node.right = rn; return [node, r]; }
    // node to delete
    if (!node.left) return [node.right, true];
    if (!node.right) return [node.left, true];
    // two children: replace with inorder successor
    let succParent = node;
    let succ = node.right;
    while (succ.left) { succParent = succ; succ = succ.left; }
    if (succParent !== node) succParent.left = succ.right; else succParent.right = succ.right;
    node.key = succ.key;
    return [node, true];
  }

  inOrder() { const res = []; (function dfs(n){ if (!n) return; dfs(n.left); res.push(n.key); dfs(n.right); })(this.root); return res; }
  preOrder() { const res = []; (function dfs(n){ if (!n) return; res.push(n.key); dfs(n.left); dfs(n.right); })(this.root); return res; }
  postOrder() { const res = []; (function dfs(n){ if (!n) return; dfs(n.left); dfs(n.right); res.push(n.key); })(this.root); return res; }
}

function defaultComparator(a, b) { return a === b ? 0 : a < b ? -1 : 1; }

module.exports = BinarySearchTree;

// Example usage and tests
if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const bst = new BinarySearchTree();
  [5,3,7,2,4,6,8].forEach(x => bst.insert(x));
  assert(bst.size() === 7, "size 7");
  assert(bst.contains(4), "contains 4");
  assert(!bst.contains(10), "not contains 10");
  assert(JSON.stringify(bst.inOrder()) === JSON.stringify([2,3,4,5,6,7,8]), "inOrder");
  assert(bst.min() === 2 && bst.max() === 8, "min/max");
  assert(bst.remove(3), "removed 3");
  assert(!bst.contains(3), "no 3");
  console.log("binary_search_tree.js tests passed");
}

