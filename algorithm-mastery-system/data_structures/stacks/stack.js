"use strict";
/**
 * Stack (LIFO) using JavaScript array internally.
 * Operations: push, pop, peek, size, isEmpty, clear.
 */
class Stack {
  constructor(iterable = []) {
    if (!isIterable(iterable)) throw new TypeError("Stack: iterable expected");
    this._data = [];
    for (const x of iterable) this._data.push(x);
  }
  push(x) { this._data.push(x); }
  pop() { if (this.isEmpty()) throw new Error("Stack underflow"); return this._data.pop(); }
  peek() { if (this.isEmpty()) return undefined; return this._data[this._data.length - 1]; }
  size() { return this._data.length; }
  isEmpty() { return this._data.length === 0; }
  clear() { this._data.length = 0; }
}

function isIterable(obj) {
  return obj != null && typeof obj[Symbol.iterator] === "function";
}

module.exports = Stack;

// Example usage and basic tests
if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const s = new Stack([1,2]);
  s.push(3);
  assert(s.peek() === 3, "peek should be 3");
  assert(s.pop() === 3, "pop 3");
  assert(s.size() === 2, "size 2");
  assert(!s.isEmpty(), "not empty");
  s.clear();
  assert(s.isEmpty(), "now empty");
  console.log("stack.js tests passed");
}

