"use strict";
/**
 * k-Nearest Neighbors (k-NN) Classification
 * A simple classification algorithm that classifies a new data point based on the majority class of its k-nearest neighbors.
 */

// Calculate Euclidean distance between two points
function euclideanDistance(point1, point2) {
  if (point1.length !== point2.length) {
    throw new Error('Points must have the same number of dimensions.');
  }
  let sum = 0;
  for (let i = 0; i < point1.length; i++) {
    sum += (point1[i] - point2[i]) ** 2;
  }
  return Math.sqrt(sum);
}

// k-NN classifier
function kNN(trainingData, testPoint, k) {
  if (k > trainingData.length) {
    throw new Error('k cannot be larger than the number of training data points.');
  }

  const distances = trainingData.map(data => ({
    distance: euclideanDistance(data.point, testPoint),
    label: data.label,
  }));

  distances.sort((a, b) => a.distance - b.distance);

  const kNearest = distances.slice(0, k);

  const labelCounts = {};
  for (const neighbor of kNearest) {
    labelCounts[neighbor.label] = (labelCounts[neighbor.label] || 0) + 1;
  }

  let maxCount = 0;
  let predictedLabel = null;
  for (const label in labelCounts) {
    if (labelCounts[label] > maxCount) {
      maxCount = labelCounts[label];
      predictedLabel = label;
    }
  }

  return predictedLabel;
}

module.exports = kNN;

if (require.main === module) {
  const trainingData = [
    { point: [1, 1], label: 'A' },
    { point: [1, 2], label: 'A' },
    { point: [2, 2], label: 'A' },
    { point: [5, 5], label: 'B' },
    { point: [5, 6], label: 'B' },
    { point: [6, 6], label: 'B' },
  ];

  const testPoint = [1.5, 1.8];
  const k = 3;
  const prediction = kNN(trainingData, testPoint, k);
  console.log(`The predicted label for [${testPoint.join(', ')}] is: ${prediction}`); // Expected: 'A'

  const testPoint2 = [5.5, 5.8];
  const prediction2 = kNN(trainingData, testPoint2, k);
  console.log(`The predicted label for [${testPoint2.join(', ')}] is: ${prediction2}`); // Expected: 'B'
}

