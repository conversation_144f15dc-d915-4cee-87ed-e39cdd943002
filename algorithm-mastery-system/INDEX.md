# 📖 Algorithm Index - Complete Cross-Reference Guide

> Comprehensive index for navigating the Algorithm Mastery System

## 🔍 Quick Navigation

- [📚 **By Category**](#by-category) - Algorithms organized by type
- [⚡ **By Difficulty**](#by-difficulty) - Learning progression guide  
- [🎯 **By Use Case**](#by-use-case) - Problem-specific algorithms
- [📊 **By Complexity**](#by-complexity) - Performance-based selection
- [🔗 **Cross References**](#cross-references) - Related algorithms

---

## 📚 By Category

### 🔤 **Sorting & Searching**
| Algorithm | File | Complexity | Stability | Best Use |
|-----------|------|------------|-----------|----------|
| Bubble Sort | [`sorts/bubble_sort.js`](sorts/bubble_sort.js) | O(n²) | ✅ | Learning |
| Selection Sort | [`sorts/selection_sort.js`](sorts/selection_sort.js) | O(n²) | ❌ | Memory limited |
| Insertion Sort | [`sorts/insertion_sort.js`](sorts/insertion_sort.js) | O(n²) | ✅ | Small/nearly sorted |
| Merge Sort | [`sorts/merge_sort.js`](sorts/merge_sort.js) | O(n log n) | ✅ | Large datasets |
| Quick Sort | [`sorts/quick_sort.js`](sorts/quick_sort.js) | O(n log n) | ❌ | General purpose |
| Heap Sort | [`sorts/heap_sort.js`](sorts/heap_sort.js) | O(n log n) | ❌ | Guaranteed performance |
| Linear Search | [`searches/linear_search.js`](searches/linear_search.js) | O(n) | - | Unsorted data |
| Binary Search | [`searches/binary_search.js`](searches/binary_search.js) | O(log n) | - | Sorted data |

### 🏗️ **Data Structures**
| Structure | File | Access | Insert | Delete | Use Case |
|-----------|------|--------|--------|--------|----------|
| Singly Linked List | [`data_structures/linked_list/singly_linked_list.js`](data_structures/linked_list/singly_linked_list.js) | O(n) | O(1) | O(1) | Dynamic size |
| Stack | [`data_structures/stacks/stack.js`](data_structures/stacks/stack.js) | O(1) | O(1) | O(1) | LIFO operations |
| Queue | [`data_structures/queues/queue_by_list.js`](data_structures/queues/queue_by_list.js) | O(1) | O(1) | O(1) | FIFO operations |
| Binary Search Tree | [`data_structures/binary_tree/binary_search_tree.js`](data_structures/binary_tree/binary_search_tree.js) | O(log n) | O(log n) | O(log n) | Hierarchical data |

### 🕸️ **Graph Algorithms**  
| Algorithm | File | Complexity | Problem Solved |
|-----------|------|------------|----------------|
| Breadth-First Search | [`graphs/breadth_first_search.js`](graphs/breadth_first_search.js) | O(V + E) | Shortest path, level traversal |
| Depth-First Search | [`graphs/depth_first_search.js`](graphs/depth_first_search.js) | O(V + E) | Path finding, cycle detection |
| Dijkstra's Algorithm | [`graphs/dijkstra.js`](graphs/dijkstra.js) | O((V + E) log V) | Shortest weighted path |
| Topological Sort | [`graphs/kahns_algorithm_topo.js`](graphs/kahns_algorithm_topo.js) | O(V + E) | Dependency ordering |

### ⚡ **Dynamic Programming**
| Algorithm | File | Complexity | Problem Type |
|-----------|------|------------|--------------|
| Fibonacci | [`dynamic_programming/fibonacci.js`](dynamic_programming/fibonacci.js) | O(n) | Sequence optimization |
| Knapsack | [`dynamic_programming/knapsack.js`](dynamic_programming/knapsack.js) | O(nW) | Resource allocation |
| LCS | [`dynamic_programming/longest_common_subsequence.js`](dynamic_programming/longest_common_subsequence.js) | O(mn) | String similarity |
| Min Coin Change | [`dynamic_programming/minimum_coin_change.js`](dynamic_programming/minimum_coin_change.js) | O(nS) | Optimization |

---

## ⚡ By Difficulty

### 🟢 **Beginner (Level 1)**
**Essential foundations - Start here!**
- Linear Search, Binary Search
- Bubble Sort, Insertion Sort  
- Stack, Queue basics
- Array manipulations
- **Estimated Time:** 2-4 weeks

### 🟡 **Intermediate (Level 2)**  
**Building complexity - Interview ready**
- Merge Sort, Quick Sort
- Binary Search Trees
- BFS, DFS algorithms
- Basic Dynamic Programming
- **Estimated Time:** 4-6 weeks

### 🔴 **Advanced (Level 3)**
**Complex algorithms - Senior level**
- Advanced graph algorithms
- Complex DP problems
- String matching algorithms
- Optimization techniques
- **Estimated Time:** 6-8 weeks

### 🟣 **Expert (Level 4)**
**Specialized domains - Research level**
- Machine learning algorithms
- Cryptographic algorithms
- Mathematical optimizations
- Novel algorithm design
- **Estimated Time:** Ongoing

---

## 🎯 By Use Case

### 💻 **Interview Preparation**
**Most Asked Algorithms:**
1. Two Pointers → Array problems
2. Binary Search → Search problems  
3. BFS/DFS → Graph problems
4. Dynamic Programming → Optimization
5. Sliding Window → Subarray problems

### 🌐 **Web Development**
- Hash Tables → Fast lookups
- Queues → Task processing
- Sorting → Data presentation
- Search → User interfaces

### 🎮 **Game Development** 
- Pathfinding → BFS/DFS/Dijkstra
- State Management → Stack/Queue
- Collision Detection → Spatial algorithms
- AI → Decision trees, minimax

### 🏢 **Enterprise Systems**
- Sorting → Data processing
- Graph algorithms → Network analysis  
- DP → Resource optimization
- String algorithms → Text processing

---

## 📊 By Complexity

### ⚡ **O(1) - Constant Time**
- Hash table operations
- Stack/Queue operations  
- Array indexing

### 📈 **O(log n) - Logarithmic**
- Binary Search
- Balanced tree operations
- Binary heap operations

### 📏 **O(n) - Linear** 
- Linear Search
- Single array traversal
- Basic string processing

### 📊 **O(n log n) - Linearithmic**
- Efficient sorting (Merge, Quick, Heap)
- Divide and conquer algorithms
- Some graph algorithms

### ⚠️ **O(n²) - Quadratic**
- Simple sorting (Bubble, Selection, Insertion)
- Nested loop algorithms
- Basic string matching

---

## 🔗 Cross References

### 🔄 **Algorithm Relationships**

**Prerequisites:**
- Arrays → Sorting algorithms
- Sorting → Binary search  
- Recursion → Tree algorithms
- Trees → Graph algorithms

**Complementary Pairs:**
- BFS ↔ DFS (different traversal strategies)
- Stack ↔ Queue (opposite ordering)
- Greedy ↔ Dynamic Programming (optimization approaches)

**Evolution Paths:**
```
Linear Search → Binary Search → Hash Tables
Simple Sorting → Efficient Sorting → Specialized Sorting
Basic Trees → Balanced Trees → Advanced Trees
```

---

## 🎓 Learning Integration

This index integrates with your learning journey:

### 📚 **Study Resources**
- **Theory:** [`LEARNING-PATH.md`](LEARNING-PATH.md)
- **Practice:** [`GETTING-STARTED.md`](GETTING-STARTED.md)  
- **Selection:** [`ALGORITHM-SELECTION-GUIDE.md`](ALGORITHM-SELECTION-GUIDE.md)

### 🎯 **Progress Tracking**
- **Foundation:** Master 15+ basic algorithms
- **Intermediate:** Implement 30+ algorithms  
- **Advanced:** Optimize and combine algorithms
- **Expert:** Create novel solutions

---

*Use this index as your compass for navigating the algorithm landscape. Every expert was once a beginner - start where you are, use what you need, learn as you go!* 🧭