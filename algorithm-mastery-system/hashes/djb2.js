"use strict";
/**
 * djb2 string hash function
 * One of the oldest and simplest string hashing functions.
 * Returns a 32-bit integer hash.
 */
function djb2(s) {
  if (typeof s !== "string") throw new TypeError("djb2: input must be a string");
  let hash = 5381;
  for (let i = 0; i < s.length; i++) {
    hash = (((hash << 5) + hash) + s.charCodeAt(i)) | 0; // hash * 33 + c
  }
  return hash;
}

module.exports = djb2;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(djb2("hello world") === 2090063683, "hash for 'hello world'");
  console.log("djb2.js tests passed");
}

