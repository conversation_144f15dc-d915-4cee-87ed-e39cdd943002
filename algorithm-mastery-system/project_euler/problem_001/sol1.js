"use strict";
/**
 * Project Euler Problem 1: Multiples of 3 or 5
 * Find the sum of all the multiples of 3 or 5 below a given number.
 */
function sumMultiplesOf3Or5(n) {
  if (!Number.isSafeInteger(n) || n < 0) throw new TypeError("n must be a non-negative safe integer");
  let sum = 0;
  for (let i = 1; i < n; i++) {
    if (i % 3 === 0 || i % 5 === 0) sum += i;
  }
  return sum;
}

module.exports = sumMultiplesOf3Or5;

if (require.main === module) {
  console.log(sumMultiplesOf3Or5(1000)); // 233168
}

