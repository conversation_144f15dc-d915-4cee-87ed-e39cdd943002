"use strict";
/**
 * Count Set Bits (<PERSON>'s Algorithm)
 * Counts number of 1-bits in the binary representation of a non-negative integer.
 * Time: O(k) where k is number of set bits
 */
function countSetBits(n) {
  if (!Number.isSafeInteger(n) || n < 0) throw new TypeError("countSetBits: n must be a non-negative safe integer");
  let count = 0;
  while (n !== 0) {
    n &= (n - 1);
    count++;
  }
  return count;
}

module.exports = countSetBits;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(countSetBits(0) === 0);
  assert(countSetBits(7) === 3);
  assert(countSetBits(1023) === 10);
  console.log("count_set_bits.js tests passed");
}

