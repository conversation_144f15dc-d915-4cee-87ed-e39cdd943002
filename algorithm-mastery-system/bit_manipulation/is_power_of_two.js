"use strict";
/**
 * Check if n is a power of two
 * Works for positive safe integers.
 */
function isPowerOfTwo(n) {
  if (!Number.isSafeInteger(n) || n <= 0) throw new TypeError("isPowerOfTwo: n must be a positive safe integer");
  return (n & (n - 1)) === 0;
}

module.exports = isPowerOfTwo;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(isPowerOfTwo(1));
  assert(isPowerOfTwo(2));
  assert(!isPowerOfTwo(3));
  assert(isPowerOfTwo(1024));
  console.log("is_power_of_two.js tests passed");
}

