"use strict";
/**
 * Changes the brightness of an RGB pixel.
 * Input: pixel object {r, g, b} and a value to add/subtract from each component.
 * Output: new pixel object with clamped values [0, 255].
 */
function changeBrightness(pixel, value) {
  if (!pixel || typeof pixel.r !== 'number' || typeof pixel.g !== 'number' || typeof pixel.b !== 'number') {
    throw new TypeError("Input must be an object {r, g, b} with number values.");
  }
  if (typeof value !== 'number' || !Number.isFinite(value)) {
    throw new TypeError('Value must be a finite number.');
  }

  const clamp = (val) => Math.max(0, Math.min(255, val));

  return {
    r: clamp(pixel.r + value),
    g: clamp(pixel.g + value),
    b: clamp(pixel.b + value),
  };
}

module.exports = changeBrightness;

if (require.main === module) {
  const pixel = { r: 100, g: 150, b: 200 };
  const brighter = changeBrightness(pixel, 50);
  console.log('Brighter:', brighter); // { r: 150, g: 200, b: 250 }

  const darker = changeBrightness(pixel, -75);
  console.log('Darker:', darker); // { r: 25, g: 75, b: 125 }

  const clamped = changeBrightness(pixel, 100);
  console.log('Clamped:', clamped); // { r: 200, g: 250, b: 255 }
}

