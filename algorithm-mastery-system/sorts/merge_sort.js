"use strict";
/**
 * Merge Sort (Stable, O(n log n))
 * Divide-and-conquer: split array, sort halves, and merge.
 * Returns a new sorted array (does not mutate input).
 *
 * @param {Array} input
 * @param {(a:any,b:any)=>number} [cmp]
 * @returns {Array}
 */
function mergeSort(input, cmp = defaultComparator) {
  if (!Array.isArray(input)) throw new TypeError("mergeSort: input must be an array");
  const n = input.length;
  if (n <= 1) return input.slice();
  const mid = Math.floor(n / 2);
  const left = mergeSort(input.slice(0, mid), cmp);
  const right = mergeSort(input.slice(mid), cmp);
  return merge(left, right, cmp);
}

function merge(left, right, cmp) {
  const res = [];
  let i = 0, j = 0;
  while (i < left.length && j < right.length) {
    if (cmp(left[i], right[j]) <= 0) res.push(left[i++]);
    else res.push(right[j++]);
  }
  while (i < left.length) res.push(left[i++]);
  while (j < right.length) res.push(right[j++]);
  return res;
}

function defaultComparator(a, b) { return a === b ? 0 : a < b ? -1 : 1; }

module.exports = mergeSort;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(JSON.stringify(mergeSort([5,2,4,7,1,3,2,6])) === JSON.stringify([1,2,2,3,4,5,6,7]), "merge sort failed");
  console.log("merge_sort.js tests passed");
}

