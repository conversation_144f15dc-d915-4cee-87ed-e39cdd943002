"use strict";
/**
 * Insertion Sort (Stable, O(n^2))
 * Builds the sorted array one item at a time by inserting each element into its correct position.
 * Efficient for nearly-sorted data and small arrays.
 *
 * @param {Array} input - Array of comparable items
 * @param {(a:any,b:any)=>number} [cmp] - Optional comparator returning negative/0/positive
 * @returns {Array} new sorted array
 */
function insertionSort(input, cmp = defaultComparator) {
  if (!Array.isArray(input)) throw new TypeError("insertionSort: input must be an array");
  const arr = input.slice();
  for (let i = 1; i < arr.length; i++) {
    const key = arr[i];
    let j = i - 1;
    while (j >= 0 && cmp(arr[j], key) > 0) {
      arr[j + 1] = arr[j];
      j--;
    }
    arr[j + 1] = key;
  }
  return arr;
}

function defaultComparator(a, b) {
  if (a === b) return 0;
  return a < b ? -1 : 1;
}

module.exports = insertionSort;

// Example usage and basic tests
if (require.main === module) {
  const assert = (cond, msg) => { if (!cond) throw new Error(msg); };
  console.log("Insertion Sort examples:");
  console.log(insertionSort([5, 2, 4, 6, 1, 3]));
  assert(JSON.stringify(insertionSort([3, 1, 2])) === JSON.stringify([1, 2, 3]), "Basic sort failed");

  const items = [
    { k: 2, i: 0 },
    { k: 1, i: 1 },
    { k: 2, i: 2 },
    { k: 1, i: 3 },
  ];
  const cmp = (a, b) => a.k - b.k;
  const sorted = insertionSort(items, cmp);
  // stability check for k=1
  assert(sorted[0].i === 1 && sorted[1].i === 3, "Stability failed for k=1");
  console.log("All tests passed for insertion_sort.js");
}

