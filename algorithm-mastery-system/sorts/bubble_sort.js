"use strict";
/**
 * Bubble Sort (Stable, O(n^2))
 * Repeatedly steps through the list, compares adjacent elements and swaps them if in wrong order.
 * For education; prefer Array.prototype.sort in practice for general use.
 *
 * @param {Array} input - Array of comparable items
 * @param {(a:any,b:any)=>number} [cmp] - Optional comparator returning negative/0/positive
 * @returns {Array} new sorted array
 */
function bubbleSort(input, cmp = defaultComparator) {
  if (!Array.isArray(input)) throw new TypeError("bubbleSort: input must be an array");
  const arr = input.slice(); // avoid mutating caller's array
  const n = arr.length;
  let swapped = true;
  for (let i = 0; i < n - 1 && swapped; i++) {
    swapped = false;
    for (let j = 0; j < n - 1 - i; j++) {
      if (cmp(arr[j], arr[j + 1]) > 0) {
        [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
        swapped = true;
      }
    }
  }
  return arr;
}

function defaultComparator(a, b) {
  if (a === b) return 0;
  return a < b ? -1 : 1;
}

module.exports = bubbleSort;

// Example usage and basic tests
if (require.main === module) {
  const assert = (cond, msg) => { if (!cond) throw new Error(msg); };

  console.log("Bubble Sort examples:");
  console.log(bubbleSort([3, 1, 2])); // [1, 2, 3]

  // Stability test with objects
  const items = [
    { k: 2, i: 0 },
    { k: 1, i: 1 },
    { k: 2, i: 2 },
    { k: 1, i: 3 },
  ];
  const cmp = (a, b) => a.k - b.k;
  const sorted = bubbleSort(items, cmp);
  // items with k=1 keep original order (i:1 then i:3)
  assert(sorted[0].i === 1 && sorted[1].i === 3, "Stability failed for k=1");
  assert(sorted[2].i === 0 && sorted[3].i === 2, "Stability failed for k=2");
  console.log("All tests passed for bubble_sort.js");
}

