Sorts (JavaScript)

This folder mirrors TheAlgorithms/Python sorts category with JavaScript implementations. Each file exports a function and includes inline example usage/tests under `if (require.main === module) { ... }`.

Included algorithms
- bubble_sort.js – Simple O(n^2) stable sort.
- insertion_sort.js – O(n^2) stable sort, efficient for nearly-sorted data.
- selection_sort.js – O(n^2) in-place, not stable.
- merge_sort.js – O(n log n) stable divide-and-conquer sort.
- quick_sort.js – Average O(n log n), worst O(n^2). In-place partitioning.
- heap_sort.js – O(n log n) in-place using binary heap.

Usage
- Run any file directly with Node to see example output:
  node bubble_sort.js
- Or import in your code:
  const bubbleSort = require('./bubble_sort');

Notes
- All algorithms avoid mutating the input unless noted; many return a new sorted copy.
- Where in-place is beneficial (quick/heap), an option or separate helper is provided.

