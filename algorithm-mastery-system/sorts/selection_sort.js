"use strict";
/**
 * Selection Sort (In-place, O(n^2), not stable)
 * Repeatedly finds the minimum element from the unsorted part and moves it to the beginning.
 *
 * @param {Array} input
 * @param {(a:any,b:any)=>number} [cmp]
 * @returns {Array} new sorted array
 */
function selectionSort(input, cmp = defaultComparator) {
  if (!Array.isArray(input)) throw new TypeError("selectionSort: input must be an array");
  const arr = input.slice();
  const n = arr.length;
  for (let i = 0; i < n - 1; i++) {
    let minIdx = i;
    for (let j = i + 1; j < n; j++) {
      if (cmp(arr[j], arr[minIdx]) < 0) minIdx = j;
    }
    if (minIdx !== i) [arr[i], arr[minIdx]] = [arr[minIdx], arr[i]];
  }
  return arr;
}

function defaultComparator(a, b) { return a === b ? 0 : a < b ? -1 : 1; }

module.exports = selectionSort;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(JSON.stringify(selectionSort([64,25,12,22,11])) === JSON.stringify([11,12,22,25,64]), "selection sort failed");
  console.log("selection_sort.js tests passed");
}

