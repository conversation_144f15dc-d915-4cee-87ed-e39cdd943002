"use strict";
/**
 * Heap Sort (In-place, O(n log n))
 * Builds a max-heap then repeatedly extracts the maximum to sort the array ascending.
 * Returns a new sorted array; original input unchanged.
 *
 * @param {Array} input
 * @param {(a:any,b:any)=>number} [cmp]
 * @returns {Array}
 */
function heapSort(input, cmp = defaultComparator) {
  if (!Array.isArray(input)) throw new TypeError("heapSort: input must be an array");
  const arr = input.slice();
  const n = arr.length;
  // Build max-heap
  for (let i = Math.floor(n / 2) - 1; i >= 0; i--) heapify(arr, n, i, cmp);
  // One by one extract elements
  for (let end = n - 1; end > 0; end--) {
    [arr[0], arr[end]] = [arr[end], arr[0]]; // move current max to end
    heapify(arr, end, 0, cmp);
  }
  return arr;
}

function heapify(a, heapSize, i, cmp) {
  while (true) {
    let largest = i;
    const l = 2 * i + 1;
    const r = 2 * i + 2;
    if (l < heapSize && cmp(a[l], a[largest]) > 0) largest = l;
    if (r < heapSize && cmp(a[r], a[largest]) > 0) largest = r;
    if (largest === i) break;
    [a[i], a[largest]] = [a[largest], a[i]];
    i = largest;
  }
}

function defaultComparator(a, b) { return a === b ? 0 : a < b ? -1 : 1; }

module.exports = heapSort;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(JSON.stringify(heapSort([4,10,3,5,1])) === JSON.stringify([1,3,4,5,10]), "heap sort failed");
  console.log("heap_sort.js tests passed");
}

