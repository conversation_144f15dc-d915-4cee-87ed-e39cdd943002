"use strict";
/**
 * Quick Sort (Average O(n log n), Worst O(n^2))
 * Uses Lomuto partition scheme. Returns a new sorted array; original input unchanged.
 *
 * @param {Array} input
 * @param {(a:any,b:any)=>number} [cmp]
 * @returns {Array}
 */
function quickSort(input, cmp = defaultComparator) {
  if (!Array.isArray(input)) throw new TypeError("quickSort: input must be an array");
  const arr = input.slice();
  qsort(arr, 0, arr.length - 1, cmp);
  return arr;
}

function qsort(a, lo, hi, cmp) {
  while (lo < hi) {
    const p = partition(a, lo, hi, cmp);
    // Tail call optimization: sort smaller side first to keep stack small
    if (p - 1 - lo < hi - (p + 1)) {
      qsort(a, lo, p - 1, cmp);
      lo = p + 1;
    } else {
      qsort(a, p + 1, hi, cmp);
      hi = p - 1;
    }
  }
}

function partition(a, lo, hi, cmp) {
  const pivot = a[hi];
  let i = lo;
  for (let j = lo; j < hi; j++) {
    if (cmp(a[j], pivot) <= 0) {
      [a[i], a[j]] = [a[j], a[i]];
      i++;
    }
  }
  [a[i], a[hi]] = [a[hi], a[i]];
  return i;
}

function defaultComparator(a, b) { return a === b ? 0 : a < b ? -1 : 1; }

module.exports = quickSort;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const out = quickSort([3,6,8,10,1,2,1]);
  assert(JSON.stringify(out) === JSON.stringify([1,1,2,3,6,8,10]), "quick sort failed");
  console.log("quick_sort.js tests passed");
}

