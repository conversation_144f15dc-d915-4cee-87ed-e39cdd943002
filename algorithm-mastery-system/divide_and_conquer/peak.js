"use strict";
/**
 * Peak Element (Divide and Conquer)
 * A peak element is an element that is not smaller than its neighbors.
 * This implementation finds any one peak using a binary-search-like approach in O(log n).
 *
 * @param {number[]} arr - array of numbers (length >= 1)
 * @returns {{index:number, value:number}} a peak index and value
 */
function findPeak(arr) {
  if (!Array.isArray(arr) || arr.length === 0) throw new TypeError("findPeak: arr must be a non-empty array");
  for (const x of arr) if (typeof x !== "number" || !Number.isFinite(x)) throw new TypeError("findPeak: all elements must be finite numbers");

  let lo = 0, hi = arr.length - 1;
  while (lo < hi) {
    const mid = lo + ((hi - lo) >> 1);
    if (arr[mid] < arr[mid + 1]) lo = mid + 1; // peak on right side
    else hi = mid; // peak on left side (including mid)
  }
  return { index: lo, value: arr[lo] };
}

module.exports = findPeak;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const a = [1, 3, 20, 4, 1, 0];
  const { index, value } = findPeak(a);
  // index 2 (value 20) is a classic peak in this array, though other arrays may have multiple peaks
  assert(value === 20, "peak value should be 20");
  console.log("peak.js tests passed");
}

