Divide and Conquer (JavaScript)

Representative divide-and-conquer algorithms implemented in JavaScript. These mirror classic problems from TheAlgorithms/Python.

Included algorithms (initial set)
- power.js – Fast exponentiation by squaring (recursive divide and conquer)
- peak.js – Find a peak element using binary search-style divide and conquer
- kth_order_statistic.js – Quickselect for k-th smallest element

Usage
- node power.js
- node peak.js
- node kth_order_statistic.js

Notes
- Each file includes input validation and inline examples/tests under `if (require.main === module)`.
- For more D&C examples (closest pair of points, convex hull), this folder can be extended similarly.

