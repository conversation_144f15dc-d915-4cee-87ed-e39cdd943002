"use strict";
/**
 * K-th Order Statistic (Quickselect)
 * Finds the k-th smallest element (0-indexed) in an array in expected linear time.
 *
 * Returns the element value; does not mutate the input array by default (works on a copy).
 * For large data, you can pass { inPlace: true } to allow in-place partitioning.
 *
 * @template T
 * @param {T[]} input
 * @param {number} k - 0 <= k < input.length
 * @param {(a:T,b:T)=>number} [cmp] - comparator
 * @param {{inPlace?: boolean}} [opts]
 * @returns {T}
 */
function quickselect(input, k, cmp = defaultComparator, opts = {}) {
  if (!Array.isArray(input)) throw new TypeError("quickselect: input must be an array");
  if (!Number.isInteger(k) || k < 0 || k >= input.length) throw new RangeError("k out of range");
  const arr = opts.inPlace ? input : input.slice();
  let lo = 0, hi = arr.length - 1;
  while (lo <= hi) {
    const p = partition(arr, lo, hi, cmp);
    if (p === k) return arr[p];
    if (p < k) lo = p + 1; else hi = p - 1;
  }
  // should not reach here
  return arr[k];
}

function partition(a, lo, hi, cmp) {
  // median-of-three pivot selection for better practical performance
  const mid = lo + ((hi - lo) >> 1);
  const pivotIndex = medianOfThree(a, lo, mid, hi, cmp);
  swap(a, pivotIndex, hi);
  const pivot = a[hi];
  let i = lo;
  for (let j = lo; j < hi; j++) {
    if (cmp(a[j], pivot) <= 0) { swap(a, i, j); i++; }
  }
  swap(a, i, hi);
  return i;
}

function medianOfThree(a, i, j, k, cmp) {
  // returns index of median among a[i], a[j], a[k]
  const A = a[i], B = a[j], C = a[k];
  if (cmp(A, B) < 0) {
    if (cmp(B, C) < 0) return j; // A < B < C
    return cmp(A, C) < 0 ? k : i; // A < C <= B or C <= A < B
  } else { // B <= A
    if (cmp(A, C) < 0) return i; // B <= A < C
    return cmp(B, C) < 0 ? k : j; // B < C <= A or C <= B <= A
  }
}

function swap(a, i, j) { const t = a[i]; a[i] = a[j]; a[j] = t; }
function defaultComparator(a, b) { return a === b ? 0 : a < b ? -1 : 1; }

module.exports = quickselect;

// Inline examples/tests
if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const arr = [7, 10, 4, 3, 20, 15];
  assert(quickselect(arr, 2) === 7, "2nd smallest is 7 (0-indexed)");
  assert(quickselect(arr, 0) === 3, "min is 3");
  assert(quickselect(arr, arr.length - 1) === 20, "max is 20");
  console.log("kth_order_statistic.js tests passed");
}

