"use strict";
/**
 * Power via Divide and Conquer (Exponentiation by Squaring - recursive)
 *
 * Supports Number and BigInt (do not mix types).
 * For Numbers, exponent must be a non-negative safe integer.
 */

function power(base, exp) {
  const isBig = typeof base === "bigint" || typeof exp === "bigint";
  if (isBig) return powerBig(BigInt(base), BigInt(exp));
  if (typeof base !== "number" || typeof exp !== "number" || !Number.isSafeInteger(exp) || exp < 0) {
    throw new TypeError("power: base must be number and exp a non-negative safe integer");
  }
  if (exp === 0) return 1;
  if (exp === 1) return base;
  const half = power(base, Math.floor(exp / 2));
  if (exp % 2 === 0) return half * half;
  return half * half * base;
}

function powerBig(base, exp) {
  if (exp < 0n) throw new TypeError("powerBig: exp must be >= 0n");
  if (exp === 0n) return 1n;
  if (exp === 1n) return base;
  const half = powerBig(base, exp / 2n);
  if ((exp & 1n) === 0n) return half * half;
  return half * half * base;
}

module.exports = { power, powerBig };

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(power(2, 10) === 1024, "2^10 = 1024");
  assert(powerBig(2n, 10n) === 1024n, "BigInt 2^10");
  console.log("power.js tests passed");
}

