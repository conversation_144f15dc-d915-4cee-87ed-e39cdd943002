"use strict";
/**
 * Single-Pole Low-Pass IIR Filter
 * y[n] = y[n-1] + alpha * (x[n] - y[n-1])
 * alpha in (0,1]. Smaller alpha => stronger smoothing.
 *
 * @param {number[]} x - input samples
 * @param {number} alpha - smoothing factor in (0,1]
 * @param {number} [y0=0] - initial output
 * @returns {number[]} filtered samples
 */
function lowpass(x, alpha, y0 = 0) {
  if (!Array.isArray(x)) throw new TypeError("x must be an array of numbers");
  if (typeof alpha !== "number" || !(alpha > 0 && alpha <= 1)) throw new TypeError("alpha must be in (0,1]");
  for (const v of x) if (typeof v !== "number" || !Number.isFinite(v)) throw new TypeError("x must contain finite numbers");
  let y = y0;
  const out = new Array(x.length);
  for (let i = 0; i < x.length; i++) {
    y = y + alpha * (x[i] - y);
    out[i] = y;
  }
  return out;
}

module.exports = lowpass;

if (require.main === module) {
  const out = lowpass([0,0,1,1,1,0,0], 0.5);
  console.log(out.map(v => v.toFixed(3)).join(", "));
}

