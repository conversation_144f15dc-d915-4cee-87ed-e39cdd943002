"use strict";
/**
 * Greatest Common Divisor (Euclidean Algorithm)
 * Supports Number and BigInt inputs (but do not mix types).
 */
function gcd(a, b) {
  const isBig = typeof a === "bigint" || typeof b === "bigint";
  if (isBig) return gcdBig(BigInt(a), BigInt(b));
  if (!Number.isFinite(a) || !Number.isFinite(b)) throw new TypeError("gcd: a and b must be finite numbers or BigInt");
  a = Math.trunc(Math.abs(a));
  b = Math.trunc(Math.abs(b));
  while (b !== 0) {
    const t = b;
    b = a % b;
    a = t;
  }
  return a;
}

function gcdBig(a, b) {
  a = a < 0n ? -a : a;
  b = b < 0n ? -b : b;
  while (b !== 0n) {
    const t = b;
    b = a % b;
    a = t;
  }
  return a;
}

module.exports = gcd;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(gcd(48, 18) === 6, "gcd 48,18 = 6");
  assert(gcd(0, 5) === 5, "gcd 0,5 = 5");
  assert(gcd(12n, 18n) === 6n, "BigInt gcd");
  console.log("gcd.js tests passed");
}

