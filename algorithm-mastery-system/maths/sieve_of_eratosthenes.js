"use strict";
/**
 * Sieve of Eratosthenes
 * Generates all prime numbers up to and including n.
 * Time: O(n log log n)  Space: O(n)
 *
 * @param {number} n - upper bound (>= 2)
 * @returns {number[]} sorted list of primes <= n
 */
function sieveOfEratosthenes(n) {
  if (!Number.isInteger(n) || n < 2) throw new TypeError("sieveOfEratosthenes: n must be an integer >= 2");
  const isPrime = new Array(n + 1).fill(true);
  isPrime[0] = isPrime[1] = false;
  for (let p = 2; p * p <= n; p++) {
    if (isPrime[p]) {
      for (let k = p * p; k <= n; k += p) isPrime[k] = false;
    }
  }
  const primes = [];
  for (let i = 2; i <= n; i++) if (isPrime[i]) primes.push(i);
  return primes;
}

module.exports = sieveOfEratosthenes;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(JSON.stringify(sieveOfEratosthenes(2)) === JSON.stringify([2]), "2 -> [2]");
  assert(JSON.stringify(sieveOfEratosthenes(10)) === JSON.stringify([2,3,5,7]), "<=10 primes");
  console.log("sieve_of_eratosthenes.js tests passed");
}

