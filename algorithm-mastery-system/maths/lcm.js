"use strict";
/**
 * Least Common Multiple
 * lcm(a,b) = |a*b| / gcd(a,b)
 * Supports Number and BigInt (do not mix types).
 */
const gcd = require("./gcd");

function lcm(a, b) {
  const isBig = typeof a === "bigint" || typeof b === "bigint";
  if (isBig) return lcmBig(BigInt(a), BigInt(b));
  if (!Number.isFinite(a) || !Number.isFinite(b)) throw new TypeError("lcm: a and b must be finite numbers or BigInt");
  a = Math.trunc(a); b = Math.trunc(b);
  if (a === 0 || b === 0) return 0;
  return Math.abs(a / gcd(a, b) * b);
}

function lcmBig(a, b) {
  if (a === 0n || b === 0n) return 0n;
  const g = gcd(a, b); // works with BigInt
  return (a / g) * (b < 0n ? -b : b);
}

module.exports = lcm;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(lcm(12, 18) === 36, "lcm 12,18 = 36");
  assert(lcm(0, 5) === 0, "lcm 0,5 = 0");
  assert(lcm(21n, 6n) === 42n, "BigInt lcm");
  console.log("lcm.js tests passed");
}

