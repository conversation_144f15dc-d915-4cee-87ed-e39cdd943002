"use strict";
/**
 * Binary Exponentiation (Exponentiation by Squaring)
 *
 * Exports:
 * - pow(base, exp): Number | BigInt -> Number | BigInt
 * - powMod(base, exp, mod): fast modular exponentiation (BigInt-safe)
 *
 * Notes
 * - For Numbers, exp must be a non-negative integer within safe range.
 * - For BigInt, exp must be BigInt >= 0n.
 */

function pow(base, exp) {
  const isBig = typeof base === "bigint" || typeof exp === "bigint";
  if (isBig) return powBig(BigInt(base), BigInt(exp));
  if (typeof base !== "number" || typeof exp !== "number") throw new TypeError("pow: base/exp must be numbers or BigInt");
  if (!Number.isSafeInteger(exp) || exp < 0) throw new TypeError("pow: exp must be a non-negative safe integer");
  let result = 1;
  let b = base;
  let e = exp;
  while (e > 0) {
    if (e & 1) result *= b;
    b *= b;
    e >>= 1;
  }
  return result;
}

function powBig(base, exp) {
  if (exp < 0n) throw new TypeError("powBig: exp must be >= 0n");
  let result = 1n;
  let b = base;
  let e = exp;
  while (e > 0n) {
    if (e & 1n) result *= b;
    b *= b;
    e >>= 1n;
  }
  return result;
}

function powMod(base, exp, mod) {
  // modular exponentiation with BigInt
  base = BigInt(base);
  exp = BigInt(exp);
  mod = BigInt(mod);
  if (mod === 1n) return 0n;
  if (exp < 0n) throw new TypeError("powMod: exp must be >= 0n");
  let result = 1n;
  let b = ((base % mod) + mod) % mod;
  let e = exp;
  while (e > 0n) {
    if (e & 1n) result = (result * b) % mod;
    b = (b * b) % mod;
    e >>= 1n;
  }
  return result;
}

module.exports = { pow, powBig, powMod };

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(pow(2, 10) === 1024, "2^10 = 1024");
  assert(powBig(2n, 10n) === 1024n, "BigInt 2^10 = 1024");
  assert(powMod(2n, 10n, 1000n) === 24n, "2^10 mod 1000 = 24");
  console.log("binary_exponentiation.js tests passed");
}

