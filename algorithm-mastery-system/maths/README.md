Maths (JavaScript)

This folder mirrors selected math utilities from TheAlgorithms/Python with JavaScript implementations.

Included (initial set)
- gcd.js – Euclidean algorithm for greatest common divisor
- lcm.js – Least common multiple using gcd
- is_prime.js – Deterministic primality checks for 32-bit integers
- sieve_of_eratosthenes.js – Generate primes up to n
- binary_exponentiation.js – Fast power (modular and non-modular)

Usage
- node gcd.js
- const { powMod } = require('./binary_exponentiation');

Notes
- All functions validate inputs.
- For large integer math beyond 2^53-1, consider using BigInt; modular variant supports BigInt too.

