"use strict";
/**
 * Primality tests
 * - isPrime: Deterministic Miller-Rabin for 64-bit range using BigInt
 * - isPrimeSmall: Trial division up to sqrt(n) for small numbers (Number)
 *
 * Notes
 * - Accepts Number (safe integer) or BigInt. Numbers are converted to BigInt.
 * - Negative numbers, 0, 1 are not prime.
 */

const SMALL_BASES = [2n, 3n, 5n, 7n, 11n, 13n, 17n]; // deterministic for 64-bit

function isPrime(n) {
  // normalize to BigInt
  if (typeof n === "number") {
    if (!Number.isSafeInteger(n)) throw new TypeError("isPrime: number must be a safe integer");
    n = BigInt(n);
  } else if (typeof n !== "bigint") {
    throw new TypeError("isPrime: n must be Number or BigInt");
  }

  if (n < 2n) return false;
  const smallPrimes = [2n,3n,5n,7n,11n,13n,17n,19n,23n,29n,31n,37n];
  for (const p of smallPrimes) {
    if (n === p) return true;
    if (n % p === 0n) return n === p;
  }

  // write n-1 as d*2^s
  let d = n - 1n; let s = 0n;
  while ((d & 1n) === 0n) { d >>= 1n; s++; }

  for (const a of SMALL_BASES) {
    if (a >= n) continue;
    if (!millerRabinCheck(n, d, s, a)) return false;
  }
  return true;
}

function millerRabinCheck(n, d, s, a) {
  let x = powMod(a, d, n);
  if (x === 1n || x === n - 1n) return true;
  for (let r = 1n; r < s; r++) {
    x = (x * x) % n;
    if (x === n - 1n) return true;
  }
  return false;
}

function powMod(base, exp, mod) {
  if (mod === 1n) return 0n;
  let result = 1n;
  base %= mod;
  while (exp > 0n) {
    if (exp & 1n) result = (result * base) % mod;
    base = (base * base) % mod;
    exp >>= 1n;
  }
  return result;
}

function isPrimeSmall(n) {
  if (!Number.isSafeInteger(n)) throw new TypeError("isPrimeSmall: n must be a safe integer");
  if (n < 2) return false;
  if (n % 2 === 0) return n === 2;
  for (let d = 3; d * d <= n; d += 2) if (n % d === 0) return false;
  return true;
}

module.exports = { isPrime, isPrimeSmall };

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(isPrime(2), "2 prime");
  assert(isPrime(3), "3 prime");
  assert(!isPrime(1), "1 not prime");
  assert(isPrime(97), "97 prime");
  assert(!isPrime(221), "221 = 13*17 not prime");
  // BigInt large prime test (e.g., 2^61-1 is prime)
  const p = (1n << 61n) - 1n;
  assert(isPrime(p), "2^61-1 prime");
  console.log("is_prime.js tests passed");
}

