"use strict";
/**
 * Minimum Coin Change (Dynamic Programming)
 * Given coin denominations and an amount, return the minimum number of coins to make the amount,
 * or -1 if it's not possible.
 * Time: O(n * amount)  Space: O(amount)
 *
 * @param {number[]} coins - distinct positive integers
 * @param {number} amount - non-negative integer
 * @returns {number}
 */
function minimumCoinChange(coins, amount) {
  if (!Array.isArray(coins)) throw new TypeError("coins must be an array");
  if (!Number.isInteger(amount) || amount < 0) throw new TypeError("amount must be a non-negative integer");
  for (const c of coins) if (!Number.isInteger(c) || c <= 0) throw new TypeError("coin values must be positive integers");
  const INF = amount + 1;
  const dp = new Array(amount + 1).fill(INF);
  dp[0] = 0;
  for (const c of coins) {
    for (let a = c; a <= amount; a++) {
      dp[a] = Math.max(0, Math.min(dp[a], dp[a - c] + 1));
    }
  }
  return dp[amount] === INF ? -1 : dp[amount];
}

module.exports = minimumCoinChange;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(minimumCoinChange([1, 2, 5], 11) === 3, "11 -> 5+5+1");
  assert(minimumCoinChange([2], 3) === -1, "impossible");
  console.log("minimum_coin_change.js tests passed");
}

