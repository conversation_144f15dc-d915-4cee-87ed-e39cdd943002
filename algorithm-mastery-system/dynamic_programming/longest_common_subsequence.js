"use strict";
/**
 * Longest Common Subsequence (LCS)
 * Returns the LCS length and one LCS string reconstruction.
 * Time: O(n*m)  Space: O(n*m)
 *
 * @param {string} a
 * @param {string} b
 * @returns {{length:number, sequence:string}}
 */
function longestCommonSubsequence(a, b) {
  if (typeof a !== "string" || typeof b !== "string") throw new TypeError("LCS: inputs must be strings");
  const n = a.length, m = b.length;
  const dp = Array.from({ length: n + 1 }, () => new Array(m + 1).fill(0));
  for (let i = 1; i <= n; i++) {
    for (let j = 1; j <= m; j++) {
      if (a[i - 1] === b[j - 1]) dp[i][j] = dp[i - 1][j - 1] + 1;
      else dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
    }
  }
  // reconstruct
  let i = n, j = m; const chars = [];
  while (i > 0 && j > 0) {
    if (a[i - 1] === b[j - 1]) { chars.push(a[i - 1]); i--; j--; }
    else if (dp[i - 1][j] >= dp[i][j - 1]) i--; else j--;
  }
  chars.reverse();
  return { length: dp[n][m], sequence: chars.join("") };
}

module.exports = longestCommonSubsequence;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const { length, sequence } = longestCommonSubsequence("ABCBDAB", "BDCABA");
  assert(length === 4, "LCS length should be 4");
  assert(["BCBA","BDAB","BCAB","BDCA"].includes(sequence), "LCS sequence plausible");
  console.log("longest_common_subsequence.js tests passed");
}

