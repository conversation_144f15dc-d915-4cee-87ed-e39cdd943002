Dynamic Programming (JavaScript)

This folder mirrors key dynamic programming algorithms from TheAlgorithms/Python with JavaScript implementations. Each file exports one or more functions and includes inline examples/tests.

Included algorithms (initial set)
- fibonacci.js – classic DP (memoized and bottom-up variants)
- knapsack.js – 0/1 knapsack (maximum value)
- longest_common_subsequence.js – LCS length and reconstruction
- minimum_coin_change.js – minimum number of coins to form an amount

Usage
- node fibonacci.js
- const knapsack01 = require('./knapsack');

Notes
- Implementations aim for clarity first; some offer both memoized and tabulation solutions.
- All functions validate inputs and avoid mutating caller data.

