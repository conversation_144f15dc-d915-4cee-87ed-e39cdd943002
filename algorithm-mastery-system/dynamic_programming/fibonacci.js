"use strict";
/**
 * <PERSON><PERSON><PERSON><PERSON> (Dynamic Programming)
 * Provides memoized (top-down) and tabulation (bottom-up) implementations.
 *
 * Both variants validate n >= 0 and return the nth <PERSON><PERSON> number with F(0)=0, F(1)=1.
 */

function fibMemo(n, memo = new Map([[0, 0], [1, 1]])) {
  if (!Number.isInteger(n) || n < 0) throw new TypeError("fibMemo: n must be a non-negative integer");
  if (memo.has(n)) return memo.get(n);
  const val = fibMemo(n - 1, memo) + fibMemo(n - 2, memo);
  memo.set(n, val);
  return val;
}

function fibTab(n) {
  if (!Number.isInteger(n) || n < 0) throw new TypeError("fibTab: n must be a non-negative integer");
  if (n <= 1) return n;
  let a = 0, b = 1;
  for (let i = 2; i <= n; i++) {
    const c = a + b;
    a = b; b = c;
  }
  return b;
}

module.exports = { fibMemo, fibTab };

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  assert(fibMemo(0) === 0 && fibTab(0) === 0, "F(0)=0");
  assert(fibMemo(1) === 1 && fibTab(1) === 1, "F(1)=1");
  assert(fibMemo(10) === 55 && fibTab(10) === 55, "F(10)=55");
  console.log("fibonacci.js tests passed");
}

