"use strict";
/**
 * 0/1 Knapsack (Dynamic Programming)
 * Given weights and values of n items, put these items in a knapsack of capacity W to get the maximum total value in the knapsack.
 * Time: O(n*W), Space: O(W) optimized.
 *
 * @param {number[]} weights - item weights (non-negative integers)
 * @param {number[]} values - item values (non-negative numbers)
 * @param {number} W - capacity (non-negative integer)
 * @returns {number} maximum achievable value
 */
function knapsack01(weights, values, W) {
  if (!Array.isArray(weights) || !Array.isArray(values)) throw new TypeError("knapsack01: weights/values must be arrays");
  if (weights.length !== values.length) throw new Error("weights and values must have same length");
  if (!Number.isInteger(W) || W < 0) throw new TypeError("W must be a non-negative integer");
  const n = weights.length;
  const dp = new Array(W + 1).fill(0);
  for (let i = 0; i < n; i++) {
    const wt = weights[i];
    const val = values[i];
    if (!Number.isInteger(wt) || wt < 0) throw new TypeError("weight must be non-negative integer");
    if (typeof val !== "number" || val < 0) throw new TypeError("value must be non-negative number");
    for (let w = W; w >= wt; w--) {
      dp[w] = Math.max(dp[w], dp[w - wt] + val);
    }
  }
  return dp[W];
}

module.exports = knapsack01;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const weights = [10, 20, 30];
  const values = [60, 100, 120];
  assert(knapsack01(weights, values, 50) === 220, "Expected 220");
  console.log("knapsack.js tests passed");
}

