"use strict";
/**
 * Max <PERSON> (<PERSON><PERSON> variant of <PERSON>)
 * Graph is represented as adjacency matrix or adjacency object with capacities.
 * This implementation uses an adjacency matrix for simplicity.
 *
 * @param {number[][]} capacity - capacity[u][v] is capacity from u to v (non-negative numbers)
 * @param {number} s - source index
 * @param {number} t - sink index
 * @returns {number} maximum flow value
 */
function maxFlow(capacity, s, t) {
  if (!Array.isArray(capacity) || capacity.length === 0) throw new TypeError("capacity must be a non-empty 2D array");
  const n = capacity.length;
  for (const row of capacity) if (!Array.isArray(row) || row.length !== n) throw new TypeError("capacity must be n x n");
  if (![s, t].every(x => Number.isInteger(x) && x >= 0 && x < n)) throw new RangeError("s and t must be valid indices");
  if (s === t) throw new Error("source must differ from sink");

  // build residual graph
  const residual = capacity.map(row => row.slice());
  const parent = new Array(n);
  let flow = 0;

  while (bfs(residual, s, t, parent)) {
    // find bottleneck
    let pathFlow = Infinity;
    for (let v = t; v !== s; v = parent[v]) {
      const u = parent[v];
      pathFlow = Math.min(pathFlow, residual[u][v]);
    }
    // update residual capacities
    for (let v = t; v !== s; v = parent[v]) {
      const u = parent[v];
      residual[u][v] -= pathFlow;
      residual[v][u] += pathFlow; // add reverse capacity
    }
    flow += pathFlow;
  }

  return flow;
}

function bfs(residual, s, t, parent) {
  const n = residual.length;
  const visited = new Array(n).fill(false);
  const q = [s];
  visited[s] = true;
  parent[s] = -1;
  while (q.length) {
    const u = q.shift();
    for (let v = 0; v < n; v++) {
      if (!visited[v] && residual[u][v] > 0) {
        parent[v] = u;
        visited[v] = true;
        if (v === t) return true;
        q.push(v);
      }
    }
  }
  return false;
}

module.exports = maxFlow;

if (require.main === module) {
  const c = [
    [0, 16, 13, 0, 0, 0],
    [0, 0, 10, 12, 0, 0],
    [0, 4, 0, 0, 14, 0],
    [0, 0, 9, 0, 0, 20],
    [0, 0, 0, 7, 0, 4],
    [0, 0, 0, 0, 0, 0],
  ];
  console.log("Max flow:", maxFlow(c, 0, 5)); // Expected 23
}

