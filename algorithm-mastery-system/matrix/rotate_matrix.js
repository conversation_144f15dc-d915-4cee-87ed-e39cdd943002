"use strict";
/**
 * Rotate n x n matrix 90 degrees clockwise in place.
 * Matrix represented as array of arrays of numbers.
 *
 * @param {number[][]} A
 * @returns {number[][]} the same matrix A (rotated)
 */
function rotateMatrix(A) {
  if (!Array.isArray(A) || A.length === 0) throw new TypeError("A must be a non-empty 2D array");
  const n = A.length;
  for (const row of A) if (!Array.isArray(row) || row.length !== n) throw new TypeError("A must be n x n");
  // transpose
  for (let i = 0; i < n; i++) {
    for (let j = i + 1; j < n; j++) {
      const t = A[i][j]; A[i][j] = A[j][i]; A[j][i] = t;
    }
  }
  // reverse each row
  for (let i = 0; i < n; i++) A[i].reverse();
  return A;
}

module.exports = rotateMatrix;

if (require.main === module) {
  const assertEq = (a, b) => { if (JSON.stringify(a) !== JSON.stringify(b)) throw new Error(`${JSON.stringify(a)} != ${JSON.stringify(b)}`); };
  const A = [ [1,2,3], [4,5,6], [7,8,9] ];
  rotateMatrix(A);
  assertEq(A, [ [7,4,1], [8,5,2], [9,6,3] ]);
  console.log("rotate_matrix.js tests passed");
}

