"use strict";
/**
 * Polygon Area via Shoelace Formula
 * Input: array of points [{x, y}, ...] representing a simple polygon in order (clockwise or counterclockwise)
 * Output: non-negative area
 * Time: O(n)
 */
function polygonArea(points) {
  if (!Array.isArray(points) || points.length < 3) throw new TypeError("polygonArea: points must be an array of length >= 3");
  let sum = 0;
  for (let i = 0; i < points.length; i++) {
    const p = points[i];
    const q = points[(i + 1) % points.length];
    if (!isPoint(p) || !isPoint(q)) throw new TypeError("polygonArea: each point must be {x:number, y:number}");
    sum += p.x * q.y - p.y * q.x;
  }
  return Math.abs(sum) / 2;
}

function isPoint(o) { return o && typeof o.x === "number" && typeof o.y === "number"; }

module.exports = polygonArea;

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const triangle = [{x:0,y:0},{x:4,y:0},{x:0,y:3}];
  assert(polygonArea(triangle) === 6, "area should be 6");
  const square = [{x:0,y:0},{x:1,y:0},{x:1,y:1},{x:0,y:1}];
  assert(polygonArea(square) === 1, "area 1");
  console.log("polygon_area_shoelace.js tests passed");
}

