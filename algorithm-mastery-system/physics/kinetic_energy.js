"use strict";
/**
 * Kinetic Energy
 * KE = 1/2 m v^2
 * @param {number} m - mass (kg)
 * @param {number} v - velocity (m/s)
 * @returns {number} energy (J)
 */
function kineticEnergy(m, v) {
  if (!(typeof m === "number" && Number.isFinite(m) && m >= 0)) throw new TypeError("m must be a non-negative number");
  if (!(typeof v === "number" && Number.isFinite(v))) throw new TypeError("v must be a finite number");
  return 0.5 * m * v * v;
}

module.exports = kineticEnergy;

if (require.main === module) {
  const ke = kineticEnergy(10, 3);
  console.log(ke); // 45 J
}

