"use strict";
/**
 * Shortest Job First (SJF) - Non-preemptive
 * Minimizes average waiting time by sorting jobs by burst time.
 * Input: array of job objects { id, burstTime } with positive burst times.
 * Output: { schedule: Array<{id, start, finish}>, avgWaitingTime, avgTurnaroundTime }
 */
function shortestJobFirst(jobs) {
  if (!Array.isArray(jobs) || jobs.length === 0) throw new TypeError("jobs must be a non-empty array");
  for (const j of jobs) if (!j || typeof j.id === "undefined" || typeof j.burstTime !== "number" || j.burstTime <= 0) throw new TypeError("Each job must be {id, burstTime>0}");

  const sorted = jobs.slice().sort((a, b) => a.burstTime - b.burstTime);
  const schedule = [];
  let time = 0;
  let totalWait = 0;
  let totalTurn = 0;

  for (const job of sorted) {
    const start = time;
    const finish = start + job.burstTime;
    schedule.push({ id: job.id, start, finish });
    const waiting = start; // since all arrive at time 0 in this simple model
    const turnaround = finish; // arrival 0 -> finish
    totalWait += waiting;
    totalTurn += turnaround;
    time = finish;
  }

  const n = sorted.length;
  return {
    schedule,
    avgWaitingTime: totalWait / n,
    avgTurnaroundTime: totalTurn / n,
  };
}

module.exports = shortestJobFirst;

if (require.main === module) {
  const jobs = [ {id:'J1', burstTime: 6}, {id:'J2', burstTime: 8}, {id:'J3', burstTime: 7}, {id:'J4', burstTime: 3} ];
  const res = shortestJobFirst(jobs);
  console.log(res);
}

