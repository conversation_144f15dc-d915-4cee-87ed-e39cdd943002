"use strict";
/**
 * Haversine Distance
 * Computes the great-circle distance between two points specified by latitude/longitude in degrees.
 * Returns distance in kilometers by default.
 */
function haversineDistance(lat1, lon1, lat2, lon2, radiusKm = 6371) {
  for (const x of [lat1, lon1, lat2, lon2, radiusKm]) if (typeof x !== "number" || !Number.isFinite(x)) throw new TypeError("All inputs must be finite numbers");
  const toRad = d => (d * Math.PI) / 180;
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);
  const a = Math.sin(dLat / 2) ** 2 + Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) ** 2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return radiusKm * c;
}

module.exports = haversineDistance;

if (require.main === module) {
  const d = haversineDistance(36.12, -86.67, 33.94, -118.40);
  console.log(d.toFixed(3)); // ~2887.26 km
}

