"use strict";
/**
 * <PERSON>'s Game of Life - Single Step
 * Input: grid (2D boolean array) where true=alive, false=dead
 * Output: new grid after applying Life rules
 *
 * Rules:
 * - Any live cell with 2 or 3 live neighbours survives.
 * - Any dead cell with exactly 3 live neighbours becomes a live cell.
 * - All other live cells die; all other dead cells stay dead.
 */
function gameOfLifeStep(grid) {
  if (!Array.isArray(grid) || grid.length === 0) throw new TypeError("grid must be a non-empty 2D array");
  const rows = grid.length;
  const cols = grid[0].length;
  for (const row of grid) if (!Array.isArray(row) || row.length !== cols) throw new TypeError("grid must be rectangular");
  const next = Array.from({ length: rows }, () => new Array(cols).fill(false));
  const dirs = [-1, 0, 1];
  for (let r = 0; r < rows; r++) {
    for (let c = 0; c < cols; c++) {
      let live = 0;
      for (const dr of dirs) for (const dc of dirs) {
        if (dr === 0 && dc === 0) continue;
        const nr = r + dr, nc = c + dc;
        if (nr >= 0 && nr < rows && nc >= 0 && nc < cols && !!grid[nr][nc]) live++;
      }
      next[r][c] = grid[r][c] ? (live === 2 || live === 3) : (live === 3);
    }
  }
  return next;
}

module.exports = gameOfLifeStep;

if (require.main === module) {
  const G = [
    [false, true, false],
    [false, true, false],
    [false, true, false],
  ];
  console.log(gameOfLifeStep(G));
}

