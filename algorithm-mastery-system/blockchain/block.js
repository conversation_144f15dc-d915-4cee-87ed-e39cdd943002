"use strict";
const crypto = require('crypto');

/**
 * A simple Block for a blockchain demonstration.
 * Includes index, timestamp, data, previous hash, and its own hash.
 */
class Block {
  constructor(index, timestamp, data, previousHash = '') {
    this.index = index;
    this.timestamp = timestamp;
    this.data = data;
    this.previousHash = previousHash;
    this.hash = this.calculateHash();
  }

  calculateHash() {
    const input = String(this.index) + this.timestamp + JSON.stringify(this.data) + this.previousHash;
    return crypto.createHash('sha256').update(input).digest('hex');
  }
}

module.exports = Block;

if (require.main === module) {
  const genesisBlock = new Block(0, Date.now(), { info: "Genesis Block" }, "0");
  console.log("Genesis Block:", genesisBlock);

  const secondBlock = new Block(1, Date.now(), { amount: 100 }, genesisBlock.hash);
  console.log("Second Block:", secondBlock);
}

