"use strict";
/**
 * Gaussian Elimination with Partial Pivoting
 * Solves linear system A x = b for x.
 *
 * @param {number[][]} A - n x n matrix
 * @param {number[]} b - length n vector
 * @returns {number[]} solution vector x
 */
function gaussianElimination(A, b) {
  if (!Array.isArray(A) || !Array.isArray(b)) throw new TypeError("A and b must be arrays");
  const n = A.length;
  if (n === 0 || b.length !== n) throw new Error("Dimensions mismatch");
  for (const row of A) if (!Array.isArray(row) || row.length !== n) throw new Error("A must be square n x n");

  // Build augmented matrix [A|b]
  const M = A.map((row, i) => [...row.map(x => num(x)), num(b[i])]);

  // Forward elimination with partial pivoting
  for (let k = 0; k < n; k++) {
    // pivot: find row with max |M[i][k]| for i >= k
    let iMax = k;
    for (let i = k + 1; i < n; i++) if (Math.abs(M[i][k]) > Math.abs(M[iMax][k])) iMax = i;
    if (Math.abs(M[iMax][k]) < 1e-12) throw new Error("Matrix is singular or nearly singular");
    if (iMax !== k) [M[k], M[iMax]] = [M[iMax], M[k]];

    // Eliminate below
    for (let i = k + 1; i < n; i++) {
      const f = M[i][k] / M[k][k];
      for (let j = k; j <= n; j++) M[i][j] -= f * M[k][j];
    }
  }

  // Back substitution
  const x = new Array(n).fill(0);
  for (let i = n - 1; i >= 0; i--) {
    let sum = M[i][n];
    for (let j = i + 1; j < n; j++) sum -= M[i][j] * x[j];
    x[i] = sum / M[i][i];
  }
  return x;
}

function num(v) { if (typeof v !== "number" || !Number.isFinite(v)) throw new TypeError("matrix entries must be finite numbers"); return v; }

module.exports = gaussianElimination;

if (require.main === module) {
  const assertClose = (a, b, eps=1e-9) => { if (Math.abs(a - b) > eps) throw new Error(`${a} !~ ${b}`); };
  const A = [ [2,1,-1], [-3,-1,2], [-2,1,2] ];
  const b = [8, -11, -3];
  const x = gaussianElimination(A, b);
  assertClose(x[0], 2); assertClose(x[1], 3); assertClose(x[2], -1);
  console.log("gaussian_elimination.js tests passed");
}

