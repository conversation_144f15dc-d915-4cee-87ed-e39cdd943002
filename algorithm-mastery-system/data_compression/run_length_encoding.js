"use strict";
/**
 * Run-Length Encoding (RLE)
 * Encodes a string by collapsing runs of the same character into count+char.
 * Example: AAAABBBCCDAA -> 4A3B2C1D2A
 * Includes a decoder.
 */
function encodeRLE(s) {
  if (typeof s !== "string") throw new TypeError("encodeRLE: input must be a string");
  if (s.length === 0) return "";
  let out = "";
  let count = 1;
  for (let i = 1; i <= s.length; i++) {
    if (i < s.length && s[i] === s[i - 1]) count++;
    else { out += String(count) + s[i - 1]; count = 1; }
  }
  return out;
}

function decodeRLE(s) {
  if (typeof s !== "string") throw new TypeError("decodeRLE: input must be a string");
  let out = "";
  let num = "";
  for (const ch of s) {
    if (ch >= '0' && ch <= '9') num += ch;
    else {
      const cnt = num === "" ? 1 : Number(num);
      if (!Number.isFinite(cnt) || cnt < 0) throw new Error("Invalid RLE count");
      out += ch.repeat(cnt);
      num = "";
    }
  }
  if (num !== "") throw new Error("Dangling count at end of RLE string");
  return out;
}

module.exports = { encodeRLE, decodeRLE };

if (require.main === module) {
  const assert = (c, m) => { if (!c) throw new Error(m); };
  const enc = encodeRLE("AAAABBBCCDAA");
  assert(enc === "4A3B2C1D2A", enc);
  assert(decodeRLE(enc) === "AAAABBBCCDAA");
  assert(encodeRLE("") === "");
  console.log("run_length_encoding.js tests passed");
}

