"use strict";
/**
 * AND Gate
 * Returns logical AND of two booleans a && b.
 */
function AND(a, b) {
  if (typeof a !== "boolean" || typeof b !== "boolean") throw new TypeError("AND: inputs must be boolean");
  return a && b;
}

module.exports = AND;

if (require.main === module) {
  const assert = (x, m) => { if (!x) throw new Error(m); };
  assert(AND(true, true) === true);
  assert(AND(true, false) === false);
  console.log("and_gate.js tests passed");
}

