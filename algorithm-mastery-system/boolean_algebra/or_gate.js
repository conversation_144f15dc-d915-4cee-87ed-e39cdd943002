"use strict";
/**
 * OR Gate
 * Returns logical OR of two booleans a || b.
 */
function OR(a, b) {
  if (typeof a !== "boolean" || typeof b !== "boolean") throw new TypeError("OR: inputs must be boolean");
  return a || b;
}

module.exports = OR;

if (require.main === module) {
  const assert = (x, m) => { if (!x) throw new Error(m); };
  assert(OR(false, false) === false);
  assert(OR(true, false) === true);
  console.log("or_gate.js tests passed");
}

