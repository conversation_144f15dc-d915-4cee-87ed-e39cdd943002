"use strict";
/**
 * NOT Gate
 * Returns logical negation of a boolean.
 */
function NOT(a) {
  if (typeof a !== "boolean") throw new TypeError("NOT: input must be boolean");
  return !a;
}

module.exports = NOT;

if (require.main === module) {
  const assert = (x, m) => { if (!x) throw new Error(m); };
  assert(NOT(true) === false);
  assert(NOT(false) === true);
  console.log("not_gate.js tests passed");
}

