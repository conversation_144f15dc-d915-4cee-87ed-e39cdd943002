# 🐳 Docker Compose - Production-Ready Multi-Service Setup
#
# Orchestrates the complete enterprise platform with:
# - API Gateway (NestJS)
# - AI Service (FastAPI)
# - PostgreSQL database
# - Redis cache

version: '3.8'

services:
  # ================================
  # 🗄️ Database Services
  # ================================

  postgres:
    image: postgres:15-alpine
    container_name: enterprise-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DATABASE_NAME:-enterprise_db}
      POSTGRES_USER: ${DATABASE_USER:-enterprise_user}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-enterprise_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "${DATABASE_PORT:-5432}:5432"
    networks:
      - enterprise-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USER:-enterprise_user} -d ${DATABASE_NAME:-enterprise_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis:
    image: redis:7-alpine
    container_name: enterprise-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - enterprise-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # ================================
  # 🚀 Application Services
  # ================================

  api-gateway:
    build:
      context: .
      dockerfile: apps/api-gateway/Dockerfile
      target: production
    container_name: enterprise-api-gateway
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: ${DATABASE_NAME:-enterprise_db}
      DATABASE_USER: ${DATABASE_USER:-enterprise_user}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-enterprise_password}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-your-super-secret-refresh-key-change-in-production}
      AI_SERVICE_URL: http://ai-service:8000
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,http://localhost:3001}
    ports:
      - "${API_GATEWAY_PORT:-3000}:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - enterprise-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  ai-service:
    build:
      context: .
      dockerfile: services/ai-service/Dockerfile
      target: production
    container_name: enterprise-ai-service
    restart: unless-stopped
    environment:
      ENVIRONMENT: production
      HOST: 0.0.0.0
      PORT: 8000
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: ${DATABASE_NAME:-enterprise_db}
      DATABASE_USER: ${DATABASE_USER:-enterprise_user}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-enterprise_password}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      HUGGINGFACE_API_KEY: ${HUGGINGFACE_API_KEY}
      LOG_LEVEL: INFO
    ports:
      - "${AI_SERVICE_PORT:-8000}:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - enterprise-network
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

# ================================
# 📦 Volumes
# ================================

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

# ================================
# 🌐 Networks
# ================================

networks:
  enterprise-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
