# 📊 **Monitoring & Observability Stack**

> **Comprehensive observability with Prometheus, Grafana, ELK Stack, and distributed tracing**

## 📋 **Overview**

The `monitoring/` directory contains all **observability and monitoring configurations** for comprehensive system monitoring, alerting, and performance analysis. Built with industry-standard tools and enterprise-grade practices.

## 🏗️ **Architecture Overview**

```
monitoring/
├── grafana/            # 📊 Visualization & Dashboards
└── prometheus/         # 📈 Metrics Collection & Alerting
```

## 📁 **Monitoring Components & Guides**

| Component | Technology Stack | Purpose | README Guide | Status |
|-----------|------------------|---------|--------------|--------|
| [📊 **grafana/**](grafana/README.md) | Grafana + Dashboards | Visualization, dashboards, alerts | [📖 Guide](grafana/README.md) | 🔄 |
| [📈 **prometheus/**](prometheus/README.md) | Prometheus + AlertManager | Metrics collection, alerting | [📖 Guide](prometheus/README.md) | 🔄 |

## 📊 **Grafana Dashboards**

**Comprehensive visualization** and alerting:

### **🔧 Key Features**
- ✅ **Pre-built Dashboards** - Application and infrastructure metrics
- ✅ **Real-time Monitoring** - Live data visualization
- ✅ **Custom Alerts** - Configurable alerting rules
- ✅ **Multi-Data Sources** - Prometheus, Elasticsearch, databases
- ✅ **User Management** - Multi-layered access control (RBAC, ABAC, ReBAC)
- ✅ **Dashboard Templates** - Reusable dashboard components

### **📁 Structure**
```
grafana/
├── dashboards/          # Dashboard configurations
│   ├── application/
│   │   ├── api-gateway.json
│   │   ├── microservices.json
│   │   └── ai-service.json
│   ├── infrastructure/
│   │   ├── kubernetes.json
│   │   ├── database.json
│   │   └── system-overview.json
│   ├── business/
│   │   ├── user-analytics.json
│   │   ├── performance-kpis.json
│   │   └── revenue-metrics.json
│   └── security/
│       ├── security-overview.json
│       └── audit-logs.json
├── datasources/         # Data source configurations
│   ├── prometheus.yaml
│   ├── elasticsearch.yaml
│   └── postgresql.yaml
├── provisioning/        # Grafana provisioning
│   ├── dashboards.yaml
│   ├── datasources.yaml
│   └── notifiers.yaml
├── plugins/             # Custom plugins
│   ├── custom-panels/
│   └── data-sources/
└── config/              # Grafana configuration
    ├── grafana.ini
    ├── ldap.toml
    └── custom.ini
```

### **📊 Dashboard Examples**

#### **API Gateway Dashboard**
```json
{
  "dashboard": {
    "id": null,
    "title": "API Gateway Metrics",
    "tags": ["api", "gateway", "performance"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{service=\"api-gateway\"}[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ],
        "yAxes": [
          {
            "label": "Requests/sec",
            "min": 0
          }
        ]
      },
      {
        "id": 2,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{service=\"api-gateway\"}[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{service=\"api-gateway\"}[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "id": 3,
        "title": "Error Rate",
        "type": "singlestat",
        "targets": [
          {
            "expr": "rate(http_requests_total{service=\"api-gateway\",status=~\"5..\"}[5m]) / rate(http_requests_total{service=\"api-gateway\"}[5m]) * 100",
            "legendFormat": "Error Rate %"
          }
        ],
        "thresholds": "1,5",
        "colorBackground": true
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

#### **System Overview Dashboard**
```json
{
  "dashboard": {
    "title": "System Overview",
    "panels": [
      {
        "title": "CPU Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "Memory Usage %"
          }
        ]
      },
      {
        "title": "Disk I/O",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(node_disk_read_bytes_total[5m])",
            "legendFormat": "Read {{device}}"
          },
          {
            "expr": "rate(node_disk_written_bytes_total[5m])",
            "legendFormat": "Write {{device}}"
          }
        ]
      }
    ]
  }
}
```

### **🚨 Alert Configuration**
```yaml
# grafana/provisioning/notifiers.yaml
notifiers:
  - name: slack-alerts
    type: slack
    uid: slack-alerts
    org_id: 1
    is_default: true
    settings:
      url: ${SLACK_WEBHOOK_URL}
      channel: "#alerts"
      username: "Grafana"
      title: "Alert: {{range .Alerts}}{{.AlertName}}{{end}}"
      text: "{{range .Alerts}}{{.Annotations.summary}}{{end}}"

  - name: email-alerts
    type: email
    uid: email-alerts
    org_id: 1
    settings:
      addresses: "<EMAIL>;<EMAIL>"
      subject: "Grafana Alert: {{.CommonLabels.alertname}}"
```

## 📈 **Prometheus Monitoring**

**Metrics collection** and alerting:

### **🔧 Key Features**
- ✅ **Service Discovery** - Automatic target discovery
- ✅ **Custom Metrics** - Application-specific metrics
- ✅ **Alert Rules** - Configurable alerting conditions
- ✅ **High Availability** - Clustered Prometheus setup
- ✅ **Long-term Storage** - Thanos integration
- ✅ **Federation** - Multi-cluster monitoring

### **📁 Structure**
```
prometheus/
├── config/              # Prometheus configuration
│   ├── prometheus.yml
│   ├── alert_rules.yml
│   └── recording_rules.yml
├── rules/               # Alert and recording rules
│   ├── application/
│   │   ├── api-gateway.yml
│   │   ├── microservices.yml
│   │   └── ai-service.yml
│   ├── infrastructure/
│   │   ├── kubernetes.yml
│   │   ├── database.yml
│   │   └── system.yml
│   └── business/
│       ├── sla.yml
│       └── performance.yml
├── exporters/           # Custom exporters
│   ├── business-metrics/
│   ├── application-metrics/
│   └── custom-exporters/
└── storage/             # Storage configuration
    ├── retention.yml
    └── thanos.yml
```

### **⚙️ Prometheus Configuration**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'enterprise-platform'
    environment: 'production'

rule_files:
  - "rules/application/*.yml"
  - "rules/infrastructure/*.yml"
  - "rules/business/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Kubernetes service discovery
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__

  # Application services
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'ai-service'
    static_configs:
      - targets: ['ai-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Infrastructure monitoring
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
```

### **🚨 Alert Rules**
```yaml
# rules/application/api-gateway.yml
groups:
  - name: api-gateway
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{service="api-gateway",status=~"5.."}[5m]) / rate(http_requests_total{service="api-gateway"}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
          service: api-gateway
        annotations:
          summary: "High error rate on API Gateway"
          description: "API Gateway error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{service="api-gateway"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: api-gateway
        annotations:
          summary: "High response time on API Gateway"
          description: "API Gateway 95th percentile response time is {{ $value }}s"

      - alert: LowThroughput
        expr: rate(http_requests_total{service="api-gateway"}[5m]) < 10
        for: 10m
        labels:
          severity: warning
          service: api-gateway
        annotations:
          summary: "Low throughput on API Gateway"
          description: "API Gateway throughput is {{ $value }} requests/second"

  - name: infrastructure
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }}"
```

## 🔧 **Monitoring Setup**

### **🚀 Quick Start**
```bash
# Start monitoring stack
docker-compose -f monitoring/docker-compose.yml up -d

# Import Grafana dashboards
./monitoring/scripts/import-dashboards.sh

# Configure Prometheus targets
./monitoring/scripts/configure-targets.sh

# Verify monitoring stack
./monitoring/scripts/health-check.sh
```

### **📊 Custom Metrics**
```typescript
// Application metrics example
import { register, Counter, Histogram, Gauge } from 'prom-client';

// Business metrics
export const userRegistrations = new Counter({
  name: 'user_registrations_total',
  help: 'Total number of user registrations',
  labelNames: ['source', 'plan'],
});

export const taskCompletions = new Counter({
  name: 'task_completions_total',
  help: 'Total number of completed tasks',
  labelNames: ['user_id', 'task_type'],
});

// Performance metrics
export const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
});

// System metrics
export const activeConnections = new Gauge({
  name: 'active_connections',
  help: 'Number of active connections',
  labelNames: ['service'],
});

// Usage in application
userRegistrations.inc({ source: 'web', plan: 'premium' });
taskCompletions.inc({ user_id: '123', task_type: 'analysis' });

const endTimer = httpRequestDuration.startTimer({ 
  method: 'POST', 
  route: '/api/users' 
});
// ... handle request
endTimer({ status_code: '200' });
```

## 📊 **Monitoring Best Practices**

### **📈 Key Metrics**
- ✅ **Golden Signals** - Latency, traffic, errors, saturation
- ✅ **Business Metrics** - User engagement, revenue, conversions
- ✅ **Infrastructure Metrics** - CPU, memory, disk, network
- ✅ **Application Metrics** - Response times, error rates, throughput
- ✅ **Security Metrics** - Failed logins, suspicious activities

### **🚀 Node.js Performance Monitoring**

**Event Loop & Memory Metrics:**
```javascript
// Event Loop Lag Monitoring
const toobusy = require('toobusy-js');
toobusy.maxLag(100); // Độ trễ tối đa 100ms
toobusy.interval(500); // Kiểm tra mỗi 500ms

setInterval(() => {
    const lag = toobusy.lag();
    console.log(`Event loop lag: ${lag}ms`);
    
    // Alert if lag is too high
    if (lag > 100) {
        console.warn(`⚠️ High event loop lag: ${lag}ms`);
    }
}, 1000);

// V8 Memory Statistics
const v8 = require('v8');
const heapStats = v8.getHeapStatistics();

const memoryMetrics = {
    heapSizeLimit: (heapStats.heap_size_limit / 1024 / 1024).toFixed(2),
    usedHeapSize: (heapStats.used_heap_size / 1024 / 1024).toFixed(2),
    totalAvailableSize: (heapStats.total_available_size / 1024 / 1024).toFixed(2),
    heapSizeLimit: (heapStats.heap_size_limit / 1024 / 1024).toFixed(2)
};

console.log('Memory Usage:', memoryMetrics);
```

**Performance Profiling với Clinic.js:**
```bash
# Install Clinic.js
npm install -g clinic

# CPU Profiling
clinic doctor -- node app.js
clinic flame -- node app.js

# Memory Profiling
clinic heap -- node app.js

# Async Operations Profiling
clinic bubbleprof -- node app.js
```

**Node.js Specific Metrics:**
```javascript
// Custom Prometheus metrics for Node.js
import { Counter, Histogram, Gauge } from 'prom-client';

// Event Loop Metrics
export const eventLoopLag = new Histogram({
  name: 'nodejs_eventloop_lag_seconds',
  help: 'Event loop lag in seconds',
  buckets: [0.001, 0.01, 0.1, 0.5, 1, 2, 5],
});

export const activeHandles = new Gauge({
  name: 'nodejs_active_handles',
  help: 'Number of active handles',
});

export const activeRequests = new Gauge({
  name: 'nodejs_active_requests',
  help: 'Number of active requests',
});

// Garbage Collection Metrics
export const gcDuration = new Histogram({
  name: 'nodejs_gc_duration_seconds',
  help: 'Garbage collection duration in seconds',
  labelNames: ['type'],
  buckets: [0.001, 0.01, 0.1, 0.5, 1, 2, 5],
});

// Memory Usage Metrics
export const heapUsed = new Gauge({
  name: 'nodejs_heap_size_used_bytes',
  help: 'Used heap size in bytes',
});

export const heapTotal = new Gauge({
  name: 'nodejs_heap_size_total_bytes',
  help: 'Total heap size in bytes',
});

// Update metrics periodically
setInterval(() => {
  const memUsage = process.memoryUsage();
  heapUsed.set(memUsage.heapUsed);
  heapTotal.set(memUsage.heapTotal);
  
  activeHandles.set(process._getActiveHandles().length);
  activeRequests.set(process._getActiveRequests().length);
}, 5000);
```

**Health Check Endpoint:**
```javascript
app.get('/health', (req, res) => {
  const health = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: Date.now(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    eventLoop: {
      lag: toobusy.lag(),
      maxLag: toobusy.maxLag()
    },
    v8: v8.getHeapStatistics()
  };
  
  res.json(health);
});
```

### **🚨 Alerting Strategy**
```yaml
# Alert severity levels
severity_levels:
  critical:    # Immediate action required
    - Service completely down
    - Data loss occurring
    - Security breach detected
  
  warning:     # Action required within hours
    - High error rates
    - Performance degradation
    - Resource usage high
  
  info:        # Informational alerts
    - Deployment completed
    - Scheduled maintenance
    - Capacity planning alerts
```

### **📊 SLA Monitoring**
```yaml
# SLA definitions
sla_targets:
  availability: 99.9%    # Maximum 43.2 minutes downtime per month
  response_time: 200ms   # 95th percentile response time
  error_rate: 0.1%       # Maximum error rate
  throughput: 1000rps    # Minimum throughput capacity
```

## 🔗 **Related Documentation**

- [🏗️ Project Structure](../docs/PROJECT_STRUCTURE.md)
- [🏗️ Infrastructure](../infrastructure/README.md)
- [🤖 Scripts](../scripts/README.md)
- [⚡ Services Layer](../services/README.md)
- [☁️ DevOps Guide](../docs/07-knowledge-base/05-devops-cloud/README.md)

## 🤝 **Contributing**

1. **Dashboard Development** - Create new monitoring dashboards
2. **Alert Rules** - Define meaningful alert conditions
3. **Custom Metrics** - Add application-specific metrics
4. **Documentation** - Document monitoring procedures
5. **Testing** - Test monitoring and alerting systems

---

> **Next Steps**: Explore individual monitoring component READMEs for detailed setup and configuration guides.
