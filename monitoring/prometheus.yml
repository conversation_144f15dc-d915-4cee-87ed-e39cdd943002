global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "deno-app"
    static_configs:
      - targets: ["app-dev:8000", "app-prod:8000"]
    metrics_path: /metrics
    scrape_interval: 5s

  - job_name: "nginx"
    static_configs:
      - targets: ["nginx:80"]
    metrics_path: /nginx_status
    scrape_interval: 10s

  - job_name: "postgres"
    static_configs:
      - targets: ["postgres:5432", "postgres-dev:5432"]
    scrape_interval: 10s

  - job_name: "redis"
    static_configs:
      - targets: ["redis:6379", "redis-dev:6379"]
    scrape_interval: 10s
