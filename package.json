{"name": "enterprise-platform", "version": "1.0.0", "description": "Enterprise-grade monorepo platform with AI services, API Gateway, and comprehensive algorithm mastery system", "private": true, "workspaces": ["apps/*", "services/*", "libs/*"], "scripts": {"start": "npm run dev", "dev": "concurrently \"npm run dev:api-gateway\" \"npm run dev:ai-service\"", "dev:api-gateway": "npm run dev --workspace=apps/api-gateway", "dev:ai-service": "npm run dev --workspace=services/ai-service", "build": "npm run build:all", "build:all": "npm run build --workspaces", "build:api-gateway": "npm run build --workspace=apps/api-gateway", "build:ai-service": "npm run build --workspace=services/ai-service", "test": "npm run test --workspaces", "test:unit": "npm run test:unit --workspaces", "test:integration": "npm run test:integration --workspaces", "test:e2e": "npm run test:e2e --workspaces", "test:coverage": "npm run test:coverage --workspaces", "test:performance": "npm run test:performance --workspaces", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "format": "npm run format --workspaces", "type-check": "npm run type-check --workspaces", "install:all": "npm install --workspaces", "update:all": "npm update --workspaces", "clean": "npm run clean --workspaces && rm -rf node_modules dist build coverage", "setup": "npm install && npm run install:all && npm run build:all", "migration:run": "npm run migration:run --workspace=apps/api-gateway", "migration:revert": "npm run migration:revert --workspace=apps/api-gateway", "db:seed": "npm run db:seed --workspace=apps/api-gateway", "db:reset": "npm run db:reset --workspace=apps/api-gateway", "security:scan": "npm audit && npm run security:scan --workspaces", "security:audit": "npm audit --workspaces", "docs:build": "npm run docs:build --workspaces", "docs:serve": "npm run docs:serve --workspaces", "docs:deploy": "npm run docs:deploy --workspaces", "benchmark": "npm run benchmark --workspaces", "enhance": "node enhance_algorithm.js", "enhance:array": "node enhance_algorithm.js --category=array", "enhance:string": "node enhance_algorithm.js --category=string", "enhance:tree": "node enhance_algorithm.js --category=tree", "enhance:dp": "node enhance_algorithm.js --category=dp", "validate": "node scripts/validate_enhancements.js", "report": "node scripts/generate_report.js", "progress": "node scripts/track_progress.js"}, "keywords": ["enterprise", "platform", "monorepo", "microservices", "api-gateway", "ai-service", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "algorithm", "data-structure", "interview", "coding", "leetcode", "programming", "mastery", "framework", "analysis", "patterns", "typescript", "python", "docker", "kubernetes"], "author": "Enterprise Platform Team", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "commander": "^9.4.1", "fs-extra": "^10.1.0", "lodash": "^4.17.21", "moment": "^2.29.4", "concurrently": "^7.6.0"}, "devDependencies": {"jest": "^29.3.1", "eslint": "^8.28.0", "prettier": "^2.8.0", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0", "typescript": "^4.9.4", "@types/node": "^18.11.18", "ts-node": "^10.9.1", "nodemon": "^2.0.20"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/phangiaphat982015/practice"}, "bugs": {"url": "https://github.com/phangiaphat982015/practice/issues"}, "homepage": "https://github.com/phangiaphat982015/practice#readme", "config": {"enhancement": {"outputDir": "enhanced-algorithms", "inputDir": "196-bai-code-interview", "logLevel": "info", "generateSummary": true, "validateOutput": true}, "patterns": {"confidenceThreshold": 70, "maxPatternsPerProblem": 5, "enableAutoDetection": true}, "testing": {"enablePerformanceTests": true, "enableStressTests": false, "timeoutMs": 5000}}, "bin": {"algorithm-enhance": "./enhance_algorithm.js", "algo-enhance": "./enhance_algorithm.js"}}