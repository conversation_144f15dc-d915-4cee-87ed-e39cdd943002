/**
 * 🗂️ Test Data Fixtures
 * 
 * Centralized test data for consistent testing across all test suites:
 * - User test data
 * - Authentication fixtures
 * - API response mocks
 * - Database seed data
 */

// User test data
export const testUsers = {
  validUser: {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'TestPassword123!',
    hashedPassword: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QVpnwDrwG',
    roles: ['user'],
    isActive: true,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    lastLoginAt: null,
  },
  
  adminUser: {
    id: 'admin-456',
    email: '<EMAIL>',
    name: 'Admin User',
    password: 'AdminPassword123!',
    hashedPassword: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QVpnwDrwG',
    roles: ['admin', 'user'],
    isActive: true,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    lastLoginAt: new Date('2024-01-01T10:00:00Z'),
  },
  
  inactiveUser: {
    id: 'inactive-789',
    email: '<EMAIL>',
    name: 'Inactive User',
    password: 'InactivePassword123!',
    hashedPassword: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QVpnwDrwG',
    roles: ['user'],
    isActive: false,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    lastLoginAt: null,
  },
  
  // Multiple users for bulk testing
  bulkUsers: Array.from({ length: 10 }, (_, index) => ({
    id: `bulk-user-${index}`,
    email: `bulk.user${index}@example.com`,
    name: `Bulk User ${index}`,
    password: 'BulkPassword123!',
    hashedPassword: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QVpnwDrwG',
    roles: ['user'],
    isActive: true,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    lastLoginAt: null,
  })),
};

// Authentication fixtures
export const authFixtures = {
  validLoginRequest: {
    email: testUsers.validUser.email,
    password: testUsers.validUser.password,
  },
  
  invalidLoginRequest: {
    email: '<EMAIL>',
    password: 'wrongpassword',
  },
  
  validRegisterRequest: {
    email: '<EMAIL>',
    name: 'New User',
    password: 'NewUserPass123!',
  },
  
  invalidRegisterRequests: {
    missingEmail: {
      name: 'No Email User',
      password: 'Password123!',
    },
    missingName: {
      email: '<EMAIL>',
      password: 'Password123!',
    },
    missingPassword: {
      email: '<EMAIL>',
      name: 'No Password User',
    },
    weakPassword: {
      email: '<EMAIL>',
      name: 'Weak Password User',
      password: '123',
    },
    invalidEmail: {
      email: 'invalid-email',
      name: 'Invalid Email User',
      password: 'Password123!',
    },
  },
  
  validTokens: {
    accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************.mockTokenSignature',
    refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************.mockRefreshTokenSignature',
  },
  
  invalidTokens: {
    expired: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************.expiredTokenSignature',
    malformed: 'invalid.token.format',
    empty: '',
  },
};

// API response mocks
export const apiResponseMocks = {
  healthResponse: {
    basic: {
      status: 'ok',
      timestamp: '2024-01-01T12:00:00.000Z',
      uptime: 3600,
      environment: 'test',
    },
    
    detailed: {
      status: 'ok',
      timestamp: '2024-01-01T12:00:00.000Z',
      uptime: 3600,
      environment: 'test',
      services: {
        database: {
          status: 'up',
          responseTime: '45ms',
          host: 'localhost',
          port: 5432,
          database: 'test_db',
        },
        redis: {
          status: 'up',
          responseTime: '5ms',
          host: 'localhost',
          port: 6379,
          db: 0,
        },
        aiService: {
          status: 'up',
          responseTime: '120ms',
          url: 'http://localhost:8000',
        },
      },
      system: {
        memory: {
          used: 512,
          total: 2048,
          external: 15,
          rss: 45,
        },
        cpu: {
          usage: 25.5,
        },
      },
    },
    
    degraded: {
      status: 'degraded',
      timestamp: '2024-01-01T12:00:00.000Z',
      uptime: 3600,
      services: {
        database: {
          status: 'up',
          responseTime: '45ms',
        },
        redis: {
          status: 'down',
          error: 'Connection timeout',
        },
      },
    },
  },
  
  authResponse: {
    loginSuccess: {
      user: {
        id: testUsers.validUser.id,
        email: testUsers.validUser.email,
        name: testUsers.validUser.name,
        roles: testUsers.validUser.roles,
      },
      accessToken: authFixtures.validTokens.accessToken,
      refreshToken: authFixtures.validTokens.refreshToken,
    },
    
    loginFailure: {
      statusCode: 401,
      message: 'Invalid credentials',
      error: 'Unauthorized',
    },
    
    registerSuccess: {
      user: {
        id: 'new-user-123',
        email: authFixtures.validRegisterRequest.email,
        name: authFixtures.validRegisterRequest.name,
        roles: ['user'],
      },
      accessToken: authFixtures.validTokens.accessToken,
      refreshToken: authFixtures.validTokens.refreshToken,
    },
    
    refreshSuccess: {
      accessToken: 'newAccessToken123',
    },
  },
  
  errorResponses: {
    badRequest: {
      statusCode: 400,
      message: 'Bad Request',
      error: 'Validation failed',
    },
    
    unauthorized: {
      statusCode: 401,
      message: 'Unauthorized',
      error: 'Invalid token',
    },
    
    forbidden: {
      statusCode: 403,
      message: 'Forbidden',
      error: 'Insufficient permissions',
    },
    
    notFound: {
      statusCode: 404,
      message: 'Not Found',
      error: 'Resource not found',
    },
    
    rateLimit: {
      statusCode: 429,
      message: 'Too Many Requests',
      error: 'Rate limit exceeded',
    },
    
    internalError: {
      statusCode: 500,
      message: 'Internal Server Error',
      error: 'Something went wrong',
    },
  },
};

// Database seed data
export const seedData = {
  users: [
    testUsers.validUser,
    testUsers.adminUser,
    testUsers.inactiveUser,
    ...testUsers.bulkUsers,
  ],
  
  // Additional entities can be added here
  roles: [
    {
      id: 'role-1',
      name: 'user',
      description: 'Standard user role',
      permissions: ['read:profile', 'update:profile'],
    },
    {
      id: 'role-2',  
      name: 'admin',
      description: 'Administrator role',
      permissions: [
        'read:profile',
        'update:profile',
        'read:users',
        'create:users',
        'update:users',
        'delete:users',
        'read:admin',
      ],
    },
  ],
};

// Test configuration
export const testConfig = {
  database: {
    host: 'localhost',
    port: 5432,
    username: 'test_user',
    password: 'test_password',
    database: 'test_db',
  },
  
  redis: {
    host: 'localhost',
    port: 6379,
    password: '',
    db: 1, // Use different DB for tests
  },
  
  jwt: {
    secret: 'test-jwt-secret-key',
    expiresIn: '1h',
    refreshSecret: 'test-refresh-secret-key',
    refreshExpiresIn: '7d',
  },
  
  api: {
    baseUrl: 'http://localhost:3000',
    prefix: '/api/v1',
    timeout: 5000,
  },
  
  performance: {
    thresholds: {
      responseTime: 500, // ms
      errorRate: 0.1,    // 10%
      throughput: 100,   // requests per second
    },
  },
};

// Mock factories
export const createMockUser = (overrides = {}) => ({
  ...testUsers.validUser,
  ...overrides,
  id: `mock-${Math.random().toString(36).substr(2, 9)}`,
});

export const createMockAuthResponse = (overrides = {}) => ({
  ...apiResponseMocks.authResponse.loginSuccess,
  ...overrides,
});

export const createMockHealthResponse = (overrides = {}) => ({
  ...apiResponseMocks.healthResponse.basic,
  ...overrides,
});

// Test utilities
export const generateRandomEmail = () => 
  `test.${Math.random().toString(36).substr(2, 9)}@example.com`;

export const generateRandomUser = (overrides = {}) => ({
  email: generateRandomEmail(),
  name: `Test User ${Math.random().toString(36).substr(2, 9)}`,
  password: 'TestPassword123!',
  ...overrides,
});

export const sanitizeUserForResponse = (user: any) => {
  const { password, hashedPassword, ...sanitized } = user;
  return sanitized;
};

// Export default test data
export default {
  users: testUsers,
  auth: authFixtures,
  api: apiResponseMocks,
  seed: seedData,
  config: testConfig,
  factories: {
    createMockUser,
    createMockAuthResponse,
    createMockHealthResponse,
    generateRandomEmail,
    generateRandomUser,
  },
  utils: {
    sanitizeUserForResponse,
  },
};