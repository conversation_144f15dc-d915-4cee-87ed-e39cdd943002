/**
 * 🚀 API Load Testing - k6 Performance Tests
 * 
 * Comprehensive performance testing for API endpoints:
 * - Authentication load testing
 * - User management endpoints
 * - Health check performance
 * - Database connection stress testing
 * - Rate limiting validation
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
const authSuccessRate = new Rate('auth_success_rate');
const responseTime = new Trend('response_time');
const errorRate = new Counter('errors');

// Test configuration
export const options = {
  stages: [
    // Ramp-up
    { duration: '2m', target: 10 },   // Ramp up to 10 users over 2 minutes
    { duration: '5m', target: 10 },   // Stay at 10 users for 5 minutes
    { duration: '2m', target: 20 },   // Ramp up to 20 users over 2 minutes
    { duration: '5m', target: 20 },   // Stay at 20 users for 5 minutes
    { duration: '2m', target: 50 },   // Ramp up to 50 users over 2 minutes
    { duration: '5m', target: 50 },   // Stay at 50 users for 5 minutes
    { duration: '5m', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms
    http_req_failed: ['rate<0.1'],    // Error rate should be below 10%
    auth_success_rate: ['rate>0.9'],  // Auth success rate should be above 90%
  },
};

// Environment configuration
const BASE_URL = __ENV.API_BASE_URL || 'http://localhost:3000';
const API_PREFIX = '/api/v1';

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'LoadTest123!', name: 'Load Test User 1' },
  { email: '<EMAIL>', password: 'LoadTest123!', name: 'Load Test User 2' },
  { email: '<EMAIL>', password: 'LoadTest123!', name: 'Load Test User 3' },
  { email: '<EMAIL>', password: 'LoadTest123!', name: 'Load Test User 4' },
  { email: '<EMAIL>', password: 'LoadTest123!', name: 'Load Test User 5' },
];

// Helper functions
function getRandomUser() {
  return testUsers[Math.floor(Math.random() * testUsers.length)];
}

function generateHeaders(token = null) {
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

// Setup function - runs once per VU
export function setup() {
  console.log('🚀 Starting API Load Tests...');
  console.log(`Target URL: ${BASE_URL}`);
  
  // Health check
  const healthRes = http.get(`${BASE_URL}${API_PREFIX}/health`);
  check(healthRes, {
    'API is healthy': (r) => r.status === 200,
  });
  
  if (healthRes.status !== 200) {
    console.error('❌ API health check failed, aborting tests');
    return null;
  }
  
  return { baseUrl: BASE_URL };
}

// Main test function
export default function(data) {
  if (!data) return;
  
  const user = getRandomUser();
  let accessToken = null;
  
  // Test authentication flow
  const authResult = testAuthentication(user);
  if (authResult.success) {
    accessToken = authResult.token;
    
    // Test protected endpoints
    testUserProfile(accessToken);
    testHealthEndpoints();
    testErrorHandling(accessToken);
  }
  
  sleep(1); // Wait between iterations
}

// Authentication load testing
function testAuthentication(user) {
  const loginData = {
    email: user.email,
    password: user.password,
  };
  
  const startTime = new Date().getTime();
  
  const response = http.post(
    `${BASE_URL}${API_PREFIX}/auth/login`,
    JSON.stringify(loginData),
    { headers: generateHeaders() }
  );
  
  const endTime = new Date().getTime();
  const duration = endTime - startTime;
  
  responseTime.add(duration);
  
  const isSuccess = check(response, {
    'login status is 200': (r) => r.status === 200,
    'login response has token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.accessToken !== undefined;
      } catch {
        return false;
      }
    },
    'login response time < 1000ms': () => duration < 1000,
  });
  
  authSuccessRate.add(isSuccess);
  
  if (!isSuccess) {
    errorRate.add(1);
    console.error(`❌ Login failed for ${user.email}: ${response.status}`);
    return { success: false };
  }
  
  try {
    const responseBody = JSON.parse(response.body);
    return {
      success: true,
      token: responseBody.accessToken,
    };
  } catch {
    errorRate.add(1);
    return { success: false };
  }
}

// User profile endpoint testing
function testUserProfile(token) {
  const response = http.get(
    `${BASE_URL}${API_PREFIX}/users/profile`,
    { headers: generateHeaders(token) }
  );
  
  check(response, {
    'profile status is 200': (r) => r.status === 200,
    'profile has user data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.id !== undefined && body.email !== undefined;
      } catch {
        return false;
      }
    },
  });
  
  if (response.status !== 200) {
    errorRate.add(1);
  }
}

// Health endpoints performance testing
function testHealthEndpoints() {
  // Basic health check
  const basicHealth = http.get(`${BASE_URL}${API_PREFIX}/health`);
  check(basicHealth, {
    'basic health status is 200': (r) => r.status === 200,
    'basic health response time < 100ms': (r) => r.timings.duration < 100,
  });
  
  // Detailed health check
  const detailedHealth = http.get(`${BASE_URL}${API_PREFIX}/health/detailed`);
  check(detailedHealth, {
    'detailed health status is 200': (r) => r.status === 200,
    'detailed health response time < 500ms': (r) => r.timings.duration < 500,
    'detailed health has services': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.services !== undefined;
      } catch {
        return false;
      }
    },
  });
  
  // Readiness probe
  const readiness = http.get(`${BASE_URL}${API_PREFIX}/health/ready`);
  check(readiness, {
    'readiness probe success': (r) => r.status === 200 || r.status === 503,
    'readiness response time < 200ms': (r) => r.timings.duration < 200,
  });
}

// Error handling and rate limiting testing
function testErrorHandling(token) {
  // Test invalid endpoint
  const notFound = http.get(`${BASE_URL}${API_PREFIX}/invalid-endpoint`);
  check(notFound, {
    'invalid endpoint returns 404': (r) => r.status === 404,
  });
  
  // Test unauthorized access
  const unauthorized = http.get(`${BASE_URL}${API_PREFIX}/admin/users`);
  check(unauthorized, {
    'unauthorized returns 401': (r) => r.status === 401,
  });
  
  // Test rate limiting (make rapid requests)
  const rapidRequests = [];
  for (let i = 0; i < 10; i++) {
    rapidRequests.push(
      http.get(`${BASE_URL}${API_PREFIX}/health`, { headers: generateHeaders(token) })
    );
  }
  
  // At least some requests should succeed
  const successCount = rapidRequests.filter(r => r.status === 200).length;
  check(null, {
    'rate limiting allows some requests': () => successCount > 0,
  });
}

// Spike testing scenario
export function spikeTesting() {
  const spikeOptions = {
    stages: [
      { duration: '10s', target: 100 }, // Spike to 100 users
      { duration: '1m', target: 100 },  // Stay at 100 users
      { duration: '10s', target: 0 },   // Drop to 0 users
    ],
  };
  
  // Override options for spike test
  Object.assign(options, spikeOptions);
}

// Stress testing scenario  
export function stressTesting() {
  const stressOptions = {
    stages: [
      { duration: '5m', target: 10 },   // Ramp up
      { duration: '10m', target: 50 },  // Normal load
      { duration: '5m', target: 100 },  // Stress load
      { duration: '10m', target: 200 }, // Heavy stress
      { duration: '5m', target: 0 },    // Ramp down
    ],
  };
  
  Object.assign(options, stressOptions);
}

// Cleanup function
export function teardown(data) {
  console.log('🧹 Cleaning up load test...');
  
  // Final health check
  const finalHealth = http.get(`${BASE_URL}${API_PREFIX}/health`);
  if (finalHealth.status === 200) {
    console.log('✅ API is still healthy after load test');
  } else {
    console.log('⚠️ API health degraded after load test');
  }
}

// Performance thresholds validation
export function handleSummary(data) {
  return {
    'load-test-results.json': JSON.stringify(data, null, 2),
    stdout: `
🚀 Load Test Summary:
===================
Total Requests: ${data.metrics.http_reqs.count}
Failed Requests: ${data.metrics.http_req_failed.count} (${(data.metrics.http_req_failed.rate * 100).toFixed(2)}%)
Average Response Time: ${data.metrics.http_req_duration.avg.toFixed(2)}ms
95th Percentile: ${data.metrics['http_req_duration{p(95)}']?.toFixed(2) || 'N/A'}ms
Authentication Success Rate: ${((data.metrics.auth_success_rate?.rate || 0) * 100).toFixed(2)}%

Thresholds:
- Response Time (p95): ${data.metrics['http_req_duration{p(95)}'] < 500 ? '✅ PASS' : '❌ FAIL'}
- Error Rate: ${data.metrics.http_req_failed.rate < 0.1 ? '✅ PASS' : '❌ FAIL'}
- Auth Success Rate: ${(data.metrics.auth_success_rate?.rate || 0) > 0.9 ? '✅ PASS' : '❌ FAIL'}
`,
  };
}