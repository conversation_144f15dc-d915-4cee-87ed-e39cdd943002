/**
 * 🎭 Authentication E2E Tests
 * 
 * End-to-end tests for authentication flow:
 * - Complete user registration flow
 * - Login and session management
 * - Protected route access
 * - Multi-device authentication
 * - Security validations
 */

import { test, expect, Page, BrowserContext } from '@playwright/test';

// Test configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const WEB_BASE_URL = process.env.WEB_BASE_URL || 'http://localhost:3001';

// Test data
const testUsers = {
  validUser: {
    email: '<EMAIL>',
    name: 'E2E Test User',
    password: 'SecurePass123!',
  },
  adminUser: {
    email: '<EMAIL>',
    name: 'E2E Admin User',
    password: 'AdminPass123!',
    roles: ['admin', 'user'],
  },
};

test.describe('Authentication E2E Flow', () => {
  let page: Page;
  let context: BrowserContext;

  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext({
      baseURL: WEB_BASE_URL,
      ignoreHTTPSErrors: true,
    });
    page = await context.newPage();

    // Setup API interceptors for testing
    await page.route(`${API_BASE_URL}/api/v1/**`, async (route) => {
      // Log API calls for debugging
      console.log(`API Call: ${route.request().method()} ${route.request().url()}`);
      await route.continue();
    });
  });

  test.afterAll(async () => {
    await context.close();
  });

  test.beforeEach(async () => {
    // Clear all storage before each test
    await context.clearCookies();
    await context.clearPermissions();
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  });

  test.describe('User Registration Flow', () => {
    test('should complete user registration successfully', async () => {
      // Navigate to registration page
      await page.goto('/register');
      await expect(page.locator('[data-testid=register-form]')).toBeVisible();

      // Fill registration form
      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=name-input]', testUsers.validUser.name);
      await page.fill('[data-testid=password-input]', testUsers.validUser.password);
      await page.fill('[data-testid=confirm-password-input]', testUsers.validUser.password);

      // Accept terms and conditions
      await page.check('[data-testid=terms-checkbox]');

      // Submit registration
      await page.click('[data-testid=register-button]');

      // Verify successful registration
      await expect(page.locator('[data-testid=success-message]')).toBeVisible();
      await expect(page.locator('[data-testid=success-message]')).toContainText(
        'Registration successful'
      );

      // Should redirect to email verification page
      await expect(page).toHaveURL(/\/verify-email/);
      await expect(page.locator('[data-testid=verification-message]')).toContainText(
        'Please check your email'
      );
    });

    test('should show validation errors for invalid data', async () => {
      await page.goto('/register');

      // Submit empty form
      await page.click('[data-testid=register-button]');

      // Verify validation errors
      await expect(page.locator('[data-testid=email-error]')).toContainText(
        'Email is required'
      );
      await expect(page.locator('[data-testid=name-error]')).toContainText(
        'Name is required'
      );
      await expect(page.locator('[data-testid=password-error]')).toContainText(
        'Password is required'
      );
    });

    test('should validate password strength requirements', async () => {
      await page.goto('/register');

      await page.fill('[data-testid=email-input]', '<EMAIL>');
      await page.fill('[data-testid=name-input]', 'Test User');
      await page.fill('[data-testid=password-input]', '123'); // Weak password
      await page.fill('[data-testid=confirm-password-input]', '123');

      await page.click('[data-testid=register-button]');

      await expect(page.locator('[data-testid=password-error]')).toContainText(
        'Password must be at least 8 characters'
      );
    });

    test('should handle duplicate email registration', async () => {
      await page.goto('/register');

      // Fill with existing user email
      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=name-input]', 'Another User');
      await page.fill('[data-testid=password-input]', 'AnotherPass123!');
      await page.fill('[data-testid=confirm-password-input]', 'AnotherPass123!');
      await page.check('[data-testid=terms-checkbox]');

      await page.click('[data-testid=register-button]');

      await expect(page.locator('[data-testid=error-message]')).toContainText(
        'User with this email already exists'
      );
    });
  });

  test.describe('Login Flow', () => {
    test('should login successfully with valid credentials', async () => {
      await page.goto('/login');

      // Fill login form
      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=password-input]', testUsers.validUser.password);

      // Submit login
      await page.click('[data-testid=login-button]');

      // Verify successful login
      await expect(page).toHaveURL(/\/dashboard/);
      await expect(page.locator('[data-testid=user-menu]')).toContainText(
        testUsers.validUser.name
      );
      await expect(page.locator('[data-testid=logout-button]')).toBeVisible();
    });

    test('should show error for invalid credentials', async () => {
      await page.goto('/login');

      await page.fill('[data-testid=email-input]', '<EMAIL>');
      await page.fill('[data-testid=password-input]', 'wrongpassword');

      await page.click('[data-testid=login-button]');

      await expect(page.locator('[data-testid=error-message]')).toContainText(
        'Invalid credentials'
      );
      await expect(page).toHaveURL('/login'); // Should stay on login page
    });

    test('should handle "Remember Me" functionality', async () => {
      await page.goto('/login');

      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=password-input]', testUsers.validUser.password);
      await page.check('[data-testid=remember-checkbox]');

      await page.click('[data-testid=login-button]');

      // Close and reopen browser
      await page.close();
      page = await context.newPage();
      await page.goto('/');

      // Should still be logged in
      await expect(page.locator('[data-testid=user-menu]')).toBeVisible();
    });
  });

  test.describe('Protected Routes', () => {
    test('should redirect unauthenticated users to login', async () => {
      await page.goto('/dashboard');

      // Should redirect to login
      await expect(page).toHaveURL(/\/login/);
      await expect(page.locator('[data-testid=login-form]')).toBeVisible();
    });

    test('should allow access to protected routes after login', async () => {
      // Login first
      await page.goto('/login');
      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=password-input]', testUsers.validUser.password);
      await page.click('[data-testid=login-button]');

      // Try accessing protected routes
      await page.goto('/dashboard');
      await expect(page.locator('[data-testid=dashboard-content]')).toBeVisible();

      await page.goto('/profile');
      await expect(page.locator('[data-testid=profile-form]')).toBeVisible();

      await page.goto('/settings');
      await expect(page.locator('[data-testid=settings-panel]')).toBeVisible();
    });

    test('should restrict admin routes to admin users only', async () => {
      // Login as regular user
      await page.goto('/login');
      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=password-input]', testUsers.validUser.password);
      await page.click('[data-testid=login-button]');

      // Try accessing admin route
      await page.goto('/admin');
      await expect(page.locator('[data-testid=access-denied]')).toBeVisible();
      await expect(page.locator('[data-testid=access-denied]')).toContainText(
        'Access Denied'
      );
    });
  });

  test.describe('Session Management', () => {
    test('should logout successfully', async () => {
      // Login first
      await page.goto('/login');
      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=password-input]', testUsers.validUser.password);
      await page.click('[data-testid=login-button]');

      // Logout
      await page.click('[data-testid=user-menu]');
      await page.click('[data-testid=logout-button]');

      // Verify logout
      await expect(page).toHaveURL('/login');
      await expect(page.locator('[data-testid=login-form]')).toBeVisible();

      // Try accessing protected route - should redirect
      await page.goto('/dashboard');
      await expect(page).toHaveURL(/\/login/);
    });

    test('should handle session expiration', async () => {
      // Login first
      await page.goto('/login');
      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=password-input]', testUsers.validUser.password);
      await page.click('[data-testid=login-button]');

      // Mock token expiration
      await page.evaluate(() => {
        localStorage.setItem('accessToken', 'expired-token');
      });

      // Try to access protected route
      await page.goto('/dashboard');

      // Should show session expired message and redirect to login
      await expect(page.locator('[data-testid=session-expired]')).toBeVisible();
      await expect(page).toHaveURL(/\/login/);
    });

    test('should refresh token automatically', async () => {
      // Login first
      await page.goto('/login');
      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=password-input]', testUsers.validUser.password);
      await page.click('[data-testid=login-button]');

      // Wait for potential token refresh
      await page.waitForTimeout(2000);

      // Make API call that would trigger token refresh
      await page.evaluate(async () => {
        await fetch('/api/v1/users/profile', {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
          },
        });
      });

      // Should still be logged in
      await expect(page.locator('[data-testid=user-menu]')).toBeVisible();
    });
  });

  test.describe('Multi-Device Authentication', () => {
    test('should handle concurrent sessions across devices', async () => {
      // Create second browser context (simulating another device)
      const secondContext = await page.context().browser().newContext({
        baseURL: WEB_BASE_URL,
      });
      const secondPage = await secondContext.newPage();

      try {
        // Login on first device
        await page.goto('/login');
        await page.fill('[data-testid=email-input]', testUsers.validUser.email);
        await page.fill('[data-testid=password-input]', testUsers.validUser.password);
        await page.click('[data-testid=login-button]');

        // Login on second device
        await secondPage.goto('/login');
        await secondPage.fill('[data-testid=email-input]', testUsers.validUser.email);
        await secondPage.fill('[data-testid=password-input]', testUsers.validUser.password);
        await secondPage.click('[data-testid=login-button]');

        // Both devices should be logged in
        await expect(page.locator('[data-testid=user-menu]')).toBeVisible();
        await expect(secondPage.locator('[data-testid=user-menu]')).toBeVisible();

        // Logout from first device
        await page.click('[data-testid=user-menu]');
        await page.click('[data-testid=logout-button]');

        // Second device should still be logged in
        await expect(secondPage.locator('[data-testid=user-menu]')).toBeVisible();
      } finally {
        await secondContext.close();
      }
    });
  });

  test.describe('Security Features', () => {
    test('should implement CSRF protection', async () => {
      await page.goto('/login');

      // Check for CSRF token in form
      const csrfToken = await page.locator('[name="_token"]').getAttribute('value');
      expect(csrfToken).toBeTruthy();
      expect(csrfToken).toMatch(/^[a-zA-Z0-9+/]+=*$/); // Base64 pattern
    });

    test('should implement rate limiting for login attempts', async () => {
      await page.goto('/login');

      // Make multiple failed login attempts
      for (let i = 0; i < 5; i++) {
        await page.fill('[data-testid=email-input]', '<EMAIL>');
        await page.fill('[data-testid=password-input]', 'wrongpassword');
        await page.click('[data-testid=login-button]');
        await page.waitForTimeout(100);
      }

      // Should show rate limit message
      await expect(page.locator('[data-testid=rate-limit-error]')).toContainText(
        'Too many login attempts'
      );
    });

    test('should sanitize input to prevent XSS', async () => {
      await page.goto('/register');

      const xssPayload = '<script>alert("xss")</script>';
      
      await page.fill('[data-testid=name-input]', xssPayload);
      await page.fill('[data-testid=email-input]', '<EMAIL>');
      await page.fill('[data-testid=password-input]', 'Password123!');
      await page.fill('[data-testid=confirm-password-input]', 'Password123!');
      await page.check('[data-testid=terms-checkbox]');

      await page.click('[data-testid=register-button]');

      // XSS should be prevented - no alert should fire
      const dialogPromise = page.waitForEvent('dialog', { timeout: 1000 }).catch(() => null);
      const dialog = await dialogPromise;
      expect(dialog).toBeNull();
    });

    test('should use HTTPS in production', async () => {
      // In production environment, should use HTTPS
      if (process.env.NODE_ENV === 'production') {
        expect(page.url()).toMatch(/^https:/);
      }
    });
  });

  test.describe('Performance Tests', () => {
    test('should load login page quickly', async () => {
      const startTime = Date.now();
      
      await page.goto('/login');
      await expect(page.locator('[data-testid=login-form]')).toBeVisible();
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(3000); // Should load within 3 seconds
    });

    test('should complete login process efficiently', async () => {
      await page.goto('/login');
      
      const startTime = Date.now();
      
      await page.fill('[data-testid=email-input]', testUsers.validUser.email);
      await page.fill('[data-testid=password-input]', testUsers.validUser.password);
      await page.click('[data-testid=login-button]');
      
      await expect(page).toHaveURL(/\/dashboard/);
      
      const loginTime = Date.now() - startTime;
      expect(loginTime).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });
});