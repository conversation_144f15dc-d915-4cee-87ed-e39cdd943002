/**
 * 🧪 Auth Service Unit Tests
 * 
 * Comprehensive unit tests for authentication service:
 * - User registration and validation
 * - Password hashing and verification
 * - JWT token generation and validation
 * - Role-based access control
 * - Security edge cases
 */

import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';

import { AuthService } from '../../../apps/api-gateway/src/modules/auth/auth.service';
import { UserService } from '../../../apps/api-gateway/src/modules/users/user.service';
import { CreateUserDto } from '../../../apps/api-gateway/src/modules/auth/dto/create-user.dto';
import { LoginDto } from '../../../apps/api-gateway/src/modules/auth/dto/login.dto';

jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AuthService', () => {
  let authService: AuthService;
  let userService: jest.Mocked<UserService>;
  let jwtService: jest.Mocked<JwtService>;
  let configService: jest.Mocked<ConfigService>;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword123',
    roles: ['user'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockUserService = {
      findByEmail: jest.fn(),
      create: jest.fn(),
      findById: jest.fn(),
      updateLastLogin: jest.fn(),
    };

    const mockJwtService = {
      sign: jest.fn(),
      verify: jest.fn(),
      decode: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    authService = module.get<AuthService>(AuthService);
    userService = module.get(UserService);
    jwtService = module.get(JwtService);
    configService = module.get(ConfigService);

    // Setup default mock configurations
    configService.get.mockImplementation((key: string) => {
      const config = {
        'jwt.secret': 'test-secret',
        'jwt.expiresIn': '24h',
        'jwt.refreshSecret': 'test-refresh-secret',
        'jwt.refreshExpiresIn': '7d',
        'auth.saltRounds': 12,
      };
      return config[key];
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const registerDto: CreateUserDto = {
      email: '<EMAIL>',
      name: 'New User',
      password: 'SecurePass123!',
    };

    it('should register a new user successfully', async () => {
      // Arrange
      userService.findByEmail.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashedPassword' as never);
      userService.create.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('mock-jwt-token');

      // Act
      const result = await authService.register(registerDto);

      // Assert
      expect(userService.findByEmail).toHaveBeenCalledWith(registerDto.email);
      expect(mockedBcrypt.hash).toHaveBeenCalledWith(registerDto.password, 12);
      expect(userService.create).toHaveBeenCalledWith({
        ...registerDto,
        password: 'hashedPassword',
      });
      expect(result).toEqual({
        user: expect.objectContaining({
          id: mockUser.id,
          email: mockUser.email,
          name: mockUser.name,
        }),
        accessToken: 'mock-jwt-token',
        refreshToken: expect.any(String),
      });
    });

    it('should throw error if user already exists', async () => {
      // Arrange
      userService.findByEmail.mockResolvedValue(mockUser);

      // Act & Assert
      await expect(authService.register(registerDto)).rejects.toThrow(
        'User with this email already exists'
      );
      expect(userService.create).not.toHaveBeenCalled();
    });

    it('should validate password strength', async () => {
      // Arrange
      const weakPasswordDto = {
        ...registerDto,
        password: '123',
      };

      // Act & Assert
      await expect(authService.register(weakPasswordDto)).rejects.toThrow(
        'Password does not meet security requirements'
      );
    });

    it('should validate email format', async () => {
      // Arrange
      const invalidEmailDto = {
        ...registerDto,
        email: 'invalid-email',
      };

      // Act & Assert
      await expect(authService.register(invalidEmailDto)).rejects.toThrow(
        'Invalid email format'
      );
    });
  });

  describe('login', () => {
    const loginDto: LoginDto = {
      email: '<EMAIL>',
      password: 'SecurePass123!',
    };

    it('should login user successfully with valid credentials', async () => {
      // Arrange
      userService.findByEmail.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(true as never);
      jwtService.sign.mockReturnValue('mock-jwt-token');

      // Act
      const result = await authService.login(loginDto);

      // Assert
      expect(userService.findByEmail).toHaveBeenCalledWith(loginDto.email);
      expect(mockedBcrypt.compare).toHaveBeenCalledWith(
        loginDto.password,
        mockUser.password
      );
      expect(userService.updateLastLogin).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual({
        user: expect.objectContaining({
          id: mockUser.id,
          email: mockUser.email,
          name: mockUser.name,
        }),
        accessToken: 'mock-jwt-token',
        refreshToken: expect.any(String),
      });
    });

    it('should throw error for non-existent user', async () => {
      // Arrange
      userService.findByEmail.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.login(loginDto)).rejects.toThrow(
        'Invalid credentials'
      );
      expect(mockedBcrypt.compare).not.toHaveBeenCalled();
    });

    it('should throw error for invalid password', async () => {
      // Arrange
      userService.findByEmail.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(false as never);

      // Act & Assert
      await expect(authService.login(loginDto)).rejects.toThrow(
        'Invalid credentials'
      );
      expect(userService.updateLastLogin).not.toHaveBeenCalled();
    });

    it('should throw error for inactive user', async () => {
      // Arrange
      const inactiveUser = { ...mockUser, isActive: false };
      userService.findByEmail.mockResolvedValue(inactiveUser);
      mockedBcrypt.compare.mockResolvedValue(true as never);

      // Act & Assert
      await expect(authService.login(loginDto)).rejects.toThrow(
        'Account is disabled'
      );
    });
  });

  describe('validateToken', () => {
    it('should validate valid JWT token', async () => {
      // Arrange
      const payload = {
        sub: mockUser.id,
        email: mockUser.email,
        roles: mockUser.roles,
      };
      jwtService.verify.mockReturnValue(payload);
      userService.findById.mockResolvedValue(mockUser);

      // Act
      const result = await authService.validateToken('valid-token');

      // Assert
      expect(jwtService.verify).toHaveBeenCalledWith('valid-token');
      expect(userService.findById).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual(mockUser);
    });

    it('should throw error for invalid token', async () => {
      // Arrange
      jwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act & Assert
      await expect(authService.validateToken('invalid-token')).rejects.toThrow(
        'Invalid token'
      );
    });

    it('should throw error for token with non-existent user', async () => {
      // Arrange
      const payload = { sub: 'non-existent-id', email: '<EMAIL>' };
      jwtService.verify.mockReturnValue(payload);
      userService.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.validateToken('valid-token')).rejects.toThrow(
        'User not found'
      );
    });
  });

  describe('refreshToken', () => {
    it('should generate new access token with valid refresh token', async () => {
      // Arrange
      const payload = {
        sub: mockUser.id,
        email: mockUser.email,
        type: 'refresh',
      };
      jwtService.verify.mockReturnValue(payload);
      userService.findById.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('new-access-token');

      // Act
      const result = await authService.refreshToken('valid-refresh-token');

      // Assert
      expect(jwtService.verify).toHaveBeenCalledWith(
        'valid-refresh-token',
        expect.objectContaining({ secret: 'test-refresh-secret' })
      );
      expect(result).toEqual({
        accessToken: 'new-access-token',
      });
    });

    it('should throw error for invalid refresh token type', async () => {
      // Arrange
      const payload = {
        sub: mockUser.id,
        email: mockUser.email,
        type: 'access', // Wrong type
      };
      jwtService.verify.mockReturnValue(payload);

      // Act & Assert
      await expect(
        authService.refreshToken('invalid-refresh-token')
      ).rejects.toThrow('Invalid refresh token');
    });
  });

  describe('generateTokens', () => {
    it('should generate both access and refresh tokens', () => {
      // Arrange
      jwtService.sign
        .mockReturnValueOnce('access-token')
        .mockReturnValueOnce('refresh-token');

      // Act
      const result = authService.generateTokens(mockUser);

      // Assert
      expect(jwtService.sign).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
      });
    });
  });

  describe('hashPassword', () => {
    it('should hash password with configured salt rounds', async () => {
      // Arrange
      mockedBcrypt.hash.mockResolvedValue('hashed-password' as never);

      // Act
      const result = await authService.hashPassword('plain-password');

      // Assert
      expect(mockedBcrypt.hash).toHaveBeenCalledWith('plain-password', 12);
      expect(result).toBe('hashed-password');
    });
  });

  describe('Security Tests', () => {
    it('should handle rate limiting for failed login attempts', async () => {
      // Arrange
      userService.findByEmail.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(false as never);

      const loginDto = { email: '<EMAIL>', password: 'wrong-pass' };

      // Act & Assert - Simulate multiple failed attempts
      for (let i = 0; i < 3; i++) {
        await expect(authService.login(loginDto)).rejects.toThrow(
          'Invalid credentials'
        );
      }

      // Should implement rate limiting after multiple failures
      expect(userService.findByEmail).toHaveBeenCalledTimes(3);
    });

    it('should not expose timing attacks in user validation', async () => {
      // Arrange
      const startTime = Date.now();
      userService.findByEmail.mockResolvedValue(null);

      // Act
      try {
        await authService.login({
          email: '<EMAIL>',
          password: 'any-password',
        });
      } catch (error) {
        // Expected to throw
      }

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Assert - Should take reasonable time even for non-existent user
      expect(executionTime).toBeGreaterThan(50); // Minimum processing time
    });

    it('should sanitize user data in responses', async () => {
      // Arrange
      userService.findByEmail.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(true as never);
      jwtService.sign.mockReturnValue('token');

      // Act
      const result = await authService.login({
        email: '<EMAIL>',
        password: 'correct-password',
      });

      // Assert - Password should not be in response
      expect(result.user).not.toHaveProperty('password');
      expect(result.user).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        name: mockUser.name,
        roles: mockUser.roles,
      });
    });
  });
});