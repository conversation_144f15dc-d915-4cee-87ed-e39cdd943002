/**
 * 🧪 User Repository Integration Tests
 * 
 * Integration tests for user repository with real database:
 * - CRUD operations
 * - Transaction handling
 * - Query optimization
 * - Data integrity constraints
 */

import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';

import { User } from '../../../apps/api-gateway/src/modules/users/entities/user.entity';
import { UserRepository } from '../../../apps/api-gateway/src/modules/users/repositories/user.repository';
import { CreateUserDto } from '../../../apps/api-gateway/src/modules/auth/dto/create-user.dto';

describe('UserRepository Integration Tests', () => {
  let module: TestingModule;
  let userRepository: UserRepository;
  let dataSource: DataSource;
  let repository: Repository<User>;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: (configService: ConfigService) => ({
            type: 'postgres',
            host: configService.get('TEST_DATABASE_HOST', 'localhost'),
            port: configService.get('TEST_DATABASE_PORT', 5432),
            username: configService.get('TEST_DATABASE_USER', 'test_user'),
            password: configService.get('TEST_DATABASE_PASSWORD', 'test_password'),
            database: configService.get('TEST_DATABASE_NAME', 'test_db'),
            entities: [User],
            synchronize: true,
            logging: false,
            dropSchema: true,
          }),
          inject: [ConfigService],
        }),
        TypeOrmModule.forFeature([User]),
      ],
      providers: [UserRepository],
    }).compile();

    userRepository = module.get<UserRepository>(UserRepository);
    dataSource = module.get<DataSource>(DataSource);
    repository = dataSource.getRepository(User);
  });

  afterAll(async () => {
    await dataSource.destroy();
    await module.close();
  });

  beforeEach(async () => {
    // Clean database before each test
    await repository.clear();
  });

  describe('create', () => {
    it('should create user successfully with valid data', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        name: 'Test User',
        password: 'hashedPassword123',
      };

      // Act
      const createdUser = await userRepository.create(createUserDto);

      // Assert
      expect(createdUser).toMatchObject({
        id: expect.any(String),
        email: createUserDto.email,
        name: createUserDto.name,
        password: createUserDto.password,
        roles: ['user'],
        isActive: true,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });

      // Verify in database
      const userInDb = await repository.findOne({
        where: { id: createdUser.id },
      });
      expect(userInDb).toBeDefined();
      expect(userInDb.email).toBe(createUserDto.email);
    });

    it('should throw error for duplicate email', async () => {
      // Arrange
      const userDto = {
        email: '<EMAIL>',
        name: 'First User',
        password: 'password123',
      };

      await userRepository.create(userDto);

      const duplicateDto = {
        email: '<EMAIL>',
        name: 'Second User',
        password: 'password456',
      };

      // Act & Assert
      await expect(userRepository.create(duplicateDto)).rejects.toThrow();
    });

    it('should handle special characters in user data', async () => {
      // Arrange
      const specialCharDto = {
        email: 'special@tëst.com',
        name: 'Tëst Üser 中文',
        password: 'pä$$w0rd!@#',
      };

      // Act
      const user = await userRepository.create(specialCharDto);

      // Assert
      expect(user.name).toBe(specialCharDto.name);
      expect(user.email).toBe(specialCharDto.email);
    });
  });

  describe('findByEmail', () => {
    it('should find user by email successfully', async () => {
      // Arrange
      const userDto = {
        email: '<EMAIL>',
        name: 'Find Me',
        password: 'password123',
      };
      const createdUser = await userRepository.create(userDto);

      // Act
      const foundUser = await userRepository.findByEmail(userDto.email);

      // Assert
      expect(foundUser).toBeDefined();
      expect(foundUser.id).toBe(createdUser.id);
      expect(foundUser.email).toBe(userDto.email);
    });

    it('should return null for non-existent email', async () => {
      // Act
      const user = await userRepository.findByEmail('<EMAIL>');

      // Assert
      expect(user).toBeNull();
    });

    it('should be case-insensitive for email search', async () => {
      // Arrange
      const userDto = {
        email: '<EMAIL>',
        name: 'Case User',
        password: 'password123',
      };
      await userRepository.create(userDto);

      // Act
      const foundUser = await userRepository.findByEmail('<EMAIL>');

      // Assert
      expect(foundUser).toBeDefined();
      expect(foundUser.email.toLowerCase()).toBe(userDto.email.toLowerCase());
    });
  });

  describe('findById', () => {
    it('should find user by ID successfully', async () => {
      // Arrange
      const userDto = {
        email: '<EMAIL>',
        name: 'Find By ID',
        password: 'password123',
      };
      const createdUser = await userRepository.create(userDto);

      // Act
      const foundUser = await userRepository.findById(createdUser.id);

      // Assert
      expect(foundUser).toBeDefined();
      expect(foundUser.id).toBe(createdUser.id);
      expect(foundUser.email).toBe(userDto.email);
    });

    it('should return null for non-existent ID', async () => {
      // Act
      const user = await userRepository.findById('non-existent-id');

      // Assert
      expect(user).toBeNull();
    });
  });

  describe('update', () => {
    it('should update user successfully', async () => {
      // Arrange
      const userDto = {
        email: '<EMAIL>',
        name: 'Original Name',
        password: 'password123',
      };
      const createdUser = await userRepository.create(userDto);

      const updateData = {
        name: 'Updated Name',
        lastLoginAt: new Date(),
      };

      // Act
      const updatedUser = await userRepository.update(createdUser.id, updateData);

      // Assert
      expect(updatedUser).toBeDefined();
      expect(updatedUser.name).toBe(updateData.name);
      expect(updatedUser.lastLoginAt).toEqual(updateData.lastLoginAt);
      expect(updatedUser.email).toBe(userDto.email); // Unchanged
    });

    it('should throw error when updating non-existent user', async () => {
      // Act & Assert
      await expect(
        userRepository.update('non-existent-id', { name: 'New Name' })
      ).rejects.toThrow();
    });
  });

  describe('delete', () => {
    it('should soft delete user successfully', async () => {
      // Arrange
      const userDto = {
        email: '<EMAIL>',
        name: 'Delete Me',
        password: 'password123',
      };
      const createdUser = await userRepository.create(userDto);

      // Act
      await userRepository.delete(createdUser.id);

      // Assert
      const deletedUser = await userRepository.findById(createdUser.id);
      expect(deletedUser).toBeNull();

      // Check if soft deleted (with deleted records)
      const userWithDeleted = await repository.findOne({
        where: { id: createdUser.id },
        withDeleted: true,
      });
      expect(userWithDeleted).toBeDefined();
      expect(userWithDeleted.deletedAt).toBeDefined();
    });
  });

  describe('findByRole', () => {
    it('should find users by role successfully', async () => {
      // Arrange
      await userRepository.create({
        email: '<EMAIL>',
        name: 'Admin User',
        password: 'password123',
        roles: ['admin', 'user'],
      });

      await userRepository.create({
        email: '<EMAIL>',
        name: 'Regular User',
        password: 'password123',
        roles: ['user'],
      });

      // Act
      const adminUsers = await userRepository.findByRole('admin');
      const regularUsers = await userRepository.findByRole('user');

      // Assert
      expect(adminUsers).toHaveLength(1);
      expect(adminUsers[0].roles).toContain('admin');
      
      expect(regularUsers).toHaveLength(2);
      regularUsers.forEach(user => {
        expect(user.roles).toContain('user');
      });
    });
  });

  describe('updateLastLogin', () => {
    it('should update user last login timestamp', async () => {
      // Arrange
      const userDto = {
        email: '<EMAIL>',
        name: 'Login User',
        password: 'password123',
      };
      const createdUser = await userRepository.create(userDto);

      // Act
      const beforeUpdate = new Date();
      await userRepository.updateLastLogin(createdUser.id);

      // Assert
      const updatedUser = await userRepository.findById(createdUser.id);
      expect(updatedUser.lastLoginAt).toBeDefined();
      expect(updatedUser.lastLoginAt).toBeInstanceOf(Date);
      expect(updatedUser.lastLoginAt.getTime()).toBeGreaterThanOrEqual(
        beforeUpdate.getTime()
      );
    });
  });

  describe('findActiveUsers', () => {
    it('should return only active users', async () => {
      // Arrange
      const activeUser = await userRepository.create({
        email: '<EMAIL>',
        name: 'Active User',
        password: 'password123',
        isActive: true,
      });

      const inactiveUser = await userRepository.create({
        email: '<EMAIL>',
        name: 'Inactive User',
        password: 'password123',
        isActive: false,
      });

      // Act
      const activeUsers = await userRepository.findActiveUsers();

      // Assert
      expect(activeUsers).toHaveLength(1);
      expect(activeUsers[0].id).toBe(activeUser.id);
      expect(activeUsers[0].isActive).toBe(true);

      const foundInactiveUser = activeUsers.find(u => u.id === inactiveUser.id);
      expect(foundInactiveUser).toBeUndefined();
    });
  });

  describe('Transaction Tests', () => {
    it('should handle transaction rollback on error', async () => {
      // Arrange
      const userDto = {
        email: '<EMAIL>',
        name: 'Transaction User',
        password: 'password123',
      };

      // Act & Assert
      await expect(async () => {
        await dataSource.transaction(async (manager) => {
          const userRepo = manager.getRepository(User);
          
          // Create user
          const user = userRepo.create(userDto);
          await userRepo.save(user);

          // Simulate error
          throw new Error('Transaction failed');
        });
      }).rejects.toThrow('Transaction failed');

      // Verify rollback - user should not exist
      const user = await userRepository.findByEmail(userDto.email);
      expect(user).toBeNull();
    });

    it('should commit transaction successfully', async () => {
      // Arrange
      const userDto = {
        email: '<EMAIL>',
        name: 'Commit User',
        password: 'password123',
      };

      let userId: string;

      // Act
      await dataSource.transaction(async (manager) => {
        const userRepo = manager.getRepository(User);
        
        const user = userRepo.create(userDto);
        const savedUser = await userRepo.save(user);
        userId = savedUser.id;
      });

      // Assert
      const user = await userRepository.findById(userId);
      expect(user).toBeDefined();
      expect(user.email).toBe(userDto.email);
    });
  });

  describe('Performance Tests', () => {
    it('should handle bulk user creation efficiently', async () => {
      // Arrange
      const users = Array.from({ length: 100 }, (_, index) => ({
        email: `bulk${index}@example.com`,
        name: `Bulk User ${index}`,
        password: 'password123',
      }));

      const startTime = Date.now();

      // Act
      const createdUsers = await Promise.all(
        users.map(userDto => userRepository.create(userDto))
      );

      const endTime = Date.now();

      // Assert
      expect(createdUsers).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(10000); // Should complete in under 10 seconds

      // Verify all users exist
      const userCount = await repository.count();
      expect(userCount).toBe(100);
    });

    it('should optimize queries with proper indexing', async () => {
      // Arrange - Create users with different emails
      const users = Array.from({ length: 50 }, (_, index) => ({
        email: `query${index}@example.com`,
        name: `Query User ${index}`,
        password: 'password123',
      }));

      await Promise.all(users.map(userDto => userRepository.create(userDto)));

      // Act & Assert - Email searches should be fast
      const startTime = Date.now();
      
      for (let i = 0; i < 10; i++) {
        await userRepository.findByEmail(`query${i}@example.com`);
      }

      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(1000); // Should be under 1 second for 10 queries
    });
  });
});
