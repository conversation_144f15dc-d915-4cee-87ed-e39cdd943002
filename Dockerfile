# Multi-stage build for production optimization
# Build stage
FROM denoland/deno:alpine AS builder

# Use existing deno user from base image

WORKDIR /app

# Copy dependency files first for better caching
COPY deno.json deno.lock ./

# Cache dependencies
RUN deno cache --lock=deno.lock deno.json

# Copy source code
COPY src/ ./src/
COPY tests/ ./tests/

# Cache the application
RUN deno cache src/app.ts

# Production stage
FROM denoland/deno:alpine AS production

# Use existing deno user from base image

WORKDIR /app

# Copy only necessary files from builder
COPY --from=builder --chown=deno:deno /app/deno.json /app/deno.lock ./
COPY --from=builder --chown=deno:deno /app/src/ ./src/

# Switch to non-root user
USER deno

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD deno eval "try { await fetch('http://localhost:8000/health'); } catch { Deno.exit(1); }"

# Run with minimal permissions
CMD ["deno", "run", "--allow-net=0.0.0.0:8000", "--allow-read=/app", "--allow-env", "src/app.ts"]
