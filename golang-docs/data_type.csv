Category,Type,Size_Range,Zero_Value,Example
<PERSON><PERSON>an,bool,true/false,false,var flag bool = true
Integer,int,Platform dependent,0,var count int = 42
Integer,int8,1 byte (-128 to 127),0,var b int8 = 127
Integer,int16,2 bytes (-32768 to 32767),0,var s int16 = 1000
Integer,int32,4 bytes (-2^31 to 2^31-1),0,var i int32 = 100000
Integer,int64,8 bytes (-2^63 to 2^63-1),0,var l int64 = 1000000
Integer,rune,alias for int32,0,var r rune = 'A'
Unsigned Integer,uint,Platform dependent,0,var u uint = 42
Unsigned Integer,uint8,1 byte (0 to 255),0,var b uint8 = 255
Unsigned Integer,uint16,2 bytes (0 to 65535),0,var s uint16 = 65000
Unsigned Integer,uint32,4 bytes (0 to 2^32-1),0,var i uint32 = 4000000
Unsigned Integer,uint64,8 bytes (0 to 2^64-1),0,var l uint64 = 18446744073709551615
Float,float32,4 bytes (IEEE-754),0.0,var f float32 = 3.14
Float,float64,8 bytes (IEEE-754),0.0,var d float64 = 2.718281828
Complex,complex64,64-bit float + 64-bit imaginary,(0+0i),var c complex64 = 1+2i
Complex,complex128,128-bit float + 128-bit imaginary,(0+0i),var c complex128 = 3+4i
String,string,UTF-8 encoded,"""""","var name string = ""Go"""
String,byte,alias for uint8,0,var b byte = 65
Composite,array,Fixed size collection,elements are zero values,var arr [5]int
Composite,slice,Dynamic array,nil,var slice []int
Composite,map,Key-value pairs,nil,var m map[string]int
Composite,struct,Custom type,zero values for fields,type Person struct{}
Composite,pointer,Memory address,nil,var ptr *int
Composite,interface,Method set,nil,var i interface{}