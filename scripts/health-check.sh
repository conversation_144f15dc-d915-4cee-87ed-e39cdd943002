#!/bin/bash

# 🏥 Enterprise Platform Health Check Script
# Comprehensive health monitoring for all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis
HEART="💓"
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"

# Health check configuration
TIMEOUT=10
RETRY_COUNT=3
RETRY_DELAY=2

# Function to print colored output
print_status() {
    local color=$1
    local emoji=$2
    local message=$3
    echo -e "${color}${emoji} ${message}${NC}"
}

print_success() { print_status "$GREEN" "$CHECK" "$1"; }
print_error() { print_status "$RED" "$CROSS" "$1"; }
print_warning() { print_status "$YELLOW" "$WARNING" "$1"; }
print_info() { print_status "$BLUE" "$INFO" "$1"; }
print_header() { print_status "$PURPLE" "$HEART" "$1"; }

# Function to check HTTP endpoint
check_http_endpoint() {
    local url=$1
    local name=$2
    local expected_status=${3:-200}

    for i in $(seq 1 $RETRY_COUNT); do
        if curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$url" | grep -q "$expected_status"; then
            print_success "$name is healthy (HTTP $expected_status)"
            return 0
        fi

        if [ $i -lt $RETRY_COUNT ]; then
            print_warning "$name check failed, retrying in ${RETRY_DELAY}s... (attempt $i/$RETRY_COUNT)"
            sleep $RETRY_DELAY
        fi
    done

    print_error "$name is unhealthy (failed after $RETRY_COUNT attempts)"
    return 1
}

# Function to check TCP port
check_tcp_port() {
    local host=$1
    local port=$2
    local name=$3

    for i in $(seq 1 $RETRY_COUNT); do
        if nc -z -w $TIMEOUT "$host" "$port" 2>/dev/null; then
            print_success "$name is reachable (TCP $host:$port)"
            return 0
        fi

        if [ $i -lt $RETRY_COUNT ]; then
            print_warning "$name check failed, retrying in ${RETRY_DELAY}s... (attempt $i/$RETRY_COUNT)"
            sleep $RETRY_DELAY
        fi
    done

    print_error "$name is unreachable (TCP $host:$port)"
    return 1
}

# Function to check Docker container
check_docker_container() {
    local container_name=$1
    local service_name=$2

    if docker ps --format "table {{.Names}}" | grep -q "$container_name"; then
        local status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")
        case $status in
            "healthy")
                print_success "$service_name container is healthy"
                return 0
                ;;
            "unhealthy")
                print_error "$service_name container is unhealthy"
                return 1
                ;;
            "starting")
                print_warning "$service_name container is starting"
                return 1
                ;;
            *)
                print_success "$service_name container is running (no health check)"
                return 0
                ;;
        esac
    else
        print_error "$service_name container is not running"
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    local db_type=$1
    local connection_string=$2
    local name=$3

    case $db_type in
        "postgres")
            if docker-compose exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
                print_success "$name database is ready"
                return 0
            else
                print_error "$name database is not ready"
                return 1
            fi
            ;;
        "redis")
            if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
                print_success "$name cache is ready"
                return 0
            else
                print_error "$name cache is not ready"
                return 1
            fi
            ;;
        *)
            print_warning "Unknown database type: $db_type"
            return 1
            ;;
    esac
}

# Function to check system resources
check_system_resources() {
    print_header "System Resources"

    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        print_success "Disk usage: ${disk_usage}% (healthy)"
    elif [ "$disk_usage" -lt 90 ]; then
        print_warning "Disk usage: ${disk_usage}% (warning)"
    else
        print_error "Disk usage: ${disk_usage}% (critical)"
    fi

    # Check memory usage
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$memory_usage" -lt 80 ]; then
        print_success "Memory usage: ${memory_usage}% (healthy)"
    elif [ "$memory_usage" -lt 90 ]; then
        print_warning "Memory usage: ${memory_usage}% (warning)"
    else
        print_error "Memory usage: ${memory_usage}% (critical)"
    fi

    # Check CPU load
    local cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local cpu_usage=$(echo "$cpu_load * 100 / $cpu_cores" | bc -l | cut -d. -f1)

    if [ "$cpu_usage" -lt 70 ]; then
        print_success "CPU load: ${cpu_load} (${cpu_usage}% - healthy)"
    elif [ "$cpu_usage" -lt 90 ]; then
        print_warning "CPU load: ${cpu_load} (${cpu_usage}% - warning)"
    else
        print_error "CPU load: ${cpu_load} (${cpu_usage}% - critical)"
    fi
}

# Main health check function
main_health_check() {
    print_header "Enterprise Platform Health Check"
    echo ""

    local overall_health=0

    # Check system resources
    check_system_resources
    echo ""

    # Check Docker containers
    print_header "Docker Containers"
    check_docker_container "postgres" "PostgreSQL" || ((overall_health++))
    check_docker_container "redis" "Redis" || ((overall_health++))
    check_docker_container "api-gateway" "API Gateway" || ((overall_health++))
    check_docker_container "ai-service" "AI Service" || ((overall_health++))
    check_docker_container "grafana" "Grafana" || ((overall_health++))
    check_docker_container "prometheus" "Prometheus" || ((overall_health++))
    echo ""

    # Check databases
    print_header "Database Connectivity"
    check_database "postgres" "" "PostgreSQL" || ((overall_health++))
    check_database "redis" "" "Redis" || ((overall_health++))
    echo ""

    # Check HTTP endpoints
    print_header "HTTP Endpoints"
    check_http_endpoint "http://localhost:3000/health" "API Gateway Health" || ((overall_health++))
    check_http_endpoint "http://localhost:3000/docs" "API Documentation" || ((overall_health++))
    check_http_endpoint "http://localhost:8000/health" "AI Service Health" || ((overall_health++))
    check_http_endpoint "http://localhost:8000/docs" "AI Service Docs" || ((overall_health++))
    check_http_endpoint "http://localhost:3001" "Grafana Dashboard" || ((overall_health++))
    check_http_endpoint "http://localhost:9090" "Prometheus Metrics" || ((overall_health++))
    check_http_endpoint "http://localhost:16686" "Jaeger Tracing" || ((overall_health++))
    echo ""

    # Check TCP ports
    print_header "TCP Port Connectivity"
    check_tcp_port "localhost" "5432" "PostgreSQL Port" || ((overall_health++))
    check_tcp_port "localhost" "6379" "Redis Port" || ((overall_health++))
    check_tcp_port "localhost" "3000" "API Gateway Port" || ((overall_health++))
    check_tcp_port "localhost" "8000" "AI Service Port" || ((overall_health++))
    echo ""

    # Check API functionality
    print_header "API Functionality"

    # Test API Gateway endpoints
    if curl -s "http://localhost:3000/api/v1/health" | grep -q "ok"; then
        print_success "API Gateway health endpoint working"
    else
        print_error "API Gateway health endpoint not working"
        ((overall_health++))
    fi

    # Test AI Service endpoints
    if curl -s "http://localhost:8000/health" | grep -q "healthy"; then
        print_success "AI Service health endpoint working"
    else
        print_error "AI Service health endpoint not working"
        ((overall_health++))
    fi

    echo ""

    # Overall health summary
    print_header "Health Check Summary"

    if [ $overall_health -eq 0 ]; then
        print_success "All systems are healthy! 🎉"
        echo ""
        echo -e "${GREEN}🌟 Platform Status: EXCELLENT${NC}"
        echo -e "${GREEN}✅ All services are running optimally${NC}"
        echo -e "${GREEN}✅ All endpoints are responding${NC}"
        echo -e "${GREEN}✅ All databases are connected${NC}"
        echo -e "${GREEN}✅ System resources are healthy${NC}"
    elif [ $overall_health -le 3 ]; then
        print_warning "Some issues detected but system is mostly healthy"
        echo ""
        echo -e "${YELLOW}⚠️ Platform Status: DEGRADED${NC}"
        echo -e "${YELLOW}⚠️ $overall_health issues detected${NC}"
        echo -e "${YELLOW}⚠️ Some services may be starting up${NC}"
        echo -e "${YELLOW}⚠️ Monitor and retry if needed${NC}"
    else
        print_error "Multiple issues detected - system needs attention"
        echo ""
        echo -e "${RED}🚨 Platform Status: UNHEALTHY${NC}"
        echo -e "${RED}❌ $overall_health issues detected${NC}"
        echo -e "${RED}❌ Immediate attention required${NC}"
        echo -e "${RED}❌ Check logs and service status${NC}"
    fi

    echo ""
    echo -e "${BLUE}📊 Quick Actions:${NC}"
    echo -e "${BLUE}  • View logs: make logs${NC}"
    echo -e "${BLUE}  • Restart services: make restart${NC}"
    echo -e "${BLUE}  • Full reset: make full-reset${NC}"
    echo -e "${BLUE}  • Service status: make status${NC}"

    return $overall_health
}

# Function to run continuous health monitoring
continuous_monitoring() {
    local interval=${1:-30}

    print_header "Starting Continuous Health Monitoring (interval: ${interval}s)"
    print_info "Press Ctrl+C to stop monitoring"
    echo ""

    while true; do
        clear
        main_health_check
        echo ""
        print_info "Next check in ${interval} seconds..."
        sleep $interval
    done
}

# Function to export health check results
export_health_report() {
    local output_file="health-report-$(date +%Y%m%d-%H%M%S).json"

    print_header "Exporting Health Report"

    # Create JSON report
    cat > "$output_file" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "platform": "enterprise-platform",
  "version": "1.0.0",
  "health_check": {
    "overall_status": "$([ $? -eq 0 ] && echo "healthy" || echo "unhealthy")",
    "services": {
      "api_gateway": "$(curl -s http://localhost:3000/health >/dev/null && echo "healthy" || echo "unhealthy")",
      "ai_service": "$(curl -s http://localhost:8000/health >/dev/null && echo "healthy" || echo "unhealthy")",
      "postgres": "$(docker-compose exec -T postgres pg_isready -U postgres >/dev/null 2>&1 && echo "healthy" || echo "unhealthy")",
      "redis": "$(docker-compose exec -T redis redis-cli ping >/dev/null 2>&1 && echo "healthy" || echo "unhealthy")",
      "grafana": "$(curl -s http://localhost:3001 >/dev/null && echo "healthy" || echo "unhealthy")",
      "prometheus": "$(curl -s http://localhost:9090 >/dev/null && echo "healthy" || echo "unhealthy")"
    },
    "system_resources": {
      "disk_usage": "$(df / | awk 'NR==2 {print $5}')",
      "memory_usage": "$(free | awk 'NR==2{printf "%.0f%%", $3*100/$2}')",
      "cpu_load": "$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')"
    }
  }
}
EOF

    print_success "Health report exported to: $output_file"
}

# Parse command line arguments
case "${1:-check}" in
    "check"|"")
        main_health_check
        ;;
    "monitor")
        continuous_monitoring "${2:-30}"
        ;;
    "export")
        main_health_check
        export_health_report
        ;;
    "help"|"-h"|"--help")
        echo "Enterprise Platform Health Check Script"
        echo ""
        echo "Usage: $0 [command] [options]"
        echo ""
        echo "Commands:"
        echo "  check          Run one-time health check (default)"
        echo "  monitor [sec]  Run continuous monitoring (default: 30s interval)"
        echo "  export         Run health check and export JSON report"
        echo "  help           Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                    # Run health check"
        echo "  $0 monitor           # Monitor with 30s interval"
        echo "  $0 monitor 60        # Monitor with 60s interval"
        echo "  $0 export            # Export health report"
        echo ""
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac