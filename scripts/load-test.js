import http from "k6/http";
import { check, sleep } from "k6";
import { Rate } from "k6/metrics";

// Custom metrics
const errorRate = new Rate("errors");

// Test configuration
export const options = {
  stages: [
    { duration: "30s", target: 20 }, // Ramp up to 20 users
    { duration: "1m", target: 20 }, // Stay at 20 users
    { duration: "20s", target: 0 }, // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ["p(95)<500"], // 95% of requests must complete below 500ms
    http_req_failed: ["rate<0.1"], // Error rate must be less than 10%
    errors: ["rate<0.1"], // Custom error rate
  },
};

// Test scenarios
export default function () {
  const baseUrl = __ENV.BASE_URL || "http://localhost:8000";

  // Health check - this should always work
  const healthCheck = http.get(`${baseUrl}/health`);
  const healthCheckPassed = check(healthCheck, {
    "health check status is 200": (r) => r.status === 200,
    "health check response time < 500ms": (r) => r.timings.duration < 500,
    "health check has correct content": (r) =>
      r.body && r.body.includes("status"),
  });

  // Record errors for health check (critical)
  errorRate.add(!healthCheckPassed);

  // Think time between requests
  sleep(1);
}

// Setup function (runs once before the test)
export function setup() {
  console.log("Starting load test...");
  console.log(`Base URL: ${__ENV.BASE_URL || "http://localhost:8000"}`);
}

// Teardown function (runs once after the test)
export function teardown() {
  console.log("Load test completed");
}
