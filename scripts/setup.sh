#!/bin/bash

# 🚀 Enterprise Platform Setup Script
# Complete one-command setup for the entire enterprise architecture

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis for better UX
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/setup.log"

# Function to print colored output
print_status() {
    local color=$1
    local emoji=$2
    local message=$3
    echo -e "${color}${emoji} ${message}${NC}"
}

print_success() { print_status "$GREEN" "$CHECK" "$1"; }
print_error() { print_status "$RED" "$CROSS" "$1"; }
print_warning() { print_status "$YELLOW" "$WARNING" "$1"; }
print_info() { print_status "$BLUE" "$INFO" "$1"; }
print_header() { print_status "$PURPLE" "$ROCKET" "$1"; }

# Function to log and execute commands
execute_command() {
    local cmd="$1"
    local description="$2"

    print_info "Executing: $description"
    echo "$(date): $description - $cmd" >> "$LOG_FILE"

    if eval "$cmd" >> "$LOG_FILE" 2>&1; then
        print_success "$description completed"
        return 0
    else
        print_error "$description failed"
        echo "Check $LOG_FILE for details"
        return 1
    fi
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check system requirements
check_requirements() {
    print_header "Checking System Requirements"

    local requirements_met=true

    # Check Node.js
    if command_exists node; then
        local node_version=$(node --version | cut -d'v' -f2)
        local required_node="18.0.0"
        if [ "$(printf '%s\n' "$required_node" "$node_version" | sort -V | head -n1)" = "$required_node" ]; then
            print_success "Node.js $node_version (>= $required_node required)"
        else
            print_error "Node.js $node_version found, but >= $required_node required"
            requirements_met=false
        fi
    else
        print_error "Node.js not found. Please install Node.js >= 18.0.0"
        requirements_met=false
    fi

    # Check npm
    if command_exists npm; then
        local npm_version=$(npm --version)
        print_success "npm $npm_version"
    else
        print_error "npm not found"
        requirements_met=false
    fi

    # Check Python
    if command_exists python3; then
        local python_version=$(python3 --version | cut -d' ' -f2)
        local required_python="3.9.0"
        if [ "$(printf '%s\n' "$required_python" "$python_version" | sort -V | head -n1)" = "$required_python" ]; then
            print_success "Python $python_version (>= $required_python required)"
        else
            print_error "Python $python_version found, but >= $required_python required"
            requirements_met=false
        fi
    else
        print_error "Python 3 not found. Please install Python >= 3.9.0"
        requirements_met=false
    fi

    # Check Docker
    if command_exists docker; then
        if docker info >/dev/null 2>&1; then
            local docker_version=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
            print_success "Docker $docker_version (running)"
        else
            print_error "Docker found but not running. Please start Docker"
            requirements_met=false
        fi
    else
        print_error "Docker not found. Please install Docker"
        requirements_met=false
    fi

    # Check Docker Compose
    if command_exists docker-compose || docker compose version >/dev/null 2>&1; then
        print_success "Docker Compose available"
    else
        print_error "Docker Compose not found"
        requirements_met=false
    fi

    # Check Git
    if command_exists git; then
        local git_version=$(git --version | cut -d' ' -f3)
        print_success "Git $git_version"
    else
        print_error "Git not found"
        requirements_met=false
    fi

    if [ "$requirements_met" = false ]; then
        print_error "Some requirements are not met. Please install missing dependencies."
        exit 1
    fi

    print_success "All system requirements met!"
}

# Function to setup project dependencies
setup_dependencies() {
    print_header "Setting Up Project Dependencies"

    cd "$PROJECT_ROOT"

    # Install root dependencies
    if [ -f "package.json" ]; then
        execute_command "npm install" "Installing root dependencies"
    fi

    # Install service dependencies
    for service_dir in services/*/; do
        if [ -d "$service_dir" ] && [ -f "${service_dir}package.json" ]; then
            local service_name=$(basename "$service_dir")
            cd "$service_dir"
            execute_command "npm install" "Installing dependencies for $service_name"
            cd "$PROJECT_ROOT"
        fi
    done

    # Install app dependencies
    for app_dir in apps/*/; do
        if [ -d "$app_dir" ] && [ -f "${app_dir}package.json" ]; then
            local app_name=$(basename "$app_dir")
            cd "$app_dir"
            execute_command "npm install" "Installing dependencies for $app_name"
            cd "$PROJECT_ROOT"
        fi
    done

    # Setup Python virtual environment and dependencies
    if [ -f "requirements.txt" ]; then
        execute_command "python3 -m venv venv" "Creating Python virtual environment"
        execute_command "source venv/bin/activate && pip install -r requirements.txt" "Installing Python dependencies"
    fi
}

# Function to setup environment files
setup_environment() {
    print_header "Setting Up Environment Configuration"

    cd "$PROJECT_ROOT"

    # Copy environment templates
    if [ -f ".env.template" ] && [ ! -f ".env" ]; then
        execute_command "cp .env.template .env" "Creating root .env file"
        print_warning "Please edit .env file with your configuration"
    fi

    # Setup service environment files
    for service_dir in services/*/; do
        if [ -d "$service_dir" ]; then
            local service_name=$(basename "$service_dir")
            if [ -f "${service_dir}.env.template" ] && [ ! -f "${service_dir}.env" ]; then
                execute_command "cp ${service_dir}.env.template ${service_dir}.env" "Creating .env for $service_name"
            fi
        fi
    done

    # Setup app environment files
    for app_dir in apps/*/; do
        if [ -d "$app_dir" ]; then
            local app_name=$(basename "$app_dir")
            if [ -f "${app_dir}.env.template" ] && [ ! -f "${app_dir}.env" ]; then
                execute_command "cp ${app_dir}.env.template ${app_dir}.env" "Creating .env for $app_name"
            fi
        fi
    done
}

# Function to setup Docker infrastructure
setup_docker() {
    print_header "Setting Up Docker Infrastructure"

    cd "$PROJECT_ROOT"

    # Build Docker images
    if [ -f "docker-compose.yml" ]; then
        execute_command "docker-compose build" "Building Docker images"
        execute_command "docker-compose up -d postgres redis" "Starting database services"

        # Wait for databases to be ready
        print_info "Waiting for databases to be ready..."
        sleep 10

        # Check if databases are ready
        local max_attempts=30
        local attempt=1

        while [ $attempt -le $max_attempts ]; do
            if docker-compose exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
                print_success "PostgreSQL is ready"
                break
            fi

            if [ $attempt -eq $max_attempts ]; then
                print_error "PostgreSQL failed to start after $max_attempts attempts"
                return 1
            fi

            print_info "Waiting for PostgreSQL... (attempt $attempt/$max_attempts)"
            sleep 2
            ((attempt++))
        done

        # Test Redis connection
        if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
            print_success "Redis is ready"
        else
            print_warning "Redis may not be ready yet"
        fi
    fi
}

# Function to run database migrations
setup_database() {
    print_header "Setting Up Database"

    cd "$PROJECT_ROOT"

    # Run migrations for each service that has them
    for service_dir in services/*/; do
        if [ -d "$service_dir" ]; then
            local service_name=$(basename "$service_dir")
            cd "$service_dir"

            if [ -f "package.json" ] && grep -q "migration:run" package.json; then
                execute_command "npm run migration:run" "Running migrations for $service_name"
            fi

            cd "$PROJECT_ROOT"
        fi
    done
}

# Function to setup monitoring stack
setup_monitoring() {
    print_header "Setting Up Monitoring Stack"

    cd "$PROJECT_ROOT"

    if [ -f "docker-compose.yml" ]; then
        # Start monitoring services
        execute_command "docker-compose up -d grafana prometheus jaeger" "Starting monitoring services"

        # Wait for services to be ready
        sleep 5

        print_info "Monitoring services starting up..."
        print_info "Grafana will be available at: http://localhost:3001"
        print_info "Prometheus will be available at: http://localhost:9090"
        print_info "Jaeger will be available at: http://localhost:16686"
    fi
}

# Function to run health checks
run_health_checks() {
    print_header "Running Health Checks"

    local services_healthy=true

    # Check if main services are responding
    local services=(
        "http://localhost:3000/health:API Gateway"
        "http://localhost:8000/health:AI Service"
        "http://localhost:3001:Grafana"
        "http://localhost:9090:Prometheus"
    )

    for service in "${services[@]}"; do
        local url=$(echo "$service" | cut -d':' -f1-2)
        local name=$(echo "$service" | cut -d':' -f3)

        if curl -s "$url" >/dev/null 2>&1; then
            print_success "$name is healthy"
        else
            print_warning "$name is not responding (may still be starting up)"
            services_healthy=false
        fi
    done

    if [ "$services_healthy" = true ]; then
        print_success "All services are healthy!"
    else
        print_warning "Some services may still be starting up. Check again in a few minutes."
    fi
}

# Function to display setup summary
display_summary() {
    print_header "Setup Complete!"

    echo ""
    echo -e "${CYAN}🎉 Enterprise Platform Setup Summary${NC}"
    echo -e "${CYAN}=====================================${NC}"
    echo ""
    echo -e "${GREEN}✅ System requirements verified${NC}"
    echo -e "${GREEN}✅ Dependencies installed${NC}"
    echo -e "${GREEN}✅ Environment configured${NC}"
    echo -e "${GREEN}✅ Docker infrastructure ready${NC}"
    echo -e "${GREEN}✅ Database migrations completed${NC}"
    echo -e "${GREEN}✅ Monitoring stack deployed${NC}"
    echo ""
    echo -e "${BLUE}🌐 Access Points:${NC}"
    echo -e "${BLUE}  • API Gateway:     http://localhost:3000${NC}"
    echo -e "${BLUE}  • API Docs:        http://localhost:3000/docs${NC}"
    echo -e "${BLUE}  • AI Service:      http://localhost:8000${NC}"
    echo -e "${BLUE}  • Grafana:         http://localhost:3001 (admin/admin123)${NC}"
    echo -e "${BLUE}  • Prometheus:      http://localhost:9090${NC}"
    echo -e "${BLUE}  • Jaeger:          http://localhost:16686${NC}"
    echo ""
    echo -e "${YELLOW}📋 Next Steps:${NC}"
    echo -e "${YELLOW}  1. Review and update .env files with your configuration${NC}"
    echo -e "${YELLOW}  2. Read the documentation: docs/README.md${NC}"
    echo -e "${YELLOW}  3. Start development: npm run dev${NC}"
    echo -e "${YELLOW}  4. Run tests: npm test${NC}"
    echo ""
    echo -e "${PURPLE}📖 Documentation: docs/01-getting-started/README.md${NC}"
    echo -e "${PURPLE}🤝 Contributing: docs/10-contributing/README.md${NC}"
    echo ""
}

# Function to handle cleanup on exit
cleanup() {
    if [ $? -ne 0 ]; then
        print_error "Setup failed. Check $LOG_FILE for details."
        echo ""
        echo -e "${YELLOW}Common solutions:${NC}"
        echo -e "${YELLOW}  • Ensure Docker is running${NC}"
        echo -e "${YELLOW}  • Check system requirements${NC}"
        echo -e "${YELLOW}  • Review error logs in $LOG_FILE${NC}"
        echo -e "${YELLOW}  • Try running individual setup steps manually${NC}"
    fi
}

# Main setup function
main() {
    # Setup trap for cleanup
    trap cleanup EXIT

    # Clear log file
    > "$LOG_FILE"

    print_header "Enterprise Platform Setup Starting..."
    echo ""

    # Run setup steps
    check_requirements
    setup_dependencies
    setup_environment
    setup_docker
    setup_database
    setup_monitoring

    # Wait a bit for services to fully start
    print_info "Waiting for services to fully initialize..."
    sleep 10

    run_health_checks
    display_summary

    print_success "Setup completed successfully! 🎉"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-requirements)
            SKIP_REQUIREMENTS=true
            shift
            ;;
        --skip-docker)
            SKIP_DOCKER=true
            shift
            ;;
        --help|-h)
            echo "Enterprise Platform Setup Script"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --skip-requirements    Skip system requirements check"
            echo "  --skip-docker         Skip Docker setup"
            echo "  --help, -h            Show this help message"
            echo ""
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run main setup
main