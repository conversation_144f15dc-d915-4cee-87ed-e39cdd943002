#!/bin/bash

# Docker commands for Deno application
# Usage: ./scripts/docker-commands.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Development commands
dev() {
    print_header "Starting Development Environment"
    docker-compose --profile dev up -d
    print_status "Development environment started"
    print_status "App: http://localhost:8000"
    print_status "Grafana: http://localhost:3000 (admin/admin)"
    print_status "Prometheus: http://localhost:9090"
}

dev-build() {
    print_header "Building Development Environment"
    docker-compose --profile dev build
    print_status "Development build completed"
}

dev-logs() {
    print_header "Development Logs"
    docker-compose --profile dev logs -f
}

dev-down() {
    print_header "Stopping Development Environment"
    docker-compose --profile dev down
    print_status "Development environment stopped"
}

# Production commands
prod() {
    print_header "Starting Production Environment"
    docker-compose up -d app-prod nginx
    print_status "Production environment started"
    print_status "App: http://localhost:8001"
    print_status "Nginx: http://localhost:80"
}

prod-build() {
    print_header "Building Production Environment"
    docker-compose build app-prod
    print_status "Production build completed"
}

prod-logs() {
    print_header "Production Logs"
    docker-compose logs -f app-prod nginx
}

prod-down() {
    print_header "Stopping Production Environment"
    docker-compose down
    print_status "Production environment stopped"
}

# Database commands
db-start() {
    print_header "Starting Database Services"
    docker-compose up -d postgres redis
    print_status "Database services started"
    print_status "PostgreSQL: localhost:5432"
    print_status "Redis: localhost:6379"
}

db-stop() {
    print_header "Stopping Database Services"
    docker-compose stop postgres redis
    print_status "Database services stopped"
}

# Monitoring commands
monitor() {
    print_header "Starting Monitoring Stack"
    docker-compose --profile dev up -d prometheus grafana
    print_status "Monitoring stack started"
    print_status "Grafana: http://localhost:3000 (admin/admin)"
    print_status "Prometheus: http://localhost:9090"
}

monitor-stop() {
    print_header "Stopping Monitoring Stack"
    docker-compose --profile dev stop prometheus grafana
    print_status "Monitoring stack stopped"
}

# Utility commands
clean() {
    print_header "Cleaning Docker Resources"
    docker-compose down -v --remove-orphans
    docker system prune -f
    print_status "Docker resources cleaned"
}

logs() {
    print_header "All Container Logs"
    docker-compose logs -f
}

status() {
    print_header "Container Status"
    docker-compose ps
}

shell() {
    print_header "Opening Shell in Development Container"
    docker-compose exec app-dev /bin/sh
}

test() {
    print_header "Running Tests"
    docker-compose exec app-dev deno test --allow-all
}

# Health check
health() {
    print_header "Health Check"
    echo "Checking application health..."
    
    # Check if containers are running
    if docker-compose ps | grep -q "Up"; then
        print_status "Containers are running"
    else
        print_error "No containers are running"
        exit 1
    fi
    
    # Check application health endpoint
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_status "Application health check passed"
    else
        print_error "Application health check failed"
        exit 1
    fi
}

# Performance test
perf-test() {
    print_header "Performance Test"
    print_status "Running load test with k6..."

    # Use production URL by default, fallback to dev
    local base_url="${BASE_URL:-http://localhost:8001}"

    # Check if k6 is available
    if ! command -v k6 &> /dev/null; then
        print_warning "k6 not found. Using Docker k6..."
        BASE_URL="$base_url" docker run --rm -i --network host grafana/k6 run - < scripts/load-test.js
    else
        BASE_URL="$base_url" k6 run scripts/load-test.js
    fi
}

# Show help
help() {
    print_header "Docker Commands Help"
    echo "Usage: $0 [command]"
    echo ""
    echo "Development Commands:"
    echo "  dev         - Start development environment"
    echo "  dev-build   - Build development environment"
    echo "  dev-logs    - Show development logs"
    echo "  dev-down    - Stop development environment"
    echo ""
    echo "Production Commands:"
    echo "  prod        - Start production environment"
    echo "  prod-build  - Build production environment"
    echo "  prod-logs   - Show production logs"
    echo "  prod-down   - Stop production environment"
    echo ""
    echo "Database Commands:"
    echo "  db-start    - Start database services"
    echo "  db-stop     - Stop database services"
    echo ""
    echo "Monitoring Commands:"
    echo "  monitor     - Start monitoring stack"
    echo "  monitor-stop- Stop monitoring stack"
    echo ""
    echo "Utility Commands:"
    echo "  clean       - Clean Docker resources"
    echo "  logs        - Show all logs"
    echo "  status      - Show container status"
    echo "  shell       - Open shell in development container"
    echo "  test        - Run tests"
    echo "  health      - Health check"
    echo "  perf-test   - Performance test"
    echo "  help        - Show this help"
}

# Main script logic
case "${1:-help}" in
    dev)
        dev
        ;;
    dev-build)
        dev-build
        ;;
    dev-logs)
        dev-logs
        ;;
    dev-down)
        dev-down
        ;;
    prod)
        prod
        ;;
    prod-build)
        prod-build
        ;;
    prod-logs)
        prod-logs
        ;;
    prod-down)
        prod-down
        ;;
    db-start)
        db-start
        ;;
    db-stop)
        db-stop
        ;;
    monitor)
        monitor
        ;;
    monitor-stop)
        monitor-stop
        ;;
    clean)
        clean
        ;;
    logs)
        logs
        ;;
    status)
        status
        ;;
    shell)
        shell
        ;;
    test)
        test
        ;;
    health)
        health
        ;;
    perf-test)
        perf-test
        ;;
    help|*)
        help
        ;;
esac 