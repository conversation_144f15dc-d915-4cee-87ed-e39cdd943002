# 🤖 **Automation Scripts - DevOps & Maintenance**

> **Comprehensive automation scripts for deployment, monitoring, maintenance, and operational tasks**

## 📋 **Overview**

The `scripts/` directory contains all **automation scripts** for operational tasks including deployment, monitoring, maintenance, and system administration. These scripts follow enterprise standards and provide reliable automation for all environments.

## 🏗️ **Architecture Overview**

```
scripts/
├── setup/              # 🚀 Environment Setup & Installation
├── deployment/         # 🚀 Deployment & Release Management
├── maintenance/        # 🔧 System Maintenance & Cleanup
└── monitoring/         # 📊 Health Checks & Performance Monitoring
```

## 📁 **Scripts Directory & Guides**

| Script Category | Technology Stack | Purpose | README Guide | Status |
|-----------------|------------------|---------|--------------|--------|
| [🚀 **setup/**](setup/README.md) | Bash + Node.js + Docker | Environment setup, installation | [📖 Guide](setup/README.md) | 🔄 |
| [🚀 **deployment/**](deployment/README.md) | Bash + Kubernetes + CI/CD | Deployment automation | [📖 Guide](deployment/README.md) | 🔄 |
| [🔧 **maintenance/**](maintenance/README.md) | Bash + Python + Cron | System maintenance tasks | [📖 Guide](maintenance/README.md) | 🔄 |
| [📊 **monitoring/**](monitoring/README.md) | Bash + Python + Prometheus | Health checks, monitoring | [📖 Guide](monitoring/README.md) | 🔄 |

## 🚀 **Setup Scripts**

**Automated environment setup** and installation:

### **🔧 Key Features**
- ✅ **One-Command Setup** - Complete environment setup
- ✅ **Dependency Management** - Automated dependency installation
- ✅ **Environment Configuration** - Environment-specific configurations
- ✅ **Database Initialization** - Database setup and seeding
- ✅ **Service Registration** - Service discovery setup
- ✅ **Health Verification** - Post-setup health checks

### **📁 Structure**
```
setup/
├── install-dependencies.sh    # Install all dependencies
├── setup-database.sh         # Database initialization
├── configure-environment.sh  # Environment configuration
├── setup-monitoring.sh       # Monitoring stack setup
├── setup-security.sh         # Security configuration
├── verify-installation.sh    # Installation verification
└── quick-start.sh            # One-command complete setup
```

### **🚀 Quick Start Script**
```bash
#!/bin/bash
# quick-start.sh - Complete environment setup

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Main setup function
main() {
    log "🚀 Starting Enterprise Platform Quick Setup..."
    
    # Check prerequisites
    log "🔍 Checking prerequisites..."
    "$SCRIPT_DIR/check-prerequisites.sh"
    
    # Install dependencies
    log "📦 Installing dependencies..."
    "$SCRIPT_DIR/install-dependencies.sh"
    
    # Setup database
    log "🗄️ Setting up database..."
    "$SCRIPT_DIR/setup-database.sh"
    
    # Configure environment
    log "⚙️ Configuring environment..."
    "$SCRIPT_DIR/configure-environment.sh"
    
    # Setup monitoring
    log "📊 Setting up monitoring..."
    "$SCRIPT_DIR/setup-monitoring.sh"
    
    # Setup security
    log "🔒 Configuring security..."
    "$SCRIPT_DIR/setup-security.sh"
    
    # Verify installation
    log "✅ Verifying installation..."
    "$SCRIPT_DIR/verify-installation.sh"
    
    # Success message
    cat << EOF

🎉 ${GREEN}Enterprise Platform Setup Complete!${NC}

📋 ${BLUE}Access Information:${NC}
┌─────────────────────────────────────────────────────┐
│ 🌐 API Gateway:    http://localhost:3000           │
│ 🤖 AI Service:     http://localhost:8000           │
│ 📊 Grafana:        http://localhost:3001           │
│ 📈 Prometheus:     http://localhost:9090           │
│ 📚 API Docs:       http://localhost:3000/docs      │
│ 🔍 Health Check:   http://localhost:3000/health    │
└─────────────────────────────────────────────────────┘

🔑 ${YELLOW}Default Credentials:${NC}
┌─────────────────────────────────────────────────────┐
│ Grafana:  admin / admin123                          │
│ Database: enterprise_user / secure_password_123     │
└─────────────────────────────────────────────────────┘

📖 ${BLUE}Next Steps:${NC}
1. Visit http://localhost:3000/docs for API documentation
2. Explore Grafana dashboards at http://localhost:3001
3. Check the logs: docker-compose logs -f
4. Read the documentation in docs/ folder

🚀 ${GREEN}Happy coding!${NC}

EOF
}

# Run main function
main "$@"
```

## 🚀 **Deployment Scripts**

**Automated deployment** and release management:

### **🔧 Key Features**
- ✅ **Multi-Environment Deployment** - Dev, staging, production
- ✅ **Blue-Green Deployment** - Zero-downtime deployments
- ✅ **Canary Releases** - Gradual rollout strategy
- ✅ **Rollback Procedures** - Automated rollback on failure
- ✅ **Health Checks** - Post-deployment verification
- ✅ **Notification Integration** - Slack/Teams notifications

### **📁 Structure**
```
deployment/
├── deploy-development.sh     # Development deployment
├── deploy-staging.sh         # Staging deployment
├── deploy-production.sh      # Production deployment
├── deploy-canary.sh          # Canary deployment
├── rollback.sh              # Rollback procedures
├── health-check.sh          # Post-deployment health checks
├── notify-deployment.sh     # Deployment notifications
└── utils/                   # Deployment utilities
    ├── docker-utils.sh
    ├── k8s-utils.sh
    └── database-migration.sh
```

### **🚀 Production Deployment Script**
```bash
#!/bin/bash
# deploy-production.sh

set -euo pipefail

ENVIRONMENT="production"
NAMESPACE="enterprise-platform"
DEPLOYMENT_ID=$(date +%Y%m%d-%H%M%S)
SLACK_WEBHOOK=${SLACK_WEBHOOK:-""}

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

notify_slack() {
    if [ -n "$SLACK_WEBHOOK" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$1\"}" \
            "$SLACK_WEBHOOK"
    fi
}

# Pre-deployment checks
pre_deployment_checks() {
    log "🔍 Running pre-deployment checks..."
    
    # Check cluster health
    kubectl cluster-info
    
    # Check resource availability
    kubectl top nodes
    
    # Verify images exist
    for service in api-gateway user-service ai-service; do
        if ! docker manifest inspect "$service:$IMAGE_TAG" > /dev/null 2>&1; then
            error "Image $service:$IMAGE_TAG not found"
        fi
    done
    
    # Run database migrations (dry-run)
    ./scripts/deployment/utils/database-migration.sh --dry-run
    
    log "✅ Pre-deployment checks passed"
}

# Deploy services
deploy_services() {
    log "🚀 Deploying services to $ENVIRONMENT..."
    
    # Update image tags in manifests
    for service in api-gateway user-service ai-service; do
        kubectl set image deployment/$service \
            $service=$service:$IMAGE_TAG \
            -n $NAMESPACE
    done
    
    # Wait for rollout to complete
    for service in api-gateway user-service ai-service; do
        kubectl rollout status deployment/$service -n $NAMESPACE --timeout=600s
    done
    
    log "✅ Services deployed successfully"
}

# Run database migrations
run_migrations() {
    log "🗄️ Running database migrations..."
    
    ./scripts/deployment/utils/database-migration.sh --environment=$ENVIRONMENT
    
    log "✅ Database migrations completed"
}

# Post-deployment verification
post_deployment_verification() {
    log "🔍 Running post-deployment verification..."
    
    # Health checks
    ./scripts/deployment/health-check.sh --environment=$ENVIRONMENT
    
    # Smoke tests
    ./scripts/deployment/smoke-tests.sh --environment=$ENVIRONMENT
    
    # Performance baseline
    ./scripts/monitoring/performance-baseline.sh --environment=$ENVIRONMENT
    
    log "✅ Post-deployment verification completed"
}

# Main deployment function
main() {
    IMAGE_TAG=${1:-latest}
    
    log "🚀 Starting production deployment..."
    log "Image Tag: $IMAGE_TAG"
    log "Deployment ID: $DEPLOYMENT_ID"
    
    notify_slack "🚀 Starting production deployment - ID: $DEPLOYMENT_ID"
    
    # Deployment steps
    pre_deployment_checks
    deploy_services
    run_migrations
    post_deployment_verification
    
    notify_slack "✅ Production deployment completed successfully - ID: $DEPLOYMENT_ID"
    
    log "🎉 Production deployment completed successfully!"
}

# Error handling
trap 'error "Deployment failed at line $LINENO"' ERR

error() {
    log "❌ $1"
    notify_slack "❌ Production deployment failed - ID: $DEPLOYMENT_ID - Error: $1"
    exit 1
}

main "$@"
```

## 🔧 **Maintenance Scripts**

**System maintenance** and cleanup tasks:

### **🔧 Key Features**
- ✅ **Automated Cleanup** - Log rotation, temp file cleanup
- ✅ **Database Maintenance** - Index optimization, vacuum
- ✅ **Security Updates** - Automated security patching
- ✅ **Performance Optimization** - System performance tuning
- ✅ **Backup Management** - Automated backup procedures
- ✅ **Resource Monitoring** - Resource usage optimization

### **📁 Structure**
```
maintenance/
├── cleanup-logs.sh          # Log cleanup and rotation
├── cleanup-docker.sh        # Docker cleanup
├── database-maintenance.sh  # Database optimization
├── security-updates.sh      # Security patch management
├── performance-tuning.sh    # Performance optimization
├── backup-management.sh     # Backup procedures
├── resource-cleanup.sh      # Resource cleanup
└── scheduled/               # Scheduled maintenance tasks
    ├── daily-maintenance.sh
    ├── weekly-maintenance.sh
    └── monthly-maintenance.sh
```

### **🔧 Daily Maintenance Script**
```bash
#!/bin/bash
# scheduled/daily-maintenance.sh

set -euo pipefail

MAINTENANCE_LOG="/var/log/maintenance/daily-$(date +%Y%m%d).log"
RETENTION_DAYS=30

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$MAINTENANCE_LOG"
}

# Log cleanup
cleanup_logs() {
    log "🧹 Starting log cleanup..."
    
    # Application logs
    find /var/log/app -name "*.log" -mtime +7 -delete
    
    # Docker logs
    docker system prune -f --filter "until=24h"
    
    # System logs
    journalctl --vacuum-time=7d
    
    log "✅ Log cleanup completed"
}

# Database maintenance
database_maintenance() {
    log "🗄️ Starting database maintenance..."
    
    # PostgreSQL maintenance
    psql -h localhost -U postgres -d enterprise_platform -c "
        VACUUM ANALYZE;
        REINDEX DATABASE enterprise_platform;
    "
    
    # Redis cleanup
    redis-cli FLUSHDB
    
    log "✅ Database maintenance completed"
}

# Performance monitoring
performance_monitoring() {
    log "📊 Collecting performance metrics..."
    
    # System metrics
    df -h > "/tmp/disk-usage-$(date +%Y%m%d).txt"
    free -h > "/tmp/memory-usage-$(date +%Y%m%d).txt"
    
    # Application metrics
    curl -s http://localhost:3000/metrics > "/tmp/app-metrics-$(date +%Y%m%d).txt"
    
    log "✅ Performance metrics collected"
}

# Security checks
security_checks() {
    log "🔒 Running security checks..."
    
    # Check for security updates
    apt list --upgradable | grep -i security || true
    
    # Scan for vulnerabilities
    if command -v trivy &> /dev/null; then
        trivy fs --security-checks vuln /app
    fi
    
    log "✅ Security checks completed"
}

# Main maintenance function
main() {
    log "🔧 Starting daily maintenance..."
    
    cleanup_logs
    database_maintenance
    performance_monitoring
    security_checks
    
    # Cleanup old maintenance logs
    find /var/log/maintenance -name "daily-*.log" -mtime +$RETENTION_DAYS -delete
    
    log "✅ Daily maintenance completed successfully"
}

main "$@"
```

## 📊 **Monitoring Scripts**

**Health checks** and performance monitoring:

### **🔧 Key Features**
- ✅ **Health Monitoring** - Service health checks
- ✅ **Performance Testing** - Load testing automation
- ✅ **Alert Management** - Automated alerting
- ✅ **Log Analysis** - Log parsing and analysis
- ✅ **Metric Collection** - Custom metric collection
- ✅ **Report Generation** - Automated reporting

### **📁 Structure**
```
monitoring/
├── health-check.sh          # Comprehensive health checks
├── performance-test.sh      # Performance testing
├── log-analysis.sh          # Log analysis and parsing
├── metric-collection.sh     # Custom metric collection
├── alert-manager.sh         # Alert management
├── report-generator.sh      # Automated reporting
└── dashboards/              # Dashboard configurations
    ├── grafana-import.sh
    └── prometheus-config.sh
```

### **📊 Health Check Script**
```bash
#!/bin/bash
# health-check.sh

set -euo pipefail

ENVIRONMENT=${1:-development}
TIMEOUT=30
FAILED_CHECKS=0

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    ((FAILED_CHECKS++))
}

# Check HTTP endpoint
check_http_endpoint() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    log "🔍 Checking $name..."
    
    if response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$url"); then
        if [ "$response" = "$expected_status" ]; then
            log "✅ $name is healthy (HTTP $response)"
        else
            error "$name returned HTTP $response (expected $expected_status)"
        fi
    else
        error "$name is unreachable"
    fi
}

# Check database connection
check_database() {
    log "🔍 Checking database connection..."
    
    if pg_isready -h localhost -p 5432 -U postgres; then
        log "✅ PostgreSQL is healthy"
    else
        error "PostgreSQL connection failed"
    fi
    
    if redis-cli ping | grep -q PONG; then
        log "✅ Redis is healthy"
    else
        error "Redis connection failed"
    fi
}

# Check Kubernetes resources
check_kubernetes() {
    if [ "$ENVIRONMENT" != "development" ]; then
        log "🔍 Checking Kubernetes resources..."
        
        # Check pod status
        if kubectl get pods -n enterprise-platform --field-selector=status.phase!=Running | grep -q "0/"; then
            error "Some pods are not running"
        else
            log "✅ All pods are running"
        fi
        
        # Check service endpoints
        if kubectl get endpoints -n enterprise-platform | grep -q "<none>"; then
            error "Some services have no endpoints"
        else
            log "✅ All services have endpoints"
        fi
    fi
}

# Check disk space
check_disk_space() {
    log "🔍 Checking disk space..."
    
    while read -r line; do
        usage=$(echo "$line" | awk '{print $5}' | sed 's/%//')
        mount=$(echo "$line" | awk '{print $6}')
        
        if [ "$usage" -gt 90 ]; then
            error "Disk usage on $mount is ${usage}% (critical)"
        elif [ "$usage" -gt 80 ]; then
            warn "Disk usage on $mount is ${usage}% (warning)"
        else
            log "✅ Disk usage on $mount is ${usage}%"
        fi
    done < <(df -h | grep -E '^/dev/')
}

# Check memory usage
check_memory() {
    log "🔍 Checking memory usage..."
    
    memory_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    
    if [ "$memory_usage" -gt 90 ]; then
        error "Memory usage is ${memory_usage}% (critical)"
    elif [ "$memory_usage" -gt 80 ]; then
        warn "Memory usage is ${memory_usage}% (warning)"
    else
        log "✅ Memory usage is ${memory_usage}%"
    fi
}

# Main health check function
main() {
    log "🏥 Starting health checks for $ENVIRONMENT environment..."
    
    # Service endpoints
    check_http_endpoint "API Gateway" "http://localhost:3000/health"
    check_http_endpoint "AI Service" "http://localhost:8000/health"
    check_http_endpoint "Grafana" "http://localhost:3001/api/health"
    check_http_endpoint "Prometheus" "http://localhost:9090/-/healthy"
    
    # Infrastructure
    check_database
    check_kubernetes
    check_disk_space
    check_memory
    
    # Summary
    if [ $FAILED_CHECKS -eq 0 ]; then
        log "🎉 All health checks passed!"
        exit 0
    else
        error "❌ $FAILED_CHECKS health check(s) failed!"
        exit 1
    fi
}

main "$@"
```

## 🔧 **Script Configuration**

### **📋 Environment Variables**
```bash
# Common environment variables
export ENVIRONMENT=${ENVIRONMENT:-development}
export LOG_LEVEL=${LOG_LEVEL:-info}
export TIMEOUT=${TIMEOUT:-30}
export RETRY_COUNT=${RETRY_COUNT:-3}
export SLACK_WEBHOOK=${SLACK_WEBHOOK:-""}
export EMAIL_NOTIFICATIONS=${EMAIL_NOTIFICATIONS:-false}
```

### **⚙️ Cron Jobs**
```cron
# /etc/cron.d/enterprise-platform

# Daily maintenance at 2 AM
0 2 * * * root /app/scripts/maintenance/scheduled/daily-maintenance.sh

# Weekly maintenance on Sunday at 1 AM
0 1 * * 0 root /app/scripts/maintenance/scheduled/weekly-maintenance.sh

# Health checks every 5 minutes
*/5 * * * * root /app/scripts/monitoring/health-check.sh

# Performance monitoring every hour
0 * * * * root /app/scripts/monitoring/performance-test.sh

# Log cleanup every 6 hours
0 */6 * * * root /app/scripts/maintenance/cleanup-logs.sh
```

## 🔗 **Related Documentation**

- [🏗️ Project Structure](../docs/PROJECT_STRUCTURE.md)
- [🏗️ Infrastructure](../infrastructure/README.md)
- [🔧 Development Tools](../tools/README.md)
- [📊 Monitoring](../monitoring/README.md)
- [☁️ DevOps Guide](../docs/07-knowledge-base/05-devops-cloud/README.md)

## 🤝 **Contributing**

1. **Script Development** - Create new automation scripts
2. **Error Handling** - Implement robust error handling
3. **Logging** - Add comprehensive logging
4. **Testing** - Test scripts in all environments
5. **Documentation** - Document script usage and parameters

---

> **Next Steps**: Explore individual script category READMEs for detailed usage guides and start automating your operational tasks.
