import { Application } from "oak";

const app = new Application();
const port = 8000;

// Basic middleware for logging
app.use(async (ctx, next) => {
  const start = Date.now();
  await next();
  const ms = Date.now() - start;
  console.log(`${ctx.request.method} ${ctx.request.url.pathname} - ${ms}ms`);
});

// Health check route
app.use((ctx) => {
  if (ctx.request.url.pathname === "/health") {
    ctx.response.body = {
      status: "ok",
      timestamp: new Date().toISOString(),
      service: "Smart Task Management System",
    };
  }
});

console.log(`🚀 Server running on http://localhost:${port}`);
await app.listen({ port });
