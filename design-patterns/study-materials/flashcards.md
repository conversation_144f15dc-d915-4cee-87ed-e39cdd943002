# Flashcards - Design Patterns

> Flashcard học nhanh để ôn luyện các khái niệm quan trọng về Design Patterns

## 📋 Cách sử dụng

1. **Đọc câu hỏi** (Mặt trước)
2. **<PERSON><PERSON> nghĩ về câu trả lời**
3. **Kiểm tra đáp án** (Mặt sau)
4. **Đ<PERSON>h dấu** những câu cần ôn lại

---

## 🏗️ Nền tảng OOP

### Card 1: OOP Cơ bản
**Mặt trước:** OOP là gì và mối quan hệ với lớp và đối tượng?

**Mặt sau:** **Lập trình hướng đối tượng (OOP)** tổ chức dữ liệu và hành vi vào các **đối tượng**, đư<PERSON><PERSON> tạo ra từ các **lớp** (bản thiết kế) do lập trình viên định nghĩa. Một **đối tượng** là một thể hiện của một **lớp**, chia sẻ các thuộc tính (trường) và hành vi (phương thức) chung.

### Card 2: Hệ thống phân cấp
**Mặt trước:** Hệ thống phân cấp lớp và Kế thừa là gì?

**Mặt sau:** **Hệ thống phân cấp lớp** tổ chức các lớp theo mối quan hệ cha-con. **Lớp con** kế thừa các thuộc tính và hành vi từ **lớp cha**. Lớp con có thể **ghi đè (override)** các phương thức kế thừa để thay thế hoặc bổ sung chức năng.

### Card 3: Bốn trụ cột OOP
**Mặt trước:** Kể tên 4 trụ cột của OOP.

**Mặt sau:** Bốn khái niệm nền tảng của OOP là **Abstraction (Trừu tượng hóa)**, **Encapsulation (Đóng gói)**, **Inheritance (Kế thừa)** và **Polymorphism (Đa hình)**.

### Card 4: Abstraction
**Mặt trước:** Abstraction là gì?

**Mặt sau:** **Abstraction** là mô hình hóa các đối tượng thực tế bằng cách chỉ tập trung vào các thuộc tính và hành vi **liên quan** đến một ngữ cảnh cụ thể, bỏ qua các chi tiết không cần thiết.

### Card 5: Encapsulation
**Mặt trước:** Encapsulation là gì?

**Mặt sau:** **Encapsulation** là việc che giấu các hoạt động bên trong của một đối tượng và chỉ để lộ một **giao diện đơn giản** để tương tác, đảm bảo tương tác trực tiếp.

### Card 6: Polymorphism
**Mặt trước:** Polymorphism là gì?

**Mặt sau:** **Polymorphism** cho phép chương trình xác định lớp cụ thể của một đối tượng và gọi các phương thức của nó mà không cần biết chính xác kiểu của nó trước, cho phép hành vi đối tượng linh hoạt.

### Card 7: Quan hệ đối tượng
**Mặt trước:** Association, Dependency, Composition, Aggregation trong UML là gì?

**Mặt sau:**
- **Association:** Một đối tượng sử dụng hoặc tương tác với đối tượng khác.
- **Dependency:** Dạng yếu hơn của association, không có liên kết vĩnh viễn, thường khi một đối tượng sử dụng đối tượng khác làm tham số.
- **Composition:** Mối quan hệ "toàn thể-bộ phận" mạnh mẽ, thành phần không thể tồn tại độc lập khỏi đối tượng chứa.
- **Aggregation:** Dạng lỏng lẻo hơn của composition, đối tượng giữ tham chiếu nhưng không kiểm soát vòng đời, thành phần có thể tồn tại độc lập.

---

## 🎯 Nguyên tắc thiết kế

### Card 8: Design Pattern định nghĩa
**Mặt trước:** Design Pattern là gì và khác gì Algorithm?

**Mặt sau:** **Design Patterns** là các giải pháp điển hình, có thể tùy chỉnh cho các vấn đề thiết kế phần mềm lặp đi lặp lại. Khác với **Algorithm** cung cấp hành động chính xác, pattern là mô tả cấp cao hơn của giải pháp.

### Card 9: Gang of Four
**Mặt trước:** Ai đã phổ biến Design Patterns trong lập trình?

**Mặt sau:** Khái niệm này ban đầu từ **Christopher Alexander** (kiến trúc), được phổ biến trong lập trình bởi "Gang of Four" (**Erich Gamma, John Vlissides, Ralph Johnson, và Richard Helm**) qua cuốn sách "Design Patterns: Elements of Reusable Object-Oriented Software".

### Card 10: Lợi ích Design Patterns
**Mặt trước:** Lợi ích của việc học Design Patterns?

**Mặt sau:**
- **Toolkit of Solutions:** Cung cấp các giải pháp đã được kiểm nghiệm cho các vấn đề thiết kế.
- **Common Language:** Thiết lập một vốn từ vựng chung giữa các thành viên trong nhóm, cải thiện giao tiếp.

### Card 11: Đặc điểm thiết kế tốt
**Mặt trước:** Code Reuse và Extensibility là gì và tại sao quan trọng?

**Mặt sau:**
- **Code Reuse (Tái sử dụng mã):** Chiến lược quan trọng để giảm chi phí phát triển và tăng tốc thời gian đưa sản phẩm ra thị trường.
- **Extensibility (Khả năng mở rộng):** Quan trọng vì thay đổi là điều không thể tránh khỏi trong lập trình; giúp ứng dụng thích nghi với yêu cầu và xu hướng mới.

### Card 12: Encapsulate What Varies
**Mặt trước:** Nguyên tắc "Encapsulate What Varies" là gì?

**Mặt sau:** Nguyên tắc này khuyên bạn nên xác định và **tách biệt các phần của ứng dụng có thể thay đổi** khỏi các phần cố định để giảm thiểu tác động của những thay đổi đó.

### Card 13: Program to Interface
**Mặt trước:** Nguyên tắc "Program to an Interface, not an Implementation" là gì?

**Mặt sau:** Nhấn mạnh lập trình dựa trên một **giao diện (abstraction)** thay vì một **triển khai cụ thể**, nhằm đạt được sự linh hoạt và khả năng mở rộng trong thiết kế.

### Card 14: Composition vs Inheritance
**Mặt trước:** Tại sao nên "Favor Composition Over Inheritance"?

**Mặt sau:** Kế thừa có thể dẫn đến **hạn chế giao diện, phá vỡ đóng gói, ghép nối chặt chẽ, và hệ thống phân cấp phức tạp**. **Composition** (tổng hợp) ưu tiên mối quan hệ "có một" ("has a") thay vì "là một" ("is a"), cho phép linh hoạt hơn và tránh các nhược điểm của kế thừa.

---

## 🔧 SOLID Principles

### Card 15: SRP
**Mặt trước:** Nguyên tắc SRP là gì?

**Mặt sau:** Một lớp chỉ nên có **một lý do để thay đổi**, tập trung vào một phần chức năng duy nhất. Mục tiêu chính là **giảm độ phức tạp** và tăng khả năng bảo trì.

### Card 16: OCP
**Mặt trước:** Nguyên tắc OCP là gì?

**Mặt sau:** Các lớp nên **mở để mở rộng nhưng đóng để sửa đổi**. Điều này ngăn chặn mã hiện có bị hỏng khi thêm tính năng mới. Bạn nên tạo lớp con để mở rộng thay vì sửa trực tiếp lớp gốc.

### Card 17: LSP
**Mặt trước:** Nguyên tắc LSP là gì?

**Mặt sau:** Các đối tượng của một lớp con nên có thể **thay thế cho các đối tượng của lớp cha** mà không làm thay đổi tính đúng đắn của chương trình. Nguyên tắc này có các hướng dẫn về **tương thích kiểu tham số/kiểu trả về**, xử lý ngoại lệ, và bảo toàn tiền/hậu điều kiện, bất biến.

### Card 18: ISP
**Mặt trước:** Nguyên tắc ISP là gì?

**Mặt sau:** Khách hàng không nên bị buộc phải phụ thuộc vào các phương thức mà họ không sử dụng. Nên tạo các **giao diện hẹp** và cụ thể để các lớp khách hàng chỉ triển khai các hành vi thực sự cần.

### Card 19: DIP
**Mặt trước:** Nguyên tắc DIP là gì?

**Mặt sau:** Các lớp cấp cao không nên phụ thuộc vào các lớp cấp thấp. **Cả hai nên phụ thuộc vào các abstraction (giao diện)**. Điều này đảo ngược hướng phụ thuộc truyền thống, làm cho thiết kế linh hoạt và dễ bảo trì hơn.

---

## 🏭 Creational Patterns

### Card 20: Factory Method
**Mặt trước:** Factory Method là gì?

**Mặt sau:** Một **creational design pattern** cung cấp một giao diện để tạo đối tượng trong một lớp cha, cho phép các lớp con quyết định loại đối tượng cụ thể sẽ được tạo.

### Card 21: Factory Method - Vấn đề
**Mặt trước:** Vấn đề Factory Method giải quyết?

**Mặt sau:** Giảm **ghép nối chặt chẽ** giữa mã và các lớp sản phẩm cụ thể, làm cho việc thêm các loại đối tượng mới trở nên dễ dàng hơn.

### Card 22: Abstract Factory
**Mặt trước:** Abstract Factory là gì?

**Mặt sau:** Một **creational design pattern** cho phép tạo ra các **họ đối tượng liên quan** mà không cần chỉ rõ các lớp cụ thể của chúng.

### Card 23: Abstract Factory - Lợi ích
**Mặt trước:** Lợi ích của Abstract Factory?

**Mặt sau:** Đảm bảo **khả năng tương thích sản phẩm**, giảm ghép nối chặt chẽ giữa sản phẩm và mã khách hàng, và hỗ trợ tuân thủ SRP, OCP.

### Card 24: Builder
**Mặt trước:** Builder là gì và vấn đề giải quyết?

**Mặt sau:** Một **creational design pattern** cho phép **xây dựng từng bước các đối tượng phức tạp**. Nó giải quyết các vấn đề liên quan đến hàm tạo phức tạp với nhiều tham số tùy chọn hoặc sự cần thiết của nhiều lớp con cho mỗi cấu hình.

### Card 25: Builder - Director
**Mặt trước:** Vai trò của Director trong Builder?

**Mặt sau:** Lớp **Director** tổ chức chuỗi các bước xây dựng, đơn giản hóa tương tác của khách hàng với builder.

### Card 26: Prototype
**Mặt trước:** Prototype là gì và vấn đề giải quyết?

**Mặt sau:** Một **creational design pattern** cho phép **sao chép các đối tượng hiện có** mà không cần mã phụ thuộc vào lớp của chúng. Nó giải quyết khó khăn khi sao chép đối tượng "từ bên ngoài" do các trường riêng tư và sự phụ thuộc vào các lớp cụ thể.

### Card 27: Prototype - Giải pháp
**Mặt trước:** Giải pháp của Prototype?

**Mặt sau:** Ủy quyền quá trình sao chép cho chính các đối tượng thông qua một **giao diện chung** (thường có phương thức `clone`).

### Card 28: Singleton
**Mặt trước:** Singleton là gì và vấn đề giải quyết?

**Mặt sau:** Một **creational design pattern** đảm bảo một lớp chỉ có **một thể hiện duy nhất** và cung cấp một **điểm truy cập toàn cầu** đến thể hiện đó. Thường dùng để kiểm soát quyền truy cập vào tài nguyên dùng chung (như cơ sở dữ liệu). _Lưu ý: Vi phạm Nguyên tắc Đơn trách nhiệm_.

### Card 29: Singleton - Triển khai
**Mặt trước:** Cách triển khai Singleton?

**Mặt sau:**
- Hàm tạo riêng tư.
- Phương thức tạo tĩnh (`getInstance()`) để khởi tạo và trả về thể hiện duy nhất (lazy initialization).

---

## 🔗 Structural Patterns

### Card 30: Adapter
**Mặt trước:** Adapter là gì và vấn đề giải quyết?

**Mặt sau:** Một **structural design pattern** cho phép **hợp tác giữa các đối tượng có giao diện không tương thích**. Nó giải quyết vấn đề sử dụng thư viện bên thứ ba với giao diện không tương thích mà không cần sửa đổi mã hiện có.

### Card 31: Adapter - Giải pháp
**Mặt trước:** Giải pháp của Adapter?

**Mặt sau:** Tạo một **adapter** đóng vai trò trung gian, chuyển đổi dữ liệu giữa các định dạng/giao diện không tương thích.

### Card 32: Bridge
**Mặt trước:** Bridge là gì và vấn đề giải quyết?

**Mặt sau:** Một **structural design pattern** tách một lớp lớn thành hai hệ thống phân cấp riêng biệt: **abstraction (trừu tượng)** và **implementation (triển khai)**, cho phép phát triển độc lập. Nó giải quyết "bùng nổ lớp" khi mở rộng hệ thống phân cấp theo nhiều chiều.

### Card 33: Bridge - Giải pháp
**Mặt trước:** Giải pháp của Bridge?

**Mặt sau:** Chuyển từ kế thừa sang **composition**. Một lớp trừu tượng tham chiếu đến một đối tượng triển khai, cho phép cả hai phát triển độc lập.

---

## 💡 Mẹo học tập

### Cách ôn tập hiệu quả:
1. **Lặp lại thường xuyên** - Ôn lại các card mỗi ngày
2. **Tập trung vào những card khó** - Đánh dấu và ôn nhiều lần
3. **Áp dụng vào thực tế** - Tìm ví dụ trong code thực tế
4. **Giải thích cho người khác** - Cách tốt nhất để kiểm tra hiểu biết

### Thứ tự học tập đề xuất:
1. **Nền tảng OOP** (Cards 1-7)
2. **Nguyên tắc thiết kế** (Cards 8-14)  
3. **SOLID Principles** (Cards 15-19)
4. **Creational Patterns** (Cards 20-29)
5. **Structural Patterns** (Cards 30-33)
6. **Behavioral Patterns** (sẽ bổ sung)

---

**Tiếp theo:** [Quick Reference](quick-reference.md) - Tham khảo nhanh các patterns
