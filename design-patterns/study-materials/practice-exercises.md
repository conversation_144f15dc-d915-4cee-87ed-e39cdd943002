# Practice Exercises - Design Patterns

> <PERSON><PERSON><PERSON> tập thực hành để củng cố kiến thức về Design Patterns

## 📋 Hướng dẫn sử dụng

1. **Đọc đề bài** và xác định pattern phù hợp
2. **Thiết kế solution** trước khi code
3. **Triển khai** theo pattern đã chọn
4. **Kiểm tra** với test cases
5. **So sánh** với solution gợi ý

---

## 🏗️ Creational Patterns

### Exercise 1: Document Factory (Factory Method)
**Đề bài:** Tạo hệ thống tạo documents cho ứng dụng office

**Yêu cầu:**
- Hỗ trợ tạo Word, PDF, Excel documents
- Mỗi loại document có cách mở và lưu khác nhau
- Dễ dàng thêm loại document mới (PowerPoint, etc.)

**Interface cần thiết:**
```java
interface Document {
    void open();
    void save();
    void close();
}
```

**Test cases:**
```java
// Tạo Word document
DocumentCreator wordCreator = new WordDocumentCreator();
Document doc = wordCreator.createDocument();
doc.open(); // Should print: "Opening Word document"

// Tạo PDF document  
DocumentCreator pdfCreator = new PDFDocumentCreator();
Document pdf = pdfCreator.createDocument();
pdf.save(); // Should print: "Saving PDF document"
```

**Gợi ý:** Sử dụng Factory Method pattern với abstract DocumentCreator

---

### Exercise 2: Cross-Platform UI (Abstract Factory)
**Đề bài:** Tạo UI components cho nhiều platform (Windows, Mac, Linux)

**Yêu cầu:**
- Mỗi platform có Button, Checkbox, TextBox riêng
- Đảm bảo tất cả components trong cùng platform tương thích
- Client code không biết platform cụ thể

**Test cases:**
```java
UIFactory factory = new WindowsUIFactory();
Button btn = factory.createButton();
Checkbox cb = factory.createCheckbox();
btn.render(); // Windows-style button
cb.render(); // Windows-style checkbox
```

**Gợi ý:** Sử dụng Abstract Factory với UIFactory interface

---

### Exercise 3: Computer Builder (Builder)
**Đề bài:** Tạo hệ thống build máy tính với nhiều cấu hình

**Yêu cầu:**
- Computer có: CPU, RAM, Storage, GPU (optional)
- Hỗ trợ build Gaming PC, Office PC, Server
- Có thể build từng bước hoặc dùng preset

**Test cases:**
```java
Computer gaming = new ComputerBuilder()
    .setCPU("Intel i9")
    .setRAM("32GB")
    .setStorage("1TB SSD")
    .setGPU("RTX 4080")
    .build();

Computer office = ComputerDirector.buildOfficePC();
```

**Gợi ý:** Sử dụng Builder pattern với optional Director

---

## 🔗 Structural Patterns

### Exercise 4: Media Player Adapter (Adapter)
**Đề bài:** Tích hợp legacy audio player với media player mới

**Yêu cầu:**
- MediaPlayer hiện tại chỉ play MP3
- Cần hỗ trợ MP4, AVI thông qua AdvancedMediaPlayer
- Không thay đổi MediaPlayer interface

**Existing code:**
```java
interface MediaPlayer {
    void play(String audioType, String fileName);
}

interface AdvancedMediaPlayer {
    void playVlc(String fileName);
    void playMp4(String fileName);
}
```

**Test cases:**
```java
MediaPlayer player = new AudioPlayer();
player.play("mp3", "song.mp3");   // Direct play
player.play("mp4", "video.mp4");  // Through adapter
player.play("vlc", "movie.vlc");  // Through adapter
```

**Gợi ý:** Tạo MediaAdapter implement MediaPlayer

---

### Exercise 5: Coffee Shop Decorator (Decorator)
**Đề bài:** Tạo hệ thống order coffee với các add-ons

**Yêu cầu:**
- Base coffee: Espresso, HouseBlend
- Add-ons: Milk, Sugar, Whip, Soy
- Tính tổng giá và mô tả đầy đủ

**Test cases:**
```java
Beverage beverage = new Espresso();
beverage = new Milk(beverage);
beverage = new Sugar(beverage);
System.out.println(beverage.getDescription()); // "Espresso, Milk, Sugar"
System.out.println(beverage.cost()); // 2.5 + 0.3 + 0.2 = 3.0
```

**Gợi ý:** Beverage abstract class với CondimentDecorator

---

### Exercise 6: Smart Home Facade (Facade)
**Đề bài:** Đơn giản hóa điều khiển smart home system

**Yêu cầu:**
- Subsystems: Lights, AirConditioner, TV, SecuritySystem
- Modes: "Movie Night", "Sleep", "Away"
- Một lệnh điều khiển nhiều thiết bị

**Test cases:**
```java
SmartHomeFacade home = new SmartHomeFacade();
home.movieNight(); // Dim lights, turn on TV, set AC to 22°C
home.sleepMode();  // Turn off lights, TV, set AC to 25°C
home.awayMode();   // Turn off all, activate security
```

**Gợi ý:** SmartHomeFacade tương tác với tất cả subsystems

---

## 🎭 Behavioral Patterns

### Exercise 7: Support Ticket Chain (Chain of Responsibility)
**Đề bài:** Hệ thống xử lý support tickets theo cấp độ

**Yêu cầu:**
- Levels: Level1Support, Level2Support, Manager
- Mỗi level xử lý tickets trong khả năng
- Chuyển tiếp nếu không xử lý được

**Test cases:**
```java
SupportHandler chain = new Level1Support();
chain.setNext(new Level2Support())
     .setNext(new Manager());

chain.handleRequest(new Ticket("Password reset", Priority.LOW));    // Level1
chain.handleRequest(new Ticket("Server down", Priority.CRITICAL));  // Manager
```

**Gợi ý:** Abstract SupportHandler với handleRequest method

---

### Exercise 8: Text Editor Commands (Command)
**Đề bài:** Tạo text editor với undo/redo functionality

**Yêu cầu:**
- Commands: Copy, Paste, Cut, Type
- Hỗ trợ undo/redo
- Macro recording (optional)

**Test cases:**
```java
TextEditor editor = new TextEditor();
Command copy = new CopyCommand(editor);
Command paste = new PasteCommand(editor);

editor.executeCommand(copy);
editor.executeCommand(paste);
editor.undo(); // Undo paste
editor.redo(); // Redo paste
```

**Gợi ý:** Command interface với execute() và undo() methods

---

### Exercise 9: Stock Price Observer (Observer)
**Đề bài:** Hệ thống thông báo giá cổ phiếu

**Yêu cầu:**
- Stock có giá thay đổi
- Investors, Displays subscribe để nhận thông báo
- Có thể subscribe/unsubscribe động

**Test cases:**
```java
Stock appleStock = new Stock("AAPL", 150.0);
Investor investor1 = new Investor("John");
Display display1 = new StockDisplay();

appleStock.addObserver(investor1);
appleStock.addObserver(display1);
appleStock.setPrice(155.0); // Notify all observers
```

**Gợi ý:** Observable/Observer pattern với update() method

---

### Exercise 10: Payment Strategy (Strategy)
**Đề bài:** Hệ thống thanh toán với nhiều phương thức

**Yêu cầu:**
- Payment methods: CreditCard, PayPal, Bitcoin
- Mỗi method có cách xử lý khác nhau
- Có thể thay đổi method runtime

**Test cases:**
```java
ShoppingCart cart = new ShoppingCart();
cart.addItem("Laptop", 1000);

cart.setPaymentStrategy(new CreditCardPayment("1234-5678"));
cart.checkout(); // Pay with credit card

cart.setPaymentStrategy(new PayPalPayment("<EMAIL>"));
cart.checkout(); // Pay with PayPal
```

**Gợi ý:** PaymentStrategy interface với pay() method

---

## 🎯 Comprehensive Exercises

### Exercise 11: Game Development Framework
**Đề bài:** Tạo framework cho game development

**Patterns cần dùng:**
- Factory Method: Tạo different game objects
- Observer: Game events và UI updates  
- State: Game states (Menu, Playing, Paused)
- Strategy: AI behaviors

**Yêu cầu:**
- Game có nhiều levels với objects khác nhau
- UI updates khi game state thay đổi
- AI enemies có behaviors khác nhau

---

### Exercise 12: E-commerce Platform
**Đề bài:** Thiết kế hệ thống e-commerce

**Patterns cần dùng:**
- Abstract Factory: Product catalogs cho different categories
- Decorator: Product options và add-ons
- Command: Order processing với undo
- Observer: Inventory updates

**Yêu cầu:**
- Products có nhiều variants và options
- Order có thể cancel/modify
- Inventory tracking real-time

---

## 💡 Tips for Success

### Cách tiếp cận bài tập:

1. **Phân tích yêu cầu:**
   - Xác định objects và relationships
   - Tìm điểm thay đổi (varying parts)
   - Xác định pattern phù hợp

2. **Thiết kế trước khi code:**
   - Vẽ class diagram
   - Định nghĩa interfaces
   - Xác định responsibilities

3. **Implement từng bước:**
   - Bắt đầu với core functionality
   - Thêm patterns dần dần
   - Test từng component

4. **Refactor và improve:**
   - Review code quality
   - Kiểm tra SOLID principles
   - Optimize performance

### Common mistakes to avoid:

- **Over-engineering:** Không dùng pattern khi không cần
- **Wrong pattern:** Chọn pattern không phù hợp với vấn đề
- **Tight coupling:** Quên áp dụng nguyên tắc loose coupling
- **No testing:** Không test thoroughly

---

## 📚 Solutions

Solutions cho tất cả exercises có thể tìm thấy trong thư mục [examples](../examples/) với code đầy đủ cho Java, Python, và TypeScript.

**Tiếp theo:** [Examples](../examples/) - Xem code solutions chi tiết
