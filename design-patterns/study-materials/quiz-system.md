# Interactive Quiz System - Design Patterns

> <PERSON><PERSON> thống quiz tương tác để kiểm tra kiến thức về Design Patterns

## 📋 Cách sử dụng

1. **Chọn level** phù hợp với trình độ
2. **<PERSON><PERSON><PERSON> câu hỏi** cẩn thận
3. **Chọn đáp án** bằng cách ghi số tương ứng
4. **Xem giải thích** sau mỗi câu
5. **Kiểm tra điểm** cuối quiz

---

## 🎯 Level 1: Beginner (Cơ bản)

### Câu 1: OOP Fundamentals
**Câu hỏi:** Bốn trụ cột của OOP là gì?

A) Abstraction, Encapsulation, Inheritance, Polymorphism  
B) Classes, Objects, Methods, Properties  
C) Public, Private, Protected, Static  
D) Create, Read, Update, Delete  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: A**

**G<PERSON><PERSON><PERSON> thích:** <PERSON><PERSON>n trụ cột của OOP là:
- **Abstraction (Tr<PERSON><PERSON> tượng):** Ẩn chi tiết phức tạp, chỉ hiển thị những gì cần thiết
- **Encapsulation (Đóng gói):** Gói gọn dữ liệu và methods vào một đơn vị
- **Inheritance (Kế thừa):** Cho phép class con kế thừa từ class cha
- **Polymorphism (Đa hình):** Cùng một interface có thể có nhiều implementations khác nhau

</details>

### Câu 2: Design Patterns Classification
**Câu hỏi:** Design Patterns được chia thành mấy nhóm chính?

A) 2 nhóm: Structural và Behavioral  
B) 3 nhóm: Creational, Structural, Behavioral  
C) 4 nhóm: Creational, Structural, Behavioral, Architectural  
D) 5 nhóm: Creational, Structural, Behavioral, Concurrency, Architectural  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: B**

**Giải thích:** Gang of Four định nghĩa 3 nhóm chính:
- **Creational Patterns:** Tập trung vào cách tạo objects (Factory, Singleton, Builder...)
- **Structural Patterns:** Tập trung vào cách tổ chức objects (Adapter, Decorator, Facade...)
- **Behavioral Patterns:** Tập trung vào giao tiếp giữa objects (Observer, Strategy, Command...)

</details>

### Câu 3: Singleton Pattern
**Câu hỏi:** Singleton Pattern đảm bảo điều gì?

A) Một class có thể có nhiều instances  
B) Một class chỉ có một instance duy nhất  
C) Một class không thể có instance nào  
D) Một class có thể clone instances  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: B**

**Giải thích:** Singleton Pattern đảm bảo:
- Chỉ có **một instance duy nhất** của class trong toàn bộ application
- Cung cấp **global access point** đến instance đó
- Thường dùng cho Database connections, Loggers, Configuration managers

**Ví dụ use case:** Database connection pool - chỉ cần một pool duy nhất để quản lý connections

</details>

---

## 🎯 Level 2: Intermediate (Trung cấp)

### Câu 4: Factory Method vs Abstract Factory
**Câu hỏi:** Sự khác biệt chính giữa Factory Method và Abstract Factory là gì?

A) Factory Method tạo một product, Abstract Factory tạo families of products  
B) Factory Method phức tạp hơn Abstract Factory  
C) Abstract Factory chỉ dùng cho Singleton objects  
D) Không có sự khác biệt, chỉ là tên gọi khác nhau  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: A**

**Giải thích:**
- **Factory Method:** Tạo **một loại product**, subclasses quyết định concrete type
  ```java
  abstract class LogisticsFactory {
      abstract Transport createTransport(); // Một product
  }
  ```

- **Abstract Factory:** Tạo **families of related products**
  ```java
  interface UIFactory {
      Button createButton();     // Product family:
      Checkbox createCheckbox(); // UI components
      TextBox createTextBox();   // cho cùng platform
  }
  ```

**Khi nào dùng:**
- Factory Method: Khi cần tạo variations của cùng một product
- Abstract Factory: Khi cần đảm bảo products tương thích với nhau

</details>

### Câu 5: Observer Pattern Problem
**Câu hỏi:** Observer Pattern giải quyết vấn đề gì?

A) Tạo objects phức tạp từng bước  
B) Thông báo cho nhiều objects khi state thay đổi  
C) Ẩn complexity của subsystem  
D) Thêm behavior mà không thay đổi structure  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: B**

**Giải thích:** Observer Pattern giải quyết:
- **One-to-many dependency:** Một subject có nhiều observers
- **Automatic notification:** Khi subject state thay đổi, tất cả observers được thông báo
- **Loose coupling:** Subject không cần biết concrete observer classes

**Ví dụ thực tế:**
```java
// Stock price changes notify all investors
StockPrice apple = new StockPrice("AAPL", 150.0);
apple.addObserver(investor1);
apple.addObserver(investor2);
apple.setPrice(155.0); // Both investors notified automatically
```

**Use cases:** Model-View architectures, Event systems, Stock monitoring

</details>

### Câu 6: Strategy Pattern Benefits
**Câu hỏi:** Lợi ích chính của Strategy Pattern là gì?

A) Giảm số lượng classes cần thiết  
B) Cho phép thay đổi algorithms runtime  
C) Tăng performance của application  
D) Đơn giản hóa inheritance hierarchy  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: B**

**Giải thích:** Strategy Pattern cho phép:
- **Runtime algorithm switching:** Thay đổi thuật toán trong lúc chạy
- **Eliminate conditionals:** Không cần if-else cho algorithm selection
- **Open/Closed Principle:** Thêm algorithms mới mà không sửa existing code

**Ví dụ:**
```java
PaymentProcessor processor = new PaymentProcessor();
processor.setStrategy(new CreditCardPayment()); // Runtime switch
processor.processPayment(100.0);

processor.setStrategy(new PayPalPayment());     // Switch again
processor.processPayment(200.0);
```

**Khi nào dùng:** Khi có nhiều cách thực hiện cùng một task

</details>

---

## 🎯 Level 3: Advanced (Nâng cao)

### Câu 7: SOLID Principles Violation
**Câu hỏi:** Đoạn code sau vi phạm nguyên tắc SOLID nào?

```java
public class EmailService {
    public void sendEmail(String to, String message) {
        // Validate email
        if (!to.contains("@")) {
            throw new IllegalArgumentException("Invalid email");
        }
        
        // Log the action
        System.out.println("Sending email to: " + to);
        
        // Send via SMTP
        SmtpClient client = new SmtpClient();
        client.send(to, message);
        
        // Save to database
        Database.save("email_log", to + ": " + message);
    }
}
```

A) Single Responsibility Principle  
B) Open/Closed Principle  
C) Liskov Substitution Principle  
D) Interface Segregation Principle  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: A**

**Giải thích:** Vi phạm **Single Responsibility Principle** vì class có quá nhiều responsibilities:

1. **Email validation** - should be in EmailValidator
2. **Logging** - should be in Logger
3. **Email sending** - core responsibility (OK)
4. **Database operations** - should be in EmailRepository

**Refactored version:**
```java
public class EmailService {
    private EmailValidator validator;
    private Logger logger;
    private EmailSender sender;
    private EmailRepository repository;
    
    public void sendEmail(String to, String message) {
        validator.validate(to);           // Single responsibility
        logger.log("Sending email to: " + to);
        sender.send(to, message);
        repository.saveLog(to, message);
    }
}
```

**SRP Benefits:** Easier testing, maintenance, và modification

</details>

### Câu 8: Pattern Combination
**Câu hỏi:** Trong một e-commerce system, bạn cần:
- Tạo different types of products (Electronics, Clothing, Books)
- Apply different discounts (Percentage, Fixed amount, Buy-one-get-one)
- Notify customers về price changes

Patterns nào nên combine?

A) Factory + Strategy + Observer  
B) Builder + Decorator + Command  
C) Singleton + Adapter + State  
D) Prototype + Bridge + Mediator  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: A**

**Giải thích:**

**Factory Pattern:** Tạo different product types
```java
abstract class ProductFactory {
    abstract Product createProduct(String type);
}
class ElectronicsFactory extends ProductFactory { ... }
class ClothingFactory extends ProductFactory { ... }
```

**Strategy Pattern:** Apply different discount strategies
```java
interface DiscountStrategy {
    double applyDiscount(double price);
}
class PercentageDiscount implements DiscountStrategy { ... }
class FixedAmountDiscount implements DiscountStrategy { ... }
```

**Observer Pattern:** Notify customers về price changes
```java
class Product implements Subject {
    public void setPrice(double price) {
        this.price = price;
        notifyObservers(); // Notify all subscribed customers
    }
}
```

**Pattern Synergy:** Các patterns work together để create flexible, maintainable system

</details>

### Câu 9: Anti-Pattern Recognition
**Câu hỏi:** Đoạn code sau thể hiện anti-pattern nào?

```java
public class OrderProcessor {
    public void processOrder(Order order) {
        if (order.getType().equals("ONLINE")) {
            if (order.getPaymentMethod().equals("CREDIT_CARD")) {
                if (order.getAmount() > 1000) {
                    // Apply premium processing
                    processPremiumCreditCardOrder(order);
                } else {
                    // Apply standard processing
                    processStandardCreditCardOrder(order);
                }
            } else if (order.getPaymentMethod().equals("PAYPAL")) {
                // PayPal processing logic
                processPayPalOrder(order);
            }
        } else if (order.getType().equals("STORE")) {
            // Store processing logic
            processStoreOrder(order);
        }
        // More nested conditions...
    }
}
```

A) God Object  
B) Spaghetti Code  
C) Arrow Anti-Pattern (Deep Nesting)  
D) Magic Numbers  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: C**

**Giải thích:** **Arrow Anti-Pattern** - deep nesting tạo ra "arrow" shape trong code.

**Problems:**
- Hard to read và understand
- Difficult to test individual conditions
- High cyclomatic complexity
- Violates Single Responsibility Principle

**Solutions using Design Patterns:**

**1. Strategy Pattern:**
```java
public class OrderProcessor {
    private Map<String, OrderProcessingStrategy> strategies;
    
    public void processOrder(Order order) {
        OrderProcessingStrategy strategy = strategies.get(order.getType());
        strategy.process(order);
    }
}
```

**2. Chain of Responsibility:**
```java
abstract class OrderHandler {
    protected OrderHandler next;
    
    public void handle(Order order) {
        if (canHandle(order)) {
            process(order);
        } else if (next != null) {
            next.handle(order);
        }
    }
}
```

**3. Command Pattern:**
```java
interface OrderCommand {
    void execute(Order order);
}

OrderCommand command = OrderCommandFactory.createCommand(order);
command.execute(order);
```

</details>

---

## 🎯 Level 4: Expert (Chuyên gia)

### Câu 10: Performance Considerations
**Câu hỏi:** Trong high-performance system, pattern nào có thể gây performance issues và cách optimize?

A) Singleton - thread contention, solution: double-checked locking  
B) Observer - notification overhead, solution: async notifications  
C) Decorator - object creation overhead, solution: flyweight  
D) Tất cả các đáp án trên  

<details>
<summary>🔍 Đáp án và giải thích</summary>

**Đáp án: D**

**Giải thích:** Tất cả patterns đều có potential performance issues:

**Singleton Issues:**
```java
// Problem: Thread contention
public static synchronized Singleton getInstance() {
    if (instance == null) {
        instance = new Singleton();
    }
    return instance;
}

// Solution: Double-checked locking
public static Singleton getInstance() {
    if (instance == null) {
        synchronized (Singleton.class) {
            if (instance == null) {
                instance = new Singleton();
            }
        }
    }
    return instance;
}
```

**Observer Issues:**
```java
// Problem: Synchronous notifications block caller
public void notifyObservers() {
    for (Observer observer : observers) {
        observer.update(this); // Blocking call
    }
}

// Solution: Async notifications
public void notifyObservers() {
    CompletableFuture.runAsync(() -> {
        observers.parallelStream().forEach(observer -> 
            observer.update(this)
        );
    });
}
```

**Decorator Issues:**
```java
// Problem: Multiple object creation
Coffee coffee = new MilkDecorator(
    new SugarDecorator(
        new WhipDecorator(
            new Espresso()
        )
    )
); // 4 objects created

// Solution: Flyweight pattern for decorators
class DecoratorFactory {
    private static final Map<String, Decorator> decorators = new HashMap<>();
    
    public static Decorator getDecorator(String type) {
        return decorators.computeIfAbsent(type, DecoratorImpl::new);
    }
}
```

**Performance Best Practices:**
- Profile before optimizing
- Consider object pooling
- Use lazy initialization
- Implement caching strategies
- Monitor memory usage

</details>

---

## 📊 Scoring System

### Điểm số và đánh giá

**Level 1 (Câu 1-3): Mỗi câu 10 điểm**
- 30 điểm: Excellent - Nắm vững cơ bản
- 20 điểm: Good - Cần ôn lại một số khái niệm
- 10 điểm: Fair - Cần học thêm về fundamentals
- 0 điểm: Poor - Nên bắt đầu từ OOP basics

**Level 2 (Câu 4-6): Mỗi câu 15 điểm**
- 45 điểm: Excellent - Hiểu sâu về patterns
- 30 điểm: Good - Cần practice thêm
- 15 điểm: Fair - Cần đọc lại documentation
- 0 điểm: Poor - Quay lại Level 1

**Level 3 (Câu 7-9): Mỗi câu 20 điểm**
- 60 điểm: Excellent - Ready for real projects
- 40 điểm: Good - Cần experience thực tế
- 20 điểm: Fair - Cần practice coding
- 0 điểm: Poor - Cần consolidate knowledge

**Level 4 (Câu 10): 25 điểm**
- 25 điểm: Expert - Có thể mentor others
- 0 điểm: Cần experience với large-scale systems

### Tổng điểm tối đa: 160 điểm

**Phân loại:**
- **140-160:** Expert Level - Có thể lead technical decisions
- **120-139:** Advanced - Ready for senior roles
- **100-119:** Intermediate - Good foundation, cần practice
- **80-99:** Beginner+ - Hiểu concepts, cần apply thực tế
- **60-79:** Beginner - Cần học thêm fundamentals
- **< 60:** Novice - Bắt đầu từ OOP basics

---

## 🎓 Learning Path Recommendations

### Dựa trên điểm số của bạn:

**Expert (140-160):**
- Nghiên cứu architectural patterns
- Contribute to open source projects
- Mentor junior developers
- Explore domain-specific patterns

**Advanced (120-139):**
- Practice với real-world projects
- Study performance optimization
- Learn about anti-patterns
- Explore pattern combinations

**Intermediate (100-119):**
- Complete practice exercises
- Build projects using multiple patterns
- Study code examples thoroughly
- Focus on when NOT to use patterns

**Beginner+ (80-99):**
- Practice coding each pattern
- Focus on understanding problems patterns solve
- Study UML diagrams
- Work through examples step by step

**Beginner (60-79):**
- Review OOP fundamentals
- Start with simple patterns (Factory, Observer)
- Use flashcards for memorization
- Focus on pattern intent and structure

**Novice (< 60):**
- Master OOP concepts first
- Understand classes, objects, inheritance
- Practice basic programming concepts
- Return to patterns after OOP mastery

---

## 🔄 Next Steps

1. **Retake quiz** sau khi học thêm
2. **Practice exercises** trong thư mục solutions
3. **Build real projects** applying patterns
4. **Join coding communities** để discuss patterns
5. **Read source code** của popular libraries

**Remember:** Patterns are tools, not goals. Focus on solving problems effectively! 🎯
