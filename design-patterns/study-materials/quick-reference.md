# Quick Reference - Design Patterns

> <PERSON><PERSON> kh<PERSON><PERSON> nhanh các Design Patterns và nguyên tắc thiết kế

## 📋 Mục lục

1. [SOLID Principles](#solid-principles)
2. [Creational Patterns](#creational-patterns)
3. [Structural Patterns](#structural-patterns)
4. [Behavioral Patterns](#behavioral-patterns)
5. [Cheat Sheet](#cheat-sheet)

---

## SOLID Principles

### S - Single Responsibility Principle (SRP)
- **<PERSON>uyên tắc:** Một lớp chỉ nên có một lý do để thay đổi
- **Mục tiêu:** <PERSON><PERSON><PERSON><PERSON> độ phức tạp, tăng tính gắn kết
- **V<PERSON> dụ:** Tách `Employee` và `TimesheetReport` thành hai lớp riêng

### O - Open/Closed Principle (OCP)
- **Nguyên tắc:** Mở để mở rộng, đóng để sửa đổi
- **<PERSON><PERSON><PERSON> áp dụng:** <PERSON>ử dụng kế thừa và giao diện thay vì sửa mã trực tiếp
- **<PERSON><PERSON> dụ:** Thêm `SeaLogistics` mà không sửa `RoadLogistics`

### L - Liskov Substitution Principle (LSP)
- **Nguyên tắc:** Đối tượng lớp con có thể thay thế đối tượng lớp cha
- **Yêu cầu:** Tương thích kiểu, bảo toàn tiền/hậu điều kiện
- **Ví dụ:** `ReadOnlyDocument` không nên ném exception trong `save()`

### I - Interface Segregation Principle (ISP)
- **Nguyên tắc:** Client không nên phụ thuộc vào phương thức không sử dụng
- **Cách áp dụng:** Tạo giao diện hẹp, cụ thể
- **Ví dụ:** Chia `CloudProvider` thành `CloudHosting`, `CDN`, `CloudStorage`

### D - Dependency Inversion Principle (DIP)
- **Nguyên tắc:** Phụ thuộc vào abstraction, không phải concrete classes
- **Cách áp dụng:** Sử dụng interface thay vì lớp cụ thể
- **Ví dụ:** `BudgetReport` phụ thuộc vào `Database` interface

---

## Creational Patterns

### Factory Method
- **Mục đích:** Tạo đối tượng thông qua giao diện, lớp con quyết định kiểu cụ thể
- **Khi dùng:** Không biết trước kiểu đối tượng, cần mở rộng
- **Cấu trúc:** `Creator` → `ConcreteCreator` → `Product`
- **Ví dụ:** `LogisticsFactory` → `RoadLogistics`, `SeaLogistics`

### Abstract Factory
- **Mục đích:** Tạo họ đối tượng liên quan
- **Khi dùng:** Cần đảm bảo tính tương thích giữa sản phẩm
- **Cấu trúc:** `AbstractFactory` → `ConcreteFactory` → `ProductFamily`
- **Ví dụ:** `FurnitureFactory` → `ModernFactory`, `VictorianFactory`

### Builder
- **Mục đích:** Xây dựng đối tượng phức tạp từng bước
- **Khi dùng:** Constructor có nhiều tham số, cần các cấu hình khác nhau
- **Cấu trúc:** `Builder` → `ConcreteBuilder` → `Director` → `Product`
- **Ví dụ:** `HouseBuilder` → `WoodenHouseBuilder`, `StoneHouseBuilder`

### Prototype
- **Mục đích:** Sao chép đối tượng hiện có
- **Khi dùng:** Tạo đối tượng tốn kém, cần clone
- **Cấu trúc:** `Prototype` → `ConcretePrototype` → `clone()`
- **Ví dụ:** `Shape` → `Circle.clone()`, `Rectangle.clone()`

### Singleton
- **Mục đích:** Đảm bảo chỉ có một instance duy nhất
- **Khi dùng:** Cần kiểm soát tài nguyên dùng chung
- **Cấu trúc:** Private constructor + `getInstance()`
- **Ví dụ:** `DatabaseConnection`, `Logger`, `ConfigManager`

---

## Structural Patterns

### Adapter
- **Mục đích:** Kết nối các giao diện không tương thích
- **Khi dùng:** Tích hợp thư viện bên thứ ba, legacy code
- **Cấu trúc:** `Client` → `Adapter` → `Adaptee`
- **Ví dụ:** `XMLToJSONAdapter` cho analytics library

### Bridge
- **Mục đích:** Tách abstraction khỏi implementation
- **Khi dùng:** Tránh "bùng nổ lớp" khi mở rộng theo nhiều chiều
- **Cấu trúc:** `Abstraction` → `Implementation` (composition)
- **Ví dụ:** `Shape` + `Color` → `RedCircle`, `BlueSquare`

### Composite
- **Mục đích:** Tổ chức đối tượng thành cấu trúc cây
- **Khi dùng:** Cần xử lý đối tượng đơn và nhóm đối tượng thống nhất
- **Cấu trúc:** `Component` → `Leaf` + `Composite`
- **Ví dụ:** File system: `File` và `Directory`

### Decorator
- **Mục đích:** Thêm chức năng động cho đối tượng
- **Khi dùng:** Cần mở rộng chức năng mà không thay đổi cấu trúc
- **Cấu trúc:** `Component` → `ConcreteComponent` + `Decorator`
- **Ví dụ:** `Coffee` → `MilkDecorator`, `SugarDecorator`

### Facade
- **Mục đích:** Cung cấp giao diện đơn giản cho hệ thống phức tạp
- **Khi dùng:** Cần đơn giản hóa tương tác với subsystem
- **Cấu trúc:** `Facade` → `Subsystem Classes`
- **Ví dụ:** `VideoConverter` che giấu codec, compression logic

### Flyweight
- **Mục đích:** Tối ưu bộ nhớ bằng chia sẻ dữ liệu chung
- **Khi dùng:** Có nhiều đối tượng tương tự tiêu thụ RAM
- **Cấu trúc:** `Flyweight` (intrinsic) + `Context` (extrinsic)
- **Ví dụ:** Game particles: `ParticleType` + `Particle`

### Proxy
- **Mục đích:** Kiểm soát quyền truy cập đến đối tượng khác
- **Khi dùng:** Lazy loading, access control, caching
- **Cấu trúc:** `Proxy` → `RealSubject` (cùng interface)
- **Ví dụ:** `ImageProxy` cho lazy loading hình ảnh

---

## Behavioral Patterns

### Chain of Responsibility
- **Mục đích:** Chuyển yêu cầu qua chuỗi handlers
- **Khi dùng:** Nhiều đối tượng có thể xử lý yêu cầu
- **Cấu trúc:** `Handler` → `ConcreteHandler` chain
- **Ví dụ:** Authentication → Authorization → Validation

### Command
- **Mục đích:** Đóng gói yêu cầu thành đối tượng
- **Khi dùng:** Undo/redo, queuing, logging operations
- **Cấu trúc:** `Command` → `ConcreteCommand` → `Receiver`
- **Ví dụ:** Text editor: `CopyCommand`, `PasteCommand`

### Iterator
- **Mục đích:** Duyệt collection mà không lộ cấu trúc
- **Khi dùng:** Cần duyệt qua các cấu trúc dữ liệu khác nhau
- **Cấu trúc:** `Iterator` → `ConcreteIterator` → `Collection`
- **Ví dụ:** `TreeIterator`, `ListIterator`

### Mediator
- **Mục đích:** Định nghĩa cách các đối tượng tương tác
- **Khi dùng:** Nhiều đối tượng tương tác phức tạp
- **Cấu trúc:** `Mediator` → `ConcreteMediator` ↔ `Components`
- **Ví dụ:** Dialog box với các UI controls

### Memento
- **Mục đích:** Lưu và khôi phục trạng thái đối tượng
- **Khi dùng:** Undo functionality, snapshots
- **Cấu trúc:** `Originator` → `Memento` → `Caretaker`
- **Ví dụ:** Text editor undo/redo

### Observer
- **Mục đích:** Thông báo thay đổi cho nhiều đối tượng
- **Khi dùng:** One-to-many dependency, event handling
- **Cấu trúc:** `Subject` → `Observer` → `ConcreteObserver`
- **Ví dụ:** Model-View pattern, event listeners

### State
- **Mục đích:** Thay đổi hành vi dựa trên trạng thái nội bộ
- **Khi dùng:** Đối tượng có nhiều trạng thái, hành vi khác nhau
- **Cấu trúc:** `Context` → `State` → `ConcreteState`
- **Ví dụ:** Media player: `PlayingState`, `PausedState`

### Strategy
- **Mục đích:** Đóng gói thuật toán và làm chúng có thể hoán đổi
- **Khi dùng:** Nhiều cách thực hiện cùng một tác vụ
- **Cấu trúc:** `Context` → `Strategy` → `ConcreteStrategy`
- **Ví dụ:** Payment: `CreditCard`, `PayPal`, `Bitcoin`

### Template Method
- **Mục đích:** Định nghĩa khung thuật toán, lớp con triển khai chi tiết
- **Khi dùng:** Thuật toán có cấu trúc chung, chi tiết khác nhau
- **Cấu trúc:** `AbstractClass` → `ConcreteClass` (override steps)
- **Ví dụ:** Data mining: `PDFDataMiner`, `CSVDataMiner`

### Visitor
- **Mục đích:** Tách thuật toán khỏi cấu trúc đối tượng
- **Khi dùng:** Cần thêm operations mà không thay đổi classes
- **Cấu trúc:** `Visitor` → `ConcreteVisitor` → `Element.accept()`
- **Ví dụ:** Compiler: `TypeChecker`, `CodeGenerator`

---

## Cheat Sheet

### Khi nào dùng pattern nào?

#### Tạo đối tượng:
- **Không biết kiểu cụ thể** → Factory Method
- **Cần họ đối tượng tương thích** → Abstract Factory  
- **Đối tượng phức tạp** → Builder
- **Sao chép đối tượng** → Prototype
- **Chỉ một instance** → Singleton

#### Cấu trúc:
- **Giao diện không tương thích** → Adapter
- **Tách abstraction/implementation** → Bridge
- **Cấu trúc cây** → Composite
- **Thêm chức năng động** → Decorator
- **Đơn giản hóa interface** → Facade
- **Tiết kiệm bộ nhớ** → Flyweight
- **Kiểm soát truy cập** → Proxy

#### Hành vi:
- **Chuỗi xử lý** → Chain of Responsibility
- **Đóng gói request** → Command
- **Duyệt collection** → Iterator
- **Trung gian giao tiếp** → Mediator
- **Lưu trạng thái** → Memento
- **Thông báo thay đổi** → Observer
- **Thay đổi hành vi theo trạng thái** → State
- **Hoán đổi thuật toán** → Strategy
- **Khung thuật toán** → Template Method
- **Thêm operation** → Visitor

### Nguyên tắc thiết kế quan trọng:

1. **Encapsulate what varies** - Đóng gói phần thay đổi
2. **Program to interface, not implementation** - Lập trình theo giao diện
3. **Favor composition over inheritance** - Ưu tiên composition
4. **Loose coupling, high cohesion** - Kết nối lỏng, gắn kết cao
5. **SOLID principles** - Nền tảng thiết kế tốt

### Anti-patterns cần tránh:

- **Over-engineering** - Thiết kế quá phức tạp
- **God Object** - Đối tượng làm quá nhiều việc
- **Tight coupling** - Kết nối chặt chẽ
- **Code duplication** - Lặp lại mã
- **Magic numbers/strings** - Hằng số không rõ nghĩa

---

**Tham khảo thêm:**
- [Flashcards](flashcards.md) - Ôn tập nhanh
- [Practice Exercises](practice-exercises.md) - Bài tập thực hành
- [Examples](../examples/) - Code examples
