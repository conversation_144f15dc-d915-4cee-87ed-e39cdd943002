/**
 * Factory Method Pattern Example
 * 
 * Scenario: Logistics company với different transport methods
 * Problem: Cần tạo different transport objects mà không hardcode
 * Solution: Factory Method cho phép subclasses quyết định object type
 */

// Product interface - định nghĩa giao diện chung cho tất cả transport
interface Transport {
    void deliver();
}

// Concrete Products - các implementation cụ thể
class Truck implements Transport {
    @Override
    public void deliver() {
        System.out.println("🚛 Deliver by land in a box");
    }
}

class Ship implements Transport {
    @Override
    public void deliver() {
        System.out.println("🚢 Deliver by sea in a container");
    }
}

class Plane implements Transport {
    @Override
    public void deliver() {
        System.out.println("✈️ Deliver by air in a package");
    }
}

// Creator abstract class - định nghĩa factory method
abstract class Logistics {
    
    /**
     * Factory method - abstract method để subclasses implement
     * Đây là core của Factory Method pattern
     */
    public abstract Transport createTransport();
    
    /**
     * Business logic sử dụng factory method
     * Method này không cần biết concrete transport type
     */
    public void planDelivery() {
        System.out.println("📋 Planning delivery...");
        
        // Gọi factory method để tạo transport
        Transport transport = createTransport();
        
        // Sử dụng transport mà không cần biết type cụ thể
        transport.deliver();
        
        System.out.println("✅ Delivery planned successfully!\n");
    }
}

// Concrete Creators - implement factory method cho specific products
class RoadLogistics extends Logistics {
    @Override
    public Transport createTransport() {
        System.out.println("🏭 Creating truck transport...");
        return new Truck();
    }
}

class SeaLogistics extends Logistics {
    @Override
    public Transport createTransport() {
        System.out.println("🏭 Creating ship transport...");
        return new Ship();
    }
}

class AirLogistics extends Logistics {
    @Override
    public Transport createTransport() {
        System.out.println("🏭 Creating plane transport...");
        return new Plane();
    }
}

// Client code demonstration
public class FactoryMethod {
    
    /**
     * Client method - không cần biết concrete logistics type
     */
    public static void processOrder(Logistics logistics) {
        logistics.planDelivery();
    }
    
    public static void main(String[] args) {
        System.out.println("=== Factory Method Pattern Demo ===\n");
        
        // Scenario 1: Road delivery
        System.out.println("📦 Order 1: Domestic delivery");
        Logistics roadLogistics = new RoadLogistics();
        processOrder(roadLogistics);
        
        // Scenario 2: Sea delivery  
        System.out.println("📦 Order 2: International delivery");
        Logistics seaLogistics = new SeaLogistics();
        processOrder(seaLogistics);
        
        // Scenario 3: Air delivery
        System.out.println("📦 Order 3: Express delivery");
        Logistics airLogistics = new AirLogistics();
        processOrder(airLogistics);
        
        // Demonstration: Easy to add new transport types
        System.out.println("🔧 Adding new transport type is easy!");
        System.out.println("   Just create new ConcreteCreator class");
        System.out.println("   No need to modify existing code (OCP)");
    }
}

/**
 * Key Benefits demonstrated:
 * 
 * 1. LOOSE COUPLING: Client code không phụ thuộc vào concrete classes
 * 2. EXTENSIBILITY: Dễ dàng thêm transport types mới
 * 3. SRP: Mỗi creator chỉ chịu trách nhiệm tạo một loại transport
 * 4. OCP: Open for extension (new transports), closed for modification
 * 
 * When to use Factory Method:
 * - Không biết trước exact types của objects cần tạo
 * - Muốn cho phép users extend internal components
 * - Cần save system resources bằng cách reuse existing objects
 * 
 * Real-world examples:
 * - GUI frameworks (createButton() cho different OS)
 * - Database connections (createConnection() cho different DBs)
 * - Document processors (createDocument() cho different formats)
 */
