"""
Factory Method Pattern Example in Python

Scenario: Logistics company với different transport methods
Problem: Cần tạo different transport objects mà không hardcode
Solution: Factory Method cho phép subclasses quyết định object type
"""

from abc import ABC, abstractmethod
from typing import Protocol


# Product interface - định nghĩa giao diện chung cho tất cả transport
class Transport(Protocol):
    def deliver(self) -> None:
        """Deliver the package"""
        pass


# Concrete Products - các implementation cụ thể
class Truck:
    def deliver(self) -> None:
        print("🚛 Deliver by land in a box")


class Ship:
    def deliver(self) -> None:
        print("🚢 Deliver by sea in a container")


class Plane:
    def deliver(self) -> None:
        print("✈️ Deliver by air in a package")


# Creator abstract class - định nghĩa factory method
class Logistics(ABC):
    
    @abstractmethod
    def create_transport(self) -> Transport:
        """
        Factory method - abstract method để subclasses implement
        Đây là core của Factory Method pattern
        """
        pass
    
    def plan_delivery(self) -> None:
        """
        Business logic sử dụng factory method
        Method này không cần biết concrete transport type
        """
        print("📋 Planning delivery...")
        
        # Gọi factory method để tạo transport
        transport = self.create_transport()
        
        # Sử dụng transport mà không cần biết type cụ thể
        transport.deliver()
        
        print("✅ Delivery planned successfully!\n")


# Concrete Creators - implement factory method cho specific products
class RoadLogistics(Logistics):
    def create_transport(self) -> Transport:
        print("🏭 Creating truck transport...")
        return Truck()


class SeaLogistics(Logistics):
    def create_transport(self) -> Transport:
        print("🏭 Creating ship transport...")
        return Ship()


class AirLogistics(Logistics):
    def create_transport(self) -> Transport:
        print("🏭 Creating plane transport...")
        return Plane()


# Client code demonstration
def process_order(logistics: Logistics) -> None:
    """
    Client method - không cần biết concrete logistics type
    """
    logistics.plan_delivery()


def main():
    print("=== Factory Method Pattern Demo ===\n")
    
    # Scenario 1: Road delivery
    print("📦 Order 1: Domestic delivery")
    road_logistics = RoadLogistics()
    process_order(road_logistics)
    
    # Scenario 2: Sea delivery  
    print("📦 Order 2: International delivery")
    sea_logistics = SeaLogistics()
    process_order(sea_logistics)
    
    # Scenario 3: Air delivery
    print("📦 Order 3: Express delivery")
    air_logistics = AirLogistics()
    process_order(air_logistics)
    
    # Demonstration: Easy to add new transport types
    print("🔧 Adding new transport type is easy!")
    print("   Just create new ConcreteCreator class")
    print("   No need to modify existing code (OCP)")


# Advanced example: Document Factory
class Document(ABC):
    @abstractmethod
    def create(self) -> None:
        pass


class WordDocument(Document):
    def create(self) -> None:
        print("📄 Creating Word document")


class PDFDocument(Document):
    def create(self) -> None:
        print("📄 Creating PDF document")


class ExcelDocument(Document):
    def create(self) -> None:
        print("📊 Creating Excel document")


class DocumentCreator(ABC):
    @abstractmethod
    def create_document(self) -> Document:
        pass
    
    def new_document(self) -> None:
        print("🔧 Starting document creation process...")
        doc = self.create_document()
        doc.create()
        print("✅ Document creation completed!\n")


class WordDocumentCreator(DocumentCreator):
    def create_document(self) -> Document:
        return WordDocument()


class PDFDocumentCreator(DocumentCreator):
    def create_document(self) -> Document:
        return PDFDocument()


class ExcelDocumentCreator(DocumentCreator):
    def create_document(self) -> Document:
        return ExcelDocument()


def document_demo():
    print("=== Document Factory Demo ===\n")
    
    # Create different document types
    creators = [
        ("Word", WordDocumentCreator()),
        ("PDF", PDFDocumentCreator()),
        ("Excel", ExcelDocumentCreator())
    ]
    
    for doc_type, creator in creators:
        print(f"Creating {doc_type} document:")
        creator.new_document()


# Real-world example: UI Button Factory
class Button(ABC):
    @abstractmethod
    def render(self) -> None:
        pass
    
    @abstractmethod
    def on_click(self) -> None:
        pass


class WindowsButton(Button):
    def render(self) -> None:
        print("🖼️ Rendering Windows-style button")
    
    def on_click(self) -> None:
        print("👆 Windows button clicked!")


class MacButton(Button):
    def render(self) -> None:
        print("🖼️ Rendering Mac-style button")
    
    def on_click(self) -> None:
        print("👆 Mac button clicked!")


class LinuxButton(Button):
    def render(self) -> None:
        print("🖼️ Rendering Linux-style button")
    
    def on_click(self) -> None:
        print("👆 Linux button clicked!")


class Dialog(ABC):
    @abstractmethod
    def create_button(self) -> Button:
        pass
    
    def render_window(self) -> None:
        print("🪟 Rendering dialog window...")
        button = self.create_button()
        button.render()
        print("✅ Window rendered successfully!")
        return button


class WindowsDialog(Dialog):
    def create_button(self) -> Button:
        return WindowsButton()


class MacDialog(Dialog):
    def create_button(self) -> Button:
        return MacButton()


class LinuxDialog(Dialog):
    def create_button(self) -> Button:
        return LinuxButton()


def ui_demo():
    print("=== Cross-Platform UI Demo ===\n")
    
    import platform
    system = platform.system().lower()
    
    # Factory method based on current OS
    if "windows" in system:
        dialog = WindowsDialog()
    elif "darwin" in system:  # macOS
        dialog = MacDialog()
    else:  # Linux and others
        dialog = LinuxDialog()
    
    print(f"Detected OS: {platform.system()}")
    button = dialog.render_window()
    button.on_click()


if __name__ == "__main__":
    main()
    print("\n" + "="*50 + "\n")
    document_demo()
    print("\n" + "="*50 + "\n")
    ui_demo()


"""
Key Benefits demonstrated:

1. LOOSE COUPLING: Client code không phụ thuộc vào concrete classes
2. EXTENSIBILITY: Dễ dàng thêm transport types mới
3. SRP: Mỗi creator chỉ chịu trách nhiệm tạo một loại transport
4. OCP: Open for extension (new transports), closed for modification

When to use Factory Method:
- Không biết trước exact types của objects cần tạo
- Muốn cho phép users extend internal components
- Cần save system resources bằng cách reuse existing objects

Real-world examples:
- GUI frameworks (create_button() cho different OS)
- Database connections (create_connection() cho different DBs)
- Document processors (create_document() cho different formats)

Python-specific advantages:
- Duck typing makes Protocol usage optional
- ABC module provides clean abstract base classes
- Type hints improve code documentation
- Multiple inheritance allows flexible designs
"""
