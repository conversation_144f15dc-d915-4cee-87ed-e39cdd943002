/**
 * Factory Method Pattern Example in TypeScript
 * 
 * Scenario: Logistics company với different transport methods
 * Problem: Cần tạo different transport objects mà không hardcode
 * Solution: Factory Method cho phép subclasses quyết định object type
 */

// Product interface - định nghĩa giao diện chung cho tất cả transport
interface Transport {
    deliver(): void;
}

// Concrete Products - các implementation cụ thể
class Truck implements Transport {
    deliver(): void {
        console.log("🚛 Deliver by land in a box");
    }
}

class Ship implements Transport {
    deliver(): void {
        console.log("🚢 Deliver by sea in a container");
    }
}

class Plane implements Transport {
    deliver(): void {
        console.log("✈️ Deliver by air in a package");
    }
}

// Creator abstract class - định nghĩa factory method
abstract class Logistics {
    /**
     * Factory method - abstract method để subclasses implement
     * Đây là core của Factory Method pattern
     */
    abstract createTransport(): Transport;
    
    /**
     * Business logic sử dụng factory method
     * Method này không cần biết concrete transport type
     */
    planDelivery(): void {
        console.log("📋 Planning delivery...");
        
        // Gọi factory method để tạo transport
        const transport = this.createTransport();
        
        // Sử dụng transport mà không cần biết type cụ thể
        transport.deliver();
        
        console.log("✅ Delivery planned successfully!\n");
    }
}

// Concrete Creators - implement factory method cho specific products
class RoadLogistics extends Logistics {
    createTransport(): Transport {
        console.log("🏭 Creating truck transport...");
        return new Truck();
    }
}

class SeaLogistics extends Logistics {
    createTransport(): Transport {
        console.log("🏭 Creating ship transport...");
        return new Ship();
    }
}

class AirLogistics extends Logistics {
    createTransport(): Transport {
        console.log("🏭 Creating plane transport...");
        return new Plane();
    }
}

// Client code demonstration
function processOrder(logistics: Logistics): void {
    /**
     * Client method - không cần biết concrete logistics type
     */
    logistics.planDelivery();
}

function main(): void {
    console.log("=== Factory Method Pattern Demo ===\n");
    
    // Scenario 1: Road delivery
    console.log("📦 Order 1: Domestic delivery");
    const roadLogistics = new RoadLogistics();
    processOrder(roadLogistics);
    
    // Scenario 2: Sea delivery  
    console.log("📦 Order 2: International delivery");
    const seaLogistics = new SeaLogistics();
    processOrder(seaLogistics);
    
    // Scenario 3: Air delivery
    console.log("📦 Order 3: Express delivery");
    const airLogistics = new AirLogistics();
    processOrder(airLogistics);
    
    // Demonstration: Easy to add new transport types
    console.log("🔧 Adding new transport type is easy!");
    console.log("   Just create new ConcreteCreator class");
    console.log("   No need to modify existing code (OCP)");
}

// Advanced example: Document Factory with generics
interface Document {
    create(): void;
}

class WordDocument implements Document {
    create(): void {
        console.log("📄 Creating Word document");
    }
}

class PDFDocument implements Document {
    create(): void {
        console.log("📄 Creating PDF document");
    }
}

class ExcelDocument implements Document {
    create(): void {
        console.log("📊 Creating Excel document");
    }
}

abstract class DocumentCreator<T extends Document> {
    abstract createDocument(): T;
    
    newDocument(): void {
        console.log("🔧 Starting document creation process...");
        const doc = this.createDocument();
        doc.create();
        console.log("✅ Document creation completed!\n");
    }
}

class WordDocumentCreator extends DocumentCreator<WordDocument> {
    createDocument(): WordDocument {
        return new WordDocument();
    }
}

class PDFDocumentCreator extends DocumentCreator<PDFDocument> {
    createDocument(): PDFDocument {
        return new PDFDocument();
    }
}

class ExcelDocumentCreator extends DocumentCreator<ExcelDocument> {
    createDocument(): ExcelDocument {
        return new ExcelDocument();
    }
}

function documentDemo(): void {
    console.log("=== Document Factory Demo ===\n");
    
    // Create different document types
    const creators: Array<[string, DocumentCreator<any>]> = [
        ["Word", new WordDocumentCreator()],
        ["PDF", new PDFDocumentCreator()],
        ["Excel", new ExcelDocumentCreator()]
    ];
    
    creators.forEach(([docType, creator]) => {
        console.log(`Creating ${docType} document:`);
        creator.newDocument();
    });
}

// Real-world example: UI Button Factory with enum
enum Platform {
    WINDOWS = "windows",
    MAC = "mac",
    LINUX = "linux"
}

interface Button {
    render(): void;
    onClick(): void;
}

class WindowsButton implements Button {
    render(): void {
        console.log("🖼️ Rendering Windows-style button");
    }
    
    onClick(): void {
        console.log("👆 Windows button clicked!");
    }
}

class MacButton implements Button {
    render(): void {
        console.log("🖼️ Rendering Mac-style button");
    }
    
    onClick(): void {
        console.log("👆 Mac button clicked!");
    }
}

class LinuxButton implements Button {
    render(): void {
        console.log("🖼️ Rendering Linux-style button");
    }
    
    onClick(): void {
        console.log("👆 Linux button clicked!");
    }
}

abstract class Dialog {
    abstract createButton(): Button;
    
    renderWindow(): Button {
        console.log("🪟 Rendering dialog window...");
        const button = this.createButton();
        button.render();
        console.log("✅ Window rendered successfully!");
        return button;
    }
}

class WindowsDialog extends Dialog {
    createButton(): Button {
        return new WindowsButton();
    }
}

class MacDialog extends Dialog {
    createButton(): Button {
        return new MacButton();
    }
}

class LinuxDialog extends Dialog {
    createButton(): Button {
        return new LinuxButton();
    }
}

// Factory for creating dialogs based on platform
class DialogFactory {
    static createDialog(platform: Platform): Dialog {
        switch (platform) {
            case Platform.WINDOWS:
                return new WindowsDialog();
            case Platform.MAC:
                return new MacDialog();
            case Platform.LINUX:
                return new LinuxDialog();
            default:
                throw new Error(`Unsupported platform: ${platform}`);
        }
    }
}

function uiDemo(): void {
    console.log("=== Cross-Platform UI Demo ===\n");
    
    // Simulate different platforms
    const platforms = [Platform.WINDOWS, Platform.MAC, Platform.LINUX];
    
    platforms.forEach(platform => {
        console.log(`\nTesting on ${platform.toUpperCase()}:`);
        const dialog = DialogFactory.createDialog(platform);
        const button = dialog.renderWindow();
        button.onClick();
    });
}

// Modern TypeScript features: Factory with async operations
interface AsyncResource {
    load(): Promise<void>;
}

class DatabaseConnection implements AsyncResource {
    constructor(private connectionString: string) {}
    
    async load(): Promise<void> {
        console.log(`🔌 Connecting to database: ${this.connectionString}`);
        // Simulate async connection
        await new Promise(resolve => setTimeout(resolve, 100));
        console.log("✅ Database connected!");
    }
}

class FileResource implements AsyncResource {
    constructor(private filePath: string) {}
    
    async load(): Promise<void> {
        console.log(`📁 Loading file: ${this.filePath}`);
        // Simulate async file loading
        await new Promise(resolve => setTimeout(resolve, 50));
        console.log("✅ File loaded!");
    }
}

abstract class ResourceLoader {
    abstract createResource(): AsyncResource;
    
    async loadResource(): Promise<AsyncResource> {
        console.log("🚀 Starting resource loading...");
        const resource = this.createResource();
        await resource.load();
        console.log("🎉 Resource loading completed!\n");
        return resource;
    }
}

class DatabaseLoader extends ResourceLoader {
    constructor(private connectionString: string) {
        super();
    }
    
    createResource(): AsyncResource {
        return new DatabaseConnection(this.connectionString);
    }
}

class FileLoader extends ResourceLoader {
    constructor(private filePath: string) {
        super();
    }
    
    createResource(): AsyncResource {
        return new FileResource(this.filePath);
    }
}

async function asyncDemo(): Promise<void> {
    console.log("=== Async Factory Demo ===\n");
    
    const dbLoader = new DatabaseLoader("postgresql://localhost:5432/mydb");
    const fileLoader = new FileLoader("/path/to/config.json");
    
    try {
        await dbLoader.loadResource();
        await fileLoader.loadResource();
    } catch (error) {
        console.error("❌ Error loading resources:", error);
    }
}

// Run all demos
async function runAllDemos(): Promise<void> {
    main();
    console.log("\n" + "=".repeat(50) + "\n");
    
    documentDemo();
    console.log("\n" + "=".repeat(50) + "\n");
    
    uiDemo();
    console.log("\n" + "=".repeat(50) + "\n");
    
    await asyncDemo();
}

// Export for module usage
export {
    Transport,
    Logistics,
    RoadLogistics,
    SeaLogistics,
    AirLogistics,
    Platform,
    DialogFactory
};

// Run if this file is executed directly
if (require.main === module) {
    runAllDemos().catch(console.error);
}

/**
 * Key Benefits demonstrated:
 * 
 * 1. LOOSE COUPLING: Client code không phụ thuộc vào concrete classes
 * 2. EXTENSIBILITY: Dễ dàng thêm transport types mới
 * 3. SRP: Mỗi creator chỉ chịu trách nhiệm tạo một loại transport
 * 4. OCP: Open for extension (new transports), closed for modification
 * 
 * TypeScript-specific advantages:
 * - Strong typing ensures type safety
 * - Generics provide flexible, reusable code
 * - Enums improve code readability
 * - Async/await support for modern applications
 * - Interface segregation promotes clean design
 * 
 * When to use Factory Method:
 * - Không biết trước exact types của objects cần tạo
 * - Muốn cho phép users extend internal components
 * - Cần save system resources bằng cách reuse existing objects
 * 
 * Real-world examples:
 * - GUI frameworks (createButton() cho different OS)
 * - Database connections (createConnection() cho different DBs)
 * - Document processors (createDocument() cho different formats)
 * - API clients (createClient() cho different services)
 */
