/**
 * Solution for Exercise 1: Document Factory (Factory Method)
 * 
 * Requirements:
 * - Hỗ trợ tạo Word, PDF, Excel documents
 * - Mỗi loại document có cách mở và lưu khác nhau
 * - <PERSON><PERSON> dàng thêm loại document mới (PowerPoint, etc.)
 */

// Document interface
interface Document {
    void open();
    void save();
    void close();
}

// Concrete Documents
class WordDocument implements Document {
    private String fileName;
    
    public WordDocument(String fileName) {
        this.fileName = fileName;
    }
    
    @Override
    public void open() {
        System.out.println("Opening Word document: " + fileName);
        System.out.println("Loading Microsoft Word...");
    }
    
    @Override
    public void save() {
        System.out.println("Saving Word document: " + fileName);
        System.out.println("Saved in .docx format");
    }
    
    @Override
    public void close() {
        System.out.println("Closing Word document: " + fileName);
    }
}

class PDFDocument implements Document {
    private String fileName;
    
    public PDFDocument(String fileName) {
        this.fileName = fileName;
    }
    
    @Override
    public void open() {
        System.out.println("Opening PDF document: " + fileName);
        System.out.println("Loading PDF reader...");
    }
    
    @Override
    public void save() {
        System.out.println("Saving PDF document: " + fileName);
        System.out.println("Saved in .pdf format");
    }
    
    @Override
    public void close() {
        System.out.println("Closing PDF document: " + fileName);
    }
}

class ExcelDocument implements Document {
    private String fileName;
    
    public ExcelDocument(String fileName) {
        this.fileName = fileName;
    }
    
    @Override
    public void open() {
        System.out.println("Opening Excel document: " + fileName);
        System.out.println("Loading Microsoft Excel...");
    }
    
    @Override
    public void save() {
        System.out.println("Saving Excel document: " + fileName);
        System.out.println("Saved in .xlsx format");
    }
    
    @Override
    public void close() {
        System.out.println("Closing Excel document: " + fileName);
    }
}

// Abstract Creator
abstract class DocumentCreator {
    // Factory method
    public abstract Document createDocument(String fileName);
    
    // Template method using factory method
    public void processDocument(String fileName) {
        System.out.println("=== Processing Document ===");
        Document document = createDocument(fileName);
        document.open();
        
        // Simulate some work
        System.out.println("Working on document...");
        
        document.save();
        document.close();
        System.out.println("=== Document Processing Complete ===\n");
    }
}

// Concrete Creators
class WordDocumentCreator extends DocumentCreator {
    @Override
    public Document createDocument(String fileName) {
        System.out.println("Creating Word document...");
        return new WordDocument(fileName);
    }
}

class PDFDocumentCreator extends DocumentCreator {
    @Override
    public Document createDocument(String fileName) {
        System.out.println("Creating PDF document...");
        return new PDFDocument(fileName);
    }
}

class ExcelDocumentCreator extends DocumentCreator {
    @Override
    public Document createDocument(String fileName) {
        System.out.println("Creating Excel document...");
        return new ExcelDocument(fileName);
    }
}

// Extension: PowerPoint document (demonstrates extensibility)
class PowerPointDocument implements Document {
    private String fileName;
    
    public PowerPointDocument(String fileName) {
        this.fileName = fileName;
    }
    
    @Override
    public void open() {
        System.out.println("Opening PowerPoint document: " + fileName);
        System.out.println("Loading Microsoft PowerPoint...");
    }
    
    @Override
    public void save() {
        System.out.println("Saving PowerPoint document: " + fileName);
        System.out.println("Saved in .pptx format");
    }
    
    @Override
    public void close() {
        System.out.println("Closing PowerPoint document: " + fileName);
    }
}

class PowerPointDocumentCreator extends DocumentCreator {
    @Override
    public Document createDocument(String fileName) {
        System.out.println("Creating PowerPoint document...");
        return new PowerPointDocument(fileName);
    }
}

// Document Factory for easier usage
class DocumentFactory {
    public static DocumentCreator getCreator(String documentType) {
        switch (documentType.toLowerCase()) {
            case "word":
                return new WordDocumentCreator();
            case "pdf":
                return new PDFDocumentCreator();
            case "excel":
                return new ExcelDocumentCreator();
            case "powerpoint":
                return new PowerPointDocumentCreator();
            default:
                throw new IllegalArgumentException("Unknown document type: " + documentType);
        }
    }
}

// Client code
public class DocumentFactory {
    public static void main(String[] args) {
        System.out.println("=== Document Factory Pattern Demo ===\n");
        
        // Test cases as specified in exercise
        
        // Tạo Word document
        DocumentCreator wordCreator = new WordDocumentCreator();
        Document doc = wordCreator.createDocument("report.docx");
        doc.open(); // Should print: "Opening Word document: report.docx"
        doc.save();
        doc.close();
        
        System.out.println();
        
        // Tạo PDF document  
        DocumentCreator pdfCreator = new PDFDocumentCreator();
        Document pdf = pdfCreator.createDocument("manual.pdf");
        pdf.save(); // Should print: "Saving PDF document: manual.pdf"
        
        System.out.println();
        
        // Using factory for easier creation
        System.out.println("=== Using Document Factory ===");
        
        String[] documentTypes = {"word", "pdf", "excel", "powerpoint"};
        String[] fileNames = {"proposal.docx", "guide.pdf", "budget.xlsx", "presentation.pptx"};
        
        for (int i = 0; i < documentTypes.length; i++) {
            try {
                DocumentCreator creator = DocumentFactory.getCreator(documentTypes[i]);
                creator.processDocument(fileNames[i]);
            } catch (IllegalArgumentException e) {
                System.err.println("Error: " + e.getMessage());
            }
        }
        
        // Demonstrate extensibility
        System.out.println("=== Extensibility Demo ===");
        System.out.println("Adding new document type without modifying existing code:");
        
        DocumentCreator newCreator = new PowerPointDocumentCreator();
        newCreator.processDocument("new-presentation.pptx");
        
        // Advanced usage: Document processing pipeline
        System.out.println("=== Document Processing Pipeline ===");
        
        DocumentProcessor processor = new DocumentProcessor();
        processor.processMultipleDocuments();
    }
}

// Advanced example: Document processing pipeline
class DocumentProcessor {
    public void processMultipleDocuments() {
        // Simulate a document processing workflow
        String[][] documents = {
            {"word", "contract.docx"},
            {"pdf", "invoice.pdf"},
            {"excel", "financial-report.xlsx"},
            {"powerpoint", "quarterly-review.pptx"}
        };
        
        System.out.println("Processing document batch...");
        
        for (String[] docInfo : documents) {
            String type = docInfo[0];
            String fileName = docInfo[1];
            
            try {
                DocumentCreator creator = DocumentFactory.getCreator(type);
                
                // Create and process document
                Document document = creator.createDocument(fileName);
                document.open();
                
                // Simulate processing based on document type
                processDocumentContent(document, type);
                
                document.save();
                document.close();
                
                System.out.println("✅ Successfully processed: " + fileName + "\n");
                
            } catch (Exception e) {
                System.err.println("❌ Failed to process " + fileName + ": " + e.getMessage());
            }
        }
        
        System.out.println("Batch processing completed!");
    }
    
    private void processDocumentContent(Document document, String type) {
        switch (type) {
            case "word":
                System.out.println("📝 Spell checking and formatting...");
                break;
            case "pdf":
                System.out.println("🔍 Extracting text and metadata...");
                break;
            case "excel":
                System.out.println("📊 Calculating formulas and charts...");
                break;
            case "powerpoint":
                System.out.println("🎨 Optimizing slides and animations...");
                break;
        }
    }
}

/**
 * Solution Analysis:
 * 
 * ✅ Requirements Met:
 * 1. Supports Word, PDF, Excel documents ✓
 * 2. Each document type has different open/save behavior ✓
 * 3. Easy to add new document types (PowerPoint example) ✓
 * 
 * 🏗️ Design Patterns Used:
 * - Factory Method: DocumentCreator hierarchy
 * - Template Method: processDocument() method
 * - Strategy: Different document behaviors
 * 
 * 🎯 Benefits Demonstrated:
 * - Open/Closed Principle: Can add new document types without modifying existing code
 * - Single Responsibility: Each creator handles one document type
 * - Loose Coupling: Client code doesn't depend on concrete document classes
 * - Extensibility: PowerPoint support added easily
 * 
 * 🔧 Additional Features:
 * - DocumentFactory utility class for easier usage
 * - Document processing pipeline example
 * - Error handling for unknown document types
 * - Batch processing capabilities
 * 
 * 📝 Usage Patterns:
 * 1. Direct creator usage: new WordDocumentCreator().createDocument()
 * 2. Factory-based creation: DocumentFactory.getCreator().createDocument()
 * 3. Template method usage: creator.processDocument()
 * 4. Batch processing: DocumentProcessor.processMultipleDocuments()
 */
