# 💻 Code Examples - Design Patterns Implementation

> Production-ready code examples for all 23 Design Patterns in Java, Python, and TypeScript

[![Languages](https://img.shields.io/badge/Languages-3-blue)](#supported-languages)
[![Patterns](https://img.shields.io/badge/Patterns-23-green)](#pattern-coverage)
[![Examples](https://img.shields.io/badge/Examples-69-orange)](#examples-overview)
[![Production Ready](https://img.shields.io/badge/Production-Ready-success)](#quality-standards)

---

## 🚀 Quick Start

### 🏃‍♂️ **Run Your First Example**

**Java:**
```bash
cd examples/java/creational/
javac FactoryMethod.java
java FactoryMethod
```

**Python:**
```bash
cd examples/python/creational/
python factory_method.py
```

**TypeScript:**
```bash
cd examples/typescript/creational/
npx ts-node factory-method.ts
```

### 📂 **Browse by Category**
- 🏗️ [**Creational**](#creational-patterns) - Object creation (5 patterns)
- 🔧 [**Structural**](#structural-patterns) - Object composition (7 patterns)  
- 🎭 [**Behavioral**](#behavioral-patterns) - Object interaction (11 patterns)

### 🎯 **Find by Use Case**
- 📱 **Web Development:** Observer, Strategy, Factory Method
- 🎮 **Game Development:** Prototype, State, Flyweight
- 🏢 **Enterprise:** Abstract Factory, Chain of Responsibility, Mediator

---

## 🗂️ Directory Structure

```
examples/
├── 🇯🇦 java/                     # Java 11+ implementations
│   ├── creational/             # 5 creation patterns
│   ├── structural/             # 7 structure patterns  
│   └── behavioral/             # 11 behavior patterns
│
├── 🐍 python/                   # Python 3.8+ implementations
│   ├── creational/             # Clean, Pythonic code
│   ├── structural/             # Type hints included
│   └── behavioral/             # Production patterns
│
├── 📜 typescript/              # TypeScript/Node.js implementations
│   ├── creational/             # Modern ES6+ syntax
│   ├── structural/             # Strict type checking
│   └── behavioral/             # Async/await patterns
│
├── 📋 templates/                # Starter templates
│   ├── java-template/          # Maven project template
│   ├── python-template/        # Poetry project template
│   └── typescript-template/    # npm/yarn project template
│
└── 📝 solutions/                # Exercise solutions
    ├── exercise-01-document-factory/
    ├── exercise-02-cross-platform-ui/
    └── exercise-03-computer-builder/
```

### 📈 **Quality Standards**
- ✅ **Production Ready:** All code tested and documented
- ✅ **Best Practices:** Follows language conventions  
- ✅ **Real-world Scenarios:** Practical business use cases
- ✅ **Performance Optimized:** Memory and CPU efficient
- ✅ **Error Handling:** Comprehensive exception management

---

## 🚀 Cách sử dụng

### Java Examples
```bash
# Compile và chạy Java examples
cd java/creational
javac FactoryMethod.java
java FactoryMethod
```

### Python Examples
```bash
# Chạy Python examples
cd python/creational
python factory_method.py
```

### TypeScript Examples
```bash
# Compile và chạy TypeScript examples
cd typescript/creational
tsc factory_method.ts
node factory_method.js
```

## 📚 Danh sách Examples

### Creational Patterns

#### Factory Method
- **Java:** Logistics system với Truck/Ship transport
- **Python:** Document creator với Word/PDF documents
- **TypeScript:** UI Dialog với Windows/Mac buttons

#### Abstract Factory
- **Java:** Furniture store với Modern/Victorian styles
- **Python:** GUI toolkit với Windows/Mac/Linux themes
- **TypeScript:** Game với Medieval/Sci-fi asset families

#### Builder
- **Java:** Computer builder với Gaming/Office configurations
- **Python:** House builder với Wood/Stone materials
- **TypeScript:** SQL query builder

#### Prototype
- **Java:** Shape cloning system
- **Python:** Document template system
- **TypeScript:** Game character cloning

#### Singleton
- **Java:** Database connection manager
- **Python:** Logger system
- **TypeScript:** Configuration manager

### Structural Patterns

#### Adapter
- **Java:** Media player với legacy audio formats
- **Python:** Data format converter (XML to JSON)
- **TypeScript:** Third-party API integration

#### Bridge
- **Java:** Remote control với different devices
- **Python:** Drawing API với different rendering engines
- **TypeScript:** Notification system với multiple platforms

#### Composite
- **Java:** File system với files và directories
- **Python:** Organization hierarchy
- **TypeScript:** UI component tree

#### Decorator
- **Java:** Coffee shop với add-ons
- **Python:** Text formatting system
- **TypeScript:** Data validation pipeline

#### Facade
- **Java:** Smart home automation
- **Python:** Video conversion system
- **TypeScript:** Payment processing facade

#### Flyweight
- **Java:** Game particle system
- **Python:** Text editor character formatting
- **TypeScript:** Icon management system

#### Proxy
- **Java:** Image lazy loading
- **Python:** Database connection proxy
- **TypeScript:** API caching proxy

### Behavioral Patterns

#### Chain of Responsibility
- **Java:** Support ticket routing
- **Python:** Middleware pipeline
- **TypeScript:** Event handling chain

#### Command
- **Java:** Text editor với undo/redo
- **Python:** Remote control system
- **TypeScript:** Task queue system

#### Iterator
- **Java:** Tree traversal
- **Python:** Custom collection iterator
- **TypeScript:** Async data stream iterator

#### Mediator
- **Java:** Chat room system
- **Python:** Air traffic control
- **TypeScript:** Component communication hub

#### Memento
- **Java:** Game save/load system
- **Python:** Document version control
- **TypeScript:** Form state management

#### Observer
- **Java:** Stock price monitoring
- **Python:** Event notification system
- **TypeScript:** Model-View updates

#### State
- **Java:** Media player states
- **Python:** Order processing states
- **TypeScript:** Game character states

#### Strategy
- **Java:** Payment processing
- **Python:** Sorting algorithms
- **TypeScript:** Route calculation strategies

#### Template Method
- **Java:** Data mining framework
- **Python:** Report generation
- **TypeScript:** Test framework

#### Visitor
- **Java:** Compiler AST processing
- **Python:** File system operations
- **TypeScript:** DOM manipulation

## 🎯 Features của Examples

### Realistic Scenarios
- **Practical use cases** từ thực tế
- **Complete implementations** có thể chạy được
- **Clear comments** giải thích từng phần

### Best Practices
- **SOLID principles** được áp dụng
- **Clean code** standards
- **Error handling** appropriate

### Learning Aids
- **Step-by-step comments** trong code
- **UML diagrams** cho complex patterns
- **Performance notes** khi cần thiết

## 🔧 Setup Requirements

### Java
- JDK 8 hoặc cao hơn
- IDE: IntelliJ IDEA, Eclipse, hoặc VS Code

### Python
- Python 3.7 hoặc cao hơn
- IDE: PyCharm, VS Code, hoặc Jupyter

### TypeScript
- Node.js 14 hoặc cao hơn
- TypeScript compiler: `npm install -g typescript`
- IDE: VS Code, WebStorm

## 📖 Learning Path

### Beginner
1. Bắt đầu với **Creational patterns** (đơn giản nhất)
2. Đọc code và comments carefully
3. Chạy examples và observe output
4. Modify code để hiểu behavior

### Intermediate
1. Study **Structural patterns**
2. Compare implementations across languages
3. Try to implement variations
4. Focus on when to use each pattern

### Advanced
1. Master **Behavioral patterns**
2. Combine multiple patterns
3. Design your own examples
4. Optimize for performance

## 🤝 Contributing

Nếu bạn muốn đóng góp examples:

1. **Follow coding standards** của từng ngôn ngữ
2. **Add comprehensive comments**
3. **Include test cases** nếu có thể
4. **Update README** với example mới

## 📚 Additional Resources

- [Pattern Documentation](../patterns/) - Chi tiết về từng pattern
- [Practice Exercises](../study-materials/practice-exercises.md) - Bài tập thực hành
- [Quick Reference](../study-materials/quick-reference.md) - Tham khảo nhanh

---

**Happy Coding! 🎉**

## 🏗️ Creational Patterns
> **Focus:** Smart object creation strategies

| Pattern | Java Example | Python Example | TypeScript Example | Complexity |
|---------|--------------|----------------|---------------------|------------|
| **Factory Method** | `LogisticsFactory` | `DocumentCreator` | `UIDialogFactory` | ⭐⭐ |
| **Abstract Factory** | `FurnitureStore` | `GUIToolkit` | `GameAssetFactory` | ⭐⭐⭐ |
| **Builder** | `ComputerBuilder` | `HouseBuilder` | `SQLQueryBuilder` | ⭐⭐ |
| **Prototype** | `ShapeCloner` | `DocumentTemplate` | `CharacterCloner` | ⭐⭐ |
| **Singleton** | `DatabaseManager` | `Logger` | `ConfigManager` | ⭐ |

### 🎯 **Real-world Use Cases**
- **Factory Method:** Framework components, plugin systems
- **Abstract Factory:** Cross-platform UI, theme systems
- **Builder:** Configuration objects, SQL queries
- **Prototype:** Game objects, document templates
- **Singleton:** Loggers, database connections, caches

---

## 🔧 Structural Patterns
> **Focus:** Object composition and relationships

| Pattern | Java Example | Python Example | TypeScript Example | Complexity |
|---------|--------------|----------------|---------------------|------------|
| **Adapter** | `MediaPlayerAdapter` | `XMLToJSONAdapter` | `LegacyAPIAdapter` | ⭐⭐ |
| **Bridge** | `RemoteControlBridge` | `DrawingAPIBridge` | `NotificationBridge` | ⭐⭐⭐⭐ |
| **Composite** | `FileSystemComposite` | `OrganizationTree` | `UIComponentTree` | ⭐⭐⭐ |
| **Decorator** | `CoffeeDecorator` | `TextFormatterDecorator` | `ValidationDecorator` | ⭐⭐⭐ |
| **Facade** | `SmartHomeFacade` | `VideoConverterFacade` | `PaymentFacade` | ⭐⭐ |
| **Flyweight** | `ParticleSystemFlyweight` | `TextEditorFlyweight` | `IconManagerFlyweight` | ⭐⭐⭐⭐ |
| **Proxy** | `ImageLazyProxy` | `DatabaseProxy` | `CachingProxy` | ⭐⭐⭐ |

### 🎯 **Real-world Use Cases**
- **Adapter:** Third-party library integration
- **Bridge:** Multi-platform support, device drivers
- **Composite:** File systems, UI hierarchies
- **Decorator:** Middleware, feature toggles
- **Facade:** API simplification, subsystem hiding
- **Flyweight:** Memory optimization, caching
- **Proxy:** Security, lazy loading, caching

---

## 🎭 Behavioral Patterns
> **Focus:** Communication and responsibility distribution

| Pattern | Java Example | Python Example | TypeScript Example | Complexity |
|---------|--------------|----------------|---------------------|------------|
| **Chain of Responsibility** | `SupportTicketChain` | `MiddlewarePipeline` | `EventHandlerChain` | ⭐⭐⭐ |
| **Command** | `TextEditorCommand` | `RemoteControlCommand` | `TaskQueueCommand` | ⭐⭐⭐ |
| **Iterator** | `TreeIterator` | `CustomCollectionIterator` | `AsyncStreamIterator` | ⭐⭐ |
| **Mediator** | `ChatRoomMediator` | `AirTrafficMediator` | `ComponentHubMediator` | ⭐⭐⭐⭐ |
| **Memento** | `GameSaveMemento` | `DocumentVersionMemento` | `FormStateMemento` | ⭐⭐⭐ |
| **Observer** | `StockPriceObserver` | `EventNotificationObserver` | `ModelViewObserver` | ⭐⭐ |
| **State** | `MediaPlayerState` | `OrderProcessingState` | `CharacterState` | ⭐⭐⭐ |
| **Strategy** | `PaymentStrategy` | `SortingStrategy` | `RouteStrategy` | ⭐⭐ |
| **Template Method** | `DataMiningTemplate` | `ReportTemplate` | `TestFrameworkTemplate` | ⭐⭐ |
| **Visitor** | `CompilerVisitor` | `FileSystemVisitor` | `DOMVisitor` | ⭐⭐⭐⭐⭐ |
| **Interpreter** | `SQLInterpreter` | `MathExpressionInterpreter` | `QueryLanguageInterpreter` | ⭐⭐⭐⭐ |

### 🎯 **Real-world Use Cases**
- **Chain of Responsibility:** Middleware, error handling
- **Command:** Undo/redo, macro recording, queuing
- **Iterator:** Data traversal, streaming
- **Mediator:** Complex UI interactions, service orchestration
- **Memento:** Versioning, snapshots, rollback
- **Observer:** Event systems, MVC, reactive programming
- **State:** State machines, workflow engines
- **Strategy:** Algorithm selection, policy configuration
- **Template Method:** Frameworks, code reuse
- **Visitor:** Compilers, AST processing, data transformation

---

## 🚀 Getting Started

### 📋 **Prerequisites**
- **Java:** JDK 11+ installed
- **Python:** Python 3.8+ with type hints support
- **TypeScript:** Node.js 16+ and TypeScript compiler

### ⚡ **Quick Commands**

**Java Examples:**
```bash
cd examples/java/creational/
javac FactoryMethod.java && java FactoryMethod
```

**Python Examples:**
```bash
cd examples/python/creational/
python factory_method.py
```

**TypeScript Examples:**
```bash
cd examples/typescript/creational/
npx ts-node factory-method.ts
# OR compile first:
tsc factory-method.ts && node factory-method.js
```

### 🎮 **Interactive Examples**
Each example includes:
- 📝 **Complete source code** with extensive comments
- 🧪 **Test cases** demonstrating usage
- 📊 **Performance benchmarks** where applicable
- 🔍 **Debug output** showing internal workings
- 💡 **Variations** and alternative implementations

---