# Memento Pattern

> **Behavioral Pattern** - <PERSON><PERSON><PERSON> trữ và khôi phục trạng thái trước đó của object mà không vi phạm encapsulation

## 📋 <PERSON><PERSON><PERSON> lụ<PERSON>

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#gi<PERSON><PERSON>-pháp)
4. [C<PERSON>u trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON><PERSON><PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Memento là một **behavioral design pattern** cho phép **capture và externalize internal state của object** mà không vi phạm encapsulation, để object c<PERSON> thể đượ<PERSON> restored về state này sau đó.

### <PERSON><PERSON><PERSON> đích chính
- **Save object state** mà không expose internal structure
- **Restore object** về previous state
- **Implement undo/redo** functionality
- **Maintain encapsulation** while providing state access

### Tên gọi khác
- **Snapshot Pattern**
- **Token Pattern**

### Ví dụ thực tế
Giống như **save game feature**: bạn có thể save game state tại bất kỳ thời điểm nào và load lại sau đó. Game không cần expose internal details về characters, inventory, progress.

---

## Vấn đề

### Tình huống thực tế
Bạn đang phát triển text editor cần undo/redo functionality:

```java
public class TextEditor {
    private String content;
    private String font;
    private int fontSize;
    private boolean bold;
    private boolean italic;
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public void setFont(String font) {
        this.font = font;
    }
    
    public void setFontSize(int fontSize) {
        this.fontSize = fontSize;
    }
    
    public void setBold(boolean bold) {
        this.bold = bold;
    }
    
    public void setItalic(boolean italic) {
        this.italic = italic;
    }
    
    // How to implement undo/redo?
}
```

### Vấn đề phát sinh

#### 1. Violating encapsulation
```java
// Naive approach - expose all internal state
public class TextEditor {
    public String getContent() { return content; }
    public String getFont() { return font; }
    public int getFontSize() { return fontSize; }
    public boolean isBold() { return bold; }
    public boolean isItalic() { return italic; }
    
    // All internal state exposed!
    // Breaks encapsulation principle
}

public class UndoManager {
    public void saveState(TextEditor editor) {
        // Must access all internal details
        String content = editor.getContent();
        String font = editor.getFont();
        int fontSize = editor.getFontSize();
        boolean bold = editor.isBold();
        boolean italic = editor.isItalic();
        
        // Store somehow...
    }
}
```

#### 2. Tight coupling
```java
// UndoManager tightly coupled to TextEditor internals
public class UndoManager {
    private List<EditorState> history = new ArrayList<>();
    
    public void saveState(TextEditor editor) {
        // Knows about TextEditor's internal structure
        EditorState state = new EditorState(
            editor.getContent(),
            editor.getFont(),
            editor.getFontSize(),
            editor.isBold(),
            editor.isItalic()
        );
        history.add(state);
    }
    
    // If TextEditor adds new fields, UndoManager must change!
}
```

#### 3. Complex state management
```java
// What if TextEditor has complex internal objects?
public class TextEditor {
    private Document document; // Complex object
    private List<Plugin> plugins; // Collection of objects
    private Map<String, Object> settings; // Dynamic settings
    
    // How to save/restore these complex states?
    // Deep copy? Serialization? Manual field copying?
}
```

#### 4. No control over state access
```java
// External classes can modify state inappropriately
public class BadCode {
    public void messWithEditor(TextEditor editor) {
        editor.setContent(""); // Accidentally clear content
        editor.setFont(null);  // Break editor state
        // No protection against misuse
    }
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Memento pattern đề xuất **delegate creating state snapshots đến actual owner của state** - originator object. Thay vì other objects trying to copy editor's state từ outside, editor class itself có thể make snapshot vì nó có full access đến own state.

### Cách hoạt động

#### 1. Memento class
```java
public class EditorMemento {
    private final String content;
    private final String font;
    private final int fontSize;
    private final boolean bold;
    private final boolean italic;
    private final Date timestamp;
    
    // Package-private constructor - only TextEditor can create
    EditorMemento(String content, String font, int fontSize, boolean bold, boolean italic) {
        this.content = content;
        this.font = font;
        this.fontSize = fontSize;
        this.bold = bold;
        this.italic = italic;
        this.timestamp = new Date();
    }
    
    // Package-private getters - only TextEditor can access
    String getContent() { return content; }
    String getFont() { return font; }
    int getFontSize() { return fontSize; }
    boolean isBold() { return bold; }
    boolean isItalic() { return italic; }
    
    public Date getTimestamp() { return timestamp; }
    
    @Override
    public String toString() {
        return String.format("Memento[%s, %s, %d, bold=%s, italic=%s]", 
                           content.substring(0, Math.min(20, content.length())), 
                           font, fontSize, bold, italic);
    }
}
```

#### 2. Originator (TextEditor)
```java
public class TextEditor {
    private String content = "";
    private String font = "Arial";
    private int fontSize = 12;
    private boolean bold = false;
    private boolean italic = false;
    
    // Public methods for normal operations
    public void setContent(String content) {
        this.content = content != null ? content : "";
    }
    
    public void setFont(String font) {
        this.font = font != null ? font : "Arial";
    }
    
    public void setFontSize(int fontSize) {
        this.fontSize = Math.max(8, Math.min(72, fontSize));
    }
    
    public void setBold(boolean bold) {
        this.bold = bold;
    }
    
    public void setItalic(boolean italic) {
        this.italic = italic;
    }
    
    // Create memento - captures current state
    public EditorMemento createMemento() {
        return new EditorMemento(content, font, fontSize, bold, italic);
    }
    
    // Restore from memento
    public void restoreFromMemento(EditorMemento memento) {
        if (memento != null) {
            this.content = memento.getContent();
            this.font = memento.getFont();
            this.fontSize = memento.getFontSize();
            this.bold = memento.isBold();
            this.italic = memento.isItalic();
        }
    }
    
    // Public read-only access
    public String getDisplayText() {
        StringBuilder sb = new StringBuilder();
        if (bold) sb.append("<b>");
        if (italic) sb.append("<i>");
        sb.append(content);
        if (italic) sb.append("</i>");
        if (bold) sb.append("</b>");
        return sb.toString();
    }
    
    public String getCurrentFont() {
        return font + " " + fontSize + "pt";
    }
    
    @Override
    public String toString() {
        return String.format("TextEditor[content='%s', font=%s %dpt, bold=%s, italic=%s]",
                           content.length() > 20 ? content.substring(0, 20) + "..." : content,
                           font, fontSize, bold, italic);
    }
}
```

#### 3. Caretaker (UndoManager)
```java
public class UndoManager {
    private Stack<EditorMemento> undoStack = new Stack<>();
    private Stack<EditorMemento> redoStack = new Stack<>();
    private static final int MAX_HISTORY = 50;
    
    public void saveState(EditorMemento memento) {
        // Clear redo stack when new state is saved
        redoStack.clear();
        
        // Add to undo stack
        undoStack.push(memento);
        
        // Limit history size
        if (undoStack.size() > MAX_HISTORY) {
            undoStack.remove(0); // Remove oldest
        }
        
        System.out.println("State saved: " + memento);
    }
    
    public EditorMemento undo() {
        if (canUndo()) {
            EditorMemento currentState = undoStack.pop();
            redoStack.push(currentState);
            
            if (!undoStack.isEmpty()) {
                EditorMemento previousState = undoStack.peek();
                System.out.println("Undo to: " + previousState);
                return previousState;
            }
        }
        
        System.out.println("Nothing to undo");
        return null;
    }
    
    public EditorMemento redo() {
        if (canRedo()) {
            EditorMemento stateToRestore = redoStack.pop();
            undoStack.push(stateToRestore);
            System.out.println("Redo to: " + stateToRestore);
            return stateToRestore;
        }
        
        System.out.println("Nothing to redo");
        return null;
    }
    
    public boolean canUndo() {
        return undoStack.size() > 1; // Need at least 2 states to undo
    }
    
    public boolean canRedo() {
        return !redoStack.isEmpty();
    }
    
    public void clear() {
        undoStack.clear();
        redoStack.clear();
        System.out.println("History cleared");
    }
    
    public void showHistory() {
        System.out.println("=== Undo History ===");
        for (int i = undoStack.size() - 1; i >= 0; i--) {
            String marker = (i == undoStack.size() - 1) ? " <- Current" : "";
            System.out.println((i + 1) + ". " + undoStack.get(i) + marker);
        }
        
        if (!redoStack.isEmpty()) {
            System.out.println("=== Redo Stack ===");
            for (int i = redoStack.size() - 1; i >= 0; i--) {
                System.out.println((i + 1) + ". " + redoStack.get(i));
            }
        }
        System.out.println("==================");
    }
}
```

#### 4. Client usage
```java
public class TextEditorApp {
    private TextEditor editor;
    private UndoManager undoManager;
    
    public TextEditorApp() {
        this.editor = new TextEditor();
        this.undoManager = new UndoManager();
        
        // Save initial state
        undoManager.saveState(editor.createMemento());
    }
    
    public void typeText(String text) {
        editor.setContent(editor.getDisplayText() + text);
        undoManager.saveState(editor.createMemento());
    }
    
    public void changeFont(String font, int size) {
        editor.setFont(font);
        editor.setFontSize(size);
        undoManager.saveState(editor.createMemento());
    }
    
    public void toggleBold() {
        editor.setBold(!editor.toString().contains("bold=true"));
        undoManager.saveState(editor.createMemento());
    }
    
    public void toggleItalic() {
        editor.setItalic(!editor.toString().contains("italic=true"));
        undoManager.saveState(editor.createMemento());
    }
    
    public void undo() {
        EditorMemento previousState = undoManager.undo();
        if (previousState != null) {
            editor.restoreFromMemento(previousState);
        }
    }
    
    public void redo() {
        EditorMemento nextState = undoManager.redo();
        if (nextState != null) {
            editor.restoreFromMemento(nextState);
        }
    }
    
    public void showCurrentState() {
        System.out.println("Current: " + editor);
    }
    
    public void showHistory() {
        undoManager.showHistory();
    }
}
```

#### 5. Demo usage
```java
public class MementoPatternDemo {
    public static void main(String[] args) {
        TextEditorApp app = new TextEditorApp();
        
        System.out.println("=== Text Editor with Undo/Redo ===");
        app.showCurrentState();
        
        // Type some text
        System.out.println("\n--- Typing text ---");
        app.typeText("Hello");
        app.showCurrentState();
        
        app.typeText(" World");
        app.showCurrentState();
        
        // Change formatting
        System.out.println("\n--- Changing format ---");
        app.changeFont("Times New Roman", 14);
        app.showCurrentState();
        
        app.toggleBold();
        app.showCurrentState();
        
        app.toggleItalic();
        app.showCurrentState();
        
        // Show history
        System.out.println("\n--- History ---");
        app.showHistory();
        
        // Undo operations
        System.out.println("\n--- Undo operations ---");
        app.undo();
        app.showCurrentState();
        
        app.undo();
        app.showCurrentState();
        
        app.undo();
        app.showCurrentState();
        
        // Redo operations
        System.out.println("\n--- Redo operations ---");
        app.redo();
        app.showCurrentState();
        
        app.redo();
        app.showCurrentState();
        
        // Show final history
        System.out.println("\n--- Final History ---");
        app.showHistory();
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Originator
├── - state: Object
├── + createMemento(): Memento
├── + restoreFromMemento(Memento): void
├── + setState(Object): void
├── + getState(): Object
│
Memento
├── - state: Object
├── + Memento(Object)
├── + getState(): Object
│
Caretaker
├── - mementos: List<Memento>
├── + saveMemento(Memento): void
├── + getMemento(int): Memento
├── + undo(): Memento
├── + redo(): Memento
```

### Các thành phần chính

#### 1. Originator
- **Vai trò:** Object có state cần được saved/restored
- **Đặc điểm:** Creates và consumes mementos

#### 2. Memento
- **Vai trò:** Stores snapshot của originator's state
- **Đặc điểm:** Immutable, limited access to state

#### 3. Caretaker
- **Vai trò:** Manages mementos nhưng không access content
- **Đặc điểm:** Treats mementos as opaque objects

---

## Cách triển khai

### Bước 1: Define Memento class
```java
public class GameMemento {
    private final int level;
    private final int score;
    private final int lives;
    private final Date saveTime;
    
    GameMemento(int level, int score, int lives) {
        this.level = level;
        this.score = score;
        this.lives = lives;
        this.saveTime = new Date();
    }
    
    int getLevel() { return level; }
    int getScore() { return score; }
    int getLives() { return lives; }
    public Date getSaveTime() { return saveTime; }
}
```

### Bước 2: Implement Originator
```java
public class Game {
    private int level = 1;
    private int score = 0;
    private int lives = 3;
    
    public GameMemento save() {
        return new GameMemento(level, score, lives);
    }
    
    public void restore(GameMemento memento) {
        this.level = memento.getLevel();
        this.score = memento.getScore();
        this.lives = memento.getLives();
    }
    
    // Game operations
    public void nextLevel() { level++; }
    public void addScore(int points) { score += points; }
    public void loseLife() { lives--; }
}
```

### Bước 3: Implement Caretaker
```java
public class SaveGameManager {
    private List<GameMemento> saves = new ArrayList<>();
    
    public void saveGame(GameMemento memento) {
        saves.add(memento);
    }
    
    public GameMemento loadGame(int index) {
        return saves.get(index);
    }
    
    public List<GameMemento> getAllSaves() {
        return new ArrayList<>(saves);
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Preserves encapsulation
```java
// Memento doesn't expose internal structure
public class EditorMemento {
    private final String content; // Private fields
    
    // Package-private constructor and getters
    EditorMemento(String content) { this.content = content; }
    String getContent() { return content; }
}
```

#### 2. Simplifies originator
```java
// Originator doesn't need complex state management
public class TextEditor {
    public EditorMemento createMemento() {
        return new EditorMemento(content, font, fontSize); // Simple
    }
}
```

#### 3. Caretaker independence
```java
// Caretaker doesn't know about state details
public class UndoManager {
    public void save(Memento memento) {
        history.add(memento); // Treats as opaque object
    }
}
```

### ❌ Nhược điểm

#### 1. Memory overhead
```java
// Each memento stores complete state
public class LargeObjectMemento {
    private final byte[] largeData; // Could be megabytes
    private final List<ComplexObject> objects; // Memory intensive
}
```

#### 2. Performance cost
```java
// Creating memento might be expensive
public DocumentMemento createMemento() {
    // Deep copy of complex object graph
    return new DocumentMemento(deepCopy(document));
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Need undo/redo functionality
```java
// Text editors, image editors, IDEs
// Games with save/load features
// Database transactions
```

#### 2. Need snapshots of object state
```java
// Backup systems
// Version control systems
// Checkpoint mechanisms
```

#### 3. Direct state access violates encapsulation
```java
// When exposing getters for all fields is inappropriate
// Complex internal state that shouldn't be exposed
```

### ❌ Không nên sử dụng khi:

#### 1. State is simple và public
```java
// Simple value objects
public class Point {
    public int x, y; // Simple public fields
}
```

#### 2. Memory constraints
```java
// When state is very large
// Limited memory environments
// Real-time systems with strict memory requirements
```

---

## Ví dụ thực tế

### Ví dụ 1: Drawing Application với Undo/Redo

```java
// Shape classes
public abstract class Shape {
    protected int x, y;
    protected String color;
    
    public Shape(int x, int y, String color) {
        this.x = x;
        this.y = y;
        this.color = color;
    }
    
    public abstract Shape clone();
    
    @Override
    public String toString() {
        return getClass().getSimpleName() + "[x=" + x + ", y=" + y + ", color=" + color + "]";
    }
}

public class Circle extends Shape {
    private int radius;
    
    public Circle(int x, int y, String color, int radius) {
        super(x, y, color);
        this.radius = radius;
    }
    
    @Override
    public Shape clone() {
        return new Circle(x, y, color, radius);
    }
    
    @Override
    public String toString() {
        return "Circle[x=" + x + ", y=" + y + ", color=" + color + ", radius=" + radius + "]";
    }
}

public class Rectangle extends Shape {
    private int width, height;
    
    public Rectangle(int x, int y, String color, int width, int height) {
        super(x, y, color);
        this.width = width;
        this.height = height;
    }
    
    @Override
    public Shape clone() {
        return new Rectangle(x, y, color, width, height);
    }
    
    @Override
    public String toString() {
        return "Rectangle[x=" + x + ", y=" + y + ", color=" + color + 
               ", width=" + width + ", height=" + height + "]";
    }
}

// Canvas memento
public class CanvasMemento {
    private final List<Shape> shapes;
    private final String canvasColor;
    private final Date timestamp;
    private final String description;
    
    CanvasMemento(List<Shape> shapes, String canvasColor, String description) {
        // Deep copy of shapes list
        this.shapes = new ArrayList<>();
        for (Shape shape : shapes) {
            this.shapes.add(shape.clone());
        }
        this.canvasColor = canvasColor;
        this.timestamp = new Date();
        this.description = description;
    }
    
    List<Shape> getShapes() {
        // Return deep copy to prevent modification
        List<Shape> copy = new ArrayList<>();
        for (Shape shape : shapes) {
            copy.add(shape.clone());
        }
        return copy;
    }
    
    String getCanvasColor() { return canvasColor; }
    public Date getTimestamp() { return timestamp; }
    public String getDescription() { return description; }
    
    @Override
    public String toString() {
        return String.format("CanvasMemento[%s, %d shapes, %s]", 
                           description, shapes.size(), canvasColor);
    }
}

// Canvas (Originator)
public class Canvas {
    private List<Shape> shapes;
    private String backgroundColor;
    
    public Canvas() {
        this.shapes = new ArrayList<>();
        this.backgroundColor = "white";
    }
    
    // Drawing operations
    public void addShape(Shape shape) {
        shapes.add(shape);
        System.out.println("Added: " + shape);
    }
    
    public void removeShape(int index) {
        if (index >= 0 && index < shapes.size()) {
            Shape removed = shapes.remove(index);
            System.out.println("Removed: " + removed);
        }
    }
    
    public void setBackgroundColor(String color) {
        this.backgroundColor = color;
        System.out.println("Background color changed to: " + color);
    }
    
    public void clear() {
        shapes.clear();
        System.out.println("Canvas cleared");
    }
    
    // Memento operations
    public CanvasMemento createMemento(String description) {
        return new CanvasMemento(shapes, backgroundColor, description);
    }
    
    public void restoreFromMemento(CanvasMemento memento) {
        this.shapes = memento.getShapes();
        this.backgroundColor = memento.getCanvasColor();
        System.out.println("Canvas restored from: " + memento.getDescription());
    }
    
    // Display
    public void display() {
        System.out.println("=== Canvas (Background: " + backgroundColor + ") ===");
        if (shapes.isEmpty()) {
            System.out.println("(Empty canvas)");
        } else {
            for (int i = 0; i < shapes.size(); i++) {
                System.out.println((i + 1) + ". " + shapes.get(i));
            }
        }
        System.out.println("=====================================");
    }
    
    public int getShapeCount() {
        return shapes.size();
    }
}

// History manager (Caretaker)
public class DrawingHistoryManager {
    private Stack<CanvasMemento> undoStack;
    private Stack<CanvasMemento> redoStack;
    private static final int MAX_HISTORY = 20;
    
    public DrawingHistoryManager() {
        this.undoStack = new Stack<>();
        this.redoStack = new Stack<>();
    }
    
    public void saveState(CanvasMemento memento) {
        // Clear redo stack when new state is saved
        redoStack.clear();
        
        // Add to undo stack
        undoStack.push(memento);
        
        // Limit history size
        if (undoStack.size() > MAX_HISTORY) {
            undoStack.remove(0);
        }
        
        System.out.println("📸 State saved: " + memento.getDescription());
    }
    
    public CanvasMemento undo() {
        if (canUndo()) {
            CanvasMemento currentState = undoStack.pop();
            redoStack.push(currentState);
            
            if (!undoStack.isEmpty()) {
                CanvasMemento previousState = undoStack.peek();
                System.out.println("↶ Undo to: " + previousState.getDescription());
                return previousState;
            }
        }
        
        System.out.println("Nothing to undo");
        return null;
    }
    
    public CanvasMemento redo() {
        if (canRedo()) {
            CanvasMemento stateToRestore = redoStack.pop();
            undoStack.push(stateToRestore);
            System.out.println("↷ Redo to: " + stateToRestore.getDescription());
            return stateToRestore;
        }
        
        System.out.println("Nothing to redo");
        return null;
    }
    
    public boolean canUndo() {
        return undoStack.size() > 1;
    }
    
    public boolean canRedo() {
        return !redoStack.isEmpty();
    }
    
    public void showHistory() {
        System.out.println("\n=== Drawing History ===");
        
        if (undoStack.isEmpty()) {
            System.out.println("No history available");
            return;
        }
        
        System.out.println("Undo Stack:");
        for (int i = undoStack.size() - 1; i >= 0; i--) {
            String marker = (i == undoStack.size() - 1) ? " <- Current" : "";
            System.out.println("  " + (i + 1) + ". " + undoStack.get(i).getDescription() + marker);
        }
        
        if (!redoStack.isEmpty()) {
            System.out.println("Redo Stack:");
            for (int i = redoStack.size() - 1; i >= 0; i--) {
                System.out.println("  " + (i + 1) + ". " + redoStack.get(i).getDescription());
            }
        }
        
        System.out.println("=====================\n");
    }
}

// Drawing application
public class DrawingApplication {
    private Canvas canvas;
    private DrawingHistoryManager historyManager;
    
    public DrawingApplication() {
        this.canvas = new Canvas();
        this.historyManager = new DrawingHistoryManager();
        
        // Save initial empty state
        historyManager.saveState(canvas.createMemento("Initial empty canvas"));
    }
    
    public void drawCircle(int x, int y, String color, int radius) {
        canvas.addShape(new Circle(x, y, color, radius));
        historyManager.saveState(canvas.createMemento("Added circle"));
    }
    
    public void drawRectangle(int x, int y, String color, int width, int height) {
        canvas.addShape(new Rectangle(x, y, color, width, height));
        historyManager.saveState(canvas.createMemento("Added rectangle"));
    }
    
    public void removeShape(int index) {
        canvas.removeShape(index);
        historyManager.saveState(canvas.createMemento("Removed shape"));
    }
    
    public void changeBackground(String color) {
        canvas.setBackgroundColor(color);
        historyManager.saveState(canvas.createMemento("Changed background"));
    }
    
    public void clearCanvas() {
        canvas.clear();
        historyManager.saveState(canvas.createMemento("Cleared canvas"));
    }
    
    public void undo() {
        CanvasMemento previousState = historyManager.undo();
        if (previousState != null) {
            canvas.restoreFromMemento(previousState);
        }
    }
    
    public void redo() {
        CanvasMemento nextState = historyManager.redo();
        if (nextState != null) {
            canvas.restoreFromMemento(nextState);
        }
    }
    
    public void showCanvas() {
        canvas.display();
    }
    
    public void showHistory() {
        historyManager.showHistory();
    }
}

// Usage
public class DrawingApplicationDemo {
    public static void main(String[] args) {
        DrawingApplication app = new DrawingApplication();
        
        System.out.println("=== Drawing Application with Undo/Redo ===");
        app.showCanvas();
        
        // Draw some shapes
        System.out.println("\n--- Drawing shapes ---");
        app.drawCircle(10, 10, "red", 5);
        app.showCanvas();
        
        app.drawRectangle(20, 20, "blue", 10, 8);
        app.showCanvas();
        
        app.drawCircle(30, 30, "green", 7);
        app.showCanvas();
        
        // Change background
        System.out.println("\n--- Changing background ---");
        app.changeBackground("lightgray");
        app.showCanvas();
        
        // Show history
        app.showHistory();
        
        // Undo operations
        System.out.println("--- Undo operations ---");
        app.undo(); // Undo background change
        app.showCanvas();
        
        app.undo(); // Undo last circle
        app.showCanvas();
        
        app.undo(); // Undo rectangle
        app.showCanvas();
        
        // Redo operations
        System.out.println("--- Redo operations ---");
        app.redo(); // Redo rectangle
        app.showCanvas();
        
        app.redo(); // Redo circle
        app.showCanvas();
        
        // Add new shape (should clear redo stack)
        System.out.println("--- Adding new shape (clears redo) ---");
        app.drawRectangle(40, 40, "yellow", 6, 6);
        app.showCanvas();
        
        // Show final history
        app.showHistory();
        
        System.out.println("Memento Pattern Benefits:");
        System.out.println("- Complete undo/redo functionality");
        System.out.println("- Encapsulation preserved (Canvas internals hidden)");
        System.out.println("- History manager independent of Canvas implementation");
        System.out.println("- Easy to extend with new shape types");
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Memento Pattern** captures object state mà không vi phạm encapsulation
2. **Three roles:** Originator, Memento, Caretaker
3. **Enables undo/redo** functionality
4. **Preserves object boundaries** và data hiding

### So sánh với patterns khác
| Pattern | Purpose | State Management |
|---------|---------|------------------|
| **Memento** | Save/restore object state | External snapshots |
| **Command** | Encapsulate operations | Operation history |
| **Prototype** | Clone objects | Object copying |
| **State** | Change behavior with state | Internal state transitions |

### Best Practices
- **Limit memento size** để avoid memory issues
- **Use immutable mementos** để prevent tampering
- **Implement memento cleanup** cho long-running applications
- **Consider serialization** cho persistent storage
- **Document memento lifecycle** và usage patterns

---

**Tiếp theo:** [Visitor](visitor.md) - Thực hiện operations trên object structures
