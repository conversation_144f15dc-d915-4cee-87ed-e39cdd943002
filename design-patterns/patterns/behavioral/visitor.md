# Visitor Pattern

> **Behavioral Pattern** - <PERSON><PERSON><PERSON> nghĩa operations mới trên object structure mà không thay đổi classes của elements

## 📋 Mục lục

1. [Tổng quan](#tổng-quan)
2. [V<PERSON>n đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [Cách triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nhược-điểm)
7. [<PERSON>hi nào sử dụng](#khi-nào-sử-dụng)
8. [V<PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Visitor là một **behavioral design pattern** cho phép **define new operations mà không thay đổi classes của elements** mà nó operates on. Pat<PERSON> separates algorithms từ object structure mà nó operates on.

### <PERSON><PERSON><PERSON> đích chính
- **Add new operations** mà không modify existing classes
- **Separate algorithms** từ object structure
- **Perform operations** across heterogeneous object collections
- **Follow Open/Closed Principle** cho operations

### Tên gọi khác
- **Double Dispatch Pattern**
- **Operation Pattern**

### Ví dụ thực tế
Giống như **tax inspector**: inspector có thể visit different types of buildings (residential, commercial, industrial) và apply different tax calculations cho mỗi type mà không thay đổi building classes.

---

## Vấn đề

### Tình huống thực tế
Bạn có geometric shapes hierarchy cần different operations:

```java
public abstract class Shape {
    public abstract double getArea();
}

public class Circle extends Shape {
    private double radius;
    
    public Circle(double radius) {
        this.radius = radius;
    }
    
    @Override
    public double getArea() {
        return Math.PI * radius * radius;
    }
    
    public double getRadius() {
        return radius;
    }
}

public class Rectangle extends Shape {
    private double width, height;
    
    public Rectangle(double width, double height) {
        this.width = width;
        this.height = height;
    }
    
    @Override
    public double getArea() {
        return width * height;
    }
    
    public double getWidth() { return width; }
    public double getHeight() { return height; }
}
```

### Vấn đề phát sinh

#### 1. Adding new operations requires modifying all classes
```java
// Need to add XML export functionality
public abstract class Shape {
    public abstract double getArea();
    public abstract String toXML(); // New method - modify all subclasses!
}

public class Circle extends Shape {
    // ... existing code
    
    @Override
    public String toXML() {
        return "<circle radius='" + radius + "'/>";
    }
}

public class Rectangle extends Shape {
    // ... existing code
    
    @Override
    public String toXML() {
        return "<rectangle width='" + width + "' height='" + height + "'/>";
    }
}

// What if we need JSON export? PDF export? Database export?
// Every new operation requires modifying ALL shape classes!
```

#### 2. Violates Single Responsibility Principle
```java
public class Circle extends Shape {
    private double radius;
    
    // Core responsibility: being a circle
    public double getArea() { ... }
    public double getRadius() { ... }
    
    // Additional responsibilities that don't belong:
    public String toXML() { ... }        // XML formatting
    public String toJSON() { ... }       // JSON formatting
    public void drawOnCanvas() { ... }   // Drawing logic
    public void saveToDatabase() { ... } // Persistence logic
    public double calculateTax() { ... } // Business logic
    
    // Circle class becomes bloated with unrelated responsibilities!
}
```

#### 3. Violates Open/Closed Principle
```java
// Cannot add new operations without modifying existing classes
// Classes are not closed for modification
// Every new feature requires changing stable, tested code
```

#### 4. Type checking and casting
```java
public void processShapes(List<Shape> shapes) {
    for (Shape shape : shapes) {
        // Ugly type checking and casting
        if (shape instanceof Circle) {
            Circle circle = (Circle) shape;
            // Circle-specific operation
        } else if (shape instanceof Rectangle) {
            Rectangle rectangle = (Rectangle) shape;
            // Rectangle-specific operation
        } else if (shape instanceof Triangle) {
            Triangle triangle = (Triangle) shape;
            // Triangle-specific operation
        }
        // Adding new shape type requires modifying this method!
    }
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Visitor pattern đề xuất **place new behaviors vào separate class hierarchy** gọi là visitors. Original object "accepts" visitor và delegates call đến appropriate visitor method.

### Cách hoạt động

#### 1. Visitor interface
```java
public interface ShapeVisitor {
    void visitCircle(Circle circle);
    void visitRectangle(Rectangle rectangle);
    void visitTriangle(Triangle triangle);
}
```

#### 2. Element interface
```java
public interface Shape {
    void accept(ShapeVisitor visitor);
    double getArea(); // Core functionality remains
}
```

#### 3. Concrete Elements
```java
public class Circle implements Shape {
    private double radius;
    
    public Circle(double radius) {
        this.radius = radius;
    }
    
    @Override
    public void accept(ShapeVisitor visitor) {
        visitor.visitCircle(this); // Double dispatch
    }
    
    @Override
    public double getArea() {
        return Math.PI * radius * radius;
    }
    
    public double getRadius() {
        return radius;
    }
}

public class Rectangle implements Shape {
    private double width, height;
    
    public Rectangle(double width, double height) {
        this.width = width;
        this.height = height;
    }
    
    @Override
    public void accept(ShapeVisitor visitor) {
        visitor.visitRectangle(this); // Double dispatch
    }
    
    @Override
    public double getArea() {
        return width * height;
    }
    
    public double getWidth() { return width; }
    public double getHeight() { return height; }
}

public class Triangle implements Shape {
    private double base, height;
    
    public Triangle(double base, double height) {
        this.base = base;
        this.height = height;
    }
    
    @Override
    public void accept(ShapeVisitor visitor) {
        visitor.visitTriangle(this);
    }
    
    @Override
    public double getArea() {
        return 0.5 * base * height;
    }
    
    public double getBase() { return base; }
    public double getHeight() { return height; }
}
```

#### 4. Concrete Visitors
```java
public class XMLExportVisitor implements ShapeVisitor {
    private StringBuilder xml = new StringBuilder();
    
    @Override
    public void visitCircle(Circle circle) {
        xml.append("<circle radius='").append(circle.getRadius()).append("'/>\n");
    }
    
    @Override
    public void visitRectangle(Rectangle rectangle) {
        xml.append("<rectangle width='").append(rectangle.getWidth())
           .append("' height='").append(rectangle.getHeight()).append("'/>\n");
    }
    
    @Override
    public void visitTriangle(Triangle triangle) {
        xml.append("<triangle base='").append(triangle.getBase())
           .append("' height='").append(triangle.getHeight()).append("'/>\n");
    }
    
    public String getXML() {
        return xml.toString();
    }
}

public class JSONExportVisitor implements ShapeVisitor {
    private List<String> jsonObjects = new ArrayList<>();
    
    @Override
    public void visitCircle(Circle circle) {
        jsonObjects.add(String.format(
            "{\"type\":\"circle\",\"radius\":%.2f,\"area\":%.2f}",
            circle.getRadius(), circle.getArea()
        ));
    }
    
    @Override
    public void visitRectangle(Rectangle rectangle) {
        jsonObjects.add(String.format(
            "{\"type\":\"rectangle\",\"width\":%.2f,\"height\":%.2f,\"area\":%.2f}",
            rectangle.getWidth(), rectangle.getHeight(), rectangle.getArea()
        ));
    }
    
    @Override
    public void visitTriangle(Triangle triangle) {
        jsonObjects.add(String.format(
            "{\"type\":\"triangle\",\"base\":%.2f,\"height\":%.2f,\"area\":%.2f}",
            triangle.getBase(), triangle.getHeight(), triangle.getArea()
        ));
    }
    
    public String getJSON() {
        return "[" + String.join(",", jsonObjects) + "]";
    }
}

public class AreaCalculatorVisitor implements ShapeVisitor {
    private double totalArea = 0;
    
    @Override
    public void visitCircle(Circle circle) {
        totalArea += circle.getArea();
        System.out.println("Circle area: " + circle.getArea());
    }
    
    @Override
    public void visitRectangle(Rectangle rectangle) {
        totalArea += rectangle.getArea();
        System.out.println("Rectangle area: " + rectangle.getArea());
    }
    
    @Override
    public void visitTriangle(Triangle triangle) {
        totalArea += triangle.getArea();
        System.out.println("Triangle area: " + triangle.getArea());
    }
    
    public double getTotalArea() {
        return totalArea;
    }
}
```

#### 5. Usage
```java
public class ShapeProcessor {
    
    public static void processShapes(List<Shape> shapes) {
        // XML Export
        XMLExportVisitor xmlVisitor = new XMLExportVisitor();
        System.out.println("=== XML Export ===");
        for (Shape shape : shapes) {
            shape.accept(xmlVisitor);
        }
        System.out.println(xmlVisitor.getXML());
        
        // JSON Export
        JSONExportVisitor jsonVisitor = new JSONExportVisitor();
        System.out.println("=== JSON Export ===");
        for (Shape shape : shapes) {
            shape.accept(jsonVisitor);
        }
        System.out.println(jsonVisitor.getJSON());
        
        // Area Calculation
        AreaCalculatorVisitor areaVisitor = new AreaCalculatorVisitor();
        System.out.println("=== Area Calculation ===");
        for (Shape shape : shapes) {
            shape.accept(areaVisitor);
        }
        System.out.println("Total area: " + areaVisitor.getTotalArea());
    }
    
    public static void main(String[] args) {
        List<Shape> shapes = Arrays.asList(
            new Circle(5),
            new Rectangle(4, 6),
            new Triangle(3, 8),
            new Circle(2)
        );
        
        processShapes(shapes);
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Visitor (interface)
├── + visitConcreteElementA(ConcreteElementA): void
├── + visitConcreteElementB(ConcreteElementB): void
│
ConcreteVisitor1 implements Visitor
├── + visitConcreteElementA(ConcreteElementA): void
├── + visitConcreteElementB(ConcreteElementB): void
│
ConcreteVisitor2 implements Visitor
├── + visitConcreteElementA(ConcreteElementA): void
├── + visitConcreteElementB(ConcreteElementB): void
│
Element (interface)
├── + accept(Visitor): void
│
ConcreteElementA implements Element
├── + accept(Visitor): void
├── + operationA(): void
│
ConcreteElementB implements Element
├── + accept(Visitor): void
├── + operationB(): void
```

### Các thành phần chính

#### 1. Visitor Interface
- **Vai trò:** Declares visit methods cho each concrete element
- **Đặc điểm:** One method per element type

#### 2. Concrete Visitor
- **Vai trò:** Implements operations cho each element type
- **Đặc điểm:** Contains algorithm logic

#### 3. Element Interface
- **Vai trò:** Declares accept method
- **Đặc điểm:** Allows visitor to access element

#### 4. Concrete Element
- **Vai trò:** Implements accept method
- **Đặc điểm:** Calls appropriate visitor method (double dispatch)

---

## Cách triển khai

### Bước 1: Define Element hierarchy
```java
public interface FileSystemElement {
    void accept(FileSystemVisitor visitor);
    String getName();
    long getSize();
}

public class File implements FileSystemElement {
    private String name;
    private long size;
    private String extension;
    
    public File(String name, long size, String extension) {
        this.name = name;
        this.size = size;
        this.extension = extension;
    }
    
    @Override
    public void accept(FileSystemVisitor visitor) {
        visitor.visitFile(this);
    }
    
    public String getExtension() { return extension; }
    public String getName() { return name; }
    public long getSize() { return size; }
}

public class Directory implements FileSystemElement {
    private String name;
    private List<FileSystemElement> children = new ArrayList<>();
    
    public Directory(String name) {
        this.name = name;
    }
    
    public void add(FileSystemElement element) {
        children.add(element);
    }
    
    @Override
    public void accept(FileSystemVisitor visitor) {
        visitor.visitDirectory(this);
    }
    
    public List<FileSystemElement> getChildren() { return children; }
    public String getName() { return name; }
    
    public long getSize() {
        return children.stream().mapToLong(FileSystemElement::getSize).sum();
    }
}
```

### Bước 2: Define Visitor interface
```java
public interface FileSystemVisitor {
    void visitFile(File file);
    void visitDirectory(Directory directory);
}
```

### Bước 3: Implement Concrete Visitors
```java
public class SizeCalculatorVisitor implements FileSystemVisitor {
    private long totalSize = 0;
    
    @Override
    public void visitFile(File file) {
        totalSize += file.getSize();
    }
    
    @Override
    public void visitDirectory(Directory directory) {
        for (FileSystemElement child : directory.getChildren()) {
            child.accept(this);
        }
    }
    
    public long getTotalSize() { return totalSize; }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Open/Closed Principle
```java
// Can add new operations without modifying existing classes
public class NewOperationVisitor implements ShapeVisitor {
    // New operation - no existing code modified
}
```

#### 2. Single Responsibility Principle
```java
// Each visitor has single responsibility
public class XMLExportVisitor implements ShapeVisitor {
    // Only handles XML export logic
}

public class DatabaseSaveVisitor implements ShapeVisitor {
    // Only handles database operations
}
```

#### 3. Gather related operations
```java
// Related operations grouped in one visitor
public class StatisticsVisitor implements ShapeVisitor {
    private int circleCount = 0;
    private int rectangleCount = 0;
    private double totalArea = 0;
    
    // All statistics logic in one place
}
```

### ❌ Nhược điểm

#### 1. Adding new element types is hard
```java
// Adding new shape requires modifying ALL visitors
public interface ShapeVisitor {
    void visitCircle(Circle circle);
    void visitRectangle(Rectangle rectangle);
    void visitTriangle(Triangle triangle);
    void visitPentagon(Pentagon pentagon); // New method in ALL visitors!
}
```

#### 2. Breaking encapsulation
```java
// Visitors might need access to private data
public class Circle implements Shape {
    private double radius;
    
    // Need to expose private data for visitors
    public double getRadius() { return radius; }
}
```

#### 3. Circular dependencies
```java
// Visitor depends on concrete elements
// Elements depend on visitor interface
// Can create circular dependencies
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Need many unrelated operations on object structure
```java
// File system: compress, encrypt, backup, analyze, etc.
// AST: compile, optimize, format, analyze, etc.
// Document: export, print, validate, transform, etc.
```

#### 2. Object structure rarely changes
```java
// Stable hierarchy with frequent new operations
// Mathematical expressions, syntax trees, etc.
```

#### 3. Operations don't belong to element classes
```java
// Export operations don't belong in domain objects
// UI operations don't belong in business objects
```

### ❌ Không nên sử dụng khi:

#### 1. Object structure changes frequently
```java
// Adding new element types is expensive
// All visitors need updates
```

#### 2. Simple operations
```java
// Overkill for simple operations
// Direct method calls might be simpler
```

---

## Ví dụ thực tế

### Ví dụ 1: Compiler AST Processing

```java
// AST Node hierarchy
public interface ASTNode {
    void accept(ASTVisitor visitor);
}

// Expression nodes
public class NumberLiteral implements ASTNode {
    private double value;
    
    public NumberLiteral(double value) {
        this.value = value;
    }
    
    @Override
    public void accept(ASTVisitor visitor) {
        visitor.visitNumberLiteral(this);
    }
    
    public double getValue() { return value; }
}

public class BinaryOperation implements ASTNode {
    private ASTNode left;
    private String operator;
    private ASTNode right;
    
    public BinaryOperation(ASTNode left, String operator, ASTNode right) {
        this.left = left;
        this.operator = operator;
        this.right = right;
    }
    
    @Override
    public void accept(ASTVisitor visitor) {
        visitor.visitBinaryOperation(this);
    }
    
    public ASTNode getLeft() { return left; }
    public String getOperator() { return operator; }
    public ASTNode getRight() { return right; }
}

public class VariableReference implements ASTNode {
    private String name;
    
    public VariableReference(String name) {
        this.name = name;
    }
    
    @Override
    public void accept(ASTVisitor visitor) {
        visitor.visitVariableReference(this);
    }
    
    public String getName() { return name; }
}

public class FunctionCall implements ASTNode {
    private String functionName;
    private List<ASTNode> arguments;
    
    public FunctionCall(String functionName, List<ASTNode> arguments) {
        this.functionName = functionName;
        this.arguments = arguments;
    }
    
    @Override
    public void accept(ASTVisitor visitor) {
        visitor.visitFunctionCall(this);
    }
    
    public String getFunctionName() { return functionName; }
    public List<ASTNode> getArguments() { return arguments; }
}

// Visitor interface
public interface ASTVisitor {
    void visitNumberLiteral(NumberLiteral node);
    void visitBinaryOperation(BinaryOperation node);
    void visitVariableReference(VariableReference node);
    void visitFunctionCall(FunctionCall node);
}

// Code generation visitor
public class CodeGeneratorVisitor implements ASTVisitor {
    private StringBuilder code = new StringBuilder();
    
    @Override
    public void visitNumberLiteral(NumberLiteral node) {
        code.append(node.getValue());
    }
    
    @Override
    public void visitBinaryOperation(BinaryOperation node) {
        code.append("(");
        node.getLeft().accept(this);
        code.append(" ").append(node.getOperator()).append(" ");
        node.getRight().accept(this);
        code.append(")");
    }
    
    @Override
    public void visitVariableReference(VariableReference node) {
        code.append(node.getName());
    }
    
    @Override
    public void visitFunctionCall(FunctionCall node) {
        code.append(node.getFunctionName()).append("(");
        for (int i = 0; i < node.getArguments().size(); i++) {
            if (i > 0) code.append(", ");
            node.getArguments().get(i).accept(this);
        }
        code.append(")");
    }
    
    public String getGeneratedCode() {
        return code.toString();
    }
}

// Evaluation visitor
public class EvaluatorVisitor implements ASTVisitor {
    private Stack<Double> stack = new Stack<>();
    private Map<String, Double> variables = new HashMap<>();
    
    public EvaluatorVisitor() {
        // Initialize some variables
        variables.put("x", 10.0);
        variables.put("y", 5.0);
        variables.put("pi", Math.PI);
    }
    
    @Override
    public void visitNumberLiteral(NumberLiteral node) {
        stack.push(node.getValue());
    }
    
    @Override
    public void visitBinaryOperation(BinaryOperation node) {
        node.getLeft().accept(this);
        node.getRight().accept(this);
        
        double right = stack.pop();
        double left = stack.pop();
        double result;
        
        switch (node.getOperator()) {
            case "+": result = left + right; break;
            case "-": result = left - right; break;
            case "*": result = left * right; break;
            case "/": result = left / right; break;
            case "^": result = Math.pow(left, right); break;
            default: throw new IllegalArgumentException("Unknown operator: " + node.getOperator());
        }
        
        stack.push(result);
    }
    
    @Override
    public void visitVariableReference(VariableReference node) {
        Double value = variables.get(node.getName());
        if (value == null) {
            throw new IllegalArgumentException("Undefined variable: " + node.getName());
        }
        stack.push(value);
    }
    
    @Override
    public void visitFunctionCall(FunctionCall node) {
        // Evaluate arguments
        List<Double> args = new ArrayList<>();
        for (ASTNode arg : node.getArguments()) {
            arg.accept(this);
            args.add(stack.pop());
        }
        
        double result;
        switch (node.getFunctionName()) {
            case "sin":
                result = Math.sin(args.get(0));
                break;
            case "cos":
                result = Math.cos(args.get(0));
                break;
            case "sqrt":
                result = Math.sqrt(args.get(0));
                break;
            case "max":
                result = Math.max(args.get(0), args.get(1));
                break;
            default:
                throw new IllegalArgumentException("Unknown function: " + node.getFunctionName());
        }
        
        stack.push(result);
    }
    
    public double getResult() {
        if (stack.size() != 1) {
            throw new IllegalStateException("Invalid expression evaluation");
        }
        return stack.pop();
    }
}

// Pretty printer visitor
public class PrettyPrinterVisitor implements ASTVisitor {
    private StringBuilder output = new StringBuilder();
    private int indentLevel = 0;
    
    @Override
    public void visitNumberLiteral(NumberLiteral node) {
        output.append("Number(").append(node.getValue()).append(")");
    }
    
    @Override
    public void visitBinaryOperation(BinaryOperation node) {
        output.append("BinaryOp(").append(node.getOperator()).append(")\n");
        
        indent();
        output.append("├─ Left: ");
        indentLevel++;
        node.getLeft().accept(this);
        indentLevel--;
        
        output.append("\n");
        indent();
        output.append("└─ Right: ");
        indentLevel++;
        node.getRight().accept(this);
        indentLevel--;
    }
    
    @Override
    public void visitVariableReference(VariableReference node) {
        output.append("Variable(").append(node.getName()).append(")");
    }
    
    @Override
    public void visitFunctionCall(FunctionCall node) {
        output.append("Function(").append(node.getFunctionName()).append(")\n");
        
        for (int i = 0; i < node.getArguments().size(); i++) {
            indent();
            boolean isLast = (i == node.getArguments().size() - 1);
            output.append(isLast ? "└─ " : "├─ ").append("Arg").append(i + 1).append(": ");
            
            indentLevel++;
            node.getArguments().get(i).accept(this);
            indentLevel--;
            
            if (!isLast) output.append("\n");
        }
    }
    
    private void indent() {
        for (int i = 0; i < indentLevel; i++) {
            output.append("  ");
        }
    }
    
    public String getPrettyPrint() {
        return output.toString();
    }
}

// AST Builder utility
public class ASTBuilder {
    public static ASTNode buildSampleExpression() {
        // Build AST for: (x + 2) * sin(pi / 4)
        return new BinaryOperation(
            new BinaryOperation(
                new VariableReference("x"),
                "+",
                new NumberLiteral(2)
            ),
            "*",
            new FunctionCall("sin", Arrays.asList(
                new BinaryOperation(
                    new VariableReference("pi"),
                    "/",
                    new NumberLiteral(4)
                )
            ))
        );
    }
    
    public static ASTNode buildComplexExpression() {
        // Build AST for: max(sqrt(x^2 + y^2), 10)
        return new FunctionCall("max", Arrays.asList(
            new FunctionCall("sqrt", Arrays.asList(
                new BinaryOperation(
                    new BinaryOperation(
                        new VariableReference("x"),
                        "^",
                        new NumberLiteral(2)
                    ),
                    "+",
                    new BinaryOperation(
                        new VariableReference("y"),
                        "^",
                        new NumberLiteral(2)
                    )
                )
            )),
            new NumberLiteral(10)
        ));
    }
}

// Usage
public class CompilerDemo {
    public static void main(String[] args) {
        System.out.println("=== Compiler AST Processing Demo ===");
        
        // Build sample expressions
        ASTNode expr1 = ASTBuilder.buildSampleExpression();
        ASTNode expr2 = ASTBuilder.buildComplexExpression();
        
        processExpression("Expression 1: (x + 2) * sin(pi / 4)", expr1);
        System.out.println();
        processExpression("Expression 2: max(sqrt(x^2 + y^2), 10)", expr2);
        
        System.out.println("\nVisitor Pattern Benefits:");
        System.out.println("- Easy to add new operations (visitors)");
        System.out.println("- Operations separated from AST structure");
        System.out.println("- Can traverse complex object structures");
        System.out.println("- Type-safe double dispatch");
    }
    
    private static void processExpression(String description, ASTNode expression) {
        System.out.println("=== " + description + " ===");
        
        // Pretty print AST structure
        PrettyPrinterVisitor printer = new PrettyPrinterVisitor();
        expression.accept(printer);
        System.out.println("AST Structure:");
        System.out.println(printer.getPrettyPrint());
        
        // Generate code
        CodeGeneratorVisitor codeGen = new CodeGeneratorVisitor();
        expression.accept(codeGen);
        System.out.println("Generated Code: " + codeGen.getGeneratedCode());
        
        // Evaluate expression
        try {
            EvaluatorVisitor evaluator = new EvaluatorVisitor();
            expression.accept(evaluator);
            System.out.println("Evaluation Result: " + evaluator.getResult());
        } catch (Exception e) {
            System.out.println("Evaluation Error: " + e.getMessage());
        }
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Visitor Pattern** separates algorithms từ object structure
2. **Double dispatch** mechanism cho type-safe operations
3. **Easy to add new operations** mà không modify existing classes
4. **Follows Open/Closed Principle** cho operations

### So sánh với patterns khác
| Pattern | Purpose | Flexibility |
|---------|---------|-------------|
| **Visitor** | Add operations to object structure | Easy new operations, hard new types |
| **Strategy** | Encapsulate algorithms | Easy algorithm changes |
| **Command** | Encapsulate requests | Easy new commands |
| **Iterator** | Traverse collections | Easy traversal algorithms |

### Best Practices
- **Use when object structure is stable** nhưng operations change frequently
- **Consider performance** của double dispatch
- **Document visitor contracts** clearly
- **Handle null cases** appropriately
- **Consider using** abstract visitor base class

---

**Tiếp theo:** [Interpreter](interpreter.md) - Xử lý ngôn ngữ và expressions
