# Strategy Pattern

> **Behavioral Pattern** - <PERSON><PERSON><PERSON> nghĩa một họ thuật toán, đ<PERSON>g gói từng thuật toán và làm chúng có thể hoán đổi

## 📋 <PERSON><PERSON><PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [Gi<PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [C<PERSON>u trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON><PERSON><PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Strategy là một **behavioral design pattern** cho phép **đ<PERSON>nh nghĩa một họ thuật toán, <PERSON><PERSON><PERSON> g<PERSON>i từng thuật toán và làm chúng có thể hoán đổi**. Strategy cho phép thuật toán thay đổi độc lập với clients sử dụng nó.

### Mục đích chính
- **Encapsulate algorithms** thành separate classes
- **Make algorithms interchangeable** at runtime
- **Eliminate conditional statements** cho algorithm selection

### Tên gọi khác
- **Policy Pattern**
- **Algorithm Pattern**

### Ví dụ thực tế
Giống như **phương tiện di chuyển**: bạn có thể đến sân bay bằng bus, taxi, hoặc xe riêng. Mỗi phương tiện là một strategy khác nhau để đạt cùng một mục tiêu.

---

## Vấn đề

### Tình huống thực tế
Bạn đang xây dựng navigation app. Ban đầu chỉ hỗ trợ tìm đường cho ô tô:

```java
public class Navigator {
    public Route buildRoute(Point start, Point end) {
        // Car routing algorithm
        return findFastestCarRoute(start, end);
    }
}
```

Sau đó bạn muốn thêm walking và public transport:

### Vấn đề phát sinh

#### 1. Conditional complexity
```java
public class Navigator {
    public Route buildRoute(Point start, Point end, String transportType) {
        if (transportType.equals("car")) {
            return findFastestCarRoute(start, end);
        } else if (transportType.equals("walking")) {
            return findShortestWalkingRoute(start, end);
        } else if (transportType.equals("public_transport")) {
            return findCheapestPublicTransportRoute(start, end);
        } else if (transportType.equals("bicycle")) {
            return findSafestBicycleRoute(start, end);
        }
        // More conditions as you add transport types...
        throw new IllegalArgumentException("Unknown transport type");
    }
}
```

#### 2. Massive class
```java
public class Navigator {
    // Car routing methods
    private Route findFastestCarRoute(Point start, Point end) { ... }
    private Route findShortestCarRoute(Point start, Point end) { ... }
    private boolean isHighwayAvailable(Point point) { ... }
    
    // Walking routing methods  
    private Route findShortestWalkingRoute(Point start, Point end) { ... }
    private Route findScenicWalkingRoute(Point start, Point end) { ... }
    private boolean isSidewalkAvailable(Point point) { ... }
    
    // Public transport methods
    private Route findCheapestPublicTransportRoute(Point start, Point end) { ... }
    private Route findFastestPublicTransportRoute(Point start, Point end) { ... }
    private List<BusStop> findNearbyBusStops(Point point) { ... }
    
    // Bicycle methods
    private Route findSafestBicycleRoute(Point start, Point end) { ... }
    private boolean isBikeLaneAvailable(Point point) { ... }
    
    // Class becomes huge and hard to maintain!
}
```

#### 3. Khó testing và maintain
```java
// Khó test individual algorithms
public void testCarRouting() {
    Navigator navigator = new Navigator();
    // Phải test through the big buildRoute method
    Route route = navigator.buildRoute(start, end, "car");
    // Hard to isolate car routing logic
}

// Thay đổi car algorithm ảnh hưởng đến toàn bộ class
```

#### 4. Vi phạm Open/Closed Principle
```java
// Thêm transport type mới yêu cầu modify Navigator class
public Route buildRoute(Point start, Point end, String transportType) {
    // Phải thêm new condition vào existing method
    if (transportType.equals("drone")) { // New addition
        return findDroneRoute(start, end);
    }
    // Existing code...
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Strategy pattern đề xuất **trích xuất các thuật toán thành separate classes** gọi là strategies. Context class gốc phải có field để store reference đến một trong các strategies và delegate work cho nó.

### Cách hoạt động

#### 1. Strategy interface
```java
public interface RouteStrategy {
    Route buildRoute(Point start, Point end);
}
```

#### 2. Concrete Strategies
```java
public class CarRouteStrategy implements RouteStrategy {
    @Override
    public Route buildRoute(Point start, Point end) {
        System.out.println("Building fastest car route");
        // Car-specific routing algorithm
        return new Route("Car route from " + start + " to " + end);
    }
}

public class WalkingRouteStrategy implements RouteStrategy {
    @Override
    public Route buildRoute(Point start, Point end) {
        System.out.println("Building shortest walking route");
        // Walking-specific routing algorithm
        return new Route("Walking route from " + start + " to " + end);
    }
}

public class PublicTransportStrategy implements RouteStrategy {
    @Override
    public Route buildRoute(Point start, Point end) {
        System.out.println("Building cheapest public transport route");
        // Public transport routing algorithm
        return new Route("Public transport route from " + start + " to " + end);
    }
}
```

#### 3. Context class
```java
public class Navigator {
    private RouteStrategy strategy;
    
    public Navigator(RouteStrategy strategy) {
        this.strategy = strategy;
    }
    
    public void setStrategy(RouteStrategy strategy) {
        this.strategy = strategy;
    }
    
    public Route buildRoute(Point start, Point end) {
        return strategy.buildRoute(start, end); // Delegate to strategy
    }
}
```

#### 4. Client usage
```java
public class StrategyDemo {
    public static void main(String[] args) {
        Point start = new Point(0, 0);
        Point end = new Point(10, 10);
        
        // Create navigator with car strategy
        Navigator navigator = new Navigator(new CarRouteStrategy());
        Route carRoute = navigator.buildRoute(start, end);
        
        // Switch to walking strategy at runtime
        navigator.setStrategy(new WalkingRouteStrategy());
        Route walkingRoute = navigator.buildRoute(start, end);
        
        // Switch to public transport
        navigator.setStrategy(new PublicTransportStrategy());
        Route publicRoute = navigator.buildRoute(start, end);
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Context
├── - strategy: Strategy
├── + setStrategy(Strategy): void
├── + executeStrategy(): void
│
Strategy (interface)
├── + execute(): void
│
ConcreteStrategyA implements Strategy
├── + execute(): void
│
ConcreteStrategyB implements Strategy
├── + execute(): void
│
ConcreteStrategyC implements Strategy
├── + execute(): void
```

### Các thành phần chính

#### 1. Strategy Interface
- **Vai trò:** Khai báo method chung cho tất cả concrete strategies
- **Đặc điểm:** Defines contract cho algorithms

#### 2. Concrete Strategy
- **Vai trò:** Implements specific algorithm using Strategy interface
- **Đặc điểm:** Each strategy implements different approach

#### 3. Context
- **Vai trò:** Maintains reference to Strategy object
- **Đặc điểm:** Delegates work to strategy và có thể pass data

#### 4. Client
- **Vai trò:** Creates specific strategy object và passes it to context
- **Đặc điểm:** Decides which strategy to use

---

## Cách triển khai

### Bước 1: Định nghĩa Strategy interface
```java
public interface PaymentStrategy {
    boolean pay(double amount);
    String getPaymentDetails();
}
```

### Bước 2: Implement Concrete Strategies
```java
public class CreditCardPayment implements PaymentStrategy {
    private String cardNumber;
    private String expiryDate;
    private String cvv;
    
    public CreditCardPayment(String cardNumber, String expiryDate, String cvv) {
        this.cardNumber = cardNumber;
        this.expiryDate = expiryDate;
        this.cvv = cvv;
    }
    
    @Override
    public boolean pay(double amount) {
        System.out.println("Processing credit card payment of $" + amount);
        // Credit card processing logic
        return validateCard() && processPayment(amount);
    }
    
    @Override
    public String getPaymentDetails() {
        return "Credit Card ending in " + cardNumber.substring(cardNumber.length() - 4);
    }
    
    private boolean validateCard() {
        // Validation logic
        return cardNumber.length() == 16 && cvv.length() == 3;
    }
    
    private boolean processPayment(double amount) {
        // Payment processing
        return amount > 0;
    }
}

public class PayPalPayment implements PaymentStrategy {
    private String email;
    private String password;
    
    public PayPalPayment(String email, String password) {
        this.email = email;
        this.password = password;
    }
    
    @Override
    public boolean pay(double amount) {
        System.out.println("Processing PayPal payment of $" + amount);
        return authenticate() && processPayment(amount);
    }
    
    @Override
    public String getPaymentDetails() {
        return "PayPal account: " + email;
    }
    
    private boolean authenticate() {
        // PayPal authentication
        return email.contains("@") && password.length() >= 6;
    }
    
    private boolean processPayment(double amount) {
        // PayPal payment processing
        return amount > 0;
    }
}

public class BitcoinPayment implements PaymentStrategy {
    private String walletAddress;
    
    public BitcoinPayment(String walletAddress) {
        this.walletAddress = walletAddress;
    }
    
    @Override
    public boolean pay(double amount) {
        System.out.println("Processing Bitcoin payment of $" + amount);
        return validateWallet() && processPayment(amount);
    }
    
    @Override
    public String getPaymentDetails() {
        return "Bitcoin wallet: " + walletAddress.substring(0, 8) + "...";
    }
    
    private boolean validateWallet() {
        return walletAddress.length() >= 26;
    }
    
    private boolean processPayment(double amount) {
        // Bitcoin payment processing
        return amount > 0;
    }
}
```

### Bước 3: Context class
```java
public class ShoppingCart {
    private List<Item> items = new ArrayList<>();
    private PaymentStrategy paymentStrategy;
    
    public void addItem(Item item) {
        items.add(item);
    }
    
    public void removeItem(Item item) {
        items.remove(item);
    }
    
    public double calculateTotal() {
        return items.stream().mapToDouble(Item::getPrice).sum();
    }
    
    public void setPaymentStrategy(PaymentStrategy paymentStrategy) {
        this.paymentStrategy = paymentStrategy;
    }
    
    public boolean checkout() {
        if (paymentStrategy == null) {
            System.out.println("Please select a payment method");
            return false;
        }
        
        double total = calculateTotal();
        System.out.println("Total amount: $" + total);
        System.out.println("Payment method: " + paymentStrategy.getPaymentDetails());
        
        boolean success = paymentStrategy.pay(total);
        
        if (success) {
            System.out.println("Payment successful! Order confirmed.");
            items.clear(); // Clear cart after successful payment
        } else {
            System.out.println("Payment failed! Please try again.");
        }
        
        return success;
    }
}
```

### Bước 4: Usage
```java
public class PaymentDemo {
    public static void main(String[] args) {
        ShoppingCart cart = new ShoppingCart();
        
        // Add items to cart
        cart.addItem(new Item("Laptop", 999.99));
        cart.addItem(new Item("Mouse", 29.99));
        cart.addItem(new Item("Keyboard", 79.99));
        
        // Try different payment strategies
        
        // Credit Card Payment
        cart.setPaymentStrategy(new CreditCardPayment("1234567890123456", "12/25", "123"));
        cart.checkout();
        
        System.out.println("---");
        
        // Add more items
        cart.addItem(new Item("Monitor", 299.99));
        cart.addItem(new Item("Webcam", 89.99));
        
        // PayPal Payment
        cart.setPaymentStrategy(new PayPalPayment("<EMAIL>", "password123"));
        cart.checkout();
        
        System.out.println("---");
        
        // Add more items
        cart.addItem(new Item("Headphones", 199.99));
        
        // Bitcoin Payment
        cart.setPaymentStrategy(new BitcoinPayment("**********************************"));
        cart.checkout();
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Runtime algorithm switching
```java
// Có thể thay đổi algorithm trong runtime
SortingContext sorter = new SortingContext();

if (data.length < 100) {
    sorter.setStrategy(new InsertionSort()); // Fast for small data
} else {
    sorter.setStrategy(new QuickSort());     // Better for large data
}

sorter.sort(data);
```

#### 2. Tuân thủ Open/Closed Principle
```java
// Thêm strategy mới mà không sửa existing code
public class ApplePayPayment implements PaymentStrategy {
    @Override
    public boolean pay(double amount) {
        // Apple Pay implementation
        return true;
    }
}

// Existing code không cần thay đổi
```

#### 3. Isolate implementation details
```java
// Mỗi strategy encapsulate algorithm details
public class ComplexSortStrategy implements SortStrategy {
    @Override
    public void sort(int[] array) {
        // Complex algorithm hidden from client
        hybridMergeQuickSort(array);
    }
    
    private void hybridMergeQuickSort(int[] array) {
        // Complex implementation details
    }
}
```

#### 4. Eliminate conditional statements
```java
// Before Strategy
public void processPayment(String type, double amount) {
    if (type.equals("credit")) {
        // Credit card logic
    } else if (type.equals("paypal")) {
        // PayPal logic
    } else if (type.equals("bitcoin")) {
        // Bitcoin logic
    }
}

// After Strategy
public void processPayment(PaymentStrategy strategy, double amount) {
    strategy.pay(amount); // Clean, no conditionals
}
```

### ❌ Nhược điểm

#### 1. Increased number of classes
```java
// Mỗi algorithm cần separate class
public class BubbleSort implements SortStrategy { ... }
public class QuickSort implements SortStrategy { ... }
public class MergeSort implements SortStrategy { ... }
public class HeapSort implements SortStrategy { ... }
// Many classes for simple algorithms
```

#### 2. Client must know strategies
```java
// Client phải biết available strategies
public class PaymentProcessor {
    public void setupPayment() {
        // Client needs to know about all payment options
        if (userPreference.equals("fast")) {
            return new CreditCardPayment(...);
        } else if (userPreference.equals("secure")) {
            return new BitcoinPayment(...);
        }
        // Client has strategy selection logic
    }
}
```

#### 3. Communication overhead
```java
// Strategy có thể cần nhiều data từ context
public interface CompressionStrategy {
    byte[] compress(byte[] data, CompressionSettings settings, 
                   ProgressCallback callback, ErrorHandler handler);
}
// Complex parameter passing
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Có nhiều ways để perform same task
```java
// Different sorting algorithms
public interface SortStrategy {
    void sort(int[] array);
}

// BubbleSort for small arrays, QuickSort for large arrays
```

#### 2. Muốn switch algorithms at runtime
```java
// Game AI difficulty
public class GameAI {
    private AIStrategy strategy;
    
    public void setDifficulty(String level) {
        switch (level) {
            case "easy": strategy = new EasyAI(); break;
            case "hard": strategy = new HardAI(); break;
            case "expert": strategy = new ExpertAI(); break;
        }
    }
}
```

#### 3. Có conditional statements cho algorithm selection
```java
// Replace this with Strategy pattern
public void processData(String algorithm, Data data) {
    if (algorithm.equals("fast")) {
        // Fast processing
    } else if (algorithm.equals("accurate")) {
        // Accurate processing
    } else if (algorithm.equals("memory_efficient")) {
        // Memory efficient processing
    }
}
```

#### 4. Want to hide implementation details
```java
// Different encryption algorithms
public interface EncryptionStrategy {
    String encrypt(String data);
    String decrypt(String data);
}

// Client doesn't need to know encryption details
```

### ❌ Không nên sử dụng khi:

#### 1. Chỉ có một algorithm
```java
// Không cần Strategy cho single algorithm
public class Calculator {
    public int add(int a, int b) {
        return a + b; // Only one way to add
    }
}
```

#### 2. Algorithms hiếm khi thay đổi
```java
// Stable algorithm, không cần flexibility
public class TaxCalculator {
    public double calculateTax(double income) {
        return income * 0.25; // Fixed tax rate
    }
}
```

#### 3. Simple conditional logic
```java
// Simple if-else đơn giản hơn Strategy
public String getGreeting(boolean isMorning) {
    return isMorning ? "Good morning" : "Good evening";
}
```

---

## Ví dụ thực tế

### Ví dụ 1: Image Compression System

```java
// Strategy interface
public interface CompressionStrategy {
    byte[] compress(byte[] data);
    byte[] decompress(byte[] compressedData);
    String getCompressionInfo();
}

// Concrete strategies
public class JPEGCompression implements CompressionStrategy {
    private int quality;
    
    public JPEGCompression(int quality) {
        this.quality = quality;
    }
    
    @Override
    public byte[] compress(byte[] data) {
        System.out.println("Compressing image using JPEG with quality " + quality);
        // JPEG compression algorithm
        return simulateCompression(data, 0.7); // 70% compression
    }
    
    @Override
    public byte[] decompress(byte[] compressedData) {
        System.out.println("Decompressing JPEG image");
        return simulateDecompression(compressedData);
    }
    
    @Override
    public String getCompressionInfo() {
        return "JPEG compression (Quality: " + quality + ")";
    }
    
    private byte[] simulateCompression(byte[] data, double ratio) {
        int newSize = (int) (data.length * ratio);
        byte[] compressed = new byte[newSize];
        System.arraycopy(data, 0, compressed, 0, Math.min(data.length, newSize));
        return compressed;
    }
    
    private byte[] simulateDecompression(byte[] data) {
        // Simulate decompression
        return data;
    }
}

public class PNGCompression implements CompressionStrategy {
    private boolean lossless;
    
    public PNGCompression(boolean lossless) {
        this.lossless = lossless;
    }
    
    @Override
    public byte[] compress(byte[] data) {
        System.out.println("Compressing image using PNG (Lossless: " + lossless + ")");
        // PNG compression algorithm
        return simulateCompression(data, lossless ? 0.9 : 0.6);
    }
    
    @Override
    public byte[] decompress(byte[] compressedData) {
        System.out.println("Decompressing PNG image");
        return compressedData; // PNG decompression
    }
    
    @Override
    public String getCompressionInfo() {
        return "PNG compression (Lossless: " + lossless + ")";
    }
    
    private byte[] simulateCompression(byte[] data, double ratio) {
        int newSize = (int) (data.length * ratio);
        byte[] compressed = new byte[newSize];
        System.arraycopy(data, 0, compressed, 0, Math.min(data.length, newSize));
        return compressed;
    }
}

public class WebPCompression implements CompressionStrategy {
    @Override
    public byte[] compress(byte[] data) {
        System.out.println("Compressing image using WebP");
        // WebP compression algorithm
        return simulateCompression(data, 0.5); // Better compression
    }
    
    @Override
    public byte[] decompress(byte[] compressedData) {
        System.out.println("Decompressing WebP image");
        return compressedData;
    }
    
    @Override
    public String getCompressionInfo() {
        return "WebP compression (Modern format)";
    }
    
    private byte[] simulateCompression(byte[] data, double ratio) {
        int newSize = (int) (data.length * ratio);
        byte[] compressed = new byte[newSize];
        System.arraycopy(data, 0, compressed, 0, Math.min(data.length, newSize));
        return compressed;
    }
}

// Context
public class ImageProcessor {
    private CompressionStrategy compressionStrategy;
    
    public ImageProcessor(CompressionStrategy compressionStrategy) {
        this.compressionStrategy = compressionStrategy;
    }
    
    public void setCompressionStrategy(CompressionStrategy compressionStrategy) {
        this.compressionStrategy = compressionStrategy;
    }
    
    public byte[] processImage(byte[] imageData) {
        System.out.println("Original image size: " + imageData.length + " bytes");
        System.out.println("Using: " + compressionStrategy.getCompressionInfo());
        
        byte[] compressed = compressionStrategy.compress(imageData);
        System.out.println("Compressed size: " + compressed.length + " bytes");
        
        double compressionRatio = (1.0 - (double) compressed.length / imageData.length) * 100;
        System.out.println("Compression ratio: " + String.format("%.1f", compressionRatio) + "%");
        
        return compressed;
    }
    
    public byte[] restoreImage(byte[] compressedData) {
        return compressionStrategy.decompress(compressedData);
    }
}

// Usage
public class ImageCompressionDemo {
    public static void main(String[] args) {
        // Simulate image data
        byte[] originalImage = new byte[1000000]; // 1MB image
        
        ImageProcessor processor = new ImageProcessor(new JPEGCompression(80));
        
        // Process with JPEG
        byte[] jpegCompressed = processor.processImage(originalImage);
        
        System.out.println("\n---\n");
        
        // Switch to PNG for better quality
        processor.setCompressionStrategy(new PNGCompression(true));
        byte[] pngCompressed = processor.processImage(originalImage);
        
        System.out.println("\n---\n");
        
        // Switch to WebP for web optimization
        processor.setCompressionStrategy(new WebPCompression());
        byte[] webpCompressed = processor.processImage(originalImage);
        
        System.out.println("\n--- Comparison ---");
        System.out.println("JPEG size: " + jpegCompressed.length + " bytes");
        System.out.println("PNG size: " + pngCompressed.length + " bytes");
        System.out.println("WebP size: " + webpCompressed.length + " bytes");
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Strategy Pattern** encapsulates algorithms và makes them interchangeable
2. **Eliminates conditional statements** cho algorithm selection
3. **Supports runtime algorithm switching**
4. **Promotes Open/Closed Principle**

### So sánh với patterns khác
| Pattern | Purpose | Structure |
|---------|---------|-----------|
| **Strategy** | Encapsulate algorithms | Context → Strategy |
| **State** | Change behavior based on state | Context → State |
| **Command** | Encapsulate requests | Invoker → Command |
| **Template Method** | Define algorithm skeleton | AbstractClass → ConcreteClass |

### Best Practices
- **Use when you have multiple algorithms** cho same task
- **Consider performance overhead** của strategy switching
- **Provide factory methods** để create strategies
- **Document when to use each strategy**
- **Consider combining with Factory pattern** cho strategy creation

---

**Tiếp theo:** [Command](command.md) - Đóng gói yêu cầu thành đối tượng
