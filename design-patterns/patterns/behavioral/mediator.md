# Mediator Pattern

> **Behavioral Pattern** - <PERSON><PERSON><PERSON> nghĩa cách một tập hợp objects tương tác với nhau thông qua một mediator object

## 📋 <PERSON>ục lục

1. [Tổng quan](#tổng-quan)
2. [V<PERSON>n đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#giải-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu như<PERSON>c điểm](#ưu-nh<PERSON>ợ<PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Mediator là một **behavioral design pattern** **định nghĩa how a set of objects interact với nhau**. Mediator promotes loose coupling bằng cách **prevent objects from referring to each other explicitly**, và cho phép vary interaction independently.

### Mục đích chính
- **Reduce coupling** giữa communicating objects
- **Centralize complex communications** và control logic
- **Promote reusability** của individual components
- **Make interactions easier to understand** và maintain

### Tên gọi khác
- **Controller Pattern**
- **Intermediary Pattern**

### Ví dụ thực tế
Giống như **air traffic control tower**: máy bay không giao tiếp trực tiếp với nhau mà thông qua tower. Tower điều phối tất cả communications và đảm bảo an toàn.

---

## Vấn đề

### Tình huống thực tế
Bạn đang phát triển dialog box với nhiều UI components:

```java
public class LoginDialog {
    private TextField usernameField;
    private TextField passwordField;
    private CheckBox rememberMeCheckbox;
    private Button loginButton;
    private Button cancelButton;
    private Label statusLabel;
    
    public LoginDialog() {
        // Initialize components
        usernameField = new TextField();
        passwordField = new TextField();
        rememberMeCheckbox = new CheckBox("Remember me");
        loginButton = new Button("Login");
        cancelButton = new Button("Cancel");
        statusLabel = new Label("");
        
        // Setup interactions - PROBLEM STARTS HERE
        setupInteractions();
    }
    
    private void setupInteractions() {
        // Username field interactions
        usernameField.addTextChangeListener(() -> {
            validateForm();
            if (usernameField.getText().isEmpty()) {
                statusLabel.setText("Username required");
            }
        });
        
        // Password field interactions
        passwordField.addTextChangeListener(() -> {
            validateForm();
            if (passwordField.getText().length() < 6) {
                statusLabel.setText("Password too short");
            }
        });
        
        // Login button interactions
        loginButton.addClickListener(() -> {
            if (validateForm()) {
                performLogin();
            }
        });
        
        // Remember me checkbox interactions
        rememberMeCheckbox.addChangeListener(() -> {
            if (rememberMeCheckbox.isChecked()) {
                statusLabel.setText("Login will be remembered");
            }
        });
    }
}
```

### Vấn đề phát sinh

#### 1. Tight coupling between components
```java
// Each component knows about many others
public class TextField {
    private Button loginButton;
    private Label statusLabel;
    private CheckBox rememberMeCheckbox;
    
    public void onTextChange() {
        // Directly manipulates other components
        if (getText().isEmpty()) {
            loginButton.setEnabled(false);
            statusLabel.setText("Field required");
        }
        
        if (rememberMeCheckbox.isChecked()) {
            // More complex logic involving multiple components
        }
    }
}
```

#### 2. Complex interaction web
```java
// Components have many-to-many relationships
public class LoginButton {
    public void onClick() {
        // Needs to check multiple components
        if (usernameField.getText().isEmpty()) { ... }
        if (passwordField.getText().length() < 6) { ... }
        if (!rememberMeCheckbox.isChecked()) { ... }
        
        // Update multiple components
        statusLabel.setText("Logging in...");
        usernameField.setEnabled(false);
        passwordField.setEnabled(false);
        // Complex web of dependencies!
    }
}
```

#### 3. Difficult to reuse components
```java
// Cannot reuse TextField in other dialogs
// It's tightly coupled to specific Button and Label
public class RegistrationDialog {
    private TextField emailField; // Cannot reuse LoginDialog's TextField
    // Because it has hardcoded dependencies
}
```

#### 4. Hard to modify interactions
```java
// Adding new component requires modifying many existing components
public void addConfirmPasswordField() {
    // Need to modify:
    // - passwordField (to compare with confirm field)
    // - loginButton (to validate both passwords)
    // - statusLabel (to show password mismatch)
    // - All other components that interact with password
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Mediator pattern đề xuất **stop direct communication** giữa components mà thay vào đó **communicate indirectly bằng cách calling special mediator object** redirects calls đến appropriate components.

### Cách hoạt động

#### 1. Mediator interface
```java
public interface DialogMediator {
    void notify(Component sender, String event);
}
```

#### 2. Component base class
```java
public abstract class Component {
    protected DialogMediator mediator;
    
    public Component(DialogMediator mediator) {
        this.mediator = mediator;
    }
    
    protected void notifyMediator(String event) {
        if (mediator != null) {
            mediator.notify(this, event);
        }
    }
}
```

#### 3. Concrete Components
```java
public class TextField extends Component {
    private String text = "";
    private boolean enabled = true;
    
    public TextField(DialogMediator mediator) {
        super(mediator);
    }
    
    public void setText(String text) {
        this.text = text;
        notifyMediator("textChanged");
    }
    
    public String getText() {
        return text;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        System.out.println("TextField enabled: " + enabled);
    }
    
    public boolean isEnabled() {
        return enabled;
    }
}

public class Button extends Component {
    private String label;
    private boolean enabled = true;
    
    public Button(DialogMediator mediator, String label) {
        super(mediator);
        this.label = label;
    }
    
    public void click() {
        if (enabled) {
            notifyMediator("clicked");
        }
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        System.out.println("Button '" + label + "' enabled: " + enabled);
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public String getLabel() {
        return label;
    }
}

public class CheckBox extends Component {
    private String label;
    private boolean checked = false;
    
    public CheckBox(DialogMediator mediator, String label) {
        super(mediator);
        this.label = label;
    }
    
    public void toggle() {
        checked = !checked;
        notifyMediator("toggled");
    }
    
    public boolean isChecked() {
        return checked;
    }
    
    public String getLabel() {
        return label;
    }
}

public class Label extends Component {
    private String text = "";
    
    public Label(DialogMediator mediator) {
        super(mediator);
    }
    
    public void setText(String text) {
        this.text = text;
        System.out.println("Label: " + text);
    }
    
    public String getText() {
        return text;
    }
}
```

#### 4. Concrete Mediator
```java
public class LoginDialogMediator implements DialogMediator {
    private TextField usernameField;
    private TextField passwordField;
    private CheckBox rememberMeCheckbox;
    private Button loginButton;
    private Button cancelButton;
    private Label statusLabel;
    
    public void createComponents() {
        // Create components with mediator reference
        usernameField = new TextField(this);
        passwordField = new TextField(this);
        rememberMeCheckbox = new CheckBox(this, "Remember me");
        loginButton = new Button(this, "Login");
        cancelButton = new Button(this, "Cancel");
        statusLabel = new Label(this);
        
        // Initial state
        updateLoginButtonState();
    }
    
    @Override
    public void notify(Component sender, String event) {
        System.out.println("Mediator received: " + event + " from " + sender.getClass().getSimpleName());
        
        if (sender == usernameField && "textChanged".equals(event)) {
            handleUsernameChange();
        } else if (sender == passwordField && "textChanged".equals(event)) {
            handlePasswordChange();
        } else if (sender == loginButton && "clicked".equals(event)) {
            handleLoginClick();
        } else if (sender == cancelButton && "clicked".equals(event)) {
            handleCancelClick();
        } else if (sender == rememberMeCheckbox && "toggled".equals(event)) {
            handleRememberMeToggle();
        }
    }
    
    private void handleUsernameChange() {
        String username = usernameField.getText();
        
        if (username.isEmpty()) {
            statusLabel.setText("Username is required");
        } else if (username.length() < 3) {
            statusLabel.setText("Username too short");
        } else {
            statusLabel.setText("Username OK");
        }
        
        updateLoginButtonState();
    }
    
    private void handlePasswordChange() {
        String password = passwordField.getText();
        
        if (password.isEmpty()) {
            statusLabel.setText("Password is required");
        } else if (password.length() < 6) {
            statusLabel.setText("Password too short (min 6 characters)");
        } else {
            statusLabel.setText("Password OK");
        }
        
        updateLoginButtonState();
    }
    
    private void handleLoginClick() {
        if (isFormValid()) {
            statusLabel.setText("Logging in...");
            
            // Disable form during login
            usernameField.setEnabled(false);
            passwordField.setEnabled(false);
            loginButton.setEnabled(false);
            rememberMeCheckbox.setEnabled(false);
            
            // Simulate login process
            performLogin();
        }
    }
    
    private void handleCancelClick() {
        statusLabel.setText("Login cancelled");
        
        // Clear form
        usernameField.setText("");
        passwordField.setText("");
        rememberMeCheckbox.toggle(); // Reset to unchecked
        
        updateLoginButtonState();
    }
    
    private void handleRememberMeToggle() {
        if (rememberMeCheckbox.isChecked()) {
            statusLabel.setText("Login will be remembered");
        } else {
            statusLabel.setText("Login will not be remembered");
        }
    }
    
    private void updateLoginButtonState() {
        boolean isValid = isFormValid();
        loginButton.setEnabled(isValid);
        
        if (isValid) {
            statusLabel.setText("Ready to login");
        }
    }
    
    private boolean isFormValid() {
        return !usernameField.getText().isEmpty() && 
               usernameField.getText().length() >= 3 &&
               !passwordField.getText().isEmpty() && 
               passwordField.getText().length() >= 6;
    }
    
    private void performLogin() {
        // Simulate login process
        System.out.println("Performing login for: " + usernameField.getText());
        System.out.println("Remember me: " + rememberMeCheckbox.isChecked());
        
        // Simulate success
        statusLabel.setText("Login successful!");
    }
    
    // Getters for testing
    public TextField getUsernameField() { return usernameField; }
    public TextField getPasswordField() { return passwordField; }
    public Button getLoginButton() { return loginButton; }
    public Button getCancelButton() { return cancelButton; }
    public CheckBox getRememberMeCheckbox() { return rememberMeCheckbox; }
}
```

#### 5. Usage
```java
public class LoginDialogApp {
    public static void main(String[] args) {
        LoginDialogMediator dialog = new LoginDialogMediator();
        dialog.createComponents();
        
        System.out.println("=== Login Dialog Created ===");
        
        // Simulate user interactions
        System.out.println("\n--- User types username ---");
        dialog.getUsernameField().setText("jo");  // Too short
        
        System.out.println("\n--- User types longer username ---");
        dialog.getUsernameField().setText("john");  // Valid
        
        System.out.println("\n--- User types password ---");
        dialog.getPasswordField().setText("123");  // Too short
        
        System.out.println("\n--- User types longer password ---");
        dialog.getPasswordField().setText("password123");  // Valid
        
        System.out.println("\n--- User toggles remember me ---");
        dialog.getRememberMeCheckbox().toggle();
        
        System.out.println("\n--- User clicks login ---");
        dialog.getLoginButton().click();
        
        System.out.println("\n--- User clicks cancel ---");
        dialog.getCancelButton().click();
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Mediator (interface)
├── + notify(Component, String): void
│
ConcreteMediator implements Mediator
├── - component1: Component1
├── - component2: Component2
├── + notify(Component, String): void
│
Component (abstract)
├── # mediator: Mediator
├── + Component(Mediator)
├── # notifyMediator(String): void
│
Component1 extends Component
├── + operation1(): void
│
Component2 extends Component
├── + operation2(): void
```

### Các thành phần chính

#### 1. Mediator Interface
- **Vai trò:** Declares communication contract
- **Đặc điểm:** Usually has notify method

#### 2. Concrete Mediator
- **Vai trò:** Implements coordination logic
- **Đặc điểm:** Knows all components và manages interactions

#### 3. Component Classes
- **Vai trò:** Individual business logic
- **Đặc điểm:** Communicate only through mediator

---

## Cách triển khai

### Bước 1: Define Mediator interface
```java
public interface ChatMediator {
    void sendMessage(String message, User user);
    void addUser(User user);
    void removeUser(User user);
}
```

### Bước 2: Create Component base class
```java
public abstract class User {
    protected ChatMediator mediator;
    protected String name;
    
    public User(ChatMediator mediator, String name) {
        this.mediator = mediator;
        this.name = name;
    }
    
    public abstract void send(String message);
    public abstract void receive(String message);
    
    public String getName() {
        return name;
    }
}
```

### Bước 3: Implement Concrete Components
```java
public class ConcreteUser extends User {
    
    public ConcreteUser(ChatMediator mediator, String name) {
        super(mediator, name);
    }
    
    @Override
    public void send(String message) {
        System.out.println(name + " sends: " + message);
        mediator.sendMessage(message, this);
    }
    
    @Override
    public void receive(String message) {
        System.out.println(name + " receives: " + message);
    }
}
```

### Bước 4: Implement Concrete Mediator
```java
public class ChatRoom implements ChatMediator {
    private List<User> users = new ArrayList<>();
    
    @Override
    public void addUser(User user) {
        users.add(user);
        System.out.println(user.getName() + " joined the chat");
    }
    
    @Override
    public void removeUser(User user) {
        users.remove(user);
        System.out.println(user.getName() + " left the chat");
    }
    
    @Override
    public void sendMessage(String message, User sender) {
        for (User user : users) {
            if (user != sender) {
                user.receive(message);
            }
        }
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Reduced coupling
```java
// Components don't know about each other
public class TextField extends Component {
    public void onTextChange() {
        notifyMediator("textChanged"); // Only knows mediator
        // No direct references to Button, Label, etc.
    }
}
```

#### 2. Centralized control
```java
// All interaction logic in one place
public class DialogMediator {
    public void notify(Component sender, String event) {
        // All coordination logic here
        // Easy to understand and modify
    }
}
```

#### 3. Reusable components
```java
// Components can be reused in different contexts
TextField field1 = new TextField(loginMediator);
TextField field2 = new TextField(registrationMediator);
// Same component, different mediators
```

#### 4. Single Responsibility Principle
```java
// Each component focuses on its own logic
public class Button {
    public void click() {
        // Only button-specific logic
        notifyMediator("clicked");
    }
}
```

### ❌ Nhược điểm

#### 1. God Object risk
```java
// Mediator can become too complex
public class ComplexMediator implements Mediator {
    public void notify(Component sender, String event) {
        // Hundreds of if-else statements
        // Becomes hard to maintain
    }
}
```

#### 2. Increased complexity for simple interactions
```java
// Overkill for simple cases
public class SimpleDialog {
    // Direct interaction might be simpler
    button.onClick(() -> label.setText("Clicked"));
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Complex interactions between objects
```java
// GUI dialogs with many interdependent components
// Workflow systems with multiple steps
// Game systems with many interacting entities
```

#### 2. Tight coupling between objects
```java
// When objects reference each other directly
// Hard to reuse components in different contexts
```

#### 3. Behavior distributed across multiple classes
```java
// When interaction logic is scattered
// Hard to understand overall behavior
```

### ❌ Không nên sử dụng khi:

#### 1. Simple interactions
```java
// One-to-one relationships
// Simple event handling
```

#### 2. Stable, well-defined interfaces
```java
// When objects have clear, stable contracts
// Direct communication is simpler
```

---

## Ví dụ thực tế

### Ví dụ 1: Air Traffic Control System

```java
// Aircraft interface
public interface Aircraft {
    String getCallSign();
    String getAircraftType();
    void receiveMessage(String message);
    void requestPermission(String request);
}

// Air Traffic Control Mediator
public interface AirTrafficControl {
    void registerAircraft(Aircraft aircraft);
    void unregisterAircraft(Aircraft aircraft);
    void requestLanding(Aircraft aircraft);
    void requestTakeoff(Aircraft aircraft);
    void requestRouteChange(Aircraft aircraft, String newRoute);
    void sendMessage(Aircraft sender, String message, Aircraft recipient);
    void broadcastMessage(Aircraft sender, String message);
}

// Concrete Aircraft implementations
public class CommercialAircraft implements Aircraft {
    private String callSign;
    private String aircraftType;
    private AirTrafficControl atc;
    private String currentStatus;
    
    public CommercialAircraft(String callSign, String aircraftType, AirTrafficControl atc) {
        this.callSign = callSign;
        this.aircraftType = aircraftType;
        this.atc = atc;
        this.currentStatus = "In Flight";
    }
    
    @Override
    public String getCallSign() {
        return callSign;
    }
    
    @Override
    public String getAircraftType() {
        return aircraftType;
    }
    
    @Override
    public void receiveMessage(String message) {
        System.out.println("[" + callSign + "] Received: " + message);
    }
    
    @Override
    public void requestPermission(String request) {
        System.out.println("[" + callSign + "] Requesting: " + request);
        
        if (request.contains("landing")) {
            atc.requestLanding(this);
        } else if (request.contains("takeoff")) {
            atc.requestTakeoff(this);
        } else if (request.contains("route")) {
            atc.requestRouteChange(this, request);
        }
    }
    
    public void sendMessageToAircraft(String message, Aircraft recipient) {
        atc.sendMessage(this, message, recipient);
    }
    
    public void broadcastMessage(String message) {
        atc.broadcastMessage(this, message);
    }
    
    public String getCurrentStatus() {
        return currentStatus;
    }
    
    public void setCurrentStatus(String status) {
        this.currentStatus = status;
    }
}

public class PrivateJet implements Aircraft {
    private String callSign;
    private String aircraftType;
    private AirTrafficControl atc;
    
    public PrivateJet(String callSign, AirTrafficControl atc) {
        this.callSign = callSign;
        this.aircraftType = "Private Jet";
        this.atc = atc;
    }
    
    @Override
    public String getCallSign() {
        return callSign;
    }
    
    @Override
    public String getAircraftType() {
        return aircraftType;
    }
    
    @Override
    public void receiveMessage(String message) {
        System.out.println("[" + callSign + " - Private] Received: " + message);
    }
    
    @Override
    public void requestPermission(String request) {
        System.out.println("[" + callSign + " - Private] Requesting: " + request);
        
        if (request.contains("priority landing")) {
            atc.requestLanding(this);
        } else if (request.contains("immediate takeoff")) {
            atc.requestTakeoff(this);
        }
    }
}

// Concrete Air Traffic Control Tower
public class ControlTower implements AirTrafficControl {
    private List<Aircraft> aircraftList;
    private List<String> runwayStatus; // Available runways
    private Map<Aircraft, String> aircraftStatus;
    
    public ControlTower() {
        this.aircraftList = new ArrayList<>();
        this.runwayStatus = new ArrayList<>();
        this.aircraftStatus = new HashMap<>();
        
        // Initialize runways
        runwayStatus.add("Runway 1 - Available");
        runwayStatus.add("Runway 2 - Available");
        runwayStatus.add("Runway 3 - Available");
    }
    
    @Override
    public void registerAircraft(Aircraft aircraft) {
        aircraftList.add(aircraft);
        aircraftStatus.put(aircraft, "Registered");
        
        System.out.println("🏗️ ATC: " + aircraft.getCallSign() + " (" + aircraft.getAircraftType() + ") registered");
        aircraft.receiveMessage("Welcome to airspace. You are now under ATC control.");
        
        // Broadcast to other aircraft
        broadcastMessage(null, "New aircraft " + aircraft.getCallSign() + " has entered airspace");
    }
    
    @Override
    public void unregisterAircraft(Aircraft aircraft) {
        aircraftList.remove(aircraft);
        aircraftStatus.remove(aircraft);
        
        System.out.println("🏗️ ATC: " + aircraft.getCallSign() + " unregistered");
        broadcastMessage(null, "Aircraft " + aircraft.getCallSign() + " has left airspace");
    }
    
    @Override
    public void requestLanding(Aircraft aircraft) {
        System.out.println("🛬 ATC: Processing landing request from " + aircraft.getCallSign());
        
        // Check runway availability
        String availableRunway = findAvailableRunway();
        
        if (availableRunway != null) {
            aircraftStatus.put(aircraft, "Landing Approved");
            aircraft.receiveMessage("Landing approved on " + availableRunway + ". Maintain current heading.");
            
            // Update runway status
            int runwayIndex = runwayStatus.indexOf(availableRunway);
            runwayStatus.set(runwayIndex, availableRunway.replace("Available", "Occupied by " + aircraft.getCallSign()));
            
            // Notify other aircraft
            broadcastMessage(aircraft, "Aircraft " + aircraft.getCallSign() + " cleared for landing on " + availableRunway);
            
        } else {
            aircraftStatus.put(aircraft, "Holding Pattern");
            aircraft.receiveMessage("No runway available. Enter holding pattern at current altitude.");
            
            // Notify other aircraft about traffic
            broadcastMessage(aircraft, "Traffic alert: " + aircraft.getCallSign() + " in holding pattern");
        }
    }
    
    @Override
    public void requestTakeoff(Aircraft aircraft) {
        System.out.println("🛫 ATC: Processing takeoff request from " + aircraft.getCallSign());
        
        String availableRunway = findAvailableRunway();
        
        if (availableRunway != null) {
            aircraftStatus.put(aircraft, "Takeoff Approved");
            aircraft.receiveMessage("Takeoff approved on " + availableRunway + ". Wind 270 at 10 knots.");
            
            // Update runway status
            int runwayIndex = runwayStatus.indexOf(availableRunway);
            runwayStatus.set(runwayIndex, availableRunway.replace("Available", "Occupied by " + aircraft.getCallSign()));
            
            // Notify other aircraft
            broadcastMessage(aircraft, "Aircraft " + aircraft.getCallSign() + " cleared for takeoff on " + availableRunway);
            
        } else {
            aircraftStatus.put(aircraft, "Waiting for Takeoff");
            aircraft.receiveMessage("Hold position. Runway not available for takeoff.");
        }
    }
    
    @Override
    public void requestRouteChange(Aircraft aircraft, String newRoute) {
        System.out.println("🗺️ ATC: Processing route change request from " + aircraft.getCallSign());
        
        // Check for conflicts with other aircraft
        boolean conflictDetected = checkRouteConflicts(aircraft, newRoute);
        
        if (!conflictDetected) {
            aircraftStatus.put(aircraft, "Route Change Approved");
            aircraft.receiveMessage("Route change approved: " + newRoute);
            
            // Notify other aircraft about route change
            broadcastMessage(aircraft, "Traffic update: " + aircraft.getCallSign() + " changing route");
            
        } else {
            aircraft.receiveMessage("Route change denied due to traffic conflict. Maintain current route.");
        }
    }
    
    @Override
    public void sendMessage(Aircraft sender, String message, Aircraft recipient) {
        if (aircraftList.contains(recipient)) {
            System.out.println("📡 ATC: Relaying message from " + sender.getCallSign() + " to " + recipient.getCallSign());
            recipient.receiveMessage("Message from " + sender.getCallSign() + ": " + message);
        } else {
            sender.receiveMessage("Recipient " + recipient.getCallSign() + " not found in airspace");
        }
    }
    
    @Override
    public void broadcastMessage(Aircraft sender, String message) {
        String fullMessage = (sender != null) ? 
            "Broadcast from " + sender.getCallSign() + ": " + message :
            "ATC Broadcast: " + message;
            
        System.out.println("📢 ATC: Broadcasting - " + fullMessage);
        
        for (Aircraft aircraft : aircraftList) {
            if (aircraft != sender) {
                aircraft.receiveMessage(fullMessage);
            }
        }
    }
    
    private String findAvailableRunway() {
        for (String runway : runwayStatus) {
            if (runway.contains("Available")) {
                return runway;
            }
        }
        return null;
    }
    
    private boolean checkRouteConflicts(Aircraft aircraft, String newRoute) {
        // Simplified conflict detection
        // In real system, this would check 3D coordinates, altitudes, etc.
        return Math.random() < 0.3; // 30% chance of conflict for demo
    }
    
    public void releaseRunway(String aircraftCallSign) {
        for (int i = 0; i < runwayStatus.size(); i++) {
            String runway = runwayStatus.get(i);
            if (runway.contains(aircraftCallSign)) {
                runwayStatus.set(i, runway.split(" - ")[0] + " - Available");
                System.out.println("🛬 ATC: Runway released by " + aircraftCallSign);
                break;
            }
        }
    }
    
    public void displayStatus() {
        System.out.println("\n=== ATC Status ===");
        System.out.println("Aircraft in airspace: " + aircraftList.size());
        
        for (Aircraft aircraft : aircraftList) {
            System.out.println("- " + aircraft.getCallSign() + " (" + aircraft.getAircraftType() + "): " + 
                             aircraftStatus.get(aircraft));
        }
        
        System.out.println("\nRunway Status:");
        for (String runway : runwayStatus) {
            System.out.println("- " + runway);
        }
        System.out.println("==================\n");
    }
}

// Usage
public class AirTrafficControlDemo {
    public static void main(String[] args) {
        // Create control tower
        ControlTower controlTower = new ControlTower();
        
        // Create aircraft
        CommercialAircraft flight123 = new CommercialAircraft("AA123", "Boeing 737", controlTower);
        CommercialAircraft flight456 = new CommercialAircraft("UA456", "Airbus A320", controlTower);
        PrivateJet privateJet = new PrivateJet("N123PJ", controlTower);
        
        // Register aircraft with ATC
        System.out.println("=== Aircraft Registration ===");
        controlTower.registerAircraft(flight123);
        controlTower.registerAircraft(flight456);
        controlTower.registerAircraft(privateJet);
        
        controlTower.displayStatus();
        
        // Simulate air traffic scenarios
        System.out.println("=== Landing Requests ===");
        flight123.requestPermission("requesting landing clearance");
        flight456.requestPermission("requesting landing clearance");
        
        controlTower.displayStatus();
        
        System.out.println("=== Route Change Request ===");
        flight123.requestPermission("requesting route change to avoid weather");
        
        System.out.println("=== Aircraft Communication ===");
        flight123.sendMessageToAircraft("What's your altitude?", flight456);
        
        System.out.println("=== Emergency Broadcast ===");
        privateJet.broadcastMessage("Emergency - bird strike, requesting priority landing");
        
        System.out.println("=== Takeoff Requests ===");
        flight456.requestPermission("requesting takeoff clearance");
        
        // Simulate runway release
        controlTower.releaseRunway("AA123");
        
        controlTower.displayStatus();
        
        System.out.println("Mediator Pattern Benefits:");
        System.out.println("- Centralized air traffic coordination");
        System.out.println("- Aircraft don't communicate directly");
        System.out.println("- Easy to add new aircraft types");
        System.out.println("- Complex coordination logic in one place");
        System.out.println("- Safety through controlled communication");
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Mediator Pattern** centralizes complex communications
2. **Reduces coupling** giữa interacting objects
3. **Promotes reusability** của individual components
4. **Makes interactions** easier to understand và maintain

### So sánh với patterns khác
| Pattern | Purpose | Communication |
|---------|---------|---------------|
| **Mediator** | Centralize interactions | Through mediator |
| **Observer** | Notify multiple objects | Direct notification |
| **Chain of Responsibility** | Pass requests through chain | Sequential handling |
| **Command** | Encapsulate requests | Request as object |

### Best Practices
- **Keep mediator focused** - avoid God Object
- **Use for complex interactions** only
- **Consider** splitting large mediators
- **Document** interaction flows
- **Test** mediator logic thoroughly

---

**Tiếp theo:** [Memento](memento.md) - Lưu trữ và khôi phục trạng thái object
