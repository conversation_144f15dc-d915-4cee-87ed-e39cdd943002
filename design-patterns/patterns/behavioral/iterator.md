# Iterator Pattern

> **Behavioral Pattern** - <PERSON><PERSON> cấp cách để truy cập tuần tự các phần tử của collection mà không expose underlying representation

## 📋 Mục lục

1. [Tổng quan](#tổng-quan)
2. [V<PERSON><PERSON> đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#giải-pháp)
4. [C<PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON>ợ<PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Iterator là một **behavioral design pattern** cho phép **traverse elements của collection mà không exposing underlying representation** (list, stack, tree, etc.).

### <PERSON><PERSON><PERSON> đ<PERSON> ch<PERSON>h
- **Provide uniform interface** cho traversing collections
- **Hide internal structure** của collections
- **Support multiple traversal algorithms**
- **Enable concurrent iteration**

### Tên gọi khác
- **Cursor Pattern**
- **Enumerator Pattern**

### Ví dụ thực tế
Giống như **remote control**: bạn có thể chuyển kênh tiếp theo/trước đó mà không cần biết TV lưu trữ danh sách kênh như thế nào (array, linked list, database).

---

## Vấn đề

### Tình huống thực tế
Bạn có different types of collections cần traverse:

```java
// Array-based collection
public class ArrayCollection<T> {
    private T[] items;
    private int size;
    
    public T get(int index) {
        return items[index];
    }
    
    public int size() {
        return size;
    }
}

// Linked list collection
public class LinkedListCollection<T> {
    private Node<T> head;
    
    private static class Node<T> {
        T data;
        Node<T> next;
    }
    
    public Node<T> getHead() {
        return head;
    }
}

// Tree collection
public class TreeCollection<T> {
    private TreeNode<T> root;
    
    private static class TreeNode<T> {
        T data;
        TreeNode<T> left, right;
    }
    
    public TreeNode<T> getRoot() {
        return root;
    }
}
```

### Vấn đề phát sinh

#### 1. Different traversal methods
```java
// Client code needs to know internal structure
public void processCollections() {
    // Array traversal
    ArrayCollection<String> arrayCol = new ArrayCollection<>();
    for (int i = 0; i < arrayCol.size(); i++) {
        String item = arrayCol.get(i);
        process(item);
    }
    
    // Linked list traversal
    LinkedListCollection<String> listCol = new LinkedListCollection<>();
    Node<String> current = listCol.getHead();
    while (current != null) {
        process(current.data);
        current = current.next;
    }
    
    // Tree traversal
    TreeCollection<String> treeCol = new TreeCollection<>();
    traverseTree(treeCol.getRoot()); // Recursive method needed
}
```

#### 2. Exposed internal structure
```java
// Client depends on internal implementation
public void printItems(LinkedListCollection<String> collection) {
    Node<String> node = collection.getHead(); // Exposes internal Node
    while (node != null) {
        System.out.println(node.data); // Knows about Node structure
        node = node.next;
    }
}
```

#### 3. No uniform interface
```java
// Cannot write generic traversal code
public void processAnyCollection(???) {
    // How to traverse unknown collection type?
    // Each collection has different traversal method
}
```

#### 4. Multiple traversal algorithms
```java
// Tree can be traversed in different ways
public void traverseTreeInOrder(TreeNode<String> node) { ... }
public void traverseTreePreOrder(TreeNode<String> node) { ... }
public void traverseTreePostOrder(TreeNode<String> node) { ... }

// No way to switch algorithms dynamically
// Client needs to know all algorithms
```

---

## Giải pháp

### Ý tưởng cốt lõi
Iterator pattern đề xuất **extract traversal behavior của collection thành separate object** gọi là iterator. Iterator object encapsulates tất cả traversal details như current position và how many elements left.

### Cách hoạt động

#### 1. Iterator interface
```java
public interface Iterator<T> {
    boolean hasNext();
    T next();
    void remove(); // Optional
}
```

#### 2. Iterable interface
```java
public interface Iterable<T> {
    Iterator<T> iterator();
}
```

#### 3. Concrete Iterator implementations
```java
public class ArrayIterator<T> implements Iterator<T> {
    private T[] array;
    private int position = 0;
    
    public ArrayIterator(T[] array) {
        this.array = array;
    }
    
    @Override
    public boolean hasNext() {
        return position < array.length && array[position] != null;
    }
    
    @Override
    public T next() {
        if (!hasNext()) {
            throw new NoSuchElementException();
        }
        return array[position++];
    }
    
    @Override
    public void remove() {
        if (position <= 0) {
            throw new IllegalStateException();
        }
        // Shift elements left
        for (int i = position - 1; i < array.length - 1; i++) {
            array[i] = array[i + 1];
        }
        array[array.length - 1] = null;
        position--;
    }
}

public class LinkedListIterator<T> implements Iterator<T> {
    private Node<T> current;
    private Node<T> previous;
    private LinkedListCollection<T> collection;
    
    public LinkedListIterator(LinkedListCollection<T> collection) {
        this.collection = collection;
        this.current = collection.getHead();
    }
    
    @Override
    public boolean hasNext() {
        return current != null;
    }
    
    @Override
    public T next() {
        if (!hasNext()) {
            throw new NoSuchElementException();
        }
        T data = current.data;
        previous = current;
        current = current.next;
        return data;
    }
    
    @Override
    public void remove() {
        if (previous == null) {
            throw new IllegalStateException();
        }
        collection.remove(previous);
        previous = null;
    }
}
```

#### 4. Collections implementing Iterable
```java
public class ArrayCollection<T> implements Iterable<T> {
    private T[] items;
    private int size;
    
    @SuppressWarnings("unchecked")
    public ArrayCollection(int capacity) {
        items = (T[]) new Object[capacity];
        size = 0;
    }
    
    public void add(T item) {
        if (size < items.length) {
            items[size++] = item;
        }
    }
    
    @Override
    public Iterator<T> iterator() {
        return new ArrayIterator<>(items);
    }
}

public class LinkedListCollection<T> implements Iterable<T> {
    private Node<T> head;
    
    private static class Node<T> {
        T data;
        Node<T> next;
        
        Node(T data) {
            this.data = data;
        }
    }
    
    public void add(T item) {
        Node<T> newNode = new Node<>(item);
        newNode.next = head;
        head = newNode;
    }
    
    @Override
    public Iterator<T> iterator() {
        return new LinkedListIterator<>(this);
    }
    
    Node<T> getHead() {
        return head;
    }
    
    void remove(Node<T> node) {
        // Implementation for removing specific node
        if (head == node) {
            head = head.next;
            return;
        }
        
        Node<T> current = head;
        while (current != null && current.next != node) {
            current = current.next;
        }
        
        if (current != null) {
            current.next = node.next;
        }
    }
}
```

#### 5. Multiple iterator types for same collection
```java
public class TreeCollection<T> implements Iterable<T> {
    private TreeNode<T> root;
    
    private static class TreeNode<T> {
        T data;
        TreeNode<T> left, right;
        
        TreeNode(T data) {
            this.data = data;
        }
    }
    
    @Override
    public Iterator<T> iterator() {
        return inOrderIterator(); // Default iterator
    }
    
    public Iterator<T> inOrderIterator() {
        return new InOrderTreeIterator<>(root);
    }
    
    public Iterator<T> preOrderIterator() {
        return new PreOrderTreeIterator<>(root);
    }
    
    public Iterator<T> postOrderIterator() {
        return new PostOrderTreeIterator<>(root);
    }
    
    public void add(T item) {
        root = addRecursive(root, item);
    }
    
    private TreeNode<T> addRecursive(TreeNode<T> node, T item) {
        if (node == null) {
            return new TreeNode<>(item);
        }
        
        // Simple insertion logic (assuming Comparable)
        if (item.toString().compareTo(node.data.toString()) < 0) {
            node.left = addRecursive(node.left, item);
        } else {
            node.right = addRecursive(node.right, item);
        }
        
        return node;
    }
}

// In-order tree iterator
public class InOrderTreeIterator<T> implements Iterator<T> {
    private Stack<TreeNode<T>> stack = new Stack<>();
    
    public InOrderTreeIterator(TreeNode<T> root) {
        pushLeft(root);
    }
    
    private void pushLeft(TreeNode<T> node) {
        while (node != null) {
            stack.push(node);
            node = node.left;
        }
    }
    
    @Override
    public boolean hasNext() {
        return !stack.isEmpty();
    }
    
    @Override
    public T next() {
        if (!hasNext()) {
            throw new NoSuchElementException();
        }
        
        TreeNode<T> node = stack.pop();
        pushLeft(node.right);
        return node.data;
    }
}
```

#### 6. Uniform client code
```java
public class CollectionProcessor {
    
    public static <T> void processCollection(Iterable<T> collection) {
        System.out.println("Processing collection...");
        
        Iterator<T> iterator = collection.iterator();
        while (iterator.hasNext()) {
            T item = iterator.next();
            System.out.println("Processing: " + item);
        }
    }
    
    public static <T> void processWithEnhancedFor(Iterable<T> collection) {
        System.out.println("Processing with enhanced for loop...");
        
        for (T item : collection) { // Uses iterator internally
            System.out.println("Processing: " + item);
        }
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Iterator<T> (interface)
├── + hasNext(): boolean
├── + next(): T
├── + remove(): void
│
ConcreteIterator<T> implements Iterator<T>
├── - collection: ConcreteCollection<T>
├── - position: int
├── + hasNext(): boolean
├── + next(): T
├── + remove(): void
│
Iterable<T> (interface)
├── + iterator(): Iterator<T>
│
ConcreteCollection<T> implements Iterable<T>
├── - items: T[]
├── + iterator(): Iterator<T>
├── + add(T): void
├── + remove(T): void
```

### Các thành phần chính

#### 1. Iterator Interface
- **Vai trò:** Declares methods cho traversing collection
- **Đặc điểm:** Usually has hasNext(), next(), và optional remove()

#### 2. Concrete Iterator
- **Vai trò:** Implements traversal algorithm
- **Đặc điểm:** Keeps track of current position

#### 3. Iterable Interface
- **Vai trò:** Declares method để create iterator
- **Đặc điểm:** Allows collection to be used in enhanced for loops

#### 4. Concrete Collection
- **Vai trò:** Implements collection và returns appropriate iterator
- **Đặc điểm:** Can provide multiple iterator types

---

## Cách triển khai

### Bước 1: Define Iterator interface
```java
public interface BookIterator {
    boolean hasNext();
    Book next();
}
```

### Bước 2: Define Iterable interface
```java
public interface BookCollection {
    BookIterator createIterator();
}
```

### Bước 3: Implement Concrete Collection
```java
public class BookShelf implements BookCollection {
    private Book[] books;
    private int size;
    
    public BookShelf(int capacity) {
        books = new Book[capacity];
        size = 0;
    }
    
    public void addBook(Book book) {
        if (size < books.length) {
            books[size++] = book;
        }
    }
    
    @Override
    public BookIterator createIterator() {
        return new BookShelfIterator(this);
    }
    
    public Book getBook(int index) {
        return books[index];
    }
    
    public int getSize() {
        return size;
    }
}
```

### Bước 4: Implement Concrete Iterator
```java
public class BookShelfIterator implements BookIterator {
    private BookShelf bookShelf;
    private int position = 0;
    
    public BookShelfIterator(BookShelf bookShelf) {
        this.bookShelf = bookShelf;
    }
    
    @Override
    public boolean hasNext() {
        return position < bookShelf.getSize();
    }
    
    @Override
    public Book next() {
        if (!hasNext()) {
            throw new NoSuchElementException();
        }
        return bookShelf.getBook(position++);
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Uniform traversal interface
```java
// Same code works for any collection
public void printItems(Iterable<String> collection) {
    for (String item : collection) {
        System.out.println(item);
    }
}
```

#### 2. Encapsulation of traversal logic
```java
// Client doesn't need to know internal structure
Iterator<String> iter = collection.iterator();
while (iter.hasNext()) {
    String item = iter.next(); // Don't care how it's stored
}
```

#### 3. Multiple traversal algorithms
```java
// Same collection, different traversal orders
TreeCollection<String> tree = new TreeCollection<>();
Iterator<String> inOrder = tree.inOrderIterator();
Iterator<String> preOrder = tree.preOrderIterator();
```

#### 4. Concurrent iteration
```java
// Multiple iterators can traverse same collection
Iterator<String> iter1 = collection.iterator();
Iterator<String> iter2 = collection.iterator();
// Each maintains independent position
```

### ❌ Nhược điểm

#### 1. Overhead for simple collections
```java
// Simple array access might be faster than iterator
for (int i = 0; i < array.length; i++) {
    process(array[i]); // Direct access
}

// vs
for (String item : collection) {
    process(item); // Iterator overhead
}
```

#### 2. Memory overhead
```java
// Each iterator object takes memory
// Multiple concurrent iterators multiply overhead
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Need uniform traversal interface
```java
// Want same code to work with different collections
public void processAnyCollection(Iterable<String> collection) {
    for (String item : collection) {
        process(item);
    }
}
```

#### 2. Hide internal collection structure
```java
// Don't want clients to depend on internal representation
// Collection can change implementation without affecting clients
```

#### 3. Support multiple traversal algorithms
```java
// Tree: in-order, pre-order, post-order
// Graph: depth-first, breadth-first
// Matrix: row-wise, column-wise
```

#### 4. Need concurrent iteration
```java
// Multiple threads or algorithms traversing same collection
// Each needs independent position tracking
```

### ❌ Không nên sử dụng khi:

#### 1. Simple collections với direct access
```java
// Array with index access is simpler
int[] numbers = {1, 2, 3, 4, 5};
for (int i = 0; i < numbers.length; i++) {
    process(numbers[i]);
}
```

#### 2. Performance is critical
```java
// Iterator overhead might be unacceptable
// Direct access might be faster
```

---

## Ví dụ thực tế

### Ví dụ 1: Social Media Feed Iterator

```java
// Post class
public class Post {
    private String id;
    private String author;
    private String content;
    private Date timestamp;
    private int likes;
    
    public Post(String id, String author, String content) {
        this.id = id;
        this.author = author;
        this.content = content;
        this.timestamp = new Date();
        this.likes = 0;
    }
    
    // Getters and setters
    public String getId() { return id; }
    public String getAuthor() { return author; }
    public String getContent() { return content; }
    public Date getTimestamp() { return timestamp; }
    public int getLikes() { return likes; }
    public void setLikes(int likes) { this.likes = likes; }
    
    @Override
    public String toString() {
        return String.format("Post{id='%s', author='%s', content='%s', likes=%d}", 
                           id, author, content, likes);
    }
}

// Feed iterator interface
public interface FeedIterator {
    boolean hasNext();
    Post next();
    void reset();
}

// Social media feed interface
public interface SocialMediaFeed {
    FeedIterator createChronologicalIterator();
    FeedIterator createPopularityIterator();
    FeedIterator createAuthorIterator(String author);
}

// Chronological iterator - newest first
public class ChronologicalIterator implements FeedIterator {
    private List<Post> posts;
    private int position;
    
    public ChronologicalIterator(List<Post> posts) {
        this.posts = new ArrayList<>(posts);
        // Sort by timestamp descending (newest first)
        this.posts.sort((p1, p2) -> p2.getTimestamp().compareTo(p1.getTimestamp()));
        this.position = 0;
    }
    
    @Override
    public boolean hasNext() {
        return position < posts.size();
    }
    
    @Override
    public Post next() {
        if (!hasNext()) {
            throw new NoSuchElementException("No more posts");
        }
        return posts.get(position++);
    }
    
    @Override
    public void reset() {
        position = 0;
    }
}

// Popularity iterator - most liked first
public class PopularityIterator implements FeedIterator {
    private List<Post> posts;
    private int position;
    
    public PopularityIterator(List<Post> posts) {
        this.posts = new ArrayList<>(posts);
        // Sort by likes descending (most liked first)
        this.posts.sort((p1, p2) -> Integer.compare(p2.getLikes(), p1.getLikes()));
        this.position = 0;
    }
    
    @Override
    public boolean hasNext() {
        return position < posts.size();
    }
    
    @Override
    public Post next() {
        if (!hasNext()) {
            throw new NoSuchElementException("No more posts");
        }
        return posts.get(position++);
    }
    
    @Override
    public void reset() {
        position = 0;
    }
}

// Author-specific iterator
public class AuthorIterator implements FeedIterator {
    private List<Post> authorPosts;
    private int position;
    
    public AuthorIterator(List<Post> allPosts, String author) {
        this.authorPosts = allPosts.stream()
            .filter(post -> post.getAuthor().equals(author))
            .sorted((p1, p2) -> p2.getTimestamp().compareTo(p1.getTimestamp()))
            .collect(Collectors.toList());
        this.position = 0;
    }
    
    @Override
    public boolean hasNext() {
        return position < authorPosts.size();
    }
    
    @Override
    public Post next() {
        if (!hasNext()) {
            throw new NoSuchElementException("No more posts from this author");
        }
        return authorPosts.get(position++);
    }
    
    @Override
    public void reset() {
        position = 0;
    }
}

// Facebook feed implementation
public class FacebookFeed implements SocialMediaFeed {
    private List<Post> posts;
    
    public FacebookFeed() {
        this.posts = new ArrayList<>();
    }
    
    public void addPost(Post post) {
        posts.add(post);
    }
    
    @Override
    public FeedIterator createChronologicalIterator() {
        return new ChronologicalIterator(posts);
    }
    
    @Override
    public FeedIterator createPopularityIterator() {
        return new PopularityIterator(posts);
    }
    
    @Override
    public FeedIterator createAuthorIterator(String author) {
        return new AuthorIterator(posts, author);
    }
    
    public int getPostCount() {
        return posts.size();
    }
}

// Feed reader application
public class FeedReader {
    
    public void displayFeed(FeedIterator iterator, String feedType, int maxPosts) {
        System.out.println("=== " + feedType + " Feed ===");
        
        int count = 0;
        while (iterator.hasNext() && count < maxPosts) {
            Post post = iterator.next();
            System.out.println((count + 1) + ". " + post);
            count++;
        }
        
        if (count == 0) {
            System.out.println("No posts to display");
        }
        
        System.out.println();
    }
    
    public void searchAndDisplay(SocialMediaFeed feed, String author) {
        System.out.println("=== Posts by " + author + " ===");
        
        FeedIterator authorIterator = feed.createAuthorIterator(author);
        int count = 0;
        
        while (authorIterator.hasNext()) {
            Post post = authorIterator.next();
            System.out.println((count + 1) + ". " + post);
            count++;
        }
        
        if (count == 0) {
            System.out.println("No posts found by " + author);
        }
        
        System.out.println();
    }
}

// Usage
public class SocialMediaFeedDemo {
    public static void main(String[] args) {
        // Create Facebook feed
        FacebookFeed facebookFeed = new FacebookFeed();
        
        // Add sample posts
        facebookFeed.addPost(new Post("1", "Alice", "Just had an amazing coffee!"));
        facebookFeed.addPost(new Post("2", "Bob", "Working on a new project"));
        facebookFeed.addPost(new Post("3", "Alice", "Beautiful sunset today"));
        facebookFeed.addPost(new Post("4", "Charlie", "Learning design patterns"));
        facebookFeed.addPost(new Post("5", "Bob", "Finished my morning run"));
        
        // Simulate likes
        try {
            Thread.sleep(100); // Small delay for different timestamps
        } catch (InterruptedException e) {}
        
        // Set some likes to test popularity sorting
        setPostLikes(facebookFeed, "1", 15);
        setPostLikes(facebookFeed, "2", 8);
        setPostLikes(facebookFeed, "3", 23);
        setPostLikes(facebookFeed, "4", 12);
        setPostLikes(facebookFeed, "5", 5);
        
        FeedReader reader = new FeedReader();
        
        // Display chronological feed
        FeedIterator chronoIterator = facebookFeed.createChronologicalIterator();
        reader.displayFeed(chronoIterator, "Chronological", 5);
        
        // Display popularity feed
        FeedIterator popularityIterator = facebookFeed.createPopularityIterator();
        reader.displayFeed(popularityIterator, "Most Popular", 5);
        
        // Display posts by specific author
        reader.searchAndDisplay(facebookFeed, "Alice");
        reader.searchAndDisplay(facebookFeed, "Bob");
        reader.searchAndDisplay(facebookFeed, "David"); // No posts
        
        // Demonstrate iterator independence
        System.out.println("=== Demonstrating Iterator Independence ===");
        FeedIterator iter1 = facebookFeed.createChronologicalIterator();
        FeedIterator iter2 = facebookFeed.createChronologicalIterator();
        
        System.out.println("Iterator 1 - First post: " + iter1.next().getAuthor());
        System.out.println("Iterator 1 - Second post: " + iter1.next().getAuthor());
        
        System.out.println("Iterator 2 - First post: " + iter2.next().getAuthor());
        System.out.println("Iterator 2 - Second post: " + iter2.next().getAuthor());
        
        // Reset and iterate again
        iter1.reset();
        System.out.println("Iterator 1 after reset - First post: " + iter1.next().getAuthor());
        
        System.out.println("\nIterator Pattern Benefits:");
        System.out.println("- Multiple traversal algorithms for same data");
        System.out.println("- Independent iterator instances");
        System.out.println("- Encapsulated traversal logic");
        System.out.println("- Easy to add new iteration strategies");
    }
    
    private static void setPostLikes(FacebookFeed feed, String postId, int likes) {
        // This is a simplified way to set likes for demo purposes
        // In real implementation, you'd have proper methods
        FeedIterator iterator = feed.createChronologicalIterator();
        while (iterator.hasNext()) {
            Post post = iterator.next();
            if (post.getId().equals(postId)) {
                post.setLikes(likes);
                break;
            }
        }
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Iterator Pattern** provides uniform way to traverse collections
2. **Encapsulates traversal logic** và hides internal structure
3. **Supports multiple traversal algorithms** cho same collection
4. **Enables concurrent iteration** với independent iterators

### So sánh với patterns khác
| Pattern | Purpose | Focus |
|---------|---------|-------|
| **Iterator** | Traverse collection elements | Sequential access |
| **Visitor** | Perform operations on elements | Operations on structure |
| **Composite** | Treat individual/group uniformly | Hierarchical structures |
| **Command** | Encapsulate requests | Individual operations |

### Best Practices
- **Implement fail-fast behavior** cho concurrent modifications
- **Provide multiple iterator types** cho different traversal needs
- **Use generic types** cho type safety
- **Consider lazy evaluation** cho large collections
- **Document iterator behavior** và thread safety

---

**Tiếp theo:** [Mediator](mediator.md) - Quản lý giao tiếp phức tạp giữa objects
