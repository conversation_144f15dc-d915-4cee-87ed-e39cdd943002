# Template Method Pattern

> **Behavioral Pattern** - <PERSON><PERSON><PERSON> nghĩa khung của thuật toán trong superclass, cho phép subclasses override các bước cụ thể

## 📋 <PERSON><PERSON><PERSON> l<PERSON>

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [C<PERSON>u trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON><PERSON><PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Đ<PERSON><PERSON> nghĩa
Template Method là một **behavioral design pattern** **đ<PERSON><PERSON> nghĩa skeleton của algorithm trong superclass** nhưng cho phép subclasses override specific steps của algorithm mà không thay đổi structure.

### <PERSON><PERSON><PERSON> đích chính
- **Define algorithm structure** trong base class
- **Allow subclasses** to override specific steps
- **Promote code reuse** và consistency
- **Control algorithm flow** từ parent class

### Tên gọi khác
- **Algorithm Skeleton Pattern**
- **Framework Pattern**

### Ví dụ thực tế
Giống như **recipe nấu ăn**: có các bước cơ bản (chuẩn bị nguyên liệu, nấu, trang trí, dọn dẹp) nhưng mỗi món ăn có cách thực hiện khác nhau cho từng bước.

---

## Vấn đề

### Tình huống thực tế
Bạn đang phát triển data mining application cho different file formats:

```java
public class CSVDataMiner {
    public void mineData(String filePath) {
        // Step 1: Open file
        System.out.println("Opening CSV file: " + filePath);
        
        // Step 2: Extract data
        System.out.println("Parsing CSV data");
        
        // Step 3: Process data
        System.out.println("Processing CSV data");
        
        // Step 4: Generate report
        System.out.println("Generating CSV report");
        
        // Step 5: Close file
        System.out.println("Closing CSV file");
    }
}

public class XMLDataMiner {
    public void mineData(String filePath) {
        // Step 1: Open file
        System.out.println("Opening XML file: " + filePath);
        
        // Step 2: Extract data
        System.out.println("Parsing XML data");
        
        // Step 3: Process data
        System.out.println("Processing XML data");
        
        // Step 4: Generate report
        System.out.println("Generating XML report");
        
        // Step 5: Close file
        System.out.println("Closing XML file");
    }
}
```

### Vấn đề phát sinh

#### 1. Code duplication
```java
// Same algorithm structure repeated
public class PDFDataMiner {
    public void mineData(String filePath) {
        // Same steps as CSV and XML
        System.out.println("Opening PDF file: " + filePath);    // Step 1 - similar
        System.out.println("Parsing PDF data");                // Step 2 - different implementation
        System.out.println("Processing PDF data");             // Step 3 - similar
        System.out.println("Generating PDF report");           // Step 4 - different implementation
        System.out.println("Closing PDF file");                // Step 5 - similar
    }
}
```

#### 2. Inconsistent algorithm flow
```java
public class JSONDataMiner {
    public void mineData(String filePath) {
        // Different order of steps!
        System.out.println("Processing JSON data");  // Step 3 first?
        System.out.println("Opening JSON file: " + filePath);  // Step 1 later?
        System.out.println("Parsing JSON data");     // Step 2
        System.out.println("Generating JSON report"); // Step 4
        // Missing step 5!
    }
}
```

#### 3. Hard to maintain
```java
// Adding new step requires changing all classes
public void mineData(String filePath) {
    // Step 1: Open file
    // Step 2: Validate file format  // New step - add to all classes!
    // Step 3: Extract data
    // Step 4: Process data
    // Step 5: Generate report
    // Step 6: Close file
}
```

#### 4. No shared behavior
```java
// Common operations duplicated everywhere
private void logOperation(String operation) {
    System.out.println("[" + new Date() + "] " + operation);
}

// This method appears in every data miner class!
```

---

## Giải pháp

### Ý tưởng cốt lõi
Template Method pattern đề xuất **break down algorithm thành series of steps**, turn các steps này thành methods, và put series of calls đến các methods này vào single template method.

### Cách hoạt động

#### 1. Abstract Template Class
```java
public abstract class DataMiner {
    
    // Template method - defines algorithm skeleton
    public final void mineData(String filePath) {
        openFile(filePath);
        byte[] rawData = extractData();
        Object data = parseData(rawData);
        Object analysis = analyzeData(data);
        sendReport(analysis);
        closeFile();
    }
    
    // Concrete methods - same for all subclasses
    protected void openFile(String filePath) {
        System.out.println("Opening file: " + filePath);
    }
    
    protected void closeFile() {
        System.out.println("Closing file");
    }
    
    // Abstract methods - must be implemented by subclasses
    protected abstract byte[] extractData();
    protected abstract Object parseData(byte[] rawData);
    
    // Hook methods - optional override
    protected Object analyzeData(Object data) {
        System.out.println("Performing default data analysis");
        return data;
    }
    
    protected void sendReport(Object analysis) {
        System.out.println("Sending report via email");
    }
}
```

#### 2. Concrete Implementations
```java
public class CSVDataMiner extends DataMiner {
    
    @Override
    protected byte[] extractData() {
        System.out.println("Extracting data from CSV file");
        return "csv,data,here".getBytes();
    }
    
    @Override
    protected Object parseData(byte[] rawData) {
        System.out.println("Parsing CSV data");
        String csvData = new String(rawData);
        return csvData.split(",");
    }
    
    @Override
    protected Object analyzeData(Object data) {
        System.out.println("Performing CSV-specific analysis");
        String[] csvArray = (String[]) data;
        return "CSV Analysis: " + csvArray.length + " columns found";
    }
}

public class XMLDataMiner extends DataMiner {
    
    @Override
    protected byte[] extractData() {
        System.out.println("Extracting data from XML file");
        return "<xml><data>content</data></xml>".getBytes();
    }
    
    @Override
    protected Object parseData(byte[] rawData) {
        System.out.println("Parsing XML data using DOM parser");
        String xmlData = new String(rawData);
        return xmlData; // Simplified - would use actual XML parser
    }
    
    @Override
    protected Object analyzeData(Object data) {
        System.out.println("Performing XML-specific analysis");
        String xmlData = (String) data;
        return "XML Analysis: " + xmlData.length() + " characters processed";
    }
    
    @Override
    protected void sendReport(Object analysis) {
        System.out.println("Sending XML report via web service");
        System.out.println("Report: " + analysis);
    }
}

public class PDFDataMiner extends DataMiner {
    
    @Override
    protected byte[] extractData() {
        System.out.println("Extracting data from PDF file using OCR");
        return "pdf extracted text".getBytes();
    }
    
    @Override
    protected Object parseData(byte[] rawData) {
        System.out.println("Parsing PDF text data");
        return new String(rawData);
    }
    
    // Uses default analyzeData() - no override needed
    
    @Override
    protected void sendReport(Object analysis) {
        System.out.println("Saving PDF report to database");
        System.out.println("Report: " + analysis);
    }
}
```

#### 3. Usage
```java
public class DataMiningApp {
    public static void main(String[] args) {
        System.out.println("=== CSV Data Mining ===");
        DataMiner csvMiner = new CSVDataMiner();
        csvMiner.mineData("data.csv");
        
        System.out.println("\n=== XML Data Mining ===");
        DataMiner xmlMiner = new XMLDataMiner();
        xmlMiner.mineData("data.xml");
        
        System.out.println("\n=== PDF Data Mining ===");
        DataMiner pdfMiner = new PDFDataMiner();
        pdfMiner.mineData("document.pdf");
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
AbstractClass
├── + templateMethod(): void (final)
├── + primitiveOperation1(): void (abstract)
├── + primitiveOperation2(): void (abstract)
├── + hook(): void (optional override)
│
ConcreteClass extends AbstractClass
├── + primitiveOperation1(): void
├── + primitiveOperation2(): void
├── + hook(): void (optional)
```

### Các thành phần chính

#### 1. Abstract Class
- **Vai trò:** Defines template method và declares abstract primitive operations
- **Đặc điểm:** Template method is usually final

#### 2. Concrete Class
- **Vai trò:** Implements abstract primitive operations
- **Đặc điểm:** Can override hook methods

#### 3. Template Method
- **Vai trò:** Defines algorithm skeleton
- **Đặc điểm:** Calls primitive operations in specific order

#### 4. Primitive Operations
- **Abstract:** Must be implemented by subclasses
- **Concrete:** Default implementation in abstract class
- **Hook:** Optional override points

---

## Cách triển khai

### Bước 1: Identify algorithm steps
```java
// Game AI algorithm: Collect resources, Build units, Attack, Defend
```

### Bước 2: Abstract template class
```java
public abstract class GameAI {
    
    // Template method
    public final void takeTurn() {
        collectResources();
        buildStructures();
        buildUnits();
        attack();
        
        // Hook method
        if (shouldDefend()) {
            defend();
        }
    }
    
    // Abstract methods - must implement
    protected abstract void collectResources();
    protected abstract void buildUnits();
    
    // Concrete methods - default implementation
    protected void buildStructures() {
        System.out.println("Building basic structures");
    }
    
    protected void attack() {
        System.out.println("Performing basic attack");
    }
    
    // Hook methods - optional override
    protected boolean shouldDefend() {
        return true;
    }
    
    protected void defend() {
        System.out.println("Performing basic defense");
    }
}
```

### Bước 3: Concrete implementations
```java
public class AggressiveAI extends GameAI {
    
    @Override
    protected void collectResources() {
        System.out.println("Quickly collecting resources for military");
    }
    
    @Override
    protected void buildUnits() {
        System.out.println("Building many offensive units");
    }
    
    @Override
    protected void attack() {
        System.out.println("Launching aggressive attack on all fronts");
    }
    
    @Override
    protected boolean shouldDefend() {
        return false; // Always attack, never defend
    }
}

public class DefensiveAI extends GameAI {
    
    @Override
    protected void collectResources() {
        System.out.println("Steadily collecting resources");
    }
    
    @Override
    protected void buildUnits() {
        System.out.println("Building defensive units and fortifications");
    }
    
    @Override
    protected void buildStructures() {
        System.out.println("Building defensive walls and towers");
    }
    
    @Override
    protected void attack() {
        System.out.println("Launching careful, strategic attack");
    }
    
    @Override
    protected void defend() {
        System.out.println("Strengthening defenses and fortifications");
    }
}

public class BalancedAI extends GameAI {
    
    @Override
    protected void collectResources() {
        System.out.println("Collecting resources efficiently");
    }
    
    @Override
    protected void buildUnits() {
        System.out.println("Building balanced mix of units");
    }
    
    @Override
    protected boolean shouldDefend() {
        // Defend based on some game state
        return Math.random() > 0.5;
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Code reuse
```java
// Common algorithm structure shared
public abstract class SortingAlgorithm {
    public final void sort(int[] array) {
        if (shouldPreprocess()) {
            preprocess(array);
        }
        performSort(array);        // Different for each algorithm
        if (shouldPostprocess()) {
            postprocess(array);
        }
    }
    
    protected abstract void performSort(int[] array);
    // Common preprocessing/postprocessing shared
}
```

#### 2. Control algorithm structure
```java
// Parent class controls flow
public final void processOrder() {
    validateOrder();    // Always first
    calculatePrice();   // Always second
    processPayment();   // Always third
    shipOrder();        // Always last
    // Order cannot be changed by subclasses
}
```

#### 3. Hollywood Principle
```java
// "Don't call us, we'll call you"
// Parent class calls subclass methods, not vice versa
public final void execute() {
    step1();  // Parent calls
    step2();  // Parent calls
    step3();  // Parent calls
}
```

### ❌ Nhược điểm

#### 1. Limited flexibility
```java
// Cannot change algorithm structure
public final void rigidAlgorithm() {
    step1();  // Cannot skip
    step2();  // Cannot reorder
    step3();  // Cannot add steps between
}
```

#### 2. Inheritance dependency
```java
// Must use inheritance, cannot use composition
// Violates "favor composition over inheritance"
```

#### 3. Liskov Substitution Principle violations
```java
// Subclass might not be proper substitute
public class BrokenSubclass extends Template {
    @Override
    protected void step1() {
        throw new UnsupportedOperationException(); // Violates LSP
    }
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Multiple classes có similar algorithms
```java
// Different sorting algorithms with same structure
public abstract class SortingAlgorithm {
    public final void sort(int[] array) {
        // Common structure for all sorting algorithms
    }
}
```

#### 2. Want to control algorithm structure
```java
// Framework controlling application flow
public abstract class WebFramework {
    public final void handleRequest() {
        authenticate();     // Framework controls order
        authorize();
        processRequest();   // Application-specific
        generateResponse(); // Application-specific
        cleanup();          // Framework controls
    }
}
```

#### 3. Code duplication in similar classes
```java
// Common steps with different implementations
public abstract class ReportGenerator {
    public final void generateReport() {
        gatherData();       // Different sources
        formatData();       // Different formats
        saveReport();       // Common implementation
    }
}
```

### ❌ Không nên sử dụng khi:

#### 1. Algorithm structure varies significantly
```java
// If algorithms have completely different flows
// Template Method won't help
```

#### 2. Need runtime algorithm changes
```java
// Use Strategy pattern instead
// Template Method fixes algorithm at compile time
```

---

## Ví dụ thực tế

### Ví dụ 1: Social Media Authentication

```java
// Abstract template for social media authentication
public abstract class SocialMediaAuth {
    
    // Template method - defines authentication flow
    public final boolean authenticate(String username, String password) {
        System.out.println("Starting authentication process...");
        
        // Step 1: Validate input
        if (!validateInput(username, password)) {
            System.out.println("Invalid input provided");
            return false;
        }
        
        // Step 2: Connect to social media API
        if (!connectToAPI()) {
            System.out.println("Failed to connect to API");
            return false;
        }
        
        // Step 3: Send authentication request
        String token = sendAuthRequest(username, password);
        if (token == null) {
            System.out.println("Authentication failed");
            return false;
        }
        
        // Step 4: Validate token
        if (!validateToken(token)) {
            System.out.println("Invalid token received");
            return false;
        }
        
        // Step 5: Store user session
        storeUserSession(username, token);
        
        // Step 6: Optional post-authentication actions
        if (shouldPerformPostAuth()) {
            performPostAuthActions(username);
        }
        
        System.out.println("Authentication successful!");
        return true;
    }
    
    // Concrete methods - common implementation
    protected boolean validateInput(String username, String password) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        if (password == null || password.length() < 6) {
            return false;
        }
        return true;
    }
    
    protected void storeUserSession(String username, String token) {
        System.out.println("Storing session for user: " + username);
        // Common session storage logic
    }
    
    // Abstract methods - must be implemented by subclasses
    protected abstract boolean connectToAPI();
    protected abstract String sendAuthRequest(String username, String password);
    protected abstract boolean validateToken(String token);
    
    // Hook methods - optional override
    protected boolean shouldPerformPostAuth() {
        return false;
    }
    
    protected void performPostAuthActions(String username) {
        // Default: do nothing
    }
}

// Facebook authentication implementation
public class FacebookAuth extends SocialMediaAuth {
    
    @Override
    protected boolean connectToAPI() {
        System.out.println("Connecting to Facebook Graph API...");
        // Simulate API connection
        try {
            Thread.sleep(1000);
            System.out.println("Connected to Facebook API");
            return true;
        } catch (InterruptedException e) {
            return false;
        }
    }
    
    @Override
    protected String sendAuthRequest(String username, String password) {
        System.out.println("Sending Facebook authentication request for: " + username);
        // Simulate Facebook-specific authentication
        if ("facebook_user".equals(username) && "facebook_pass".equals(password)) {
            return "fb_token_" + System.currentTimeMillis();
        }
        return null;
    }
    
    @Override
    protected boolean validateToken(String token) {
        System.out.println("Validating Facebook token: " + token);
        // Facebook-specific token validation
        return token != null && token.startsWith("fb_token_");
    }
    
    @Override
    protected boolean shouldPerformPostAuth() {
        return true; // Facebook requires additional setup
    }
    
    @Override
    protected void performPostAuthActions(String username) {
        System.out.println("Fetching Facebook user profile and friends list");
        System.out.println("Setting up Facebook-specific permissions");
    }
}

// Google authentication implementation
public class GoogleAuth extends SocialMediaAuth {
    
    @Override
    protected boolean connectToAPI() {
        System.out.println("Connecting to Google OAuth 2.0 API...");
        try {
            Thread.sleep(800);
            System.out.println("Connected to Google API");
            return true;
        } catch (InterruptedException e) {
            return false;
        }
    }
    
    @Override
    protected String sendAuthRequest(String username, String password) {
        System.out.println("Sending Google OAuth request for: " + username);
        if ("google_user".equals(username) && "google_pass".equals(password)) {
            return "google_token_" + System.currentTimeMillis();
        }
        return null;
    }
    
    @Override
    protected boolean validateToken(String token) {
        System.out.println("Validating Google OAuth token: " + token);
        return token != null && token.startsWith("google_token_");
    }
    
    @Override
    protected boolean shouldPerformPostAuth() {
        return true;
    }
    
    @Override
    protected void performPostAuthActions(String username) {
        System.out.println("Syncing Google account data");
        System.out.println("Setting up Google Drive access");
    }
}

// Twitter authentication implementation
public class TwitterAuth extends SocialMediaAuth {
    
    @Override
    protected boolean connectToAPI() {
        System.out.println("Connecting to Twitter API v2...");
        try {
            Thread.sleep(600);
            System.out.println("Connected to Twitter API");
            return true;
        } catch (InterruptedException e) {
            return false;
        }
    }
    
    @Override
    protected String sendAuthRequest(String username, String password) {
        System.out.println("Sending Twitter authentication request for: " + username);
        if ("twitter_user".equals(username) && "twitter_pass".equals(password)) {
            return "twitter_token_" + System.currentTimeMillis();
        }
        return null;
    }
    
    @Override
    protected boolean validateToken(String token) {
        System.out.println("Validating Twitter token: " + token);
        return token != null && token.startsWith("twitter_token_");
    }
    
    // Uses default post-auth behavior (no override)
}

// Authentication manager
public class AuthenticationManager {
    private Map<String, SocialMediaAuth> authProviders;
    
    public AuthenticationManager() {
        authProviders = new HashMap<>();
        authProviders.put("facebook", new FacebookAuth());
        authProviders.put("google", new GoogleAuth());
        authProviders.put("twitter", new TwitterAuth());
    }
    
    public boolean authenticateUser(String provider, String username, String password) {
        SocialMediaAuth auth = authProviders.get(provider.toLowerCase());
        if (auth == null) {
            System.out.println("Unsupported authentication provider: " + provider);
            return false;
        }
        
        System.out.println("=== Authenticating with " + provider.toUpperCase() + " ===");
        return auth.authenticate(username, password);
    }
    
    public void addAuthProvider(String name, SocialMediaAuth provider) {
        authProviders.put(name.toLowerCase(), provider);
    }
}

// Usage
public class SocialMediaAuthDemo {
    public static void main(String[] args) {
        AuthenticationManager authManager = new AuthenticationManager();
        
        // Test Facebook authentication
        System.out.println("Testing Facebook Authentication:");
        boolean fbResult = authManager.authenticateUser("facebook", "facebook_user", "facebook_pass");
        System.out.println("Facebook auth result: " + fbResult);
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // Test Google authentication
        System.out.println("Testing Google Authentication:");
        boolean googleResult = authManager.authenticateUser("google", "google_user", "google_pass");
        System.out.println("Google auth result: " + googleResult);
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // Test Twitter authentication
        System.out.println("Testing Twitter Authentication:");
        boolean twitterResult = authManager.authenticateUser("twitter", "twitter_user", "twitter_pass");
        System.out.println("Twitter auth result: " + twitterResult);
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // Test failed authentication
        System.out.println("Testing Failed Authentication:");
        boolean failedResult = authManager.authenticateUser("facebook", "wrong_user", "wrong_pass");
        System.out.println("Failed auth result: " + failedResult);
        
        System.out.println("\nTemplate Method Pattern Benefits:");
        System.out.println("- Consistent authentication flow across all providers");
        System.out.println("- Code reuse for common validation and session management");
        System.out.println("- Easy to add new social media providers");
        System.out.println("- Controlled algorithm structure prevents implementation errors");
    }
}
```

### Ví dụ 2: Document Processing Pipeline

```java
// Abstract document processor
public abstract class DocumentProcessor {
    
    // Template method - defines document processing pipeline
    public final ProcessingResult processDocument(String filePath) {
        System.out.println("Starting document processing pipeline...");
        
        try {
            // Step 1: Validate file
            if (!validateFile(filePath)) {
                return ProcessingResult.failure("File validation failed");
            }
            
            // Step 2: Load document
            Document document = loadDocument(filePath);
            if (document == null) {
                return ProcessingResult.failure("Failed to load document");
            }
            
            // Step 3: Extract content
            String content = extractContent(document);
            if (content == null || content.isEmpty()) {
                return ProcessingResult.failure("No content extracted");
            }
            
            // Step 4: Process content
            String processedContent = processContent(content);
            
            // Step 5: Optional content enhancement
            if (shouldEnhanceContent()) {
                processedContent = enhanceContent(processedContent);
            }
            
            // Step 6: Generate output
            String output = generateOutput(processedContent);
            
            // Step 7: Save result
            String outputPath = saveResult(output, filePath);
            
            // Step 8: Optional cleanup
            if (shouldCleanup()) {
                cleanup(document, filePath);
            }
            
            System.out.println("Document processing completed successfully");
            return ProcessingResult.success(outputPath);
            
        } catch (Exception e) {
            System.err.println("Error during processing: " + e.getMessage());
            return ProcessingResult.failure("Processing error: " + e.getMessage());
        }
    }
    
    // Concrete methods - common implementation
    protected boolean validateFile(String filePath) {
        System.out.println("Validating file: " + filePath);
        // Common file validation logic
        return filePath != null && !filePath.trim().isEmpty();
    }
    
    protected String saveResult(String output, String originalPath) {
        String outputPath = generateOutputPath(originalPath);
        System.out.println("Saving result to: " + outputPath);
        // Common save logic
        return outputPath;
    }
    
    protected String generateOutputPath(String originalPath) {
        return originalPath.replaceAll("\\.[^.]+$", "_processed.txt");
    }
    
    // Abstract methods - must be implemented
    protected abstract Document loadDocument(String filePath);
    protected abstract String extractContent(Document document);
    protected abstract String processContent(String content);
    protected abstract String generateOutput(String processedContent);
    
    // Hook methods - optional override
    protected boolean shouldEnhanceContent() {
        return false;
    }
    
    protected String enhanceContent(String content) {
        return content; // Default: no enhancement
    }
    
    protected boolean shouldCleanup() {
        return true;
    }
    
    protected void cleanup(Document document, String filePath) {
        System.out.println("Performing cleanup operations");
    }
}

// PDF processor implementation
public class PDFProcessor extends DocumentProcessor {
    
    @Override
    protected Document loadDocument(String filePath) {
        System.out.println("Loading PDF document using PDF library");
        // Simulate PDF loading
        return new Document("PDF", filePath);
    }
    
    @Override
    protected String extractContent(Document document) {
        System.out.println("Extracting text from PDF using OCR");
        // Simulate PDF text extraction
        return "PDF content extracted from " + document.getPath();
    }
    
    @Override
    protected String processContent(String content) {
        System.out.println("Processing PDF content - removing headers/footers");
        // PDF-specific processing
        return content.replaceAll("Header|Footer", "").trim();
    }
    
    @Override
    protected String generateOutput(String processedContent) {
        System.out.println("Generating formatted output for PDF content");
        return "=== PDF PROCESSING RESULT ===\n" + processedContent + "\n=== END ===";
    }
    
    @Override
    protected boolean shouldEnhanceContent() {
        return true; // PDFs often need enhancement
    }
    
    @Override
    protected String enhanceContent(String content) {
        System.out.println("Enhancing PDF content - fixing OCR errors");
        return content.replaceAll("\\s+", " ").trim();
    }
}

// Word processor implementation
public class WordProcessor extends DocumentProcessor {
    
    @Override
    protected Document loadDocument(String filePath) {
        System.out.println("Loading Word document using Apache POI");
        return new Document("DOCX", filePath);
    }
    
    @Override
    protected String extractContent(Document document) {
        System.out.println("Extracting text and formatting from Word document");
        return "Word content from " + document.getPath() + " with formatting preserved";
    }
    
    @Override
    protected String processContent(String content) {
        System.out.println("Processing Word content - preserving formatting");
        return content + " [Formatting preserved]";
    }
    
    @Override
    protected String generateOutput(String processedContent) {
        System.out.println("Generating rich text output");
        return "=== WORD PROCESSING RESULT ===\n" + processedContent + "\n=== END ===";
    }
    
    @Override
    protected boolean shouldEnhanceContent() {
        return false; // Word documents usually don't need enhancement
    }
}

// Plain text processor implementation
public class TextProcessor extends DocumentProcessor {
    
    @Override
    protected Document loadDocument(String filePath) {
        System.out.println("Loading plain text file");
        return new Document("TXT", filePath);
    }
    
    @Override
    protected String extractContent(Document document) {
        System.out.println("Reading plain text content");
        return "Plain text content from " + document.getPath();
    }
    
    @Override
    protected String processContent(String content) {
        System.out.println("Processing text - normalizing whitespace");
        return content.replaceAll("\\s+", " ").trim();
    }
    
    @Override
    protected String generateOutput(String processedContent) {
        System.out.println("Generating clean text output");
        return "=== TEXT PROCESSING RESULT ===\n" + processedContent + "\n=== END ===";
    }
    
    @Override
    protected boolean shouldCleanup() {
        return false; // Text files don't need cleanup
    }
}

// Supporting classes
class Document {
    private String type;
    private String path;
    
    public Document(String type, String path) {
        this.type = type;
        this.path = path;
    }
    
    public String getType() { return type; }
    public String getPath() { return path; }
}

class ProcessingResult {
    private boolean success;
    private String message;
    private String outputPath;
    
    private ProcessingResult(boolean success, String message, String outputPath) {
        this.success = success;
        this.message = message;
        this.outputPath = outputPath;
    }
    
    public static ProcessingResult success(String outputPath) {
        return new ProcessingResult(true, "Success", outputPath);
    }
    
    public static ProcessingResult failure(String message) {
        return new ProcessingResult(false, message, null);
    }
    
    public boolean isSuccess() { return success; }
    public String getMessage() { return message; }
    public String getOutputPath() { return outputPath; }
}

// Document processing factory
public class DocumentProcessorFactory {
    public static DocumentProcessor getProcessor(String filePath) {
        String extension = getFileExtension(filePath).toLowerCase();
        
        switch (extension) {
            case "pdf":
                return new PDFProcessor();
            case "doc":
            case "docx":
                return new WordProcessor();
            case "txt":
                return new TextProcessor();
            default:
                throw new IllegalArgumentException("Unsupported file type: " + extension);
        }
    }
    
    private static String getFileExtension(String filePath) {
        int lastDot = filePath.lastIndexOf('.');
        return lastDot > 0 ? filePath.substring(lastDot + 1) : "";
    }
}

// Usage
public class DocumentProcessingDemo {
    public static void main(String[] args) {
        String[] testFiles = {
            "document.pdf",
            "report.docx",
            "notes.txt"
        };
        
        for (String filePath : testFiles) {
            System.out.println("Processing: " + filePath);
            System.out.println("=".repeat(60));
            
            try {
                DocumentProcessor processor = DocumentProcessorFactory.getProcessor(filePath);
                ProcessingResult result = processor.processDocument(filePath);
                
                if (result.isSuccess()) {
                    System.out.println("✅ Success: " + result.getOutputPath());
                } else {
                    System.out.println("❌ Failed: " + result.getMessage());
                }
                
            } catch (Exception e) {
                System.out.println("❌ Error: " + e.getMessage());
            }
            
            System.out.println("\n");
        }
        
        System.out.println("Template Method Pattern Benefits:");
        System.out.println("- Consistent processing pipeline for all document types");
        System.out.println("- Code reuse for common operations (validation, saving)");
        System.out.println("- Controlled algorithm flow prevents implementation errors");
        System.out.println("- Easy to add new document types");
        System.out.println("- Optional steps handled via hook methods");
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Template Method** defines algorithm skeleton trong superclass
2. **Subclasses override specific steps** mà không thay đổi structure
3. **Promotes code reuse** và consistency
4. **Controls algorithm flow** từ parent class

### So sánh với patterns khác
| Pattern | Purpose | Flexibility |
|---------|---------|-------------|
| **Template Method** | Define algorithm skeleton | Fixed structure, variable steps |
| **Strategy** | Encapsulate algorithms | Complete algorithm replacement |
| **State** | Change behavior based on state | Behavior changes with state |
| **Command** | Encapsulate requests | Individual operation encapsulation |

### Best Practices
- **Keep template method final** để prevent override
- **Use meaningful method names** cho steps
- **Provide hook methods** cho optional behavior
- **Document** expected behavior của abstract methods
- **Consider** using Strategy pattern cho more flexibility

---

**Tiếp theo:** [Chain of Responsibility](chain-of-responsibility.md) - Xử lý yêu cầu theo chuỗi
