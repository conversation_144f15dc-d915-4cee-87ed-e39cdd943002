# Chain of Responsibility Pattern

> **Behavioral Pattern** - <PERSON> phép truyền yêu cầu dọc theo chuỗi các handlers cho đến khi có handler xử lý được

## 📋 <PERSON><PERSON><PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [Gi<PERSON>i pháp](#gi<PERSON>i-pháp)
4. [C<PERSON>u trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON>ợ<PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Chain of Responsibility là một **behavioral design pattern** cho phép **pass requests dọc theo chain of potential handlers** cho đến khi một trong số chúng handles request.

### <PERSON><PERSON><PERSON> đích chính
- **Decouple sender** khỏi receiver
- **Allow multiple objects** to handle request
- **Chain handlers** in any order
- **Dynamic handler configuration**

### Tên gọi khác
- **Chain of Command Pattern**
- **Responsibility Chain Pattern**

### Ví dụ thực tế
Giống như **customer support system**: câu hỏi đầu tiên đến support level 1, nếu không giải quyết được thì chuyển lên level 2, rồi level 3, cuối cùng đến manager.

---

## Vấn đề

### Tình huống thực tế
Bạn đang phát triển online ordering system với authentication và authorization:

```java
public class OrderProcessor {
    public void processOrder(Order order, User user) {
        // Authentication check
        if (user == null || !user.isAuthenticated()) {
            throw new SecurityException("User not authenticated");
        }
        
        // Authorization check
        if (!user.hasPermission("PLACE_ORDER")) {
            throw new SecurityException("User not authorized");
        }
        
        // Validation check
        if (order.getItems().isEmpty()) {
            throw new ValidationException("Order is empty");
        }
        
        // Inventory check
        for (Item item : order.getItems()) {
            if (!inventoryService.isAvailable(item)) {
                throw new InventoryException("Item not available: " + item.getName());
            }
        }
        
        // Payment check
        if (!paymentService.validatePayment(order.getPayment())) {
            throw new PaymentException("Invalid payment information");
        }
        
        // Process order
        orderService.processOrder(order);
    }
}
```

### Vấn đề phát sinh

#### 1. Tight coupling
```java
// OrderProcessor phải biết tất cả validation rules
public class OrderProcessor {
    private AuthenticationService authService;
    private AuthorizationService authzService;
    private ValidationService validationService;
    private InventoryService inventoryService;
    private PaymentService paymentService;
    
    // Tightly coupled to all services
}
```

#### 2. Rigid processing order
```java
// Fixed order of checks - hard to change
public void processOrder(Order order, User user) {
    // Always authentication first
    // Always authorization second
    // Always validation third
    // Cannot reorder or skip steps
}
```

#### 3. Difficult to extend
```java
// Adding new check requires modifying processOrder method
public void processOrder(Order order, User user) {
    // Existing checks...
    
    // New requirement: fraud detection
    if (!fraudDetectionService.isLegitimate(order)) {
        throw new FraudException("Suspicious order detected");
    }
    
    // Another new requirement: promotional validation
    if (!promotionService.validatePromotion(order)) {
        throw new PromotionException("Invalid promotion code");
    }
    
    // Method keeps growing!
}
```

#### 4. No conditional processing
```java
// Cannot skip certain checks based on conditions
// Cannot have different processing chains for different order types
// All orders go through same rigid process
```

---

## Giải pháp

### Ý tưởng cốt lõi
Chain of Responsibility pattern đề xuất **transform particular behaviors thành stand-alone objects** gọi là handlers. Pattern suggests linking các handlers thành chain. Mỗi linked handler có field để store reference đến next handler trong chain.

### Cách hoạt động

#### 1. Handler interface
```java
public interface OrderHandler {
    void setNext(OrderHandler handler);
    void handle(OrderRequest request);
}
```

#### 2. Abstract Handler
```java
public abstract class AbstractOrderHandler implements OrderHandler {
    private OrderHandler nextHandler;
    
    @Override
    public void setNext(OrderHandler handler) {
        this.nextHandler = handler;
    }
    
    @Override
    public void handle(OrderRequest request) {
        if (canHandle(request)) {
            doHandle(request);
        }
        
        if (nextHandler != null) {
            nextHandler.handle(request);
        }
    }
    
    protected abstract boolean canHandle(OrderRequest request);
    protected abstract void doHandle(OrderRequest request);
}
```

#### 3. Concrete Handlers
```java
public class AuthenticationHandler extends AbstractOrderHandler {
    
    @Override
    protected boolean canHandle(OrderRequest request) {
        return true; // Always check authentication
    }
    
    @Override
    protected void doHandle(OrderRequest request) {
        User user = request.getUser();
        if (user == null || !user.isAuthenticated()) {
            throw new SecurityException("User not authenticated");
        }
        System.out.println("✓ Authentication passed");
    }
}

public class AuthorizationHandler extends AbstractOrderHandler {
    
    @Override
    protected boolean canHandle(OrderRequest request) {
        return request.getUser() != null; // Only if user exists
    }
    
    @Override
    protected void doHandle(OrderRequest request) {
        User user = request.getUser();
        if (!user.hasPermission("PLACE_ORDER")) {
            throw new SecurityException("User not authorized to place orders");
        }
        System.out.println("✓ Authorization passed");
    }
}

public class ValidationHandler extends AbstractOrderHandler {
    
    @Override
    protected boolean canHandle(OrderRequest request) {
        return true; // Always validate
    }
    
    @Override
    protected void doHandle(OrderRequest request) {
        Order order = request.getOrder();
        if (order.getItems().isEmpty()) {
            throw new ValidationException("Order cannot be empty");
        }
        if (order.getTotalAmount() <= 0) {
            throw new ValidationException("Order total must be positive");
        }
        System.out.println("✓ Validation passed");
    }
}

public class InventoryHandler extends AbstractOrderHandler {
    
    @Override
    protected boolean canHandle(OrderRequest request) {
        return !request.getOrder().getItems().isEmpty();
    }
    
    @Override
    protected void doHandle(OrderRequest request) {
        for (Item item : request.getOrder().getItems()) {
            if (!isItemAvailable(item)) {
                throw new InventoryException("Item not available: " + item.getName());
            }
        }
        System.out.println("✓ Inventory check passed");
    }
    
    private boolean isItemAvailable(Item item) {
        // Simulate inventory check
        return item.getQuantity() > 0;
    }
}

public class PaymentHandler extends AbstractOrderHandler {
    
    @Override
    protected boolean canHandle(OrderRequest request) {
        return request.getOrder().getPayment() != null;
    }
    
    @Override
    protected void doHandle(OrderRequest request) {
        Payment payment = request.getOrder().getPayment();
        if (!isValidPayment(payment)) {
            throw new PaymentException("Invalid payment information");
        }
        System.out.println("✓ Payment validation passed");
    }
    
    private boolean isValidPayment(Payment payment) {
        // Simulate payment validation
        return payment.getCardNumber() != null && payment.getCardNumber().length() == 16;
    }
}
```

#### 4. Request object
```java
public class OrderRequest {
    private Order order;
    private User user;
    private Map<String, Object> context;
    
    public OrderRequest(Order order, User user) {
        this.order = order;
        this.user = user;
        this.context = new HashMap<>();
    }
    
    // Getters and setters
    public Order getOrder() { return order; }
    public User getUser() { return user; }
    public Map<String, Object> getContext() { return context; }
    
    public void setContextValue(String key, Object value) {
        context.put(key, value);
    }
    
    public Object getContextValue(String key) {
        return context.get(key);
    }
}
```

#### 5. Chain Builder
```java
public class OrderProcessingChain {
    
    public static OrderHandler createStandardChain() {
        OrderHandler authenticationHandler = new AuthenticationHandler();
        OrderHandler authorizationHandler = new AuthorizationHandler();
        OrderHandler validationHandler = new ValidationHandler();
        OrderHandler inventoryHandler = new InventoryHandler();
        OrderHandler paymentHandler = new PaymentHandler();
        
        // Build chain
        authenticationHandler.setNext(authorizationHandler);
        authorizationHandler.setNext(validationHandler);
        validationHandler.setNext(inventoryHandler);
        inventoryHandler.setNext(paymentHandler);
        
        return authenticationHandler;
    }
    
    public static OrderHandler createExpressChain() {
        // Simplified chain for express orders
        OrderHandler authenticationHandler = new AuthenticationHandler();
        OrderHandler paymentHandler = new PaymentHandler();
        
        authenticationHandler.setNext(paymentHandler);
        return authenticationHandler;
    }
}
```

#### 6. Usage
```java
public class OrderProcessor {
    private OrderHandler processingChain;
    
    public OrderProcessor() {
        this.processingChain = OrderProcessingChain.createStandardChain();
    }
    
    public void processOrder(Order order, User user) {
        OrderRequest request = new OrderRequest(order, user);
        
        try {
            processingChain.handle(request);
            System.out.println("✅ Order processed successfully!");
        } catch (Exception e) {
            System.out.println("❌ Order processing failed: " + e.getMessage());
            throw e;
        }
    }
    
    public void setProcessingChain(OrderHandler chain) {
        this.processingChain = chain;
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Handler (interface)
├── + setNext(Handler): void
├── + handle(Request): void
│
AbstractHandler implements Handler
├── - nextHandler: Handler
├── + setNext(Handler): void
├── + handle(Request): void
├── # canHandle(Request): boolean (abstract)
├── # doHandle(Request): void (abstract)
│
ConcreteHandlerA extends AbstractHandler
├── # canHandle(Request): boolean
├── # doHandle(Request): void
│
ConcreteHandlerB extends AbstractHandler
├── # canHandle(Request): boolean
├── # doHandle(Request): void
```

### Các thành phần chính

#### 1. Handler Interface
- **Vai trò:** Declares method cho handling requests
- **Đặc điểm:** Usually has method để set next handler

#### 2. Abstract Handler
- **Vai trò:** Implements default chaining behavior
- **Đặc điểm:** Contains reference to next handler

#### 3. Concrete Handlers
- **Vai trò:** Handle requests they're responsible for
- **Đặc điểm:** Can decide whether to pass request to next handler

#### 4. Client
- **Vai trò:** Composes chains và initiates requests
- **Đặc điểm:** Can send request to any handler in chain

---

## Cách triển khai

### Bước 1: Define Handler interface
```java
public interface SupportHandler {
    void setNext(SupportHandler handler);
    void handleRequest(SupportRequest request);
}
```

### Bước 2: Abstract Handler implementation
```java
public abstract class AbstractSupportHandler implements SupportHandler {
    private SupportHandler nextHandler;
    
    @Override
    public void setNext(SupportHandler handler) {
        this.nextHandler = handler;
    }
    
    @Override
    public void handleRequest(SupportRequest request) {
        if (canHandle(request)) {
            doHandle(request);
            return; // Stop chain if handled
        }
        
        if (nextHandler != null) {
            nextHandler.handleRequest(request);
        } else {
            System.out.println("No handler available for: " + request.getDescription());
        }
    }
    
    protected abstract boolean canHandle(SupportRequest request);
    protected abstract void doHandle(SupportRequest request);
}
```

### Bước 3: Concrete Handlers
```java
public class Level1SupportHandler extends AbstractSupportHandler {
    
    @Override
    protected boolean canHandle(SupportRequest request) {
        return request.getPriority() == Priority.LOW;
    }
    
    @Override
    protected void doHandle(SupportRequest request) {
        System.out.println("Level 1 Support handling: " + request.getDescription());
        request.setStatus("Resolved by Level 1");
    }
}

public class Level2SupportHandler extends AbstractSupportHandler {
    
    @Override
    protected boolean canHandle(SupportRequest request) {
        return request.getPriority() == Priority.MEDIUM;
    }
    
    @Override
    protected void doHandle(SupportRequest request) {
        System.out.println("Level 2 Support handling: " + request.getDescription());
        request.setStatus("Resolved by Level 2");
    }
}

public class ManagerHandler extends AbstractSupportHandler {
    
    @Override
    protected boolean canHandle(SupportRequest request) {
        return request.getPriority() == Priority.HIGH;
    }
    
    @Override
    protected void doHandle(SupportRequest request) {
        System.out.println("Manager handling: " + request.getDescription());
        request.setStatus("Resolved by Manager");
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Decoupling sender và receiver
```java
// Client doesn't need to know which handler will process request
client.sendRequest(request); // Don't care who handles it
```

#### 2. Dynamic chain configuration
```java
// Can build different chains at runtime
SupportHandler chain1 = buildStandardChain();
SupportHandler chain2 = buildExpressChain();
SupportHandler chain3 = buildVIPChain();
```

#### 3. Single Responsibility Principle
```java
// Each handler has single responsibility
public class AuthenticationHandler {
    // Only handles authentication
}

public class AuthorizationHandler {
    // Only handles authorization
}
```

#### 4. Open/Closed Principle
```java
// Easy to add new handlers without modifying existing code
public class FraudDetectionHandler extends AbstractOrderHandler {
    // New handler - no existing code modified
}
```

### ❌ Nhược điểm

#### 1. No guarantee of handling
```java
// Request might not be handled by any handler
// Need to handle this case explicitly
```

#### 2. Performance concerns
```java
// Long chains can impact performance
// Request travels through entire chain
```

#### 3. Debugging difficulty
```java
// Hard to observe chain execution
// Difficult to debug which handler processed request
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Multiple objects can handle request
```java
// Support system: Level1, Level2, Manager
// Approval system: Supervisor, Manager, Director
```

#### 2. Set of handlers changes dynamically
```java
// Different processing chains for different scenarios
// Runtime configuration of handlers
```

#### 3. Want to decouple sender from receiver
```java
// Client doesn't need to know specific handler
// Handlers can be added/removed without affecting client
```

### ❌ Không nên sử dụng khi:

#### 1. Only one handler for request
```java
// If only one object can handle request
// Direct method call is simpler
```

#### 2. Handler order is critical và fixed
```java
// If processing order cannot change
// Template Method might be better
```

---

## Ví dụ thực tế

### Ví dụ 1: HTTP Request Processing Pipeline

```java
// HTTP Request object
public class HttpRequest {
    private String method;
    private String path;
    private Map<String, String> headers;
    private Map<String, String> parameters;
    private String body;
    private Map<String, Object> attributes;
    
    public HttpRequest(String method, String path) {
        this.method = method;
        this.path = path;
        this.headers = new HashMap<>();
        this.parameters = new HashMap<>();
        this.attributes = new HashMap<>();
    }
    
    // Getters and setters
    public String getMethod() { return method; }
    public String getPath() { return path; }
    public Map<String, String> getHeaders() { return headers; }
    public Map<String, String> getParameters() { return parameters; }
    public String getBody() { return body; }
    public void setBody(String body) { this.body = body; }
    
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    public Object getAttribute(String key) {
        return attributes.get(key);
    }
    
    public void addHeader(String key, String value) {
        headers.put(key, value);
    }
    
    public String getHeader(String key) {
        return headers.get(key);
    }
}

// HTTP Response object
public class HttpResponse {
    private int statusCode;
    private String statusMessage;
    private Map<String, String> headers;
    private String body;
    
    public HttpResponse() {
        this.statusCode = 200;
        this.statusMessage = "OK";
        this.headers = new HashMap<>();
    }
    
    // Getters and setters
    public int getStatusCode() { return statusCode; }
    public void setStatusCode(int statusCode) { this.statusCode = statusCode; }
    public String getStatusMessage() { return statusMessage; }
    public void setStatusMessage(String statusMessage) { this.statusMessage = statusMessage; }
    public String getBody() { return body; }
    public void setBody(String body) { this.body = body; }
    
    public void addHeader(String key, String value) {
        headers.put(key, value);
    }
}

// Request processor interface
public interface RequestProcessor {
    void setNext(RequestProcessor processor);
    HttpResponse process(HttpRequest request);
}

// Abstract processor
public abstract class AbstractRequestProcessor implements RequestProcessor {
    private RequestProcessor nextProcessor;
    
    @Override
    public void setNext(RequestProcessor processor) {
        this.nextProcessor = processor;
    }
    
    @Override
    public HttpResponse process(HttpRequest request) {
        try {
            // Process current step
            HttpResponse response = doProcess(request);
            
            // If response is set, return it (short-circuit)
            if (response != null) {
                return response;
            }
            
            // Continue to next processor
            if (nextProcessor != null) {
                return nextProcessor.process(request);
            }
            
            // End of chain - return default response
            return createDefaultResponse();
            
        } catch (Exception e) {
            return createErrorResponse(e);
        }
    }
    
    protected abstract HttpResponse doProcess(HttpRequest request);
    
    protected HttpResponse createDefaultResponse() {
        HttpResponse response = new HttpResponse();
        response.setStatusCode(404);
        response.setStatusMessage("Not Found");
        response.setBody("No handler found for this request");
        return response;
    }
    
    protected HttpResponse createErrorResponse(Exception e) {
        HttpResponse response = new HttpResponse();
        response.setStatusCode(500);
        response.setStatusMessage("Internal Server Error");
        response.setBody("Error: " + e.getMessage());
        return response;
    }
}

// Authentication processor
public class AuthenticationProcessor extends AbstractRequestProcessor {
    
    @Override
    protected HttpResponse doProcess(HttpRequest request) {
        System.out.println("🔐 Processing authentication...");
        
        String authHeader = request.getHeader("Authorization");
        
        if (authHeader == null) {
            // No auth header - return 401
            HttpResponse response = new HttpResponse();
            response.setStatusCode(401);
            response.setStatusMessage("Unauthorized");
            response.setBody("Authentication required");
            return response;
        }
        
        if (!authHeader.startsWith("Bearer ")) {
            HttpResponse response = new HttpResponse();
            response.setStatusCode(401);
            response.setStatusMessage("Unauthorized");
            response.setBody("Invalid authentication format");
            return response;
        }
        
        String token = authHeader.substring(7);
        if (!isValidToken(token)) {
            HttpResponse response = new HttpResponse();
            response.setStatusCode(401);
            response.setStatusMessage("Unauthorized");
            response.setBody("Invalid token");
            return response;
        }
        
        // Set user info for next processors
        request.setAttribute("user", getUserFromToken(token));
        System.out.println("✓ Authentication successful");
        
        return null; // Continue to next processor
    }
    
    private boolean isValidToken(String token) {
        return token.length() > 10; // Simplified validation
    }
    
    private String getUserFromToken(String token) {
        return "user_" + token.substring(0, 5); // Simplified user extraction
    }
}

// Rate limiting processor
public class RateLimitingProcessor extends AbstractRequestProcessor {
    private Map<String, Integer> requestCounts = new HashMap<>();
    private static final int MAX_REQUESTS = 100;
    
    @Override
    protected HttpResponse doProcess(HttpRequest request) {
        System.out.println("⏱️ Processing rate limiting...");
        
        String clientIP = request.getHeader("X-Client-IP");
        if (clientIP == null) {
            clientIP = "unknown";
        }
        
        int currentCount = requestCounts.getOrDefault(clientIP, 0);
        
        if (currentCount >= MAX_REQUESTS) {
            HttpResponse response = new HttpResponse();
            response.setStatusCode(429);
            response.setStatusMessage("Too Many Requests");
            response.setBody("Rate limit exceeded");
            return response;
        }
        
        requestCounts.put(clientIP, currentCount + 1);
        System.out.println("✓ Rate limiting passed (" + (currentCount + 1) + "/" + MAX_REQUESTS + ")");
        
        return null; // Continue to next processor
    }
}

// Logging processor
public class LoggingProcessor extends AbstractRequestProcessor {
    
    @Override
    protected HttpResponse doProcess(HttpRequest request) {
        System.out.println("📝 Logging request...");
        
        String logEntry = String.format("[%s] %s %s - User: %s", 
            new java.util.Date(),
            request.getMethod(),
            request.getPath(),
            request.getAttribute("user"));
        
        System.out.println("Log: " + logEntry);
        
        return null; // Continue to next processor
    }
}

// Business logic processor
public class BusinessLogicProcessor extends AbstractRequestProcessor {
    
    @Override
    protected HttpResponse doProcess(HttpRequest request) {
        System.out.println("🔧 Processing business logic...");
        
        String path = request.getPath();
        String method = request.getMethod();
        
        // Route to appropriate handler
        if ("GET".equals(method) && "/api/users".equals(path)) {
            return handleGetUsers(request);
        } else if ("POST".equals(method) && "/api/users".equals(path)) {
            return handleCreateUser(request);
        } else if ("GET".equals(method) && path.startsWith("/api/users/")) {
            return handleGetUser(request);
        }
        
        return null; // No handler found - will return 404
    }
    
    private HttpResponse handleGetUsers(HttpRequest request) {
        HttpResponse response = new HttpResponse();
        response.setBody("[{\"id\":1,\"name\":\"John\"},{\"id\":2,\"name\":\"Jane\"}]");
        response.addHeader("Content-Type", "application/json");
        return response;
    }
    
    private HttpResponse handleCreateUser(HttpRequest request) {
        HttpResponse response = new HttpResponse();
        response.setStatusCode(201);
        response.setStatusMessage("Created");
        response.setBody("{\"id\":3,\"name\":\"New User\"}");
        response.addHeader("Content-Type", "application/json");
        return response;
    }
    
    private HttpResponse handleGetUser(HttpRequest request) {
        String userId = request.getPath().substring("/api/users/".length());
        HttpResponse response = new HttpResponse();
        response.setBody("{\"id\":" + userId + ",\"name\":\"User " + userId + "\"}");
        response.addHeader("Content-Type", "application/json");
        return response;
    }
}

// HTTP Server
public class HttpServer {
    private RequestProcessor processingChain;
    
    public HttpServer() {
        buildProcessingChain();
    }
    
    private void buildProcessingChain() {
        RequestProcessor authProcessor = new AuthenticationProcessor();
        RequestProcessor rateLimitProcessor = new RateLimitingProcessor();
        RequestProcessor loggingProcessor = new LoggingProcessor();
        RequestProcessor businessProcessor = new BusinessLogicProcessor();
        
        // Build chain
        authProcessor.setNext(rateLimitProcessor);
        rateLimitProcessor.setNext(loggingProcessor);
        loggingProcessor.setNext(businessProcessor);
        
        this.processingChain = authProcessor;
    }
    
    public HttpResponse handleRequest(HttpRequest request) {
        System.out.println("🌐 Handling HTTP request: " + request.getMethod() + " " + request.getPath());
        System.out.println("=".repeat(60));
        
        HttpResponse response = processingChain.process(request);
        
        System.out.println("=".repeat(60));
        System.out.println("📤 Response: " + response.getStatusCode() + " " + response.getStatusMessage());
        
        return response;
    }
    
    public void setProcessingChain(RequestProcessor chain) {
        this.processingChain = chain;
    }
}

// Usage
public class HttpServerDemo {
    public static void main(String[] args) {
        HttpServer server = new HttpServer();
        
        // Test successful request
        System.out.println("=== Test 1: Successful Request ===");
        HttpRequest request1 = new HttpRequest("GET", "/api/users");
        request1.addHeader("Authorization", "Bearer valid_token_12345");
        request1.addHeader("X-Client-IP", "*************");
        
        HttpResponse response1 = server.handleRequest(request1);
        System.out.println("Response body: " + response1.getBody());
        
        System.out.println("\n=== Test 2: Unauthorized Request ===");
        HttpRequest request2 = new HttpRequest("POST", "/api/users");
        request2.addHeader("X-Client-IP", "*************");
        // No Authorization header
        
        HttpResponse response2 = server.handleRequest(request2);
        System.out.println("Response body: " + response2.getBody());
        
        System.out.println("\n=== Test 3: Invalid Token ===");
        HttpRequest request3 = new HttpRequest("GET", "/api/users/123");
        request3.addHeader("Authorization", "Bearer short");
        request3.addHeader("X-Client-IP", "*************");
        
        HttpResponse response3 = server.handleRequest(request3);
        System.out.println("Response body: " + response3.getBody());
        
        System.out.println("\n=== Test 4: Rate Limiting ===");
        // Simulate many requests from same IP
        RateLimitingProcessor rateLimiter = new RateLimitingProcessor();
        for (int i = 0; i < 102; i++) {
            HttpRequest request = new HttpRequest("GET", "/api/users");
            request.addHeader("Authorization", "Bearer valid_token_12345");
            request.addHeader("X-Client-IP", "*************");
            
            if (i == 101) { // Last request should be rate limited
                HttpResponse response = server.handleRequest(request);
                System.out.println("Request " + (i + 1) + " - Response: " + response.getStatusCode());
            }
        }
        
        System.out.println("\nChain of Responsibility Benefits:");
        System.out.println("- Flexible request processing pipeline");
        System.out.println("- Easy to add/remove/reorder processors");
        System.out.println("- Each processor has single responsibility");
        System.out.println("- Can short-circuit processing when needed");
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Chain of Responsibility** passes requests along chain of handlers
2. **Decouples sender** from receiver
3. **Allows dynamic handler configuration**
4. **Each handler** decides whether to process or pass request

### So sánh với patterns khác
| Pattern | Purpose | Handler Selection |
|---------|---------|-------------------|
| **Chain of Responsibility** | Pass request through chain | First capable handler |
| **Command** | Encapsulate requests | Specific command object |
| **Mediator** | Centralize communications | Central mediator |
| **Observer** | Notify multiple objects | All registered observers |

### Best Practices
- **Keep handlers simple** và focused
- **Provide default handling** cho unprocessed requests
- **Consider performance** của long chains
- **Use builder pattern** cho chain construction
- **Log chain execution** cho debugging

---

**Tiếp theo:** [Iterator](iterator.md) - Duyệt qua collection elements
