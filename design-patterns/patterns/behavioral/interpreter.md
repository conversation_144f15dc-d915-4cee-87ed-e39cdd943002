# Interpreter Pattern

> **Behavioral Pattern** - <PERSON><PERSON><PERSON> nghĩa grammar cho ngôn ngữ và interpreter đ<PERSON> xử lý các câu trong ngôn ngữ đó

## 📋 <PERSON>ụ<PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [V<PERSON><PERSON> đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#giải-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nhược-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Interpreter là một **behavioral design pattern** **định nghĩa representation cho grammar của ngôn ngữ** cùng với interpreter sử dụng representation đ<PERSON> để interpret sentences trong ngôn ngữ.

### Mục đích chính
- **Define grammar** cho simple languages
- **Interpret expressions** trong ngôn ngữ
- **Build domain-specific languages** (DSL)
- **Parse và evaluate** structured text

### Tên gọi khác
- **Grammar Pattern**
- **Language Pattern**

### Ví dụ thực tế
Giống như **calculator**: bạn nhập expression "2 + 3 * 4" và calculator parse expression này thành tree structure, sau đó evaluate để ra kết quả 14.

---

## Vấn đề

### Tình huống thực tế
Bạn cần xử lý mathematical expressions từ user input:

```java
public class SimpleCalculator {
    public double calculate(String expression) {
        // How to handle expressions like:
        // "2 + 3"
        // "5 * (2 + 3)"
        // "10 / 2 - 1"
        // "sin(30) + cos(60)"
        
        // Naive approach with string manipulation
        if (expression.contains("+")) {
            String[] parts = expression.split("\\+");
            return Double.parseDouble(parts[0]) + Double.parseDouble(parts[1]);
        } else if (expression.contains("-")) {
            String[] parts = expression.split("-");
            return Double.parseDouble(parts[0]) - Double.parseDouble(parts[1]);
        }
        // This approach quickly becomes unmanageable!
        
        return 0;
    }
}
```

### Vấn đề phát sinh

#### 1. Complex parsing logic
```java
public double parseExpression(String expr) {
    // Handle operator precedence
    // Handle parentheses
    // Handle functions
    // Handle variables
    
    // Becomes extremely complex with nested if-else
    if (expr.contains("(")) {
        // Find matching parentheses
        // Parse inner expression first
        // Handle multiple levels of nesting
    }
    
    if (expr.contains("*") || expr.contains("/")) {
        // Handle multiplication/division first (precedence)
    }
    
    if (expr.contains("+") || expr.contains("-")) {
        // Handle addition/subtraction last
    }
    
    // Code becomes unmaintainable!
}
```

#### 2. No extensibility
```java
// Adding new operators requires modifying parsing logic
public double calculate(String expression) {
    // Existing operators: +, -, *, /
    
    // Want to add: ^, %, sin, cos, log, etc.
    // Must modify this method for each new operator!
    
    if (expression.contains("^")) {
        // Add power operator logic
    }
    
    if (expression.contains("sin")) {
        // Add sine function logic
    }
    
    // Violates Open/Closed Principle
}
```

#### 3. Error handling complexity
```java
public double calculate(String expression) {
    try {
        // Complex parsing logic
        // Where exactly did parsing fail?
        // What kind of error occurred?
        // Hard to provide meaningful error messages
    } catch (Exception e) {
        throw new RuntimeException("Invalid expression"); // Not helpful!
    }
}
```

#### 4. No reusability
```java
// Cannot reuse parsing logic for different purposes
// Cannot build expression trees for optimization
// Cannot serialize/deserialize expressions
// Cannot perform symbolic manipulation
```

---

## Giải pháp

### Ý tưởng cốt lõi
Interpreter pattern đề xuất **represent each grammar rule as class**. Mỗi class implements interpretation logic cho rule đó. Grammar rules được organized thành tree structure reflecting language syntax.

### Cách hoạt động

#### 1. Abstract Expression
```java
public interface Expression {
    double interpret(Context context);
}
```

#### 2. Terminal Expressions
```java
public class NumberExpression implements Expression {
    private double number;
    
    public NumberExpression(double number) {
        this.number = number;
    }
    
    @Override
    public double interpret(Context context) {
        return number;
    }
    
    @Override
    public String toString() {
        return String.valueOf(number);
    }
}

public class VariableExpression implements Expression {
    private String variableName;
    
    public VariableExpression(String variableName) {
        this.variableName = variableName;
    }
    
    @Override
    public double interpret(Context context) {
        return context.getVariable(variableName);
    }
    
    @Override
    public String toString() {
        return variableName;
    }
}
```

#### 3. Non-terminal Expressions
```java
public class AddExpression implements Expression {
    private Expression left;
    private Expression right;
    
    public AddExpression(Expression left, Expression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public double interpret(Context context) {
        return left.interpret(context) + right.interpret(context);
    }
    
    @Override
    public String toString() {
        return "(" + left + " + " + right + ")";
    }
}

public class SubtractExpression implements Expression {
    private Expression left;
    private Expression right;
    
    public SubtractExpression(Expression left, Expression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public double interpret(Context context) {
        return left.interpret(context) - right.interpret(context);
    }
    
    @Override
    public String toString() {
        return "(" + left + " - " + right + ")";
    }
}

public class MultiplyExpression implements Expression {
    private Expression left;
    private Expression right;
    
    public MultiplyExpression(Expression left, Expression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public double interpret(Context context) {
        return left.interpret(context) * right.interpret(context);
    }
    
    @Override
    public String toString() {
        return "(" + left + " * " + right + ")";
    }
}

public class DivideExpression implements Expression {
    private Expression left;
    private Expression right;
    
    public DivideExpression(Expression left, Expression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public double interpret(Context context) {
        double rightValue = right.interpret(context);
        if (rightValue == 0) {
            throw new ArithmeticException("Division by zero");
        }
        return left.interpret(context) / rightValue;
    }
    
    @Override
    public String toString() {
        return "(" + left + " / " + right + ")";
    }
}
```

#### 4. Context
```java
public class Context {
    private Map<String, Double> variables = new HashMap<>();
    
    public void setVariable(String name, double value) {
        variables.put(name, value);
    }
    
    public double getVariable(String name) {
        Double value = variables.get(name);
        if (value == null) {
            throw new IllegalArgumentException("Undefined variable: " + name);
        }
        return value;
    }
    
    public boolean hasVariable(String name) {
        return variables.containsKey(name);
    }
    
    public void clearVariables() {
        variables.clear();
    }
    
    @Override
    public String toString() {
        return "Context" + variables;
    }
}
```

#### 5. Parser
```java
public class ExpressionParser {
    
    public static Expression parse(String expression) {
        // Simple recursive descent parser
        return parseExpression(tokenize(expression));
    }
    
    private static List<String> tokenize(String expression) {
        List<String> tokens = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        
        for (char c : expression.toCharArray()) {
            if (Character.isWhitespace(c)) {
                if (current.length() > 0) {
                    tokens.add(current.toString());
                    current.setLength(0);
                }
            } else if (c == '+' || c == '-' || c == '*' || c == '/' || c == '(' || c == ')') {
                if (current.length() > 0) {
                    tokens.add(current.toString());
                    current.setLength(0);
                }
                tokens.add(String.valueOf(c));
            } else {
                current.append(c);
            }
        }
        
        if (current.length() > 0) {
            tokens.add(current.toString());
        }
        
        return tokens;
    }
    
    private static Expression parseExpression(List<String> tokens) {
        // Simplified parser - handles basic arithmetic with precedence
        return parseAddSubtract(tokens, new int[]{0});
    }
    
    private static Expression parseAddSubtract(List<String> tokens, int[] index) {
        Expression left = parseMultiplyDivide(tokens, index);
        
        while (index[0] < tokens.size()) {
            String operator = tokens.get(index[0]);
            if ("+".equals(operator)) {
                index[0]++;
                Expression right = parseMultiplyDivide(tokens, index);
                left = new AddExpression(left, right);
            } else if ("-".equals(operator)) {
                index[0]++;
                Expression right = parseMultiplyDivide(tokens, index);
                left = new SubtractExpression(left, right);
            } else {
                break;
            }
        }
        
        return left;
    }
    
    private static Expression parseMultiplyDivide(List<String> tokens, int[] index) {
        Expression left = parseFactor(tokens, index);
        
        while (index[0] < tokens.size()) {
            String operator = tokens.get(index[0]);
            if ("*".equals(operator)) {
                index[0]++;
                Expression right = parseFactor(tokens, index);
                left = new MultiplyExpression(left, right);
            } else if ("/".equals(operator)) {
                index[0]++;
                Expression right = parseFactor(tokens, index);
                left = new DivideExpression(left, right);
            } else {
                break;
            }
        }
        
        return left;
    }
    
    private static Expression parseFactor(List<String> tokens, int[] index) {
        if (index[0] >= tokens.size()) {
            throw new IllegalArgumentException("Unexpected end of expression");
        }
        
        String token = tokens.get(index[0]);
        
        if ("(".equals(token)) {
            index[0]++; // consume '('
            Expression expr = parseAddSubtract(tokens, index);
            if (index[0] >= tokens.size() || !")".equals(tokens.get(index[0]))) {
                throw new IllegalArgumentException("Missing closing parenthesis");
            }
            index[0]++; // consume ')'
            return expr;
        } else if (isNumber(token)) {
            index[0]++;
            return new NumberExpression(Double.parseDouble(token));
        } else if (isVariable(token)) {
            index[0]++;
            return new VariableExpression(token);
        } else {
            throw new IllegalArgumentException("Unexpected token: " + token);
        }
    }
    
    private static boolean isNumber(String token) {
        try {
            Double.parseDouble(token);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private static boolean isVariable(String token) {
        return token.matches("[a-zA-Z][a-zA-Z0-9]*");
    }
}
```

#### 6. Calculator
```java
public class Calculator {
    
    public double evaluate(String expression) {
        return evaluate(expression, new Context());
    }
    
    public double evaluate(String expression, Context context) {
        try {
            Expression expr = ExpressionParser.parse(expression);
            return expr.interpret(context);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid expression: " + expression, e);
        }
    }
    
    public Expression parseExpression(String expression) {
        return ExpressionParser.parse(expression);
    }
}
```

#### 7. Usage
```java
public class InterpreterDemo {
    public static void main(String[] args) {
        Calculator calculator = new Calculator();
        Context context = new Context();
        
        // Set some variables
        context.setVariable("x", 10);
        context.setVariable("y", 5);
        
        // Test expressions
        String[] expressions = {
            "2 + 3",
            "5 * 4",
            "10 / 2",
            "2 + 3 * 4",
            "(2 + 3) * 4",
            "x + y",
            "x * 2 + y",
            "(x + y) / 3"
        };
        
        System.out.println("=== Calculator with Interpreter Pattern ===");
        System.out.println("Variables: " + context);
        System.out.println();
        
        for (String expr : expressions) {
            try {
                double result = calculator.evaluate(expr, context);
                Expression parsed = calculator.parseExpression(expr);
                
                System.out.println("Expression: " + expr);
                System.out.println("Parsed as: " + parsed);
                System.out.println("Result: " + result);
                System.out.println();
            } catch (Exception e) {
                System.out.println("Error evaluating '" + expr + "': " + e.getMessage());
                System.out.println();
            }
        }
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
AbstractExpression (interface)
├── + interpret(Context): Object
│
TerminalExpression implements AbstractExpression
├── + interpret(Context): Object
│
NonterminalExpression implements AbstractExpression
├── - expression1: AbstractExpression
├── - expression2: AbstractExpression
├── + interpret(Context): Object
│
Context
├── - variables: Map<String, Object>
├── + getVariable(String): Object
├── + setVariable(String, Object): void
│
Client
├── - context: Context
├── + evaluate(String): Object
```

### Các thành phần chính

#### 1. Abstract Expression
- **Vai trò:** Declares interpret method
- **Đặc điểm:** Common interface cho all expressions

#### 2. Terminal Expression
- **Vai trò:** Implements interpret cho terminal symbols
- **Đặc điểm:** Leaf nodes trong expression tree

#### 3. Nonterminal Expression
- **Vai trò:** Implements interpret cho grammar rules
- **Đặc điểm:** Contains references to other expressions

#### 4. Context
- **Vai trò:** Contains global information
- **Đặc điểm:** Shared across all expressions

---

## Cách triển khai

### Bước 1: Define Abstract Expression
```java
public interface BooleanExpression {
    boolean interpret(BooleanContext context);
}
```

### Bước 2: Terminal Expressions
```java
public class VariableExpression implements BooleanExpression {
    private String name;
    
    public VariableExpression(String name) {
        this.name = name;
    }
    
    @Override
    public boolean interpret(BooleanContext context) {
        return context.lookup(name);
    }
}

public class ConstantExpression implements BooleanExpression {
    private boolean value;
    
    public ConstantExpression(boolean value) {
        this.value = value;
    }
    
    @Override
    public boolean interpret(BooleanContext context) {
        return value;
    }
}
```

### Bước 3: Nonterminal Expressions
```java
public class AndExpression implements BooleanExpression {
    private BooleanExpression left, right;
    
    public AndExpression(BooleanExpression left, BooleanExpression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public boolean interpret(BooleanContext context) {
        return left.interpret(context) && right.interpret(context);
    }
}

public class OrExpression implements BooleanExpression {
    private BooleanExpression left, right;
    
    public OrExpression(BooleanExpression left, BooleanExpression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public boolean interpret(BooleanContext context) {
        return left.interpret(context) || right.interpret(context);
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Easy to extend grammar
```java
// Adding new expression type is simple
public class XorExpression implements BooleanExpression {
    private BooleanExpression left, right;
    
    @Override
    public boolean interpret(BooleanContext context) {
        return left.interpret(context) ^ right.interpret(context);
    }
}
```

#### 2. Grammar rules are explicit
```java
// Each grammar rule is separate class
// Easy to understand and maintain
// Clear separation of concerns
```

#### 3. Easy to implement
```java
// Straightforward implementation
// Direct mapping from grammar to code
// No complex parsing frameworks needed
```

### ❌ Nhược điểm

#### 1. Complex grammars become unwieldy
```java
// Many classes for complex languages
// Deep inheritance hierarchies
// Performance overhead for large expressions
```

#### 2. Hard to maintain grammar
```java
// Changing grammar requires changing many classes
// No central grammar definition
// Difficult to optimize
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Simple grammar
```java
// Mathematical expressions
// Boolean logic
// Simple query languages
// Configuration languages
```

#### 2. Grammar doesn't change often
```java
// Stable language definition
// Well-defined syntax
// Limited scope
```

#### 3. Performance is not critical
```java
// Small expressions
// Infrequent evaluation
// Development/testing tools
```

### ❌ Không nên sử dụng khi:

#### 1. Complex grammar
```java
// Full programming languages
// Complex syntax rules
// Many grammar productions
```

#### 2. Performance is critical
```java
// High-frequency evaluation
// Large expressions
// Real-time systems
```

---

## Ví dụ thực tế

### Ví dụ 1: SQL Query Builder và Interpreter

```java
// SQL Expression hierarchy
public interface SQLExpression {
    String toSQL();
    Object evaluate(DatabaseContext context);
}

// Column reference
public class ColumnExpression implements SQLExpression {
    private String tableName;
    private String columnName;
    
    public ColumnExpression(String columnName) {
        this.columnName = columnName;
    }
    
    public ColumnExpression(String tableName, String columnName) {
        this.tableName = tableName;
        this.columnName = columnName;
    }
    
    @Override
    public String toSQL() {
        return tableName != null ? tableName + "." + columnName : columnName;
    }
    
    @Override
    public Object evaluate(DatabaseContext context) {
        return context.getColumnValue(tableName, columnName);
    }
}

// Literal value
public class LiteralExpression implements SQLExpression {
    private Object value;
    
    public LiteralExpression(Object value) {
        this.value = value;
    }
    
    @Override
    public String toSQL() {
        if (value instanceof String) {
            return "'" + value.toString().replace("'", "''") + "'";
        } else if (value instanceof Number) {
            return value.toString();
        } else if (value == null) {
            return "NULL";
        } else {
            return "'" + value.toString() + "'";
        }
    }
    
    @Override
    public Object evaluate(DatabaseContext context) {
        return value;
    }
}

// Comparison expressions
public class EqualsExpression implements SQLExpression {
    private SQLExpression left;
    private SQLExpression right;
    
    public EqualsExpression(SQLExpression left, SQLExpression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public String toSQL() {
        return left.toSQL() + " = " + right.toSQL();
    }
    
    @Override
    public Object evaluate(DatabaseContext context) {
        Object leftValue = left.evaluate(context);
        Object rightValue = right.evaluate(context);
        
        if (leftValue == null && rightValue == null) return true;
        if (leftValue == null || rightValue == null) return false;
        
        return leftValue.equals(rightValue);
    }
}

public class GreaterThanExpression implements SQLExpression {
    private SQLExpression left;
    private SQLExpression right;
    
    public GreaterThanExpression(SQLExpression left, SQLExpression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public String toSQL() {
        return left.toSQL() + " > " + right.toSQL();
    }
    
    @Override
    public Object evaluate(DatabaseContext context) {
        Object leftValue = left.evaluate(context);
        Object rightValue = right.evaluate(context);
        
        if (leftValue instanceof Number && rightValue instanceof Number) {
            double leftNum = ((Number) leftValue).doubleValue();
            double rightNum = ((Number) rightValue).doubleValue();
            return leftNum > rightNum;
        }
        
        return false;
    }
}

// Logical expressions
public class AndExpression implements SQLExpression {
    private SQLExpression left;
    private SQLExpression right;
    
    public AndExpression(SQLExpression left, SQLExpression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public String toSQL() {
        return "(" + left.toSQL() + " AND " + right.toSQL() + ")";
    }
    
    @Override
    public Object evaluate(DatabaseContext context) {
        Object leftResult = left.evaluate(context);
        Object rightResult = right.evaluate(context);
        
        if (leftResult instanceof Boolean && rightResult instanceof Boolean) {
            return (Boolean) leftResult && (Boolean) rightResult;
        }
        
        return false;
    }
}

public class OrExpression implements SQLExpression {
    private SQLExpression left;
    private SQLExpression right;
    
    public OrExpression(SQLExpression left, SQLExpression right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public String toSQL() {
        return "(" + left.toSQL() + " OR " + right.toSQL() + ")";
    }
    
    @Override
    public Object evaluate(DatabaseContext context) {
        Object leftResult = left.evaluate(context);
        Object rightResult = right.evaluate(context);
        
        if (leftResult instanceof Boolean && rightResult instanceof Boolean) {
            return (Boolean) leftResult || (Boolean) rightResult;
        }
        
        return false;
    }
}

// Function expressions
public class FunctionExpression implements SQLExpression {
    private String functionName;
    private List<SQLExpression> arguments;
    
    public FunctionExpression(String functionName, List<SQLExpression> arguments) {
        this.functionName = functionName;
        this.arguments = arguments;
    }
    
    @Override
    public String toSQL() {
        StringBuilder sql = new StringBuilder();
        sql.append(functionName.toUpperCase()).append("(");
        
        for (int i = 0; i < arguments.size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append(arguments.get(i).toSQL());
        }
        
        sql.append(")");
        return sql.toString();
    }
    
    @Override
    public Object evaluate(DatabaseContext context) {
        List<Object> argValues = new ArrayList<>();
        for (SQLExpression arg : arguments) {
            argValues.add(arg.evaluate(context));
        }
        
        return context.evaluateFunction(functionName, argValues);
    }
}

// Database context
public class DatabaseContext {
    private Map<String, Map<String, Object>> currentRow = new HashMap<>();
    
    public void setCurrentRow(String tableName, Map<String, Object> row) {
        currentRow.put(tableName, row);
    }
    
    public Object getColumnValue(String tableName, String columnName) {
        Map<String, Object> tableRow = currentRow.get(tableName);
        if (tableRow == null) {
            // Try default table
            tableRow = currentRow.get(null);
        }
        
        return tableRow != null ? tableRow.get(columnName) : null;
    }
    
    public Object evaluateFunction(String functionName, List<Object> arguments) {
        switch (functionName.toUpperCase()) {
            case "UPPER":
                if (arguments.size() == 1 && arguments.get(0) instanceof String) {
                    return ((String) arguments.get(0)).toUpperCase();
                }
                break;
            case "LOWER":
                if (arguments.size() == 1 && arguments.get(0) instanceof String) {
                    return ((String) arguments.get(0)).toLowerCase();
                }
                break;
            case "LENGTH":
                if (arguments.size() == 1 && arguments.get(0) instanceof String) {
                    return ((String) arguments.get(0)).length();
                }
                break;
            case "ABS":
                if (arguments.size() == 1 && arguments.get(0) instanceof Number) {
                    return Math.abs(((Number) arguments.get(0)).doubleValue());
                }
                break;
        }
        
        throw new IllegalArgumentException("Unknown function: " + functionName);
    }
}

// Query builder
public class QueryBuilder {
    
    public static SQLExpression column(String name) {
        return new ColumnExpression(name);
    }
    
    public static SQLExpression column(String table, String name) {
        return new ColumnExpression(table, name);
    }
    
    public static SQLExpression literal(Object value) {
        return new LiteralExpression(value);
    }
    
    public static SQLExpression eq(SQLExpression left, SQLExpression right) {
        return new EqualsExpression(left, right);
    }
    
    public static SQLExpression gt(SQLExpression left, SQLExpression right) {
        return new GreaterThanExpression(left, right);
    }
    
    public static SQLExpression and(SQLExpression left, SQLExpression right) {
        return new AndExpression(left, right);
    }
    
    public static SQLExpression or(SQLExpression left, SQLExpression right) {
        return new OrExpression(left, right);
    }
    
    public static SQLExpression function(String name, SQLExpression... args) {
        return new FunctionExpression(name, Arrays.asList(args));
    }
}

// Usage
public class SQLInterpreterDemo {
    public static void main(String[] args) {
        System.out.println("=== SQL Query Builder and Interpreter ===");
        
        // Build complex WHERE clause using fluent API
        SQLExpression whereClause = QueryBuilder.and(
            QueryBuilder.eq(
                QueryBuilder.column("status"),
                QueryBuilder.literal("active")
            ),
            QueryBuilder.or(
                QueryBuilder.gt(
                    QueryBuilder.column("age"),
                    QueryBuilder.literal(18)
                ),
                QueryBuilder.eq(
                    QueryBuilder.column("role"),
                    QueryBuilder.literal("admin")
                )
            )
        );
        
        // Generate SQL
        System.out.println("Generated SQL WHERE clause:");
        System.out.println(whereClause.toSQL());
        
        // Test evaluation with sample data
        DatabaseContext context = new DatabaseContext();
        
        // Test case 1: Active adult user
        Map<String, Object> user1 = new HashMap<>();
        user1.put("status", "active");
        user1.put("age", 25);
        user1.put("role", "user");
        
        context.setCurrentRow(null, user1);
        boolean result1 = (Boolean) whereClause.evaluate(context);
        System.out.println("\nUser 1 (active, age=25, role=user): " + result1);
        
        // Test case 2: Active minor admin
        Map<String, Object> user2 = new HashMap<>();
        user2.put("status", "active");
        user2.put("age", 16);
        user2.put("role", "admin");
        
        context.setCurrentRow(null, user2);
        boolean result2 = (Boolean) whereClause.evaluate(context);
        System.out.println("User 2 (active, age=16, role=admin): " + result2);
        
        // Test case 3: Inactive user
        Map<String, Object> user3 = new HashMap<>();
        user3.put("status", "inactive");
        user3.put("age", 30);
        user3.put("role", "user");
        
        context.setCurrentRow(null, user3);
        boolean result3 = (Boolean) whereClause.evaluate(context);
        System.out.println("User 3 (inactive, age=30, role=user): " + result3);
        
        // Test function expressions
        System.out.println("\n=== Function Expressions ===");
        
        SQLExpression functionExpr = QueryBuilder.function("UPPER", 
            QueryBuilder.column("name"));
        
        Map<String, Object> user4 = new HashMap<>();
        user4.put("name", "john doe");
        
        context.setCurrentRow(null, user4);
        System.out.println("Function SQL: " + functionExpr.toSQL());
        System.out.println("Function result: " + functionExpr.evaluate(context));
        
        // Complex function expression
        SQLExpression complexExpr = QueryBuilder.gt(
            QueryBuilder.function("LENGTH", QueryBuilder.column("name")),
            QueryBuilder.literal(5)
        );
        
        System.out.println("Complex expression SQL: " + complexExpr.toSQL());
        System.out.println("Complex expression result: " + complexExpr.evaluate(context));
        
        System.out.println("\nInterpreter Pattern Benefits:");
        System.out.println("- Type-safe query building");
        System.out.println("- SQL generation and evaluation in same structure");
        System.out.println("- Easy to extend with new operators and functions");
        System.out.println("- Reusable expression components");
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Interpreter Pattern** defines grammar representation và interpreter
2. **Each grammar rule** becomes separate class
3. **Easy to extend** với new expressions
4. **Suitable for simple languages** và DSLs

### So sánh với patterns khác
| Pattern | Purpose | Complexity |
|---------|---------|------------|
| **Interpreter** | Define language grammar | Simple languages |
| **Visitor** | Operations on object structure | Complex structures |
| **Strategy** | Encapsulate algorithms | Algorithm selection |
| **Command** | Encapsulate requests | Individual operations |

### Best Practices
- **Use for simple grammars** only
- **Consider performance** implications
- **Provide good error messages**
- **Document grammar** clearly
- **Consider parser generators** cho complex languages

---

**Hoàn thành:** Tất cả 23 Design Patterns đã được documented đầy đủ! 🎉
