# State Pattern

> **Behavioral Pattern** - <PERSON> phép đối tượng thay đổi hành vi khi trạng thái nội bộ thay đổi

## 📋 <PERSON><PERSON><PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [<PERSON><PERSON><PERSON> đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#giải-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu như<PERSON>c điểm](#ưu-nhược-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [V<PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
State là một **behavioral design pattern** cho phép **object thay đổi behavior khi internal state thay đổi**. Object sẽ xuất hiện như thể đã thay đổi class.

### <PERSON><PERSON><PERSON> đích chính
- **Organize state-specific behavior** into separate classes
- **Eliminate complex conditional statements** based on state
- **Make state transitions explicit** và manageable
- **Follow Open/Closed Principle** cho state management

### Tên gọi khác
- **Objects for States Pattern**
- **State Machine Pattern**

### Ví dụ thực tế
Giống như **máy bán hàng tự động**: có các trạng thái khác nhau (No Coin, Has Coin, Sold, Out of Stock). Mỗi trạng thái có hành vi khác nhau khi nhận input (insert coin, select item, dispense).

---

## Vấn đề

### Tình huống thực tế
Bạn đang phát triển media player với các states: Stopped, Playing, Paused:

```java
public class MediaPlayer {
    private String state = "stopped";
    
    public void play() {
        if (state.equals("stopped")) {
            System.out.println("Starting playback");
            state = "playing";
        } else if (state.equals("playing")) {
            System.out.println("Already playing");
        } else if (state.equals("paused")) {
            System.out.println("Resuming playback");
            state = "playing";
        }
    }
    
    public void pause() {
        if (state.equals("playing")) {
            System.out.println("Pausing playback");
            state = "paused";
        } else if (state.equals("stopped")) {
            System.out.println("Cannot pause when stopped");
        } else if (state.equals("paused")) {
            System.out.println("Already paused");
        }
    }
    
    public void stop() {
        if (state.equals("playing") || state.equals("paused")) {
            System.out.println("Stopping playback");
            state = "stopped";
        } else if (state.equals("stopped")) {
            System.out.println("Already stopped");
        }
    }
}
```

### Vấn đề phát sinh

#### 1. Complex conditional logic
```java
public void nextTrack() {
    if (state.equals("stopped")) {
        System.out.println("Cannot go to next track when stopped");
    } else if (state.equals("playing")) {
        System.out.println("Playing next track");
        // Complex logic for playing state
    } else if (state.equals("paused")) {
        System.out.println("Moving to next track");
        // Different logic for paused state
    } else if (state.equals("loading")) {
        System.out.println("Cannot change track while loading");
    } else if (state.equals("error")) {
        System.out.println("Cannot change track in error state");
    }
    // More conditions as states are added...
}
```

#### 2. State explosion
```java
// Adding new states requires modifying all methods
public void setVolume(int volume) {
    if (state.equals("stopped")) {
        // Stopped behavior
    } else if (state.equals("playing")) {
        // Playing behavior
    } else if (state.equals("paused")) {
        // Paused behavior
    } else if (state.equals("loading")) {
        // Loading behavior
    } else if (state.equals("buffering")) {
        // Buffering behavior
    } else if (state.equals("error")) {
        // Error behavior
    }
    // Every method becomes huge!
}
```

#### 3. Difficult to maintain
```java
// Adding new state requires changes in multiple places
// Bug in one state condition affects entire class
// Hard to test individual state behaviors
// Violates Single Responsibility Principle
```

#### 4. State transition complexity
```java
public void handleNetworkError() {
    if (state.equals("playing")) {
        state = "buffering";
        // But what if already buffering?
    } else if (state.equals("loading")) {
        state = "error";
        // Complex transition logic scattered everywhere
    }
    // State transition rules are implicit and hard to track
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
State pattern đề xuất **tạo new classes cho tất cả possible states** của object và extract tất cả state-specific behaviors vào các classes này.

### Cách hoạt động

#### 1. State interface
```java
public interface PlayerState {
    void play(MediaPlayer player);
    void pause(MediaPlayer player);
    void stop(MediaPlayer player);
    void nextTrack(MediaPlayer player);
    void previousTrack(MediaPlayer player);
    String getStateName();
}
```

#### 2. Concrete States
```java
public class StoppedState implements PlayerState {
    @Override
    public void play(MediaPlayer player) {
        System.out.println("Starting playback from stopped state");
        player.setState(new PlayingState());
    }
    
    @Override
    public void pause(MediaPlayer player) {
        System.out.println("Cannot pause when stopped");
    }
    
    @Override
    public void stop(MediaPlayer player) {
        System.out.println("Already stopped");
    }
    
    @Override
    public void nextTrack(MediaPlayer player) {
        System.out.println("Cannot go to next track when stopped");
    }
    
    @Override
    public void previousTrack(MediaPlayer player) {
        System.out.println("Cannot go to previous track when stopped");
    }
    
    @Override
    public String getStateName() {
        return "Stopped";
    }
}

public class PlayingState implements PlayerState {
    @Override
    public void play(MediaPlayer player) {
        System.out.println("Already playing");
    }
    
    @Override
    public void pause(MediaPlayer player) {
        System.out.println("Pausing playback");
        player.setState(new PausedState());
    }
    
    @Override
    public void stop(MediaPlayer player) {
        System.out.println("Stopping playback");
        player.setState(new StoppedState());
    }
    
    @Override
    public void nextTrack(MediaPlayer player) {
        System.out.println("Playing next track");
        // Stay in playing state
    }
    
    @Override
    public void previousTrack(MediaPlayer player) {
        System.out.println("Playing previous track");
        // Stay in playing state
    }
    
    @Override
    public String getStateName() {
        return "Playing";
    }
}

public class PausedState implements PlayerState {
    @Override
    public void play(MediaPlayer player) {
        System.out.println("Resuming playback from paused state");
        player.setState(new PlayingState());
    }
    
    @Override
    public void pause(MediaPlayer player) {
        System.out.println("Already paused");
    }
    
    @Override
    public void stop(MediaPlayer player) {
        System.out.println("Stopping from paused state");
        player.setState(new StoppedState());
    }
    
    @Override
    public void nextTrack(MediaPlayer player) {
        System.out.println("Moving to next track (staying paused)");
        // Stay in paused state
    }
    
    @Override
    public void previousTrack(MediaPlayer player) {
        System.out.println("Moving to previous track (staying paused)");
        // Stay in paused state
    }
    
    @Override
    public String getStateName() {
        return "Paused";
    }
}
```

#### 3. Context (MediaPlayer)
```java
public class MediaPlayer {
    private PlayerState currentState;
    private String currentTrack = "Track 1";
    
    public MediaPlayer() {
        currentState = new StoppedState();
    }
    
    public void setState(PlayerState state) {
        this.currentState = state;
        System.out.println("State changed to: " + state.getStateName());
    }
    
    // Delegate all operations to current state
    public void play() {
        currentState.play(this);
    }
    
    public void pause() {
        currentState.pause(this);
    }
    
    public void stop() {
        currentState.stop(this);
    }
    
    public void nextTrack() {
        currentState.nextTrack(this);
    }
    
    public void previousTrack() {
        currentState.previousTrack(this);
    }
    
    public String getCurrentState() {
        return currentState.getStateName();
    }
    
    public String getCurrentTrack() {
        return currentTrack;
    }
    
    public void setCurrentTrack(String track) {
        this.currentTrack = track;
    }
}
```

#### 4. Usage
```java
public class StatePatternDemo {
    public static void main(String[] args) {
        MediaPlayer player = new MediaPlayer();
        
        System.out.println("Initial state: " + player.getCurrentState());
        
        // Test state transitions
        player.play();      // Stopped -> Playing
        player.pause();     // Playing -> Paused
        player.play();      // Paused -> Playing
        player.stop();      // Playing -> Stopped
        
        // Test invalid operations
        player.pause();     // Cannot pause when stopped
        player.nextTrack(); // Cannot change track when stopped
        
        // Test track operations
        player.play();
        player.nextTrack(); // Can change track when playing
        player.pause();
        player.previousTrack(); // Can change track when paused
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Context
├── - state: State
├── + request(): void
├── + setState(State): void
│
State (interface)
├── + handle(Context): void
│
ConcreteStateA implements State
├── + handle(Context): void
│
ConcreteStateB implements State
├── + handle(Context): void
│
ConcreteStateC implements State
├── + handle(Context): void
```

### Các thành phần chính

#### 1. Context
- **Vai trò:** Maintains reference to current state object
- **Đặc điểm:** Delegates state-specific work to state object

#### 2. State Interface
- **Vai trò:** Declares methods cho all concrete states
- **Đặc điểm:** Methods usually correspond to context's methods

#### 3. Concrete States
- **Vai trò:** Implement state-specific behavior
- **Đặc điểm:** May hold reference to context và trigger state transitions

---

## Cách triển khai

### Bước 1: Identify states và transitions
```java
// Vending Machine states: NoCoin, HasCoin, Sold, OutOfStock
// Transitions: insertCoin(), ejectCoin(), selectItem(), dispense()
```

### Bước 2: State interface
```java
public interface VendingMachineState {
    void insertCoin(VendingMachine machine);
    void ejectCoin(VendingMachine machine);
    void selectItem(VendingMachine machine);
    void dispense(VendingMachine machine);
    String getDescription();
}
```

### Bước 3: Concrete States
```java
public class NoCoinState implements VendingMachineState {
    @Override
    public void insertCoin(VendingMachine machine) {
        System.out.println("Coin inserted");
        machine.setState(machine.getHasCoinState());
    }
    
    @Override
    public void ejectCoin(VendingMachine machine) {
        System.out.println("No coin to eject");
    }
    
    @Override
    public void selectItem(VendingMachine machine) {
        System.out.println("Please insert coin first");
    }
    
    @Override
    public void dispense(VendingMachine machine) {
        System.out.println("Please insert coin and select item");
    }
    
    @Override
    public String getDescription() {
        return "Waiting for coin";
    }
}

public class HasCoinState implements VendingMachineState {
    @Override
    public void insertCoin(VendingMachine machine) {
        System.out.println("Coin already inserted");
    }
    
    @Override
    public void ejectCoin(VendingMachine machine) {
        System.out.println("Coin ejected");
        machine.setState(machine.getNoCoinState());
    }
    
    @Override
    public void selectItem(VendingMachine machine) {
        System.out.println("Item selected");
        if (machine.getItemCount() > 0) {
            machine.setState(machine.getSoldState());
        } else {
            machine.setState(machine.getOutOfStockState());
        }
    }
    
    @Override
    public void dispense(VendingMachine machine) {
        System.out.println("Please select an item first");
    }
    
    @Override
    public String getDescription() {
        return "Coin inserted, select item";
    }
}
```

### Bước 4: Context implementation
```java
public class VendingMachine {
    private VendingMachineState noCoinState;
    private VendingMachineState hasCoinState;
    private VendingMachineState soldState;
    private VendingMachineState outOfStockState;
    
    private VendingMachineState currentState;
    private int itemCount;
    
    public VendingMachine(int itemCount) {
        this.itemCount = itemCount;
        
        // Initialize all states
        noCoinState = new NoCoinState();
        hasCoinState = new HasCoinState();
        soldState = new SoldState();
        outOfStockState = new OutOfStockState();
        
        // Set initial state
        currentState = (itemCount > 0) ? noCoinState : outOfStockState;
    }
    
    public void insertCoin() { currentState.insertCoin(this); }
    public void ejectCoin() { currentState.ejectCoin(this); }
    public void selectItem() { currentState.selectItem(this); }
    public void dispense() { currentState.dispense(this); }
    
    public void setState(VendingMachineState state) {
        this.currentState = state;
    }
    
    public void releaseItem() {
        if (itemCount > 0) {
            itemCount--;
            System.out.println("Item dispensed. Items remaining: " + itemCount);
        }
    }
    
    // Getters for states
    public VendingMachineState getNoCoinState() { return noCoinState; }
    public VendingMachineState getHasCoinState() { return hasCoinState; }
    public VendingMachineState getSoldState() { return soldState; }
    public VendingMachineState getOutOfStockState() { return outOfStockState; }
    
    public int getItemCount() { return itemCount; }
    public String getCurrentState() { return currentState.getDescription(); }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Eliminates complex conditionals
```java
// Before State Pattern
public void operation() {
    if (state == STATE_A) {
        // Complex logic for state A
    } else if (state == STATE_B) {
        // Complex logic for state B
    } else if (state == STATE_C) {
        // Complex logic for state C
    }
}

// After State Pattern
public void operation() {
    currentState.operation(this); // Clean delegation
}
```

#### 2. Single Responsibility Principle
```java
// Each state class has single responsibility
public class PlayingState implements PlayerState {
    // Only handles playing-specific behavior
    public void pause(MediaPlayer player) {
        // Playing-specific pause logic
        player.setState(new PausedState());
    }
}
```

#### 3. Open/Closed Principle
```java
// Easy to add new states without modifying existing code
public class BufferingState implements PlayerState {
    // New state implementation
    // No need to modify existing states or context
}
```

#### 4. Explicit state transitions
```java
// State transitions are clear and explicit
public void play(MediaPlayer player) {
    System.out.println("Resuming from pause");
    player.setState(new PlayingState()); // Clear transition
}
```

### ❌ Nhược điểm

#### 1. Increased number of classes
```java
// Need separate class for each state
public class StoppedState implements PlayerState { ... }
public class PlayingState implements PlayerState { ... }
public class PausedState implements PlayerState { ... }
public class BufferingState implements PlayerState { ... }
// Many small classes
```

#### 2. Overkill for simple state machines
```java
// Simple boolean state doesn't need State pattern
public class Switch {
    private boolean isOn = false;
    
    public void toggle() {
        isOn = !isOn; // Simple enough without State pattern
    }
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Object behavior depends on state
```java
// Media player, game characters, network connections
public class NetworkConnection {
    // Behavior changes based on: Connected, Disconnected, Connecting, Error
}
```

#### 2. Complex conditional statements based on state
```java
// When you have many if-else or switch statements
public void handleRequest() {
    if (state == IDLE) {
        // Complex idle logic
    } else if (state == PROCESSING) {
        // Complex processing logic
    } else if (state == COMPLETED) {
        // Complex completion logic
    }
    // State pattern would clean this up
}
```

#### 3. State transitions are complex
```java
// When state transition logic is scattered and complex
// State pattern makes transitions explicit and manageable
```

### ❌ Không nên sử dụng khi:

#### 1. Few states với simple logic
```java
// Simple on/off states don't need State pattern
public class LightSwitch {
    private boolean isOn = false;
    public void toggle() { isOn = !isOn; }
}
```

#### 2. States rarely change
```java
// If object state is mostly static
// State pattern adds unnecessary complexity
```

---

## Ví dụ thực tế

### Ví dụ 1: ATM Machine State Management

```java
// ATM States
public interface ATMState {
    void insertCard(ATM atm);
    void ejectCard(ATM atm);
    void enterPIN(ATM atm, String pin);
    void selectTransaction(ATM atm, String transactionType);
    void dispenseCash(ATM atm, int amount);
    String getStateDescription();
}

// Concrete States
public class NoCardState implements ATMState {
    @Override
    public void insertCard(ATM atm) {
        System.out.println("Card inserted. Please enter your PIN.");
        atm.setState(atm.getHasCardState());
    }
    
    @Override
    public void ejectCard(ATM atm) {
        System.out.println("No card to eject.");
    }
    
    @Override
    public void enterPIN(ATM atm, String pin) {
        System.out.println("Please insert card first.");
    }
    
    @Override
    public void selectTransaction(ATM atm, String transactionType) {
        System.out.println("Please insert card first.");
    }
    
    @Override
    public void dispenseCash(ATM atm, int amount) {
        System.out.println("Please insert card first.");
    }
    
    @Override
    public String getStateDescription() {
        return "Waiting for card insertion";
    }
}

public class HasCardState implements ATMState {
    private int pinAttempts = 0;
    private static final int MAX_PIN_ATTEMPTS = 3;
    
    @Override
    public void insertCard(ATM atm) {
        System.out.println("Card already inserted.");
    }
    
    @Override
    public void ejectCard(ATM atm) {
        System.out.println("Card ejected.");
        pinAttempts = 0;
        atm.setState(atm.getNoCardState());
    }
    
    @Override
    public void enterPIN(ATM atm, String pin) {
        if (atm.validatePIN(pin)) {
            System.out.println("PIN accepted. Please select transaction.");
            pinAttempts = 0;
            atm.setState(atm.getAuthenticatedState());
        } else {
            pinAttempts++;
            System.out.println("Invalid PIN. Attempts remaining: " + (MAX_PIN_ATTEMPTS - pinAttempts));
            
            if (pinAttempts >= MAX_PIN_ATTEMPTS) {
                System.out.println("Too many invalid attempts. Card blocked.");
                atm.setState(atm.getBlockedState());
            }
        }
    }
    
    @Override
    public void selectTransaction(ATM atm, String transactionType) {
        System.out.println("Please enter PIN first.");
    }
    
    @Override
    public void dispenseCash(ATM atm, int amount) {
        System.out.println("Please enter PIN first.");
    }
    
    @Override
    public String getStateDescription() {
        return "Card inserted, waiting for PIN";
    }
}

public class AuthenticatedState implements ATMState {
    @Override
    public void insertCard(ATM atm) {
        System.out.println("Card already inserted and authenticated.");
    }
    
    @Override
    public void ejectCard(ATM atm) {
        System.out.println("Transaction completed. Card ejected.");
        atm.setState(atm.getNoCardState());
    }
    
    @Override
    public void enterPIN(ATM atm, String pin) {
        System.out.println("Already authenticated.");
    }
    
    @Override
    public void selectTransaction(ATM atm, String transactionType) {
        System.out.println("Transaction selected: " + transactionType);
        
        switch (transactionType.toLowerCase()) {
            case "withdraw":
                System.out.println("Enter amount to withdraw:");
                atm.setState(atm.getTransactionState());
                break;
            case "balance":
                System.out.println("Your balance is: $" + atm.getAccountBalance());
                ejectCard(atm);
                break;
            case "deposit":
                System.out.println("Insert cash for deposit:");
                atm.setState(atm.getTransactionState());
                break;
            default:
                System.out.println("Invalid transaction type.");
        }
    }
    
    @Override
    public void dispenseCash(ATM atm, int amount) {
        System.out.println("Please select transaction type first.");
    }
    
    @Override
    public String getStateDescription() {
        return "Authenticated, select transaction";
    }
}

public class TransactionState implements ATMState {
    @Override
    public void insertCard(ATM atm) {
        System.out.println("Transaction in progress.");
    }
    
    @Override
    public void ejectCard(ATM atm) {
        System.out.println("Transaction cancelled. Card ejected.");
        atm.setState(atm.getNoCardState());
    }
    
    @Override
    public void enterPIN(ATM atm, String pin) {
        System.out.println("Transaction in progress.");
    }
    
    @Override
    public void selectTransaction(ATM atm, String transactionType) {
        System.out.println("Transaction already in progress.");
    }
    
    @Override
    public void dispenseCash(ATM atm, int amount) {
        if (atm.hasSufficientCash(amount) && atm.hasSufficientBalance(amount)) {
            System.out.println("Dispensing $" + amount);
            atm.dispenseCash(amount);
            atm.deductBalance(amount);
            System.out.println("Transaction completed. Please take your cash.");
            
            // Return to authenticated state for more transactions
            atm.setState(atm.getAuthenticatedState());
        } else if (!atm.hasSufficientBalance(amount)) {
            System.out.println("Insufficient account balance.");
            atm.setState(atm.getAuthenticatedState());
        } else {
            System.out.println("ATM out of cash. Transaction cancelled.");
            atm.setState(atm.getOutOfServiceState());
        }
    }
    
    @Override
    public String getStateDescription() {
        return "Processing transaction";
    }
}

public class BlockedState implements ATMState {
    @Override
    public void insertCard(ATM atm) {
        System.out.println("This card is blocked. Please contact your bank.");
    }
    
    @Override
    public void ejectCard(ATM atm) {
        System.out.println("Blocked card ejected.");
        atm.setState(atm.getNoCardState());
    }
    
    @Override
    public void enterPIN(ATM atm, String pin) {
        System.out.println("Card is blocked.");
    }
    
    @Override
    public void selectTransaction(ATM atm, String transactionType) {
        System.out.println("Card is blocked.");
    }
    
    @Override
    public void dispenseCash(ATM atm, int amount) {
        System.out.println("Card is blocked.");
    }
    
    @Override
    public String getStateDescription() {
        return "Card blocked due to invalid PIN attempts";
    }
}

public class OutOfServiceState implements ATMState {
    @Override
    public void insertCard(ATM atm) {
        System.out.println("ATM is out of service. Card ejected.");
    }
    
    @Override
    public void ejectCard(ATM atm) {
        System.out.println("ATM is out of service.");
    }
    
    @Override
    public void enterPIN(ATM atm, String pin) {
        System.out.println("ATM is out of service.");
    }
    
    @Override
    public void selectTransaction(ATM atm, String transactionType) {
        System.out.println("ATM is out of service.");
    }
    
    @Override
    public void dispenseCash(ATM atm, int amount) {
        System.out.println("ATM is out of service.");
    }
    
    @Override
    public String getStateDescription() {
        return "Out of service";
    }
}

// ATM Context
public class ATM {
    private ATMState noCardState;
    private ATMState hasCardState;
    private ATMState authenticatedState;
    private ATMState transactionState;
    private ATMState blockedState;
    private ATMState outOfServiceState;
    
    private ATMState currentState;
    private int cashAmount;
    private int accountBalance;
    private String correctPIN = "1234";
    
    public ATM(int cashAmount, int accountBalance) {
        this.cashAmount = cashAmount;
        this.accountBalance = accountBalance;
        
        // Initialize states
        noCardState = new NoCardState();
        hasCardState = new HasCardState();
        authenticatedState = new AuthenticatedState();
        transactionState = new TransactionState();
        blockedState = new BlockedState();
        outOfServiceState = new OutOfServiceState();
        
        // Set initial state
        currentState = (cashAmount > 0) ? noCardState : outOfServiceState;
    }
    
    // Delegate methods to current state
    public void insertCard() { currentState.insertCard(this); }
    public void ejectCard() { currentState.ejectCard(this); }
    public void enterPIN(String pin) { currentState.enterPIN(this, pin); }
    public void selectTransaction(String type) { currentState.selectTransaction(this, type); }
    public void dispenseCash(int amount) { currentState.dispenseCash(this, amount); }
    
    // State management
    public void setState(ATMState state) {
        this.currentState = state;
        System.out.println("ATM State: " + state.getStateDescription());
    }
    
    // Business logic methods
    public boolean validatePIN(String pin) {
        return correctPIN.equals(pin);
    }
    
    public boolean hasSufficientCash(int amount) {
        return cashAmount >= amount;
    }
    
    public boolean hasSufficientBalance(int amount) {
        return accountBalance >= amount;
    }
    
    public void dispenseCash(int amount) {
        cashAmount -= amount;
    }
    
    public void deductBalance(int amount) {
        accountBalance -= amount;
    }
    
    // Getters
    public ATMState getNoCardState() { return noCardState; }
    public ATMState getHasCardState() { return hasCardState; }
    public ATMState getAuthenticatedState() { return authenticatedState; }
    public ATMState getTransactionState() { return transactionState; }
    public ATMState getBlockedState() { return blockedState; }
    public ATMState getOutOfServiceState() { return outOfServiceState; }
    
    public int getAccountBalance() { return accountBalance; }
    public int getCashAmount() { return cashAmount; }
    public String getCurrentStateDescription() { return currentState.getStateDescription(); }
}

// Usage
public class ATMDemo {
    public static void main(String[] args) {
        ATM atm = new ATM(1000, 500); // $1000 in ATM, $500 in account
        
        System.out.println("=== ATM State Pattern Demo ===");
        System.out.println("Initial state: " + atm.getCurrentStateDescription());
        
        // Successful transaction flow
        System.out.println("\n--- Successful Transaction ---");
        atm.insertCard();
        atm.enterPIN("1234");
        atm.selectTransaction("balance");
        
        // Withdrawal transaction
        System.out.println("\n--- Withdrawal Transaction ---");
        atm.insertCard();
        atm.enterPIN("1234");
        atm.selectTransaction("withdraw");
        atm.dispenseCash(100);
        atm.ejectCard();
        
        // Invalid PIN attempts
        System.out.println("\n--- Invalid PIN Attempts ---");
        atm.insertCard();
        atm.enterPIN("0000"); // Wrong PIN
        atm.enterPIN("1111"); // Wrong PIN
        atm.enterPIN("2222"); // Wrong PIN - should block card
        atm.ejectCard();
        
        // Insufficient funds
        System.out.println("\n--- Insufficient Funds ---");
        atm.insertCard();
        atm.enterPIN("1234");
        atm.selectTransaction("withdraw");
        atm.dispenseCash(1000); // More than account balance
        
        System.out.println("\nATM Demo completed!");
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **State Pattern** organizes state-specific behavior into separate classes
2. **Eliminates complex conditionals** based on state
3. **Makes state transitions explicit** và manageable
4. **Follows Open/Closed Principle** cho state management

### So sánh với patterns khác
| Pattern | Purpose | Use Case |
|---------|---------|----------|
| **State** | Change behavior based on state | State machines, workflows |
| **Strategy** | Change algorithm at runtime | Algorithm selection |
| **Command** | Encapsulate requests | Undo/redo, queuing |
| **Observer** | Notify multiple objects | Event handling |

### Best Practices
- **Keep states simple** và focused
- **Make state transitions explicit**
- **Consider state lifecycle** management
- **Use state factory** cho complex state creation
- **Document state diagram** cho clarity

---

**Tiếp theo:** [Template Method](template-method.md) - Định nghĩa khung thuật toán
