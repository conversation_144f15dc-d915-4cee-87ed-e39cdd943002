# Observer Pattern

> **Behavioral Pattern** - <PERSON><PERSON><PERSON> nghĩa cơ chế subscription để thông báo cho nhiều đối tượng về events xảy ra với đối tượng đang quan sát

## 📋 <PERSON><PERSON><PERSON> lụ<PERSON>

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON><PERSON> triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON><PERSON><PERSON>-điểm)
7. [<PERSON>hi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Đ<PERSON><PERSON> nghĩa
Observer là m<PERSON>t **behavioral design pattern** cho phép **<PERSON><PERSON><PERSON> nghĩa cơ chế subscription** để thông báo cho nhiều đối tượng về bất kỳ events nào xảy ra với đối tượng mà chúng đang quan sát.

### Mục đích chính
- **Thiết lập one-to-many dependency** giữa objects
- **Automatic notification** khi state thay đổi
- **Loose coupling** giữa subject và observers

### Tên gọi khác
- **Publish-Subscribe Pattern**
- **Model-View Pattern**
- **Dependents Pattern**

### Ví dụ thực tế
Giống như **subscription to newspaper**: khi có báo mới, tất cả subscribers sẽ được thông báo. Subscribers có thể subscribe/unsubscribe bất cứ lúc nào.

---

## Vấn đề

### Tình huống thực tế
Bạn có hai loại objects: `Customer` và `Store`. Customer quan tâm đến một sản phẩm cụ thể sẽ có sẵn trong store.

```java
// Customer phải liên tục check store
public class Customer {
    public void checkProduct(Store store) {
        while (true) {
            if (store.isProductAvailable("iPhone")) {
                System.out.println("iPhone is available!");
                break;
            }
            // Waste resources checking repeatedly
            Thread.sleep(1000);
        }
    }
}
```

### Vấn đề phát sinh

#### 1. Resource waste (Polling)
```java
// Multiple customers checking continuously
public class StoreChecker {
    public void startChecking() {
        while (true) {
            // Waste CPU cycles
            checkAllProducts();
            Thread.sleep(100); // Still wasteful
        }
    }
}
```

#### 2. Tight coupling
```java
public class Store {
    public void addProduct(Product product) {
        products.add(product);
        
        // Tightly coupled to specific customers
        customer1.notifyProductAvailable(product);
        customer2.notifyProductAvailable(product);
        customer3.notifyProductAvailable(product);
        // Hard to maintain!
    }
}
```

#### 3. Không linh hoạt
```java
// Khó thêm/bớt customers
public class Store {
    private Customer customer1, customer2, customer3;
    
    // Phải modify code để thêm customer mới
    public void addCustomer4(Customer customer4) {
        this.customer4 = customer4;
        // Update all notification methods!
    }
}
```

#### 4. Missed notifications
```java
// Customer có thể miss notification nếu không check đúng lúc
public class Customer {
    public void checkPeriodically() {
        // Check every 5 minutes
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            public void run() {
                checkStore(); // Might miss product that was available for 2 minutes
            }
        }, 0, 300000);
    }
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Observer pattern đề xuất **thêm subscription mechanism** vào publisher class để individual objects có thể subscribe/unsubscribe khỏi stream of events.

### Cách hoạt động

#### 1. Subject interface
```java
public interface Subject {
    void subscribe(Observer observer);
    void unsubscribe(Observer observer);
    void notifyObservers();
}
```

#### 2. Observer interface
```java
public interface Observer {
    void update(String message);
}
```

#### 3. Concrete Subject
```java
public class Store implements Subject {
    private List<Observer> observers = new ArrayList<>();
    private List<String> products = new ArrayList<>();
    
    @Override
    public void subscribe(Observer observer) {
        observers.add(observer);
        System.out.println("Observer subscribed");
    }
    
    @Override
    public void unsubscribe(Observer observer) {
        observers.remove(observer);
        System.out.println("Observer unsubscribed");
    }
    
    @Override
    public void notifyObservers() {
        for (Observer observer : observers) {
            observer.update("New product available!");
        }
    }
    
    public void addProduct(String product) {
        products.add(product);
        System.out.println("Product added: " + product);
        notifyObservers(); // Automatic notification
    }
}
```

#### 4. Concrete Observers
```java
public class Customer implements Observer {
    private String name;
    
    public Customer(String name) {
        this.name = name;
    }
    
    @Override
    public void update(String message) {
        System.out.println(name + " received notification: " + message);
    }
}
```

#### 5. Client usage
```java
public class ObserverDemo {
    public static void main(String[] args) {
        Store store = new Store();
        
        Customer alice = new Customer("Alice");
        Customer bob = new Customer("Bob");
        Customer charlie = new Customer("Charlie");
        
        // Subscribe to notifications
        store.subscribe(alice);
        store.subscribe(bob);
        store.subscribe(charlie);
        
        // Add products - all customers notified automatically
        store.addProduct("iPhone 15");
        store.addProduct("MacBook Pro");
        
        // Bob unsubscribes
        store.unsubscribe(bob);
        
        // Only Alice and Charlie notified
        store.addProduct("iPad");
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Subject (interface)
├── + subscribe(Observer): void
├── + unsubscribe(Observer): void
├── + notifyObservers(): void
│
ConcreteSubject implements Subject
├── - observers: List<Observer>
├── - state: Object
├── + getState(): Object
├── + setState(Object): void
│
Observer (interface)
├── + update(): void
│
ConcreteObserver implements Observer
├── - subject: Subject
├── + update(): void
```

### Các thành phần chính

#### 1. Subject (Publisher)
- **Vai trò:** Maintains list of observers và notifies them
- **Đặc điểm:** Provides subscription interface

#### 2. Observer (Subscriber)
- **Vai trò:** Defines updating interface cho objects cần notification
- **Đặc điểm:** Receives updates from subject

#### 3. Concrete Subject
- **Vai trò:** Stores state và sends notifications khi state changes
- **Đặc điểm:** Implements notification logic

#### 4. Concrete Observer
- **Vai trò:** Implements Observer interface
- **Đặc điểm:** Maintains reference to subject và implements update logic

---

## Cách triển khai

### Bước 1: Định nghĩa interfaces
```java
public interface Observable {
    void addObserver(Observer observer);
    void removeObserver(Observer observer);
    void notifyObservers(Object data);
}

public interface Observer {
    void update(Observable observable, Object data);
}
```

### Bước 2: Implement Concrete Subject
```java
public class WeatherStation implements Observable {
    private List<Observer> observers = new ArrayList<>();
    private float temperature;
    private float humidity;
    private float pressure;
    
    @Override
    public void addObserver(Observer observer) {
        observers.add(observer);
    }
    
    @Override
    public void removeObserver(Observer observer) {
        observers.remove(observer);
    }
    
    @Override
    public void notifyObservers(Object data) {
        for (Observer observer : observers) {
            observer.update(this, data);
        }
    }
    
    public void setMeasurements(float temperature, float humidity, float pressure) {
        this.temperature = temperature;
        this.humidity = humidity;
        this.pressure = pressure;
        measurementsChanged();
    }
    
    private void measurementsChanged() {
        WeatherData data = new WeatherData(temperature, humidity, pressure);
        notifyObservers(data);
    }
    
    // Getters
    public float getTemperature() { return temperature; }
    public float getHumidity() { return humidity; }
    public float getPressure() { return pressure; }
}
```

### Bước 3: Implement Concrete Observers
```java
public class CurrentConditionsDisplay implements Observer {
    private float temperature;
    private float humidity;
    
    @Override
    public void update(Observable observable, Object data) {
        if (data instanceof WeatherData) {
            WeatherData weatherData = (WeatherData) data;
            this.temperature = weatherData.getTemperature();
            this.humidity = weatherData.getHumidity();
            display();
        }
    }
    
    public void display() {
        System.out.println("Current conditions: " + temperature + "°C and " + humidity + "% humidity");
    }
}

public class StatisticsDisplay implements Observer {
    private List<Float> temperatures = new ArrayList<>();
    
    @Override
    public void update(Observable observable, Object data) {
        if (data instanceof WeatherData) {
            WeatherData weatherData = (WeatherData) data;
            temperatures.add(weatherData.getTemperature());
            display();
        }
    }
    
    public void display() {
        float avg = (float) temperatures.stream().mapToDouble(Float::doubleValue).average().orElse(0.0);
        float max = temperatures.stream().max(Float::compare).orElse(0.0f);
        float min = temperatures.stream().min(Float::compare).orElse(0.0f);
        
        System.out.println("Avg/Max/Min temperature: " + avg + "/" + max + "/" + min);
    }
}
```

### Bước 4: Usage
```java
public class WeatherStationDemo {
    public static void main(String[] args) {
        WeatherStation weatherStation = new WeatherStation();
        
        CurrentConditionsDisplay currentDisplay = new CurrentConditionsDisplay();
        StatisticsDisplay statisticsDisplay = new StatisticsDisplay();
        
        weatherStation.addObserver(currentDisplay);
        weatherStation.addObserver(statisticsDisplay);
        
        // Simulate weather changes
        weatherStation.setMeasurements(25.0f, 65.0f, 1013.25f);
        weatherStation.setMeasurements(27.0f, 70.0f, 1012.0f);
        weatherStation.setMeasurements(23.0f, 60.0f, 1014.0f);
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Loose coupling
```java
// Subject không cần biết concrete observer classes
public class NewsAgency implements Subject {
    public void publishNews(String news) {
        // Chỉ biết Observer interface
        notifyObservers(news); // Works with any Observer implementation
    }
}
```

#### 2. Dynamic relationships
```java
// Có thể add/remove observers runtime
NewsAgency agency = new NewsAgency();
NewsChannel cnn = new NewsChannel("CNN");
NewsChannel bbc = new NewsChannel("BBC");

agency.subscribe(cnn);    // Add at runtime
agency.subscribe(bbc);
agency.unsubscribe(cnn);  // Remove at runtime
```

#### 3. Tuân thủ Open/Closed Principle
```java
// Có thể thêm observer types mới mà không sửa subject
public class EmailNotifier implements Observer {
    public void update(String news) {
        sendEmail(news); // New observer type
    }
}

// Subject code không cần thay đổi
```

#### 4. Broadcast communication
```java
// One subject có thể notify multiple observers
stockPrice.setValue(100.50); // Notifies all investors, displays, loggers, etc.
```

### ❌ Nhược điểm

#### 1. Memory leaks nếu không unsubscribe
```java
public class MemoryLeakExample {
    public void createObserver() {
        Observer observer = new SomeObserver();
        subject.subscribe(observer);
        // Forgot to unsubscribe - observer won't be garbage collected!
    }
}
```

#### 2. Unexpected updates
```java
// Observers có thể trigger cascading updates
public class CascadingObserver implements Observer {
    public void update(Object data) {
        // This might trigger more notifications
        anotherSubject.setState(newValue);
    }
}
```

#### 3. No guarantee về notification order
```java
// Order of notification không được đảm bảo
subject.notifyObservers(); // Observer A hoặc B có thể được notify trước
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. State change cần notify multiple objects
```java
// Model-View architecture
public class UserModel implements Observable {
    private String name;
    
    public void setName(String name) {
        this.name = name;
        notifyObservers(); // Notify all views
    }
}

// Multiple views update automatically
UserView profileView = new UserView();
UserView headerView = new UserView();
```

#### 2. One-to-many communication
```java
// Event system
public class Button implements Observable {
    public void click() {
        notifyObservers("button_clicked"); // Multiple listeners
    }
}
```

#### 3. Decoupling objects
```java
// Logger system
public class Application {
    private Logger logger = Logger.getInstance();
    
    public void performAction() {
        // Don't need to know about specific loggers
        logger.log("Action performed");
    }
}
```

### ❌ Không nên sử dụng khi:

#### 1. Simple one-to-one relationships
```java
// Direct method call đơn giản hơn
public class Calculator {
    public void calculate() {
        display.show(result); // Direct call, no need for Observer
    }
}
```

#### 2. Performance critical applications
```java
// Observer pattern có overhead
public class HighFrequencyTrading {
    public void updatePrice(double price) {
        // Direct updates faster than notifications
        display.updatePrice(price);
    }
}
```

---

## Ví dụ thực tế

### Ví dụ 1: Stock Market Monitoring

```java
// Subject interface
public interface Stock {
    void addObserver(StockObserver observer);
    void removeObserver(StockObserver observer);
    void notifyObservers();
}

// Observer interface
public interface StockObserver {
    void update(String stockSymbol, double price);
}

// Concrete Subject
public class StockPrice implements Stock {
    private List<StockObserver> observers = new ArrayList<>();
    private String symbol;
    private double price;
    
    public StockPrice(String symbol, double price) {
        this.symbol = symbol;
        this.price = price;
    }
    
    @Override
    public void addObserver(StockObserver observer) {
        observers.add(observer);
    }
    
    @Override
    public void removeObserver(StockObserver observer) {
        observers.remove(observer);
    }
    
    @Override
    public void notifyObservers() {
        for (StockObserver observer : observers) {
            observer.update(symbol, price);
        }
    }
    
    public void setPrice(double price) {
        this.price = price;
        System.out.println(symbol + " price changed to $" + price);
        notifyObservers();
    }
    
    public double getPrice() { return price; }
    public String getSymbol() { return symbol; }
}

// Concrete Observers
public class Investor implements StockObserver {
    private String name;
    private double buyThreshold;
    private double sellThreshold;
    
    public Investor(String name, double buyThreshold, double sellThreshold) {
        this.name = name;
        this.buyThreshold = buyThreshold;
        this.sellThreshold = sellThreshold;
    }
    
    @Override
    public void update(String stockSymbol, double price) {
        if (price <= buyThreshold) {
            System.out.println(name + ": BUY signal for " + stockSymbol + " at $" + price);
        } else if (price >= sellThreshold) {
            System.out.println(name + ": SELL signal for " + stockSymbol + " at $" + price);
        }
    }
}

public class StockDisplay implements StockObserver {
    private String displayName;
    
    public StockDisplay(String displayName) {
        this.displayName = displayName;
    }
    
    @Override
    public void update(String stockSymbol, double price) {
        System.out.println(displayName + " Display: " + stockSymbol + " = $" + price);
    }
}

// Usage
public class StockMarketDemo {
    public static void main(String[] args) {
        StockPrice appleStock = new StockPrice("AAPL", 150.00);
        
        Investor warren = new Investor("Warren", 140.00, 160.00);
        Investor peter = new Investor("Peter", 145.00, 155.00);
        StockDisplay mainDisplay = new StockDisplay("Main Board");
        StockDisplay mobileApp = new StockDisplay("Mobile App");
        
        // Subscribe observers
        appleStock.addObserver(warren);
        appleStock.addObserver(peter);
        appleStock.addObserver(mainDisplay);
        appleStock.addObserver(mobileApp);
        
        // Simulate price changes
        appleStock.setPrice(145.00); // Peter gets buy signal
        appleStock.setPrice(138.00); // Warren gets buy signal
        appleStock.setPrice(162.00); // Both get sell signals
        
        // Peter stops monitoring
        appleStock.removeObserver(peter);
        appleStock.setPrice(135.00); // Only Warren gets notification
    }
}
```

### Ví dụ 2: Event Management System

```java
// Event types
public enum EventType {
    USER_LOGIN, USER_LOGOUT, ORDER_PLACED, PAYMENT_PROCESSED
}

// Event data
public class Event {
    private EventType type;
    private Object data;
    private long timestamp;
    
    public Event(EventType type, Object data) {
        this.type = type;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }
    
    // Getters
    public EventType getType() { return type; }
    public Object getData() { return data; }
    public long getTimestamp() { return timestamp; }
}

// Event Manager (Subject)
public class EventManager {
    private Map<EventType, List<EventListener>> listeners = new HashMap<>();
    
    public void subscribe(EventType eventType, EventListener listener) {
        listeners.computeIfAbsent(eventType, k -> new ArrayList<>()).add(listener);
    }
    
    public void unsubscribe(EventType eventType, EventListener listener) {
        List<EventListener> eventListeners = listeners.get(eventType);
        if (eventListeners != null) {
            eventListeners.remove(listener);
        }
    }
    
    public void notify(Event event) {
        List<EventListener> eventListeners = listeners.get(event.getType());
        if (eventListeners != null) {
            for (EventListener listener : eventListeners) {
                listener.handle(event);
            }
        }
    }
}

// Event Listener interface
public interface EventListener {
    void handle(Event event);
}

// Concrete Listeners
public class EmailNotificationListener implements EventListener {
    @Override
    public void handle(Event event) {
        switch (event.getType()) {
            case USER_LOGIN:
                System.out.println("Email: Welcome back, " + event.getData() + "!");
                break;
            case ORDER_PLACED:
                System.out.println("Email: Order confirmation sent for order " + event.getData());
                break;
        }
    }
}

public class AuditLogListener implements EventListener {
    @Override
    public void handle(Event event) {
        System.out.println("AUDIT LOG: " + event.getType() + " at " + 
                          new Date(event.getTimestamp()) + " with data: " + event.getData());
    }
}

public class AnalyticsListener implements EventListener {
    @Override
    public void handle(Event event) {
        System.out.println("ANALYTICS: Recording " + event.getType() + " event");
        // Send to analytics service
    }
}

// Usage
public class EventSystemDemo {
    public static void main(String[] args) {
        EventManager eventManager = new EventManager();
        
        // Create listeners
        EmailNotificationListener emailListener = new EmailNotificationListener();
        AuditLogListener auditListener = new AuditLogListener();
        AnalyticsListener analyticsListener = new AnalyticsListener();
        
        // Subscribe to events
        eventManager.subscribe(EventType.USER_LOGIN, emailListener);
        eventManager.subscribe(EventType.USER_LOGIN, auditListener);
        eventManager.subscribe(EventType.USER_LOGIN, analyticsListener);
        
        eventManager.subscribe(EventType.ORDER_PLACED, emailListener);
        eventManager.subscribe(EventType.ORDER_PLACED, auditListener);
        eventManager.subscribe(EventType.ORDER_PLACED, analyticsListener);
        
        // Trigger events
        eventManager.notify(new Event(EventType.USER_LOGIN, "<EMAIL>"));
        
        System.out.println("---");
        
        eventManager.notify(new Event(EventType.ORDER_PLACED, "ORDER-12345"));
        
        System.out.println("---");
        
        // Unsubscribe email from login events
        eventManager.unsubscribe(EventType.USER_LOGIN, emailListener);
        eventManager.notify(new Event(EventType.USER_LOGIN, "<EMAIL>"));
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Observer Pattern** thiết lập one-to-many dependency giữa objects
2. **Automatic notification** khi subject state changes
3. **Loose coupling** giữa subject và observers
4. **Dynamic subscription/unsubscription** support

### So sánh với patterns khác
| Pattern | Communication | Coupling | Use Case |
|---------|---------------|----------|----------|
| **Observer** | One-to-many broadcast | Loose | State change notifications |
| **Mediator** | Many-to-many through mediator | Loose | Complex object interactions |
| **Command** | One-to-one with queuing | Medium | Action encapsulation |

### Best Practices
- **Always unsubscribe** để tránh memory leaks
- **Consider using weak references** cho observers
- **Handle exceptions** trong observer updates
- **Document notification order** nếu quan trọng
- **Use event objects** thay vì primitive parameters

---

**Tiếp theo:** [Strategy](strategy.md) - Đóng gói thuật toán và làm chúng có thể hoán đổi
