# Command Pattern

> **Behavioral Pattern** - <PERSON><PERSON><PERSON> gói yêu cầu thành đối tượ<PERSON>, cho phép tham số hóa clients với các yêu cầu khác nhau

## 📋 <PERSON><PERSON><PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#gi<PERSON><PERSON>-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON>ợ<PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Command là một **behavioral design pattern** **biế<PERSON> requests thành stand-alone objects** ch<PERSON><PERSON> tất cả thông tin về request. Transformation này cho phé<PERSON> pass requests như method arguments, delay hoặc queue request execution, và support undoable operations.

### <PERSON><PERSON><PERSON> đích chính
- **Encapsulate requests** thành objects
- **Decouple sender** khỏi receiver
- **Support undo/redo** operations
- **Queue, log, và schedule** requests

### Tên gọi khác
- **Action Pattern**
- **Transaction Pattern**

### Ví dụ thực tế
Giống như **remote control**: mỗi button là một command object. Khi bấm button, nó execute command mà không cần biết TV, stereo hay air conditioner sẽ làm gì. Remote có thể undo last command hoặc program macro commands.

---

## Vấn đề

### Tình huống thực tế
Bạn đang phát triển text editor với GUI buttons:

```java
public class TextEditor {
    private String content = "";
    
    public void copy() {
        // Copy logic
        System.out.println("Copying text");
    }
    
    public void paste() {
        // Paste logic
        System.out.println("Pasting text");
    }
    
    public void cut() {
        // Cut logic
        System.out.println("Cutting text");
    }
}

public class Button {
    private TextEditor editor;
    
    public Button(TextEditor editor) {
        this.editor = editor;
    }
    
    public void onClick() {
        // Problem: Button tightly coupled to specific action
        editor.copy(); // Hard-coded action!
    }
}
```

### Vấn đề phát sinh

#### 1. Tight coupling
```java
// Mỗi button phải biết specific method để call
public class CopyButton extends Button {
    public void onClick() {
        editor.copy(); // Tightly coupled
    }
}

public class PasteButton extends Button {
    public void onClick() {
        editor.paste(); // Tightly coupled
    }
}

// Cần tạo class mới cho mỗi action!
```

#### 2. No undo functionality
```java
public class TextEditor {
    public void delete() {
        content = ""; // How to undo this?
    }
    
    public void insertText(String text) {
        content += text; // How to undo this?
    }
    
    // No way to track previous states
}
```

#### 3. Cannot queue or schedule operations
```java
// Không thể delay execution
button.onClick(); // Executes immediately

// Không thể queue multiple operations
// Không thể batch operations
// Không thể schedule for later
```

#### 4. No macro support
```java
// Không thể record sequence of operations
// Không thể replay operations
// Không thể create complex workflows
```

#### 5. Difficult testing
```java
// Hard to test individual operations
// Cannot mock operations easily
// Cannot verify operation calls
```

---

## Giải pháp

### Ý tưởng cốt lõi
Command pattern đề xuất **không gọi methods trực tiếp**. Thay vào đó, extract request details như object being called, method name, và method arguments vào **separate command class** với single method để trigger request.

### Cách hoạt động

#### 1. Command interface
```java
public interface Command {
    void execute();
    void undo(); // Optional: for undoable commands
}
```

#### 2. Concrete Commands
```java
public class CopyCommand implements Command {
    private TextEditor editor;
    
    public CopyCommand(TextEditor editor) {
        this.editor = editor;
    }
    
    @Override
    public void execute() {
        editor.copy();
    }
    
    @Override
    public void undo() {
        // Copy operation usually doesn't need undo
        System.out.println("Copy cannot be undone");
    }
}

public class PasteCommand implements Command {
    private TextEditor editor;
    private String previousContent;
    
    public PasteCommand(TextEditor editor) {
        this.editor = editor;
    }
    
    @Override
    public void execute() {
        previousContent = editor.getContent(); // Save state for undo
        editor.paste();
    }
    
    @Override
    public void undo() {
        editor.setContent(previousContent); // Restore previous state
    }
}

public class CutCommand implements Command {
    private TextEditor editor;
    private String previousContent;
    
    public CutCommand(TextEditor editor) {
        this.editor = editor;
    }
    
    @Override
    public void execute() {
        previousContent = editor.getContent();
        editor.cut();
    }
    
    @Override
    public void undo() {
        editor.setContent(previousContent);
    }
}
```

#### 3. Invoker (Button)
```java
public class Button {
    private Command command;
    
    public Button(Command command) {
        this.command = command;
    }
    
    public void setCommand(Command command) {
        this.command = command;
    }
    
    public void click() {
        if (command != null) {
            command.execute();
        }
    }
}
```

#### 4. Command Manager (for undo/redo)
```java
public class CommandManager {
    private Stack<Command> history = new Stack<>();
    private Stack<Command> redoStack = new Stack<>();
    
    public void executeCommand(Command command) {
        command.execute();
        history.push(command);
        redoStack.clear(); // Clear redo stack when new command executed
    }
    
    public void undo() {
        if (!history.isEmpty()) {
            Command command = history.pop();
            command.undo();
            redoStack.push(command);
        }
    }
    
    public void redo() {
        if (!redoStack.isEmpty()) {
            Command command = redoStack.pop();
            command.execute();
            history.push(command);
        }
    }
    
    public boolean canUndo() {
        return !history.isEmpty();
    }
    
    public boolean canRedo() {
        return !redoStack.isEmpty();
    }
}
```

#### 5. Usage
```java
public class TextEditorApp {
    public static void main(String[] args) {
        TextEditor editor = new TextEditor();
        CommandManager commandManager = new CommandManager();
        
        // Create commands
        Command copyCommand = new CopyCommand(editor);
        Command pasteCommand = new PasteCommand(editor);
        Command cutCommand = new CutCommand(editor);
        
        // Create buttons with commands
        Button copyButton = new Button(copyCommand);
        Button pasteButton = new Button(pasteCommand);
        Button cutButton = new Button(cutCommand);
        
        // Execute commands
        editor.setContent("Hello World");
        
        copyButton.click();  // Copy
        cutButton.click();   // Cut
        pasteButton.click(); // Paste
        
        // Undo/Redo
        commandManager.executeCommand(cutCommand);
        commandManager.executeCommand(pasteCommand);
        
        commandManager.undo(); // Undo paste
        commandManager.undo(); // Undo cut
        commandManager.redo(); // Redo cut
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Command (interface)
├── + execute(): void
├── + undo(): void
│
ConcreteCommand implements Command
├── - receiver: Receiver
├── - state: Object
├── + execute(): void
├── + undo(): void
│
Invoker
├── - command: Command
├── + setCommand(Command): void
├── + executeCommand(): void
│
Receiver
├── + action(): void
│
Client
├── creates → ConcreteCommand
├── sets → Invoker
```

### Các thành phần chính

#### 1. Command Interface
- **Vai trò:** Declares method cho executing commands
- **Đặc điểm:** Usually has execute() và optional undo()

#### 2. Concrete Command
- **Vai trò:** Implements Command interface
- **Đặc điểm:** Stores receiver và invokes receiver's methods

#### 3. Receiver
- **Vai trò:** Knows how to perform operations
- **Đặc điểm:** Any object có thể serve as receiver

#### 4. Invoker
- **Vai trò:** Asks command to carry out request
- **Đặc điểm:** Doesn't know about concrete commands

#### 5. Client
- **Vai trò:** Creates concrete command objects
- **Đặc điểm:** Sets receiver của command

---

## Cách triển khai

### Bước 1: Định nghĩa Command interface
```java
public interface Command {
    void execute();
    void undo();
    String getDescription();
}
```

### Bước 2: Create Receiver classes
```java
public class Light {
    private boolean isOn = false;
    private int brightness = 0;
    
    public void turnOn() {
        isOn = true;
        brightness = 100;
        System.out.println("Light is ON (brightness: " + brightness + ")");
    }
    
    public void turnOff() {
        isOn = false;
        brightness = 0;
        System.out.println("Light is OFF");
    }
    
    public void dim(int level) {
        if (isOn) {
            brightness = Math.max(0, Math.min(100, level));
            System.out.println("Light dimmed to " + brightness + "%");
        }
    }
    
    public boolean isOn() { return isOn; }
    public int getBrightness() { return brightness; }
}

public class Fan {
    private boolean isOn = false;
    private int speed = 0; // 0-3
    
    public void turnOn() {
        isOn = true;
        speed = 1;
        System.out.println("Fan is ON (speed: " + speed + ")");
    }
    
    public void turnOff() {
        isOn = false;
        speed = 0;
        System.out.println("Fan is OFF");
    }
    
    public void setSpeed(int speed) {
        if (isOn) {
            this.speed = Math.max(0, Math.min(3, speed));
            System.out.println("Fan speed set to " + this.speed);
        }
    }
    
    public boolean isOn() { return isOn; }
    public int getSpeed() { return speed; }
}
```

### Bước 3: Implement Concrete Commands
```java
public class LightOnCommand implements Command {
    private Light light;
    private boolean previousState;
    private int previousBrightness;
    
    public LightOnCommand(Light light) {
        this.light = light;
    }
    
    @Override
    public void execute() {
        previousState = light.isOn();
        previousBrightness = light.getBrightness();
        light.turnOn();
    }
    
    @Override
    public void undo() {
        if (previousState) {
            light.turnOn();
            light.dim(previousBrightness);
        } else {
            light.turnOff();
        }
    }
    
    @Override
    public String getDescription() {
        return "Turn Light ON";
    }
}

public class LightOffCommand implements Command {
    private Light light;
    private boolean previousState;
    private int previousBrightness;
    
    public LightOffCommand(Light light) {
        this.light = light;
    }
    
    @Override
    public void execute() {
        previousState = light.isOn();
        previousBrightness = light.getBrightness();
        light.turnOff();
    }
    
    @Override
    public void undo() {
        if (previousState) {
            light.turnOn();
            light.dim(previousBrightness);
        }
    }
    
    @Override
    public String getDescription() {
        return "Turn Light OFF";
    }
}

public class FanHighCommand implements Command {
    private Fan fan;
    private boolean previousState;
    private int previousSpeed;
    
    public FanHighCommand(Fan fan) {
        this.fan = fan;
    }
    
    @Override
    public void execute() {
        previousState = fan.isOn();
        previousSpeed = fan.getSpeed();
        
        if (!fan.isOn()) {
            fan.turnOn();
        }
        fan.setSpeed(3);
    }
    
    @Override
    public void undo() {
        if (previousState) {
            fan.setSpeed(previousSpeed);
        } else {
            fan.turnOff();
        }
    }
    
    @Override
    public String getDescription() {
        return "Set Fan to HIGH speed";
    }
}
```

### Bước 4: Macro Command
```java
public class MacroCommand implements Command {
    private Command[] commands;
    
    public MacroCommand(Command[] commands) {
        this.commands = commands;
    }
    
    @Override
    public void execute() {
        for (Command command : commands) {
            command.execute();
        }
    }
    
    @Override
    public void undo() {
        // Undo in reverse order
        for (int i = commands.length - 1; i >= 0; i--) {
            commands[i].undo();
        }
    }
    
    @Override
    public String getDescription() {
        StringBuilder sb = new StringBuilder("Macro: ");
        for (int i = 0; i < commands.length; i++) {
            sb.append(commands[i].getDescription());
            if (i < commands.length - 1) {
                sb.append(", ");
            }
        }
        return sb.toString();
    }
}
```

### Bước 5: Remote Control (Invoker)
```java
public class RemoteControl {
    private Command[] onCommands;
    private Command[] offCommands;
    private Command lastCommand;
    private static final int NUM_SLOTS = 7;
    
    public RemoteControl() {
        onCommands = new Command[NUM_SLOTS];
        offCommands = new Command[NUM_SLOTS];
        
        Command noCommand = new NoCommand();
        for (int i = 0; i < NUM_SLOTS; i++) {
            onCommands[i] = noCommand;
            offCommands[i] = noCommand;
        }
        lastCommand = noCommand;
    }
    
    public void setCommand(int slot, Command onCommand, Command offCommand) {
        onCommands[slot] = onCommand;
        offCommands[slot] = offCommand;
    }
    
    public void onButtonPressed(int slot) {
        onCommands[slot].execute();
        lastCommand = onCommands[slot];
    }
    
    public void offButtonPressed(int slot) {
        offCommands[slot].execute();
        lastCommand = offCommands[slot];
    }
    
    public void undoButtonPressed() {
        lastCommand.undo();
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("\n------ Remote Control ------\n");
        for (int i = 0; i < onCommands.length; i++) {
            sb.append("[slot ").append(i).append("] ")
              .append(onCommands[i].getDescription())
              .append("    ")
              .append(offCommands[i].getDescription())
              .append("\n");
        }
        sb.append("[undo] ").append(lastCommand.getDescription()).append("\n");
        return sb.toString();
    }
}

// Null Object pattern for empty slots
public class NoCommand implements Command {
    @Override
    public void execute() {}
    
    @Override
    public void undo() {}
    
    @Override
    public String getDescription() {
        return "No Command";
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Decoupling invoker và receiver
```java
// Invoker không cần biết receiver details
public class Button {
    private Command command;
    
    public void click() {
        command.execute(); // Doesn't know what happens
    }
}
```

#### 2. Undo/Redo support
```java
// Easy to implement undo/redo
public class CommandHistory {
    private Stack<Command> history = new Stack<>();
    
    public void execute(Command command) {
        command.execute();
        history.push(command);
    }
    
    public void undo() {
        if (!history.isEmpty()) {
            history.pop().undo();
        }
    }
}
```

#### 3. Macro commands
```java
// Combine multiple commands
Command[] partyMode = {
    new LightOnCommand(livingRoomLight),
    new FanHighCommand(ceilingFan),
    new StereoOnCommand(stereo)
};
Command partyMacro = new MacroCommand(partyMode);
```

#### 4. Queuing và logging
```java
// Queue commands for later execution
Queue<Command> commandQueue = new LinkedList<>();
commandQueue.offer(new LightOnCommand(light));
commandQueue.offer(new FanHighCommand(fan));

// Execute queued commands
while (!commandQueue.isEmpty()) {
    commandQueue.poll().execute();
}
```

### ❌ Nhược điểm

#### 1. Increased number of classes
```java
// Need separate command class for each operation
public class LightOnCommand implements Command { ... }
public class LightOffCommand implements Command { ... }
public class LightDimCommand implements Command { ... }
// Many small classes
```

#### 2. Memory overhead
```java
// Each command object takes memory
// Command history can grow large
// Need to manage command lifecycle
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Cần undo/redo functionality
```java
// Text editors, image editors, IDEs
public class TextEditor {
    private CommandManager commandManager = new CommandManager();
    
    public void insertText(String text) {
        Command command = new InsertTextCommand(this, text);
        commandManager.executeCommand(command);
    }
}
```

#### 2. Cần queue operations
```java
// Background task processing
public class TaskProcessor {
    private Queue<Command> taskQueue = new LinkedList<>();
    
    public void scheduleTask(Command task) {
        taskQueue.offer(task);
    }
    
    public void processTasks() {
        while (!taskQueue.isEmpty()) {
            taskQueue.poll().execute();
        }
    }
}
```

#### 3. Cần log operations
```java
// Database transactions, audit trails
public class TransactionLogger {
    private List<Command> transactionLog = new ArrayList<>();
    
    public void executeTransaction(Command transaction) {
        transaction.execute();
        transactionLog.add(transaction);
        logToFile(transaction);
    }
}
```

#### 4. Parameterize objects với actions
```java
// GUI buttons, menu items
public class MenuItem {
    private Command action;
    
    public MenuItem(String label, Command action) {
        this.action = action;
    }
    
    public void click() {
        action.execute();
    }
}
```

### ❌ Không nên sử dụng khi:

#### 1. Simple operations
```java
// Overkill for simple method calls
public void turnOnLight() {
    light.turnOn(); // Direct call is simpler
}
```

#### 2. No need for undo/queue/log
```java
// If you don't need command pattern benefits
// Direct method calls are more straightforward
```

---

## Ví dụ thực tế

### Ví dụ 1: Smart Home Remote Control

```java
// Commands for different devices
public class AirConditionerOnCommand implements Command {
    private AirConditioner ac;
    private boolean previousState;
    private int previousTemp;
    
    public AirConditionerOnCommand(AirConditioner ac) {
        this.ac = ac;
    }
    
    @Override
    public void execute() {
        previousState = ac.isOn();
        previousTemp = ac.getTemperature();
        ac.turnOn();
        ac.setTemperature(22); // Default comfortable temperature
    }
    
    @Override
    public void undo() {
        if (previousState) {
            ac.setTemperature(previousTemp);
        } else {
            ac.turnOff();
        }
    }
    
    @Override
    public String getDescription() {
        return "Turn AC ON";
    }
}

public class TVOnCommand implements Command {
    private TV tv;
    private boolean previousState;
    private int previousChannel;
    private int previousVolume;
    
    public TVOnCommand(TV tv) {
        this.tv = tv;
    }
    
    @Override
    public void execute() {
        previousState = tv.isOn();
        if (tv.isOn()) {
            previousChannel = tv.getChannel();
            previousVolume = tv.getVolume();
        }
        tv.turnOn();
        tv.setChannel(1); // Default channel
        tv.setVolume(20); // Default volume
    }
    
    @Override
    public void undo() {
        if (previousState) {
            tv.setChannel(previousChannel);
            tv.setVolume(previousVolume);
        } else {
            tv.turnOff();
        }
    }
    
    @Override
    public String getDescription() {
        return "Turn TV ON";
    }
}

// Device classes
public class AirConditioner {
    private boolean isOn = false;
    private int temperature = 25;
    
    public void turnOn() {
        isOn = true;
        System.out.println("AC is ON");
    }
    
    public void turnOff() {
        isOn = false;
        System.out.println("AC is OFF");
    }
    
    public void setTemperature(int temp) {
        if (isOn) {
            this.temperature = temp;
            System.out.println("AC temperature set to " + temp + "°C");
        }
    }
    
    public boolean isOn() { return isOn; }
    public int getTemperature() { return temperature; }
}

public class TV {
    private boolean isOn = false;
    private int channel = 1;
    private int volume = 10;
    
    public void turnOn() {
        isOn = true;
        System.out.println("TV is ON");
    }
    
    public void turnOff() {
        isOn = false;
        System.out.println("TV is OFF");
    }
    
    public void setChannel(int channel) {
        if (isOn) {
            this.channel = channel;
            System.out.println("TV channel set to " + channel);
        }
    }
    
    public void setVolume(int volume) {
        if (isOn) {
            this.volume = volume;
            System.out.println("TV volume set to " + volume);
        }
    }
    
    public boolean isOn() { return isOn; }
    public int getChannel() { return channel; }
    public int getVolume() { return volume; }
}

// Smart Remote with scenes
public class SmartRemoteControl {
    private Map<String, Command> commands = new HashMap<>();
    private Stack<Command> commandHistory = new Stack<>();
    
    public void setCommand(String buttonName, Command command) {
        commands.put(buttonName, command);
    }
    
    public void pressButton(String buttonName) {
        Command command = commands.get(buttonName);
        if (command != null) {
            System.out.println("Pressing button: " + buttonName);
            command.execute();
            commandHistory.push(command);
        } else {
            System.out.println("No command assigned to button: " + buttonName);
        }
    }
    
    public void undo() {
        if (!commandHistory.isEmpty()) {
            Command lastCommand = commandHistory.pop();
            System.out.println("Undoing: " + lastCommand.getDescription());
            lastCommand.undo();
        } else {
            System.out.println("Nothing to undo");
        }
    }
    
    public void createScene(String sceneName, Command... commands) {
        MacroCommand scene = new MacroCommand(commands);
        setCommand(sceneName, scene);
        System.out.println("Scene created: " + sceneName);
    }
    
    public void showCommands() {
        System.out.println("\n--- Available Commands ---");
        for (String buttonName : commands.keySet()) {
            System.out.println(buttonName + ": " + commands.get(buttonName).getDescription());
        }
        System.out.println("-------------------------\n");
    }
}

// Usage
public class SmartHomeDemo {
    public static void main(String[] args) {
        // Create devices
        Light livingRoomLight = new Light();
        Light bedroomLight = new Light();
        Fan ceilingFan = new Fan();
        AirConditioner ac = new AirConditioner();
        TV tv = new TV();
        
        // Create commands
        Command livingRoomLightOn = new LightOnCommand(livingRoomLight);
        Command livingRoomLightOff = new LightOffCommand(livingRoomLight);
        Command bedroomLightOn = new LightOnCommand(bedroomLight);
        Command bedroomLightOff = new LightOffCommand(bedroomLight);
        Command fanHigh = new FanHighCommand(ceilingFan);
        Command fanOff = new FanOffCommand(ceilingFan);
        Command acOn = new AirConditionerOnCommand(ac);
        Command acOff = new AirConditionerOffCommand(ac);
        Command tvOn = new TVOnCommand(tv);
        Command tvOff = new TVOffCommand(tv);
        
        // Setup remote control
        SmartRemoteControl remote = new SmartRemoteControl();
        
        // Assign individual commands
        remote.setCommand("Living Room Light ON", livingRoomLightOn);
        remote.setCommand("Living Room Light OFF", livingRoomLightOff);
        remote.setCommand("Bedroom Light ON", bedroomLightOn);
        remote.setCommand("Fan High", fanHigh);
        remote.setCommand("AC ON", acOn);
        remote.setCommand("TV ON", tvOn);
        
        // Create scenes (macro commands)
        remote.createScene("Movie Night", 
            livingRoomLightOff, 
            tvOn, 
            acOn
        );
        
        remote.createScene("Sleep Mode", 
            livingRoomLightOff, 
            bedroomLightOff, 
            fanOff, 
            tvOff, 
            acOn
        );
        
        remote.createScene("Party Mode", 
            livingRoomLightOn, 
            bedroomLightOn, 
            fanHigh, 
            tvOn
        );
        
        // Show available commands
        remote.showCommands();
        
        // Test individual commands
        System.out.println("=== Testing Individual Commands ===");
        remote.pressButton("Living Room Light ON");
        remote.pressButton("Fan High");
        remote.pressButton("AC ON");
        
        // Test undo
        System.out.println("\n=== Testing Undo ===");
        remote.undo(); // Undo AC ON
        remote.undo(); // Undo Fan High
        
        // Test scenes
        System.out.println("\n=== Testing Scenes ===");
        remote.pressButton("Movie Night");
        
        System.out.println("\n--- After Movie Night ---");
        
        remote.pressButton("Sleep Mode");
        
        System.out.println("\n--- After Sleep Mode ---");
        
        // Test undo scene
        System.out.println("\n=== Undoing Scene ===");
        remote.undo(); // Undo Sleep Mode scene
        
        System.out.println("\nSmart Home Demo completed!");
        System.out.println("Benefits demonstrated:");
        System.out.println("- Decoupled remote control from devices");
        System.out.println("- Undo functionality for all operations");
        System.out.println("- Macro commands for complex scenes");
        System.out.println("- Easy to add new devices and commands");
    }
}

// Additional command classes (simplified)
class FanOffCommand implements Command {
    private Fan fan;
    private boolean previousState;
    private int previousSpeed;
    
    public FanOffCommand(Fan fan) { this.fan = fan; }
    
    @Override
    public void execute() {
        previousState = fan.isOn();
        previousSpeed = fan.getSpeed();
        fan.turnOff();
    }
    
    @Override
    public void undo() {
        if (previousState) {
            fan.turnOn();
            fan.setSpeed(previousSpeed);
        }
    }
    
    @Override
    public String getDescription() { return "Turn Fan OFF"; }
}

class AirConditionerOffCommand implements Command {
    private AirConditioner ac;
    private boolean previousState;
    private int previousTemp;
    
    public AirConditionerOffCommand(AirConditioner ac) { this.ac = ac; }
    
    @Override
    public void execute() {
        previousState = ac.isOn();
        previousTemp = ac.getTemperature();
        ac.turnOff();
    }
    
    @Override
    public void undo() {
        if (previousState) {
            ac.turnOn();
            ac.setTemperature(previousTemp);
        }
    }
    
    @Override
    public String getDescription() { return "Turn AC OFF"; }
}

class TVOffCommand implements Command {
    private TV tv;
    private boolean previousState;
    private int previousChannel, previousVolume;
    
    public TVOffCommand(TV tv) { this.tv = tv; }
    
    @Override
    public void execute() {
        previousState = tv.isOn();
        if (tv.isOn()) {
            previousChannel = tv.getChannel();
            previousVolume = tv.getVolume();
        }
        tv.turnOff();
    }
    
    @Override
    public void undo() {
        if (previousState) {
            tv.turnOn();
            tv.setChannel(previousChannel);
            tv.setVolume(previousVolume);
        }
    }
    
    @Override
    public String getDescription() { return "Turn TV OFF"; }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Command Pattern** encapsulates requests as objects
2. **Decouples** invoker from receiver
3. **Supports** undo/redo, queuing, logging, macros
4. **Enables** parameterization of objects with operations

### So sánh với patterns khác
| Pattern | Purpose | Use Case |
|---------|---------|----------|
| **Command** | Encapsulate requests | Undo/redo, queuing, logging |
| **Strategy** | Encapsulate algorithms | Algorithm selection |
| **Observer** | Notify multiple objects | Event handling |
| **Mediator** | Encapsulate object interactions | Complex communications |

### Best Practices
- **Keep commands simple** và focused
- **Implement undo carefully** - save necessary state
- **Use Null Object** pattern cho empty commands
- **Consider memory usage** cho command history
- **Group related commands** into macros when appropriate

---

**Tiếp theo:** [State](state.md) - Thay đổi hành vi dựa trên trạng thái nội bộ
