# Builder Pattern

> **Creational Pattern** - Xây dựng các đối tượng phức tạp từng bước một cách có kiểm soát

## 📋 <PERSON>ục lục

1. [Tổng quan](#tổng-quan)
2. [V<PERSON>n đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON>ợ<PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [V<PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Builder là một **creational design pattern** cho phép **xây dựng các đối tượng phức tạp từng bước**. Pat<PERSON> này cho phép tạo ra các cấu hình khác nhau của một đối tượng mà không gây nhầm lẫn từ nhiều tham số hoặc lớp con.

### Mục đích chính
- **Tách biệt** quá trình xây dựng đối tượng khỏi biểu diễn của nó
- **Kiểm soát** quá trình xây dựng từng bước
- **Tạo ra** các biểu diễn khác nhau của cùng một đối tượng

### Tên gọi khác
- **Step Builder Pattern**
- **Fluent Builder Pattern**

---

## Vấn đề

### Tình huống phức tạp
Giả sử bạn cần tạo đối tượng `House` với nhiều thành phần:

```java
// Constructor nightmare - quá nhiều tham số
public House(int windows, int doors, int rooms, boolean hasGarage, 
             boolean hasSwimmingPool, boolean hasGarden, String roofType,
             String wallMaterial, String floorMaterial, boolean hasBasement) {
    // Constructor hell!
}
```

### Vấn đề phát sinh

#### 1. Telescoping Constructor Problem
```java
public class House {
    // Nhiều constructors với số lượng tham số khác nhau
    public House(int rooms) { ... }
    public House(int rooms, int windows) { ... }
    public House(int rooms, int windows, int doors) { ... }
    public House(int rooms, int windows, int doors, boolean hasGarage) { ... }
    // ... và còn nhiều nữa!
}
```

#### 2. Khó đọc và dễ nhầm lẫn
```java
// Không rõ ý nghĩa của từng tham số
House house = new House(4, 8, 2, true, false, true, "Tile", "Brick", "Wood", false);
```

#### 3. Không linh hoạt
- **Không thể tạo** đối tượng từng bước
- **Khó validate** dữ liệu trong quá trình xây dựng
- **Không thể tái sử dụng** logic xây dựng

---

## Giải pháp

### Ý tưởng cốt lõi
Builder pattern đề xuất **trích xuất mã xây dựng đối tượng** ra khỏi lớp của chính nó và chuyển nó sang các **đối tượng riêng biệt gọi là builders**.

### Cách hoạt động

#### 1. Tạo Builder interface
```java
public interface HouseBuilder {
    HouseBuilder setRooms(int rooms);
    HouseBuilder setWindows(int windows);
    HouseBuilder setDoors(int doors);
    HouseBuilder setGarage(boolean hasGarage);
    HouseBuilder setSwimmingPool(boolean hasPool);
    House build();
}
```

#### 2. Implement Concrete Builder
```java
public class ConcreteHouseBuilder implements HouseBuilder {
    private House house;
    
    public ConcreteHouseBuilder() {
        this.house = new House();
    }
    
    @Override
    public HouseBuilder setRooms(int rooms) {
        house.setRooms(rooms);
        return this; // Fluent interface
    }
    
    @Override
    public HouseBuilder setWindows(int windows) {
        house.setWindows(windows);
        return this;
    }
    
    // ... other methods
    
    @Override
    public House build() {
        return house;
    }
}
```

#### 3. Sử dụng Builder
```java
// Fluent interface - dễ đọc và hiểu
House house = new ConcreteHouseBuilder()
    .setRooms(4)
    .setWindows(8)
    .setDoors(2)
    .setGarage(true)
    .setSwimmingPool(false)
    .build();
```

### Director (Optional)
```java
public class HouseDirector {
    public House buildSimpleHouse(HouseBuilder builder) {
        return builder
            .setRooms(2)
            .setWindows(4)
            .setDoors(1)
            .build();
    }
    
    public House buildLuxuryHouse(HouseBuilder builder) {
        return builder
            .setRooms(6)
            .setWindows(12)
            .setDoors(3)
            .setGarage(true)
            .setSwimmingPool(true)
            .build();
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Director
├── + construct(Builder): Product
│
Builder (interface)
├── + buildPartA(): Builder
├── + buildPartB(): Builder
├── + build(): Product
│
ConcreteBuilder
├── + buildPartA(): Builder
├── + buildPartB(): Builder
├── + build(): Product
├── - product: Product
│
Product
├── + partA: String
├── + partB: String
```

### Các thành phần chính

#### 1. Builder (Interface)
- **Vai trò:** Khai báo các bước xây dựng chung cho tất cả builders
- **Đặc điểm:** Thường return `this` để hỗ trợ fluent interface

#### 2. Concrete Builder
- **Vai trò:** Triển khai các bước xây dựng cụ thể
- **Đặc điểm:** Có thể tạo ra các sản phẩm khác nhau

#### 3. Product
- **Vai trò:** Đối tượng kết quả được xây dựng
- **Đặc điểm:** Có thể có cấu trúc phức tạp

#### 4. Director (Optional)
- **Vai trò:** Định nghĩa thứ tự các bước xây dựng
- **Đặc điểm:** Tái sử dụng logic xây dựng

#### 5. Client
- **Vai trò:** Tạo builder và director, khởi tạo quá trình xây dựng

---

## Cách triển khai

### Bước 1: Định nghĩa Product
```java
public class Computer {
    private String CPU;
    private String RAM;
    private String storage;
    private String GPU;
    private boolean hasWiFi;
    
    // Getters and setters
    public void setCPU(String CPU) { this.CPU = CPU; }
    public void setRAM(String RAM) { this.RAM = RAM; }
    // ... other setters
    
    @Override
    public String toString() {
        return String.format("Computer[CPU=%s, RAM=%s, Storage=%s, GPU=%s, WiFi=%s]",
                CPU, RAM, storage, GPU, hasWiFi);
    }
}
```

### Bước 2: Tạo Builder interface
```java
public interface ComputerBuilder {
    ComputerBuilder setCPU(String cpu);
    ComputerBuilder setRAM(String ram);
    ComputerBuilder setStorage(String storage);
    ComputerBuilder setGPU(String gpu);
    ComputerBuilder setWiFi(boolean hasWiFi);
    Computer build();
}
```

### Bước 3: Implement Concrete Builder
```java
public class GamingComputerBuilder implements ComputerBuilder {
    private Computer computer;
    
    public GamingComputerBuilder() {
        this.computer = new Computer();
    }
    
    @Override
    public ComputerBuilder setCPU(String cpu) {
        computer.setCPU(cpu);
        return this;
    }
    
    @Override
    public ComputerBuilder setRAM(String ram) {
        computer.setRAM(ram);
        return this;
    }
    
    @Override
    public ComputerBuilder setStorage(String storage) {
        computer.setStorage(storage);
        return this;
    }
    
    @Override
    public ComputerBuilder setGPU(String gpu) {
        computer.setGPU(gpu);
        return this;
    }
    
    @Override
    public ComputerBuilder setWiFi(boolean hasWiFi) {
        computer.setWiFi(hasWiFi);
        return this;
    }
    
    @Override
    public Computer build() {
        return computer;
    }
}
```

### Bước 4: Tạo Director (Optional)
```java
public class ComputerDirector {
    public Computer buildGamingComputer(ComputerBuilder builder) {
        return builder
            .setCPU("Intel i9-12900K")
            .setRAM("32GB DDR4")
            .setStorage("1TB NVMe SSD")
            .setGPU("RTX 4080")
            .setWiFi(true)
            .build();
    }
    
    public Computer buildOfficeComputer(ComputerBuilder builder) {
        return builder
            .setCPU("Intel i5-12400")
            .setRAM("16GB DDR4")
            .setStorage("512GB SSD")
            .setWiFi(true)
            .build(); // No GPU for office computer
    }
}
```

### Bước 5: Sử dụng
```java
public class BuilderDemo {
    public static void main(String[] args) {
        // Direct building
        Computer customComputer = new GamingComputerBuilder()
            .setCPU("AMD Ryzen 9")
            .setRAM("64GB DDR4")
            .setStorage("2TB SSD")
            .setGPU("RTX 4090")
            .setWiFi(true)
            .build();
        
        // Using Director
        ComputerDirector director = new ComputerDirector();
        ComputerBuilder builder = new GamingComputerBuilder();
        
        Computer gamingPC = director.buildGamingComputer(builder);
        Computer officePC = director.buildOfficeComputer(new GamingComputerBuilder());
        
        System.out.println("Custom: " + customComputer);
        System.out.println("Gaming: " + gamingPC);
        System.out.println("Office: " + officePC);
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Xây dựng từng bước
- **Kiểm soát** quá trình xây dựng
- **Validation** dữ liệu trong từng bước
- **Dễ debug** khi có lỗi

#### 2. Fluent Interface
- **Code dễ đọc** và tự documenting
- **IDE support** tốt với autocomplete
- **Method chaining** tự nhiên

#### 3. Tái sử dụng code
- **Director** có thể tái sử dụng builders
- **Builders** có thể tạo nhiều products
- **Flexible construction** process

#### 4. Tuân thủ Single Responsibility Principle
- **Tách biệt** construction logic khỏi business logic
- **Mỗi builder** chỉ chịu trách nhiệm một loại product

### ❌ Nhược điểm

#### 1. Tăng độ phức tạp
- **Nhiều classes** cần tạo
- **Over-engineering** cho objects đơn giản

#### 2. Memory overhead
- **Builder object** cần memory
- **Intermediate state** được lưu trữ

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Constructor có quá nhiều tham số
```java
// Thay vì này
public Pizza(String dough, String sauce, String cheese, 
             boolean pepperoni, boolean mushrooms, boolean olives,
             boolean sausage, boolean bacon, boolean ham) { ... }

// Dùng Builder
Pizza pizza = new PizzaBuilder()
    .setDough("Thin")
    .setSauce("Tomato")
    .setCheese("Mozzarella")
    .addPepperoni()
    .addMushrooms()
    .build();
```

#### 2. Cần tạo nhiều biểu diễn của cùng một object
```java
// Different representations
SQLQueryBuilder sqlBuilder = new SQLQueryBuilder();
String selectQuery = sqlBuilder.select("name", "age").from("users").build();
String insertQuery = sqlBuilder.insertInto("users").values("John", 25).build();
```

#### 3. Xây dựng Composite trees
```java
// Building complex tree structures
HTMLBuilder html = new HTMLBuilder()
    .addElement("div")
        .addElement("h1").setText("Title").end()
        .addElement("p").setText("Content").end()
    .end()
    .build();
```

### ❌ Không nên sử dụng khi:

#### 1. Object đơn giản
```java
// Không cần Builder cho object đơn giản
public class Point {
    private int x, y;
    
    public Point(int x, int y) { // Simple constructor is enough
        this.x = x;
        this.y = y;
    }
}
```

#### 2. Immutable objects với ít fields
```java
// Record class (Java 14+) đã đủ
public record Person(String name, int age) {}
```

---

## Ví dụ thực tế

### Ví dụ 1: SQL Query Builder

```java
public class SQLQueryBuilder {
    private StringBuilder query;
    
    public SQLQueryBuilder() {
        this.query = new StringBuilder();
    }
    
    public SQLQueryBuilder select(String... columns) {
        query.append("SELECT ").append(String.join(", ", columns));
        return this;
    }
    
    public SQLQueryBuilder from(String table) {
        query.append(" FROM ").append(table);
        return this;
    }
    
    public SQLQueryBuilder where(String condition) {
        query.append(" WHERE ").append(condition);
        return this;
    }
    
    public SQLQueryBuilder orderBy(String column) {
        query.append(" ORDER BY ").append(column);
        return this;
    }
    
    public String build() {
        return query.toString();
    }
}

// Usage
String sql = new SQLQueryBuilder()
    .select("name", "email", "age")
    .from("users")
    .where("age > 18")
    .orderBy("name")
    .build();
// Result: "SELECT name, email, age FROM users WHERE age > 18 ORDER BY name"
```

### Ví dụ 2: HTTP Request Builder

```java
public class HttpRequestBuilder {
    private String method = "GET";
    private String url;
    private Map<String, String> headers = new HashMap<>();
    private String body;
    
    public HttpRequestBuilder setMethod(String method) {
        this.method = method;
        return this;
    }
    
    public HttpRequestBuilder setUrl(String url) {
        this.url = url;
        return this;
    }
    
    public HttpRequestBuilder addHeader(String key, String value) {
        headers.put(key, value);
        return this;
    }
    
    public HttpRequestBuilder setBody(String body) {
        this.body = body;
        return this;
    }
    
    public HttpRequest build() {
        return new HttpRequest(method, url, headers, body);
    }
}

// Usage
HttpRequest request = new HttpRequestBuilder()
    .setMethod("POST")
    .setUrl("https://api.example.com/users")
    .addHeader("Content-Type", "application/json")
    .addHeader("Authorization", "Bearer token123")
    .setBody("{\"name\":\"John\",\"age\":30}")
    .build();
```

---

## 🎯 Tóm tắt

### Key Points
1. **Builder Pattern** giải quyết vấn đề constructor phức tạp
2. **Fluent interface** làm code dễ đọc và maintainable
3. **Director** có thể tái sử dụng building logic
4. **Phù hợp** với objects phức tạp có nhiều optional parameters

### So sánh với patterns khác
| Pattern | Mục đích | Khi dùng |
|---------|----------|----------|
| **Builder** | Xây dựng từng bước | Object phức tạp, nhiều tham số |
| **Factory Method** | Tạo object qua interface | Không biết concrete type |
| **Abstract Factory** | Tạo họ objects | Cần đảm bảo compatibility |

### Mối quan hệ với patterns khác
- **Có thể phát triển từ Factory Method** khi cần more control
- **Có thể làm việc với Composite** để build trees
- **Director có thể sử dụng Strategy** cho different building algorithms

---

**Tiếp theo:** [Prototype](prototype.md) - Sao chép đối tượng hiện có
