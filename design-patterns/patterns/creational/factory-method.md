# Factory Method Pattern

> **Creational Pattern** - Cung cấp giao diện để tạo đối tượng trong lớp cha, cho phép lớp con quyết định loại đối tượng cụ thể

## 📋 Mục lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#giải-pháp)
4. [C<PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nhược-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [V<PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Đ<PERSON><PERSON> nghĩa
Factory Method là một **creational design pattern** cung cấp một **giao diện để tạo đối tượng trong một lớp cha** trong khi cho phép các lớp con quyết định loại đối tượng chính xác sẽ được tạo.

### Mục đích chính
- **Tách biệt** mã tạo sản phẩm khỏi mã sử dụng sản phẩm
- **Giảm kết nối chặt chẽ** giữa lớp tạo và các sản phẩm cụ thể
- **Cho phép mở rộng** dễ dàng khi thêm loại sản phẩm mới

### Tên gọi khác
- **Virtual Constructor**
- **Factory Pattern** (phiên bản đơn giản)

---

## Vấn đề

### Tình huống ban đầu
Giả sử bạn đang phát triển một ứng dụng quản lý logistics. Phiên bản đầu tiên chỉ xử lý vận chuyển bằng **đường bộ (Truck)**, nên hầu hết mã của bạn nằm trong lớp `Truck`.

### Yêu cầu mở rộng
Sau một thời gian, ứng dụng trở nên phổ biến và bạn nhận được yêu cầu thêm **vận chuyển đường biển (Ship)**.

### Vấn đề phát sinh

```java
// Mã hiện tại - kết nối chặt chẽ với Truck
public class LogisticsManager {
    public void planDelivery() {
        Truck truck = new Truck(); // Hardcoded!
        truck.deliver();
    }
}
```

**Thách thức:**
1. **Mã kết nối chặt chẽ** với lớp `Truck` cụ thể
2. **Thêm Ship** yêu cầu thay đổi toàn bộ codebase
3. **Trong tương lai** thêm `Plane`, `Drone` sẽ càng phức tạp hơn
4. **Mã sẽ đầy các điều kiện** `if-else` hoặc `switch-case`

### Hậu quả
- Mã trở nên **khó bảo trì**
- **Vi phạm Open/Closed Principle**
- **Tăng nguy cơ lỗi** khi thay đổi
- **Khó kiểm thử** do phụ thuộc cứng

---

## Giải pháp

### Ý tưởng cốt lõi
Factory Method đề xuất **thay thế các lệnh gọi constructor trực tiếp** bằng các lệnh gọi đến một **phương thức factory đặc biệt**.

### Cách hoạt động

#### 1. Tạo giao diện chung
```java
// Giao diện chung cho tất cả sản phẩm
public interface Transport {
    void deliver();
}
```

#### 2. Triển khai các sản phẩm cụ thể
```java
public class Truck implements Transport {
    @Override
    public void deliver() {
        System.out.println("Deliver by land in a box");
    }
}

public class Ship implements Transport {
    @Override
    public void deliver() {
        System.out.println("Deliver by sea in a container");
    }
}
```

#### 3. Tạo lớp Creator với factory method
```java
public abstract class Logistics {
    // Factory method - để lớp con quyết định
    public abstract Transport createTransport();
    
    // Logic nghiệp vụ sử dụng factory method
    public void planDelivery() {
        Transport transport = createTransport();
        transport.deliver();
    }
}
```

#### 4. Triển khai các Creator cụ thể
```java
public class RoadLogistics extends Logistics {
    @Override
    public Transport createTransport() {
        return new Truck();
    }
}

public class SeaLogistics extends Logistics {
    @Override
    public Transport createTransport() {
        return new Ship();
    }
}
```

### Lợi ích của giải pháp
- **Mã client** không cần biết lớp cụ thể nào được tạo
- **Dễ dàng thêm** loại transport mới
- **Tuân thủ SOLID principles**
- **Tách biệt** logic tạo đối tượng khỏi logic sử dụng

---

## Cấu trúc

### Sơ đồ UML
```
Creator (abstract)
├── + factoryMethod(): Product
├── + someOperation(): void
│
├── ConcreteCreatorA
│   └── + factoryMethod(): ConcreteProductA
│
└── ConcreteCreatorB
    └── + factoryMethod(): ConcreteProductB

Product (interface)
├── ConcreteProductA
└── ConcreteProductB
```

### Các thành phần chính

#### 1. Product (Sản phẩm)
- **Vai trò:** Khai báo giao diện chung cho tất cả sản phẩm
- **Ví dụ:** `Transport` interface

#### 2. Concrete Products (Sản phẩm cụ thể)
- **Vai trò:** Các triển khai khác nhau của giao diện Product
- **Ví dụ:** `Truck`, `Ship`, `Plane`

#### 3. Creator (Người tạo)
- **Vai trò:** Khai báo factory method trả về đối tượng Product
- **Đặc điểm:** Thường chứa logic nghiệp vụ cốt lõi
- **Ví dụ:** `Logistics` abstract class

#### 4. Concrete Creators (Người tạo cụ thể)
- **Vai trò:** Ghi đè factory method để tạo loại sản phẩm cụ thể
- **Ví dụ:** `RoadLogistics`, `SeaLogistics`

---

## Cách triển khai

### Bước 1: Xác định giao diện chung
```java
public interface Product {
    void operation();
}
```

### Bước 2: Tạo các sản phẩm cụ thể
```java
public class ConcreteProductA implements Product {
    @Override
    public void operation() {
        System.out.println("Operation A");
    }
}

public class ConcreteProductB implements Product {
    @Override
    public void operation() {
        System.out.println("Operation B");
    }
}
```

### Bước 3: Tạo Creator abstract
```java
public abstract class Creator {
    // Factory method
    public abstract Product factoryMethod();
    
    // Template method sử dụng factory method
    public void someOperation() {
        Product product = factoryMethod();
        product.operation();
    }
}
```

### Bước 4: Triển khai Concrete Creators
```java
public class ConcreteCreatorA extends Creator {
    @Override
    public Product factoryMethod() {
        return new ConcreteProductA();
    }
}

public class ConcreteCreatorB extends Creator {
    @Override
    public Product factoryMethod() {
        return new ConcreteProductB();
    }
}
```

### Bước 5: Sử dụng
```java
public class Client {
    public static void main(String[] args) {
        Creator creator = new ConcreteCreatorA();
        creator.someOperation(); // Output: Operation A
        
        creator = new ConcreteCreatorB();
        creator.someOperation(); // Output: Operation B
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Tránh kết nối chặt chẽ
- Creator và sản phẩm cụ thể **không phụ thuộc trực tiếp**
- Dễ dàng thay đổi implementation

#### 2. Tuân thủ Single Responsibility Principle
- **Tách biệt** mã tạo sản phẩm vào một nơi cụ thể
- Dễ bảo trì và debug

#### 3. Tuân thủ Open/Closed Principle
- **Thêm loại sản phẩm mới** mà không thay đổi mã hiện có
- Chỉ cần tạo Creator mới

#### 4. Tăng tính linh hoạt
- Có thể **thay đổi loại sản phẩm** trong runtime
- Dễ dàng mở rộng hệ thống

### ❌ Nhược điểm

#### 1. Tăng độ phức tạp
- **Cần nhiều lớp hơn** để triển khai pattern
- Có thể **over-engineering** cho các trường hợp đơn giản

#### 2. Cần hiểu về kế thừa
- Yêu cầu **kiến thức về abstract classes** và inheritance
- Có thể khó hiểu cho người mới

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Không biết trước kiểu đối tượng cụ thể
```java
// Không biết trước sẽ cần Truck hay Ship
public void processOrder(String transportType) {
    // Factory method sẽ quyết định
}
```

#### 2. Muốn cho phép mở rộng
- Framework hoặc library cần **cho phép user mở rộng**
- Cần **plugin architecture**

#### 3. Tiết kiệm tài nguyên hệ thống
- **Tái sử dụng đối tượng** thay vì tạo mới
- **Object pooling**

#### 4. Cần kiểm soát quá trình tạo đối tượng
- **Validation** trước khi tạo
- **Logging** quá trình tạo đối tượng
- **Caching** kết quả

### ❌ Không nên sử dụng khi:

#### 1. Chỉ có một loại sản phẩm
- **Over-engineering** không cần thiết
- Simple constructor đã đủ

#### 2. Sản phẩm không có giao diện chung
- Không thể tạo **abstraction** hợp lý
- Các sản phẩm quá khác biệt

---

## Ví dụ thực tế

### Ví dụ 1: UI Component Factory

```java
// Product interface
public interface Button {
    void render();
    void onClick();
}

// Concrete Products
public class WindowsButton implements Button {
    @Override
    public void render() {
        System.out.println("Render Windows button");
    }
    
    @Override
    public void onClick() {
        System.out.println("Windows button clicked");
    }
}

public class MacButton implements Button {
    @Override
    public void render() {
        System.out.println("Render Mac button");
    }
    
    @Override
    public void onClick() {
        System.out.println("Mac button clicked");
    }
}

// Creator
public abstract class Dialog {
    public abstract Button createButton();
    
    public void renderWindow() {
        Button button = createButton();
        button.render();
    }
}

// Concrete Creators
public class WindowsDialog extends Dialog {
    @Override
    public Button createButton() {
        return new WindowsButton();
    }
}

public class MacDialog extends Dialog {
    @Override
    public Button createButton() {
        return new MacButton();
    }
}
```

### Ví dụ 2: Document Factory

```java
public interface Document {
    void open();
    void save();
}

public class WordDocument implements Document {
    @Override
    public void open() {
        System.out.println("Opening Word document");
    }
    
    @Override
    public void save() {
        System.out.println("Saving Word document");
    }
}

public class PDFDocument implements Document {
    @Override
    public void open() {
        System.out.println("Opening PDF document");
    }
    
    @Override
    public void save() {
        System.out.println("Saving PDF document");
    }
}

public abstract class Application {
    public abstract Document createDocument();
    
    public void newDocument() {
        Document doc = createDocument();
        doc.open();
    }
}

public class WordApplication extends Application {
    @Override
    public Document createDocument() {
        return new WordDocument();
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Factory Method** tách biệt việc tạo đối tượng khỏi việc sử dụng
2. **Lớp con quyết định** loại đối tượng cụ thể được tạo
3. **Tuân thủ SOLID principles** và tăng tính linh hoạt
4. **Phù hợp** khi cần mở rộng hoặc không biết trước kiểu đối tượng

### Mối quan hệ với patterns khác
- **Abstract Factory** thường phát triển từ Factory Method
- **Template Method** có thể sử dụng Factory Method
- **Prototype** có thể thay thế Factory Method trong một số trường hợp

---

**Tiếp theo:** [Abstract Factory](abstract-factory.md) - Tạo họ đối tượng liên quan
