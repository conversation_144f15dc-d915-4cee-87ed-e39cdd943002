# Singleton Pattern

> **Creational Pattern** - <PERSON><PERSON><PERSON> bảo một lớp chỉ có một instance duy nhất và cung cấp điểm truy cập toàn cầu

## 📋 <PERSON>ụ<PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [Gi<PERSON><PERSON> pháp](#giải-pháp)
4. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
5. [Thread Safety](#thread-safety)
6. [Ưu nhược điểm](#ưu-nhược-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [<PERSON><PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Singleton là một **creational design pattern** đảm bảo **một lớp chỉ có một instance duy nhất** trong khi cung cấp một **điểm truy cập toàn cầu** đến instance đó.

### <PERSON><PERSON><PERSON> đích chính
- **<PERSON><PERSON><PERSON> soát instance duy nhất** của một lớp
- **Cung cấp global access point** đến instance đó
- **Lazy initialization** - tạo instance khi cần thiết

### ⚠️ Lưu ý quan trọng
Singleton **vi phạm Single Responsibility Principle** vì nó giải quyết hai vấn đề cùng lúc:
1. Đảm bảo chỉ có một instance
2. Cung cấp global access

---

## Vấn đề

### Tình huống thực tế

#### 1. Kiểm soát tài nguyên dùng chung
```java
// Vấn đề: Nhiều database connections
DatabaseConnection conn1 = new DatabaseConnection();
DatabaseConnection conn2 = new DatabaseConnection(); // Lãng phí!
DatabaseConnection conn3 = new DatabaseConnection(); // Không cần thiết!
```

#### 2. Global access nhưng an toàn
```java
// Vấn đề: Global variables không an toàn
public class Config {
    public static String DATABASE_URL = "localhost"; // Có thể bị thay đổi bất cứ đâu!
    public static int MAX_CONNECTIONS = 10;
}

// Ai cũng có thể thay đổi
Config.DATABASE_URL = "malicious-server"; // Nguy hiểm!
```

#### 3. Expensive object creation
```java
// Vấn đề: Tạo logger mới mỗi lần
public class Service {
    public void doSomething() {
        Logger logger = new Logger(); // Expensive creation
        logger.log("Service started");
        // ...
    }
}
```

### Vấn đề với giải pháp thông thường

#### Constructor public - không kiểm soát được
```java
public class DatabaseConnection {
    public DatabaseConnection() {
        // Ai cũng có thể tạo instance mới!
    }
}
```

#### Static variables - không an toàn
```java
public class GlobalConfig {
    public static GlobalConfig instance = new GlobalConfig(); // Eager initialization
    // Không lazy, tạo ngay cả khi không dùng
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Singleton pattern giải quyết bằng cách:
1. **Private constructor** - ngăn tạo instance từ bên ngoài
2. **Static method** - cung cấp controlled access
3. **Static field** - lưu trữ instance duy nhất

### Cấu trúc cơ bản
```java
public class Singleton {
    // Static field để lưu instance duy nhất
    private static Singleton instance;
    
    // Private constructor ngăn instantiation từ bên ngoài
    private Singleton() {
        // Initialization code
    }
    
    // Public static method để access instance
    public static Singleton getInstance() {
        if (instance == null) {
            instance = new Singleton();
        }
        return instance;
    }
}
```

---

## Cách triển khai

### 1. Eager Initialization (Thread-Safe)
```java
public class EagerSingleton {
    // Instance được tạo ngay khi class load
    private static final EagerSingleton INSTANCE = new EagerSingleton();
    
    private EagerSingleton() {
        // Private constructor
    }
    
    public static EagerSingleton getInstance() {
        return INSTANCE;
    }
}
```

**Ưu điểm:**
- Thread-safe by default
- Simple implementation

**Nhược điểm:**
- Không lazy - tạo ngay cả khi không dùng
- Không handle exceptions trong constructor

### 2. Lazy Initialization (Not Thread-Safe)
```java
public class LazySingleton {
    private static LazySingleton instance;
    
    private LazySingleton() {}
    
    public static LazySingleton getInstance() {
        if (instance == null) {
            instance = new LazySingleton(); // Có thể tạo multiple instances trong multi-thread
        }
        return instance;
    }
}
```

**Ưu điểm:**
- Lazy initialization
- Simple code

**Nhược điểm:**
- **Không thread-safe!**
- Race condition có thể tạo multiple instances

### 3. Thread-Safe Lazy Initialization
```java
public class ThreadSafeSingleton {
    private static ThreadSafeSingleton instance;
    
    private ThreadSafeSingleton() {}
    
    // Synchronized method - thread safe nhưng chậm
    public static synchronized ThreadSafeSingleton getInstance() {
        if (instance == null) {
            instance = new ThreadSafeSingleton();
        }
        return instance;
    }
}
```

**Ưu điểm:**
- Thread-safe
- Lazy initialization

**Nhược điểm:**
- **Performance overhead** - synchronized mỗi lần call
- Chỉ cần synchronize lần đầu tiên

### 4. Double-Checked Locking (Recommended)
```java
public class DoubleCheckedSingleton {
    // volatile để đảm bảo visibility across threads
    private static volatile DoubleCheckedSingleton instance;
    
    private DoubleCheckedSingleton() {}
    
    public static DoubleCheckedSingleton getInstance() {
        // First check - không synchronized
        if (instance == null) {
            synchronized (DoubleCheckedSingleton.class) {
                // Second check - trong synchronized block
                if (instance == null) {
                    instance = new DoubleCheckedSingleton();
                }
            }
        }
        return instance;
    }
}
```

**Ưu điểm:**
- Thread-safe
- Lazy initialization
- Good performance - chỉ synchronize lần đầu

**Nhược điểm:**
- Complex code
- Cần `volatile` keyword

### 5. Bill Pugh Solution (Best Practice)
```java
public class BillPughSingleton {
    
    private BillPughSingleton() {}
    
    // Static inner class - chỉ load khi cần
    private static class SingletonHelper {
        private static final BillPughSingleton INSTANCE = new BillPughSingleton();
    }
    
    public static BillPughSingleton getInstance() {
        return SingletonHelper.INSTANCE;
    }
}
```

**Ưu điểm:**
- Thread-safe (JVM guarantees)
- Lazy initialization
- No synchronization overhead
- Simple and clean

**Nhược điểm:**
- Sử dụng inner class (có thể confusing)

### 6. Enum Singleton (Joshua Bloch's Approach)
```java
public enum EnumSingleton {
    INSTANCE;
    
    public void doSomething() {
        System.out.println("Doing something...");
    }
}

// Usage
EnumSingleton.INSTANCE.doSomething();
```

**Ưu điểm:**
- Thread-safe
- Serialization safe
- Reflection proof
- Very concise

**Nhược điểm:**
- Không lazy initialization
- Không thể extend class khác

---

## Thread Safety

### Race Condition Problem
```java
// Thread 1                    Thread 2
if (instance == null) {        if (instance == null) {     // Cả hai thấy null
    instance = new Singleton();   instance = new Singleton(); // Tạo 2 instances!
}                              }
```

### Solutions Comparison

| Approach | Thread Safe | Lazy | Performance | Complexity |
|----------|-------------|------|-------------|------------|
| Eager | ✅ | ❌ | ⭐⭐⭐ | ⭐ |
| Lazy | ❌ | ✅ | ⭐⭐⭐ | ⭐ |
| Synchronized | ✅ | ✅ | ⭐ | ⭐⭐ |
| Double-Checked | ✅ | ✅ | ⭐⭐⭐ | ⭐⭐⭐ |
| Bill Pugh | ✅ | ✅ | ⭐⭐⭐ | ⭐⭐ |
| Enum | ✅ | ❌ | ⭐⭐⭐ | ⭐ |

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Đảm bảo single instance
```java
DatabaseConnection conn1 = DatabaseConnection.getInstance();
DatabaseConnection conn2 = DatabaseConnection.getInstance();
System.out.println(conn1 == conn2); // true - same instance
```

#### 2. Global access point
```java
// Có thể access từ bất cứ đâu
public class ServiceA {
    public void method() {
        Logger.getInstance().log("ServiceA called");
    }
}

public class ServiceB {
    public void method() {
        Logger.getInstance().log("ServiceB called"); // Same logger instance
    }
}
```

#### 3. Lazy initialization
```java
// Chỉ tạo khi thực sự cần
public class ExpensiveResource {
    private ExpensiveResource() {
        // Expensive initialization chỉ chạy khi cần
        loadLargeDataset();
        connectToExternalService();
    }
}
```

#### 4. Memory efficiency
- Chỉ một instance trong memory
- Tiết kiệm resources cho expensive objects

### ❌ Nhược điểm

#### 1. Vi phạm Single Responsibility Principle
```java
public class DatabaseManager {
    // Responsibility 1: Manage database operations
    public void executeQuery(String sql) { ... }
    
    // Responsibility 2: Control instance creation
    public static DatabaseManager getInstance() { ... }
}
```

#### 2. Khó testing
```java
public class UserService {
    public void createUser(String name) {
        // Hard to mock DatabaseManager.getInstance()
        DatabaseManager.getInstance().save(new User(name));
    }
}

// Test khó khăn vì không thể inject mock
```

#### 3. Hidden dependencies
```java
public class OrderService {
    public void processOrder(Order order) {
        // Hidden dependency - không rõ ràng từ method signature
        Logger.getInstance().log("Processing order");
        PaymentGateway.getInstance().charge(order.getAmount());
    }
}
```

#### 4. Global state problems
```java
// Singleton có thể tạo ra global state
public class ConfigManager {
    private Map<String, String> config = new HashMap<>();
    
    public void setConfig(String key, String value) {
        config.put(key, value); // Global state change!
    }
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Tài nguyên dùng chung
```java
// Database connection pool
public class ConnectionPool {
    private List<Connection> connections;
    private static ConnectionPool instance;
    
    private ConnectionPool() {
        connections = createConnections(); // Expensive operation
    }
    
    public static ConnectionPool getInstance() {
        if (instance == null) {
            instance = new ConnectionPool();
        }
        return instance;
    }
}
```

#### 2. Configuration management
```java
public class AppConfig {
    private Properties properties;
    
    private AppConfig() {
        properties = loadConfiguration(); // Load from file once
    }
    
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
}
```

#### 3. Logging
```java
public class Logger {
    private FileWriter fileWriter;
    
    private Logger() {
        try {
            fileWriter = new FileWriter("app.log", true);
        } catch (IOException e) {
            throw new RuntimeException("Cannot create logger", e);
        }
    }
    
    public void log(String message) {
        // Thread-safe logging
        synchronized(this) {
            fileWriter.write(new Date() + ": " + message + "\n");
            fileWriter.flush();
        }
    }
}
```

### ❌ Không nên sử dụng khi:

#### 1. Cần multiple instances
```java
// Sai: Dùng Singleton cho User
public class User {
    private String name;
    // Cần nhiều users khác nhau!
}
```

#### 2. Stateless objects
```java
// Sai: Utility class không cần Singleton
public class MathUtils {
    public static int add(int a, int b) {
        return a + b; // Pure function, không cần instance
    }
}
```

#### 3. Testing là priority
```java
// Khó test vì không thể inject dependencies
public class EmailService {
    public void sendEmail(String to, String message) {
        SmtpClient.getInstance().send(to, message); // Hard to mock
    }
}

// Better: Dependency injection
public class EmailService {
    private SmtpClient smtpClient;
    
    public EmailService(SmtpClient smtpClient) {
        this.smtpClient = smtpClient; // Easy to inject mock
    }
}
```

---

## Ví dụ thực tế

### Ví dụ 1: Database Connection Manager

```java
public class DatabaseManager {
    private static volatile DatabaseManager instance;
    private Connection connection;
    
    private DatabaseManager() {
        try {
            // Expensive database connection setup
            String url = "********************************";
            String username = "user";
            String password = "password";
            
            this.connection = DriverManager.getConnection(url, username, password);
            System.out.println("Database connection established");
        } catch (SQLException e) {
            throw new RuntimeException("Failed to connect to database", e);
        }
    }
    
    public static DatabaseManager getInstance() {
        if (instance == null) {
            synchronized (DatabaseManager.class) {
                if (instance == null) {
                    instance = new DatabaseManager();
                }
            }
        }
        return instance;
    }
    
    public ResultSet executeQuery(String sql) throws SQLException {
        Statement statement = connection.createStatement();
        return statement.executeQuery(sql);
    }
    
    public int executeUpdate(String sql) throws SQLException {
        Statement statement = connection.createStatement();
        return statement.executeUpdate(sql);
    }
    
    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("Database connection closed");
            }
        } catch (SQLException e) {
            System.err.println("Error closing connection: " + e.getMessage());
        }
    }
}
```

### Ví dụ 2: Application Logger

```java
public class ApplicationLogger {
    private static ApplicationLogger instance;
    private PrintWriter logWriter;
    private String logLevel = "INFO";
    
    private ApplicationLogger() {
        try {
            // Create log file with timestamp
            String fileName = "app_" + System.currentTimeMillis() + ".log";
            logWriter = new PrintWriter(new FileWriter(fileName, true));
            log("INFO", "Logger initialized");
        } catch (IOException e) {
            throw new RuntimeException("Cannot initialize logger", e);
        }
    }
    
    public static synchronized ApplicationLogger getInstance() {
        if (instance == null) {
            instance = new ApplicationLogger();
        }
        return instance;
    }
    
    public synchronized void log(String level, String message) {
        if (shouldLog(level)) {
            String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String logEntry = String.format("[%s] %s: %s", timestamp, level, message);
            
            logWriter.println(logEntry);
            logWriter.flush();
            
            // Also print to console for development
            System.out.println(logEntry);
        }
    }
    
    public void info(String message) { log("INFO", message); }
    public void warn(String message) { log("WARN", message); }
    public void error(String message) { log("ERROR", message); }
    public void debug(String message) { log("DEBUG", message); }
    
    private boolean shouldLog(String level) {
        // Simple level checking
        return true; // In real implementation, check against configured level
    }
    
    public void setLogLevel(String level) {
        this.logLevel = level;
        log("INFO", "Log level changed to: " + level);
    }
    
    public void close() {
        if (logWriter != null) {
            log("INFO", "Logger shutting down");
            logWriter.close();
        }
    }
}

// Usage
public class Application {
    public static void main(String[] args) {
        ApplicationLogger logger = ApplicationLogger.getInstance();
        
        logger.info("Application started");
        logger.warn("This is a warning");
        logger.error("This is an error");
        
        // Same instance from anywhere
        ApplicationLogger sameLogger = ApplicationLogger.getInstance();
        sameLogger.info("Same logger instance: " + (logger == sameLogger));
        
        logger.close();
    }
}
```

### Ví dụ 3: Configuration Manager

```java
public class ConfigurationManager {
    private static ConfigurationManager instance;
    private Properties properties;
    private String configFile;
    
    private ConfigurationManager() {
        this.configFile = "application.properties";
        loadConfiguration();
    }
    
    public static ConfigurationManager getInstance() {
        if (instance == null) {
            synchronized (ConfigurationManager.class) {
                if (instance == null) {
                    instance = new ConfigurationManager();
                }
            }
        }
        return instance;
    }
    
    private void loadConfiguration() {
        properties = new Properties();
        try (InputStream input = getClass().getClassLoader().getResourceAsStream(configFile)) {
            if (input != null) {
                properties.load(input);
                System.out.println("Configuration loaded from " + configFile);
            } else {
                // Load default configuration
                setDefaultConfiguration();
                System.out.println("Default configuration loaded");
            }
        } catch (IOException e) {
            setDefaultConfiguration();
            System.err.println("Error loading configuration, using defaults: " + e.getMessage());
        }
    }
    
    private void setDefaultConfiguration() {
        properties.setProperty("database.url", "jdbc:h2:mem:testdb");
        properties.setProperty("database.username", "sa");
        properties.setProperty("database.password", "");
        properties.setProperty("server.port", "8080");
        properties.setProperty("app.name", "MyApplication");
    }
    
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    public int getIntProperty(String key, int defaultValue) {
        String value = properties.getProperty(key);
        try {
            return value != null ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        return value != null ? Boolean.parseBoolean(value) : defaultValue;
    }
    
    public void setProperty(String key, String value) {
        properties.setProperty(key, value);
    }
    
    public void reloadConfiguration() {
        loadConfiguration();
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Singleton** đảm bảo chỉ có một instance và cung cấp global access
2. **Thread safety** là concern quan trọng trong multi-threaded environment
3. **Bill Pugh solution** thường là best practice cho Java
4. **Cân nhắc kỹ** trước khi sử dụng vì có nhiều nhược điểm

### Best Practices
- **Sử dụng Bill Pugh** hoặc Enum approach
- **Avoid global state** trong Singleton
- **Consider dependency injection** thay vì Singleton
- **Make it thread-safe** nếu cần thiết
- **Handle exceptions** trong constructor properly

### Alternatives to consider
- **Dependency Injection** frameworks (Spring, Guice)
- **Static utility classes** cho stateless operations
- **Factory patterns** cho controlled object creation
- **Registry patterns** cho managing instances

---

**Tiếp theo:** [Structural Patterns](../structural/) - Tìm hiểu các mẫu cấu trúc
