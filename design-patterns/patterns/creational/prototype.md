# Prototype Pattern

> **Creational Pattern** - <PERSON><PERSON> chép các đối tượng hiện có mà không cần phụ thuộc vào lớp cụ thể

## 📋 <PERSON>ụ<PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [V<PERSON>n đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON><PERSON> triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON>ợ<PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [V<PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Prototype là một **creational design pattern** cho phép **sao chép các đối tượng hiện có mà không cần mã phụ thuộc vào lớp của chúng**.

### <PERSON><PERSON><PERSON> đích chính
- **T<PERSON><PERSON> đối tượng mới** bằng cách clone đối tượng hiện có
- **Tránh phụ thuộc** vào concrete classes
- **Tiết kiệm chi phí** tạo đối tượng phức tạp

### Tên gọi khác
- **Clone Pattern**
- **Object Cloning Pattern**

---

## Vấn đề

### Tình huống thực tế
Giả sử bạn có một đối tượng và muốn tạo một bản sao chính xác của nó. Cách làm thông thường:

```java
// Cách làm truyền thống - có vấn đề
public void copyObject(SomeObject original) {
    SomeObject copy = new SomeObject();
    copy.setField1(original.getField1());
    copy.setField2(original.getField2());
    copy.setField3(original.getField3());
    // ... copy tất cả fields
}
```

### Vấn đề phát sinh

#### 1. Phụ thuộc vào concrete class
```java
// Client code phải biết exact type
if (shape instanceof Circle) {
    Circle copy = new Circle();
    copy.setRadius(((Circle) shape).getRadius());
    copy.setCenter(((Circle) shape).getCenter());
} else if (shape instanceof Rectangle) {
    Rectangle copy = new Rectangle();
    copy.setWidth(((Rectangle) shape).getWidth());
    copy.setHeight(((Rectangle) shape).getHeight());
}
```

#### 2. Không thể truy cập private fields
```java
public class SecretObject {
    private String secretData; // Không thể access từ bên ngoài
    private List<String> privateList;
    
    // Làm sao copy được private fields?
}
```

#### 3. Chỉ biết interface, không biết concrete type
```java
public void processShapes(List<Shape> shapes) {
    for (Shape shape : shapes) {
        // Làm sao tạo copy khi chỉ biết Shape interface?
        Shape copy = ???; // Không biết concrete type
    }
}
```

#### 4. Chi phí tạo đối tượng cao
```java
// Đối tượng phức tạp, tốn kém để tạo
public class ComplexObject {
    private DatabaseConnection connection;
    private List<ExpensiveResource> resources;
    
    public ComplexObject() {
        // Expensive initialization
        connection = DatabasePool.getConnection();
        resources = loadExpensiveResources();
    }
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Prototype pattern **ủy quyền quá trình cloning cho chính các đối tượng được clone**. Pattern khai báo một giao diện chung cho tất cả các đối tượng hỗ trợ cloning.

### Cách hoạt động

#### 1. Tạo Prototype interface
```java
public interface Prototype {
    Prototype clone();
}
```

#### 2. Implement cloning trong concrete classes
```java
public class Circle implements Prototype {
    private int radius;
    private Point center;
    
    public Circle(int radius, Point center) {
        this.radius = radius;
        this.center = center;
    }
    
    // Copy constructor
    public Circle(Circle other) {
        this.radius = other.radius;
        this.center = new Point(other.center); // Deep copy
    }
    
    @Override
    public Prototype clone() {
        return new Circle(this);
    }
}
```

#### 3. Client code sử dụng cloning
```java
public class ShapeProcessor {
    public List<Shape> duplicateShapes(List<Shape> shapes) {
        List<Shape> copies = new ArrayList<>();
        
        for (Shape shape : shapes) {
            // Không cần biết concrete type!
            copies.add((Shape) shape.clone());
        }
        
        return copies;
    }
}
```

### Prototype Registry (Optional)
```java
public class ShapeRegistry {
    private Map<String, Prototype> prototypes = new HashMap<>();
    
    public void registerPrototype(String key, Prototype prototype) {
        prototypes.put(key, prototype);
    }
    
    public Prototype getPrototype(String key) {
        Prototype prototype = prototypes.get(key);
        return prototype != null ? prototype.clone() : null;
    }
}

// Usage
ShapeRegistry registry = new ShapeRegistry();
registry.registerPrototype("circle", new Circle(5, new Point(0, 0)));
registry.registerPrototype("rectangle", new Rectangle(10, 20));

// Tạo objects từ prototypes
Shape circle1 = (Shape) registry.getPrototype("circle");
Shape circle2 = (Shape) registry.getPrototype("circle");
```

---

## Cấu trúc

### Sơ đồ UML
```
Prototype (interface)
├── + clone(): Prototype
│
ConcretePrototype1
├── + clone(): Prototype
├── - field1: String
├── - field2: int
│
ConcretePrototype2
├── + clone(): Prototype
├── - fieldA: Object
├── - fieldB: List<String>
│
Client
├── - prototype: Prototype
├── + operation(): void
```

### Các thành phần chính

#### 1. Prototype Interface
- **Vai trò:** Khai báo phương thức cloning
- **Đặc điểm:** Thường chỉ có method `clone()`

#### 2. Concrete Prototype
- **Vai trò:** Triển khai phương thức cloning
- **Đặc điểm:** Biết cách copy chính nó

#### 3. Client
- **Vai trò:** Tạo đối tượng mới bằng cách clone prototype
- **Đặc điểm:** Không cần biết concrete classes

#### 4. Prototype Registry (Optional)
- **Vai trò:** Lưu trữ và quản lý các prototypes
- **Đặc điểm:** Factory cho pre-built prototypes

---

## Cách triển khai

### Bước 1: Định nghĩa Prototype interface
```java
public interface Cloneable {
    Object clone();
}

// Hoặc generic version
public interface Prototype<T> {
    T clone();
}
```

### Bước 2: Implement concrete prototypes
```java
public class Document implements Prototype<Document> {
    private String title;
    private String content;
    private List<String> tags;
    private DocumentMetadata metadata;
    
    public Document(String title, String content) {
        this.title = title;
        this.content = content;
        this.tags = new ArrayList<>();
        this.metadata = new DocumentMetadata();
    }
    
    // Copy constructor
    public Document(Document other) {
        this.title = other.title;
        this.content = other.content;
        this.tags = new ArrayList<>(other.tags); // Shallow copy of list
        this.metadata = other.metadata.clone(); // Deep copy of metadata
    }
    
    @Override
    public Document clone() {
        return new Document(this);
    }
    
    // Getters and setters
    public void addTag(String tag) {
        tags.add(tag);
    }
}
```

### Bước 3: Handle deep vs shallow copy
```java
public class ComplexObject implements Prototype<ComplexObject> {
    private String simpleField;
    private List<String> listField;
    private NestedObject nestedObject;
    
    @Override
    public ComplexObject clone() {
        ComplexObject copy = new ComplexObject();
        
        // Shallow copy for immutable objects
        copy.simpleField = this.simpleField;
        
        // Deep copy for mutable collections
        copy.listField = new ArrayList<>(this.listField);
        
        // Deep copy for nested objects
        copy.nestedObject = this.nestedObject.clone();
        
        return copy;
    }
}
```

### Bước 4: Sử dụng với Registry
```java
public class PrototypeManager {
    private Map<String, Prototype> prototypes = new HashMap<>();
    
    public void addPrototype(String key, Prototype prototype) {
        prototypes.put(key, prototype);
    }
    
    public Prototype getPrototype(String key) {
        Prototype prototype = prototypes.get(key);
        return prototype != null ? prototype.clone() : null;
    }
    
    public void removePrototype(String key) {
        prototypes.remove(key);
    }
}
```

### Bước 5: Client usage
```java
public class PrototypeDemo {
    public static void main(String[] args) {
        // Setup prototypes
        PrototypeManager manager = new PrototypeManager();
        
        Document template = new Document("Template", "Default content");
        template.addTag("template");
        manager.addPrototype("document-template", template);
        
        // Create copies
        Document doc1 = (Document) manager.getPrototype("document-template");
        doc1.setTitle("Document 1");
        doc1.addTag("important");
        
        Document doc2 = (Document) manager.getPrototype("document-template");
        doc2.setTitle("Document 2");
        doc2.addTag("draft");
        
        // doc1 and doc2 are independent copies
        System.out.println("Doc1 tags: " + doc1.getTags()); // [template, important]
        System.out.println("Doc2 tags: " + doc2.getTags()); // [template, draft]
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Giảm coupling
- **Client không cần biết** concrete classes
- **Chỉ phụ thuộc vào** Prototype interface
- **Dễ dàng thêm** prototype types mới

#### 2. Loại bỏ initialization code lặp lại
```java
// Thay vì lặp lại initialization
Document doc1 = new Document();
doc1.setDefaultSettings();
doc1.loadTemplate();
doc1.setupMetadata();

Document doc2 = new Document();
doc2.setDefaultSettings(); // Lặp lại!
doc2.loadTemplate();       // Lặp lại!
doc2.setupMetadata();      // Lặp lại!

// Dùng Prototype
Document template = createConfiguredDocument();
Document doc1 = template.clone(); // Đã có sẵn configuration
Document doc2 = template.clone();
```

#### 3. Tiện lợi cho complex objects
- **Tránh expensive initialization**
- **Preserve object state**
- **Faster than creating from scratch**

#### 4. Alternative to subclassing
```java
// Thay vì tạo nhiều subclasses
class RedCircle extends Circle { ... }
class BlueCircle extends Circle { ... }
class GreenCircle extends Circle { ... }

// Dùng prototypes với different configurations
Circle redCirclePrototype = new Circle(Color.RED);
Circle blueCirclePrototype = new Circle(Color.BLUE);
```

### ❌ Nhược điểm

#### 1. Cloning complex objects khó khăn
```java
// Circular references problem
public class Node {
    private Node parent;
    private List<Node> children;
    
    // Làm sao clone khi có circular reference?
}
```

#### 2. Deep vs Shallow copy confusion
- **Shallow copy:** Chỉ copy references
- **Deep copy:** Copy toàn bộ object graph
- **Cần quyết định** strategy cho từng field

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Tạo object tốn kém
```java
// Expensive object creation
public class DatabaseReport {
    public DatabaseReport() {
        // Connect to database
        // Run complex queries
        // Process large datasets
        // Generate charts
    }
}

// Better: Clone from prototype
DatabaseReport template = createReportTemplate();
DatabaseReport report1 = template.clone(); // Much faster
```

#### 2. Cần nhiều objects tương tự
```java
// Game development - tạo nhiều enemies tương tự
Enemy goblinPrototype = new Goblin(100, 10, "sword");
List<Enemy> goblins = new ArrayList<>();

for (int i = 0; i < 50; i++) {
    goblins.add(goblinPrototype.clone());
}
```

#### 3. Object configuration phức tạp
```java
// Complex configuration
EmailTemplate template = new EmailTemplate();
template.setHeader(complexHeader);
template.setFooter(complexFooter);
template.setStyles(complexStyles);
template.setLayout(complexLayout);

// Clone instead of reconfiguring
EmailTemplate newsletter = template.clone();
newsletter.setContent("Newsletter content");
```

### ❌ Không nên sử dụng khi:

#### 1. Simple objects
```java
// Không cần Prototype cho simple objects
public class Point {
    private int x, y;
    
    public Point(int x, int y) { // Simple constructor is enough
        this.x = x;
        this.y = y;
    }
}
```

#### 2. Immutable objects
```java
// Immutable objects có thể share safely
public final class ImmutablePerson {
    private final String name;
    private final int age;
    
    // No need to clone immutable objects
}
```

---

## Ví dụ thực tế

### Ví dụ 1: Game Character System

```java
public abstract class GameCharacter implements Prototype<GameCharacter> {
    protected String name;
    protected int health;
    protected int mana;
    protected List<Skill> skills;
    protected Equipment equipment;
    
    public GameCharacter(String name, int health, int mana) {
        this.name = name;
        this.health = health;
        this.mana = mana;
        this.skills = new ArrayList<>();
        this.equipment = new Equipment();
    }
    
    // Copy constructor
    protected GameCharacter(GameCharacter other) {
        this.name = other.name;
        this.health = other.health;
        this.mana = other.mana;
        this.skills = new ArrayList<>();
        for (Skill skill : other.skills) {
            this.skills.add(skill.clone());
        }
        this.equipment = other.equipment.clone();
    }
}

public class Warrior extends GameCharacter {
    private int strength;
    
    public Warrior(String name, int health, int mana, int strength) {
        super(name, health, mana);
        this.strength = strength;
    }
    
    public Warrior(Warrior other) {
        super(other);
        this.strength = other.strength;
    }
    
    @Override
    public GameCharacter clone() {
        return new Warrior(this);
    }
}

// Usage
public class CharacterFactory {
    private Map<String, GameCharacter> prototypes = new HashMap<>();
    
    public void initializePrototypes() {
        Warrior warriorTemplate = new Warrior("Warrior", 100, 50, 80);
        warriorTemplate.addSkill(new Skill("Sword Strike"));
        warriorTemplate.addSkill(new Skill("Shield Block"));
        
        prototypes.put("warrior", warriorTemplate);
    }
    
    public GameCharacter createCharacter(String type, String name) {
        GameCharacter prototype = prototypes.get(type);
        if (prototype != null) {
            GameCharacter character = prototype.clone();
            character.setName(name);
            return character;
        }
        return null;
    }
}
```

### Ví dụ 2: Document Template System

```java
public class DocumentTemplate implements Prototype<DocumentTemplate> {
    private String templateName;
    private String header;
    private String footer;
    private List<Section> sections;
    private DocumentStyle style;
    
    public DocumentTemplate(String templateName) {
        this.templateName = templateName;
        this.sections = new ArrayList<>();
        this.style = new DocumentStyle();
    }
    
    public DocumentTemplate(DocumentTemplate other) {
        this.templateName = other.templateName;
        this.header = other.header;
        this.footer = other.footer;
        this.sections = new ArrayList<>();
        for (Section section : other.sections) {
            this.sections.add(section.clone());
        }
        this.style = other.style.clone();
    }
    
    @Override
    public DocumentTemplate clone() {
        return new DocumentTemplate(this);
    }
    
    public Document generateDocument(String title, String content) {
        DocumentTemplate template = this.clone();
        return new Document(template, title, content);
    }
}

// Usage
DocumentTemplate reportTemplate = new DocumentTemplate("Report");
reportTemplate.setHeader("Company Report Header");
reportTemplate.setFooter("Confidential");
reportTemplate.addSection(new Section("Executive Summary"));
reportTemplate.addSection(new Section("Financial Data"));

// Generate multiple reports from template
Document q1Report = reportTemplate.generateDocument("Q1 Report", "Q1 data...");
Document q2Report = reportTemplate.generateDocument("Q2 Report", "Q2 data...");
```

---

## 🎯 Tóm tắt

### Key Points
1. **Prototype Pattern** cho phép clone objects mà không phụ thuộc concrete classes
2. **Giải quyết vấn đề** expensive object creation và complex initialization
3. **Hỗ trợ** cả shallow và deep copying strategies
4. **Registry pattern** có thể kết hợp để quản lý prototypes

### So sánh với patterns khác
| Pattern | Mục đích | Khi dùng |
|---------|----------|----------|
| **Prototype** | Clone existing objects | Expensive creation, complex config |
| **Factory Method** | Create through interface | Don't know concrete type |
| **Builder** | Build step by step | Complex construction process |

### Mối quan hệ với patterns khác
- **Có thể kết hợp với Factory Method** để return cloned prototypes
- **Registry có thể sử dụng Singleton** pattern
- **Memento pattern** cũng sử dụng cloning concept

---

**Tiếp theo:** [Singleton](singleton.md) - Đảm bảo chỉ có một instance duy nhất
