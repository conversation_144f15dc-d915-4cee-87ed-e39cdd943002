# Abstract Factory Pattern

> **Creational Pattern** - T<PERSON><PERSON> ra các họ đối tượng liên quan mà không cần chỉ định các lớp cụ thể

## 📋 <PERSON>ụ<PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [G<PERSON><PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [C<PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON>ợ<PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [V<PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Abstract Factory là một **creational design pattern** cho phép **tạo ra các họ đối tượng liên quan** mà không cần chỉ định các lớp cụ thể của chúng.

### <PERSON><PERSON><PERSON> đích chính
- **Đảm bảo tính tương thích** giữa các sản phẩm trong cùng một họ
- **Tách biệt** client code khỏi các lớp sản phẩm cụ thể
- **Dễ dàng thay đổi** toàn bộ họ sản phẩm

### Tên gọi khác
- **Kit Pattern**
- **Family of Products Pattern**

---

## Vấn đề

### Tình huống thực tế
Giả sử bạn đang phát triển một cửa hàng nội thất. Cửa hàng bán các sản phẩm thuộc nhiều họ khác nhau:

**Sản phẩm:** `Chair`, `Sofa`, `CoffeeTable`
**Phong cách:** `Modern`, `Victorian`, `ArtDeco`

### Yêu cầu nghiệp vụ
1. **Tính nhất quán:** Khách hàng không muốn mua ghế Modern với sofa Victorian
2. **Tính linh hoạt:** Dễ dàng thêm phong cách mới hoặc sản phẩm mới
3. **Tách biệt:** Client code không nên phụ thuộc vào các lớp cụ thể

### Vấn đề với cách tiếp cận truyền thống

```java
// Cách làm không tốt - client phải biết tất cả lớp cụ thể
public class FurnitureStore {
    public void createModernSet() {
        Chair chair = new ModernChair();
        Sofa sofa = new ModernSofa();
        CoffeeTable table = new ModernCoffeeTable();
        // Phải nhớ tạo đúng phong cách!
    }
    
    public void createVictorianSet() {
        Chair chair = new VictorianChair();
        Sofa sofa = new VictorianSofa();
        CoffeeTable table = new VictorianCoffeeTable();
        // Lặp lại logic tương tự!
    }
}
```

**Vấn đề:**
1. **Khó đảm bảo tính nhất quán** - có thể tạo nhầm lẫn phong cách
2. **Mã lặp lại** cho mỗi phong cách
3. **Khó mở rộng** khi thêm phong cách mới
4. **Vi phạm Open/Closed Principle**

---

## Giải pháp

### Ý tưởng cốt lõi
Abstract Factory đề xuất **khai báo giao diện riêng biệt cho từng loại sản phẩm** và **tạo giao diện Abstract Factory** với các phương thức tạo cho tất cả sản phẩm.

### Các bước giải quyết

#### 1. Khai báo giao diện cho từng sản phẩm
```java
public interface Chair {
    void sitOn();
    boolean hasLegs();
}

public interface Sofa {
    void lieOn();
    int getSeats();
}

public interface CoffeeTable {
    void putCup();
    String getMaterial();
}
```

#### 2. Triển khai các biến thể sản phẩm
```java
// Modern family
public class ModernChair implements Chair {
    @Override
    public void sitOn() {
        System.out.println("Sitting on modern chair");
    }
    
    @Override
    public boolean hasLegs() {
        return false; // Modern chairs often don't have visible legs
    }
}

public class ModernSofa implements Sofa {
    @Override
    public void lieOn() {
        System.out.println("Lying on modern sofa");
    }
    
    @Override
    public int getSeats() {
        return 3;
    }
}

// Victorian family
public class VictorianChair implements Chair {
    @Override
    public void sitOn() {
        System.out.println("Sitting on Victorian chair");
    }
    
    @Override
    public boolean hasLegs() {
        return true; // Victorian chairs have ornate legs
    }
}
```

#### 3. Tạo Abstract Factory interface
```java
public interface FurnitureFactory {
    Chair createChair();
    Sofa createSofa();
    CoffeeTable createCoffeeTable();
}
```

#### 4. Triển khai Concrete Factories
```java
public class ModernFurnitureFactory implements FurnitureFactory {
    @Override
    public Chair createChair() {
        return new ModernChair();
    }
    
    @Override
    public Sofa createSofa() {
        return new ModernSofa();
    }
    
    @Override
    public CoffeeTable createCoffeeTable() {
        return new ModernCoffeeTable();
    }
}

public class VictorianFurnitureFactory implements FurnitureFactory {
    @Override
    public Chair createChair() {
        return new VictorianChair();
    }
    
    @Override
    public Sofa createSofa() {
        return new VictorianSofa();
    }
    
    @Override
    public CoffeeTable createCoffeeTable() {
        return new VictorianCoffeeTable();
    }
}
```

#### 5. Client code sử dụng factory
```java
public class FurnitureStore {
    private FurnitureFactory factory;
    
    public FurnitureStore(FurnitureFactory factory) {
        this.factory = factory;
    }
    
    public void createFurnitureSet() {
        Chair chair = factory.createChair();
        Sofa sofa = factory.createSofa();
        CoffeeTable table = factory.createCoffeeTable();
        
        // Đảm bảo tất cả đều cùng phong cách!
        chair.sitOn();
        sofa.lieOn();
        table.putCup();
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
AbstractFactory
├── + createProductA(): AbstractProductA
├── + createProductB(): AbstractProductB
│
├── ConcreteFactory1
│   ├── + createProductA(): ProductA1
│   └── + createProductB(): ProductB1
│
└── ConcreteFactory2
    ├── + createProductA(): ProductA2
    └── + createProductB(): ProductB2

AbstractProductA          AbstractProductB
├── ProductA1            ├── ProductB1
└── ProductA2            └── ProductB2
```

### Các thành phần chính

#### 1. Abstract Products
- **Vai trò:** Khai báo giao diện cho từng loại sản phẩm
- **Ví dụ:** `Chair`, `Sofa`, `CoffeeTable`

#### 2. Concrete Products
- **Vai trò:** Triển khai các biến thể của sản phẩm, được nhóm theo họ
- **Ví dụ:** `ModernChair`, `VictorianChair`

#### 3. Abstract Factory
- **Vai trò:** Khai báo giao diện với các phương thức tạo cho mỗi abstract product
- **Ví dụ:** `FurnitureFactory`

#### 4. Concrete Factories
- **Vai trò:** Triển khai các phương thức tạo của abstract factory
- **Đặc điểm:** Mỗi factory tương ứng với một biến thể sản phẩm cụ thể
- **Ví dụ:** `ModernFurnitureFactory`, `VictorianFurnitureFactory`

#### 5. Client
- **Vai trò:** Sử dụng abstract factory và abstract products
- **Đặc điểm:** Không biết về concrete classes

---

## Cách triển khai

### Bước 1: Xác định ma trận sản phẩm
```
           | Modern    | Victorian | ArtDeco
-----------|-----------|-----------|----------
Chair      | ModernC   | VictorianC| ArtDecoC
Sofa       | ModernS   | VictorianS| ArtDecoS
Table      | ModernT   | VictorianT| ArtDecoT
```

### Bước 2: Tạo abstract products
```java
public interface Chair {
    void sitOn();
}

public interface Sofa {
    void lieOn();
}
```

### Bước 3: Triển khai concrete products
```java
// Modern family
public class ModernChair implements Chair {
    @Override
    public void sitOn() {
        System.out.println("Sitting on a modern chair");
    }
}

public class ModernSofa implements Sofa {
    @Override
    public void lieOn() {
        System.out.println("Lying on a modern sofa");
    }
}

// Victorian family
public class VictorianChair implements Chair {
    @Override
    public void sitOn() {
        System.out.println("Sitting on a Victorian chair");
    }
}
```

### Bước 4: Tạo abstract factory
```java
public interface FurnitureFactory {
    Chair createChair();
    Sofa createSofa();
}
```

### Bước 5: Triển khai concrete factories
```java
public class ModernFurnitureFactory implements FurnitureFactory {
    @Override
    public Chair createChair() {
        return new ModernChair();
    }
    
    @Override
    public Sofa createSofa() {
        return new ModernSofa();
    }
}
```

### Bước 6: Sử dụng trong client
```java
public class Application {
    private FurnitureFactory factory;
    private Chair chair;
    private Sofa sofa;
    
    public Application(FurnitureFactory factory) {
        this.factory = factory;
    }
    
    public void createFurniture() {
        chair = factory.createChair();
        sofa = factory.createSofa();
    }
}

// Usage
public class Main {
    public static void main(String[] args) {
        FurnitureFactory factory = new ModernFurnitureFactory();
        Application app = new Application(factory);
        app.createFurniture();
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Đảm bảo tính tương thích sản phẩm
- **Các sản phẩm từ cùng factory** luôn tương thích với nhau
- **Không thể tạo nhầm lẫn** giữa các họ sản phẩm

#### 2. Giảm kết nối chặt chẽ
- **Client code** không phụ thuộc vào concrete classes
- **Dễ dàng thay đổi** họ sản phẩm

#### 3. Tuân thủ Single Responsibility Principle
- **Mỗi factory** chỉ chịu trách nhiệm tạo một họ sản phẩm
- **Tách biệt** logic tạo đối tượng

#### 4. Tuân thủ Open/Closed Principle
- **Thêm họ sản phẩm mới** mà không thay đổi mã hiện có
- **Mở rộng dễ dàng**

### ❌ Nhược điểm

#### 1. Tăng độ phức tạp
- **Nhiều giao diện và lớp** cần được tạo
- **Có thể over-engineering** cho các trường hợp đơn giản

#### 2. Khó thêm sản phẩm mới
- **Thêm loại sản phẩm mới** yêu cầu thay đổi tất cả factories
- **Vi phạm Open/Closed** khi mở rộng theo chiều sản phẩm

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Có nhiều họ sản phẩm liên quan
```java
// UI Components cho các platform khác nhau
WindowsFactory -> WindowsButton, WindowsCheckbox
MacFactory -> MacButton, MacCheckbox
LinuxFactory -> LinuxButton, LinuxCheckbox
```

#### 2. Cần đảm bảo tính tương thích
- **Sản phẩm phải hoạt động cùng nhau**
- **Không được trộn lẫn** giữa các họ

#### 3. Muốn ẩn implementation details
- **Client không cần biết** cách tạo sản phẩm
- **Tập trung vào interface** thay vì implementation

#### 4. Cần thay đổi họ sản phẩm dễ dàng
- **Runtime switching** giữa các họ sản phẩm
- **Configuration-based** product family selection

### ❌ Không nên sử dụng khi:

#### 1. Chỉ có một loại sản phẩm
- **Factory Method** đã đủ
- **Không cần abstract factory**

#### 2. Sản phẩm không liên quan
- **Không có khái niệm "họ sản phẩm"**
- **Mỗi sản phẩm độc lập**

#### 3. Hiếm khi thay đổi
- **Stable product families**
- **Simple factory** có thể đủ

---

## Ví dụ thực tế

### Ví dụ 1: Cross-Platform UI

```java
// Abstract Products
public interface Button {
    void paint();
}

public interface Checkbox {
    void paint();
}

// Windows Products
public class WindowsButton implements Button {
    @Override
    public void paint() {
        System.out.println("Render Windows button");
    }
}

public class WindowsCheckbox implements Checkbox {
    @Override
    public void paint() {
        System.out.println("Render Windows checkbox");
    }
}

// Mac Products
public class MacButton implements Button {
    @Override
    public void paint() {
        System.out.println("Render Mac button");
    }
}

// Abstract Factory
public interface GUIFactory {
    Button createButton();
    Checkbox createCheckbox();
}

// Concrete Factories
public class WindowsFactory implements GUIFactory {
    @Override
    public Button createButton() {
        return new WindowsButton();
    }
    
    @Override
    public Checkbox createCheckbox() {
        return new WindowsCheckbox();
    }
}

// Application
public class Application {
    private Button button;
    private Checkbox checkbox;
    
    public Application(GUIFactory factory) {
        button = factory.createButton();
        checkbox = factory.createCheckbox();
    }
    
    public void paint() {
        button.paint();
        checkbox.paint();
    }
}
```

### Ví dụ 2: Database Connection Factory

```java
public interface Connection {
    void connect();
}

public interface Command {
    void execute(String sql);
}

public interface DataReader {
    String read();
}

// MySQL Family
public class MySQLConnection implements Connection {
    @Override
    public void connect() {
        System.out.println("Connecting to MySQL");
    }
}

// PostgreSQL Family
public class PostgreSQLConnection implements Connection {
    @Override
    public void connect() {
        System.out.println("Connecting to PostgreSQL");
    }
}

// Abstract Factory
public interface DatabaseFactory {
    Connection createConnection();
    Command createCommand();
    DataReader createDataReader();
}

// Concrete Factory
public class MySQLFactory implements DatabaseFactory {
    @Override
    public Connection createConnection() {
        return new MySQLConnection();
    }
    
    @Override
    public Command createCommand() {
        return new MySQLCommand();
    }
    
    @Override
    public DataReader createDataReader() {
        return new MySQLDataReader();
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Abstract Factory** tạo ra các họ đối tượng liên quan
2. **Đảm bảo tính tương thích** giữa các sản phẩm trong cùng họ
3. **Client code** không phụ thuộc vào concrete classes
4. **Dễ dàng thay đổi** toàn bộ họ sản phẩm

### So sánh với Factory Method
| Aspect | Factory Method | Abstract Factory |
|--------|----------------|------------------|
| **Mục đích** | Tạo một sản phẩm | Tạo họ sản phẩm |
| **Phức tạp** | Đơn giản hơn | Phức tạp hơn |
| **Sử dụng** | Một loại sản phẩm | Nhiều sản phẩm liên quan |

### Mối quan hệ với patterns khác
- **Thường phát triển từ Factory Method**
- **Có thể sử dụng với Builder**
- **Có thể triển khai dưới dạng Singleton**

---

**Tiếp theo:** [Builder](builder.md) - Xây dựng đối tượng phức tạp từng bước
