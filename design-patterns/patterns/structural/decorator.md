# Decorator Pattern

> **Structural Pattern** - Thêm chức năng mới cho đối tượng một cách động mà không thay đổi cấu trúc

## 📋 <PERSON>ụ<PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [Gi<PERSON>i pháp](#gi<PERSON>i-pháp)
4. [C<PERSON>u trúc](#cấu-trúc)
5. [Cách triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nhược-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Decorator là một **structural design pattern** cho phép **thêm hành vi mới cho objects bằng cách đặt chúng vào trong các wrapper objects đặc biệt** chứa các hành vi đó.

### Mụ<PERSON> đích chính
- **Thêm functionality** cho objects mà không thay đổi structure
- **Combine behaviors** bằng cách wrapping objects
- **Alternative to subclassing** cho extending functionality

### Tên gọi khác
- **Wrapper Pattern**
- **Smart Proxy Pattern**

### Ví dụ thực tế
Giống như **trang trí quần áo**: bạn có thể mặc áo sơ mi (base), thêm áo vest (decorator), rồi thêm áo khoác (another decorator). Mỗi layer thêm chức năng mới mà không thay đổi layer bên trong.

---

## Vấn đề

### Tình huống thực tế
Bạn đang phát triển notification library. Ban đầu chỉ có `EmailNotifier`:

```java
public class EmailNotifier {
    public void send(String message) {
        System.out.println("Sending email: " + message);
    }
}
```

Sau đó users muốn thêm SMS, Slack, Facebook notifications:

### Vấn đề phát sinh

#### 1. Subclass explosion
```java
// Cách tiếp cận inheritance - tạo ra quá nhiều classes
class EmailNotifier { ... }
class SMSNotifier { ... }
class SlackNotifier { ... }

// Combinations
class EmailAndSMSNotifier { ... }
class EmailAndSlackNotifier { ... }
class SMSAndSlackNotifier { ... }
class EmailSMSAndSlackNotifier { ... }

// Thêm Facebook?
class FacebookNotifier { ... }
class EmailAndFacebookNotifier { ... }
class SMSAndFacebookNotifier { ... }
// ... 8 more combinations!
```

#### 2. Code duplication
```java
class EmailAndSMSNotifier {
    public void send(String message) {
        // Duplicate email logic
        System.out.println("Sending email: " + message);
        // Duplicate SMS logic
        System.out.println("Sending SMS: " + message);
    }
}

class EmailAndSlackNotifier {
    public void send(String message) {
        // Same email logic duplicated!
        System.out.println("Sending email: " + message);
        // Slack logic
        System.out.println("Sending Slack: " + message);
    }
}
```

#### 3. Inflexible combinations
```java
// Không thể dynamic combination
public class NotificationService {
    public void notify(String message, boolean email, boolean sms, boolean slack) {
        if (email && sms && slack) {
            new EmailSMSAndSlackNotifier().send(message);
        } else if (email && sms) {
            new EmailAndSMSNotifier().send(message);
        }
        // Endless if-else combinations!
    }
}
```

#### 4. Runtime modification impossible
```java
// Không thể thay đổi behavior runtime
EmailNotifier notifier = new EmailNotifier();
// Làm sao thêm SMS notification sau khi đã tạo object?
```

---

## Giải pháp

### Ý tưởng cốt lõi
Decorator pattern đề xuất **wrapping objects trong decorator objects** có cùng interface. Decorators có thể execute behavior trước/sau khi delegate call đến wrapped object.

### Cách hoạt động

#### 1. Component interface
```java
public interface Notifier {
    void send(String message);
}
```

#### 2. Concrete Component
```java
public class EmailNotifier implements Notifier {
    @Override
    public void send(String message) {
        System.out.println("📧 Sending email: " + message);
    }
}
```

#### 3. Base Decorator
```java
public abstract class NotifierDecorator implements Notifier {
    protected Notifier notifier;
    
    public NotifierDecorator(Notifier notifier) {
        this.notifier = notifier;
    }
    
    @Override
    public void send(String message) {
        notifier.send(message); // Delegate to wrapped object
    }
}
```

#### 4. Concrete Decorators
```java
public class SMSDecorator extends NotifierDecorator {
    public SMSDecorator(Notifier notifier) {
        super(notifier);
    }
    
    @Override
    public void send(String message) {
        super.send(message); // Call wrapped object first
        sendSMS(message);    // Add new behavior
    }
    
    private void sendSMS(String message) {
        System.out.println("📱 Sending SMS: " + message);
    }
}

public class SlackDecorator extends NotifierDecorator {
    public SlackDecorator(Notifier notifier) {
        super(notifier);
    }
    
    @Override
    public void send(String message) {
        super.send(message);
        sendSlack(message);
    }
    
    private void sendSlack(String message) {
        System.out.println("💬 Sending Slack: " + message);
    }
}

public class FacebookDecorator extends NotifierDecorator {
    public FacebookDecorator(Notifier notifier) {
        super(notifier);
    }
    
    @Override
    public void send(String message) {
        super.send(message);
        sendFacebook(message);
    }
    
    private void sendFacebook(String message) {
        System.out.println("📘 Sending Facebook: " + message);
    }
}
```

#### 5. Flexible usage
```java
public class DecoratorDemo {
    public static void main(String[] args) {
        // Base notifier
        Notifier notifier = new EmailNotifier();
        
        // Add SMS
        notifier = new SMSDecorator(notifier);
        
        // Add Slack
        notifier = new SlackDecorator(notifier);
        
        // Add Facebook
        notifier = new FacebookDecorator(notifier);
        
        // Send notification through all channels
        notifier.send("Hello World!");
        
        // Output:
        // 📧 Sending email: Hello World!
        // 📱 Sending SMS: Hello World!
        // 💬 Sending Slack: Hello World!
        // 📘 Sending Facebook: Hello World!
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Component (interface)
├── + operation(): void
│
ConcreteComponent implements Component
├── + operation(): void
│
Decorator implements Component
├── - component: Component
├── + operation(): void
│
ConcreteDecoratorA extends Decorator
├── + operation(): void
├── + addedBehavior(): void
│
ConcreteDecoratorB extends Decorator
├── + operation(): void
├── + addedState: Object
```

### Các thành phần chính

#### 1. Component
- **Vai trò:** Interface chung cho objects và decorators
- **Đặc điểm:** Defines operations có thể được altered by decorators

#### 2. Concrete Component
- **Vai trò:** Class định nghĩa basic behavior
- **Đặc điểm:** Can be wrapped by decorators

#### 3. Base Decorator
- **Vai trò:** Has reference field pointing to wrapped object
- **Đặc điểm:** Delegates all operations to wrapped object

#### 4. Concrete Decorators
- **Vai trò:** Define extra behaviors có thể được added to components
- **Đặc điểm:** Override methods và execute behavior before/after calling parent

---

## Cách triển khai

### Bước 1: Định nghĩa Component interface
```java
public interface Coffee {
    String getDescription();
    double getCost();
}
```

### Bước 2: Implement Concrete Component
```java
public class Espresso implements Coffee {
    @Override
    public String getDescription() {
        return "Espresso";
    }
    
    @Override
    public double getCost() {
        return 2.50;
    }
}

public class HouseBlend implements Coffee {
    @Override
    public String getDescription() {
        return "House Blend Coffee";
    }
    
    @Override
    public double getCost() {
        return 1.99;
    }
}
```

### Bước 3: Base Decorator
```java
public abstract class CondimentDecorator implements Coffee {
    protected Coffee coffee;
    
    public CondimentDecorator(Coffee coffee) {
        this.coffee = coffee;
    }
    
    @Override
    public String getDescription() {
        return coffee.getDescription();
    }
    
    @Override
    public double getCost() {
        return coffee.getCost();
    }
}
```

### Bước 4: Concrete Decorators
```java
public class Milk extends CondimentDecorator {
    public Milk(Coffee coffee) {
        super(coffee);
    }
    
    @Override
    public String getDescription() {
        return coffee.getDescription() + ", Milk";
    }
    
    @Override
    public double getCost() {
        return coffee.getCost() + 0.30;
    }
}

public class Sugar extends CondimentDecorator {
    public Sugar(Coffee coffee) {
        super(coffee);
    }
    
    @Override
    public String getDescription() {
        return coffee.getDescription() + ", Sugar";
    }
    
    @Override
    public double getCost() {
        return coffee.getCost() + 0.20;
    }
}

public class Whip extends CondimentDecorator {
    public Whip(Coffee coffee) {
        super(coffee);
    }
    
    @Override
    public String getDescription() {
        return coffee.getDescription() + ", Whip";
    }
    
    @Override
    public double getCost() {
        return coffee.getCost() + 0.50;
    }
}
```

### Bước 5: Usage
```java
public class CoffeeShop {
    public static void main(String[] args) {
        // Order 1: Simple Espresso
        Coffee coffee1 = new Espresso();
        System.out.println(coffee1.getDescription() + " $" + coffee1.getCost());
        
        // Order 2: House Blend with Milk and Sugar
        Coffee coffee2 = new HouseBlend();
        coffee2 = new Milk(coffee2);
        coffee2 = new Sugar(coffee2);
        System.out.println(coffee2.getDescription() + " $" + coffee2.getCost());
        
        // Order 3: Espresso with everything
        Coffee coffee3 = new Espresso();
        coffee3 = new Milk(coffee3);
        coffee3 = new Sugar(coffee3);
        coffee3 = new Whip(coffee3);
        System.out.println(coffee3.getDescription() + " $" + coffee3.getCost());
        
        // Output:
        // Espresso $2.5
        // House Blend Coffee, Milk, Sugar $2.49
        // Espresso, Milk, Sugar, Whip $3.5
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Runtime behavior extension
```java
// Có thể thêm/bớt behaviors runtime
Coffee coffee = new Espresso();

if (customer.wantsMilk()) {
    coffee = new Milk(coffee);
}

if (customer.wantsSugar()) {
    coffee = new Sugar(coffee);
}
```

#### 2. Multiple decorations
```java
// Có thể combine multiple decorators
Coffee deluxeCoffee = new Whip(
    new Sugar(
        new Milk(
            new Espresso()
        )
    )
);
```

#### 3. Tuân thủ Single Responsibility Principle
```java
// Mỗi decorator có một responsibility
class EncryptionDecorator extends DataDecorator {
    public String process(String data) {
        return encrypt(super.process(data)); // Only handles encryption
    }
}

class CompressionDecorator extends DataDecorator {
    public String process(String data) {
        return compress(super.process(data)); // Only handles compression
    }
}
```

#### 4. Alternative to inheritance
```java
// Thay vì tạo nhiều subclasses
class EncryptedCompressedData extends Data { ... }
class CompressedEncryptedData extends Data { ... }

// Dùng decorators
Data data = new CompressionDecorator(
    new EncryptionDecorator(
        new BasicData()
    )
);
```

### ❌ Nhược điểm

#### 1. Complex object creation
```java
// Có thể trở nên phức tạp với nhiều decorators
Coffee complexCoffee = new WhipDecorator(
    new SugarDecorator(
        new MilkDecorator(
            new VanillaDecorator(
                new CaramelDecorator(
                    new Espresso()
                )
            )
        )
    )
);
```

#### 2. Hard to remove specific decorators
```java
// Khó remove decorator ở giữa chain
Coffee coffee = new Whip(new Sugar(new Milk(new Espresso())));
// Làm sao remove Sugar mà giữ Whip và Milk?
```

#### 3. Identity problems
```java
// Decorated object không còn là original type
Coffee coffee = new Milk(new Espresso());
if (coffee instanceof Espresso) { // false!
    // This won't work
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Cần thêm behaviors mà không thay đổi code
```java
// Existing text processing system
interface TextProcessor {
    String process(String text);
}

// Add new features without modifying existing classes
class BoldDecorator extends TextDecorator {
    public String process(String text) {
        return "<b>" + super.process(text) + "</b>";
    }
}
```

#### 2. Extension by inheritance không practical
```java
// Inheritance would create too many combinations
// Bold, Italic, Underline = 2^3 = 8 combinations
// With decorators: 3 decorator classes can create any combination
```

#### 3. Cần dynamic behavior composition
```java
// User preferences determine which features to apply
TextProcessor processor = new BasicTextProcessor();

if (userPrefs.isBoldEnabled()) {
    processor = new BoldDecorator(processor);
}

if (userPrefs.isItalicEnabled()) {
    processor = new ItalicDecorator(processor);
}
```

### ❌ Không nên sử dụng khi:

#### 1. Simple behavior addition
```java
// Overkill for simple cases
public class Logger {
    public void log(String message) {
        System.out.println(new Date() + ": " + message); // Simple addition
    }
}
```

#### 2. Component interface thay đổi thường xuyên
```java
// Unstable interface makes decorators hard to maintain
interface UnstableComponent {
    void method1();
    void method2(); // Added later
    void method3(); // Added even later
    // All decorators need updates!
}
```

---

## Ví dụ thực tế

### Ví dụ 1: Data Processing Pipeline

```java
// Component interface
public interface DataProcessor {
    String process(String data);
}

// Concrete component
public class BasicDataProcessor implements DataProcessor {
    @Override
    public String process(String data) {
        return data; // Pass through unchanged
    }
}

// Base decorator
public abstract class DataProcessorDecorator implements DataProcessor {
    protected DataProcessor processor;
    
    public DataProcessorDecorator(DataProcessor processor) {
        this.processor = processor;
    }
    
    @Override
    public String process(String data) {
        return processor.process(data);
    }
}

// Concrete decorators
public class EncryptionDecorator extends DataProcessorDecorator {
    public EncryptionDecorator(DataProcessor processor) {
        super(processor);
    }
    
    @Override
    public String process(String data) {
        String processed = super.process(data);
        return encrypt(processed);
    }
    
    private String encrypt(String data) {
        // Simple encryption simulation
        return "ENCRYPTED[" + data + "]";
    }
}

public class CompressionDecorator extends DataProcessorDecorator {
    public CompressionDecorator(DataProcessor processor) {
        super(processor);
    }
    
    @Override
    public String process(String data) {
        String processed = super.process(data);
        return compress(processed);
    }
    
    private String compress(String data) {
        // Simple compression simulation
        return "COMPRESSED[" + data + "]";
    }
}

public class Base64Decorator extends DataProcessorDecorator {
    public Base64Decorator(DataProcessor processor) {
        super(processor);
    }
    
    @Override
    public String process(String data) {
        String processed = super.process(data);
        return encodeBase64(processed);
    }
    
    private String encodeBase64(String data) {
        // Simple base64 simulation
        return "BASE64[" + data + "]";
    }
}

// Usage
public class DataProcessingDemo {
    public static void main(String[] args) {
        String originalData = "Sensitive user data";
        
        // Create processing pipeline
        DataProcessor processor = new BasicDataProcessor();
        
        // Add compression
        processor = new CompressionDecorator(processor);
        
        // Add encryption
        processor = new EncryptionDecorator(processor);
        
        // Add base64 encoding
        processor = new Base64Decorator(processor);
        
        String result = processor.process(originalData);
        System.out.println("Original: " + originalData);
        System.out.println("Processed: " + result);
        // Output: BASE64[ENCRYPTED[COMPRESSED[Sensitive user data]]]
        
        // Different pipeline for different security levels
        DataProcessor lightProcessor = new Base64Decorator(
            new BasicDataProcessor()
        );
        
        DataProcessor heavyProcessor = new Base64Decorator(
            new EncryptionDecorator(
                new CompressionDecorator(
                    new BasicDataProcessor()
                )
            )
        );
        
        System.out.println("Light: " + lightProcessor.process("Test data"));
        System.out.println("Heavy: " + heavyProcessor.process("Test data"));
    }
}
```

### Ví dụ 2: Web Request/Response Processing

```java
// Component interface
public interface HttpHandler {
    HttpResponse handle(HttpRequest request);
}

// Basic handler
public class BasicHttpHandler implements HttpHandler {
    @Override
    public HttpResponse handle(HttpRequest request) {
        return new HttpResponse(200, "Basic response for: " + request.getPath());
    }
}

// Base decorator
public abstract class HttpHandlerDecorator implements HttpHandler {
    protected HttpHandler handler;
    
    public HttpHandlerDecorator(HttpHandler handler) {
        this.handler = handler;
    }
    
    @Override
    public HttpResponse handle(HttpRequest request) {
        return handler.handle(request);
    }
}

// Concrete decorators
public class LoggingDecorator extends HttpHandlerDecorator {
    public LoggingDecorator(HttpHandler handler) {
        super(handler);
    }
    
    @Override
    public HttpResponse handle(HttpRequest request) {
        System.out.println("🔍 LOG: Handling request to " + request.getPath());
        long startTime = System.currentTimeMillis();
        
        HttpResponse response = super.handle(request);
        
        long duration = System.currentTimeMillis() - startTime;
        System.out.println("🔍 LOG: Response " + response.getStatus() + " in " + duration + "ms");
        
        return response;
    }
}

public class AuthenticationDecorator extends HttpHandlerDecorator {
    public AuthenticationDecorator(HttpHandler handler) {
        super(handler);
    }
    
    @Override
    public HttpResponse handle(HttpRequest request) {
        String token = request.getHeader("Authorization");
        
        if (token == null || !isValidToken(token)) {
            return new HttpResponse(401, "Unauthorized");
        }
        
        System.out.println("🔐 AUTH: User authenticated");
        return super.handle(request);
    }
    
    private boolean isValidToken(String token) {
        return token.startsWith("Bearer ") && token.length() > 10;
    }
}

public class CachingDecorator extends HttpHandlerDecorator {
    private Map<String, HttpResponse> cache = new HashMap<>();
    
    public CachingDecorator(HttpHandler handler) {
        super(handler);
    }
    
    @Override
    public HttpResponse handle(HttpRequest request) {
        String cacheKey = request.getMethod() + ":" + request.getPath();
        
        if (cache.containsKey(cacheKey)) {
            System.out.println("💾 CACHE: Cache hit for " + cacheKey);
            return cache.get(cacheKey);
        }
        
        System.out.println("💾 CACHE: Cache miss for " + cacheKey);
        HttpResponse response = super.handle(request);
        
        if (response.getStatus() == 200) {
            cache.put(cacheKey, response);
        }
        
        return response;
    }
}

// Usage
public class WebServerDemo {
    public static void main(String[] args) {
        // Create handler pipeline
        HttpHandler handler = new BasicHttpHandler();
        
        // Add caching
        handler = new CachingDecorator(handler);
        
        // Add authentication
        handler = new AuthenticationDecorator(handler);
        
        // Add logging
        handler = new LoggingDecorator(handler);
        
        // Test requests
        HttpRequest request1 = new HttpRequest("GET", "/api/users", 
            Map.of("Authorization", "Bearer valid_token_123"));
        
        HttpRequest request2 = new HttpRequest("GET", "/api/users", 
            Map.of("Authorization", "Bearer valid_token_123"));
        
        HttpRequest request3 = new HttpRequest("GET", "/api/users", 
            Map.of()); // No auth header
        
        System.out.println("=== Request 1 ===");
        HttpResponse response1 = handler.handle(request1);
        System.out.println("Response: " + response1.getBody());
        
        System.out.println("\n=== Request 2 (should hit cache) ===");
        HttpResponse response2 = handler.handle(request2);
        System.out.println("Response: " + response2.getBody());
        
        System.out.println("\n=== Request 3 (no auth) ===");
        HttpResponse response3 = handler.handle(request3);
        System.out.println("Response: " + response3.getBody());
    }
}

// Helper classes
class HttpRequest {
    private String method;
    private String path;
    private Map<String, String> headers;
    
    public HttpRequest(String method, String path, Map<String, String> headers) {
        this.method = method;
        this.path = path;
        this.headers = headers;
    }
    
    public String getMethod() { return method; }
    public String getPath() { return path; }
    public String getHeader(String name) { return headers.get(name); }
}

class HttpResponse {
    private int status;
    private String body;
    
    public HttpResponse(int status, String body) {
        this.status = status;
        this.body = body;
    }
    
    public int getStatus() { return status; }
    public String getBody() { return body; }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Decorator Pattern** thêm behavior động mà không thay đổi structure
2. **Composition over inheritance** để tránh class explosion
3. **Flexible combinations** của behaviors
4. **Runtime modification** của object behavior

### So sánh với patterns khác
| Pattern | Purpose | Structure |
|---------|---------|-----------|
| **Decorator** | Add behavior dynamically | Wrapper chain |
| **Adapter** | Make incompatible interfaces work | Single wrapper |
| **Proxy** | Control access to object | Single wrapper |
| **Composite** | Treat individual/group uniformly | Tree structure |

### Best Practices
- **Keep decorators simple** và focused
- **Consider order** của decorators trong chain
- **Provide builder** cho complex decorator combinations
- **Document** expected decorator behavior
- **Handle null cases** properly

---

**Tiếp theo:** [Facade](facade.md) - Cung cấp giao diện đơn giản cho hệ thống phức tạp
