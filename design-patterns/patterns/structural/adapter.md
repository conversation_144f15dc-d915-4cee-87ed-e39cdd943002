# Adapter Pattern

> **Structural Pattern** - <PERSON> phép các đối tượng có giao diện không tương thích làm việc cùng nhau

## 📋 <PERSON><PERSON><PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON><PERSON><PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [V<PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Adapter là một **structural design pattern** cho phép **các đối tượng có giao diện không tương thích hợp tác với nhau**.

### <PERSON><PERSON><PERSON> đích chính
- **Kết nối** các interfaces không tương thích
- **Tái sử dụng** existing code mà không cần modify
- **Tích hợp** third-party libraries hoặc legacy systems

### Tên gọi khác
- **Wrapper Pattern**
- **Translator Pattern**

### Ví dụ thực tế
Giống như **bộ chuyển đổi phích cắm điện** cho phép thiết bị Mỹ (110V) hoạt động với ổ cắm Châu Âu (220V).

---

## Vấn đề

### Tình huống thực tế
Bạn đang phát triển ứng dụng stock monitoring và muốn tích hợp thư viện analytics của bên thứ ba:

```java
// Your existing code works with XML
public class StockData {
    public void processXMLData(String xmlData) {
        // Process XML stock data
    }
}

// Third-party analytics library only accepts JSON
public class AnalyticsLibrary {
    public void analyzeData(String jsonData) {
        // Analyze JSON data
    }
}
```

### Vấn đề phát sinh

#### 1. Interface incompatibility
```java
// Không thể kết nối trực tiếp
StockData stockData = new StockData();
AnalyticsLibrary analytics = new AnalyticsLibrary();

String xmlData = stockData.getXMLData();
analytics.analyzeData(xmlData); // ERROR! Expects JSON, got XML
```

#### 2. Cannot modify existing code
```java
// Không thể sửa third-party library
public class AnalyticsLibrary {
    public void analyzeData(String jsonData) {
        // Cannot modify this method to accept XML
    }
}

// Không muốn sửa existing code
public class StockData {
    public void processXMLData(String xmlData) {
        // Don't want to change this working code
    }
}
```

#### 3. Different method signatures
```java
// Your interface
interface MediaPlayer {
    void play(String audioType, String fileName);
}

// Legacy audio player
class AdvancedMediaPlayer {
    void playVlc(String fileName) { ... }
    void playMp4(String fileName) { ... }
    // Different method names and parameters!
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Adapter pattern tạo một **wrapper class** đóng vai trò **trung gian** giữa client và service, chuyển đổi interface của service thành interface mà client mong đợi.

### Cách hoạt động

#### 1. Tạo Adapter class
```java
public class XMLToJSONAdapter {
    private AnalyticsLibrary analyticsLibrary;
    
    public XMLToJSONAdapter(AnalyticsLibrary analyticsLibrary) {
        this.analyticsLibrary = analyticsLibrary;
    }
    
    public void analyzeXMLData(String xmlData) {
        // Convert XML to JSON
        String jsonData = convertXMLToJSON(xmlData);
        
        // Call the third-party library
        analyticsLibrary.analyzeData(jsonData);
    }
    
    private String convertXMLToJSON(String xmlData) {
        // Conversion logic here
        return "{ \"data\": \"converted from XML\" }";
    }
}
```

#### 2. Client sử dụng Adapter
```java
public class StockAnalyzer {
    public void analyzeStock(String xmlData) {
        AnalyticsLibrary analytics = new AnalyticsLibrary();
        XMLToJSONAdapter adapter = new XMLToJSONAdapter(analytics);
        
        // Client chỉ cần biết adapter interface
        adapter.analyzeXMLData(xmlData);
    }
}
```

### Object Adapter vs Class Adapter

#### Object Adapter (Composition - Recommended)
```java
public class MediaAdapter implements MediaPlayer {
    private AdvancedMediaPlayer advancedPlayer; // Composition
    
    public MediaAdapter(String audioType) {
        if (audioType.equalsIgnoreCase("vlc")) {
            advancedPlayer = new VlcPlayer();
        } else if (audioType.equalsIgnoreCase("mp4")) {
            advancedPlayer = new Mp4Player();
        }
    }
    
    @Override
    public void play(String audioType, String fileName) {
        if (audioType.equalsIgnoreCase("vlc")) {
            advancedPlayer.playVlc(fileName);
        } else if (audioType.equalsIgnoreCase("mp4")) {
            advancedPlayer.playMp4(fileName);
        }
    }
}
```

#### Class Adapter (Inheritance - Limited)
```java
// Chỉ có thể dùng trong ngôn ngữ hỗ trợ multiple inheritance
public class MediaAdapter extends AdvancedMediaPlayer implements MediaPlayer {
    @Override
    public void play(String audioType, String fileName) {
        if (audioType.equalsIgnoreCase("vlc")) {
            playVlc(fileName); // Inherited method
        } else if (audioType.equalsIgnoreCase("mp4")) {
            playMp4(fileName); // Inherited method
        }
    }
}
```

---

## Cấu trúc

### Object Adapter Structure
```
Client
├── uses → Target (interface)
│
Adapter implements Target
├── - adaptee: Adaptee
├── + request(): void
│
Adaptee
├── + specificRequest(): void
```

### Các thành phần chính

#### 1. Target Interface
- **Vai trò:** Interface mà client mong đợi
- **Đặc điểm:** Định nghĩa domain-specific interface

#### 2. Adaptee
- **Vai trò:** Class hiện có với interface không tương thích
- **Đặc điểm:** Cần được adapted để work với client

#### 3. Adapter
- **Vai trò:** Wrapper class implement Target interface
- **Đặc điểm:** Chứa instance của Adaptee và translate calls

#### 4. Client
- **Vai trò:** Sử dụng objects thông qua Target interface
- **Đặc điểm:** Không biết về Adaptee hay Adapter

---

## Cách triển khai

### Bước 1: Định nghĩa Target interface
```java
public interface MediaPlayer {
    void play(String audioType, String fileName);
}
```

### Bước 2: Existing Adaptee classes
```java
public interface AdvancedMediaPlayer {
    void playVlc(String fileName);
    void playMp4(String fileName);
}

public class VlcPlayer implements AdvancedMediaPlayer {
    @Override
    public void playVlc(String fileName) {
        System.out.println("Playing vlc file: " + fileName);
    }
    
    @Override
    public void playMp4(String fileName) {
        // Do nothing
    }
}

public class Mp4Player implements AdvancedMediaPlayer {
    @Override
    public void playVlc(String fileName) {
        // Do nothing
    }
    
    @Override
    public void playMp4(String fileName) {
        System.out.println("Playing mp4 file: " + fileName);
    }
}
```

### Bước 3: Tạo Adapter
```java
public class MediaAdapter implements MediaPlayer {
    private AdvancedMediaPlayer advancedMusicPlayer;
    
    public MediaAdapter(String audioType) {
        if (audioType.equalsIgnoreCase("vlc")) {
            advancedMusicPlayer = new VlcPlayer();
        } else if (audioType.equalsIgnoreCase("mp4")) {
            advancedMusicPlayer = new Mp4Player();
        }
    }
    
    @Override
    public void play(String audioType, String fileName) {
        if (audioType.equalsIgnoreCase("vlc")) {
            advancedMusicPlayer.playVlc(fileName);
        } else if (audioType.equalsIgnoreCase("mp4")) {
            advancedMusicPlayer.playMp4(fileName);
        }
    }
}
```

### Bước 4: Concrete Target implementation
```java
public class AudioPlayer implements MediaPlayer {
    private MediaAdapter mediaAdapter;
    
    @Override
    public void play(String audioType, String fileName) {
        // Built-in support for mp3
        if (audioType.equalsIgnoreCase("mp3")) {
            System.out.println("Playing mp3 file: " + fileName);
        }
        // Use adapter for other formats
        else if (audioType.equalsIgnoreCase("vlc") || 
                 audioType.equalsIgnoreCase("mp4")) {
            mediaAdapter = new MediaAdapter(audioType);
            mediaAdapter.play(audioType, fileName);
        } else {
            System.out.println("Invalid media. " + audioType + " format not supported");
        }
    }
}
```

### Bước 5: Client usage
```java
public class AdapterDemo {
    public static void main(String[] args) {
        AudioPlayer audioPlayer = new AudioPlayer();
        
        audioPlayer.play("mp3", "beyond_the_horizon.mp3");
        audioPlayer.play("mp4", "alone.mp4");
        audioPlayer.play("vlc", "far_far_away.vlc");
        audioPlayer.play("avi", "mind_me.avi");
    }
}

// Output:
// Playing mp3 file: beyond_the_horizon.mp3
// Playing mp4 file: alone.mp4
// Playing vlc file: far_far_away.vlc
// Invalid media. avi format not supported
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Tuân thủ Single Responsibility Principle
```java
// Adapter chỉ chịu trách nhiệm interface conversion
public class DatabaseAdapter {
    private LegacyDatabase legacyDb;
    
    // Only responsible for adapting interface
    public List<User> getUsers() {
        String[] rawData = legacyDb.fetchUserData();
        return convertToUserList(rawData);
    }
}
```

#### 2. Tuân thủ Open/Closed Principle
```java
// Có thể thêm adapters mới mà không sửa existing code
public class NewFormatAdapter implements MediaPlayer {
    private NewFormatPlayer newPlayer;
    
    @Override
    public void play(String audioType, String fileName) {
        newPlayer.playNewFormat(fileName);
    }
}
```

#### 3. Tách biệt interface conversion khỏi business logic
```java
public class PaymentProcessor {
    public void processPayment(PaymentMethod method, double amount) {
        // Business logic không quan tâm đến interface differences
        method.pay(amount);
    }
}

// Adapter handles interface conversion
public class PayPalAdapter implements PaymentMethod {
    private PayPalGateway paypalGateway;
    
    @Override
    public void pay(double amount) {
        // Convert to PayPal's expected format
        paypalGateway.makePayment(amount, "USD");
    }
}
```

### ❌ Nhược điểm

#### 1. Tăng độ phức tạp của code
```java
// Thêm layer of indirection
Client → Adapter → Adaptee
// Thay vì direct call: Client → Service
```

#### 2. Có thể ảnh hưởng performance
```java
public class DataAdapter {
    public String getData() {
        String rawData = legacySystem.getRawData();
        // Additional conversion overhead
        return convertData(rawData);
    }
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Tích hợp legacy systems
```java
// Legacy system với interface cũ
public class LegacyPrinter {
    public void printOldFormat(String data) {
        System.out.println("Legacy: " + data);
    }
}

// Modern interface
public interface Printer {
    void print(Document document);
}

// Adapter để tích hợp
public class LegacyPrinterAdapter implements Printer {
    private LegacyPrinter legacyPrinter;
    
    @Override
    public void print(Document document) {
        String oldFormat = document.convertToOldFormat();
        legacyPrinter.printOldFormat(oldFormat);
    }
}
```

#### 2. Third-party library integration
```java
// Third-party library
public class ExternalEmailService {
    public void sendMail(String to, String subject, String body, String from) {
        // External API
    }
}

// Your interface
public interface EmailSender {
    void send(Email email);
}

// Adapter
public class ExternalEmailAdapter implements EmailSender {
    private ExternalEmailService externalService;
    
    @Override
    public void send(Email email) {
        externalService.sendMail(
            email.getTo(),
            email.getSubject(), 
            email.getBody(),
            email.getFrom()
        );
    }
}
```

#### 3. Data format conversion
```java
// XML to JSON adapter
public class XMLToJSONAdapter implements DataProcessor {
    private JSONProcessor jsonProcessor;
    
    @Override
    public void process(String data) {
        String jsonData = convertXMLToJSON(data);
        jsonProcessor.process(jsonData);
    }
}
```

### ❌ Không nên sử dụng khi:

#### 1. Có thể modify existing interfaces
```java
// Nếu có thể sửa được, không cần adapter
public interface MediaPlayer {
    void play(String fileName);
    void playVlc(String fileName); // Just add new method
    void playMp4(String fileName);
}
```

#### 2. Simple interface differences
```java
// Quá đơn giản, không cần adapter
public class SimpleWrapper {
    public void newMethodName() {
        oldMethodName(); // Just delegate
    }
}
```

---

## Ví dụ thực tế

### Ví dụ 1: Database Adapter

```java
// Modern database interface
public interface Database {
    void connect(String connectionString);
    List<Record> query(String sql);
    void disconnect();
}

// Legacy database system
public class LegacyDatabase {
    public void establishConnection(String host, int port, String db) {
        System.out.println("Legacy: Connected to " + host + ":" + port + "/" + db);
    }
    
    public String[] executeQuery(String query) {
        System.out.println("Legacy: Executing " + query);
        return new String[]{"row1", "row2", "row3"};
    }
    
    public void closeConnection() {
        System.out.println("Legacy: Connection closed");
    }
}

// Adapter
public class LegacyDatabaseAdapter implements Database {
    private LegacyDatabase legacyDb;
    
    public LegacyDatabaseAdapter() {
        this.legacyDb = new LegacyDatabase();
    }
    
    @Override
    public void connect(String connectionString) {
        // Parse modern connection string
        String[] parts = connectionString.split(":");
        String host = parts[0];
        int port = Integer.parseInt(parts[1]);
        String database = parts[2];
        
        // Adapt to legacy format
        legacyDb.establishConnection(host, port, database);
    }
    
    @Override
    public List<Record> query(String sql) {
        String[] rawResults = legacyDb.executeQuery(sql);
        
        // Convert to modern format
        List<Record> records = new ArrayList<>();
        for (String raw : rawResults) {
            records.add(new Record(raw));
        }
        return records;
    }
    
    @Override
    public void disconnect() {
        legacyDb.closeConnection();
    }
}

// Usage
public class DatabaseClient {
    public void useDatabase() {
        Database db = new LegacyDatabaseAdapter();
        
        db.connect("localhost:3306:mydb");
        List<Record> results = db.query("SELECT * FROM users");
        
        for (Record record : results) {
            System.out.println("Record: " + record.getData());
        }
        
        db.disconnect();
    }
}
```

### Ví dụ 2: Payment Gateway Adapter

```java
// Standard payment interface
public interface PaymentProcessor {
    PaymentResult processPayment(double amount, String currency, String cardNumber);
}

// PayPal gateway (third-party)
public class PayPalGateway {
    public PayPalResponse makePayment(PayPalRequest request) {
        System.out.println("PayPal: Processing $" + request.getAmount());
        return new PayPalResponse("SUCCESS", "TXN123");
    }
}

// Stripe gateway (third-party)
public class StripeGateway {
    public StripeCharge charge(int amountInCents, String currency, String token) {
        System.out.println("Stripe: Charging " + amountInCents + " cents");
        return new StripeCharge("ch_123", "succeeded");
    }
}

// PayPal Adapter
public class PayPalAdapter implements PaymentProcessor {
    private PayPalGateway paypalGateway;
    
    public PayPalAdapter() {
        this.paypalGateway = new PayPalGateway();
    }
    
    @Override
    public PaymentResult processPayment(double amount, String currency, String cardNumber) {
        // Convert to PayPal format
        PayPalRequest request = new PayPalRequest();
        request.setAmount(amount);
        request.setCurrency(currency);
        request.setCardNumber(cardNumber);
        
        // Call PayPal
        PayPalResponse response = paypalGateway.makePayment(request);
        
        // Convert response back
        return new PaymentResult(
            response.getStatus().equals("SUCCESS"),
            response.getTransactionId()
        );
    }
}

// Stripe Adapter
public class StripeAdapter implements PaymentProcessor {
    private StripeGateway stripeGateway;
    
    public StripeAdapter() {
        this.stripeGateway = new StripeGateway();
    }
    
    @Override
    public PaymentResult processPayment(double amount, String currency, String cardNumber) {
        // Convert to Stripe format (cents)
        int amountInCents = (int) (amount * 100);
        String token = "tok_" + cardNumber.substring(cardNumber.length() - 4);
        
        // Call Stripe
        StripeCharge charge = stripeGateway.charge(amountInCents, currency, token);
        
        // Convert response back
        return new PaymentResult(
            charge.getStatus().equals("succeeded"),
            charge.getId()
        );
    }
}

// Payment service using adapters
public class PaymentService {
    private Map<String, PaymentProcessor> processors;
    
    public PaymentService() {
        processors = new HashMap<>();
        processors.put("paypal", new PayPalAdapter());
        processors.put("stripe", new StripeAdapter());
    }
    
    public PaymentResult processPayment(String gateway, double amount, String currency, String cardNumber) {
        PaymentProcessor processor = processors.get(gateway);
        if (processor != null) {
            return processor.processPayment(amount, currency, cardNumber);
        }
        throw new IllegalArgumentException("Unsupported gateway: " + gateway);
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Adapter Pattern** cho phép incompatible interfaces work together
2. **Object composition** thường preferred over inheritance
3. **Không thay đổi** existing code, chỉ thêm adapter layer
4. **Useful** cho legacy integration và third-party libraries

### So sánh với patterns khác
| Pattern | Mục đích | Khi dùng |
|---------|----------|----------|
| **Adapter** | Make incompatible interfaces work | Legacy integration |
| **Bridge** | Separate abstraction from implementation | Design flexibility |
| **Decorator** | Add behavior dynamically | Extend functionality |
| **Facade** | Simplify complex interface | Hide complexity |

### Best Practices
- **Prefer object adapter** over class adapter
- **Keep adapter simple** - chỉ convert interface
- **Consider performance** impact of conversion
- **Document** what is being adapted and why

---

**Tiếp theo:** [Bridge](bridge.md) - Tách abstraction khỏi implementation
