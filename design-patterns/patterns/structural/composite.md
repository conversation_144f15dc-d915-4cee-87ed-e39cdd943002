# Composite Pattern

> **Structural Pattern** - Tổ chức các đối tượng thành cấu trúc cây và xử lý chúng như đối tượng đơn lẻ

## 📋 <PERSON><PERSON><PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [Gi<PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [C<PERSON><PERSON> trúc](#cấu-trúc)
5. [Cách triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON><PERSON><PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Composite là một **structural design pattern** cho phép **tổ chức các đối tượng thành cấu trúc cây** đ<PERSON> biểu diễn mối quan hệ "part-whole" hierarchies. Pattern này cho phép client **xử lý các đối tượng đơn lẻ và composite một cách thống nhất**.

### Mục đích chính
- **Tạo tree structures** của objects
- **Xử lý uniform** individual objects và compositions
- **Đơn giản hóa** client code khi làm việc với complex hierarchies

### Tên gọi khác
- **Part-Whole Pattern**
- **Object Tree Pattern**

### Ví dụ thực tế
Giống như **file system**: một folder có thể chứa files và folders khác. Bạn có thể copy, move, delete một file đơn lẻ hoặc cả folder (với tất cả contents) bằng cùng một cách.

---

## Vấn đề

### Tình huống thực tế
Bạn đang xây dựng hệ thống quản lý đơn hàng với các sản phẩm đơn lẻ và packages (chứa nhiều sản phẩm):

```java
// Individual products
class Product {
    private String name;
    private double price;
    
    public double getPrice() {
        return price;
    }
}

// Package containing multiple items
class Package {
    private List<Product> products;
    private List<Package> subPackages;
    
    public double getPrice() {
        double total = 0;
        for (Product product : products) {
            total += product.getPrice();
        }
        for (Package pkg : subPackages) {
            total += pkg.getPrice(); // Recursive calculation
        }
        return total;
    }
}
```

### Vấn đề phát sinh

#### 1. Client code phức tạp
```java
public class OrderCalculator {
    public double calculateTotal(Object item) {
        if (item instanceof Product) {
            return ((Product) item).getPrice();
        } else if (item instanceof Package) {
            return ((Package) item).getPrice();
        }
        // Phải check type cho mỗi operation!
        throw new IllegalArgumentException("Unknown item type");
    }
    
    public void printDetails(Object item) {
        if (item instanceof Product) {
            // Handle product
        } else if (item instanceof Package) {
            // Handle package differently
        }
        // Type checking everywhere!
    }
}
```

#### 2. Khó mở rộng
```java
// Thêm loại item mới (như Service)
class Service {
    private double cost;
    
    public double getCost() { // Different method name!
        return cost;
    }
}

// Phải update tất cả client code
public double calculateTotal(Object item) {
    if (item instanceof Product) {
        return ((Product) item).getPrice();
    } else if (item instanceof Package) {
        return ((Package) item).getPrice();
    } else if (item instanceof Service) {
        return ((Service) item).getCost(); // Different interface!
    }
    // More type checking...
}
```

#### 3. Code duplication
```java
// Logic tương tự lặp lại ở nhiều nơi
public void applyDiscount(Object item, double discount) {
    if (item instanceof Product) {
        // Apply discount to product
    } else if (item instanceof Package) {
        // Apply discount to package
    }
}

public void printInvoice(Object item) {
    if (item instanceof Product) {
        // Print product details
    } else if (item instanceof Package) {
        // Print package details
    }
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Composite pattern đề xuất **làm việc với Products và Packages thông qua một giao diện chung** khai báo một phương thức để tính tổng giá.

### Cách hoạt động

#### 1. Tạo Component interface
```java
public interface OrderItem {
    double getPrice();
    void print();
}
```

#### 2. Leaf components (individual items)
```java
public class Product implements OrderItem {
    private String name;
    private double price;
    
    public Product(String name, double price) {
        this.name = name;
        this.price = price;
    }
    
    @Override
    public double getPrice() {
        return price;
    }
    
    @Override
    public void print() {
        System.out.println("Product: " + name + " - $" + price);
    }
}
```

#### 3. Composite components (containers)
```java
public class Package implements OrderItem {
    private String name;
    private List<OrderItem> items = new ArrayList<>();
    
    public Package(String name) {
        this.name = name;
    }
    
    public void add(OrderItem item) {
        items.add(item);
    }
    
    public void remove(OrderItem item) {
        items.remove(item);
    }
    
    @Override
    public double getPrice() {
        double total = 0;
        for (OrderItem item : items) {
            total += item.getPrice(); // Uniform interface!
        }
        return total;
    }
    
    @Override
    public void print() {
        System.out.println("Package: " + name);
        for (OrderItem item : items) {
            System.out.print("  ");
            item.print(); // Recursive printing
        }
    }
}
```

#### 4. Simplified client code
```java
public class OrderCalculator {
    public double calculateTotal(OrderItem item) {
        return item.getPrice(); // Same for both Product and Package!
    }
    
    public void printOrder(OrderItem item) {
        item.print(); // Same interface for all!
    }
}

// Usage
public class CompositeDemo {
    public static void main(String[] args) {
        // Create individual products
        OrderItem phone = new Product("iPhone", 999);
        OrderItem case = new Product("Phone Case", 29);
        OrderItem charger = new Product("Charger", 49);
        
        // Create package
        Package phonePackage = new Package("Phone Bundle");
        phonePackage.add(phone);
        phonePackage.add(case);
        phonePackage.add(charger);
        
        // Create larger package
        Package order = new Package("Complete Order");
        order.add(phonePackage);
        order.add(new Product("Headphones", 199));
        
        // Uniform treatment
        OrderCalculator calculator = new OrderCalculator();
        System.out.println("Total: $" + calculator.calculateTotal(order));
        calculator.printOrder(order);
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Component (interface)
├── + operation(): void
├── + add(Component): void
├── + remove(Component): void
├── + getChild(int): Component
│
Leaf implements Component
├── + operation(): void
│
Composite implements Component
├── - children: List<Component>
├── + operation(): void
├── + add(Component): void
├── + remove(Component): void
├── + getChild(int): Component
```

### Các thành phần chính

#### 1. Component
- **Vai trò:** Interface chung cho tất cả objects trong composition
- **Đặc điểm:** Khai báo operations cho cả simple và complex objects

#### 2. Leaf
- **Vai trò:** Represents end objects (không có children)
- **Đặc điểm:** Thực hiện actual work

#### 3. Composite
- **Vai trò:** Container có thể chứa children (Leaf hoặc Composite khác)
- **Đặc điểm:** Delegate work cho children và aggregate results

#### 4. Client
- **Vai trò:** Manipulate objects thông qua Component interface
- **Đặc điểm:** Không cần biết đang work với Leaf hay Composite

---

## Cách triển khai

### Bước 1: Định nghĩa Component interface
```java
public interface FileSystemItem {
    void display(String indent);
    long getSize();
    String getName();
}
```

### Bước 2: Implement Leaf classes
```java
public class File implements FileSystemItem {
    private String name;
    private long size;
    
    public File(String name, long size) {
        this.name = name;
        this.size = size;
    }
    
    @Override
    public void display(String indent) {
        System.out.println(indent + "📄 " + name + " (" + size + " bytes)");
    }
    
    @Override
    public long getSize() {
        return size;
    }
    
    @Override
    public String getName() {
        return name;
    }
}
```

### Bước 3: Implement Composite class
```java
public class Directory implements FileSystemItem {
    private String name;
    private List<FileSystemItem> items = new ArrayList<>();
    
    public Directory(String name) {
        this.name = name;
    }
    
    public void add(FileSystemItem item) {
        items.add(item);
    }
    
    public void remove(FileSystemItem item) {
        items.remove(item);
    }
    
    @Override
    public void display(String indent) {
        System.out.println(indent + "📁 " + name + "/");
        for (FileSystemItem item : items) {
            item.display(indent + "  ");
        }
    }
    
    @Override
    public long getSize() {
        long totalSize = 0;
        for (FileSystemItem item : items) {
            totalSize += item.getSize();
        }
        return totalSize;
    }
    
    @Override
    public String getName() {
        return name;
    }
}
```

### Bước 4: Client usage
```java
public class FileSystemDemo {
    public static void main(String[] args) {
        // Create files
        File readme = new File("README.md", 1024);
        File config = new File("config.json", 512);
        File main = new File("Main.java", 2048);
        File test = new File("Test.java", 1536);
        
        // Create directories
        Directory src = new Directory("src");
        src.add(main);
        
        Directory tests = new Directory("tests");
        tests.add(test);
        
        Directory project = new Directory("MyProject");
        project.add(readme);
        project.add(config);
        project.add(src);
        project.add(tests);
        
        // Display structure
        project.display("");
        
        // Calculate total size
        System.out.println("\nTotal project size: " + project.getSize() + " bytes");
        
        // Uniform operations
        printItemInfo(readme);    // Works with File
        printItemInfo(project);   // Works with Directory
    }
    
    public static void printItemInfo(FileSystemItem item) {
        System.out.println("Item: " + item.getName() + ", Size: " + item.getSize());
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Uniform treatment
```java
// Same code works for both individual items and collections
public void processItems(List<OrderItem> items) {
    for (OrderItem item : items) {
        double price = item.getPrice(); // Works for Product and Package
        item.print();                   // Works for both
    }
}
```

#### 2. Tuân thủ Open/Closed Principle
```java
// Có thể thêm component types mới
public class Service implements OrderItem {
    private String description;
    private double cost;
    
    @Override
    public double getPrice() {
        return cost;
    }
    
    @Override
    public void print() {
        System.out.println("Service: " + description + " - $" + cost);
    }
}

// Existing code không cần thay đổi
```

#### 3. Simplified client code
```java
// Client code rất đơn giản
public class ShoppingCart {
    private List<OrderItem> items = new ArrayList<>();
    
    public void addItem(OrderItem item) {
        items.add(item); // Same for Product, Package, Service
    }
    
    public double getTotal() {
        return items.stream()
                   .mapToDouble(OrderItem::getPrice)
                   .sum();
    }
}
```

#### 4. Easy to add new operations
```java
// Thêm operation mới cho tất cả components
public interface OrderItem {
    double getPrice();
    void print();
    void applyDiscount(double percentage); // New operation
}
```

### ❌ Nhược điểm

#### 1. Có thể làm design quá general
```java
// Interface có thể trở nên quá broad
public interface Component {
    void operation1();
    void operation2();
    void operation3(); // Not all components need all operations
}
```

#### 2. Khó restrict component types
```java
// Khó enforce rules như "Package chỉ chứa Products"
public class Package implements OrderItem {
    public void add(OrderItem item) {
        items.add(item); // Có thể add bất kỳ OrderItem nào
    }
}

// Muốn restrict nhưng khó implement
package.add(anotherPackage); // Có thể không mong muốn
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Cần implement tree-like structure
```java
// Organization hierarchy
public interface Employee {
    void showDetails();
    double getSalary();
}

public class Developer implements Employee { ... }

public class Manager implements Employee {
    private List<Employee> subordinates;
    
    public double getSalary() {
        // Manager salary + team budget
        return baseSalary + subordinates.stream()
                                      .mapToDouble(Employee::getSalary)
                                      .sum() * 0.1;
    }
}
```

#### 2. Muốn client ignore difference giữa individual và composite objects
```java
// Graphics system
public interface Graphic {
    void draw();
    void move(int x, int y);
}

public class Circle implements Graphic { ... }
public class Rectangle implements Graphic { ... }

public class CompositeGraphic implements Graphic {
    private List<Graphic> graphics;
    
    public void draw() {
        graphics.forEach(Graphic::draw); // Draw all children
    }
}
```

#### 3. Part-whole hierarchies
```java
// Menu system
public interface MenuComponent {
    void display();
    void select();
}

public class MenuItem implements MenuComponent { ... }

public class Menu implements MenuComponent {
    private List<MenuComponent> items;
    
    public void display() {
        items.forEach(MenuComponent::display);
    }
}
```

### ❌ Không nên sử dụng khi:

#### 1. Structure không phải tree-like
```java
// Simple list of items - không cần Composite
public class ShoppingList {
    private List<String> items; // Simple strings, no hierarchy
}
```

#### 2. Operations khác nhau cho different types
```java
// Nếu operations quá khác biệt
public interface Vehicle {
    void start(); // Car có thể start, nhưng Wheel thì không
}
```

---

## Ví dụ thực tế

### Ví dụ 1: GUI Component System

```java
// Component interface
public interface UIComponent {
    void render();
    void handleClick(int x, int y);
    int getWidth();
    int getHeight();
}

// Leaf components
public class Button implements UIComponent {
    private String text;
    private int x, y, width, height;
    
    public Button(String text, int x, int y, int width, int height) {
        this.text = text;
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    
    @Override
    public void render() {
        System.out.println("Rendering button: " + text + " at (" + x + "," + y + ")");
    }
    
    @Override
    public void handleClick(int clickX, int clickY) {
        if (clickX >= x && clickX <= x + width && 
            clickY >= y && clickY <= y + height) {
            System.out.println("Button '" + text + "' clicked!");
        }
    }
    
    @Override
    public int getWidth() { return width; }
    
    @Override
    public int getHeight() { return height; }
}

public class Label implements UIComponent {
    private String text;
    private int x, y;
    
    public Label(String text, int x, int y) {
        this.text = text;
        this.x = x;
        this.y = y;
    }
    
    @Override
    public void render() {
        System.out.println("Rendering label: " + text + " at (" + x + "," + y + ")");
    }
    
    @Override
    public void handleClick(int x, int y) {
        // Labels don't handle clicks
    }
    
    @Override
    public int getWidth() { return text.length() * 8; }
    
    @Override
    public int getHeight() { return 20; }
}

// Composite component
public class Panel implements UIComponent {
    private List<UIComponent> components = new ArrayList<>();
    private int x, y, width, height;
    private String title;
    
    public Panel(String title, int x, int y, int width, int height) {
        this.title = title;
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    
    public void add(UIComponent component) {
        components.add(component);
    }
    
    public void remove(UIComponent component) {
        components.remove(component);
    }
    
    @Override
    public void render() {
        System.out.println("Rendering panel: " + title + " at (" + x + "," + y + ")");
        for (UIComponent component : components) {
            component.render();
        }
    }
    
    @Override
    public void handleClick(int clickX, int clickY) {
        // Check if click is within panel bounds
        if (clickX >= x && clickX <= x + width && 
            clickY >= y && clickY <= y + height) {
            
            // Delegate to child components
            for (UIComponent component : components) {
                component.handleClick(clickX, clickY);
            }
        }
    }
    
    @Override
    public int getWidth() { return width; }
    
    @Override
    public int getHeight() { return height; }
}

// Usage
public class GUIDemo {
    public static void main(String[] args) {
        // Create individual components
        Button okButton = new Button("OK", 10, 10, 80, 30);
        Button cancelButton = new Button("Cancel", 100, 10, 80, 30);
        Label titleLabel = new Label("Settings", 10, 50);
        
        // Create panels
        Panel buttonPanel = new Panel("Buttons", 0, 0, 200, 50);
        buttonPanel.add(okButton);
        buttonPanel.add(cancelButton);
        
        Panel mainPanel = new Panel("Main", 0, 0, 300, 200);
        mainPanel.add(titleLabel);
        mainPanel.add(buttonPanel);
        
        // Render entire UI
        mainPanel.render();
        
        System.out.println("\n--- Simulating clicks ---");
        
        // Simulate clicks
        mainPanel.handleClick(50, 25);  // Click OK button
        mainPanel.handleClick(150, 25); // Click Cancel button
        mainPanel.handleClick(250, 100); // Click outside
    }
}
```

### Ví dụ 2: Mathematical Expression Evaluator

```java
// Component interface
public interface Expression {
    double evaluate();
    String toString();
}

// Leaf - Number
public class Number implements Expression {
    private double value;
    
    public Number(double value) {
        this.value = value;
    }
    
    @Override
    public double evaluate() {
        return value;
    }
    
    @Override
    public String toString() {
        return String.valueOf(value);
    }
}

// Composite - Binary Operation
public class BinaryOperation implements Expression {
    private Expression left;
    private Expression right;
    private String operator;
    
    public BinaryOperation(Expression left, String operator, Expression right) {
        this.left = left;
        this.operator = operator;
        this.right = right;
    }
    
    @Override
    public double evaluate() {
        double leftValue = left.evaluate();
        double rightValue = right.evaluate();
        
        switch (operator) {
            case "+": return leftValue + rightValue;
            case "-": return leftValue - rightValue;
            case "*": return leftValue * rightValue;
            case "/": return leftValue / rightValue;
            default: throw new IllegalArgumentException("Unknown operator: " + operator);
        }
    }
    
    @Override
    public String toString() {
        return "(" + left.toString() + " " + operator + " " + right.toString() + ")";
    }
}

// Composite - Unary Operation
public class UnaryOperation implements Expression {
    private Expression operand;
    private String operator;
    
    public UnaryOperation(String operator, Expression operand) {
        this.operator = operator;
        this.operand = operand;
    }
    
    @Override
    public double evaluate() {
        double value = operand.evaluate();
        
        switch (operator) {
            case "-": return -value;
            case "sqrt": return Math.sqrt(value);
            case "sin": return Math.sin(value);
            case "cos": return Math.cos(value);
            default: throw new IllegalArgumentException("Unknown operator: " + operator);
        }
    }
    
    @Override
    public String toString() {
        return operator + "(" + operand.toString() + ")";
    }
}

// Usage
public class ExpressionDemo {
    public static void main(String[] args) {
        // Build expression: (5 + 3) * sqrt(16) - 2
        Expression five = new Number(5);
        Expression three = new Number(3);
        Expression sixteen = new Number(16);
        Expression two = new Number(2);
        
        Expression sum = new BinaryOperation(five, "+", three);
        Expression sqrt = new UnaryOperation("sqrt", sixteen);
        Expression product = new BinaryOperation(sum, "*", sqrt);
        Expression result = new BinaryOperation(product, "-", two);
        
        System.out.println("Expression: " + result.toString());
        System.out.println("Result: " + result.evaluate());
        
        // More complex: sin(3.14159 / 2) + cos(0)
        Expression pi = new Number(3.14159);
        Expression piDiv2 = new BinaryOperation(pi, "/", new Number(2));
        Expression sinPiDiv2 = new UnaryOperation("sin", piDiv2);
        Expression cos0 = new UnaryOperation("cos", new Number(0));
        Expression complex = new BinaryOperation(sinPiDiv2, "+", cos0);
        
        System.out.println("\nComplex expression: " + complex.toString());
        System.out.println("Result: " + complex.evaluate());
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Composite Pattern** cho phép treat individual objects và compositions uniformly
2. **Tree structures** là use case chính của pattern
3. **Simplifies client code** bằng cách eliminate type checking
4. **Recursive operations** được handle naturally

### So sánh với patterns khác
| Pattern | Structure | Purpose |
|---------|-----------|---------|
| **Composite** | Tree hierarchy | Treat individual/group uniformly |
| **Decorator** | Linear chain | Add behavior dynamically |
| **Chain of Responsibility** | Linear chain | Pass request along chain |

### Best Practices
- **Keep Component interface simple** và focused
- **Consider performance** của recursive operations
- **Handle edge cases** như empty composites
- **Provide clear documentation** về expected tree structure

---

**Tiếp theo:** [Decorator](decorator.md) - Thêm chức năng động cho đối tượng
