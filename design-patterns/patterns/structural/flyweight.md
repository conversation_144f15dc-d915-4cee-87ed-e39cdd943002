# Flyweight Pattern

> **Structural Pattern** - Tiết kiệm bộ nhớ bằng cách chia sẻ hiệu quả dữ liệu chung giữa nhiều đối tượng tương tự

## 📋 <PERSON>ục lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [Gi<PERSON>i pháp](#gi<PERSON>i-pháp)
4. [Cấu trúc](#cấu-trúc)
5. [Cách triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nh<PERSON>ợ<PERSON>-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [Ví dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Flyweight là một **structural design pattern** cho phép **fit nhiều objects hơn vào RAM có sẵn bằng cách chia sẻ hiệu quả các phần state chung** giữa nhiều objects thay vì lưu trữ tất cả data trong mỗi object.

### Mục đích chính
- **Minimize memory usage** khi làm việc với large numbers of objects
- **Share common data** giữa multiple objects
- **Separate intrinsic và extrinsic state**

### Tên gọi khác
- **Cache Pattern** (trong một số contexts)
- **Shared Object Pattern**

### Ví dụ thực tế
Giống như **font rendering**: thay vì mỗi ký tự 'A' trong document lưu trữ font data riêng, tất cả ký tự 'A' chia sẻ cùng một font object và chỉ lưu position, size riêng biệt.

---

## Vấn đề

### Tình huống thực tế
Bạn đang phát triển game bắn súng với hàng triệu particles (đạn, mảnh vỡ, khói):

```java
public class Particle {
    private int x, y, z;           // Position (extrinsic)
    private int velocityX, velocityY, velocityZ; // Velocity (extrinsic)
    private Color color;           // Color (intrinsic)
    private Sprite sprite;         // Sprite image (intrinsic)
    private int size;              // Size (extrinsic)
    private double rotation;       // Rotation (extrinsic)
    
    // Methods...
}
```

### Vấn đề phát sinh

#### 1. Memory explosion
```java
// 1 million particles in game
List<Particle> particles = new ArrayList<>();

for (int i = 0; i < 1_000_000; i++) {
    particles.add(new Particle(
        random.nextInt(1000), random.nextInt(1000), random.nextInt(1000), // Position
        random.nextInt(10), random.nextInt(10), random.nextInt(10),       // Velocity
        Color.RED,        // Same color for many particles
        bulletSprite,     // Same sprite for many particles
        5,                // Same size for many particles
        0.0               // Same rotation for many particles
    ));
}

// Memory usage: 1M particles × ~100 bytes each = ~100MB
// But most data is duplicated!
```

#### 2. Redundant data storage
```java
// Many particles share same properties
Particle bullet1 = new Particle(10, 20, 30, 1, 0, 0, Color.RED, bulletSprite, 5, 0.0);
Particle bullet2 = new Particle(15, 25, 35, 1, 0, 0, Color.RED, bulletSprite, 5, 0.0);
Particle bullet3 = new Particle(20, 30, 40, 1, 0, 0, Color.RED, bulletSprite, 5, 0.0);

// Color.RED, bulletSprite, size=5, rotation=0.0 duplicated in each object!
// Only position and velocity are different
```

#### 3. Performance issues
```java
public class GameRenderer {
    public void render(List<Particle> particles) {
        for (Particle particle : particles) {
            // Loading same sprite data repeatedly
            Graphics.loadSprite(particle.getSprite()); // Expensive operation repeated
            Graphics.setColor(particle.getColor());    // Same color loaded many times
            Graphics.drawAt(particle.getX(), particle.getY());
        }
    }
}
```

#### 4. Cache misses
```java
// Large objects don't fit well in CPU cache
// Accessing 1M large Particle objects causes many cache misses
// Performance degrades significantly
```

---

## Giải pháp

### Ý tưởng cốt lõi
Flyweight pattern đề xuất **ngừng lưu trữ extrinsic state bên trong object**. Thay vào đó, pass state này vào specific methods mà cần nó. Chỉ intrinsic state được giữ bên trong object, cho phép reuse nó trong nhiều contexts khác nhau.

### Intrinsic vs Extrinsic State

#### Intrinsic State (Shared)
- **Không thay đổi** giữa contexts
- **Có thể chia sẻ** giữa multiple objects
- **Stored trong flyweight**

#### Extrinsic State (Context-specific)
- **Thay đổi** giữa contexts
- **Unique** cho mỗi object
- **Passed as parameters**

### Cách hoạt động

#### 1. Flyweight interface
```java
public interface ParticleFlyweight {
    void render(int x, int y, int z, int velocityX, int velocityY, int velocityZ, 
                int size, double rotation, Graphics graphics);
}
```

#### 2. Concrete Flyweight
```java
public class BulletParticle implements ParticleFlyweight {
    private final Color color;      // Intrinsic state
    private final Sprite sprite;    // Intrinsic state
    
    public BulletParticle(Color color, Sprite sprite) {
        this.color = color;
        this.sprite = sprite;
    }
    
    @Override
    public void render(int x, int y, int z, int velocityX, int velocityY, int velocityZ,
                      int size, double rotation, Graphics graphics) {
        // Use intrinsic state
        graphics.setColor(color);
        graphics.loadSprite(sprite);
        
        // Use extrinsic state passed as parameters
        graphics.drawAt(x, y, z);
        graphics.setSize(size);
        graphics.setRotation(rotation);
    }
}
```

#### 3. Flyweight Factory
```java
public class ParticleFactory {
    private static final Map<String, ParticleFlyweight> flyweights = new HashMap<>();
    
    public static ParticleFlyweight getParticle(String type, Color color, Sprite sprite) {
        String key = type + "_" + color.toString() + "_" + sprite.getName();
        
        ParticleFlyweight flyweight = flyweights.get(key);
        if (flyweight == null) {
            switch (type) {
                case "bullet":
                    flyweight = new BulletParticle(color, sprite);
                    break;
                case "explosion":
                    flyweight = new ExplosionParticle(color, sprite);
                    break;
                // Add more types...
            }
            flyweights.put(key, flyweight);
            System.out.println("Created new flyweight: " + key);
        }
        
        return flyweight;
    }
    
    public static int getCreatedFlyweightsCount() {
        return flyweights.size();
    }
}
```

#### 4. Context class
```java
public class Particle {
    // Extrinsic state only
    private int x, y, z;
    private int velocityX, velocityY, velocityZ;
    private int size;
    private double rotation;
    private ParticleFlyweight flyweight; // Reference to shared flyweight
    
    public Particle(int x, int y, int z, int velocityX, int velocityY, int velocityZ,
                   int size, double rotation, ParticleFlyweight flyweight) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.velocityX = velocityX;
        this.velocityY = velocityY;
        this.velocityZ = velocityZ;
        this.size = size;
        this.rotation = rotation;
        this.flyweight = flyweight;
    }
    
    public void render(Graphics graphics) {
        flyweight.render(x, y, z, velocityX, velocityY, velocityZ, size, rotation, graphics);
    }
    
    public void update() {
        x += velocityX;
        y += velocityY;
        z += velocityZ;
    }
}
```

#### 5. Usage
```java
public class Game {
    private List<Particle> particles = new ArrayList<>();
    
    public void createParticles() {
        // Create 1 million particles
        for (int i = 0; i < 1_000_000; i++) {
            ParticleFlyweight flyweight = ParticleFactory.getParticle(
                "bullet", Color.RED, SpriteLoader.load("bullet.png")
            );
            
            particles.add(new Particle(
                random.nextInt(1000), random.nextInt(1000), random.nextInt(1000),
                random.nextInt(10), random.nextInt(10), random.nextInt(10),
                5, 0.0, flyweight
            ));
        }
        
        System.out.println("Created " + particles.size() + " particles");
        System.out.println("Using only " + ParticleFactory.getCreatedFlyweightsCount() + " flyweights");
        // Output: Created 1000000 particles using only 1 flyweight!
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Flyweight (interface)
├── + operation(extrinsicState): void
│
ConcreteFlyweight implements Flyweight
├── - intrinsicState: Object
├── + operation(extrinsicState): void
│
FlyweightFactory
├── - flyweights: Map<String, Flyweight>
├── + getFlyweight(key): Flyweight
│
Context
├── - extrinsicState: Object
├── - flyweight: Flyweight
├── + operation(): void
│
Client
├── uses → FlyweightFactory
├── uses → Context
```

### Các thành phần chính

#### 1. Flyweight Interface
- **Vai trò:** Declares methods qua đó flyweights có thể receive và act on extrinsic state
- **Đặc điểm:** Methods accept extrinsic state as parameters

#### 2. Concrete Flyweight
- **Vai trò:** Implements Flyweight interface và stores intrinsic state
- **Đặc điểm:** Must be shareable và independent of context

#### 3. Flyweight Factory
- **Vai trò:** Manages flyweight instances và ensures sharing
- **Đặc điểm:** Returns existing flyweights hoặc creates new ones

#### 4. Context
- **Vai trò:** Contains extrinsic state và reference to flyweight
- **Đặc điểm:** Delegates operations to flyweight

---

## Cách triển khai

### Bước 1: Identify intrinsic và extrinsic state
```java
// Character in text editor
// Intrinsic: font, size, style (shared by many characters)
// Extrinsic: position, color (unique per character)
```

### Bước 2: Flyweight interface
```java
public interface CharacterFlyweight {
    void render(int x, int y, Color color, Graphics graphics);
}
```

### Bước 3: Concrete Flyweight
```java
public class Character implements CharacterFlyweight {
    private final char character;  // Intrinsic
    private final String font;     // Intrinsic
    private final int fontSize;    // Intrinsic
    
    public Character(char character, String font, int fontSize) {
        this.character = character;
        this.font = font;
        this.fontSize = fontSize;
    }
    
    @Override
    public void render(int x, int y, Color color, Graphics graphics) {
        graphics.setFont(font, fontSize);
        graphics.setColor(color);
        graphics.drawChar(character, x, y);
    }
}
```

### Bước 4: Factory
```java
public class CharacterFactory {
    private static final Map<String, CharacterFlyweight> characters = new HashMap<>();
    
    public static CharacterFlyweight getCharacter(char ch, String font, int fontSize) {
        String key = ch + "_" + font + "_" + fontSize;
        
        CharacterFlyweight character = characters.get(key);
        if (character == null) {
            character = new Character(ch, font, fontSize);
            characters.put(key, character);
        }
        
        return character;
    }
}
```

### Bước 5: Context
```java
public class CharacterContext {
    private int x, y;              // Extrinsic state
    private Color color;           // Extrinsic state
    private CharacterFlyweight flyweight; // Reference to flyweight
    
    public CharacterContext(int x, int y, Color color, CharacterFlyweight flyweight) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.flyweight = flyweight;
    }
    
    public void render(Graphics graphics) {
        flyweight.render(x, y, color, graphics);
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Significant memory savings
```java
// Before Flyweight: 1M objects × 100 bytes = 100MB
// After Flyweight: 1M contexts × 20 bytes + 10 flyweights × 50 bytes = 20MB + 500 bytes ≈ 20MB
// Memory reduction: 80%!
```

#### 2. Better cache performance
```java
// Smaller context objects fit better in CPU cache
// Improved iteration performance over large collections
```

#### 3. Centralized state management
```java
// Intrinsic state managed centrally in factory
// Easier to update shared properties
public void updateAllBulletSprites(Sprite newSprite) {
    // Update flyweight, affects all bullets using it
    bulletFlyweight.setSprite(newSprite);
}
```

### ❌ Nhược điểm

#### 1. Increased complexity
```java
// More complex object creation
CharacterFlyweight flyweight = CharacterFactory.getCharacter('A', "Arial", 12);
CharacterContext context = new CharacterContext(10, 20, Color.BLACK, flyweight);

// vs simple:
Character character = new Character('A', "Arial", 12, 10, 20, Color.BLACK);
```

#### 2. CPU overhead
```java
// Method calls with many parameters
flyweight.render(x, y, z, velocityX, velocityY, velocityZ, size, rotation, graphics);
// vs direct field access:
// graphics.draw(this.sprite, this.x, this.y);
```

#### 3. Potential memory leaks
```java
// Factory holds references to all flyweights
// May prevent garbage collection if not managed properly
public class FlyweightFactory {
    private static Map<String, Flyweight> cache = new HashMap<>();
    
    // Need cleanup mechanism for unused flyweights
    public static void cleanup() {
        cache.clear(); // Manual cleanup required
    }
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Large number of similar objects
```java
// Game với millions of particles, bullets, enemies
// Text editor với thousands of characters
// Graphics application với many shapes
```

#### 2. Storage cost cao
```java
// Mobile applications với limited memory
// Embedded systems với memory constraints
// Large-scale applications với memory pressure
```

#### 3. Object state có thể chia thành intrinsic/extrinsic
```java
// Clear separation possible
// Intrinsic state: font, sprite, color scheme
// Extrinsic state: position, velocity, individual properties
```

### ❌ Không nên sử dụng khi:

#### 1. Few objects
```java
// Overkill for small number of objects
List<User> users; // Only 100 users - no need for flyweight
```

#### 2. Most state is extrinsic
```java
// If objects have little shared state
public class UniqueDocument {
    private String uniqueContent;    // Different for each
    private String uniqueTitle;      // Different for each
    private Date uniqueCreatedDate;  // Different for each
    // No shared state to optimize
}
```

---

## Ví dụ thực tế

### Ví dụ 1: Text Editor Character Rendering

```java
// Flyweight interface
public interface CharacterFlyweight {
    void render(int x, int y, Color color, boolean bold, boolean italic, Graphics graphics);
}

// Concrete flyweight
public class CharacterGlyph implements CharacterFlyweight {
    private final char character;     // Intrinsic
    private final String fontFamily;  // Intrinsic
    private final int fontSize;       // Intrinsic
    
    public CharacterGlyph(char character, String fontFamily, int fontSize) {
        this.character = character;
        this.fontFamily = fontFamily;
        this.fontSize = fontSize;
        System.out.println("Created glyph for '" + character + "' in " + fontFamily + " " + fontSize + "pt");
    }
    
    @Override
    public void render(int x, int y, Color color, boolean bold, boolean italic, Graphics graphics) {
        // Use intrinsic state
        graphics.setFont(fontFamily, fontSize);
        
        // Apply extrinsic formatting
        if (bold) graphics.setBold(true);
        if (italic) graphics.setItalic(true);
        graphics.setColor(color);
        
        // Render at extrinsic position
        graphics.drawCharacter(character, x, y);
        
        // Reset formatting
        graphics.setBold(false);
        graphics.setItalic(false);
    }
}

// Factory
public class FontFactory {
    private static final Map<String, CharacterFlyweight> glyphs = new HashMap<>();
    
    public static CharacterFlyweight getGlyph(char character, String fontFamily, int fontSize) {
        String key = character + "_" + fontFamily + "_" + fontSize;
        
        CharacterFlyweight glyph = glyphs.get(key);
        if (glyph == null) {
            glyph = new CharacterGlyph(character, fontFamily, fontSize);
            glyphs.put(key, glyph);
        }
        
        return glyph;
    }
    
    public static void printStatistics() {
        System.out.println("Total unique glyphs created: " + glyphs.size());
        System.out.println("Memory saved by sharing: " + 
                          (glyphs.size() > 0 ? "Significant" : "None"));
    }
}

// Context
public class TextCharacter {
    private int x, y;                    // Extrinsic: position
    private Color color;                 // Extrinsic: color
    private boolean bold, italic;        // Extrinsic: formatting
    private CharacterFlyweight glyph;    // Reference to flyweight
    
    public TextCharacter(int x, int y, Color color, boolean bold, boolean italic, 
                        CharacterFlyweight glyph) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.bold = bold;
        this.italic = italic;
        this.glyph = glyph;
    }
    
    public void render(Graphics graphics) {
        glyph.render(x, y, color, bold, italic, graphics);
    }
    
    // Getters and setters for extrinsic state
    public void setPosition(int x, int y) {
        this.x = x;
        this.y = y;
    }
    
    public void setColor(Color color) {
        this.color = color;
    }
    
    public void setBold(boolean bold) {
        this.bold = bold;
    }
    
    public void setItalic(boolean italic) {
        this.italic = italic;
    }
}

// Document
public class Document {
    private List<TextCharacter> characters = new ArrayList<>();
    
    public void addText(String text, int startX, int startY, String fontFamily, 
                       int fontSize, Color color, boolean bold, boolean italic) {
        int x = startX;
        int y = startY;
        
        for (char ch : text.toCharArray()) {
            if (ch == '\n') {
                x = startX;
                y += fontSize + 5; // Line spacing
                continue;
            }
            
            CharacterFlyweight glyph = FontFactory.getGlyph(ch, fontFamily, fontSize);
            TextCharacter character = new TextCharacter(x, y, color, bold, italic, glyph);
            characters.add(character);
            
            x += fontSize; // Character spacing (simplified)
        }
    }
    
    public void render(Graphics graphics) {
        System.out.println("Rendering document with " + characters.size() + " characters...");
        for (TextCharacter character : characters) {
            character.render(graphics);
        }
    }
    
    public void changeColor(Color newColor) {
        for (TextCharacter character : characters) {
            character.setColor(newColor);
        }
    }
}

// Usage
public class TextEditorDemo {
    public static void main(String[] args) {
        Document document = new Document();
        Graphics graphics = new MockGraphics();
        
        // Add different text with various formatting
        document.addText("Hello World!", 10, 10, "Arial", 12, Color.BLACK, false, false);
        document.addText("Bold Text", 10, 30, "Arial", 12, Color.RED, true, false);
        document.addText("Italic Text", 10, 50, "Times", 14, Color.BLUE, false, true);
        document.addText("Hello World!", 10, 70, "Arial", 12, Color.GREEN, false, false);
        
        // Render document
        document.render(graphics);
        
        // Show flyweight statistics
        FontFactory.printStatistics();
        
        // Demonstrate memory efficiency
        System.out.println("\nMemory efficiency demonstration:");
        System.out.println("Without flyweight: Each character would store font info");
        System.out.println("With flyweight: Font info shared among same characters");
        
        // Change all text color
        System.out.println("\nChanging all text to purple...");
        document.changeColor(Color.MAGENTA);
    }
}

// Mock Graphics class for demonstration
class MockGraphics implements Graphics {
    @Override
    public void setFont(String fontFamily, int fontSize) {
        // Mock implementation
    }
    
    @Override
    public void setColor(Color color) {
        // Mock implementation
    }
    
    @Override
    public void setBold(boolean bold) {
        // Mock implementation
    }
    
    @Override
    public void setItalic(boolean italic) {
        // Mock implementation
    }
    
    @Override
    public void drawCharacter(char character, int x, int y) {
        System.out.println("Drawing '" + character + "' at (" + x + ", " + y + ")");
    }
}

interface Graphics {
    void setFont(String fontFamily, int fontSize);
    void setColor(Color color);
    void setBold(boolean bold);
    void setItalic(boolean italic);
    void drawCharacter(char character, int x, int y);
}

enum Color {
    BLACK, RED, BLUE, GREEN, MAGENTA
}
```

### Ví dụ 2: Game Forest Simulation

```java
// Flyweight interface
public interface TreeType {
    void render(int x, int y, int age, double health, Graphics graphics);
}

// Concrete flyweight
public class TreeTypeImpl implements TreeType {
    private final String name;        // Intrinsic
    private final Color color;        // Intrinsic
    private final Sprite sprite;      // Intrinsic
    private final String description; // Intrinsic
    
    public TreeTypeImpl(String name, Color color, Sprite sprite, String description) {
        this.name = name;
        this.color = color;
        this.sprite = sprite;
        this.description = description;
        System.out.println("Created tree type: " + name);
    }
    
    @Override
    public void render(int x, int y, int age, double health, Graphics graphics) {
        // Use intrinsic state
        graphics.setColor(adjustColorForHealth(color, health));
        graphics.setSprite(adjustSpriteForAge(sprite, age));
        
        // Render at extrinsic position
        graphics.drawSprite(x, y);
        
        // Optional: show tree info
        if (graphics.isDebugMode()) {
            graphics.drawText(name + " (Age: " + age + ", Health: " + 
                            String.format("%.1f", health * 100) + "%)", x, y - 20);
        }
    }
    
    private Color adjustColorForHealth(Color baseColor, double health) {
        // Adjust color based on health (extrinsic state)
        if (health < 0.3) return Color.BROWN; // Dying
        if (health < 0.7) return Color.YELLOW; // Sick
        return baseColor; // Healthy
    }
    
    private Sprite adjustSpriteForAge(Sprite baseSprite, int age) {
        // Adjust sprite size based on age (extrinsic state)
        if (age < 5) return baseSprite.scale(0.5); // Young
        if (age < 20) return baseSprite.scale(0.8); // Mature
        return baseSprite; // Old
    }
    
    public String getName() { return name; }
    public String getDescription() { return description; }
}

// Factory
public class TreeTypeFactory {
    private static final Map<String, TreeType> treeTypes = new HashMap<>();
    
    static {
        // Pre-create common tree types
        createTreeType("Oak", Color.GREEN, SpriteLoader.load("oak.png"), "Strong deciduous tree");
        createTreeType("Pine", Color.DARK_GREEN, SpriteLoader.load("pine.png"), "Evergreen conifer");
        createTreeType("Birch", Color.LIGHT_GREEN, SpriteLoader.load("birch.png"), "Fast-growing deciduous");
        createTreeType("Maple", Color.RED, SpriteLoader.load("maple.png"), "Beautiful autumn colors");
    }
    
    public static TreeType getTreeType(String name) {
        TreeType treeType = treeTypes.get(name);
        if (treeType == null) {
            throw new IllegalArgumentException("Unknown tree type: " + name);
        }
        return treeType;
    }
    
    private static void createTreeType(String name, Color color, Sprite sprite, String description) {
        treeTypes.put(name, new TreeTypeImpl(name, color, sprite, description));
    }
    
    public static void printStatistics() {
        System.out.println("Tree types created: " + treeTypes.size());
        for (String type : treeTypes.keySet()) {
            System.out.println("- " + type);
        }
    }
}

// Context
public class Tree {
    private int x, y;           // Extrinsic: position
    private int age;            // Extrinsic: age in years
    private double health;      // Extrinsic: health 0.0-1.0
    private TreeType treeType;  // Reference to flyweight
    
    public Tree(int x, int y, int age, double health, TreeType treeType) {
        this.x = x;
        this.y = y;
        this.age = age;
        this.health = health;
        this.treeType = treeType;
    }
    
    public void render(Graphics graphics) {
        treeType.render(x, y, age, health, graphics);
    }
    
    public void grow() {
        age++;
        // Health might change with age
        if (age > 50) {
            health = Math.max(0.1, health - 0.01); // Gradual decline
        }
    }
    
    public void damage(double amount) {
        health = Math.max(0.0, health - amount);
    }
    
    public void heal(double amount) {
        health = Math.min(1.0, health + amount);
    }
    
    // Getters
    public int getX() { return x; }
    public int getY() { return y; }
    public int getAge() { return age; }
    public double getHealth() { return health; }
    public boolean isAlive() { return health > 0.0; }
}

// Forest
public class Forest {
    private List<Tree> trees = new ArrayList<>();
    
    public void plantTree(int x, int y, String treeTypeName) {
        TreeType treeType = TreeTypeFactory.getTreeType(treeTypeName);
        Tree tree = new Tree(x, y, 0, 1.0, treeType); // New tree: age 0, full health
        trees.add(tree);
    }
    
    public void plantRandomForest(int numTrees, int width, int height) {
        Random random = new Random();
        String[] treeTypes = {"Oak", "Pine", "Birch", "Maple"};
        
        for (int i = 0; i < numTrees; i++) {
            int x = random.nextInt(width);
            int y = random.nextInt(height);
            String type = treeTypes[random.nextInt(treeTypes.length)];
            
            plantTree(x, y, type);
        }
    }
    
    public void simulateYear() {
        System.out.println("Simulating one year of forest growth...");
        for (Tree tree : trees) {
            tree.grow();
            
            // Random events
            if (Math.random() < 0.1) { // 10% chance of damage
                tree.damage(0.2);
                System.out.println("Tree at (" + tree.getX() + ", " + tree.getY() + ") damaged!");
            }
            
            if (Math.random() < 0.05) { // 5% chance of healing
                tree.heal(0.1);
            }
        }
        
        // Remove dead trees
        trees.removeIf(tree -> !tree.isAlive());
    }
    
    public void render(Graphics graphics) {
        System.out.println("Rendering forest with " + trees.size() + " trees...");
        for (Tree tree : trees) {
            tree.render(graphics);
        }
    }
    
    public void printStatistics() {
        Map<String, Integer> typeCounts = new HashMap<>();
        int totalAge = 0;
        double totalHealth = 0;
        
        for (Tree tree : trees) {
            // Note: We can't directly access tree type name from tree
            // This is a limitation of flyweight pattern
            totalAge += tree.getAge();
            totalHealth += tree.getHealth();
        }
        
        System.out.println("Forest Statistics:");
        System.out.println("- Total trees: " + trees.size());
        System.out.println("- Average age: " + (trees.size() > 0 ? totalAge / trees.size() : 0));
        System.out.println("- Average health: " + 
                          (trees.size() > 0 ? String.format("%.1f%%", (totalHealth / trees.size()) * 100) : "0%"));
    }
}

// Usage
public class ForestSimulationDemo {
    public static void main(String[] args) {
        Forest forest = new Forest();
        Graphics graphics = new MockGraphics();
        
        // Plant a forest
        System.out.println("Planting forest...");
        forest.plantRandomForest(10000, 1000, 1000); // 10,000 trees!
        
        // Show flyweight efficiency
        TreeTypeFactory.printStatistics();
        System.out.println("Memory efficiency: 10,000 trees using only 4 tree type objects!");
        
        // Simulate forest growth
        for (int year = 1; year <= 5; year++) {
            System.out.println("\n=== Year " + year + " ===");
            forest.simulateYear();
            forest.printStatistics();
        }
        
        // Render forest
        System.out.println("\nRendering forest...");
        graphics.setDebugMode(false); // Turn off debug info for performance
        forest.render(graphics);
        
        System.out.println("\nFlyweight pattern benefits:");
        System.out.println("- Memory usage: Minimal (shared tree type data)");
        System.out.println("- Performance: Good (small context objects)");
        System.out.println("- Scalability: Excellent (can handle millions of trees)");
    }
}

// Mock classes for demonstration
class MockGraphics implements Graphics {
    private boolean debugMode = false;
    
    public void setDebugMode(boolean debug) { this.debugMode = debug; }
    public boolean isDebugMode() { return debugMode; }
    
    @Override
    public void setColor(Color color) { /* Mock */ }
    
    @Override
    public void setSprite(Sprite sprite) { /* Mock */ }
    
    @Override
    public void drawSprite(int x, int y) {
        if (debugMode) {
            System.out.println("Drawing sprite at (" + x + ", " + y + ")");
        }
    }
    
    @Override
    public void drawText(String text, int x, int y) {
        System.out.println("Text at (" + x + ", " + y + "): " + text);
    }
}

interface Graphics {
    void setColor(Color color);
    void setSprite(Sprite sprite);
    void drawSprite(int x, int y);
    void drawText(String text, int x, int y);
    void setDebugMode(boolean debug);
    boolean isDebugMode();
}

class Sprite {
    private String filename;
    private double scale = 1.0;
    
    public Sprite(String filename) { this.filename = filename; }
    
    public Sprite scale(double factor) {
        Sprite scaled = new Sprite(filename);
        scaled.scale = this.scale * factor;
        return scaled;
    }
    
    public String getFilename() { return filename; }
    public double getScale() { return scale; }
}

class SpriteLoader {
    public static Sprite load(String filename) {
        return new Sprite(filename);
    }
}

enum Color {
    GREEN, DARK_GREEN, LIGHT_GREEN, RED, BROWN, YELLOW
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Flyweight Pattern** tiết kiệm memory bằng sharing common data
2. **Intrinsic state** được shared, **extrinsic state** passed as parameters
3. **Factory manages** flyweight instances để ensure sharing
4. **Trade-off:** Memory savings vs increased complexity

### So sánh với patterns khác
| Pattern | Purpose | Memory Impact |
|---------|---------|---------------|
| **Flyweight** | Share common data | Reduce memory usage |
| **Singleton** | Single instance | Minimal memory impact |
| **Prototype** | Clone objects | May increase memory |
| **Object Pool** | Reuse expensive objects | Control memory allocation |

### Best Practices
- **Identify** intrinsic vs extrinsic state carefully
- **Use factory** để manage flyweight instances
- **Consider** performance trade-offs
- **Monitor** memory usage improvements
- **Document** shared state clearly

---

**Tiếp theo:** [Proxy](proxy.md) - Kiểm soát quyền truy cập đến đối tượng khác
