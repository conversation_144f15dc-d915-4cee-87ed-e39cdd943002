# Facade Pattern

> **Structural Pattern** - <PERSON><PERSON> cấp giao diện đơn giản cho một hệ thống con phức tạp

## 📋 <PERSON><PERSON><PERSON> lụ<PERSON>

1. [Tổng quan](#tổng-quan)
2. [V<PERSON>n đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#gi<PERSON>i-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nhược-điểm)
7. [<PERSON><PERSON> nào sử dụng](#khi-nào-sử-dụng)
8. [<PERSON><PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Facade là một **structural design pattern** cung cấp **giao diện đơn giản cho một thư viện, framework, hoặc bất kỳ tập hợp phức tạp nào của các lớp**.

### <PERSON><PERSON><PERSON> đích chính
- **Đơn gi<PERSON>n hóa** interface phức tạp
- **Ẩn complexity** của subsystem
- **Cung cấp unified interface** cho client

### Tên gọi khác
- **Wrapper Pattern** (khác với Decorator)
- **Interface Pattern**

### Ví dụ thực tế
Giống như **receptionist tại khách sạn**: thay vì bạn phải tự liên hệ với housekeeping, restaurant, maintenance, security... bạn chỉ cần gọi reception và họ sẽ lo tất cả.

---

## Vấn đề

### Tình huống phức tạp
Bạn cần tích hợp code với thư viện video conversion phức tạp có hàng chục classes:

```java
// Complex subsystem classes
VideoFile video = new VideoFile("funny-cats-video.ogg");
Codec codec = CodecFactory.extract(video);

if (codec instanceof MPEG4CompressionCodec) {
    // MPEG4 specific processing
    BitrateReader reader = new BitrateReader(video.getFileName());
    BufferedOutputStream buffer = new BufferedOutputStream();
    // ... 20 more lines of complex setup
} else if (codec instanceof OggCompressionCodec) {
    // OGG specific processing  
    AudioMixer mixer = new AudioMixer();
    // ... another 20 lines
}

// More complex operations...
```

### Vấn đề phát sinh

#### 1. Client code phức tạp
```java
public class VideoConverter {
    public void convertVideo(String filename) {
        // Client phải biết tất cả subsystem details
        VideoFile video = new VideoFile(filename);
        Codec codec = CodecFactory.extract(video);
        
        // Complex codec handling
        if (codec instanceof MPEG4CompressionCodec) {
            BitrateReader reader = new BitrateReader(filename);
            BufferedOutputStream buffer = new BufferedOutputStream();
            
            // Configure compression
            CompressionSettings settings = new CompressionSettings();
            settings.setBitrate(reader.read());
            settings.setQuality(Quality.HIGH);
            
            // Setup audio processing
            AudioMixer mixer = new AudioMixer();
            mixer.setChannels(2);
            mixer.setSampleRate(44100);
            
            // Process video
            VideoProcessor processor = new VideoProcessor(codec, settings);
            processor.setAudioMixer(mixer);
            processor.setBuffer(buffer);
            
            // Finally convert
            processor.process(video);
            
        } else if (codec instanceof OggCompressionCodec) {
            // Completely different setup for OGG...
            // Another 30 lines of complex code
        }
        
        // Client code becomes a mess!
    }
}
```

#### 2. Tight coupling với subsystem
```java
// Client depends on many subsystem classes
import com.videolib.VideoFile;
import com.videolib.CodecFactory;
import com.videolib.MPEG4CompressionCodec;
import com.videolib.OggCompressionCodec;
import com.videolib.BitrateReader;
import com.videolib.BufferedOutputStream;
import com.videolib.AudioMixer;
import com.videolib.CompressionSettings;
import com.videolib.VideoProcessor;
// ... 20 more imports!

public class ClientCode {
    // Tightly coupled to subsystem implementation
}
```

#### 3. Code duplication
```java
// Same complex setup repeated everywhere
public class VideoEditor {
    public void editVideo() {
        // Duplicate setup code
        VideoFile video = new VideoFile("input.mp4");
        Codec codec = CodecFactory.extract(video);
        // ... same 50 lines as above
    }
}

public class VideoUploader {
    public void uploadVideo() {
        // Same setup code again!
        VideoFile video = new VideoFile("upload.avi");
        // ... duplicate complexity
    }
}
```

#### 4. Hard to maintain
```java
// Khi subsystem thay đổi, tất cả client code phải update
// VideoProcessor constructor changed? Update everywhere!
// New codec added? Update all client code!
```

---

## Giải pháp

### Ý tưởng cốt lõi
Facade pattern đề xuất **tạo một lớp facade cung cấp giao diện đơn giản** cho tất cả sophisticated features của subsystem.

### Cách hoạt động

#### 1. Tạo Facade class
```java
public class VideoConversionFacade {
    
    public File convertVideo(String fileName, String format) {
        System.out.println("VideoConversionFacade: conversion started.");
        
        // Hide all complexity behind simple interface
        VideoFile file = new VideoFile(fileName);
        Codec sourceCodec = CodecFactory.extract(file);
        
        Codec destinationCodec;
        if (format.equals("mp4")) {
            destinationCodec = new MPEG4CompressionCodec();
        } else {
            destinationCodec = new OggCompressionCodec();
        }
        
        // Handle all the complex setup internally
        VideoFile buffer = BitrateReader.read(file, sourceCodec);
        VideoFile intermediateResult = BitrateReader.convert(buffer, destinationCodec);
        File result = (new AudioMixer()).fix(intermediateResult);
        
        System.out.println("VideoConversionFacade: conversion completed.");
        return result;
    }
}
```

#### 2. Simplified client code
```java
public class Application {
    public static void main(String[] args) {
        VideoConversionFacade converter = new VideoConversionFacade();
        
        // Simple, clean interface
        File mp4Video = converter.convertVideo("funny-cats-video.ogg", "mp4");
        
        // That's it! No complex subsystem knowledge needed
    }
}
```

### Advanced Facade with multiple operations
```java
public class VideoConversionFacade {
    
    // Simple conversion
    public File convertVideo(String fileName, String format) {
        return performConversion(fileName, format, getDefaultSettings());
    }
    
    // Conversion with quality settings
    public File convertVideoWithQuality(String fileName, String format, Quality quality) {
        ConversionSettings settings = getDefaultSettings();
        settings.setQuality(quality);
        return performConversion(fileName, format, settings);
    }
    
    // Batch conversion
    public List<File> convertMultipleVideos(List<String> fileNames, String format) {
        List<File> results = new ArrayList<>();
        for (String fileName : fileNames) {
            results.add(convertVideo(fileName, format));
        }
        return results;
    }
    
    // Private method handles all complexity
    private File performConversion(String fileName, String format, ConversionSettings settings) {
        // All complex subsystem interactions hidden here
        VideoFile file = new VideoFile(fileName);
        Codec sourceCodec = CodecFactory.extract(file);
        
        // Complex processing logic...
        
        return result;
    }
    
    private ConversionSettings getDefaultSettings() {
        ConversionSettings settings = new ConversionSettings();
        settings.setBitrate(1000);
        settings.setQuality(Quality.MEDIUM);
        return settings;
    }
}
```

---

## Cấu trúc

### Sơ đồ UML
```
Facade
├── + operation1(): void
├── + operation2(): void
├── - subsystemA: SubsystemA
├── - subsystemB: SubsystemB
├── - subsystemC: SubsystemC
│
SubsystemA
├── + operationA1(): void
├── + operationA2(): void
│
SubsystemB  
├── + operationB1(): void
├── + operationB2(): void
│
SubsystemC
├── + operationC1(): void
├── + operationC2(): void
│
Client
├── uses → Facade
```

### Các thành phần chính

#### 1. Facade
- **Vai trò:** Cung cấp convenient access đến subsystem functionality
- **Đặc điểm:** Delegates client requests đến appropriate subsystem objects

#### 2. Subsystem Classes
- **Vai trò:** Implement subsystem functionality
- **Đặc điểm:** Handle work assigned by Facade, không aware của Facade

#### 3. Client
- **Vai trò:** Uses Facade thay vì calling subsystem objects directly
- **Đặc điểm:** Decoupled from subsystem complexity

---

## Cách triển khai

### Bước 1: Identify subsystem complexity
```java
// Complex subsystem classes
class CPU {
    public void freeze() { System.out.println("CPU: Freezing..."); }
    public void jump(long position) { System.out.println("CPU: Jumping to " + position); }
    public void execute() { System.out.println("CPU: Executing..."); }
}

class Memory {
    public void load(long position, byte[] data) {
        System.out.println("Memory: Loading data at position " + position);
    }
}

class HardDrive {
    public byte[] read(long lba, int size) {
        System.out.println("HardDrive: Reading " + size + " bytes from LBA " + lba);
        return new byte[size];
    }
}
```

### Bước 2: Create Facade
```java
public class ComputerFacade {
    private CPU cpu;
    private Memory memory;
    private HardDrive hardDrive;
    
    public ComputerFacade() {
        this.cpu = new CPU();
        this.memory = new Memory();
        this.hardDrive = new HardDrive();
    }
    
    public void start() {
        System.out.println("ComputerFacade: Starting computer...");
        
        // Hide complex boot sequence
        cpu.freeze();
        memory.load(0, hardDrive.read(0, 1024));
        cpu.jump(0);
        cpu.execute();
        
        System.out.println("ComputerFacade: Computer started successfully!");
    }
}
```

### Bước 3: Client usage
```java
public class User {
    public static void main(String[] args) {
        ComputerFacade computer = new ComputerFacade();
        
        // Simple interface hides complex boot process
        computer.start();
        
        // User doesn't need to know about CPU, Memory, HardDrive details
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Simplified interface
```java
// Before Facade: Complex client code
VideoFile file = new VideoFile("input.avi");
Codec codec = CodecFactory.extract(file);
BitrateReader reader = new BitrateReader(file.getName());
// ... 20 more lines

// After Facade: Simple client code
VideoFacade facade = new VideoFacade();
File result = facade.convertVideo("input.avi", "mp4");
```

#### 2. Decoupling from subsystem
```java
// Client only depends on Facade
public class VideoEditor {
    private VideoFacade videoFacade; // Single dependency
    
    public void processVideo() {
        videoFacade.convertVideo("input.mp4", "avi");
        // No knowledge of subsystem classes needed
    }
}
```

#### 3. Layered architecture support
```java
// Facade can provide different abstraction levels
public class DatabaseFacade {
    // High-level operations
    public void saveUser(User user) { ... }
    public User findUser(String email) { ... }
    
    // Mid-level operations  
    public void executeQuery(String sql) { ... }
    
    // Low-level operations still available if needed
    public Connection getConnection() { ... }
}
```

### ❌ Nhược điểm

#### 1. Can become God Object
```java
// Facade có thể trở nên quá lớn
public class SystemFacade {
    // Too many responsibilities
    public void manageUsers() { ... }
    public void processPayments() { ... }
    public void generateReports() { ... }
    public void sendEmails() { ... }
    public void manageInventory() { ... }
    // Violates Single Responsibility Principle
}
```

#### 2. Limited flexibility
```java
// Facade có thể hạn chế access đến advanced features
public class SimpleDatabaseFacade {
    public void save(Object obj) {
        // Only provides basic save operation
        // Advanced features like transactions, batch operations not available
    }
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Complex subsystem với nhiều classes
```java
// E-commerce system với nhiều subsystems
public class ECommerceFacade {
    private InventoryService inventory;
    private PaymentService payment;
    private ShippingService shipping;
    private NotificationService notification;
    
    public OrderResult placeOrder(Order order) {
        // Coordinate multiple subsystems
        if (inventory.checkAvailability(order.getItems())) {
            PaymentResult paymentResult = payment.processPayment(order.getPayment());
            if (paymentResult.isSuccessful()) {
                shipping.scheduleDelivery(order);
                notification.sendConfirmation(order.getCustomer());
                return OrderResult.success();
            }
        }
        return OrderResult.failure();
    }
}
```

#### 2. Muốn layer subsystem
```java
// API Gateway pattern
public class APIGateway {
    private UserService userService;
    private ProductService productService;
    private OrderService orderService;
    
    // Unified REST API
    public ResponseEntity<User> getUser(String id) {
        return userService.findById(id);
    }
    
    public ResponseEntity<List<Product>> getProducts() {
        return productService.getAllProducts();
    }
}
```

#### 3. Minimize dependencies
```java
// Third-party library integration
public class PaymentFacade {
    private StripePaymentProcessor stripe;
    private PayPalPaymentProcessor paypal;
    
    public PaymentResult processPayment(PaymentRequest request) {
        // Hide complexity of multiple payment providers
        if (request.getProvider().equals("stripe")) {
            return stripe.charge(request);
        } else {
            return paypal.processPayment(request);
        }
    }
}
```

### ❌ Không nên sử dụng khi:

#### 1. Subsystem đơn giản
```java
// Overkill for simple subsystem
public class SimpleMathFacade {
    public int add(int a, int b) {
        return a + b; // Too simple for facade
    }
}
```

#### 2. Cần full control over subsystem
```java
// Advanced users need direct access
public class DatabaseExpert {
    public void optimizeQuery() {
        // Needs direct access to query planner, indexes, etc.
        // Facade would be limiting
    }
}
```

---

## Ví dụ thực tế

### Ví dụ 1: Smart Home System

```java
// Subsystem classes
class Lights {
    public void turnOn() { System.out.println("💡 Lights turned on"); }
    public void turnOff() { System.out.println("💡 Lights turned off"); }
    public void dim(int level) { System.out.println("💡 Lights dimmed to " + level + "%"); }
}

class AirConditioner {
    public void turnOn() { System.out.println("❄️ AC turned on"); }
    public void turnOff() { System.out.println("❄️ AC turned off"); }
    public void setTemperature(int temp) { System.out.println("❄️ AC set to " + temp + "°C"); }
}

class SecuritySystem {
    public void arm() { System.out.println("🔒 Security system armed"); }
    public void disarm() { System.out.println("🔓 Security system disarmed"); }
}

class MusicSystem {
    public void turnOn() { System.out.println("🎵 Music system on"); }
    public void turnOff() { System.out.println("🎵 Music system off"); }
    public void setVolume(int volume) { System.out.println("🎵 Volume set to " + volume); }
    public void playPlaylist(String playlist) { System.out.println("🎵 Playing " + playlist); }
}

class TV {
    public void turnOn() { System.out.println("📺 TV turned on"); }
    public void turnOff() { System.out.println("📺 TV turned off"); }
    public void setChannel(int channel) { System.out.println("📺 Channel set to " + channel); }
}

// Facade
public class SmartHomeFacade {
    private Lights lights;
    private AirConditioner ac;
    private SecuritySystem security;
    private MusicSystem music;
    private TV tv;
    
    public SmartHomeFacade() {
        this.lights = new Lights();
        this.ac = new AirConditioner();
        this.security = new SecuritySystem();
        this.music = new MusicSystem();
        this.tv = new TV();
    }
    
    // Scenario-based operations
    public void movieNight() {
        System.out.println("🎬 Setting up Movie Night...");
        lights.dim(20);
        ac.setTemperature(22);
        tv.turnOn();
        tv.setChannel(1); // Netflix
        music.turnOff(); // Quiet for movie
        security.arm();
        System.out.println("🎬 Movie Night setup complete!\n");
    }
    
    public void partyMode() {
        System.out.println("🎉 Setting up Party Mode...");
        lights.turnOn();
        ac.setTemperature(20); // Cooler for crowd
        music.turnOn();
        music.setVolume(80);
        music.playPlaylist("Party Hits");
        tv.turnOff(); // Focus on music
        security.disarm(); // Guests coming
        System.out.println("🎉 Party Mode setup complete!\n");
    }
    
    public void sleepMode() {
        System.out.println("😴 Setting up Sleep Mode...");
        lights.turnOff();
        tv.turnOff();
        music.turnOff();
        ac.setTemperature(24); // Comfortable sleeping temp
        security.arm();
        System.out.println("😴 Sleep Mode setup complete!\n");
    }
    
    public void awayMode() {
        System.out.println("🚗 Setting up Away Mode...");
        lights.turnOff();
        tv.turnOff();
        music.turnOff();
        ac.setTemperature(26); // Energy saving
        security.arm();
        System.out.println("🚗 Away Mode setup complete!\n");
    }
    
    public void welcomeHome() {
        System.out.println("🏠 Welcome Home...");
        security.disarm();
        lights.turnOn();
        ac.setTemperature(22);
        music.turnOn();
        music.setVolume(30);
        music.playPlaylist("Chill");
        System.out.println("🏠 Welcome Home setup complete!\n");
    }
}

// Usage
public class SmartHomeDemo {
    public static void main(String[] args) {
        SmartHomeFacade smartHome = new SmartHomeFacade();
        
        // Simple commands for complex scenarios
        smartHome.welcomeHome();
        
        smartHome.movieNight();
        
        smartHome.partyMode();
        
        smartHome.sleepMode();
        
        smartHome.awayMode();
        
        // Without facade, user would need to:
        // lights.dim(20); ac.setTemperature(22); tv.turnOn(); tv.setChannel(1);
        // music.turnOff(); security.arm(); 
        // And remember all these steps for each scenario!
    }
}
```

### Ví dụ 2: Banking System Facade

```java
// Complex subsystem classes
class AccountService {
    public Account getAccount(String accountNumber) {
        System.out.println("AccountService: Retrieving account " + accountNumber);
        return new Account(accountNumber, 1000.0);
    }
    
    public void updateBalance(String accountNumber, double newBalance) {
        System.out.println("AccountService: Updating balance for " + accountNumber + " to $" + newBalance);
    }
}

class TransactionService {
    public void recordTransaction(String fromAccount, String toAccount, double amount, String type) {
        System.out.println("TransactionService: Recording " + type + " of $" + amount + 
                          " from " + fromAccount + " to " + toAccount);
    }
}

class NotificationService {
    public void sendSMS(String phoneNumber, String message) {
        System.out.println("NotificationService: SMS to " + phoneNumber + ": " + message);
    }
    
    public void sendEmail(String email, String subject, String body) {
        System.out.println("NotificationService: Email to " + email + " - " + subject);
    }
}

class FraudDetectionService {
    public boolean checkTransaction(String accountNumber, double amount) {
        System.out.println("FraudDetectionService: Checking transaction for account " + accountNumber);
        // Simple fraud check
        return amount <= 10000; // Flag transactions over $10,000
    }
}

class AuditService {
    public void logTransaction(String accountNumber, String operation, double amount) {
        System.out.println("AuditService: Logging " + operation + " of $" + amount + 
                          " for account " + accountNumber);
    }
}

// Facade
public class BankingFacade {
    private AccountService accountService;
    private TransactionService transactionService;
    private NotificationService notificationService;
    private FraudDetectionService fraudDetection;
    private AuditService auditService;
    
    public BankingFacade() {
        this.accountService = new AccountService();
        this.transactionService = new TransactionService();
        this.notificationService = new NotificationService();
        this.fraudDetection = new FraudDetectionService();
        this.auditService = new AuditService();
    }
    
    public TransferResult transferMoney(String fromAccount, String toAccount, 
                                       double amount, String customerPhone, String customerEmail) {
        System.out.println("🏦 BankingFacade: Starting money transfer...");
        
        try {
            // Step 1: Fraud detection
            if (!fraudDetection.checkTransaction(fromAccount, amount)) {
                System.out.println("❌ Transfer blocked: Fraud detected");
                return new TransferResult(false, "Transaction flagged as potentially fraudulent");
            }
            
            // Step 2: Check account balances
            Account fromAcc = accountService.getAccount(fromAccount);
            Account toAcc = accountService.getAccount(toAccount);
            
            if (fromAcc.getBalance() < amount) {
                System.out.println("❌ Transfer failed: Insufficient funds");
                return new TransferResult(false, "Insufficient funds");
            }
            
            // Step 3: Perform transfer
            accountService.updateBalance(fromAccount, fromAcc.getBalance() - amount);
            accountService.updateBalance(toAccount, toAcc.getBalance() + amount);
            
            // Step 4: Record transaction
            transactionService.recordTransaction(fromAccount, toAccount, amount, "TRANSFER");
            
            // Step 5: Audit logging
            auditService.logTransaction(fromAccount, "DEBIT", amount);
            auditService.logTransaction(toAccount, "CREDIT", amount);
            
            // Step 6: Notify customer
            notificationService.sendSMS(customerPhone, 
                "Transfer of $" + amount + " completed successfully");
            notificationService.sendEmail(customerEmail, 
                "Transfer Confirmation", 
                "Your transfer of $" + amount + " has been processed");
            
            System.out.println("✅ Transfer completed successfully");
            return new TransferResult(true, "Transfer completed successfully");
            
        } catch (Exception e) {
            System.out.println("❌ Transfer failed: " + e.getMessage());
            return new TransferResult(false, "Transfer failed: " + e.getMessage());
        }
    }
    
    public BalanceResult checkBalance(String accountNumber, String customerPhone) {
        System.out.println("🏦 BankingFacade: Checking balance...");
        
        try {
            Account account = accountService.getAccount(accountNumber);
            auditService.logTransaction(accountNumber, "BALANCE_INQUIRY", 0);
            
            notificationService.sendSMS(customerPhone, 
                "Your current balance is $" + account.getBalance());
            
            return new BalanceResult(true, account.getBalance());
            
        } catch (Exception e) {
            return new BalanceResult(false, 0);
        }
    }
}

// Helper classes
class Account {
    private String accountNumber;
    private double balance;
    
    public Account(String accountNumber, double balance) {
        this.accountNumber = accountNumber;
        this.balance = balance;
    }
    
    public String getAccountNumber() { return accountNumber; }
    public double getBalance() { return balance; }
}

class TransferResult {
    private boolean success;
    private String message;
    
    public TransferResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    public boolean isSuccess() { return success; }
    public String getMessage() { return message; }
}

class BalanceResult {
    private boolean success;
    private double balance;
    
    public BalanceResult(boolean success, double balance) {
        this.success = success;
        this.balance = balance;
    }
    
    public boolean isSuccess() { return success; }
    public double getBalance() { return balance; }
}

// Usage
public class BankingDemo {
    public static void main(String[] args) {
        BankingFacade bank = new BankingFacade();
        
        // Simple interface for complex banking operations
        System.out.println("=== Money Transfer ===");
        TransferResult result = bank.transferMoney(
            "ACC001", "ACC002", 500.0, 
            "+**********", "<EMAIL>"
        );
        System.out.println("Result: " + result.getMessage());
        
        System.out.println("\n=== Balance Check ===");
        BalanceResult balance = bank.checkBalance("ACC001", "+**********");
        if (balance.isSuccess()) {
            System.out.println("Balance: $" + balance.getBalance());
        }
        
        // Without facade, client would need to:
        // 1. Call fraud detection service
        // 2. Get account details from account service  
        // 3. Update balances in account service
        // 4. Record transaction in transaction service
        // 5. Log audit trail in audit service
        // 6. Send notifications via notification service
        // 7. Handle all error cases and rollbacks
        // Much more complex!
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Facade Pattern** đơn giản hóa complex subsystem interfaces
2. **Hides complexity** behind clean, simple interface
3. **Reduces dependencies** giữa client và subsystem
4. **Provides unified interface** cho related functionality

### So sánh với patterns khác
| Pattern | Purpose | Complexity |
|---------|---------|------------|
| **Facade** | Simplify complex interface | Hide subsystem complexity |
| **Adapter** | Make incompatible interfaces work | Convert interface |
| **Proxy** | Control access to object | Add access control |
| **Decorator** | Add behavior dynamically | Extend functionality |

### Best Practices
- **Keep facade simple** và focused
- **Don't hide everything** - allow direct access when needed
- **Consider multiple facades** cho different user types
- **Document** what complexity is being hidden
- **Avoid God Object** - split large facades

---

**Tiếp theo:** [Flyweight](flyweight.md) - Tối ưu bộ nhớ bằng chia sẻ dữ liệu
