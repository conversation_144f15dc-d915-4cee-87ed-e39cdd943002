# Bridge Pattern

> **Structural Pattern** - Tách abstraction khỏi implementation để cả hai có thể phát triển độc lập

## 📋 Mục lục

1. [Tổng quan](#tổng-quan)
2. [Vấn đề](#vấn-đề)
3. [G<PERSON><PERSON><PERSON> pháp](#giải-pháp)
4. [<PERSON><PERSON><PERSON> trúc](#cấu-trúc)
5. [C<PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nhược-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [V<PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Bridge là một **structural design pattern** cho phép **tách một lớp lớn hoặc một tập hợp các lớp liên quan chặt chẽ thành hai hệ thống phân cấp riêng biệt** - abstraction và implementation - có thể được phát triển độc lập.

### Mục đích chính
- **Tách biệt** abstraction khỏi implementation
- **Cho phép** cả hai phát triển độc lập
- **Tránh** "class explosion" khi có nhiều dimensions of variation

### Tên gọi khác
- **Handle/Body Pattern**
- **Pimpl (Pointer to Implementation) Pattern**

### Ví dụ thực tế
Giống như **remote control** (abstraction) có thể điều khiển nhiều loại **TV khác nhau** (implementation). Remote control interface không thay đổi dù TV là Samsung, LG hay Sony.

---

## Vấn đề

### Tình huống phức tạp
Giả sử bạn có lớp `Shape` với các subclasses `Circle` và `Square`. Bây giờ bạn muốn mở rộng để có các màu khác nhau: `Red` và `Blue`.

### Class Explosion Problem

#### Cách tiếp cận inheritance truyền thống
```java
// Base class
abstract class Shape {
    abstract void draw();
}

// Shape types
class Circle extends Shape { ... }
class Square extends Shape { ... }

// Khi thêm colors, cần tạo nhiều classes
class RedCircle extends Circle { ... }
class BlueCircle extends Circle { ... }
class RedSquare extends Square { ... }
class BlueSquare extends Square { ... }

// Thêm Triangle?
class Triangle extends Shape { ... }
class RedTriangle extends Triangle { ... }
class BlueTriangle extends Triangle { ... }

// Thêm Green color?
class GreenCircle extends Circle { ... }
class GreenSquare extends Square { ... }
class GreenTriangle extends Triangle { ... }
```

### Vấn đề phát sinh

#### 1. Exponential growth
```
Shapes: Circle, Square, Triangle (3)
Colors: Red, Blue, Green (3)
Total classes needed: 3 × 3 = 9 classes!

Add Rectangle: 4 × 3 = 12 classes
Add Yellow: 4 × 4 = 16 classes
```

#### 2. Tight coupling
```java
class RedCircle extends Circle {
    @Override
    void draw() {
        // Tightly coupled to both Circle behavior AND Red color
        drawCircleInRed();
    }
}
```

#### 3. Khó maintain và extend
```java
// Muốn thay đổi cách draw Circle?
// Phải sửa tất cả: RedCircle, BlueCircle, GreenCircle...

// Muốn thêm behavior mới (như resize)?
// Phải modify tất cả existing classes
```

#### 4. Code duplication
```java
class RedCircle extends Circle {
    void draw() {
        setColor("red");    // Duplicate color logic
        drawCircle();       // Duplicate shape logic
    }
}

class RedSquare extends Square {
    void draw() {
        setColor("red");    // Same color logic duplicated!
        drawSquare();
    }
}
```

---

## Giải pháp

### Ý tưởng cốt lõi
Bridge pattern giải quyết bằng cách **chuyển từ inheritance sang composition**. Tách các dimensions thành separate class hierarchies và kết nối chúng thông qua **bridge**.

### Cách hoạt động

#### 1. Tạo Implementation interface
```java
// Implementation interface for colors
public interface Color {
    void applyColor();
}
```

#### 2. Concrete Implementations
```java
public class Red implements Color {
    @Override
    public void applyColor() {
        System.out.println("Applying red color");
    }
}

public class Blue implements Color {
    @Override
    public void applyColor() {
        System.out.println("Applying blue color");
    }
}
```

#### 3. Abstraction với bridge
```java
// Abstraction
public abstract class Shape {
    protected Color color; // Bridge to implementation
    
    public Shape(Color color) {
        this.color = color;
    }
    
    public abstract void draw();
}
```

#### 4. Refined Abstractions
```java
public class Circle extends Shape {
    public Circle(Color color) {
        super(color);
    }
    
    @Override
    public void draw() {
        System.out.print("Drawing Circle. ");
        color.applyColor(); // Delegate to implementation
    }
}

public class Square extends Shape {
    public Square(Color color) {
        super(color);
    }
    
    @Override
    public void draw() {
        System.out.print("Drawing Square. ");
        color.applyColor();
    }
}
```

#### 5. Client usage
```java
public class BridgeDemo {
    public static void main(String[] args) {
        // Flexible combinations
        Shape redCircle = new Circle(new Red());
        Shape blueSquare = new Square(new Blue());
        
        redCircle.draw();  // Drawing Circle. Applying red color
        blueSquare.draw(); // Drawing Square. Applying blue color
        
        // Easy to add new combinations
        Shape blueCircle = new Circle(new Blue());
        blueCircle.draw(); // Drawing Circle. Applying blue color
    }
}
```

### Lợi ích của giải pháp
```
Without Bridge: N shapes × M colors = N×M classes
With Bridge: N shapes + M colors = N+M classes

Example: 3 shapes × 3 colors
- Without Bridge: 9 classes
- With Bridge: 6 classes (3+3)
```

---

## Cấu trúc

### Sơ đồ UML
```
Abstraction
├── - implementation: Implementation
├── + operation(): void
│
RefinedAbstraction extends Abstraction
├── + operation(): void
│
Implementation (interface)
├── + operationImpl(): void
│
ConcreteImplementationA implements Implementation
├── + operationImpl(): void
│
ConcreteImplementationB implements Implementation
├── + operationImpl(): void
```

### Các thành phần chính

#### 1. Abstraction
- **Vai trò:** Định nghĩa abstraction interface
- **Đặc điểm:** Chứa reference đến Implementation object
- **Ví dụ:** `Shape` class

#### 2. Refined Abstraction
- **Vai trò:** Mở rộng Abstraction với variants
- **Đặc điểm:** Implement specific behavior
- **Ví dụ:** `Circle`, `Square` classes

#### 3. Implementation
- **Vai trò:** Interface cho implementation classes
- **Đặc điểm:** Không cần match Abstraction interface
- **Ví dụ:** `Color` interface

#### 4. Concrete Implementation
- **Vai trò:** Triển khai cụ thể của Implementation
- **Đặc điểm:** Platform-specific hoặc variant-specific code
- **Ví dụ:** `Red`, `Blue` classes

---

## Cách triển khai

### Bước 1: Xác định dimensions
```java
// Dimension 1: Device types
// Dimension 2: Operating systems
```

### Bước 2: Tạo Implementation interface
```java
public interface Device {
    boolean isEnabled();
    void enable();
    void disable();
    int getVolume();
    void setVolume(int volume);
    int getChannel();
    void setChannel(int channel);
}
```

### Bước 3: Concrete Implementations
```java
public class Radio implements Device {
    private boolean on = false;
    private int volume = 30;
    private int channel = 1;
    
    @Override
    public boolean isEnabled() {
        return on;
    }
    
    @Override
    public void enable() {
        on = true;
        System.out.println("Radio is now ON");
    }
    
    @Override
    public void disable() {
        on = false;
        System.out.println("Radio is now OFF");
    }
    
    @Override
    public int getVolume() {
        return volume;
    }
    
    @Override
    public void setVolume(int volume) {
        if (volume > 100) {
            this.volume = 100;
        } else if (volume < 0) {
            this.volume = 0;
        } else {
            this.volume = volume;
        }
        System.out.println("Radio volume set to " + this.volume);
    }
    
    @Override
    public int getChannel() {
        return channel;
    }
    
    @Override
    public void setChannel(int channel) {
        this.channel = channel;
        System.out.println("Radio channel set to " + channel);
    }
}

public class TV implements Device {
    private boolean on = false;
    private int volume = 50;
    private int channel = 1;
    
    // Similar implementation for TV...
    @Override
    public void enable() {
        on = true;
        System.out.println("TV is now ON");
    }
    
    // ... other methods
}
```

### Bước 4: Abstraction
```java
public class RemoteControl {
    protected Device device;
    
    public RemoteControl(Device device) {
        this.device = device;
    }
    
    public void togglePower() {
        if (device.isEnabled()) {
            device.disable();
        } else {
            device.enable();
        }
    }
    
    public void volumeDown() {
        device.setVolume(device.getVolume() - 10);
    }
    
    public void volumeUp() {
        device.setVolume(device.getVolume() + 10);
    }
    
    public void channelDown() {
        device.setChannel(device.getChannel() - 1);
    }
    
    public void channelUp() {
        device.setChannel(device.getChannel() + 1);
    }
}
```

### Bước 5: Refined Abstractions
```java
public class AdvancedRemoteControl extends RemoteControl {
    
    public AdvancedRemoteControl(Device device) {
        super(device);
    }
    
    public void mute() {
        device.setVolume(0);
        System.out.println("Device muted");
    }
    
    public void setChannel(int channel) {
        device.setChannel(channel);
    }
    
    public void setVolume(int volume) {
        device.setVolume(volume);
    }
}
```

### Bước 6: Client usage
```java
public class BridgePatternDemo {
    public static void main(String[] args) {
        // Test with Radio
        Device radio = new Radio();
        RemoteControl basicRemote = new RemoteControl(radio);
        
        basicRemote.togglePower();
        basicRemote.volumeUp();
        basicRemote.channelUp();
        
        System.out.println("---");
        
        // Test with TV using advanced remote
        Device tv = new TV();
        AdvancedRemoteControl advancedRemote = new AdvancedRemoteControl(tv);
        
        advancedRemote.togglePower();
        advancedRemote.setVolume(75);
        advancedRemote.setChannel(5);
        advancedRemote.mute();
        
        System.out.println("---");
        
        // Same remote can control different devices
        AdvancedRemoteControl radioAdvanced = new AdvancedRemoteControl(radio);
        radioAdvanced.setVolume(25);
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Tách biệt platform-independent và platform-specific code
```java
// Platform-independent abstraction
public abstract class Window {
    protected WindowImpl impl; // Bridge
    
    public void drawWindow() {
        impl.drawBorder();
        impl.drawTitle();
        drawContent(); // Abstract method
    }
    
    protected abstract void drawContent();
}

// Platform-specific implementations
public class WindowsWindowImpl implements WindowImpl {
    public void drawBorder() {
        System.out.println("Drawing Windows-style border");
    }
}

public class MacWindowImpl implements WindowImpl {
    public void drawBorder() {
        System.out.println("Drawing Mac-style border");
    }
}
```

#### 2. Tuân thủ Open/Closed Principle
```java
// Có thể thêm new abstractions
public class DialogWindow extends Window {
    protected void drawContent() {
        System.out.println("Drawing dialog content");
    }
}

// Có thể thêm new implementations
public class LinuxWindowImpl implements WindowImpl {
    public void drawBorder() {
        System.out.println("Drawing Linux-style border");
    }
}
```

#### 3. Tuân thủ Single Responsibility Principle
```java
// Abstraction chỉ quan tâm high-level logic
public class RemoteControl {
    public void togglePower() {
        // High-level operation
        if (device.isEnabled()) {
            device.disable();
        } else {
            device.enable();
        }
    }
}

// Implementation chỉ quan tâm platform-specific details
public class SmartTV implements Device {
    public void enable() {
        // TV-specific enable logic
        initializeSmartFeatures();
        connectToWiFi();
        System.out.println("Smart TV enabled");
    }
}
```

#### 4. Ẩn implementation details khỏi client
```java
// Client chỉ cần biết abstraction
RemoteControl remote = new RemoteControl(someDevice);
remote.volumeUp(); // Không cần biết device cụ thể là gì
```

### ❌ Nhược điểm

#### 1. Tăng độ phức tạp cho simple scenarios
```java
// Overkill cho simple case
public class SimpleCalculator {
    // Không cần Bridge pattern cho simple operations
    public int add(int a, int b) {
        return a + b;
    }
}
```

#### 2. Cần thiết kế cẩn thận interfaces
```java
// Implementation interface phải được thiết kế tốt
public interface DatabaseImpl {
    // Phải cover tất cả operations cần thiết
    void connect();
    ResultSet query(String sql);
    void disconnect();
    // Thiếu method nào sẽ khó extend sau
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Muốn tránh permanent binding giữa abstraction và implementation
```java
// Có thể switch implementation runtime
public class MediaPlayer {
    private AudioCodec codec; // Can be MP3, AAC, FLAC...
    
    public void setCodec(AudioCodec codec) {
        this.codec = codec; // Runtime switching
    }
    
    public void play(String file) {
        codec.decode(file);
    }
}
```

#### 2. Cả abstraction và implementation cần extend independently
```java
// Abstractions: BasicPlayer, AdvancedPlayer, ProPlayer
// Implementations: MP3Codec, AACCodec, FLACCodec
// Có thể combine bất kỳ: ProPlayer + MP3Codec, BasicPlayer + FLACCodec
```

#### 3. Có nhiều platforms hoặc variants
```java
// Cross-platform GUI
public abstract class Button {
    protected PlatformButton impl;
    
    public Button(PlatformButton impl) {
        this.impl = impl;
    }
    
    public void click() {
        impl.nativeClick();
    }
}

// Platforms: Windows, Mac, Linux
// Button types: PushButton, ToggleButton, RadioButton
```

#### 4. Muốn chia sẻ implementation giữa multiple objects
```java
// Multiple windows có thể share same rendering engine
WindowImpl sharedRenderer = new OpenGLRenderer();

Window window1 = new GameWindow(sharedRenderer);
Window window2 = new EditorWindow(sharedRenderer);
```

### ❌ Không nên sử dụng khi:

#### 1. Chỉ có một implementation
```java
// Không cần Bridge nếu chỉ có một cách implement
public class SimpleLogger {
    public void log(String message) {
        System.out.println(message); // Only one way to log
    }
}
```

#### 2. Abstraction và implementation không thay đổi
```java
// Stable, không cần flexibility
public class MathCalculator {
    public double sqrt(double x) {
        return Math.sqrt(x); // Standard implementation
    }
}
```

---

## Ví dụ thực tế

### Ví dụ 1: Cross-Platform GUI Framework

```java
// Implementation interface
public interface PlatformButton {
    void render();
    void handleClick();
}

// Windows implementation
public class WindowsButton implements PlatformButton {
    @Override
    public void render() {
        System.out.println("Rendering Windows-style button");
    }
    
    @Override
    public void handleClick() {
        System.out.println("Windows button click handled");
    }
}

// Mac implementation
public class MacButton implements PlatformButton {
    @Override
    public void render() {
        System.out.println("Rendering Mac-style button");
    }
    
    @Override
    public void handleClick() {
        System.out.println("Mac button click handled");
    }
}

// Abstraction
public abstract class Button {
    protected PlatformButton impl;
    protected String text;
    
    public Button(PlatformButton impl, String text) {
        this.impl = impl;
        this.text = text;
    }
    
    public void render() {
        System.out.println("Button text: " + text);
        impl.render();
    }
    
    public void click() {
        impl.handleClick();
        onClick(); // Template method
    }
    
    protected abstract void onClick();
}

// Refined abstractions
public class SubmitButton extends Button {
    public SubmitButton(PlatformButton impl) {
        super(impl, "Submit");
    }
    
    @Override
    protected void onClick() {
        System.out.println("Submitting form...");
    }
}

public class CancelButton extends Button {
    public CancelButton(PlatformButton impl) {
        super(impl, "Cancel");
    }
    
    @Override
    protected void onClick() {
        System.out.println("Cancelling operation...");
    }
}

// Usage
public class GUIDemo {
    public static void main(String[] args) {
        // Windows platform
        PlatformButton winImpl = new WindowsButton();
        Button submitBtn = new SubmitButton(winImpl);
        Button cancelBtn = new CancelButton(winImpl);
        
        submitBtn.render();
        submitBtn.click();
        
        System.out.println("---");
        
        // Mac platform - same buttons, different implementation
        PlatformButton macImpl = new MacButton();
        Button macSubmit = new SubmitButton(macImpl);
        
        macSubmit.render();
        macSubmit.click();
    }
}
```

### Ví dụ 2: Database Abstraction Layer

```java
// Implementation interface
public interface DatabaseDriver {
    void connect(String connectionString);
    ResultSet executeQuery(String sql);
    int executeUpdate(String sql);
    void disconnect();
}

// MySQL driver
public class MySQLDriver implements DatabaseDriver {
    private boolean connected = false;
    
    @Override
    public void connect(String connectionString) {
        System.out.println("Connecting to MySQL: " + connectionString);
        connected = true;
    }
    
    @Override
    public ResultSet executeQuery(String sql) {
        System.out.println("MySQL executing query: " + sql);
        return new MockResultSet("MySQL data");
    }
    
    @Override
    public int executeUpdate(String sql) {
        System.out.println("MySQL executing update: " + sql);
        return 1;
    }
    
    @Override
    public void disconnect() {
        System.out.println("Disconnecting from MySQL");
        connected = false;
    }
}

// PostgreSQL driver
public class PostgreSQLDriver implements DatabaseDriver {
    @Override
    public void connect(String connectionString) {
        System.out.println("Connecting to PostgreSQL: " + connectionString);
    }
    
    @Override
    public ResultSet executeQuery(String sql) {
        System.out.println("PostgreSQL executing query: " + sql);
        return new MockResultSet("PostgreSQL data");
    }
    
    @Override
    public int executeUpdate(String sql) {
        System.out.println("PostgreSQL executing update: " + sql);
        return 1;
    }
    
    @Override
    public void disconnect() {
        System.out.println("Disconnecting from PostgreSQL");
    }
}

// Abstraction
public abstract class Database {
    protected DatabaseDriver driver;
    
    public Database(DatabaseDriver driver) {
        this.driver = driver;
    }
    
    public void connect(String connectionString) {
        driver.connect(connectionString);
    }
    
    public void disconnect() {
        driver.disconnect();
    }
    
    public abstract void createTable(String tableName, String[] columns);
    public abstract List<String> findAll(String tableName);
}

// Refined abstraction
public class UserDatabase extends Database {
    public UserDatabase(DatabaseDriver driver) {
        super(driver);
    }
    
    @Override
    public void createTable(String tableName, String[] columns) {
        String sql = "CREATE TABLE " + tableName + " (" + String.join(", ", columns) + ")";
        driver.executeUpdate(sql);
    }
    
    @Override
    public List<String> findAll(String tableName) {
        String sql = "SELECT * FROM " + tableName;
        ResultSet rs = driver.executeQuery(sql);
        
        List<String> results = new ArrayList<>();
        // Process ResultSet...
        results.add("User data from " + tableName);
        return results;
    }
    
    public void createUser(String name, String email) {
        String sql = "INSERT INTO users (name, email) VALUES ('" + name + "', '" + email + "')";
        driver.executeUpdate(sql);
    }
}

// Usage
public class DatabaseDemo {
    public static void main(String[] args) {
        // Use with MySQL
        DatabaseDriver mysqlDriver = new MySQLDriver();
        UserDatabase userDb = new UserDatabase(mysqlDriver);
        
        userDb.connect("mysql://localhost:3306/mydb");
        userDb.createTable("users", new String[]{"id INT", "name VARCHAR(50)", "email VARCHAR(100)"});
        userDb.createUser("John Doe", "<EMAIL>");
        
        System.out.println("---");
        
        // Switch to PostgreSQL - same abstraction, different implementation
        DatabaseDriver postgresDriver = new PostgreSQLDriver();
        UserDatabase postgresUserDb = new UserDatabase(postgresDriver);
        
        postgresUserDb.connect("postgresql://localhost:5432/mydb");
        postgresUserDb.createUser("Jane Smith", "<EMAIL>");
        
        userDb.disconnect();
        postgresUserDb.disconnect();
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Bridge Pattern** tách abstraction khỏi implementation
2. **Composition over inheritance** để tránh class explosion
3. **Cho phép** cả hai hierarchies phát triển độc lập
4. **Hữu ích** cho cross-platform development và plugin architectures

### So sánh với patterns khác
| Pattern | Mục đích | Structure |
|---------|----------|-----------|
| **Bridge** | Separate abstraction/implementation | Abstraction → Implementation |
| **Adapter** | Make incompatible interfaces work | Client → Adapter → Adaptee |
| **Strategy** | Encapsulate algorithms | Context → Strategy |
| **State** | Change behavior based on state | Context → State |

### Best Practices
- **Identify dimensions** of variation early
- **Design implementation interface** carefully
- **Keep abstraction simple** and focused
- **Consider performance** impact of indirection

---

**Tiếp theo:** [Composite](composite.md) - Tổ chức đối tượng thành cấu trúc cây
