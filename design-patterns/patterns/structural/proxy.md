# Proxy Pattern

> **Structural Pattern** - Cung cấp placeholder hoặc surrogate cho đối tượng khác để kiểm soát quyền truy cập

## 📋 <PERSON><PERSON><PERSON> lục

1. [Tổng quan](#tổng-quan)
2. [<PERSON><PERSON><PERSON> đề](#vấn-đề)
3. [<PERSON><PERSON><PERSON><PERSON> pháp](#giải-pháp)
4. [<PERSON><PERSON><PERSON> loại Proxy](#các-loại-proxy)
5. [<PERSON><PERSON>ch triển khai](#cách-triển-khai)
6. [Ưu nhược điểm](#ưu-nhược-điểm)
7. [Khi nào sử dụng](#khi-nào-sử-dụng)
8. [V<PERSON> dụ thực tế](#ví-dụ-thực-tế)

---

## Tổng quan

### Định nghĩa
Proxy là một **structural design pattern** cho phép **cung cấp substitute hoặc placeholder cho đối tượng khác**. Proxy kiểm soát quyền truy cậ<PERSON> đến original object, cho phép thực hiện something trước hoặc sau khi request được chuyển đến original object.

### Mục đích chính
- **Kiểm soát access** đến expensive objects
- **Add extra functionality** mà không thay đổi original object
- **Lazy initialization** của expensive resources
- **Caching, logging, access control**

### Tên gọi khác
- **Surrogate Pattern**
- **Placeholder Pattern**

### Ví dụ thực tế
Giống như **thẻ tín dụng**: thay vì mang tiền mặt (expensive object), bạn dùng thẻ tín dụng (proxy) để truy cập tài khoản ngân hàng. Thẻ kiểm soát quyền truy cập và thêm các tính năng như logging, security.

---

## Vấn đề

### Tình huống thực tế
Bạn có một object thực hiện network requests tốn kém:

```java
public class DatabaseService {
    public DatabaseService() {
        // Expensive initialization
        connectToDatabase();
        loadConfiguration();
        setupConnectionPool();
    }
    
    public String query(String sql) {
        // Expensive network operation
        return executeQuery(sql);
    }
    
    private void connectToDatabase() {
        // Takes 5 seconds to connect
        try { Thread.sleep(5000); } catch (InterruptedException e) {}
        System.out.println("Connected to database");
    }
}
```

### Vấn đề phát sinh

#### 1. Expensive initialization
```java
public class Application {
    public void start() {
        // Database service created immediately, even if not used
        DatabaseService db = new DatabaseService(); // 5 second delay!
        
        // Maybe we don't even need database in this session
        if (userWantsOfflineMode()) {
            return; // Wasted 5 seconds for nothing
        }
        
        db.query("SELECT * FROM users");
    }
}
```

#### 2. No access control
```java
public class UserService {
    private DatabaseService db = new DatabaseService();
    
    public void deleteAllUsers() {
        // Dangerous operation - no access control!
        db.query("DELETE FROM users"); // Anyone can call this
    }
}
```

#### 3. No caching
```java
public class ReportService {
    private DatabaseService db = new DatabaseService();
    
    public String generateReport() {
        // Same expensive query called multiple times
        String data1 = db.query("SELECT * FROM sales"); // Expensive
        String data2 = db.query("SELECT * FROM sales"); // Same query, expensive again!
        return processReport(data1, data2);
    }
}
```

#### 4. No logging/monitoring
```java
// No way to track database usage
// No way to log slow queries
// No way to monitor performance
```

---

## Giải pháp

### Ý tưởng cốt lõi
Proxy pattern đề xuất **tạo new proxy class với same interface** như original service object. Sau đó update app để pass proxy object đến tất cả original object's clients.

### Cách hoạt động

#### 1. Subject interface
```java
public interface DatabaseInterface {
    String query(String sql);
}
```

#### 2. Real Subject
```java
public class DatabaseService implements DatabaseInterface {
    public DatabaseService() {
        connectToDatabase();
    }
    
    @Override
    public String query(String sql) {
        System.out.println("Executing query: " + sql);
        // Expensive database operation
        return "Database result for: " + sql;
    }
    
    private void connectToDatabase() {
        try { Thread.sleep(2000); } catch (InterruptedException e) {}
        System.out.println("Connected to database");
    }
}
```

#### 3. Proxy
```java
public class DatabaseProxy implements DatabaseInterface {
    private DatabaseService realService;
    private Map<String, String> cache = new HashMap<>();
    private Set<String> allowedUsers = Set.of("admin", "user1");
    private String currentUser;
    
    public DatabaseProxy(String currentUser) {
        this.currentUser = currentUser;
    }
    
    @Override
    public String query(String sql) {
        // Access control
        if (!checkAccess()) {
            return "Access denied for user: " + currentUser;
        }
        
        // Caching
        if (cache.containsKey(sql)) {
            System.out.println("Cache hit for: " + sql);
            return cache.get(sql);
        }
        
        // Lazy initialization
        if (realService == null) {
            System.out.println("Initializing database service...");
            realService = new DatabaseService();
        }
        
        // Logging
        System.out.println("Proxy: Executing query for user " + currentUser);
        long startTime = System.currentTimeMillis();
        
        // Delegate to real service
        String result = realService.query(sql);
        
        // Performance monitoring
        long duration = System.currentTimeMillis() - startTime;
        System.out.println("Query completed in " + duration + "ms");
        
        // Cache result
        cache.put(sql, result);
        
        return result;
    }
    
    private boolean checkAccess() {
        return allowedUsers.contains(currentUser);
    }
}
```

#### 4. Client usage
```java
public class Application {
    public static void main(String[] args) {
        // Use proxy instead of real service
        DatabaseInterface db = new DatabaseProxy("admin");
        
        // First query - initializes real service and caches result
        String result1 = db.query("SELECT * FROM users");
        System.out.println("Result: " + result1);
        
        // Second query - cache hit
        String result2 = db.query("SELECT * FROM users");
        System.out.println("Result: " + result2);
        
        // Unauthorized user
        DatabaseInterface unauthorizedDb = new DatabaseProxy("hacker");
        String result3 = unauthorizedDb.query("DELETE FROM users");
        System.out.println("Result: " + result3);
    }
}
```

---

## Các loại Proxy

### 1. Virtual Proxy (Lazy Loading)
```java
public class ImageProxy implements Image {
    private String filename;
    private RealImage realImage; // Loaded on demand
    
    public ImageProxy(String filename) {
        this.filename = filename;
    }
    
    @Override
    public void display() {
        if (realImage == null) {
            realImage = new RealImage(filename); // Lazy loading
        }
        realImage.display();
    }
}
```

### 2. Protection Proxy (Access Control)
```java
public class ProtectedFileProxy implements FileInterface {
    private RealFile realFile;
    private String userRole;
    
    public ProtectedFileProxy(String filename, String userRole) {
        this.realFile = new RealFile(filename);
        this.userRole = userRole;
    }
    
    @Override
    public void read() {
        if (userRole.equals("admin") || userRole.equals("user")) {
            realFile.read();
        } else {
            System.out.println("Access denied: insufficient permissions");
        }
    }
    
    @Override
    public void write(String content) {
        if (userRole.equals("admin")) {
            realFile.write(content);
        } else {
            System.out.println("Access denied: write permission required");
        }
    }
}
```

### 3. Remote Proxy (Network Access)
```java
public class RemoteServiceProxy implements ServiceInterface {
    private String serverUrl;
    private HttpClient httpClient;
    
    public RemoteServiceProxy(String serverUrl) {
        this.serverUrl = serverUrl;
        this.httpClient = new HttpClient();
    }
    
    @Override
    public String processData(String data) {
        try {
            // Handle network communication
            HttpRequest request = new HttpRequest(serverUrl + "/process");
            request.setBody(data);
            
            HttpResponse response = httpClient.send(request);
            return response.getBody();
            
        } catch (NetworkException e) {
            return "Error: Unable to connect to remote service";
        }
    }
}
```

### 4. Caching Proxy
```java
public class CachingProxy implements DataService {
    private RealDataService realService;
    private Map<String, Object> cache = new HashMap<>();
    private long cacheTimeout = 300000; // 5 minutes
    private Map<String, Long> cacheTimestamps = new HashMap<>();
    
    @Override
    public Object getData(String key) {
        // Check cache validity
        if (cache.containsKey(key)) {
            long timestamp = cacheTimestamps.get(key);
            if (System.currentTimeMillis() - timestamp < cacheTimeout) {
                System.out.println("Cache hit for: " + key);
                return cache.get(key);
            } else {
                // Cache expired
                cache.remove(key);
                cacheTimestamps.remove(key);
            }
        }
        
        // Load from real service
        if (realService == null) {
            realService = new RealDataService();
        }
        
        Object data = realService.getData(key);
        
        // Cache the result
        cache.put(key, data);
        cacheTimestamps.put(key, System.currentTimeMillis());
        
        return data;
    }
}
```

---

## Cách triển khai

### Bước 1: Định nghĩa Service interface
```java
public interface VideoDownloader {
    String downloadVideo(String url);
}
```

### Bước 2: Real Service implementation
```java
public class YouTubeDownloader implements VideoDownloader {
    @Override
    public String downloadVideo(String url) {
        System.out.println("Connecting to YouTube...");
        simulateNetworkDelay();
        System.out.println("Downloading video from: " + url);
        simulateDownload();
        return "Video content from " + url;
    }
    
    private void simulateNetworkDelay() {
        try { Thread.sleep(1000); } catch (InterruptedException e) {}
    }
    
    private void simulateDownload() {
        try { Thread.sleep(3000); } catch (InterruptedException e) {}
    }
}
```

### Bước 3: Proxy implementation
```java
public class CachedVideoDownloader implements VideoDownloader {
    private YouTubeDownloader downloader;
    private Map<String, String> cache = new HashMap<>();
    
    @Override
    public String downloadVideo(String url) {
        // Check cache first
        if (cache.containsKey(url)) {
            System.out.println("Cache hit! Returning cached video for: " + url);
            return cache.get(url);
        }
        
        // Lazy initialization
        if (downloader == null) {
            downloader = new YouTubeDownloader();
        }
        
        // Download and cache
        String video = downloader.downloadVideo(url);
        cache.put(url, video);
        
        return video;
    }
    
    public void clearCache() {
        cache.clear();
        System.out.println("Cache cleared");
    }
}
```

### Bước 4: Client usage
```java
public class VideoApp {
    public static void main(String[] args) {
        VideoDownloader downloader = new CachedVideoDownloader();
        
        // First download - slow
        System.out.println("=== First Download ===");
        String video1 = downloader.downloadVideo("https://youtube.com/watch?v=123");
        
        // Second download - fast (cached)
        System.out.println("\n=== Second Download (Same URL) ===");
        String video2 = downloader.downloadVideo("https://youtube.com/watch?v=123");
        
        // Different URL - slow again
        System.out.println("\n=== Different URL ===");
        String video3 = downloader.downloadVideo("https://youtube.com/watch?v=456");
    }
}
```

---

## Ưu nhược điểm

### ✅ Ưu điểm

#### 1. Lazy initialization
```java
// Object chỉ được tạo khi thực sự cần
public class ExpensiveResourceProxy implements Resource {
    private ExpensiveResource realResource;
    
    @Override
    public void useResource() {
        if (realResource == null) {
            realResource = new ExpensiveResource(); // Created only when needed
        }
        realResource.useResource();
    }
}
```

#### 2. Access control
```java
// Kiểm soát quyền truy cập
public class SecureProxy implements SecureService {
    private String userRole;
    
    @Override
    public void sensitiveOperation() {
        if (userRole.equals("admin")) {
            realService.sensitiveOperation();
        } else {
            throw new SecurityException("Access denied");
        }
    }
}
```

#### 3. Caching và performance
```java
// Cải thiện performance với caching
public class CachingProxy implements DataService {
    private Map<String, Object> cache = new HashMap<>();
    
    @Override
    public Object getData(String key) {
        return cache.computeIfAbsent(key, k -> realService.getData(k));
    }
}
```

#### 4. Logging và monitoring
```java
// Thêm logging mà không thay đổi original service
public class LoggingProxy implements Service {
    @Override
    public void operation() {
        System.out.println("Before operation");
        realService.operation();
        System.out.println("After operation");
    }
}
```

### ❌ Nhược điểm

#### 1. Increased complexity
```java
// Thêm layer of indirection
Client → Proxy → RealService
// vs direct: Client → RealService
```

#### 2. Potential performance overhead
```java
// Proxy operations có thể add overhead
public class OverheadProxy implements Service {
    @Override
    public void operation() {
        // Additional proxy logic adds overhead
        checkPermissions();
        logOperation();
        validateInput();
        
        realService.operation(); // Actual work
        
        updateStatistics();
        notifyObservers();
    }
}
```

---

## Khi nào sử dụng

### ✅ Nên sử dụng khi:

#### 1. Lazy initialization (Virtual Proxy)
```java
// Expensive objects chỉ tạo khi cần
public class DatabaseConnectionProxy implements Connection {
    private DatabaseConnection realConnection;
    
    @Override
    public void execute(String sql) {
        if (realConnection == null) {
            realConnection = new DatabaseConnection(); // Expensive creation
        }
        realConnection.execute(sql);
    }
}
```

#### 2. Access control (Protection Proxy)
```java
// Kiểm soát quyền truy cập
public class AdminOnlyProxy implements AdminService {
    @Override
    public void deleteAllData() {
        if (!currentUser.isAdmin()) {
            throw new SecurityException("Admin access required");
        }
        realService.deleteAllData();
    }
}
```

#### 3. Caching expensive operations
```java
// Cache kết quả expensive operations
public class CachingCalculatorProxy implements Calculator {
    private Map<String, Double> cache = new HashMap<>();
    
    @Override
    public double complexCalculation(String input) {
        return cache.computeIfAbsent(input, k -> realCalculator.complexCalculation(k));
    }
}
```

#### 4. Remote object access
```java
// Truy cập remote services
public class RemoteAPIProxy implements APIService {
    @Override
    public String callAPI(String endpoint) {
        // Handle network communication, retries, etc.
        return httpClient.get(baseUrl + endpoint);
    }
}
```

### ❌ Không nên sử dụng khi:

#### 1. Simple objects
```java
// Overkill cho simple objects
public class SimpleStringProxy implements StringService {
    @Override
    public String getString() {
        return realService.getString(); // No added value
    }
}
```

#### 2. No additional functionality needed
```java
// Nếu không cần thêm functionality gì
// Direct access đơn giản hơn
```

---

## Ví dụ thực tế

### Ví dụ 1: Image Loading với Virtual Proxy

```java
// Subject interface
public interface Image {
    void display();
    String getInfo();
}

// Real Subject - Expensive image loading
public class RealImage implements Image {
    private String filename;
    private byte[] imageData;
    private int width, height;
    
    public RealImage(String filename) {
        this.filename = filename;
        loadImageFromDisk();
    }
    
    private void loadImageFromDisk() {
        System.out.println("Loading image: " + filename);
        // Simulate expensive loading operation
        try {
            Thread.sleep(2000); // 2 second loading time
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Simulate loading image data
        this.imageData = new byte[1024 * 1024]; // 1MB image
        this.width = 1920;
        this.height = 1080;
        
        System.out.println("Image loaded: " + filename + " (" + width + "x" + height + ")");
    }
    
    @Override
    public void display() {
        System.out.println("Displaying image: " + filename);
        // Simulate rendering
        System.out.println("Rendering " + width + "x" + height + " pixels");
    }
    
    @Override
    public String getInfo() {
        return filename + " (" + width + "x" + height + ", " + imageData.length + " bytes)";
    }
}

// Virtual Proxy - Lazy loading
public class ImageProxy implements Image {
    private String filename;
    private RealImage realImage;
    
    public ImageProxy(String filename) {
        this.filename = filename;
        System.out.println("ImageProxy created for: " + filename);
    }
    
    @Override
    public void display() {
        // Load image only when needed
        if (realImage == null) {
            System.out.println("First access - loading real image...");
            realImage = new RealImage(filename);
        }
        realImage.display();
    }
    
    @Override
    public String getInfo() {
        if (realImage == null) {
            return filename + " (not loaded yet)";
        }
        return realImage.getInfo();
    }
}

// Image Gallery
public class ImageGallery {
    private List<Image> images = new ArrayList<>();
    
    public void addImage(String filename) {
        // Use proxy for lazy loading
        images.add(new ImageProxy(filename));
        System.out.println("Added image to gallery: " + filename);
    }
    
    public void showGallery() {
        System.out.println("\n=== Image Gallery ===");
        for (int i = 0; i < images.size(); i++) {
            System.out.println((i + 1) + ". " + images.get(i).getInfo());
        }
    }
    
    public void displayImage(int index) {
        if (index >= 0 && index < images.size()) {
            System.out.println("\n=== Displaying Image " + (index + 1) + " ===");
            images.get(index).display();
        } else {
            System.out.println("Invalid image index");
        }
    }
}

// Usage
public class ImageGalleryDemo {
    public static void main(String[] args) {
        ImageGallery gallery = new ImageGallery();
        
        // Add images to gallery (fast - only proxies created)
        System.out.println("Adding images to gallery...");
        gallery.addImage("vacation1.jpg");
        gallery.addImage("vacation2.jpg");
        gallery.addImage("vacation3.jpg");
        gallery.addImage("vacation4.jpg");
        
        // Show gallery info (fast - real images not loaded yet)
        gallery.showGallery();
        
        // Display specific images (slow - real images loaded on demand)
        gallery.displayImage(0); // First access - loads real image
        gallery.displayImage(0); // Second access - uses already loaded image
        gallery.displayImage(2); // Loads another real image
        
        // Show updated gallery info
        gallery.showGallery();
        
        System.out.println("\nBenefits demonstrated:");
        System.out.println("- Fast gallery creation (only proxies)");
        System.out.println("- Images loaded only when displayed");
        System.out.println("- Memory efficient for large galleries");
    }
}
```

### Ví dụ 2: Web Service với Protection và Caching Proxy

```java
// Service interface
public interface UserService {
    User getUserById(String userId);
    List<User> getAllUsers();
    void updateUser(User user);
    void deleteUser(String userId);
}

// Real service
public class RealUserService implements UserService {
    private Map<String, User> users = new HashMap<>();
    
    public RealUserService() {
        // Initialize with some test data
        users.put("1", new User("1", "John Doe", "<EMAIL>", "user"));
        users.put("2", new User("2", "Jane Smith", "<EMAIL>", "admin"));
        users.put("3", new User("3", "Bob Johnson", "<EMAIL>", "user"));
    }
    
    @Override
    public User getUserById(String userId) {
        System.out.println("RealUserService: Fetching user " + userId + " from database");
        simulateNetworkDelay();
        return users.get(userId);
    }
    
    @Override
    public List<User> getAllUsers() {
        System.out.println("RealUserService: Fetching all users from database");
        simulateNetworkDelay();
        return new ArrayList<>(users.values());
    }
    
    @Override
    public void updateUser(User user) {
        System.out.println("RealUserService: Updating user " + user.getId() + " in database");
        simulateNetworkDelay();
        users.put(user.getId(), user);
    }
    
    @Override
    public void deleteUser(String userId) {
        System.out.println("RealUserService: Deleting user " + userId + " from database");
        simulateNetworkDelay();
        users.remove(userId);
    }
    
    private void simulateNetworkDelay() {
        try {
            Thread.sleep(1000); // Simulate 1 second network delay
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

// Protected and Cached Proxy
public class UserServiceProxy implements UserService {
    private RealUserService realService;
    private String currentUserRole;
    private String currentUserId;
    
    // Caching
    private Map<String, User> userCache = new HashMap<>();
    private List<User> allUsersCache;
    private long cacheTimeout = 30000; // 30 seconds
    private Map<String, Long> cacheTimestamps = new HashMap<>();
    private long allUsersCacheTimestamp;
    
    public UserServiceProxy(String currentUserId, String currentUserRole) {
        this.currentUserId = currentUserId;
        this.currentUserRole = currentUserRole;
    }
    
    @Override
    public User getUserById(String userId) {
        // Access control - users can only access their own data unless admin
        if (!currentUserRole.equals("admin") && !currentUserId.equals(userId)) {
            throw new SecurityException("Access denied: Cannot access other user's data");
        }
        
        // Check cache
        if (userCache.containsKey(userId)) {
            Long timestamp = cacheTimestamps.get(userId);
            if (timestamp != null && System.currentTimeMillis() - timestamp < cacheTimeout) {
                System.out.println("UserServiceProxy: Cache hit for user " + userId);
                return userCache.get(userId);
            }
        }
        
        // Lazy initialization
        if (realService == null) {
            realService = new RealUserService();
        }
        
        // Fetch from real service
        System.out.println("UserServiceProxy: Cache miss, fetching from real service");
        User user = realService.getUserById(userId);
        
        // Cache the result
        if (user != null) {
            userCache.put(userId, user);
            cacheTimestamps.put(userId, System.currentTimeMillis());
        }
        
        return user;
    }
    
    @Override
    public List<User> getAllUsers() {
        // Access control - only admins can get all users
        if (!currentUserRole.equals("admin")) {
            throw new SecurityException("Access denied: Admin role required");
        }
        
        // Check cache
        if (allUsersCache != null && 
            System.currentTimeMillis() - allUsersCacheTimestamp < cacheTimeout) {
            System.out.println("UserServiceProxy: Cache hit for all users");
            return new ArrayList<>(allUsersCache);
        }
        
        // Lazy initialization
        if (realService == null) {
            realService = new RealUserService();
        }
        
        // Fetch from real service
        System.out.println("UserServiceProxy: Cache miss, fetching all users from real service");
        List<User> users = realService.getAllUsers();
        
        // Cache the result
        allUsersCache = new ArrayList<>(users);
        allUsersCacheTimestamp = System.currentTimeMillis();
        
        return users;
    }
    
    @Override
    public void updateUser(User user) {
        // Access control - users can only update their own data unless admin
        if (!currentUserRole.equals("admin") && !currentUserId.equals(user.getId())) {
            throw new SecurityException("Access denied: Cannot update other user's data");
        }
        
        // Lazy initialization
        if (realService == null) {
            realService = new RealUserService();
        }
        
        // Update in real service
        realService.updateUser(user);
        
        // Invalidate cache
        userCache.remove(user.getId());
        cacheTimestamps.remove(user.getId());
        allUsersCache = null; // Invalidate all users cache
        
        System.out.println("UserServiceProxy: Cache invalidated for user " + user.getId());
    }
    
    @Override
    public void deleteUser(String userId) {
        // Access control - only admins can delete users
        if (!currentUserRole.equals("admin")) {
            throw new SecurityException("Access denied: Admin role required");
        }
        
        // Lazy initialization
        if (realService == null) {
            realService = new RealUserService();
        }
        
        // Delete from real service
        realService.deleteUser(userId);
        
        // Invalidate cache
        userCache.remove(userId);
        cacheTimestamps.remove(userId);
        allUsersCache = null; // Invalidate all users cache
        
        System.out.println("UserServiceProxy: Cache invalidated for deleted user " + userId);
    }
    
    public void clearCache() {
        userCache.clear();
        cacheTimestamps.clear();
        allUsersCache = null;
        System.out.println("UserServiceProxy: All caches cleared");
    }
}

// User class
class User {
    private String id;
    private String name;
    private String email;
    private String role;
    
    public User(String id, String name, String email, String role) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.role = role;
    }
    
    // Getters and setters
    public String getId() { return id; }
    public String getName() { return name; }
    public String getEmail() { return email; }
    public String getRole() { return role; }
    
    public void setName(String name) { this.name = name; }
    public void setEmail(String email) { this.email = email; }
    
    @Override
    public String toString() {
        return "User{id='" + id + "', name='" + name + "', email='" + email + "', role='" + role + "'}";
    }
}

// Usage
public class UserServiceDemo {
    public static void main(String[] args) {
        System.out.println("=== Admin User Demo ===");
        UserService adminService = new UserServiceProxy("2", "admin");
        
        // Admin can access all users
        System.out.println("\n1. Admin getting all users:");
        List<User> allUsers = adminService.getAllUsers();
        allUsers.forEach(System.out::println);
        
        // Second call - should hit cache
        System.out.println("\n2. Admin getting all users again (should hit cache):");
        allUsers = adminService.getAllUsers();
        
        // Admin can access any user
        System.out.println("\n3. Admin getting specific user:");
        User user = adminService.getUserById("1");
        System.out.println(user);
        
        // Second call - should hit cache
        System.out.println("\n4. Admin getting same user again (should hit cache):");
        user = adminService.getUserById("1");
        
        System.out.println("\n=== Regular User Demo ===");
        UserService userService = new UserServiceProxy("1", "user");
        
        // User can access their own data
        System.out.println("\n5. User getting their own data:");
        User ownData = userService.getUserById("1");
        System.out.println(ownData);
        
        // User cannot access other user's data
        System.out.println("\n6. User trying to access other user's data:");
        try {
            userService.getUserById("2");
        } catch (SecurityException e) {
            System.out.println("Security exception: " + e.getMessage());
        }
        
        // User cannot get all users
        System.out.println("\n7. User trying to get all users:");
        try {
            userService.getAllUsers();
        } catch (SecurityException e) {
            System.out.println("Security exception: " + e.getMessage());
        }
        
        System.out.println("\nProxy benefits demonstrated:");
        System.out.println("- Access control based on user role");
        System.out.println("- Caching for improved performance");
        System.out.println("- Lazy initialization of expensive service");
        System.out.println("- Transparent to client code");
    }
}
```

---

## 🎯 Tóm tắt

### Key Points
1. **Proxy Pattern** kiểm soát access đến objects khác
2. **Multiple types:** Virtual, Protection, Remote, Caching
3. **Transparent** to client - same interface as real object
4. **Adds functionality** without changing original object

### So sánh với patterns khác
| Pattern | Purpose | When to Use |
|---------|---------|-------------|
| **Proxy** | Control access to object | Lazy loading, security, caching |
| **Adapter** | Make incompatible interfaces work | Interface mismatch |
| **Decorator** | Add behavior dynamically | Extend functionality |
| **Facade** | Simplify complex interface | Hide complexity |

### Best Practices
- **Choose appropriate proxy type** cho use case
- **Keep proxy interface identical** to real object
- **Handle proxy lifecycle** properly
- **Consider performance** impact of proxy operations
- **Document** what functionality proxy adds

---

**Tiếp theo:** [Behavioral Patterns](../behavioral/) - Tìm hiểu các mẫu hành vi
