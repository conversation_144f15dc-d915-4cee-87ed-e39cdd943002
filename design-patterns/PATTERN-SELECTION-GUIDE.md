# 🔍 Pattern Selection Guide - Choosing the Right Design Pattern

> Hướng dẫn lựa chọn Design Pattern phù hợp cho từng tình huống cụ thể

## 📋 Mục lục

1. [Decision Tree](#decision-tree)
2. [Comparison Tables](#comparison-tables)
3. [When to Use Each Pattern](#when-to-use-each-pattern)
4. [Pattern Combinations](#pattern-combinations)
5. [Common Mistakes](#common-mistakes)

---

## 🌳 Decision Tree

### 🤔 Bạn đang giải quyết vấn đề gì?

```
📝 Start Here: What's your main concern?
│
├── 🏗️ Object Creation Issues
│   ├── Don't know exact type at compile time → Factory Method
│   ├── Need family of related objects → Abstract Factory
│   ├── Complex object construction → Builder
│   ├── Expensive object creation → Prototype
│   └── Need exactly one instance → Singleton
│
├── 🔧 Object Structure Issues
│   ├── Incompatible interfaces → Adapter
│   ├── Separate abstraction from implementation → Bridge
│   ├── Tree-like structures → Composite
│   ├── Add behavior without modification → Decorator
│   ├── Simplify complex subsystem → Facade
│   ├── Memory optimization → Flyweight
│   └── Control access to objects → Proxy
│
└── 🎭 Object Behavior Issues
    ├── Chain of processing → Chain of Responsibility
    ├── Encapsulate requests → Command
    ├── Traverse collections → Iterator
    ├── Complex object interactions → Mediator
    ├── Save/restore state → Memento
    ├── Notify multiple objects → Observer
    ├── State-dependent behavior → State
    ├── Interchangeable algorithms → Strategy
    ├── Algorithm template → Template Method
    ├── Add operations to hierarchy → Visitor
    └── Interpret language/grammar → Interpreter
```

---

## 📊 Comparison Tables

### 🏗️ Creational Patterns Comparison

| Pattern | Problem Solved | When NOT to Use | Alternative |
|---------|----------------|-----------------|-------------|
| **Factory Method** | Unknown object type at runtime | Only 1 concrete type | Direct constructor |
| **Abstract Factory** | Multiple related object families | Single product family | Factory Method |
| **Builder** | Complex object construction | Simple objects | Constructor |
| **Prototype** | Expensive object creation | Cheap object creation | Constructor |
| **Singleton** | Need exactly one instance | Multiple instances needed | Regular class |

### 🔧 Structural Patterns Comparison

| Pattern | Problem Solved | Complexity | Memory Impact |
|---------|----------------|------------|---------------|
| **Adapter** | Interface incompatibility | ⭐⭐ | Low |
| **Bridge** | Abstraction-implementation coupling | ⭐⭐⭐⭐ | Medium |
| **Composite** | Tree structures | ⭐⭐⭐ | Medium |
| **Decorator** | Dynamic behavior addition | ⭐⭐⭐ | Medium |
| **Facade** | Complex subsystem | ⭐⭐ | Low |
| **Flyweight** | Memory optimization | ⭐⭐⭐⭐ | High savings |
| **Proxy** | Access control | ⭐⭐⭐ | Low |

### 🎭 Behavioral Patterns Comparison

| Pattern | Communication Type | Coupling Level | Use Case |
|---------|-------------------|----------------|----------|
| **Chain of Responsibility** | Sequential processing | Loose | Middleware, filters |
| **Command** | Request encapsulation | Medium | Undo/redo, queuing |
| **Iterator** | Collection traversal | Loose | Data access |
| **Mediator** | Centralized control | Medium | Complex interactions |
| **Memento** | State capture | Tight | State management |
| **Observer** | Event notification | Loose | Event systems |
| **State** | State-based behavior | Medium | State machines |
| **Strategy** | Algorithm selection | Loose | Algorithm families |
| **Template Method** | Algorithm template | Medium | Code reuse |
| **Visitor** | External operations | Tight | AST processing |

---

## 🎯 When to Use Each Pattern

### 🏗️ Creational Patterns

#### Factory Method
**✅ Use when:**
- Creating objects based on user input/config
- Framework needs to delegate object creation
- Supporting multiple product variants

**❌ Don't use when:**
- Only one concrete implementation exists
- Object creation is simple and stable

**🔄 Consider instead:**
- Direct constructor for simple cases
- Abstract Factory for product families

#### Builder
**✅ Use when:**
- Constructor has 4+ parameters
- Optional parameters exist
- Step-by-step construction needed

**❌ Don't use when:**
- Simple objects with few properties
- Immutable objects are not required

**🔄 Consider instead:**
- Constructor with parameter objects
- Factory methods for variants

### 🔧 Structural Patterns

#### Adapter vs Bridge
**Adapter:**
- **When:** Integrating legacy/3rd party code
- **Goal:** Make existing interfaces work together
- **Timing:** After development

**Bridge:**
- **When:** Preventing "class explosion"
- **Goal:** Separate abstraction from implementation  
- **Timing:** During design phase

#### Decorator vs Strategy
**Decorator:**
- **Focus:** Adding behavior to objects
- **Structure:** Wrapper chains
- **Example:** Coffee with milk, sugar, etc.

**Strategy:**
- **Focus:** Swapping algorithms
- **Structure:** Composition
- **Example:** Payment methods (Cash, Card, PayPal)

### 🎭 Behavioral Patterns

#### Observer vs Mediator
**Observer:**
- **Relationship:** One-to-many
- **Coupling:** Publisher doesn't know subscribers
- **Example:** Newsletter subscriptions

**Mediator:**
- **Relationship:** Many-to-many
- **Coupling:** Objects know mediator only
- **Example:** Dialog box with interdependent controls

#### State vs Strategy
**State:**
- **Focus:** Object changes behavior based on internal state
- **Transitions:** Automatic state changes
- **Example:** Media player (Play → Pause → Stop)

**Strategy:**
- **Focus:** Client chooses algorithm
- **Selection:** External algorithm selection
- **Example:** Sorting algorithms (Quick, Merge, Bubble)

---

## 🔗 Pattern Combinations

### Common Pattern Pairs

#### 1. **Abstract Factory + Builder**
```java
// Create families of complex objects
UIFactory factory = new WindowsUIFactory();
Button button = new ButtonBuilder()
    .setFactory(factory)
    .setText("Save")
    .setIcon("save.png")
    .build();
```

#### 2. **Command + Memento**
```java
// Undoable commands with state backup
public class UndoableCommand implements Command {
    private Memento backup;
    
    public void execute() {
        backup = receiver.createMemento();
        receiver.action();
    }
    
    public void undo() {
        receiver.restore(backup);
    }
}
```

#### 3. **Observer + Strategy**
```java
// Configurable event processing
class EventProcessor {
    private ProcessingStrategy strategy;
    private List<EventListener> listeners;
    
    void processEvent(Event event) {
        Event processed = strategy.process(event);
        notifyListeners(processed);
    }
}
```

#### 4. **Factory Method + Singleton**
```java
// Singleton factories for object families
public class DatabaseFactory {
    private static DatabaseFactory instance;
    
    public static DatabaseFactory getInstance() {
        if (instance == null) {
            instance = new DatabaseFactory();
        }
        return instance;
    }
    
    public Database createDatabase(String type) {
        // Factory logic
    }
}
```

### Anti-Patterns to Avoid

#### ❌ **God Object with Singleton**
```java
// DON'T: Singleton that does everything
public class ApplicationManager {
    // Database access, UI management, business logic...
}

// ✅ DO: Separate concerns with multiple patterns
public class DatabaseManager { /* Singleton for DB */ }
public class UIController { /* Observer for events */ }
public class BusinessService { /* Strategy for algorithms */ }
```

#### ❌ **Excessive Decorator Chains**
```java
// DON'T: Too many nested decorators
Coffee coffee = new MilkDecorator(
    new SugarDecorator(
        new VanillaDecorator(
            new CaramelDecorator(
                new WhipDecorator(new BasicCoffee())
            )
        )
    )
);

// ✅ DO: Use Builder for complex configurations
Coffee coffee = new CoffeeBuilder()
    .addMilk()
    .addSugar() 
    .addVanilla()
    .build();
```

---

## 🧭 Selection Framework

### Step 1: Identify the Core Problem

| Question | Pattern Category |
|----------|-----------------|
| "How do I create objects?" | Creational |
| "How do I structure relationships?" | Structural |
| "How do I manage interactions?" | Behavioral |

### Step 2: Narrow Down by Context

#### For Web Applications:
- **Factory Method** - HTTP handlers by request type
- **Observer** - Event-driven architectures
- **Strategy** - Authentication methods
- **Facade** - API wrappers

#### For Game Development:
- **Prototype** - Game object cloning
- **State** - Character behaviors
- **Command** - Input handling
- **Flyweight** - Particle systems

#### For Enterprise Systems:
- **Abstract Factory** - Cross-platform components
- **Chain of Responsibility** - Processing pipelines
- **Mediator** - Service orchestration
- **Memento** - Transaction rollback

### Step 3: Consider Performance Implications

| Pattern | Memory Impact | CPU Impact | Scalability |
|---------|---------------|-------------|-------------|
| **Singleton** | ⬇️ Low | ⬇️ Low | ⚠️ Bottleneck risk |
| **Flyweight** | ⬇️⬇️ Very Low | ➡️ Medium | ⬆️ High |
| **Proxy** | ➡️ Medium | ➡️ Medium | ⬆️ High |
| **Observer** | ⬆️ High | ⬆️ High | ⚠️ Event storms |

---

## 🎮 Interactive Decision Helper

### Scenario-Based Selection

#### Scenario 1: E-commerce Shopping Cart
**Requirements:**
- Track items and quantities
- Calculate totals with different tax strategies  
- Notify users of price changes
- Undo/redo item additions

**Recommended Patterns:**
1. **Strategy** - Tax calculation algorithms
2. **Observer** - Price change notifications  
3. **Command** - Undo/redo operations
4. **Singleton** - Cart instance per session

#### Scenario 2: Document Processing System
**Requirements:**
- Support multiple formats (PDF, DOC, TXT)
- Add features like encryption, compression
- Chain validation steps
- Parse document structures

**Recommended Patterns:**
1. **Factory Method** - Document creation by type
2. **Decorator** - Add processing features
3. **Chain of Responsibility** - Validation pipeline
4. **Visitor** - Document structure traversal

#### Scenario 3: Game Character System
**Requirements:**
- Multiple character types and abilities
- State-based behavior (idle, attacking, defending)
- Optimize memory for thousands of characters
- Clone characters with variations

**Recommended Patterns:**
1. **Abstract Factory** - Character families
2. **State** - Character behaviors
3. **Flyweight** - Shared character data
4. **Prototype** - Character variations

---

## 📚 Quick Reference Cards

### 🚨 Emergency Pattern Selection

**"I need this NOW!"** scenarios:

| Situation | Quick Solution | 5-min Implementation |
|-----------|----------------|---------------------|
| Incompatible 3rd party library | **Adapter** | Wrapper class |
| Too many constructor params | **Builder** | Builder class |
| Need global access | **Singleton** | Static instance |
| Multiple algorithms | **Strategy** | Interface + implementations |
| Event notifications | **Observer** | Listener pattern |
| Undo functionality | **Command** | Command interface |

### 🎯 Pattern Selection Checklist

Before implementing any pattern, ask:

- [ ] **Is the problem recurring?** (Don't over-engineer one-offs)
- [ ] **Will this add or reduce complexity?** (Measure cognitive load)
- [ ] **Can I explain this to teammates easily?** (Communication test)
- [ ] **Does this solve the actual problem?** (Not just apply patterns)
- [ ] **What's the maintenance cost?** (Long-term implications)

---

## 🔄 Refactoring to Patterns

### Common Code Smells → Pattern Solutions

#### 1. **Long Parameter Lists** → Builder
```java
// Before: Hard to use and remember
public User createUser(String name, String email, int age, 
                      String address, String phone, boolean active) {
}

// After: Clear and flexible
User user = new UserBuilder()
    .name("John")
    .email("<EMAIL>") 
    .age(25)
    .build();
```

#### 2. **Switch Statements** → Strategy/State
```java
// Before: Hard to extend
public void processPayment(String type, double amount) {
    switch(type) {
        case "CREDIT": // credit card logic
        case "PAYPAL": // PayPal logic
    }
}

// After: Easy to extend
PaymentProcessor processor = PaymentFactory.getProcessor(type);
processor.process(amount);
```

#### 3. **Tight Coupling** → Observer
```java
// Before: Direct dependencies
public class OrderService {
    public void createOrder(Order order) {
        // ... order logic ...
        emailService.sendConfirmation(order); // Tight coupling
        inventoryService.updateStock(order);  // Tight coupling
    }
}

// After: Loose coupling via events
public class OrderService {
    public void createOrder(Order order) {
        // ... order logic ...
        eventBus.publish(new OrderCreatedEvent(order));
    }
}
```

---

## 🎓 Learning Path Integration

This selection guide integrates with your learning journey:

### For Beginners (Level 1):
- Focus on **decision tree** for quick pattern identification
- Use **emergency selection table** for immediate needs
- Practice with **scenario-based examples**

### For Intermediate (Level 2):  
- Study **pattern combinations** for real-world solutions
- Learn **anti-patterns** to avoid common mistakes
- Apply **refactoring guidelines** to existing code

### For Advanced (Level 3):
- Master **performance implications** for each pattern
- Create **custom selection frameworks** for your domain
- Develop **pattern languages** for your team

---

## 🔗 Related Resources

- **Implementation Examples:** [`examples/`](examples/) directory
- **Pattern Details:** [`patterns/`](patterns/) directory  
- **Practice Exercises:** [`study-materials/practice-exercises.md`](study-materials/practice-exercises.md)
- **Quick Reference:** [`study-materials/quick-reference.md`](study-materials/quick-reference.md)

---

*Remember: The best pattern is often no pattern. Apply patterns only when they solve real problems and add clear value! 🎯*