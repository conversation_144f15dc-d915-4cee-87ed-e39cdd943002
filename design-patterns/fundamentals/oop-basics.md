# Nền Tảng Lập Trình Hướng Đối <PERSON> (OOP)

> Chương 1-3: <PERSON><PERSON><PERSON> kh<PERSON>i niệm cơ bản về OOP và mối quan hệ giữa các đối tượng

## 📋 Mục lục

1. [<PERSON><PERSON><PERSON>hái Niệm Cơ Bản của OOP](#các-khái-niệm-cơ-bản-của-oop)
2. [Bốn Trụ <PERSON> của OOP](#bốn-trụ-cột-của-oop)
3. [<PERSON>uan Hệ Giữa Các Đố<PERSON>](#quan-hệ-giữa-các-đối-tượng)

---

## C<PERSON><PERSON> Khái Niệm Cơ Bản của OOP

### Định nghĩa
**Lập trình hướng đối tượng (OOP)** là một mô hình lập trình tổ chức dữ liệu và hành vi thành cá<PERSON> **"đối tượng"**, <PERSON><PERSON><PERSON><PERSON> tạo ra từ các **"lớp"** (class) – nh<PERSON>ng bản thiết kế do lập trình viên định nghĩa.

### Đối Tượng và Lớp (Objects and Classes)

**Lớp (Class):**
- Là một bản thiết kế định nghĩa cấu trúc cho các đối tượng
- Chứa các thuộc tính (trường - fields) và hành vi (phương thức - methods)
- Ví dụ: Lớp `Cat` định nghĩa các thuộc tính như `tên`, `tuổi` và hành vi như `ăn()`, `ngủ()`

**Đối tượng (Object):**
- Là các thể hiện cụ thể của lớp
- Gói gọn các mảnh dữ liệu và hành vi liên quan
- Ví dụ: `Oscar` là một đối tượng thuộc lớp `Cat`

### Phân Cấp Lớp (Class Hierarchies)

- Các ứng dụng thường chứa nhiều lớp được tổ chức thành hệ thống phân cấp
- **Lớp cha (superclass)** như `Animal` chứa thuộc tính và hành vi chung
- **Lớp con (subclass)** như `Cat`, `Dog` kế thừa từ lớp cha và có thể:
  - Kế thừa các đặc tính chung
  - Định nghĩa các phương thức độc đáo riêng

### Ghi Đè Phương Thức (Method Overriding)

- Lớp con có thể **ghi đè** các phương thức kế thừa từ lớp cha
- Cho phép thay thế hoàn toàn hành vi mặc định hoặc bổ sung chức năng
- Ví dụ: Phương thức `makeSound()` có thể được ghi đè khác nhau trong `Cat` và `Dog`

**💡 Key Point:** Việc hiểu rõ mối quan hệ giữa các lớp và đối tượng là rất quan trọng để thiết kế OOP hiệu quả.

---

## Bốn Trụ Cột của OOP

### 1. Trừu Tượng (Abstraction)

**Định nghĩa:** Mô hình hóa các đối tượng trong thế giới thực bằng cách **tập trung vào các thuộc tính và hành vi có liên quan** trong một ngữ cảnh cụ thể, bỏ qua các chi tiết không cần thiết.

**Ví dụ:**
- Trong ứng dụng đặt vé máy bay: Lớp `Airplane` chỉ cần thông tin về số hiệu chuyến bay, điểm đến, thời gian khởi hành
- Không cần chi tiết về động cơ, hệ thống nhiên liệu

**💡 Key Point:** Trừu tượng hóa giúp bạn tập trung vào các khía cạnh thiết yếu khi thiết kế hệ thống phần mềm.

### 2. Đóng Gói (Encapsulation)

**Định nghĩa:** Ẩn giấu các hoạt động bên trong của một đối tượng và chỉ phơi bày một **giao diện đơn giản** để tương tác.

**Ví dụ:**
- Khởi động động cơ ô tô: Chỉ cần xoay chìa khóa hoặc nhấn nút
- Che giấu các thao tác phức tạp bên trong như đánh lửa, phun nhiên liệu

**Lợi ích:**
- Đảm bảo tương tác với đối tượng đơn giản và trực tiếp
- Bảo vệ dữ liệu nội bộ khỏi truy cập trái phép
- Dễ bảo trì và thay đổi implementation

### 3. Kế Thừa (Inheritance)

**Định nghĩa:** Cho phép tạo các lớp mới bằng cách **mở rộng các lớp hiện có**, thúc đẩy việc tái sử dụng mã nguồn.

**Đặc điểm:**
- Lớp con kế thừa thuộc tính và phương thức từ lớp cha
- Có thể thêm chức năng bổ sung mà không cần lặp lại mã
- Phải duy trì cùng giao diện với lớp cha
- Phải triển khai tất cả các phương thức trừu tượng

**Hạn chế:**
- Lớp con không thể giảm giao diện của lớp cha
- Có thể tạo ra sự phụ thuộc chặt chẽ

### 4. Đa Hình (Polymorphism)

**Định nghĩa:** Cho phép chương trình xác định lớp cụ thể của một đối tượng và gọi các phương thức của nó mà **không cần biết chính xác kiểu của nó trước**.

**Ví dụ:**
```java
Animal[] animals = {new Cat(), new Dog()};
for (Animal animal : animals) {
    animal.makeSound(); // Sẽ gọi đúng phương thức của từng loại
}
```

**Lợi ích:**
- Cho phép xử lý các đối tượng khác nhau một cách thống nhất
- Tăng tính linh hoạt và khả năng mở rộng
- Giảm sự phụ thuộc vào các lớp cụ thể

---

## Quan Hệ Giữa Các Đối Tượng

Trong OOP, các lớp không tồn tại độc lập mà tương tác với nhau thông qua nhiều loại quan hệ:

### 1. Association (Kết Hợp)

**Định nghĩa:** Mối quan hệ mà một đối tượng **sử dụng hoặc tương tác** với đối tượng khác.

**Đặc điểm:**
- Có thể là quan hệ hai chiều
- Biểu thị bằng mũi tên trong UML
- Ví dụ: `Teacher` dạy `Student`

### 2. Dependency (Phụ Thuộc)

**Định nghĩa:** Dạng yếu hơn của association, **không có liên kết vĩnh viễn** giữa các đối tượng.

**Khi nào xảy ra:**
- Một đối tượng sử dụng đối tượng khác làm tham số phương thức
- Khởi tạo đối tượng tạm thời trong phương thức
- Thay đổi trong một đối tượng có thể yêu cầu sửa đổi ở đối tượng khác

### 3. Composition (Thành Phần)

**Định nghĩa:** Mối quan hệ **"toàn thể-bộ phận"** mạnh mẽ, nơi một đối tượng chứa được cấu thành từ một hoặc nhiều thành phần.

**Đặc điểm quan trọng:**
- **Sự tồn tại của thành phần phụ thuộc vào đối tượng chứa**
- Khi đối tượng chứa bị hủy, các thành phần cũng bị hủy
- Ví dụ: `House` chứa `Room` - khi nhà bị phá, các phòng cũng không còn

### 4. Aggregation (Tập Hợp)

**Định nghĩa:** Phiên bản **lỏng lẻo hơn** của composition, nơi một đối tượng giữ tham chiếu đến đối tượng khác mà **không kiểm soát vòng đời** của nó.

**Đặc điểm:**
- Các thành phần có thể tồn tại độc lập với đối tượng chứa
- Có thể là một phần của nhiều đối tượng chứa
- Ví dụ: `Department` chứa `Employee` - nhân viên có thể chuyển phòng ban

---

## 🎯 Tóm tắt chính

1. **OOP tổ chức mã** thành các đối tượng được tạo từ các lớp
2. **Bốn trụ cột** (Abstraction, Encapsulation, Inheritance, Polymorphism) là nền tảng của OOP
3. **Các mối quan hệ** giữa đối tượng (Association, Dependency, Composition, Aggregation) định hình cách chúng tương tác
4. **Hiểu rõ các khái niệm này** là điều kiện tiên quyết để áp dụng Design Patterns hiệu quả

---

**Tiếp theo:** [Design Principles](design-principles.md) - Tìm hiểu các nguyên tắc thiết kế cốt lõi
