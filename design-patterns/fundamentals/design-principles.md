# <PERSON><PERSON><PERSON> T<PERSON> Thiết Kế Phần Mềm Cốt Lõ<PERSON>

> Chương 6-14: Đặc trưng của thiết kế tốt và các nguyên tắc SOLID

## 📋 Mục lục

1. [Đặc Trưng của Thiết Kế Tốt](#đặc-trưng-của-thiết-kế-tốt)
2. [Ba Nguyên Tắc Thiết Kế Cơ Bản](#ba-nguyên-tắc-thiết-kế-cơ-bản)
3. [SOLID Principles](#solid-principles)

---

## Đặc Trưng của Thiết Kế Tốt

### Code Reuse (Tái Sử Dụng Mã)

**Tầm quan trọng:**
- Chi<PERSON>n lược quan trọng để **giảm chi phí phát triển**
- **Tăng tốc thời gian** đ<PERSON>a sản phẩm ra thị trường
- <PERSON><PERSON><PERSON><PERSON> thiểu lỗi thông qua việc sử dụng mã đã được kiểm thử

**Thách thức:**
- **K<PERSON>t nối chặt chẽ (tight coupling)** giữa các thành phần
- Phụ thuộc vào các lớp cụ thể thay vì giao diện
- Các hoạt động được mã hóa cứng

**Giải pháp:** Áp dụng các mẫu thiết kế để nâng cao tính linh hoạt của thành phần.

### Extensibility (Khả Năng Mở Rộng)

**Thực tế:** Thay đổi là khía cạnh **không thể tránh khỏi** của lập trình.

**Nguyên nhân thay đổi:**
- Yêu cầu phát triển theo thời gian
- Xu hướng thị trường mới
- Hiểu biết về vấn đề được cải thiện
- Công nghệ mới xuất hiện

**Mục tiêu:** Thiết kế hệ thống có thể **thích nghi dễ dàng** với các thay đổi mà không cần viết lại toàn bộ.

---

## Ba Nguyên Tắc Thiết Kế Cơ Bản

### 1. Encapsulate What Varies (Đóng Gói Phần Thay Đổi)

#### Nguyên tắc
**Xác định các khía cạnh của ứng dụng có thể thay đổi và tách chúng khỏi các phần cố định** để giảm thiểu tác động của những thay đổi đó.

#### Tư duy thiết kế
> Hãy tưởng tượng chương trình của bạn là một con tàu, và những thay đổi là những quả thủy lôi ẩn dưới nước. Bằng cách chia thân tàu thành các khoang độc lập có thể được niêm phong an toàn, bạn có thể hạn chế thiệt hại chỉ trong một khoang duy nhất.

#### Cách áp dụng
- **Cấp độ phương thức:** Tách logic có thể thay đổi vào phương thức riêng
- **Cấp độ lớp:** Tách hành vi và logic liên quan vào các lớp mới

#### Ưu điểm
- Giúp quản lý độ phức tạp
- Cải thiện khả năng bảo trì
- Dễ dàng kiểm thử các thay đổi

#### ⚠️ Cảnh báo
Việc cô lập quá mức có thể dẫn đến **over-engineering** - kiến trúc phức tạp và khó hiểu.

### 2. Program to an Interface, Not an Implementation

#### Nguyên tắc
**Lập trình theo giao diện (interface) thay vì triển khai cụ thể (concrete implementation)** để đạt được tính linh hoạt và khả năng mở rộng.

#### Tư duy thiết kế
> "Phụ thuộc vào các khái niệm trừu tượng, không phải vào các lớp cụ thể"

#### Cách thiết lập
1. **Xác định các phương thức** một đối tượng yêu cầu từ đối tượng khác
2. **Tạo giao diện hoặc lớp trừu tượng** cho các phương thức này
3. **Làm cho lớp phụ thuộc triển khai** giao diện này
4. **Làm cho lớp thứ hai phụ thuộc vào giao diện** thay vì lớp cụ thể

#### Ưu điểm
- Thiết kế linh hoạt, có thể **mở rộng mà không làm hỏng mã hiện có**
- Giảm sự ghép nối chặt chẽ
- Cho phép sử dụng **đa hình** hiệu quả
- Dễ dàng thay thế implementation

#### Nhược điểm
- Mã có thể trở nên phức tạp hơn ban đầu
- Yêu cầu cấu trúc bổ sung cho việc định nghĩa giao diện

### 3. Favor Composition Over Inheritance

#### Vấn đề với Kế thừa

**Hạn chế giao diện:**
- Lớp con không thể giảm giao diện của lớp cha
- Buộc phải triển khai tất cả phương thức trừu tượng

**Vấn đề đóng gói:**
- Kế thừa có thể phá vỡ tính đóng gói
- Lớp con truy cập vào chi tiết của lớp cha

**Kết nối chặt chẽ:**
- Lớp con bị ghép nối chặt chẽ với lớp cha
- Thay đổi lớp cha có thể làm hỏng lớp con

**Phân cấp phức tạp:**
- Có thể tạo ra hệ thống phân cấp kế thừa song song phức tạp

#### Ưu điểm của Composition

**Composition** nhấn mạnh mối quan hệ **"có một" (has a)** thay vì **"là một" (is a)**.

**Lợi ích:**
- Linh hoạt hơn để thay đổi hành vi trong thời gian chạy
- Cải thiện khả năng bảo trì
- Giảm sự phụ thuộc và ghép nối giữa các lớp
- Ngăn chặn sự bùng nổ của các lớp con

---

## SOLID Principles

SOLID là bộ 5 nguyên tắc thiết kế quan trọng, giúp tạo ra phần mềm dễ hiểu, linh hoạt và bảo trì.

### S - Single Responsibility Principle (SRP)

#### Nguyên tắc
**Một lớp chỉ nên có một lý do để thay đổi** - tập trung vào một phần chức năng duy nhất.

#### Mục tiêu
- Giảm độ phức tạp, đặc biệt khi chương trình phát triển
- Tăng tính gắn kết (cohesion) trong lớp

#### Ví dụ vi phạm
Lớp `Employee` vừa quản lý dữ liệu nhân viên vừa tạo báo cáo bảng chấm công.

#### Giải pháp
Tách chức năng báo cáo thành lớp `TimesheetReport` riêng biệt.

#### ⚠️ Cảnh báo
Có thể dẫn đến **sự gia tăng quá mức số lượng lớp**, gây phức tạp cho kiến trúc.

### O - Open/Closed Principle (OCP)

#### Nguyên tắc
**Các lớp nên mở để mở rộng nhưng đóng để sửa đổi.**

#### Định nghĩa
- **"Mở":** Có thể được mở rộng thông qua lớp con, thêm phương thức mới hoặc ghi đè hành vi
- **"Đóng":** Được định nghĩa đầy đủ với giao diện ổn định, không thay đổi trong tương lai

#### Tư duy
Thay đổi trực tiếp mã của một lớp có thể gây rủi ro. Thay vào đó, nên tạo lớp con để mở rộng.

#### Lưu ý
Nguyên tắc này **không áp dụng cho việc sửa lỗi** - lỗi nên được sửa trực tiếp trong lớp gốc.

#### ⚠️ Cảnh báo
Có thể dẫn đến **over-engineering** và sự gia tăng quá mức số lượng lớp.

### L - Liskov Substitution Principle (LSP)

#### Nguyên tắc
**Các đối tượng của lớp con nên có thể thay thế cho các đối tượng của lớp cha** mà không làm thay đổi tính đúng đắn của chương trình.

#### Các hướng dẫn chính

**Tương thích kiểu:**
- Tham số của lớp con phải khớp hoặc trừu tượng hơn lớp cha
- Kiểu trả về phải khớp hoặc là kiểu con của lớp cha

**Xử lý ngoại lệ:**
- Lớp con không được ném ngoại lệ mà lớp cha không mong đợi

**Bảo toàn điều kiện:**
- **Tiền điều kiện:** Không được tăng cường
- **Hậu điều kiện:** Không được làm suy yếu
- **Bất biến:** Phải được bảo toàn

#### Ví dụ vi phạm
Lớp `ReadOnlyDocument` ném ngoại lệ trong phương thức `save()` bị ghi đè.

### I - Interface Segregation Principle (ISP)

#### Nguyên tắc
**Client không nên bị buộc phụ thuộc vào các phương thức mà họ không sử dụng.**

#### Tư duy
- Biến các giao diện "fat" thành các giao diện chi tiết và cụ thể
- Một lớp có thể triển khai nhiều giao diện đồng thời
- Client chỉ triển khai các phương thức thực sự cần

#### Ưu điểm
- Nâng cao tính mô đun hóa
- Tránh buộc client phụ thuộc vào những thứ không cần thiết

#### ⚠️ Cảnh báo
Phân chia giao diện quá mức có thể dẫn đến **độ phức tạp không cần thiết**.

### D - Dependency Inversion Principle (DIP)

#### Nguyên tắc
**Các lớp cấp cao không nên phụ thuộc vào các lớp cấp thấp. Cả hai nên phụ thuộc vào các abstraction.**

#### Khái niệm
- **Lớp cấp cao:** Chứa logic nghiệp vụ phức tạp
- **Lớp cấp thấp:** Xử lý các thao tác cơ bản (database, file system)

#### Hướng dẫn áp dụng
1. **Định nghĩa giao diện** cho các hoạt động cấp thấp
2. **Lớp cấp cao phụ thuộc vào giao diện** thay vì lớp cụ thể
3. **Lớp cấp thấp triển khai giao diện** → **đảo ngược hướng phụ thuộc**

#### Ưu điểm
- Tăng khả năng bảo trì và kiểm thử
- Cải thiện tính linh hoạt
- Dễ dàng thay đổi implementation

---

## 🎯 Tóm tắt chính

1. **Thiết kế tốt** tập trung vào tái sử dụng mã và khả năng mở rộng
2. **Ba nguyên tắc cơ bản** giúp tạo nền tảng cho thiết kế linh hoạt
3. **SOLID principles** cung cấp hướng dẫn cụ thể để viết mã chất lượng cao
4. **Cân bằng** giữa việc tuân thủ nguyên tắc và tránh over-engineering

---

**Tiếp theo:** [Pattern Overview](pattern-overview.md) - Giới thiệu về Design Patterns
