# Giới Thiệu Về Design Patterns

> Chương 4-5: Design Pattern là gì và tại sao nên học?

## 📋 Mục lục

1. [Design Pattern là gì?](#design-pattern-là-gì)
2. [Tại sao nên học Design Patterns?](#tại-sao-nên-học-design-patterns)
3. [Phân loại Design Patterns](#phân-loại-design-patterns)
4. [Cách sử dụng hiệu quả](#cách-sử-dụng-hiệu-quả)

---

## Design Pattern là gì?

### Định nghĩa

**Design Patterns** là các **giải pháp điển hình cho các vấn đề thường gặp** trong thiết kế phần mềm, đóng vai trò như **bản thiết kế có thể tùy chỉnh** cho các vấn đề thiết kế lặp đi lặp lại.

### Khác biệt với Algorithm

| Aspect | Algorithm | Design Pattern |
|--------|-----------|----------------|
| **Mục đích** | Cung cấp hành động chính xác để đạt mục tiêu | Mô tả cấu trúc và mối quan hệ cấp cao |
| **Chi tiết** | Các bước cụ thể, rõ ràng | Khái niệm tổng quát cần điều chỉnh |
| **Áp dụng** | Triển khai trực tiếp | Cần thích nghi với ngữ cảnh cụ thể |

### Cấu trúc của một Pattern

Mỗi Design Pattern thường bao gồm:

#### 1. Intent (Mục đích)
- **Vấn đề** mà pattern giải quyết
- **Giải pháp** mà pattern cung cấp
- Mô tả ngắn gọn về pattern

#### 2. Motivation (Động lực)
- **Ngữ cảnh** cụ thể khi cần sử dụng pattern
- **Bản chất vấn đề** và lý do cần giải quyết
- Ví dụ thực tế minh họa

#### 3. Structure (Cấu trúc)
- **Mối quan hệ giữa các thành phần** trong pattern
- Sơ đồ UML hoặc class diagram
- Vai trò của từng thành phần

#### 4. Code Example (Ví dụ mã)
- **Triển khai cụ thể** trong một ngôn ngữ lập trình
- Minh họa cách áp dụng pattern
- Code có thể chạy được

### Nguồn gốc lịch sử

#### Christopher Alexander (Kiến trúc)
- Khái niệm ban đầu từ thiết kế đô thị và kiến trúc
- Ý tưởng về "ngôn ngữ pattern" để giải quyết các vấn đề thiết kế

#### Gang of Four (Lập trình)
**Tác giả:** Erich Gamma, John Vlissides, Ralph Johnson, và Richard Helm

**Cuốn sách:** "Design Patterns: Elements of Reusable Object-Oriented Software" (1994)

**Đóng góp:**
- Phổ biến khái niệm Design Patterns trong lập trình
- Định nghĩa 23 patterns cổ điển
- Tạo nền tảng cho cộng đồng phát triển patterns

---

## Tại sao nên học Design Patterns?

### 1. Bộ Công Cụ Giải Pháp (Toolkit of Solutions)

**Lợi ích:**
- Cung cấp **giải pháp đã được kiểm nghiệm** cho các vấn đề thiết kế phổ biến
- Nâng cao khả năng giải quyết vấn đề thông qua **nguyên tắc thiết kế hướng đối tượng**
- Giúp tránh "phát minh lại bánh xe"

**Ví dụ:**
- Cần tạo đối tượng phức tạp? → Sử dụng **Builder Pattern**
- Cần thông báo cho nhiều đối tượng? → Sử dụng **Observer Pattern**
- Cần kiểm soát quyền truy cập? → Sử dụng **Proxy Pattern**

### 2. Ngôn Ngữ Chung (Common Language)

**Tầm quan trọng:**
- Thiết lập **vốn từ vựng chung** giữa các thành viên trong nhóm
- Cải thiện **giao tiếp và sự rõ ràng** trong thảo luận kỹ thuật
- Giảm thời gian giải thích thiết kế

**Ví dụ thực tế:**
```
Thay vì: "Chúng ta cần tạo một lớp để tạo ra các đối tượng khác nhau 
         tùy thuộc vào điều kiện..."

Nói: "Chúng ta sử dụng Factory Pattern ở đây."
```

### 3. Cải thiện kỹ năng thiết kế

**Lợi ích:**
- Hiểu sâu hơn về **các nguyên tắc thiết kế OOP**
- Học cách **cân bằng giữa các trade-offs** trong thiết kế
- Phát triển **tư duy kiến trúc** phần mềm

### ⚠️ Cảnh báo quan trọng

**Tránh Pattern Obsession:**
- Việc **chỉ dựa vào patterns có thể dẫn đến tự mãn** và thiếu đổi mới
- **Lập trình viên hiệu quả nhất** điều chỉnh các phương pháp theo ngữ cảnh độc đáo
- Patterns là **công cụ**, không phải **mục tiêu cuối cùng**

**💡 Key Point:** Hiểu Design Patterns như một bộ công cụ, không phải giáo điều.

---

## Phân loại Design Patterns

Design Patterns được phân loại theo **mục đích** thành 3 nhóm chính:

### 1. Creational Patterns (Mẫu Khởi tạo)

**Mục đích:** Tập trung vào **cách tạo đối tượng** để tăng tính linh hoạt và khả năng tái sử dụng.

**Các patterns chính:**
- **Factory Method** - Tạo đối tượng thông qua interface
- **Abstract Factory** - Tạo họ đối tượng liên quan
- **Builder** - Xây dựng đối tượng phức tạp từng bước
- **Prototype** - Sao chép đối tượng hiện có
- **Singleton** - Đảm bảo chỉ có một instance

**Khi nào sử dụng:**
- Cần kiểm soát quá trình tạo đối tượng
- Muốn ẩn logic tạo đối tượng phức tạp
- Cần đảm bảo tính nhất quán khi tạo đối tượng

### 2. Structural Patterns (Mẫu Cấu trúc)

**Mục đích:** Mô tả **cách lắp ráp các đối tượng và lớp** thành các cấu trúc lớn hơn, linh hoạt và hiệu quả.

**Các patterns chính:**
- **Adapter** - Kết nối các interface không tương thích
- **Bridge** - Tách abstraction khỏi implementation
- **Composite** - Tổ chức đối tượng thành cấu trúc cây
- **Decorator** - Thêm chức năng động cho đối tượng
- **Facade** - Cung cấp interface đơn giản cho hệ thống phức tạp
- **Flyweight** - Tối ưu hóa bộ nhớ bằng chia sẻ dữ liệu
- **Proxy** - Kiểm soát quyền truy cập đến đối tượng

**Khi nào sử dụng:**
- Cần tổ chức mối quan hệ giữa các đối tượng
- Muốn thêm chức năng mà không thay đổi cấu trúc
- Cần tối ưu hóa hiệu suất hoặc bộ nhớ

### 3. Behavioral Patterns (Mẫu Hành vi)

**Mục đích:** Giải quyết **giao tiếp và phân công trách nhiệm** giữa các đối tượng.

**Các patterns chính:**
- **Chain of Responsibility** - Chuyển yêu cầu qua chuỗi handlers
- **Command** - Đóng gói yêu cầu thành đối tượng
- **Iterator** - Duyệt qua collection mà không lộ cấu trúc
- **Mediator** - Định nghĩa cách các đối tượng tương tác
- **Memento** - Lưu và khôi phục trạng thái đối tượng
- **Observer** - Thông báo thay đổi cho nhiều đối tượng
- **State** - Thay đổi hành vi dựa trên trạng thái
- **Strategy** - Đóng gói thuật toán và làm chúng có thể hoán đổi
- **Template Method** - Định nghĩa khung thuật toán
- **Visitor** - Thêm hoạt động mới mà không thay đổi cấu trúc

**Khi nào sử dụng:**
- Cần quản lý giao tiếp phức tạp giữa các đối tượng
- Muốn tách biệt thuật toán khỏi cấu trúc dữ liệu
- Cần xử lý các trạng thái hoặc hành vi động

### 🔄 Lưu ý về phân loại

**Tính linh hoạt:**
- Một số patterns có thể **pha trộn các khía cạnh** của nhiều loại
- Phân loại này **không tuyệt đối** và có thể **giảm tính linh hoạt** trong thực tế
- Quan trọng hơn là hiểu **bản chất và cách áp dụng** của từng pattern

---

## Cách sử dụng hiệu quả

### 1. Học tuần tự

**Bước 1:** Nắm vững nền tảng OOP và các nguyên tắc thiết kế
**Bước 2:** Bắt đầu với Creational Patterns (đơn giản nhất)
**Bước 3:** Tiếp tục với Structural Patterns
**Bước 4:** Kết thúc với Behavioral Patterns (phức tạp nhất)

### 2. Thực hành với ví dụ thực tế

- **Không chỉ đọc lý thuyết** - hãy code các ví dụ
- **Áp dụng vào dự án cá nhân** để hiểu sâu hơn
- **Thử nghiệm với các ngôn ngữ khác nhau**

### 3. Hiểu khi nào KHÔNG nên dùng

- **Tránh over-engineering** - không áp dụng pattern khi không cần thiết
- **Đánh giá trade-offs** - mỗi pattern có ưu nhược điểm riêng
- **Ưu tiên đơn giản** - giải pháp đơn giản thường tốt hơn

### 4. Kết hợp với nhau

- Nhiều patterns có thể **làm việc cùng nhau**
- Hiểu **mối quan hệ giữa các patterns**
- Học cách **chọn pattern phù hợp** cho từng tình huống

---

## 🎯 Tóm tắt chính

1. **Design Patterns** là giải pháp đã được kiểm nghiệm cho các vấn đề thiết kế phổ biến
2. **Lợi ích chính** là cung cấp bộ công cụ giải pháp và ngôn ngữ chung
3. **Ba nhóm patterns** phục vụ các mục đích khác nhau trong thiết kế
4. **Sử dụng như công cụ**, không phải giáo điều - cân bằng giữa áp dụng pattern và giữ đơn giản

---

**Tiếp theo:** Khám phá chi tiết từng pattern bắt đầu với [Creational Patterns](../patterns/creational/)
