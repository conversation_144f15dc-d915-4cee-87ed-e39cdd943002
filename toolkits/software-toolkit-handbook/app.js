// Software Engineer Toolkit - Interactive JavaScript

class ToolkitApp {
    constructor() {
        this.data = {
            categories: [
                {
                    "id": "books",
                    "name": "<PERSON><PERSON><PERSON>",
                    "icon": "📚",
                    "items": [
                        {
                            "title": "Clean Code",
                            "author": "<PERSON>",
                            "description": "Cách viết code dễ đọc, b<PERSON><PERSON> tr<PERSON>. Principles, patterns, practices",
                            "difficulty": "beginner",
                            "type": "book",
                            "essential": true
                        },
                        {
                            "title": "The Pragmatic Programmer",
                            "author": "<PERSON> & David Thomas", 
                            "description": "Mindset và career development. Practical advice for everyday programming",
                            "difficulty": "beginner",
                            "type": "book",
                            "essential": true
                        },
                        {
                            "title": "Code Complete",
                            "author": "<PERSON>",
                            "description": "Comprehensive guide for software construction. Best practices cho quality code",
                            "difficulty": "intermediate",
                            "type": "book",
                            "essential": true
                        },
                        {
                            "title": "Design Patterns",
                            "author": "Gang of Four",
                            "description": "23 foundational design patterns. Object-oriented programming mastery",
                            "difficulty": "intermediate", 
                            "type": "book",
                            "essential": true
                        },
                        {
                            "title": "Refactoring",
                            "author": "<PERSON>",
                            "description": "Code improvement techniques. Maintaining legacy codebases",
                            "difficulty": "intermediate",
                            "type": "book",
                            "essential": true
                        },
                        {
                            "title": "Designing Data-Intensive Applications",
                            "author": "Martin Kleppmann",
                            "description": "Distributed systems and scalability. Modern application architecture",
                            "difficulty": "advanced",
                            "type": "book",
                            "essential": true
                        },
                        {
                            "title": "System Design Interview",
                            "author": "Alex Xu",
                            "description": "System architecture và scalability. Interview preparation",
                            "difficulty": "advanced",
                            "type": "book",
                            "essential": false
                        },
                        {
                            "title": "Domain-Driven Design",
                            "author": "Eric Evans", 
                            "description": "Complex business logic handling. Software architecture patterns",
                            "difficulty": "advanced",
                            "type": "book",
                            "essential": false
                        }
                    ]
                },
                {
                    "id": "tools",
                    "name": "Công Cụ Phát Triển",
                    "icon": "🛠️",
                    "items": [
                        {
                            "title": "Visual Studio Code",
                            "description": "Extensible, cross-platform code editor",
                            "category": "IDE",
                            "difficulty": "beginner",
                            "type": "tool",
                            "essential": true
                        },
                        {
                            "title": "Git",
                            "description": "Industry standard version control system",
                            "category": "Version Control",
                            "difficulty": "beginner",
                            "type": "tool",
                            "essential": true
                        },
                        {
                            "title": "Docker",
                            "description": "Containerization platform for consistent environments",
                            "category": "DevOps",
                            "difficulty": "intermediate",
                            "type": "tool",
                            "essential": true
                        },
                        {
                            "title": "PostgreSQL",
                            "description": "Advanced relational database system",
                            "category": "Database",
                            "difficulty": "intermediate",
                            "type": "tool",
                            "essential": true
                        },
                        {
                            "title": "AWS",
                            "description": "Comprehensive cloud computing platform",
                            "category": "Cloud",
                            "difficulty": "intermediate",
                            "type": "platform",
                            "essential": false
                        },
                        {
                            "title": "Kubernetes",
                            "description": "Container orchestration platform",
                            "category": "DevOps",
                            "difficulty": "advanced",
                            "type": "platform",
                            "essential": false
                        }
                    ]
                },
                {
                    "id": "architecture",
                    "name": "Kiến Trúc và Thiết Kế",
                    "icon": "🏗️",
                    "items": [
                        {
                            "title": "Microservices Architecture",
                            "description": "Distributed system design pattern",
                            "difficulty": "advanced",
                            "type": "concept",
                            "essential": true
                        },
                        {
                            "title": "REST API Design",
                            "description": "RESTful web service architecture",
                            "difficulty": "intermediate",
                            "type": "concept",
                            "essential": true
                        },
                        {
                            "title": "Domain-Driven Design (DDD)",
                            "description": "Software design approach for complex domains",
                            "difficulty": "advanced", 
                            "type": "methodology",
                            "essential": false
                        },
                        {
                            "title": "Event-Driven Architecture",
                            "description": "Asynchronous communication pattern",
                            "difficulty": "advanced",
                            "type": "pattern",
                            "essential": false
                        }
                    ]
                },
                {
                    "id": "testing",
                    "name": "Testing & QA",
                    "icon": "🧪",
                    "items": [
                        {
                            "title": "Unit Testing",
                            "description": "Individual component testing methodology",
                            "difficulty": "beginner",
                            "type": "methodology",
                            "essential": true
                        },
                        {
                            "title": "Jest",
                            "description": "JavaScript testing framework",
                            "difficulty": "beginner",
                            "type": "tool",
                            "essential": true
                        },
                        {
                            "title": "Test-Driven Development (TDD)",
                            "description": "Development methodology with tests first",
                            "difficulty": "intermediate",
                            "type": "methodology", 
                            "essential": true
                        },
                        {
                            "title": "Cypress",
                            "description": "End-to-end testing framework",
                            "difficulty": "intermediate",
                            "type": "tool",
                            "essential": false
                        }
                    ]
                },
                {
                    "id": "ai-tools",
                    "name": "AI Tools & Modern Dev",
                    "icon": "🤖",
                    "items": [
                        {
                            "title": "GitHub Copilot",
                            "description": "AI pair programming assistant",
                            "difficulty": "beginner",
                            "type": "ai-tool",
                            "essential": true
                        },
                        {
                            "title": "ChatGPT/Claude", 
                            "description": "Conversational AI for coding assistance",
                            "difficulty": "beginner",
                            "type": "ai-tool",
                            "essential": true
                        },
                        {
                            "title": "Cursor AI",
                            "description": "AI-powered code editor",
                            "difficulty": "intermediate",
                            "type": "ai-tool",
                            "essential": false
                        },
                        {
                            "title": "Tabnine",
                            "description": "AI code completion assistant",
                            "difficulty": "beginner",
                            "type": "ai-tool",
                            "essential": false
                        }
                    ]
                }
            ],
            roadmap: [
                {
                    "phase": "Fundamentals",
                    "duration": "3-6 months",
                    "description": "Core programming concepts and practices",
                    "topics": ["Master one programming language", "Data structures & algorithms", "Version control (Git)", "Clean coding principles"]
                },
                {
                    "phase": "Intermediate", 
                    "duration": "6-12 months",
                    "description": "System design and architecture",
                    "topics": ["Database design", "API development", "Testing strategies", "Basic system design"]
                },
                {
                    "phase": "Advanced",
                    "duration": "12+ months", 
                    "description": "Distributed systems and leadership",
                    "topics": ["Distributed systems", "Cloud expertise", "DevOps automation", "Team leadership"]
                }
            ]
        };
        
        this.userProgress = this.loadProgress();
        this.bookmarks = this.loadBookmarks();
        this.currentSection = 'dashboard';
        this.currentFilter = 'all';
        this.searchQuery = '';
        
        this.init();
    }

    init() {
        try {
            this.loadTheme();
            this.setupEventListeners();
            this.renderDashboard();
            this.renderRoadmap();
            this.renderAllSections();
            this.updateProgress();
            this.setupSearch();
        } catch (error) {
            console.warn('Software Toolkit initialization error:', error);
        }
    }

    setupSearch() {
        try {
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.querySelector('.search-btn');

            if (!searchInput || !searchBtn) {
                console.warn('Search elements not found');
                return;
            }

            let searchTimeout;
            
            const performSearchHandler = (query) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(query);
                }, 300);
            };

            searchInput.addEventListener('input', (e) => {
                performSearchHandler(e.target.value);
            });

            searchBtn.addEventListener('click', () => {
                this.performSearch(searchInput.value);
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch(searchInput.value);
                }
            });
            
            console.log('Search functionality initialized');
        } catch (error) {
            console.warn('Search setup failed:', error);
        }
    }

    performSearch(query) {
        try {
            if (!query || query.length < 2) {
                this.clearSearch();
                return;
            }

            const results = [];
            const queryLower = query.toLowerCase();
            
            // Search through all categories and items
            this.data.categories.forEach(category => {
                category.items.forEach(item => {
                    const title = item.title?.toLowerCase() || '';
                    const description = item.description?.toLowerCase() || '';
                    const author = item.author?.toLowerCase() || '';
                    const category_name = category.name?.toLowerCase() || '';
                    
                    if (title.includes(queryLower) || 
                        description.includes(queryLower) || 
                        author.includes(queryLower) ||
                        category_name.includes(queryLower)) {
                        results.push({
                            ...item,
                            categoryId: category.id,
                            categoryName: category.name
                        });
                    }
                });
            });
            
            this.displaySearchResults(results, query);
        } catch (error) {
            console.warn('Search execution failed:', error);
        }
    }

    displaySearchResults(results, query) {
        try {
            // Switch to search results view
            this.navigateToSection('search');
            
            // Update search results display
            const searchSection = document.getElementById('search-section');
            if (searchSection) {
                let html = `
                    <div class="search-results">
                        <h2>Search Results for "${query}" (${results.length} found)</h2>
                `;
                
                if (results.length > 0) {
                    html += '<div class="items-grid">';
                    results.forEach(item => {
                        html += this.renderItemCard(item);
                    });
                    html += '</div>';
                } else {
                    html += '<p class="no-results">No results found. Try different keywords.</p>';
                }
                
                html += '</div>';
                searchSection.innerHTML = html;
            }
        } catch (error) {
            console.warn('Search results display failed:', error);
        }
    }

    clearSearch() {
        try {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
            }
            this.searchQuery = '';
        } catch (error) {
            console.warn('Clear search failed:', error);
        }
    }

    loadTheme() {
        // Set light theme as default per project specification
        document.body.dataset.colorScheme = 'light';
        
        // Listen for theme changes from parent (unified toolkit)
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'THEME_CHANGE') {
                document.body.dataset.colorScheme = 'light'; // Always use light theme
            }
        });
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-section-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.closest('button').dataset.section;
                if (section) {
                    this.navigateToSection(section);
                }
            });
        });

        // Theme toggle removed - using light theme only per project specification

        // Search - removed duplicate listeners since setupSearch handles this
        // Search functionality is handled in setupSearch()

        // Quick actions
        const randomBtn = document.getElementById('randomRecommendation');
        const bookmarksBtn = document.getElementById('showBookmarks');
        const quickStartBtn = document.getElementById('quickStart');
        
        if (randomBtn) randomBtn.addEventListener('click', this.showRandomRecommendation.bind(this));
        if (bookmarksBtn) bookmarksBtn.addEventListener('click', () => this.showBookmarks());
        if (quickStartBtn) quickStartBtn.addEventListener('click', () => this.showModal('quickStartModal'));

        // Data management
        const exportBtn = document.getElementById('exportData');
        const resetBtn = document.getElementById('resetProgress');
        
        if (exportBtn) exportBtn.addEventListener('click', this.exportData.bind(this));
        if (resetBtn) resetBtn.addEventListener('click', this.resetProgress.bind(this));

        // Modal handling
        document.querySelectorAll('.modal-close, .modal-backdrop').forEach(element => {
            element.addEventListener('click', (e) => {
                e.preventDefault();
                const modalId = e.target.dataset.modal || e.target.closest('[data-modal]')?.dataset.modal;
                if (modalId) {
                    this.hideModal(modalId);
                }
            });
        });

        // Filter buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-btn')) {
                e.preventDefault();
                this.handleFilter(e);
            }
        });

        // Sidebar toggle for mobile
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', this.toggleSidebar.bind(this));
        }
    }

    navigateToSection(sectionId) {
        console.log('Navigating to section:', sectionId);
        
        // Update active nav button
        document.querySelectorAll('.nav-section-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const targetBtn = document.querySelector(`[data-section="${sectionId}"]`);
        if (targetBtn) {
            targetBtn.classList.add('active');
        }

        // Show section
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        const targetSection = document.getElementById(`${sectionId}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        this.currentSection = sectionId;
        this.currentFilter = 'all';
        
        // Reset filters
        if (sectionId !== 'dashboard' && sectionId !== 'roadmap') {
            this.resetFilters(sectionId);
        }

        // Clear search when navigating
        this.clearSearch();
    }

    resetFilters(sectionId) {
        const section = document.getElementById(`${sectionId}-section`);
        if (section) {
            const filterBtns = section.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.filter === 'all') {
                    btn.classList.add('active');
                }
            });
            this.currentFilter = 'all';
            this.filterItems(section, 'all');
        }
    }

    handleFilter(e) {
        const filter = e.target.dataset.filter;
        const section = e.target.closest('.content-section');
        
        if (!filter || !section) return;
        
        // Update active filter button
        section.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        e.target.classList.add('active');

        this.currentFilter = filter;
        this.filterItems(section, filter);
    }

    filterItems(section, filter) {
        const items = section.querySelectorAll('.item-card');
        
        items.forEach(item => {
            const difficulty = item.dataset.difficulty;
            const essential = item.dataset.essential === 'true';
            
            let show = true;
            
            if (filter === 'all') {
                show = true;
            } else if (filter === 'essential') {
                show = essential;
            } else {
                show = difficulty === filter;
            }
            
            // Also apply search filter if there's an active search
            if (show && this.searchQuery) {
                const title = item.querySelector('.item-title')?.textContent.toLowerCase() || '';
                const description = item.querySelector('.item-description')?.textContent.toLowerCase() || '';
                const author = item.querySelector('.item-author')?.textContent.toLowerCase() || '';
                
                show = title.includes(this.searchQuery) || 
                       description.includes(this.searchQuery) || 
                       author.includes(this.searchQuery);
            }
            
            item.style.display = show ? 'block' : 'none';
        });
    }

    handleSearch() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;
        
        const query = searchInput.value.toLowerCase().trim();
        this.searchQuery = query;
        
        console.log('Searching for:', query);
        
        if (!query) {
            this.showAllItems();
            return;
        }

        // Search across all categories
        this.data.categories.forEach(category => {
            const section = document.getElementById(`${category.id}-section`);
            if (section) {
                const items = section.querySelectorAll('.item-card');
                
                items.forEach(item => {
                    const title = item.querySelector('.item-title')?.textContent.toLowerCase() || '';
                    const description = item.querySelector('.item-description')?.textContent.toLowerCase() || '';
                    const author = item.querySelector('.item-author')?.textContent.toLowerCase() || '';
                    
                    const matches = title.includes(query) || 
                                  description.includes(query) || 
                                  author.includes(query);
                    
                    item.style.display = matches ? 'block' : 'none';
                });
            }
        });
    }

    clearSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
        }
        this.searchQuery = '';
        this.showAllItems();
    }

    showAllItems() {
        document.querySelectorAll('.item-card').forEach(item => {
            item.style.display = 'block';
        });
    }

    renderDashboard() {
        const totalItems = this.getTotalItems();
        const completedItems = this.getCompletedItems();
        const bookmarkedItems = Object.keys(this.bookmarks).length;
        const overallProgress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

        const totalEl = document.getElementById('totalItems');
        const completedEl = document.getElementById('completedItems');
        const bookmarkedEl = document.getElementById('bookmarkedItems');
        const progressEl = document.getElementById('overallProgressPercent');

        if (totalEl) totalEl.textContent = totalItems;
        if (completedEl) completedEl.textContent = completedItems;
        if (bookmarkedEl) bookmarkedEl.textContent = bookmarkedItems;
        if (progressEl) progressEl.textContent = `${overallProgress}%`;
    }

    renderRoadmap() {
        const container = document.getElementById('roadmapContainer');
        if (!container) return;
        
        container.innerHTML = this.data.roadmap.map(phase => `
            <div class="roadmap-phase">
                <div class="phase-header">
                    <h3 class="phase-title">${phase.phase}</h3>
                    <span class="phase-duration">${phase.duration}</span>
                </div>
                <p class="phase-description">${phase.description}</p>
                <div class="phase-topics">
                    ${phase.topics.map(topic => `
                        <div class="topic-item">${topic}</div>
                    `).join('')}
                </div>
            </div>
        `).join('');
    }

    renderAllSections() {
        this.data.categories.forEach(category => {
            this.renderSection(category);
        });
    }

    renderSection(category) {
        const grid = document.getElementById(`${category.id}-grid`);
        if (!grid) return;
        
        grid.innerHTML = category.items.map((item, index) => {
            const itemId = `${category.id}-${index}`;
            const isCompleted = this.userProgress[itemId] || false;
            const isBookmarked = this.bookmarks[itemId] || false;
            
            return `
                <div class="item-card ${isCompleted ? 'completed' : ''}" 
                     data-difficulty="${item.difficulty}" 
                     data-essential="${item.essential}"
                     data-item-id="${itemId}">
                    <div class="item-header">
                        <div>
                            <h4 class="item-title">${item.title}</h4>
                            ${item.author ? `<p class="item-author">bởi ${item.author}</p>` : ''}
                        </div>
                        <div class="item-actions">
                            <button class="action-btn bookmark-btn ${isBookmarked ? 'active' : ''}" 
                                    data-item="${itemId}" title="Bookmark">⭐</button>
                            <button class="action-btn complete-btn ${isCompleted ? 'active' : ''}" 
                                    data-item="${itemId}" title="Đánh dấu hoàn thành">✅</button>
                        </div>
                    </div>
                    <p class="item-description">${item.description}</p>
                    <div class="item-footer">
                        <div class="item-tags">
                            <span class="item-tag tag-difficulty-${item.difficulty}">${this.getDifficultyLabel(item.difficulty)}</span>
                            ${item.essential ? '<span class="item-tag tag-essential">Thiết Yếu</span>' : ''}
                            <span class="item-tag tag-type">${this.getTypeLabel(item.type)}</span>
                            ${item.category ? `<span class="item-tag tag-type">${item.category}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        // Add event listeners for action buttons
        grid.querySelectorAll('.complete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleComplete(e.target.dataset.item);
            });
        });

        grid.querySelectorAll('.bookmark-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleBookmark(e.target.dataset.item);
            });
        });
    }

    getDifficultyLabel(difficulty) {
        const labels = {
            'beginner': 'Cơ Bản',
            'intermediate': 'Trung Cấp',
            'advanced': 'Nâng Cao'
        };
        return labels[difficulty] || difficulty;
    }

    getTypeLabel(type) {
        const labels = {
            'book': 'Sách',
            'tool': 'Công Cụ',
            'platform': 'Nền Tảng',
            'concept': 'Khái Niệm',
            'methodology': 'Phương Pháp',
            'pattern': 'Mô Hình',
            'ai-tool': 'AI Tool'
        };
        return labels[type] || type;
    }

    toggleComplete(itemId) {
        if (!itemId) return;
        
        this.userProgress[itemId] = !this.userProgress[itemId];
        this.saveProgress();
        this.updateProgress();
        this.updateItemDisplay(itemId);
        
        console.log('Toggled completion for:', itemId, this.userProgress[itemId]);
    }

    toggleBookmark(itemId) {
        if (!itemId) return;
        
        if (this.bookmarks[itemId]) {
            delete this.bookmarks[itemId];
        } else {
            this.bookmarks[itemId] = true;
        }
        this.saveBookmarks();
        this.updateItemDisplay(itemId);
        this.renderDashboard();
        
        console.log('Toggled bookmark for:', itemId, this.bookmarks[itemId]);
    }

    updateItemDisplay(itemId) {
        const card = document.querySelector(`[data-item-id="${itemId}"]`);
        if (!card) return;
        
        const completeBtn = card.querySelector('.complete-btn');
        const bookmarkBtn = card.querySelector('.bookmark-btn');
        
        // Update completion status
        if (this.userProgress[itemId]) {
            card.classList.add('completed');
            if (completeBtn) completeBtn.classList.add('active');
        } else {
            card.classList.remove('completed');
            if (completeBtn) completeBtn.classList.remove('active');
        }
        
        // Update bookmark status
        if (this.bookmarks[itemId]) {
            if (bookmarkBtn) bookmarkBtn.classList.add('active');
        } else {
            if (bookmarkBtn) bookmarkBtn.classList.remove('active');
        }
    }

    updateProgress() {
        // Update section progress badges
        this.data.categories.forEach(category => {
            const progress = this.getCategoryProgress(category.id);
            const badge = document.getElementById(`${category.id}-progress`);
            if (badge) {
                badge.textContent = `${progress}%`;
            }
        });

        // Update overall progress bar
        const overallProgress = this.getOverallProgress();
        const progressBar = document.getElementById('overallProgress');
        if (progressBar) {
            progressBar.style.width = `${overallProgress}%`;
        }
        
        // Update dashboard
        this.renderDashboard();
    }

    getCategoryProgress(categoryId) {
        const category = this.data.categories.find(cat => cat.id === categoryId);
        if (!category) return 0;
        
        const totalItems = category.items.length;
        const completedItems = category.items.filter((item, index) => {
            const itemId = `${categoryId}-${index}`;
            return this.userProgress[itemId];
        }).length;
        
        return totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
    }

    getOverallProgress() {
        const totalItems = this.getTotalItems();
        const completedItems = this.getCompletedItems();
        return totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
    }

    getTotalItems() {
        return this.data.categories.reduce((total, category) => total + category.items.length, 0);
    }

    getCompletedItems() {
        return Object.values(this.userProgress).filter(Boolean).length;
    }

    showRandomRecommendation() {
        const allItems = [];
        
        this.data.categories.forEach(category => {
            category.items.forEach((item, index) => {
                const itemId = `${category.id}-${index}`;
                if (!this.userProgress[itemId]) { // Only incomplete items
                    allItems.push({
                        ...item,
                        categoryName: category.name,
                        categoryIcon: category.icon,
                        itemId
                    });
                }
            });
        });

        const contentEl = document.getElementById('recommendationContent');
        if (!contentEl) return;

        if (allItems.length === 0) {
            contentEl.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <h4>🎉 Chúc mừng!</h4>
                    <p>Bạn đã hoàn thành tất cả tài nguyên!</p>
                </div>
            `;
        } else {
            const randomItem = allItems[Math.floor(Math.random() * allItems.length)];
            contentEl.innerHTML = `
                <div class="item-card">
                    <div class="item-header">
                        <div>
                            <h4 class="item-title">${randomItem.title}</h4>
                            ${randomItem.author ? `<p class="item-author">bởi ${randomItem.author}</p>` : ''}
                        </div>
                        <div style="font-size: 24px;">${randomItem.categoryIcon}</div>
                    </div>
                    <p class="item-description">${randomItem.description}</p>
                    <div class="item-footer">
                        <div class="item-tags">
                            <span class="item-tag tag-difficulty-${randomItem.difficulty}">${this.getDifficultyLabel(randomItem.difficulty)}</span>
                            ${randomItem.essential ? '<span class="item-tag tag-essential">Thiết Yếu</span>' : ''}
                            <span class="item-tag tag-type">${this.getTypeLabel(randomItem.type)}</span>
                        </div>
                    </div>
                    <div style="margin-top: 16px; text-align: center;">
                        <button class="btn btn--primary" onclick="window.app.toggleComplete('${randomItem.itemId}'); window.app.hideModal('recommendationModal');">
                            ✅ Đánh Dấu Hoàn Thành
                        </button>
                    </div>
                </div>
            `;
        }
        
        this.showModal('recommendationModal');
    }

    showBookmarks() {
        const bookmarkedItems = [];
        
        this.data.categories.forEach(category => {
            category.items.forEach((item, index) => {
                const itemId = `${category.id}-${index}`;
                if (this.bookmarks[itemId]) {
                    bookmarkedItems.push({
                        ...item,
                        categoryName: category.name,
                        categoryIcon: category.icon,
                        categoryId: category.id
                    });
                }
            });
        });

        if (bookmarkedItems.length > 0) {
            // Navigate to the first bookmarked item's category
            const firstBookmarkedCategory = bookmarkedItems[0].categoryId;
            this.navigateToSection(firstBookmarkedCategory);
        } else {
            alert('Bạn chưa bookmark tài nguyên nào!');
        }
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    // Theme toggle removed - using light theme only per project specification
    // toggleTheme() method removed

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('open');
        }
    }

    exportData() {
        const data = {
            progress: this.userProgress,
            bookmarks: this.bookmarks,
            exportDate: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'toolkit-progress.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    resetProgress() {
        if (confirm('Bạn có chắc muốn reset tất cả tiến độ? Hành động này không thể hoàn tác.')) {
            this.userProgress = {};
            this.bookmarks = {};
            this.saveProgress();
            this.saveBookmarks();
            this.updateProgress();
            this.renderAllSections();
            alert('Đã reset thành công!');
        }
    }

    loadProgress() {
        try {
            return JSON.parse(localStorage.getItem('toolkit-progress') || '{}');
        } catch {
            return {};
        }
    }

    saveProgress() {
        try {
            localStorage.setItem('toolkit-progress', JSON.stringify(this.userProgress));
        } catch (e) {
            console.error('Failed to save progress:', e);
        }
    }

    loadBookmarks() {
        try {
            return JSON.parse(localStorage.getItem('toolkit-bookmarks') || '{}');
        } catch {
            return {};
        }
    }

    saveBookmarks() {
        try {
            localStorage.setItem('toolkit-bookmarks', JSON.stringify(this.bookmarks));
        } catch (e) {
            console.error('Failed to save bookmarks:', e);
        }
    }

    renderItemCard(item) {
        try {
            const itemId = `${item.categoryId}-${item.title.replace(/\s+/g, '-').toLowerCase()}`;
            const isCompleted = this.userProgress[itemId] || false;
            const isBookmarked = this.bookmarks[itemId] || false;
            
            return `
                <div class="item-card ${isCompleted ? 'completed' : ''}" 
                     data-difficulty="${item.difficulty}" 
                     data-essential="${item.essential}"
                     data-item-id="${itemId}">
                    <div class="item-header">
                        <div>
                            <h4 class="item-title">${item.title}</h4>
                            ${item.author ? `<p class="item-author">bởi ${item.author}</p>` : ''}
                            <p class="item-category">Danh mục: ${item.categoryName}</p>
                        </div>
                        <div class="item-actions">
                            <button class="action-btn bookmark-btn ${isBookmarked ? 'active' : ''}" 
                                    data-item="${itemId}" title="Bookmark" onclick="app.toggleBookmark('${itemId}');">⭐</button>
                            <button class="action-btn complete-btn ${isCompleted ? 'active' : ''}" 
                                    data-item="${itemId}" title="Đánh dấu hoàn thành" onclick="app.toggleComplete('${itemId}');">✅</button>
                        </div>
                    </div>
                    <p class="item-description">${item.description}</p>
                    <div class="item-footer">
                        <div class="item-tags">
                            <span class="item-tag tag-difficulty-${item.difficulty}">${this.getDifficultyLabel(item.difficulty)}</span>
                            ${item.essential ? '<span class="item-tag tag-essential">Thiết Yếu</span>' : ''}
                            <span class="item-tag tag-type">${this.getTypeLabel(item.type)}</span>
                            ${item.category ? `<span class="item-tag tag-category">${item.category}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            console.warn('Error rendering item card:', error);
            return '<div class="item-card error">Error loading item</div>';
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing Toolkit App...');
    window.app = new ToolkitApp();
});