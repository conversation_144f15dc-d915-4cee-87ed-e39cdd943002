{"meta_principles": {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> - <PERSON><PERSON><PERSON> biến qua mọi thời đại", "principles": [{"name": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> thể (Subject Principle)", "description": "<PERSON><PERSON><PERSON> thực thể đều có <PERSON> (Tĩnh) và H<PERSON>nh vi (Động)", "components": ["Trạng thái/State", "Hành vi/Behavior"]}, {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> thuẫn (Contradiction Principle)", "description": "<PERSON>ự phát triển sinh ra từ việc giải quyết mâu thuẫn nội tại", "components": ["<PERSON><PERSON><PERSON><PERSON> diện mâu thuẫn", "<PERSON><PERSON><PERSON> g<PERSON><PERSON> ph<PERSON>p bi<PERSON> ch<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>t đ<PERSON><PERSON> lập"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> (Quantity-Quality Principle)", "description": "Sự tích lũy về lượng dẫn đến chuyển hóa về chất", "components": ["<PERSON> t<PERSON>ch l<PERSON>", "<PERSON><PERSON><PERSON><PERSON> diện điểm nút", "<PERSON><PERSON><PERSON> bị chuyển đổi"]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý Phủ định của Phủ định (Negation Principle)", "description": "<PERSON><PERSON><PERSON> triển theo x<PERSON>, mỗi gi<PERSON>i pháp tạo ra vấn đề mới ở cấp độ cao hơn", "components": ["<PERSON><PERSON><PERSON> l<PERSON> sử", "<PERSON><PERSON> đoán phủ định", "<PERSON><PERSON><PERSON> bị vòng tiếp theo"]}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Systems Principle)", "description": "Tổng thể lớn hơn tổng c<PERSON>c <PERSON>, tập trung vào mối quan hệ", "components": ["<PERSON><PERSON> g<PERSON>i", "<PERSON><PERSON><PERSON> quan hệ", "<PERSON><PERSON><PERSON>", "Phần-<PERSON><PERSON><PERSON> thể"]}, {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> quy <PERSON> (Meta-Recursion Principle)", "description": "Framework tư duy phải áp dụng cho ch<PERSON>h nó", "components": ["Tự phản tỉnh", "<PERSON><PERSON> cải tiến", "<PERSON><PERSON> nhận thức"]}]}, "cognitive_architecture": {"title": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON>ức", "modules": [{"name": "Perception Module", "function": "<PERSON>hu thập và lọc thông tin", "components": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nhận dạng mẫu"]}, {"name": "Memory Module", "function": "<PERSON><PERSON><PERSON> trữ và truy xuất kiến thức", "components": ["<PERSON>ộ nhớ ngắn hạn", "Bộ nhớ dài hạn", "Bộ nhớ làm việc"]}, {"name": "Reasoning Module", "function": "Xử lý logic và suy luận", "components": ["<PERSON><PERSON> lu<PERSON>n qui nạp", "<PERSON><PERSON> <PERSON>u<PERSON><PERSON>", "<PERSON><PERSON> lu<PERSON><PERSON> tư<PERSON> tự"]}, {"name": "Decision Module", "function": "<PERSON> quyết định và lựa chọn hành động", "components": ["<PERSON><PERSON><PERSON> giá l<PERSON>a ch<PERSON>n", "<PERSON> trận quyết đ<PERSON>nh", "<PERSON><PERSON><PERSON><PERSON> lý r<PERSON> ro"]}, {"name": "Metacognition Module", "function": "<PERSON><PERSON><PERSON><PERSON> sát và điều chỉnh tư duy", "components": ["<PERSON><PERSON> nhận thức", "Tự điều chỉnh", "<PERSON><PERSON><PERSON> giá hiệu quả"]}]}, "universal_process": {"title": "<PERSON><PERSON><PERSON><PERSON> 12 b<PERSON><PERSON>c Bất <PERSON>ến", "phases": [{"phase": "NHẬN THỨC (Perception)", "steps": [{"step": 1, "name": "Define", "action": "<PERSON><PERSON><PERSON><PERSON> diện mâu thuẫn cốt lõi bằng 5W1H"}, {"step": 2, "name": "Measure", "action": "<PERSON><PERSON><PERSON><PERSON> hóa mâu thuẫn bằng số liệu"}, {"step": 3, "name": "Analyze", "action": "<PERSON><PERSON> tích bản chất mâu thuẫn"}]}, {"phase": "PHÂN TÍCH (Analysis)", "steps": [{"step": 4, "name": "First Principles", "action": "Phân rã về nguyên lý cơ bản"}, {"step": 5, "name": "Systems View", "action": "<PERSON>em xét trong b<PERSON><PERSON> cảnh hệ thống"}, {"step": 6, "name": "Hypothesize", "action": "Đ<PERSON><PERSON> ra các phương án giải quyết"}]}, {"phase": "QUYẾT ĐỊNH (Decision)", "steps": [{"step": 7, "name": "Evaluate", "action": "<PERSON><PERSON><PERSON> giá các phương án bằng ma trận"}, {"step": 8, "name": "Decide", "action": "<PERSON><PERSON><PERSON> quyết định và ghi lại ADR"}, {"step": 9, "name": "Design", "action": "<PERSON><PERSON><PERSON><PERSON> kế giải pháp chi tiết"}]}, {"phase": "THỰC THI (Execution)", "steps": [{"step": 10, "name": "Implement", "action": "<PERSON><PERSON><PERSON> khai theo kế hoạch"}, {"step": 11, "name": "Verify", "action": "<PERSON><PERSON><PERSON> thử và xác <PERSON>n"}, {"step": 12, "name": "Improve", "action": "<PERSON><PERSON><PERSON> tiến và chuẩn bị vòng tiếp theo"}]}]}, "mental_models": {"universal_models": [{"name": "First Principles Thinking", "description": "Phân rã vấn đề về các nguyên lý cơ bản nhất", "steps": ["<PERSON><PERSON><PERSON> đ<PERSON>nh gi<PERSON> định", "Phân rã về cơ bản", "<PERSON><PERSON><PERSON> dựng từ đầu", "<PERSON><PERSON><PERSON> thử"], "applications": ["Đ<PERSON>i mới", "<PERSON><PERSON><PERSON><PERSON> quyết vấn đề phức tạp", "Tạo breakthrough"]}, {"name": "Systems Thinking", "description": "<PERSON><PERSON><PERSON> hệ thống qua mối quan hệ và tương tác", "steps": ["<PERSON><PERSON><PERSON>h g<PERSON>i", "<PERSON><PERSON> xạ mối quan hệ", "Tìm leverage points", "Hiểu feedback loops"], "applications": ["<PERSON><PERSON><PERSON><PERSON> lý p<PERSON> tạp", "<PERSON><PERSON><PERSON><PERSON> kế tổ chức", "<PERSON><PERSON><PERSON> t<PERSON>c động"]}, {"name": "Dialectical Thinking", "description": "<PERSON><PERSON><PERSON><PERSON> nhất các mặt đối lập", "steps": ["<PERSON><PERSON><PERSON><PERSON> di<PERSON>n đ<PERSON>i lập", "Hiểu tension", "Tìm synthesis", "Tạo unity"], "applications": ["<PERSON><PERSON><PERSON><PERSON> lý mâu thuẫn", "Đ<PERSON>i mới", "Leadership"]}, {"name": "Metacognitive Monitoring", "description": "<PERSON><PERSON> duy về tư duy", "steps": ["<PERSON><PERSON> s<PERSON>t tư duy", "<PERSON><PERSON><PERSON> giá hiệu quả", "Điều chỉnh strategy", "<PERSON><PERSON><PERSON> từ sai lầm"], "applications": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> t<PERSON>", "<PERSON><PERSON> nhận thức"]}], "decision_tools": [{"name": "Decision Matrix", "description": "Ma trận đánh giá đa tiêu chí", "criteria": ["Performance", "Cost", "Risk", "Time", "Quality", "Complexity"], "scoring": "1-10 scale với trọng số"}, {"name": "Pre-mortem Analysis", "description": "<PERSON><PERSON> tích thất bại tr<PERSON><PERSON><PERSON> khi thực hiện", "steps": ["Tưởng tượng thất bại", "Liệt kê nguyên nhân", "<PERSON><PERSON><PERSON><PERSON> kế phòng ngừa", "Monitor risks"]}, {"name": "Red Team / Blue Team", "description": "<PERSON><PERSON><PERSON> giá từ nhiều góc độ đối lập", "red_team": "<PERSON><PERSON><PERSON> cô<PERSON>, tì<PERSON> lỗ hổng", "blue_team": "<PERSON><PERSON><PERSON>, t<PERSON><PERSON> h<PERSON>a"}, {"name": "OODA Loop", "description": "Observe-Orient-Decide-Act cycle", "steps": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> đ<PERSON>"], "frequency": "Continuous iteration"}], "pattern_recognition": [{"name": "Leverage Points", "description": "12 điểm can thi<PERSON><PERSON> c<PERSON><PERSON>", "points": ["Constants, parameters, numbers, subsidies", "Material stocks and flows", "Regulating negative feedback loops", "Driving positive feedback loops", "Information flows", "Rules of the system", "Power to add, change, evolve rules", "Goals of the system", "Mindset or paradigm", "Power to transcend paradigms"]}, {"name": "Cognitive Biases", "description": "<PERSON><PERSON><PERSON><PERSON> thiên lệch nhận thức cần tránh", "categories": ["Confirmation bias", "Anchoring", "Availability heuristic", "Sunk cost fallacy"]}]}, "practice_guidelines": {"daily_practices": [{"name": "Morning Reflection", "duration": "10 minutes", "activities": ["Review framework", "Set thinking intentions", "Identify day's challenges"]}, {"name": "Decision Logging", "frequency": "Each major decision", "format": "ADR (Architecture Decision Record)", "elements": ["Context", "Decision", "Consequences", "Alternatives"]}, {"name": "Evening Review", "duration": "15 minutes", "activities": ["Assess thinking quality", "Identify patterns", "Plan improvements"]}], "weekly_practices": [{"name": "Framework Review", "purpose": "C<PERSON>i tiến framework dựa trên experience", "questions": ["<PERSON>ần nào hữu ích nhất?", "Thiếu gì?", "<PERSON>ần thay đổi gì?"]}, {"name": "Mental Model Study", "purpose": "<PERSON><PERSON>c mental model m<PERSON><PERSON>", "sources": ["Books", "Papers", "Case studies", "Experiments"]}], "monthly_practices": [{"name": "Deep Reflection", "purpose": "<PERSON><PERSON><PERSON> giá sự phát triển tư duy", "activities": ["Review major decisions", "Identify growth areas", "Update framework"]}]}}