// Learning Method App - Fixed JavaScript with Working Navigation

class LearningMethodApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.sessionTimer = {
            isRunning: false,
            isPaused: false,
            currentPhase: 0,
            timeRemaining: 0,
            interval: null,
            phases: [
                { name: 'Deep Error', duration: 45 * 60, description: 'Tập trung vào 1 lỗi cụ thể' },
                { name: 'Build & Ship', duration: 45 * 60, description: 'Tạo sản phẩm thực tế' },
                { name: 'Feedback & Reflect', duration: 60 * 60, description: 'Nhận feedback và reflection' }
            ]
        };
        
        // Load saved data
        this.userData = this.loadUserData();
        this.progressChart = null;
        
        this.init();
    }

    init() {
        console.log('Initializing Learning Method App...');
        this.loadTheme();
        this.setupEventListeners();
        this.renderDashboard();
        this.updateNeurochemicalStatus();
        this.loadDailyChecklist();
        this.loadMistakeHistory();
        
        // Initialize progress chart after a short delay to ensure canvas is ready
        setTimeout(() => {
            this.initializeProgressChart();
        }, 100);
        
        // Ensure initial section is visible
        this.showInitialSection();
    }

    showInitialSection() {
        // Make sure dashboard is visible on load
        const dashboardSection = document.getElementById('dashboard-section');
        if (dashboardSection) {
            dashboardSection.classList.add('active');
        }
        
        // Make sure dashboard nav button is active
        const dashboardBtn = document.querySelector('[data-section="dashboard"]');
        if (dashboardBtn) {
            dashboardBtn.classList.add('active');
        }
    }

    loadTheme() {
        // Set light theme as default per project specification
        document.body.dataset.colorScheme = 'light';
        
        // Listen for theme changes from parent (unified toolkit)
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'THEME_CHANGE') {
                document.body.dataset.colorScheme = 'light'; // Always use light theme
            }
        });
    }

    setupEventListeners() {
        console.log('Setting up event listeners...');
        
        // Navigation - Fixed to properly handle section switching
        document.querySelectorAll('.nav-section-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.closest('button').dataset.section;
                console.log('Navigation clicked:', section);
                if (section) {
                    this.navigateToSection(section);
                }
            });
        });

        // Theme toggle removed - using light theme only per project specification

        // Session timer controls - Fixed to ensure they work
        document.addEventListener('click', (e) => {
            if (e.target.id === 'startTimer') {
                console.log('Start timer clicked');
                this.startSession();
            } else if (e.target.id === 'pauseTimer') {
                console.log('Pause timer clicked');
                this.pauseSession();
            } else if (e.target.id === 'resetTimer') {
                console.log('Reset timer clicked');
                this.resetSession();
            }
        });

        // Quick action buttons
        const quickStartBtn = document.getElementById('quickStartGuide');
        const skillMapBtn = document.getElementById('skillMapBuilder');
        const reflectionBtn = document.getElementById('dailyReflection');
        const startSessionBtn = document.getElementById('startSession');
        const exportBtn = document.getElementById('exportProgress');

        if (quickStartBtn) quickStartBtn.addEventListener('click', () => this.showModal('quickStartModal'));
        if (skillMapBtn) skillMapBtn.addEventListener('click', () => this.showModal('skillMapModal'));
        if (reflectionBtn) reflectionBtn.addEventListener('click', () => this.showModal('reflectionModal'));
        if (startSessionBtn) startSessionBtn.addEventListener('click', () => this.startLearningSession());
        if (exportBtn) exportBtn.addEventListener('click', () => this.exportProgress());

        // Modal handling
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-close') || e.target.classList.contains('modal-backdrop')) {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.classList.add('hidden');
                }
            }
        });

        // Neurochemical status indicators
        document.querySelectorAll('.status-indicator').forEach(indicator => {
            indicator.addEventListener('click', (e) => {
                console.log('Status indicator clicked:', e.target.id);
                this.toggleStatusIndicator(e.target.id);
            });
        });

        // Daily checklist - Fixed to use event delegation
        document.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.closest('#dailyChecklist')) {
                console.log('Checklist item changed:', e.target.id);
                this.updateChecklistProgress(e.target.id, e.target.checked);
            }
        });

        // Mistake log
        document.addEventListener('click', (e) => {
            if (e.target.id === 'saveMistake') {
                console.log('Save mistake clicked');
                this.saveMistakeEntry();
            }
        });

        // Skill map builder
        document.addEventListener('click', (e) => {
            if (e.target.id === 'addSubSkill') {
                this.addSubSkillInput();
            } else if (e.target.id === 'saveSkillMap') {
                this.saveSkillMap();
            }
        });

        // Daily reflection
        document.addEventListener('click', (e) => {
            if (e.target.id === 'saveReflection') {
                this.saveReflection();
            }
        });

        // Sidebar toggle for mobile
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', this.toggleSidebar.bind(this));
        }
    }

    navigateToSection(sectionId) {
        console.log('Navigating to section:', sectionId);
        
        // Update active nav button
        document.querySelectorAll('.nav-section-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const targetBtn = document.querySelector(`[data-section="${sectionId}"]`);
        if (targetBtn) {
            targetBtn.classList.add('active');
            console.log('Activated nav button for:', sectionId);
        }

        // Show section
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        const targetSection = document.getElementById(`${sectionId}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            console.log('Activated section:', sectionId);
        } else {
            console.error('Section not found:', `${sectionId}-section`);
        }

        this.currentSection = sectionId;
        
        // Initialize section-specific features
        if (sectionId === 'progress') {
            setTimeout(() => this.initializeProgressChart(), 100);
        }
    }

    // Session Timer Functions - Fixed and improved
    startSession() {
        console.log('Starting session...');
        
        if (!this.sessionTimer.isRunning) {
            this.sessionTimer.isRunning = true;
            this.sessionTimer.isPaused = false;
            this.sessionTimer.currentPhase = 0;
            this.sessionTimer.timeRemaining = this.sessionTimer.phases[0].duration;
            this.updateTimerDisplay();
            this.sessionTimer.interval = setInterval(() => this.updateTimer(), 1000);
            
            // Update button states
            const startBtn = document.getElementById('startTimer');
            const pauseBtn = document.getElementById('pauseTimer');
            const resetBtn = document.getElementById('resetTimer');
            
            if (startBtn) {
                startBtn.disabled = true;
                startBtn.textContent = 'Đang Chạy...';
            }
            if (pauseBtn) pauseBtn.disabled = false;
            if (resetBtn) resetBtn.disabled = false;
            
            console.log('Session started successfully');
        }
    }

    pauseSession() {
        const pauseBtn = document.getElementById('pauseTimer');
        
        if (this.sessionTimer.isRunning && !this.sessionTimer.isPaused) {
            this.sessionTimer.isPaused = true;
            clearInterval(this.sessionTimer.interval);
            if (pauseBtn) pauseBtn.textContent = 'Tiếp Tục';
            console.log('Session paused');
        } else if (this.sessionTimer.isPaused) {
            this.sessionTimer.isPaused = false;
            this.sessionTimer.interval = setInterval(() => this.updateTimer(), 1000);
            if (pauseBtn) pauseBtn.textContent = 'Tạm Dừng';
            console.log('Session resumed');
        }
    }

    resetSession() {
        console.log('Resetting session...');
        
        this.sessionTimer.isRunning = false;
        this.sessionTimer.isPaused = false;
        this.sessionTimer.currentPhase = 0;
        this.sessionTimer.timeRemaining = 0;
        
        if (this.sessionTimer.interval) {
            clearInterval(this.sessionTimer.interval);
        }
        
        this.updateTimerDisplay();
        
        // Reset button states
        const startBtn = document.getElementById('startTimer');
        const pauseBtn = document.getElementById('pauseTimer');
        const resetBtn = document.getElementById('resetTimer');
        
        if (startBtn) {
            startBtn.disabled = false;
            startBtn.textContent = 'Bắt Đầu Session';
        }
        if (pauseBtn) {
            pauseBtn.disabled = true;
            pauseBtn.textContent = 'Tạm Dừng';
        }
        if (resetBtn) resetBtn.disabled = true;
        
        console.log('Session reset successfully');
    }

    updateTimer() {
        if (this.sessionTimer.timeRemaining > 0) {
            this.sessionTimer.timeRemaining--;
            this.updateTimerDisplay();
        } else {
            // Move to next phase
            this.sessionTimer.currentPhase++;
            
            if (this.sessionTimer.currentPhase < this.sessionTimer.phases.length) {
                this.sessionTimer.timeRemaining = this.sessionTimer.phases[this.sessionTimer.currentPhase].duration;
                this.showPhaseTransition();
            } else {
                // Session completed
                this.completeSession();
            }
        }
    }

    updateTimerDisplay() {
        const clock = document.getElementById('timerClock');
        const phase = document.getElementById('timerPhase');
        
        if (clock) {
            const minutes = Math.floor(this.sessionTimer.timeRemaining / 60);
            const seconds = this.sessionTimer.timeRemaining % 60;
            clock.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        if (phase) {
            if (this.sessionTimer.isRunning) {
                const currentPhaseObj = this.sessionTimer.phases[this.sessionTimer.currentPhase];
                if (currentPhaseObj) {
                    phase.textContent = `${currentPhaseObj.name} - ${currentPhaseObj.description}`;
                }
            } else {
                phase.textContent = 'Chưa Bắt Đầu';
            }
        }
    }

    showPhaseTransition() {
        const currentPhaseObj = this.sessionTimer.phases[this.sessionTimer.currentPhase];
        
        // Show notification
        alert(`🎯 Chuyển sang giai đoạn: ${currentPhaseObj.name}\n\n${currentPhaseObj.description}\n\nThời gian: ${Math.floor(currentPhaseObj.duration / 60)} phút`);
        
        this.updateTimerDisplay();
    }

    completeSession() {
        this.resetSession();
        
        // Update user stats
        this.userData.sessionsCompleted = (this.userData.sessionsCompleted || 0) + 1;
        this.userData.totalDays = this.calculateTotalDays();
        this.saveUserData();
        
        // Show completion message
        alert('🎉 Congratulations! Bạn đã hoàn thành một session học tập!\n\n✅ Hãy ghi lại những gì đã học được và kế hoạch cho session tiếp theo.');
        
        // Navigate to progress section
        this.navigateToSection('progress');
        this.renderDashboard();
    }

    // Neurochemical Status Functions
    updateNeurochemicalStatus() {
        const today = new Date().toDateString();
        const todayStatus = this.userData.neurochemicalStatus?.[today] || {};
        
        Object.keys(todayStatus).forEach(statusId => {
            const indicator = document.getElementById(statusId);
            if (indicator && todayStatus[statusId]) {
                indicator.classList.add('active');
            }
        });
    }

    toggleStatusIndicator(statusId) {
        const indicator = document.getElementById(statusId);
        if (!indicator) return;
        
        const today = new Date().toDateString();
        
        if (!this.userData.neurochemicalStatus) {
            this.userData.neurochemicalStatus = {};
        }
        if (!this.userData.neurochemicalStatus[today]) {
            this.userData.neurochemicalStatus[today] = {};
        }
        
        const isActive = indicator.classList.toggle('active');
        this.userData.neurochemicalStatus[today][statusId] = isActive;
        
        this.saveUserData();
        console.log(`Toggled ${statusId}:`, isActive);
    }

    // Dashboard Functions
    renderDashboard() {
        const totalDaysEl = document.getElementById('totalDays');
        const sessionsCompletedEl = document.getElementById('sessionsCompleted');
        const mistakesFixedEl = document.getElementById('mistakesFixed');
        const overallProgressEl = document.getElementById('overallProgressPercent');

        if (totalDaysEl) totalDaysEl.textContent = this.userData.totalDays || 0;
        if (sessionsCompletedEl) sessionsCompletedEl.textContent = this.userData.sessionsCompleted || 0;
        if (mistakesFixedEl) mistakesFixedEl.textContent = this.userData.mistakesFixed || 0;
        
        const progress = this.calculateOverallProgress();
        if (overallProgressEl) overallProgressEl.textContent = `${progress}%`;
        
        // Update progress bar
        const progressBar = document.getElementById('overallProgress');
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
    }

    calculateOverallProgress() {
        const totalChecks = 6; // Number of daily checklist items
        const today = new Date().toDateString();
        const todayChecklist = this.userData.dailyChecklist?.[today] || {};
        const completedToday = Object.values(todayChecklist).filter(Boolean).length;
        
        return Math.round((completedToday / totalChecks) * 100);
    }

    calculateTotalDays() {
        const firstSessionDate = this.userData.firstSessionDate;
        if (!firstSessionDate) {
            this.userData.firstSessionDate = new Date().toDateString();
            return 1;
        }
        
        const first = new Date(firstSessionDate);
        const today = new Date();
        const diffTime = Math.abs(today - first);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return diffDays + 1;
    }

    // Progress Chart Functions - Fixed
    initializeProgressChart() {
        const ctx = document.getElementById('progressChart');
        if (!ctx) {
            console.log('Progress chart canvas not found');
            return;
        }

        // Destroy existing chart if it exists
        if (this.progressChart) {
            this.progressChart.destroy();
        }

        const chartData = this.getChartData();
        
        try {
            this.progressChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartData.labels,
                    datasets: [
                        {
                            label: 'Tiến Độ Hàng Ngày (%)',
                            data: chartData.progress,
                            borderColor: '#1FB8CD',
                            backgroundColor: 'rgba(31, 184, 205, 0.1)',
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'Sessions Hoàn Thành',
                            data: chartData.sessions,
                            borderColor: '#FFC185',
                            backgroundColor: 'rgba(255, 193, 133, 0.1)',
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Tiến Độ Học Tập Theo Thời Gian'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
            console.log('Progress chart initialized successfully');
        } catch (error) {
            console.error('Error initializing progress chart:', error);
        }
    }

    getChartData() {
        const last7Days = [];
        const today = new Date();
        
        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            last7Days.push(date.toDateString());
        }
        
        const labels = last7Days.map(date => {
            const d = new Date(date);
            return `${d.getDate()}/${d.getMonth() + 1}`;
        });
        
        const progress = last7Days.map(date => {
            const dayChecklist = this.userData.dailyChecklist?.[date] || {};
            const completed = Object.values(dayChecklist).filter(Boolean).length;
            return Math.round((completed / 6) * 100);
        });
        
        const sessions = last7Days.map(date => {
            return this.userData.dailySessions?.[date] || 0;
        });
        
        return { labels, progress, sessions };
    }

    // Daily Checklist Functions
    loadDailyChecklist() {
        const today = new Date().toDateString();
        const todayChecklist = this.userData.dailyChecklist?.[today] || {};
        
        Object.keys(todayChecklist).forEach(checkId => {
            const checkbox = document.getElementById(checkId);
            if (checkbox) {
                checkbox.checked = todayChecklist[checkId];
            }
        });
    }

    updateChecklistProgress(checkId, isChecked) {
        const today = new Date().toDateString();
        
        if (!this.userData.dailyChecklist) {
            this.userData.dailyChecklist = {};
        }
        if (!this.userData.dailyChecklist[today]) {
            this.userData.dailyChecklist[today] = {};
        }
        
        this.userData.dailyChecklist[today][checkId] = isChecked;
        this.saveUserData();
        this.renderDashboard();
        
        console.log(`Updated checklist ${checkId}:`, isChecked);
    }

    // Mistake Log Functions
    saveMistakeEntry() {
        const input = document.getElementById('mistakeInput');
        if (!input || !input.value.trim()) {
            alert('Vui lòng nhập nội dung ghi chú!');
            return;
        }
        
        const entry = {
            date: new Date().toDateString(),
            time: new Date().toLocaleTimeString(),
            content: input.value.trim()
        };
        
        if (!this.userData.mistakeLog) {
            this.userData.mistakeLog = [];
        }
        
        this.userData.mistakeLog.unshift(entry); // Add to beginning
        this.userData.mistakesFixed = (this.userData.mistakesFixed || 0) + 1;
        
        input.value = '';
        this.saveUserData();
        this.loadMistakeHistory();
        this.renderDashboard();
        
        alert('✅ Đã lưu ghi chú thành công!');
        console.log('Saved mistake entry:', entry);
    }

    loadMistakeHistory() {
        const historyContainer = document.getElementById('mistakeHistory');
        if (!historyContainer) return;
        
        if (!this.userData.mistakeLog || this.userData.mistakeLog.length === 0) {
            historyContainer.innerHTML = '<p style="text-align: center; color: var(--color-text-secondary);">Chưa có ghi chú nào. Hãy bắt đầu ghi lại những gì bạn học được!</p>';
            return;
        }
        
        const recent10 = this.userData.mistakeLog.slice(0, 10);
        
        historyContainer.innerHTML = recent10.map(entry => `
            <div class="mistake-entry">
                <div class="mistake-date">${entry.date} - ${entry.time}</div>
                <div class="mistake-content">${entry.content}</div>
            </div>
        `).join('');
    }

    // Skill Map Functions
    addSubSkillInput() {
        const container = document.getElementById('subSkills');
        if (!container) return;
        
        const input = document.createElement('input');
        input.type = 'text';
        input.placeholder = `Kỹ năng con ${container.children.length + 1}`;
        
        container.appendChild(input);
    }

    saveSkillMap() {
        const mainSkill = document.getElementById('mainSkill')?.value.trim();
        const subSkillInputs = document.querySelectorAll('#subSkills input');
        
        if (!mainSkill) {
            alert('Vui lòng nhập kỹ năng chính!');
            return;
        }
        
        const subSkills = Array.from(subSkillInputs)
            .map(input => input.value.trim())
            .filter(skill => skill.length > 0);
        
        const skillMap = {
            mainSkill,
            subSkills,
            createdDate: new Date().toDateString()
        };
        
        this.userData.skillMap = skillMap;
        this.saveUserData();
        
        this.hideModal('skillMapModal');
        alert(`✅ Đã lưu bản đồ kỹ năng!\n\nKỹ năng chính: ${mainSkill}\nKỹ năng con: ${subSkills.length} items`);
        
        // Clear the form
        document.getElementById('mainSkill').value = '';
        const subSkillContainer = document.getElementById('subSkills');
        subSkillContainer.innerHTML = `
            <input type="text" placeholder="Kỹ năng con 1">
            <input type="text" placeholder="Kỹ năng con 2">
            <input type="text" placeholder="Kỹ năng con 3">
        `;
        
        console.log('Saved skill map:', skillMap);
    }

    // Daily Reflection Functions
    saveReflection() {
        const reflection1 = document.getElementById('reflection1')?.value.trim();
        const reflection2 = document.getElementById('reflection2')?.value.trim();
        const reflection3 = document.getElementById('reflection3')?.value.trim();
        
        if (!reflection1 || !reflection2 || !reflection3) {
            alert('Vui lòng trả lời đầy đủ 3 câu hỏi!');
            return;
        }
        
        const reflectionEntry = {
            date: new Date().toDateString(),
            time: new Date().toLocaleTimeString(),
            answers: {
                mistake: reflection1,
                feeling: reflection2,
                tomorrow: reflection3
            }
        };
        
        if (!this.userData.reflections) {
            this.userData.reflections = [];
        }
        
        this.userData.reflections.unshift(reflectionEntry);
        this.saveUserData();
        
        // Clear the form
        document.getElementById('reflection1').value = '';
        document.getElementById('reflection2').value = '';
        document.getElementById('reflection3').value = '';
        
        this.hideModal('reflectionModal');
        alert('✅ Đã lưu nhật ký reflection!');
        
        console.log('Saved reflection:', reflectionEntry);
    }

    // Modal Functions
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    // Learning Session Function
    startLearningSession() {
        this.navigateToSection('neurochemical');
        
        // Auto-start the timer
        setTimeout(() => {
            if (confirm('🚀 Bắt đầu session học tập ngay bây giờ?\n\nSession bao gồm 3 giai đoạn:\n1. Deep Error (45 phút)\n2. Build & Ship (45 phút)\n3. Feedback & Reflect (60 phút)')) {
                this.startSession();
            }
        }, 500);
    }

    // Theme Functions - removed, using light theme only per project specification

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('open');
        }
    }

    // Data Management Functions
    exportProgress() {
        const data = {
            userData: this.userData,
            exportDate: new Date().toISOString(),
            appVersion: '1.0.0'
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `learning-progress-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        alert('✅ Dữ liệu đã được xuất thành công!');
        console.log('Progress exported');
    }

    loadUserData() {
        try {
            const stored = localStorage.getItem('learning-method-data');
            return stored ? JSON.parse(stored) : this.getDefaultUserData();
        } catch (e) {
            console.error('Failed to load user data:', e);
            return this.getDefaultUserData();
        }
    }

    saveUserData() {
        try {
            localStorage.setItem('learning-method-data', JSON.stringify(this.userData));
        } catch (e) {
            console.error('Failed to save user data:', e);
        }
    }

    getDefaultUserData() {
        return {
            totalDays: 0,
            sessionsCompleted: 0,
            mistakesFixed: 0,
            dailyChecklist: {},
            neurochemicalStatus: {},
            mistakeLog: [],
            reflections: [],
            skillMap: null,
            firstSessionDate: null,
            dailySessions: {}
        };
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM Content Loaded - Initializing Learning Method App...');
    window.learningApp = new LearningMethodApp();
});

// Export for debugging in console
window.LearningMethodApp = LearningMethodApp;