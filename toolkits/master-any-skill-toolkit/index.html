<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>ỹ Năng Trong 1 Tháng - Toolkit <PERSON></title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1 class="header-title">🧠 Học Nhanh Trong 1 Tháng</h1>
            <div class="header-actions">
                <div class="neurochemical-status" id="neurochemicalStatus">
                    <div class="status-indicator" id="noveltyStatus" title="Novelty">🔄</div>
                    <div class="status-indicator" id="focusStatus" title="Focus">🎯</div>
                    <div class="status-indicator" id="dopamineStatus" title="Dopamine">⚡</div>
                    <div class="status-indicator" id="sleepStatus" title="Sleep">😴</div>
                </div>

            </div>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" id="overallProgress"></div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h3>Phương Pháp 4N</h3>
            <button class="sidebar-toggle" id="sidebarToggle">←</button>
        </div>
        <div class="nav-sections">
            <div class="nav-section">
                <button class="nav-section-btn active" data-section="dashboard">
                    📊 Tổng Quan
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="brain-science">
                    🧠 Khoa Học Não Bộ
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="method-4n">
                    🎯 Phương Pháp 4N
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="hour-rule">
                    ⏰ Quy Tắc 10,000 Giờ
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="neurochemical">
                    ⚗️ Neurochemical Gating
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="roadmap">
                    🗺️ Lộ Trình 2 Tuần
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="traps">
                    ⚠️ Bẫy Thường Gặp
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="progress">
                    📈 Theo Dõi Tiến Độ
                </button>
            </div>
        </div>
        <div class="sidebar-footer">
            <button class="btn btn--primary btn--sm" id="startSession">🎯 Bắt Đầu Session</button>
            <button class="btn btn--secondary btn--sm" id="exportProgress">📤 Xuất Dữ Liệu</button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="content-section active">
            <div class="dashboard-header">
                <h2>Phương Pháp Khoa Học Học Tập Trong 1 Tháng</h2>
                <p class="dashboard-subtitle">Dựa trên khoa học thần kinh và neuroplasticity</p>
            </div>
            
            <div class="methodology-intro">
                <div class="intro-card">
                    <h3>🧠 Phương Pháp Học Tập Có Cơ Sở Khoa Học</h3>
                    <p>Phương pháp học tập có cơ sở khoa học để thành thạo bất kỳ kỹ năng nào trong 30 ngày. Dựa trên nghiên cứu neuroplasticity và khả năng tái cấu trúc não bộ.</p>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📚</div>
                    <div class="stat-content">
                        <h3 id="totalDays">0</h3>
                        <p>Ngày Học Tập</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🎯</div>
                    <div class="stat-content">
                        <h3 id="sessionsCompleted">0</h3>
                        <p>Sessions Hoàn Thành</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-content">
                        <h3 id="mistakesFixed">0</h3>
                        <p>Lỗi Đã Sửa</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📈</div>
                    <div class="stat-content">
                        <h3 id="overallProgressPercent">0%</h3>
                        <p>Tiến Độ Tổng</p>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <h3>Hành Động Nhanh</h3>
                <div class="quick-action-buttons">
                    <button class="btn btn--primary" id="quickStartGuide">🚀 Hướng Dẫn Nhanh</button>
                    <button class="btn btn--secondary" id="skillMapBuilder">🗺️ Xây Dựng Bản Đồ Kỹ Năng</button>
                    <button class="btn btn--outline" id="dailyReflection">📝 Nhật Ký Hôm Nay</button>
                </div>
            </div>
        </section>

        <!-- Brain Science Section -->
        <section id="brain-science-section" class="content-section">
            <div class="section-header">
                <h2>🧠 Khoa Học Về Não Bộ</h2>
                <p>Hiểu rõ cơ chế hoạt động của não bộ khi học kỹ năng mới</p>
            </div>

            <div class="brain-content">
                <div class="brain-myth">
                    <h3>Cái Bẫy "Cần Nhiều Giờ Để Giỏi"</h3>
                    <p>Niềm tin rằng cần 10 năm đánh đàn, 5 năm code, 3 năm viết báo là sai lầm. Nghiên cứu khoa học cho thấy não người có khả năng thay đổi nhanh chóng thông qua neuroplasticity.</p>
                    <div class="science-fact">
                        <h4>🔬 Sự Thật Khoa Học</h4>
                        <p>Sự khác biệt nằm ở khả năng tái cấu trúc não bộ (neuroplasticity). Mỗi lần học trung cao độ, lặp lại có chủ đích, hệ thần kinh tạo ra lớp myelin dày hơn quanh sợi trực neuron.</p>
                    </div>
                </div>

                <div class="neuroplasticity-card">
                    <h3>Bộ Não Thay Đổi Ra Sao Khi Học Điều Mới</h3>
                    <div class="brain-process">
                        <div class="process-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4>4-6 Tuần Đầu</h4>
                                <p>Myelin hình thành nhanh nhất trong 4-6 tuần đầu học kỹ năng mới. Tăng tốc độ truyền tín hiệu lên đến 100 lần.</p>
                            </div>
                        </div>
                        <div class="process-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4>Sau 8 Tuần</h4>
                                <p>Sự thay đổi về chất xám ở vùng ghi nhớ và học tập. Cường độ và phương pháp quan trọng hơn thời gian.</p>
                            </div>
                        </div>
                        <div class="process-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4>Lặp Lại Đúng Cách</h4>
                                <p>Mỗi lần lặp lại đúng cách, hệ thần kinh tạo ra lớp myelin dày hơn, tăng tốc độ xử lý thông tin.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Method 4N Section -->
        <section id="method-4n-section" class="content-section">
            <div class="section-header">
                <h2>🎯 Mở Khóa Chế Độ Học Nhanh Với 4N</h2>
                <p>4 yếu tố bắt buộc để kích hoạt chế độ học nhanh của não bộ</p>
            </div>

            <div class="method-4n-grid">
                <div class="method-card">
                    <div class="method-icon">🔄</div>
                    <h3>Novelty (Mới Lạ)</h3>
                    <p>Não người lớn vẫn học rất nhanh khi có điều mới lạ kèm rủi ro vừa phải.</p>
                    <div class="implementation">
                        <h4>Cách Thực Hiện:</h4>
                        <p>1 tháng đầu tiên phải có mùi phiêu lưu: công khai mục tiêu, nhận job nhỏ, hoặc tự đặt deadline.</p>
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-icon">🎯</div>
                    <h3>Needle (Mũi Kim)</h3>
                    <p>Đừng học lần, chọn một điểm sai để xuyên thủng mỗi lần.</p>
                    <div class="implementation">
                        <h4>Cách Thực Hiện:</h4>
                        <p>Dùng video chỉ luyện 'nhịp cắt thoại', chưa đụng màu/nhạc/hiệu ứng. Tạo 'sai số có chủ đích' (prediction error).</p>
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-icon">⚗️</div>
                    <h3>Neurochemical Gating</h3>
                    <p>Sắp xếp buổi học theo 3 chất 'đồng pha'.</p>
                    <div class="implementation">
                        <h4>3 Giai Đoạn:</h4>
                        <ul>
                            <li><strong>Khởi động (3-5'):</strong> làm tim/thở hơi nhanh</li>
                            <li><strong>Tập trung (25-45'):</strong> tập nhiều, chỉ nhìn 1 lỗi</li>
                            <li><strong>Chốt buổi (2-3'):</strong> tự chấm điểm và tự thưởng</li>
                        </ul>
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-icon">🌙</div>
                    <h3>Night (Ban Đêm)</h3>
                    <p>Ngủ tạo ký ức - não phát lại điều vừa học và lưu vào bộ nhớ dài hạn.</p>
                    <div class="implementation">
                        <h4>Tầm Quan Trọng:</h4>
                        <p>Bạn càng ngủ ngon, 'file học' càng được ghi sâu, kiến thức càng bám lâu.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 10,000 Hour Rule Section -->
        <section id="hour-rule-section" class="content-section">
            <div class="section-header">
                <h2>⏰ Phá Vỡ Niềm Tin 10,000 Giờ</h2>
                <p>Tại sao quy tắc 10,000 giờ không áp dụng cho hầu hết kỹ năng</p>
            </div>

            <div class="hour-rule-content">
                <div class="myth-busting">
                    <h3>🚫 Sự Hiểu Lầm Phổ Biến</h3>
                    <p>Quy tắc 10,000 giờ bị đơn giản hóa thành 'muốn giỏi hay học được thứ gì cũng cần 10,000 giờ'.</p>
                    
                    <div class="reality-check">
                        <h4>🎯 Thực Tế</h4>
                        <p>Chỉ áp dụng khi bạn muốn đứng đầu một lĩnh vực siêu cạnh tranh (như kiến trúc sư thế giới). Để học được những kỹ năng cơ bản và trở nên thành thạo vừa đủ trong đa số lĩnh vực thông thường, bạn không cần đến con số khổng lồ đó.</p>
                    </div>
                </div>

                <div class="learning-curve">
                    <h3>📈 Đường Cong Học Tập Thực Tế</h3>
                    <div class="curve-phases">
                        <div class="phase">
                            <h4>Giai Đoạn 1: Tiến Bộ Nhanh</h4>
                            <p>Giai đoạn cải thiện nhanh nhất xảy ra trong vài tuần đầu khi bắt đầu học một thứ hoàn toàn mới.</p>
                        </div>
                        <div class="phase">
                            <h4>Giai Đoạn 2: Thành Thạo</h4>
                            <p>Hầu hết kỹ năng có thể học được ở mức thành thạo cao trong thời gian ngắn hơn nhiều với phương pháp đúng.</p>
                        </div>
                        <div class="phase">
                            <h4>Giai Đoạn 3: Chậm Lại</h4>
                            <p>Tiến bộ sẽ chậm lại ở giai đoạn sau, nhưng giai đoạn đầu là khi bạn tiếp thu được hầu hết kỹ năng nền tảng trong thời gian ngắn nhất.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Neurochemical Gating Section -->
        <section id="neurochemical-section" class="content-section">
            <div class="section-header">
                <h2>⚗️ Neurochemical Gating Chi Tiết</h2>
                <p>Hướng dẫn chi tiết về session học tập 120-150 phút</p>
            </div>

            <div class="session-timer" id="sessionTimer">
                <div class="timer-display">
                    <div class="timer-clock" id="timerClock">00:00</div>
                    <div class="timer-phase" id="timerPhase">Chưa Bắt Đầu</div>
                </div>
                <div class="timer-controls">
                    <button class="btn btn--primary" id="startTimer">Bắt Đầu Session</button>
                    <button class="btn btn--secondary" id="pauseTimer">Tạm Dừng</button>
                    <button class="btn btn--outline" id="resetTimer">Reset</button>
                </div>
            </div>

            <div class="session-breakdown">
                <h3>Cấu Trúc Session Học Tập</h3>
                <div class="phase-grid">
                    <div class="phase-card">
                        <h4>🎯 Deep Error (45 phút)</h4>
                        <p>Chỉ tập trung vào 1 lỗi hoặc 1 vi kỹ năng. Tạo prediction error để não học nhanh nhất.</p>
                        <div class="phase-tips">
                            <strong>Mẹo:</strong> Chọn 1 điểm yếu cụ thể, lặp lại 10-15 lần liên tiếp.
                        </div>
                    </div>

                    <div class="phase-card">
                        <h4>🚀 Build & Ship (45 phút)</h4>
                        <p>Tạo đầu ra thật: code chạy được, đoạn nhạc 15s, bài viết 300 chữ.</p>
                        <div class="phase-tips">
                            <strong>Mẹo:</strong> Phải có sản phẩm cụ thể để nhận feedback thực tế.
                        </div>
                    </div>

                    <div class="phase-card">
                        <h4>💭 Feedback & Reflect (30-60 phút)</h4>
                        <p>Nhận góp ý, ghi log, chốt bài tập ngày mai.</p>
                        <div class="phase-tips">
                            <strong>Mẹo:</strong> Ghi lại điều học được và kế hoạch cải thiện.
                        </div>
                    </div>
                </div>

                <div class="session-notes">
                    <h4>📝 Lưu Ý Quan Trọng</h4>
                    <ul>
                        <li>2 lần nghỉ 5-10 phút giữa các pha (đi bộ, nhắm mắt, thở)</li>
                        <li>Ngủ 7-8h, có thể nghỉ ngắn 20 phút sau pha 1</li>
                        <li>Không dùng điện thoại trong thời gian tập trung</li>
                        <li>Chuẩn bị đủ nước và đồ ăn nhẹ trước khi bắt đầu</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Roadmap Section -->
        <section id="roadmap-section" class="content-section">
            <div class="section-header">
                <h2>🗺️ Lộ Trình 2 Tuần Chi Tiết</h2>
                <p>Kế hoạch cụ thể để áp dụng phương pháp 4N hiệu quả</p>
            </div>

            <div class="roadmap-container">
                <div class="week-card">
                    <div class="week-header">
                        <h3>📍 Tuần 1: Làm Bản Đồ Và Gỡ Vướng</h3>
                        <div class="week-goal">Để não chạy mượt, ít 'kẹt'</div>
                    </div>
                    
                    <div class="daily-tasks">
                        <div class="day-task">
                            <h4>Ngày 1-2: Bẻ nhỏ kỹ năng</h4>
                            <p>Liệt kê kỹ năng con, chọn 1-2 kỹ năng con làm trục của tuần 1</p>
                            <div class="example">
                                <strong>VD với viết:</strong> câu mở hút người / lập luận 3 tầng / nhịp câu / ẩn dụ / chốt cảm xúc
                            </div>
                        </div>

                        <div class="day-task">
                            <h4>Ngày 3-4: Dọn mọi thứ gây vướng</h4>
                            <p>Cải thiện công cụ, chuẩn bị mẫu sẵn, đặt phím tắt. Ít loay hoay sẽ có nhiều phút học thật.</p>
                        </div>

                        <div class="day-task">
                            <h4>Ngày 5-7: Mỗi ngày chỉ 1 lỗi</h4>
                            <p>Mỗi ngày chọn 1 lỗi duy nhất (VD: 'câu mở chưa có lực'). Làm khoảng 10 lượt, tự chấm theo thang đơn giản như yêu / ổn / tốt.</p>
                        </div>
                    </div>

                    <div class="journaling-section">
                        <h4>📝 Nhật Ký Hàng Ngày (3 câu, 3 phút, mỗi tối)</h4>
                        <div class="journal-questions">
                            <div class="question">❓ Hôm nay mình sửa lỗi nào?</div>
                            <div class="question">❓ Khi làm đúng, cơ thể thấy gì?</div>
                            <div class="question">❓ Ngày mai 'mũi kim' đâm vào điểm nào?</div>
                        </div>
                    </div>

                    <div class="week-result">
                        <strong>🎯 Kết tuần 1:</strong> Có bản đồ kỹ năng con, vướng mắc gần như bằng 0, vòng học đã nổ máy
                    </div>
                </div>

                <div class="week-card">
                    <div class="week-header">
                        <h3>🚀 Tuần 2: Sai Số Có Chủ Đích Để Học Sâu</h3>
                        <div class="week-goal">Để dopamine thường đúng cự</div>
                    </div>

                    <div class="ratio-breakdown">
                        <h4>📊 Tỉ Lệ 60-30-10</h4>
                        <div class="ratio-grid">
                            <div class="ratio-item">
                                <div class="ratio-percent">60%</div>
                                <div class="ratio-content">
                                    <strong>Bài vừa sức +</strong>
                                    <p>Sai khoảng 15-20%. VD ngoại ngữ: nói chuyện theo mẫu 3 phút</p>
                                </div>
                            </div>
                            <div class="ratio-item">
                                <div class="ratio-percent">30%</div>
                                <div class="ratio-content">
                                    <strong>Bài khó hơn</strong>
                                    <p>Có máu tham khảo hoặc người hướng dẫn. VD lập trình: viết đoạn mã cho yêu cầu mới, có xem mẫu</p>
                                </div>
                            </div>
                            <div class="ratio-item">
                                <div class="ratio-percent">10%</div>
                                <div class="ratio-content">
                                    <strong>Chơi thử</strong>
                                    <p>Pha trộn lại, đảo thử tự, đổi nhịp để mở kết nối sáng tạo</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="feedback-system">
                        <h4>🗣️ Góp Ý Đôi Mỗi Ngày (2 người)</h4>
                        <p>Nhận và cho góp ý theo 3 bước: <strong>Tình huống – Hành vi – Tác động</strong>, rồi chốt 1 điều giữ, 1 điều bỏ, 1 điều tăng</p>
                        <div class="feedback-example">
                            <strong>VD:</strong> "Ở đoạn mở, bạn dùng câu ngăn khiến người đọc vào được ngay. Giữ nhịp này, bỏ ẩn dụ thừa, tăng ví dụ cụ thể"
                        </div>
                    </div>

                    <div class="week-result">
                        <strong>🎯 Kết tuần 2:</strong> Cảm giác tay trôi hơn, đường truyền trong não mượt hơn
                    </div>
                </div>
            </div>
        </section>

        <!-- Common Traps Section -->
        <section id="traps-section" class="content-section">
            <div class="section-header">
                <h2>⚠️ Những Bẫy Khiến 1 Tháng Vô Nghĩa</h2>
                <p>Tránh những sai lầm phổ biến trong quá trình học</p>
            </div>

            <div class="traps-grid">
                <div class="trap-card">
                    <div class="trap-icon">🌊</div>
                    <h3>Học Lăn Man</h3>
                    <p>Mỗi ngày một chủ đề. Não không đủ lần 'đâm kim' vào cùng điểm thì synapse không dày lên.</p>
                    <div class="solution">
                        <strong>💡 Giải pháp:</strong> Tập trung 1 kỹ năng cụ thể mỗi ngày
                    </div>
                </div>

                <div class="trap-card">
                    <div class="trap-icon">😴</div>
                    <h3>Không Ngủ/Không Nghỉ</h3>
                    <p>Bạn nghĩ mình chiến thắng, thực ra bạn đang tự chơi khăm ghi nhớ kiến thức.</p>
                    <div class="solution">
                        <strong>💡 Giải pháp:</strong> Đảm bảo 7-8h ngủ và nghỉ ngơi đúng cách
                    </div>
                </div>

                <div class="trap-card">
                    <div class="trap-icon">📁</div>
                    <h3>Không Giao Sản Phẩm</h3>
                    <p>Mọi 'bài tập' chết trong máy, không có phản hồi thực nên dopamine không dòng đều.</p>
                    <div class="solution">
                        <strong>💡 Giải pháp:</strong> Luôn tạo ra output thực tế và nhận feedback
                    </div>
                </div>

                <div class="trap-card">
                    <div class="trap-icon">🎯</div>
                    <h3>Đặt Mục Tiêu Bằng Danh Xưng</h3>
                    <p>VD 'trở thành designer': đổi thành 'mỗi ngày tạo 1 visual 20 phút và publish'. Não chỉ hiểu hành vi đo được.</p>
                    <div class="solution">
                        <strong>💡 Giải pháp:</strong> Định nghĩa mục tiêu bằng hành động cụ thể, đo lường được
                    </div>
                </div>

                <div class="trap-card">
                    <div class="trap-icon">😤</div>
                    <h3>Tự Phạt Nặng Khi Sai</h3>
                    <p>Amygdala học sơ cạp. Hãy phạt đúng liều (giảm stress 5' trước mỗi session), thưởng ngay khi tiến bộ.</p>
                    <div class="solution">
                        <strong>💡 Giải pháp:</strong> Tạo môi trường học tập tích cực, không tự trừng phạt
                    </div>
                </div>

                <div class="trap-card">
                    <div class="trap-icon">💭</div>
                    <h3>Chỉ Cảm Hứng Mới Làm</h3>
                    <p>Sai nhịp tác. Hành động nhỏ sẽ kích hoạt tín hiệu thần kinh, cảm hứng đến ngay sau đó.</p>
                    <div class="solution">
                        <strong>💡 Giải pháp:</strong> Bắt đầu với hành động nhỏ, cảm hứng sẽ theo sau
                    </div>
                </div>
            </div>
        </section>

        <!-- Progress Tracking Section -->
        <section id="progress-section" class="content-section">
            <div class="section-header">
                <h2>📈 Theo Dõi Tiến Độ</h2>
                <p>Công cụ theo dõi và phân tích tiến độ học tập của bạn</p>
            </div>

            <div class="progress-dashboard">
                <div class="progress-chart-container">
                    <h3>📊 Biểu Đồ Tiến Độ</h3>
                    <div class="chart-container" style="position: relative; height: 400px;">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>

                <div class="daily-checklist">
                    <h3>✅ Checklist Hàng Ngày</h3>
                    <div class="checklist-items" id="dailyChecklist">
                        <div class="checklist-item">
                            <input type="checkbox" id="novelty-check">
                            <label for="novelty-check">Áp dụng Novelty - có yếu tố mới lạ</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="needle-check">
                            <label for="needle-check">Needle - tập trung 1 điểm sai cụ thể</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="neurochemical-check">
                            <label for="neurochemical-check">Neurochemical Gating - 3 pha học tập</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="night-check">
                            <label for="night-check">Night - ngủ đủ giấc để consolidate memory</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="traps-check">
                            <label for="traps-check">Tránh các bẫy thường gặp</label>
                        </div>
                        <div class="checklist-item">
                            <input type="checkbox" id="reflection-check">
                            <label for="reflection-check">Ghi nhật ký reflection 3 câu hỏi</label>
                        </div>
                    </div>
                </div>

                <div class="mistake-log">
                    <h3>🎯 Log Lỗi Hàng Ngày</h3>
                    <div class="mistake-input">
                        <textarea id="mistakeInput" placeholder="Hôm nay bạn tập trung sửa lỗi gì? Cảm giác như thế nào khi làm đúng?"></textarea>
                        <button class="btn btn--primary" id="saveMistake">💾 Lưu Ghi Chú</button>
                    </div>
                    <div class="mistake-history" id="mistakeHistory">
                        <!-- Mistake history will be populated here -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <div class="modal hidden" id="quickStartModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🚀 Hướng Dẫn Nhanh</h3>
                <button class="modal-close" data-modal="quickStartModal">×</button>
            </div>
            <div class="modal-body">
                <h4>Chào mừng đến với phương pháp học nhanh!</h4>
                <ol>
                    <li><strong>Bắt đầu với Khoa Học Não Bộ:</strong> Hiểu cơ chế neuroplasticity</li>
                    <li><strong>Nắm vững 4N:</strong> Novelty, Needle, Neurochemical, Night</li>
                    <li><strong>Theo lộ trình 2 tuần:</strong> Tuần 1 làm bản đồ, Tuần 2 tối ưu hóa</li>
                    <li><strong>Tránh các bẫy:</strong> Không học lăn man, đảm bảo ngủ đủ</li>
                    <li><strong>Theo dõi tiến độ:</strong> Ghi nhật ký và check progress hàng ngày</li>
                </ol>
            </div>
        </div>
        <div class="modal-backdrop" data-modal="quickStartModal"></div>
    </div>

    <div class="modal hidden" id="skillMapModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🗺️ Xây Dựng Bản Đồ Kỹ Năng</h3>
                <button class="modal-close" data-modal="skillMapModal">×</button>
            </div>
            <div class="modal-body">
                <div class="skill-input">
                    <label>🎯 Kỹ năng chính bạn muốn học:</label>
                    <input type="text" id="mainSkill" placeholder="VD: Viết blog, Chơi guitar, Lập trình Python...">
                </div>
                <div class="skill-breakdown">
                    <label>🔍 Chia nhỏ thành các kỹ năng con:</label>
                    <div id="subSkills">
                        <input type="text" placeholder="Kỹ năng con 1">
                        <input type="text" placeholder="Kỹ năng con 2">
                        <input type="text" placeholder="Kỹ năng con 3">
                    </div>
                    <button class="btn btn--secondary" id="addSubSkill">➕ Thêm kỹ năng con</button>
                </div>
                <div class="modal-actions">
                    <button class="btn btn--primary" id="saveSkillMap">💾 Lưu Bản Đồ</button>
                </div>
            </div>
        </div>
        <div class="modal-backdrop" data-modal="skillMapModal"></div>
    </div>

    <div class="modal hidden" id="reflectionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📝 Nhật Ký Reflection</h3>
                <button class="modal-close" data-modal="reflectionModal">×</button>
            </div>
            <div class="modal-body">
                <div class="reflection-questions">
                    <div class="question-group">
                        <label>❓ Hôm nay mình sửa lỗi nào?</label>
                        <textarea id="reflection1" placeholder="Mô tả lỗi cụ thể bạn đã tập trung sửa..."></textarea>
                    </div>
                    <div class="question-group">
                        <label>❓ Khi làm đúng, cơ thể thấy gì?</label>
                        <textarea id="reflection2" placeholder="Cảm giác khi thực hiện đúng, sự khác biệt so với khi sai..."></textarea>
                    </div>
                    <div class="question-group">
                        <label>❓ Ngày mai 'mũi kim' đâm vào điểm nào?</label>
                        <textarea id="reflection3" placeholder="Lỗi cụ thể sẽ tập trung sửa ngày mai..."></textarea>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn btn--primary" id="saveReflection">💾 Lưu Nhật Ký</button>
                </div>
            </div>
        </div>
        <div class="modal-backdrop" data-modal="reflectionModal"></div>
    </div>

    <script src="app.js"></script>
</body>
</html>