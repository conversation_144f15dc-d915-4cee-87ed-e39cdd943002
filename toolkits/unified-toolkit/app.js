/**
 * Enhanced Unified Toolkit Application
 * Integrates performance optimization, progress tracking, and knowledge management
 */

// Enhanced Unified Toolkit Application with Shared Systems Integration
class UnifiedToolkitApp {
  constructor() {
    this.currentTab = 'algorithm';
    this.currentTheme = 'light';
    this.iframeCache = new Map();
    this.loadingStates = new Map();
    this.performanceOptimizer = null;
    this.progressTracker = null;
    this.knowledgeStore = null;
    this.isInitialized = false;
    
    // Set to light theme only
    document.documentElement.setAttribute('data-color-scheme', 'light');
    
    this.init();
  }

  async init() {
    try {
      console.log('Initializing Enhanced Unified Toolkit...');
      
      // Load shared systems
      await this.loadSharedSystems();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Setup iframe management
      this.setupIframeManagement();
      
      // Setup performance monitoring
      this.setupPerformanceMonitoring();
      
      // Setup progress tracking integration
      this.setupProgressTracking();
      
      // Setup global search integration
      this.setupGlobalSearch();
      
      // Load initial toolkit
      await this.loadToolkit(this.currentTab);
      
      // Set frame heights
      this.setFrameHeights();
      
      this.isInitialized = true;
      console.log('Enhanced Unified Toolkit initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize Enhanced Unified Toolkit:', error);
    }
  }

  async loadSharedSystems() {
    try {
      // Load shared scripts dynamically
      await this.loadScript('../shared/error-handler.js');
      await this.loadScript('../shared/accessibility-enhancer.js');
      await this.loadScript('../shared/content-validator.js');
      await this.loadScript('../shared/testing-framework.js');
      await this.loadScript('../shared/unified-knowledge-store.js');
      await this.loadScript('../shared/performance-optimizer.js');
      await this.loadScript('../shared/unified-progress-tracker.js');
      await this.loadScript('../shared/global-search-system.js');
      await this.loadScript('../shared/knowledge-graph-system.js');
      
      // Initialize systems
      this.errorHandler = new window.StandardizedErrorHandler('unified-toolkit');
      this.accessibilityEnhancer = new window.AccessibilityEnhancer('unified-toolkit');
      this.contentValidator = new window.ContentValidator('unified-toolkit');
      this.testingFramework = new window.TestingFramework('unified-toolkit');
      this.knowledgeStore = window.unifiedKnowledgeStore || new window.UnifiedKnowledgeStore();
      this.performanceOptimizer = window.performanceOptimizer || new window.PerformanceOptimizer();
      this.progressTracker = window.unifiedProgressTracker || new window.UnifiedProgressTracker();
      this.globalSearchSystem = window.globalSearchSystem || new window.GlobalSearchSystem();
      this.knowledgeGraphSystem = window.knowledgeGraphSystem || new window.KnowledgeGraphSystem();
      
      console.log('Shared systems loaded successfully');
    } catch (error) {
      console.warn('Some shared systems failed to load:', error);
      // Continue with basic functionality
    }
  }

  loadScript(src) {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }
      
      const script = document.createElement('script');
      script.src = src;
      script.async = true;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  // Enhanced tab activation with performance optimization and progress tracking
  async activateTab(toolkit) {
    if (this.currentTab === toolkit) return;
    
    const startTime = performance.now();
    this.currentTab = toolkit;
    
    // Update tab states
    this.select('.tab', true).forEach(tab => {
      const isActive = tab.getAttribute('data-toolkit') === toolkit;
      tab.classList.toggle('active', isActive);
      tab.setAttribute('aria-selected', String(isActive));
    });

    // Update frame states with lazy loading
    this.select('.toolkit-frame', true).forEach(frame => {
      const idMatch = frame.id.replace('frame-', '') === toolkit;
      frame.classList.toggle('hidden', !idMatch);
      
      if (idMatch) {
        this.ensureFrameLoaded(frame);
      }
    });

    // Track toolkit visit
    if (this.progressTracker) {
      this.progressTracker.trackActivity(toolkit, 'toolkit-visited', {
        timestamp: new Date(),
        duration: 0
      });
    }

    // Performance optimization - preload likely next toolkits
    if (this.performanceOptimizer) {
      this.preloadRelatedToolkits(toolkit);
    }

    // Persist selection
    try { 
      localStorage.setItem('unifiedToolkit.active', toolkit); 
    } catch {}
    
    const loadTime = performance.now() - startTime;
    console.log(`Tab activation completed in ${loadTime.toFixed(2)}ms`);
  }

  preloadRelatedToolkits(currentToolkit) {
    // Define related toolkits for intelligent preloading
    const relatedToolkits = {
      'algorithm': ['handbook', 'thinkingos'],
      'handbook': ['architecture-explorer', 'backend'],
      'architecture-explorer': ['handbook', 'refactoring'],
      'thinkingos': ['algorithm', 'creative'],
      'workspace-knowledge': ['handbook', 'architecture-explorer']
    };

    const related = relatedToolkits[currentToolkit] || [];
    related.forEach(toolkitId => {
      if (this.performanceOptimizer) {
        this.performanceOptimizer.preloadToolkit(toolkitId);
      }
    });
  }

  setupEventListeners() {
    // Enhanced tab initialization
    this.select('.tab', true).forEach(tab => {
      tab.addEventListener('click', () => this.activateTab(tab.getAttribute('data-toolkit')));
      tab.addEventListener('keydown', e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.activateTab(tab.getAttribute('data-toolkit'));
        }
      });
    });

    // Load saved tab state
    const saved = (() => {
      try { 
        return localStorage.getItem('unifiedToolkit.active'); 
      } catch { 
        return null; 
      }
    })();
    
    this.currentTab = saved || 'algorithm';
    
    // Window resize handler
    window.addEventListener('resize', this.debounce(() => this.setFrameHeights(), 150));
    
    // Cross-frame communication
    window.addEventListener('message', (event) => this.handleFrameMessage(event));
  }

  handleFrameMessage(event) {
    if (!event.data) return;
    
    switch (event.data.type) {
      case 'PROGRESS_UPDATE':
        if (this.progressTracker) {
          this.progressTracker.trackActivity(
            event.data.toolkitId,
            event.data.activityType,
            event.data.activityData
          );
        }
        break;
      case 'KNOWLEDGE_INTERACTION':
        if (this.knowledgeStore) {
          this.knowledgeStore.trackInteraction(
            event.data.toolkitId,
            event.data.conceptId,
            event.data.interactionType
          );
        }
        break;
    }
  }



  // Enhanced iframe management with lazy loading
  setupIframeManagement() {
    // Observe iframes for viewport-based loading
    if (this.performanceOptimizer && this.performanceOptimizer.intersectionObserver) {
      this.select('.toolkit-frame', true).forEach(frame => {
        this.performanceOptimizer.observeElement(frame);
      });
    }
  }

  ensureFrameLoaded(frame) {
    if (!frame) return;
    
    const frameId = frame.id.replace('frame-', '');
    
    // Check if frame is already loaded
    if (frame.getAttribute('src')) {
      this.trackFrameLoad(frameId);
      return;
    }
    
    // Set loading state
    this.loadingStates.set(frameId, true);
    
    const src = frame.getAttribute('data-src');
    if (src) {
      const startTime = performance.now();
      
      frame.setAttribute('src', src);
      
      frame.onload = () => {
        const loadTime = performance.now() - startTime;
        console.log(`Toolkit ${frameId} loaded in ${loadTime.toFixed(2)}ms`);
        
        this.loadingStates.set(frameId, false);
        this.trackFrameLoad(frameId, loadTime);
        
        // Initialize communication with the frame
        this.initializeFrameCommunication(frame, frameId);
      };
      
      frame.onerror = () => {
        console.error(`Failed to load toolkit: ${frameId}`);
        this.loadingStates.set(frameId, false);
      };
    }
  }

  trackFrameLoad(frameId, loadTime = 0) {
    if (this.progressTracker) {
      this.progressTracker.trackActivity(frameId, 'toolkit-loaded', {
        loadTime,
        timestamp: new Date()
      });
    }
  }

  initializeFrameCommunication(frame, frameId) {
    // Send initialization data to the frame
    setTimeout(() => {
      if (frame.contentWindow) {
        try {
          frame.contentWindow.postMessage({
            type: 'TOOLKIT_INIT',
            toolkitId: frameId,
            userId: this.progressTracker?.userId
          }, '*');
        } catch (e) {
          console.warn(`Failed to initialize communication with ${frameId}:`, e);
        }
      }
    }, 100);
  }

  async loadToolkit(toolkitId) {
    // Use performance optimizer if available
    if (this.performanceOptimizer) {
      try {
        await this.performanceOptimizer.optimizedLoad(toolkitId, 'high');
      } catch (error) {
        console.warn(`Performance optimization failed for ${toolkitId}:`, error);
      }
    }
    
    // Activate the tab
    await this.activateTab(toolkitId);
  }

  setFrameHeights() {
    const header = this.select('.header');
    const tabs = this.select('.tabs');
    const headerH = header ? header.offsetHeight : 0;
    const tabsH = tabs ? tabs.offsetHeight : 0;
    const padding = 48;
    const target = Math.max(320, window.innerHeight - headerH - tabsH - padding);
    
    this.select('.frames .toolkit-frame', true).forEach(f => {
      f.style.height = target + 'px';
    });
  }

  setupPerformanceMonitoring() {
    if (!this.performanceOptimizer) return;
    
    // Start performance monitoring
    setInterval(() => {
      const report = this.performanceOptimizer.getPerformanceReport();
      
      if (this.progressTracker) {
        this.progressTracker.trackActivity('unified-toolkit', 'performance-report', {
          report,
          timestamp: new Date()
        });
      }
    }, 60000); // Every minute
  }

  setupProgressTracking() {
    if (!this.progressTracker) return;
    
    // Listen for progress events
    window.addEventListener('unified-progress', (event) => {
      console.log('Progress event:', event.detail);
      
      // Show achievement notifications
      if (event.detail.type === 'achievements-unlocked') {
        this.showAchievementNotification(event.detail.data);
      }
    });
  }

  setupGlobalSearch() {
    if (!this.globalSearchSystem) return;
    
    const searchInput = this.select('#global-search-input');
    const searchButton = this.select('#search-button');
    const searchResults = this.select('#search-results');
    
    if (!searchInput || !searchButton || !searchResults) return;
    
    let searchTimeout;
    let isSearchResultsVisible = false;
    
    // Search input event listeners
    searchInput.addEventListener('input', (e) => {
      clearTimeout(searchTimeout);
      const query = e.target.value.trim();
      
      if (query.length >= 2) {
        searchTimeout = setTimeout(() => {
          this.performGlobalSearch(query);
        }, 300); // Debounce search
      } else {
        this.hideSearchResults();
      }
    });
    
    // Search button click
    searchButton.addEventListener('click', () => {
      const query = searchInput.value.trim();
      if (query) {
        this.performGlobalSearch(query);
      }
    });
    
    // Enter key for search
    searchInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        const query = searchInput.value.trim();
        if (query) {
          this.performGlobalSearch(query);
        }
      } else if (e.key === 'Escape') {
        this.hideSearchResults();
        searchInput.blur();
      }
    });
    
    // Focus search input with Ctrl+K
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        searchInput.focus();
      }
    });
    
    // Hide search results when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.global-search')) {
        this.hideSearchResults();
      }
    });
    
    console.log('Global search setup completed');
  }

  showAchievementNotification(achievements) {
    achievements.forEach(achievement => {
      console.log(`🏆 Achievement unlocked: ${achievement.name}`);
      
      // Create a simple notification
      const notification = document.createElement('div');
      notification.className = 'achievement-notification';
      notification.innerHTML = `
        <div class="achievement-content">
          <span class="achievement-icon">${achievement.icon}</span>
          <div class="achievement-text">
            <div class="achievement-name">${achievement.name}</div>
            <div class="achievement-description">${achievement.description}</div>
          </div>
        </div>
      `;
      
      // Add styling
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--color-bg-2, #f0f0f0);
        border: 2px solid var(--color-accent, #007acc);
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
      `;
      
      document.body.appendChild(notification);
      
      // Remove after 5 seconds
      setTimeout(() => {
        if (notification.parentNode) {
          notification.style.animation = 'slideOut 0.3s ease-in';
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 300);
        }
      }, 5000);
    });
  }

  // Utility methods
  select(el, all = false) {
    return all ? document.querySelectorAll(el) : document.querySelector(el);
  }

  debounce(fn, wait) {
    let t;
    return function () {
      clearTimeout(t);
      t = setTimeout(() => fn.apply(this, arguments), wait);
    };
  }

  // Public API for external access
  getProgressData() {
    return this.progressTracker ? this.progressTracker.getOverallProgress() : null;
  }

  getPerformanceData() {
    return this.performanceOptimizer ? this.performanceOptimizer.getPerformanceReport() : null;
  }

  getKnowledgeOverview() {
    return this.knowledgeStore ? this.knowledgeStore.getKnowledgeOverview() : null;
  }

  getKnowledgeGraphData() {
    return this.knowledgeGraphSystem ? this.knowledgeGraphSystem.getKnowledgeGraphStatistics() : null;
  }

  getRelatedConcepts(conceptId, options) {
    return this.knowledgeGraphSystem ? this.knowledgeGraphSystem.getRelatedConcepts(conceptId, options) : [];
  }

  getLearningPath(fromConceptId, toConceptId) {
    return this.knowledgeGraphSystem ? this.knowledgeGraphSystem.getLearningPath(fromConceptId, toConceptId) : null;
  }

  // Global Search Methods
  async performGlobalSearch(query) {
    if (!this.globalSearchSystem || !this.globalSearchSystem.isInitialized()) {
      console.warn('Global search system not available');
      return;
    }
    
    try {
      const searchResults = this.globalSearchSystem.search(query, {
        limit: 10,
        includeRelated: true
      });
      
      this.displaySearchResults(searchResults);
      
      // Track search analytics
      if (this.progressTracker) {
        this.progressTracker.trackActivity('unified-toolkit', 'global-search', {
          query,
          resultsCount: searchResults.totalResults,
          timestamp: new Date()
        });
      }
      
    } catch (error) {
      console.error('Search failed:', error);
      this.showSearchError();
    }
  }
  
  displaySearchResults(searchResults) {
    const searchResultsContainer = this.select('#search-results');
    if (!searchResultsContainer) return;
    
    if (searchResults.results.length === 0) {
      searchResultsContainer.innerHTML = `
        <div class="search-no-results">
          <p>No results found for "${searchResults.query}"</p>
        </div>
        ${this.renderSearchSuggestions(searchResults.suggestions)}
      `;
    } else {
      searchResultsContainer.innerHTML = searchResults.results.map(result => {
        const knowledgeGraphRelated = this.getKnowledgeGraphRelated(result);
        
        return `
          <div class="search-result-item" data-toolkit="${result.toolkitId}" data-concept="${result.id}">
            <div class="result-header">
              <span class="result-toolkit-icon">${result.toolkitIcon}</span>
              <span class="result-toolkit-name">${result.toolkitName}</span>
            </div>
            <div class="result-title">${result.highlightedTitle}</div>
            <div class="result-description">${result.highlightedDescription}</div>
            <div class="result-meta">
              <span class="result-difficulty">${result.difficulty}</span>
              <span class="result-time">
                <span>⏱️</span>
                <span>${result.estimatedTime} min</span>
              </span>
            </div>
            ${knowledgeGraphRelated ? this.renderKnowledgeGraphRelated(knowledgeGraphRelated) : ''}
          </div>
        `;
      }).join('');
    }
    
    // Add click handlers for search results
    searchResultsContainer.querySelectorAll('.search-result-item').forEach(item => {
      item.addEventListener('click', () => {
        const toolkitId = item.dataset.toolkit;
        const conceptId = item.dataset.concept;
        this.navigateToToolkitConcept(toolkitId, conceptId);
        this.hideSearchResults();
      });
    });
    
    // Add click handlers for related concepts
    searchResultsContainer.querySelectorAll('.related-concept').forEach(item => {
      item.addEventListener('click', (e) => {
        e.stopPropagation();
        const toolkitId = item.dataset.toolkit;
        const conceptId = item.dataset.concept;
        this.navigateToToolkitConcept(toolkitId, conceptId);
        this.hideSearchResults();
      });
    });
    
    this.showSearchResults();
  }
  
  renderSearchSuggestions(suggestions) {
    if (!suggestions || suggestions.length === 0) return '';
    
    return `
      <div class="search-suggestions">
        <div class="search-suggestions-title">Suggestions:</div>
        <div class="search-suggestions-list">
          ${suggestions.map(suggestion => `
            <span class="search-suggestion" data-query="${suggestion}">${suggestion}</span>
          `).join('')}
        </div>
      </div>
    `;
  }
  
  showSearchResults() {
    const searchResults = this.select('#search-results');
    if (searchResults) {
      searchResults.style.display = 'block';
    }
  }
  
  hideSearchResults() {
    const searchResults = this.select('#search-results');
    if (searchResults) {
      searchResults.style.display = 'none';
    }
  }
  
  showSearchError() {
    const searchResults = this.select('#search-results');
    if (searchResults) {
      searchResults.innerHTML = `
        <div class="search-no-results">
          <p>Search temporarily unavailable. Please try again.</p>
        </div>
      `;
      this.showSearchResults();
    }
  }

  // Knowledge Graph Integration Methods
  getKnowledgeGraphRelated(searchResult) {
    if (!this.knowledgeGraphSystem || !this.knowledgeGraphSystem.isInitialized()) {
      return null;
    }
    
    try {
      // Try to find the concept in the knowledge graph
      const conceptId = `${searchResult.toolkitId.replace('-toolkit', '')}:${searchResult.id}`;
      const relatedConcepts = this.knowledgeGraphSystem.getRelatedConcepts(conceptId, {
        maxResults: 3,
        minStrength: 0.6
      });
      
      return relatedConcepts.length > 0 ? relatedConcepts : null;
    } catch (error) {
      console.warn('Failed to get knowledge graph related concepts:', error);
      return null;
    }
  }
  
  renderKnowledgeGraphRelated(relatedConcepts) {
    if (!relatedConcepts || relatedConcepts.length === 0) return '';
    
    return `
      <div class="result-related-concepts">
        <div class="related-concepts-title">Related Concepts:</div>
        <div class="related-concepts-list">
          ${relatedConcepts.map(related => `
            <span class="related-concept" 
                  data-toolkit="${related.concept.toolkit}" 
                  data-concept="${related.concept.id.split(':')[1]}" 
                  title="${related.relationship.description}">
              ${related.concept.title} 
              <span class="relationship-type">(${related.relationship.type})</span>
            </span>
          `).join('')}
        </div>
      </div>
    `;
  }

  // Enhanced navigation with knowledge graph awareness
  navigateToToolkitConcept(toolkitId, conceptId) {
    // Navigate to the specific toolkit
    this.activateTab(toolkitId);
    
    // Track knowledge graph navigation
    if (this.knowledgeGraphSystem && this.progressTracker) {
      this.progressTracker.trackActivity('unified-toolkit', 'knowledge-graph-navigation', {
        fromToolkit: this.currentTab,
        toToolkit: toolkitId,
        conceptId: conceptId,
        timestamp: new Date()
      });
    }
    
    // Try to navigate to specific concept within the toolkit
    setTimeout(() => {
      const frame = this.select(`#frame-${toolkitId}`);
      if (frame && frame.contentWindow) {
        try {
          frame.contentWindow.postMessage({
            type: 'NAVIGATE_TO_CONCEPT',
            conceptId: conceptId,
            source: 'global-search'
          }, '*');
        } catch (e) {
          console.warn(`Failed to navigate to concept ${conceptId} in ${toolkitId}:`, e);
        }
      }
    }, 500); // Wait for frame to load
  }
}

// Add CSS for achievement notifications
const style = document.createElement('style');
style.textContent = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
  
  .achievement-notification {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .achievement-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .achievement-icon {
    font-size: 24px;
  }
  
  .achievement-name {
    font-weight: bold;
    margin-bottom: 4px;
    color: var(--color-text, #333);
  }
  
  .achievement-description {
    font-size: 14px;
    color: var(--color-text-secondary, #666);
  }
`;
document.head.appendChild(style);

// Initialize the enhanced unified toolkit when DOM is ready
document.addEventListener('DOMContentLoaded', function () {
  window.unifiedToolkitApp = new UnifiedToolkitApp();
});

// Export for external access
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UnifiedToolkitApp;
}
