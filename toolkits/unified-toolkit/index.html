<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Unified Thinking Toolkits - Enhanced Platform</title>
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧰</text></svg>">
  <link rel="stylesheet" href="style.css" />
  <link rel="preload" as="script" href="../shared/error-handler.js">
  <link rel="preload" as="script" href="../shared/accessibility-enhancer.js">
  <link rel="preload" as="script" href="../shared/content-validator.js">
  <link rel="preload" as="script" href="../shared/testing-framework.js">
  <link rel="preload" as="script" href="../shared/unified-knowledge-store.js">
  <link rel="preload" as="script" href="../shared/performance-optimizer.js">
  <link rel="preload" as="script" href="../shared/unified-progress-tracker.js">
  <link rel="preload" as="fetch" href="../algorithm-thinking-toolkit/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../software-refactoring-toolkit/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../software-archaeology-handbook/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../backend-toolkit-handbook/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../master-any-skill-toolkit/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../software-archaeology-toolkit/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../software-toolkit-handbook/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../golang-toolkit/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../complete-linux-handbook/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../software-architecture-explorer/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../frontend-handbook/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../comprehensive-mobile-handbook/index.html" crossorigin>
  <link rel="preload" as="fetch" href="../workspace-knowledge-toolkit/index.html" crossorigin>
</head>
<body>
  <header class="header">
    <div class="container header__content">
      <div class="logo">
        <div class="logo__icon">🧰</div>
        <h1 class="logo__title">Unified Thinking Toolkits</h1>
        <span class="version-badge">v2.0 Enhanced</span>
      </div>
      <div class="header__actions">
        <div class="global-search" id="global-search">
          <div class="search-container">
            <input type="text" id="global-search-input" class="search-input" placeholder="Search across all toolkits... (Ctrl+K)" autocomplete="off">
            <button id="search-button" class="search-button" title="Search">🔍</button>
          </div>
          <div class="search-results" id="search-results" style="display: none;"></div>
        </div>
        <div class="progress-indicator" id="progress-indicator" style="display: none;">
          <span class="progress-text">Progress: <span id="progress-value">0%</span></span>
        </div>
      </div>
    </div>
  </header>

  <main class="main">
    <div class="container">
      <nav class="tabs" role="tablist" aria-label="Toolkits">
        <button class="tab active" role="tab" aria-selected="true" data-toolkit="algorithm" id="tab-algorithm">Algorithm Thinking</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="creative" id="tab-creative">Creative Thinking</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="mst" id="tab-mst">MST Toolkit</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="handbook" id="tab-handbook">SE Handbook</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="thinkingos" id="tab-thinkingos">Thinking OS</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="refactoring" id="tab-refactoring">Software Refactoring</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="archaeology" id="tab-archaeology">Software Archaeology</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="backend" id="tab-backend">Backend Toolkit</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="master-skill" id="tab-master-skill">Master Any Skill</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="archaeology-toolkit" id="tab-archaeology-toolkit">Software Archaeology Toolkit</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="software-toolkit" id="tab-software-toolkit">Software Toolkit</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="golang" id="tab-golang">Golang</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="linux-handbook" id="tab-linux-handbook">Linux Handbook</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="architecture-explorer" id="tab-architecture-explorer">Architecture Explorer</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="frontend-handbook" id="tab-frontend-handbook">Frontend Handbook</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="mobile-handbook" id="tab-mobile-handbook">Mobile Handbook</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="workspace-knowledge" id="tab-workspace-knowledge">🧠 Knowledge Hub</button>
      </nav>

      <section class="frames">
        <iframe
          id="frame-algorithm"
          class="toolkit-frame"
          title="Algorithm Thinking Toolkit"
          src="../algorithm-thinking-toolkit/index.html"
          loading="eager"
        ></iframe>

        <iframe
          id="frame-creative"
          class="toolkit-frame hidden"
          title="Creative Thinking Toolkit"
          data-src="../creative-thinking-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-mst"
          class="toolkit-frame hidden"
          title="MST Toolkit"
          data-src="../mst-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-handbook"
          class="toolkit-frame hidden"
          title="Software Engineer Handbook"
          data-src="../software-engineer-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-thinkingos"
          class="toolkit-frame hidden"
          title="Thinking OS Toolkit"
          data-src="../thinking-os-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-refactoring"
          class="toolkit-frame hidden"
          title="Software Refactoring Toolkit"
          data-src="../software-refactoring-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-archaeology"
          class="toolkit-frame hidden"
          title="Software Archaeology Handbook"
          data-src="../software-archaeology-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-backend"
          class="toolkit-frame hidden"
          title="Backend Toolkit Handbook"
          data-src="../backend-toolkit-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-master-skill"
          class="toolkit-frame hidden"
          title="Master Any Skill Toolkit"
          data-src="../master-any-skill-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-archaeology-toolkit"
          class="toolkit-frame hidden"
          title="Software Archaeology Toolkit"
          data-src="../software-archaeology-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-software-toolkit"
          class="toolkit-frame hidden"
          title="Software Toolkit Handbook"
          data-src="../software-toolkit-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-golang"
          class="toolkit-frame hidden"
          title="Golang Toolkit"
          data-src="../golang-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-linux-handbook"
          class="toolkit-frame hidden"
          title="Complete Linux Handbook"
          data-src="../complete-linux-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-architecture-explorer"
          class="toolkit-frame hidden"
          title="Software Architecture Explorer"
          data-src="../software-architecture-explorer/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-frontend-handbook"
          class="toolkit-frame hidden"
          title="Frontend Engineering Master Handbook"
          data-src="../frontend-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-mobile-handbook"
          class="toolkit-frame hidden"
          title="Comprehensive Mobile Engineering Handbook"
          data-src="../comprehensive-mobile-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-workspace-knowledge"
          class="toolkit-frame hidden"
          title="Workspace Knowledge Toolkit"
          data-src="../workspace-knowledge-toolkit/index.html"
          loading="lazy"
        ></iframe>
      </section>
    </div>
  </main>

  <script src="app.js"></script>
</body>
</html>


