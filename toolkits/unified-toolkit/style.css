:root {
  --color-bg: #fcfcf9;
  --color-surface: #fffffd;
  --color-text: #13343b;
  --color-text-secondary: #626c71;
  --color-primary: #21808d;
  --color-primary-hover: #1d7480;
  --color-border: rgba(94, 82, 64, 0.2);
}



html {
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
  color: var(--color-text);
  background: var(--color-bg);
}

body { margin: 0; }

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 16px;
}

.header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
}

.logo { display: flex; align-items: center; gap: 12px; }
.logo__icon { font-size: 24px; background: rgba(6,182,212,0.08); padding: 8px; border-radius: 8px; }
.logo__title { margin: 0; font-size: 20px; color: var(--color-primary); }

.btn { cursor: pointer; border: 1px solid var(--color-border); background: var(--color-surface); color: var(--color-text); padding: 8px 12px; border-radius: 8px; }
.btn:hover { background: rgba(94,82,64,0.08); }

.main { padding: 24px 0; }

.tabs { display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 12px; }
.tab {
  padding: 8px 12px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  color: var(--color-text-secondary);
}
.tab:hover { color: var(--color-text); border-color: var(--color-primary); }
.tab.active { background: var(--color-primary); color: #fff; border-color: var(--color-primary); }

.frames { position: relative; }
.toolkit-frame { width: 100%; height: 70vh; border: 1px solid var(--color-border); border-radius: 10px; background: var(--color-surface); }
.hidden { display: none; }

@media (max-width: 768px) {
  .toolkit-frame { height: 65vh; }
}

/* Global Search Styles */
.global-search {
  position: relative;
  width: 300px;
  margin-right: 16px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 8px 40px 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background: var(--color-surface);
  color: var(--color-text);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(33, 128, 141, 0.1);
}

.search-input::placeholder {
  color: var(--color-text-secondary);
}

.search-button {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 16px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.search-button:hover {
  opacity: 1;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 4px;
}

.search-result-item {
  padding: 12px;
  border-bottom: 1px solid var(--color-border);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-result-item:hover {
  background: rgba(33, 128, 141, 0.05);
}

.search-result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.result-toolkit-icon {
  font-size: 16px;
}

.result-toolkit-name {
  font-size: 12px;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.result-title {
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: 4px;
  font-size: 14px;
}

.result-description {
  font-size: 13px;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 6px;
  font-size: 12px;
  color: var(--color-text-secondary);
}

.result-difficulty {
  padding: 2px 6px;
  background: rgba(33, 128, 141, 0.1);
  border-radius: 4px;
  font-size: 11px;
}

.result-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

.search-no-results {
  padding: 20px;
  text-align: center;
  color: var(--color-text-secondary);
}

.search-suggestions {
  padding: 12px;
  border-top: 1px solid var(--color-border);
}

.search-suggestions-title {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.search-suggestion {
  display: inline-block;
  padding: 4px 8px;
  margin: 2px 4px 2px 0;
  background: rgba(33, 128, 141, 0.1);
  border-radius: 4px;
  font-size: 12px;
  color: var(--color-primary);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-suggestion:hover {
  background: rgba(33, 128, 141, 0.2);
}

/* Highlight matched text */
.search-result-item mark {
  background: rgba(255, 235, 59, 0.3);
  color: inherit;
  padding: 1px 2px;
  border-radius: 2px;
}



/* Knowledge Graph Related Concepts */
.result-related-concepts {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--color-border);
}

.related-concepts-title {
  font-size: 11px;
  color: var(--color-text-secondary);
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.related-concepts-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.related-concept {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 3px 6px;
  background: rgba(33, 128, 141, 0.08);
  border: 1px solid rgba(33, 128, 141, 0.2);
  border-radius: 12px;
  font-size: 11px;
  color: var(--color-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.related-concept:hover {
  background: rgba(33, 128, 141, 0.15);
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

.relationship-type {
  font-size: 10px;
  opacity: 0.7;
  font-style: italic;
}



/* Responsive search */
@media (max-width: 768px) {
  .global-search {
    width: 200px;
  }
  
  .search-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .search-results {
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .global-search {
    width: 150px;
  }
  
  .search-input::placeholder {
    content: "Search...";
  }
}


