// Enhanced Mobile Engineering Handbook JavaScript - Fixed Version
class MobileEngineeringHandbook {
    constructor() {
        this.currentTheme = 'light';
        this.currentSection = 'foundations';
        this.bookmarks = new Map();
        this.checkedItems = new Set();
        this.searchData = [];
        this.userProgress = {
            sectionsVisited: new Set(),
            topicsCompleted: new Set(),
            timeSpent: {},
            lastVisit: Date.now()
        };
        
        this.init();
    }

    init() {
        console.log('📱 Initializing Mobile Engineering Handbook...');
        
        this.loadUserData();
        this.bindEvents();
        this.buildSearchIndex();
        this.updateProgress();
        this.setupKeyboardNavigation();
        this.initializeFAB();
        
        // Set light theme as default per project specification
        document.documentElement.setAttribute('data-color-scheme', 'light');
        
        // Initialize expandable sections
        setTimeout(() => {
            this.initializeExpandableSections();
        }, 100);
        
        console.log('✅ Mobile Engineering Handbook initialized successfully!');
    }

    bindEvents() {
        // Sidebar toggle for mobile
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                console.log('Navigating to section:', section);
                this.showSection(section);
            });
        });

        // Category title expansions
        document.querySelectorAll('.category-title').forEach(title => {
            title.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Category title clicked:', title.textContent);
                this.toggleCategory(title);
            });
        });

        // Topic header expansions
        document.querySelectorAll('.topic-header').forEach(header => {
            if (!header.closest('.topic-actions')) {
                header.addEventListener('click', (e) => {
                    // Don't trigger if clicking on actions
                    if (!e.target.closest('.topic-actions')) {
                        e.preventDefault();
                        console.log('Topic header clicked');
                        this.toggleTopicContent(header);
                    }
                });
            }
        });

        // Checkbox tracking
        document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (_e) => {
                console.log('Checkbox changed:', checkbox.checked);
                this.handleCheckboxChange(checkbox);
            });
        });

        // Bookmark buttons
        document.querySelectorAll('.bookmark-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                console.log('Bookmark button clicked');
                this.toggleBookmark(btn);
            });
        });

        // Copy code buttons
        document.querySelectorAll('.copy-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.copyCode(btn);
            });
        });

        // Search functionality
        const searchBtn = document.getElementById('searchBtn');
        const searchInput = document.getElementById('searchInput');
        
        if (searchBtn) {
            searchBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Search button clicked');
                this.handleSearch();
            });
        }

        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleSearch();
                }
            });
        }

        // Modal controls
        this.bindModalEvents();

        // Bookmarks panel
        this.bindBookmarkEvents();

        // Export functionality
        this.bindExportEvents();

        // Quick reference toggle
        const toggleReference = document.getElementById('toggleReference');
        if (toggleReference) {
            toggleReference.addEventListener('click', () => {
                this.toggleQuickReference();
            });
        }

        // Listen for system theme changes - removed for light-only mode

        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    bindModalEvents() {
        // Search modal
        const closeSearch = document.getElementById('closeSearch');
        const searchModal = document.getElementById('searchModal');
        
        if (closeSearch) {
            closeSearch.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal('searchModal');
            });
        }

        if (searchModal) {
            searchModal.addEventListener('click', (e) => {
                if (e.target === searchModal || e.target.classList.contains('modal-overlay')) {
                    this.closeModal('searchModal');
                }
            });
        }

        // Export modal
        const closeExport = document.getElementById('closeExport');
        const exportModal = document.getElementById('exportModal');
        
        if (closeExport) {
            closeExport.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal('exportModal');
            });
        }

        if (exportModal) {
            exportModal.addEventListener('click', (e) => {
                if (e.target === exportModal || e.target.classList.contains('modal-overlay')) {
                    this.closeModal('exportModal');
                }
            });
        }
    }

    bindBookmarkEvents() {
        const bookmarkToggle = document.getElementById('bookmarkToggle');
        const closeBookmarks = document.getElementById('closeBookmarks');
        const clearBookmarks = document.getElementById('clearBookmarks');
        const bookmarkSearch = document.getElementById('bookmarkSearch');

        if (bookmarkToggle) {
            bookmarkToggle.addEventListener('click', () => {
                this.toggleBookmarksPanel();
            });
        }

        if (closeBookmarks) {
            closeBookmarks.addEventListener('click', () => {
                this.closeBookmarksPanel();
            });
        }

        if (clearBookmarks) {
            clearBookmarks.addEventListener('click', () => {
                this.clearAllBookmarks();
            });
        }

        if (bookmarkSearch) {
            bookmarkSearch.addEventListener('input', (e) => {
                this.filterBookmarks(e.target.value);
            });
        }
    }

    bindExportEvents() {
        const exportBtn = document.getElementById('exportBtn');
        const exportPDF = document.getElementById('exportPDF');
        const exportMarkdown = document.getElementById('exportMarkdown');
        const exportBookmarks = document.getElementById('exportBookmarks');

        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.openModal('exportModal');
            });
        }

        if (exportPDF) {
            exportPDF.addEventListener('click', () => {
                this.exportToPDF();
            });
        }

        if (exportMarkdown) {
            exportMarkdown.addEventListener('click', () => {
                this.exportToMarkdown();
            });
        }

        if (exportBookmarks) {
            exportBookmarks.addEventListener('click', () => {
                this.exportBookmarksToFile();
            });
        }
    }

    initializeFAB() {
        const fabMain = document.getElementById('fabMain');
        const fabBookmarks = document.getElementById('fabBookmarks');
        const fabSearch = document.getElementById('fabSearch');
        const fabProgress = document.getElementById('fabProgress');

        if (fabMain) {
            fabMain.addEventListener('click', () => {
                this.toggleFABMenu();
            });
        }

        if (fabBookmarks) {
            fabBookmarks.addEventListener('click', () => {
                this.toggleBookmarksPanel();
                this.closeFABMenu();
            });
        }

        if (fabSearch) {
            fabSearch.addEventListener('click', () => {
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.focus();
                }
                this.closeFABMenu();
            });
        }

        if (fabProgress) {
            fabProgress.addEventListener('click', () => {
                this.showProgressDetails();
                this.closeFABMenu();
            });
        }
    }

    toggleFABMenu() {
        const fabMenu = document.querySelector('.fab-menu');
        const fabMain = document.getElementById('fabMain');
        
        if (fabMenu && fabMain) {
            const isVisible = fabMenu.classList.contains('visible');
            if (isVisible) {
                this.closeFABMenu();
            } else {
                fabMenu.classList.add('visible');
                fabMain.style.transform = 'rotate(45deg)';
            }
        }
    }

    closeFABMenu() {
        const fabMenu = document.querySelector('.fab-menu');
        const fabMain = document.getElementById('fabMain');
        
        if (fabMenu && fabMain) {
            fabMenu.classList.remove('visible');
            fabMain.style.transform = 'rotate(0deg)';
        }
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('visible');
        }
    }

    showSection(sectionId) {
        console.log('Showing section:', sectionId);
        
        // Track section visit
        this.userProgress.sectionsVisited.add(sectionId);

        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(`${sectionId}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            console.log('Section found and activated:', sectionId);
        } else {
            console.warn('Section not found:', `${sectionId}-section`);
        }

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const activeLink = document.querySelector(`[data-section="${sectionId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        this.currentSection = sectionId;

        // Close sidebar on mobile after navigation
        if (globalThis.innerWidth <= 768) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.remove('visible');
            }
        }

        // Scroll to top of content
        globalThis.scrollTo({ top: 0, behavior: 'smooth' });

        this.saveUserData();
    }

    initializeExpandableSections() {
        console.log('Initializing expandable sections...');
        
        // Initialize categories based on data-expanded attribute
        document.querySelectorAll('.category-title').forEach(title => {
            const isExpanded = title.getAttribute('data-expanded') === 'true';
            const content = title.nextElementSibling;
            
            if (isExpanded && content) {
                title.classList.add('expanded');
                content.classList.add('expanded');
                console.log('Category expanded:', title.textContent);
            }
        });

        // Initialize topic content that should be expanded
        document.querySelectorAll('.topic-content.expanded').forEach(content => {
            content.style.display = 'block';
            console.log('Topic content shown');
        });
    }

    toggleCategory(titleElement) {
        console.log('Toggling category:', titleElement.textContent);
        
        const _content = titleElement.nextElementSibling;
        const isExpanded = titleElement.classList.contains('expanded');

        if (isExpanded) {
            this.collapseCategory(titleElement);
        } else {
            this.expandCategory(titleElement);
        }
    }

    expandCategory(titleElement) {
        const content = titleElement.nextElementSibling;
        if (content) {
            titleElement.classList.add('expanded');
            content.classList.add('expanded');
            titleElement.setAttribute('data-expanded', 'true');
            console.log('Category expanded');
        }
    }

    collapseCategory(titleElement) {
        const content = titleElement.nextElementSibling;
        if (content) {
            titleElement.classList.remove('expanded');
            content.classList.remove('expanded');
            titleElement.setAttribute('data-expanded', 'false');
            console.log('Category collapsed');
        }
    }

    toggleTopicContent(headerElement) {
        console.log('Toggling topic content');
        
        const topicCard = headerElement.closest('.topic-card');
        const content = topicCard?.querySelector('.topic-content');
        
        if (content) {
            const isExpanded = content.classList.contains('expanded');
            if (isExpanded) {
                content.classList.remove('expanded');
                console.log('Topic content collapsed');
            } else {
                content.classList.add('expanded');
                console.log('Topic content expanded');
            }
        }
    }

    handleCheckboxChange(checkbox) {
        console.log('Handling checkbox change:', checkbox.checked);
        
        const topicCard = checkbox.closest('.topic-card');
        const topicHeader = topicCard?.querySelector('.topic-header h4');
        const topicText = topicHeader?.textContent?.trim();
        
        if (!topicText) return;
        
        if (checkbox.checked) {
            this.checkedItems.add(topicText);
            this.userProgress.topicsCompleted.add(topicText);
            if (topicCard) {
                topicCard.classList.add('completed');
            }
            this.showNotification('✅ Chủ đề đã hoàn thành!', 'success');
        } else {
            this.checkedItems.delete(topicText);
            this.userProgress.topicsCompleted.delete(topicText);
            if (topicCard) {
                topicCard.classList.remove('completed');
            }
        }

        this.updateProgress();
        this.saveUserData();
    }

    updateProgress() {
        const totalItems = document.querySelectorAll('.topic-checkbox').length;
        const completedItems = this.checkedItems.size;
        const percentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

        console.log(`Progress: ${completedItems}/${totalItems} (${percentage}%)`);

        // Update progress bar
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${percentage}% hoàn thành`;
        }

        // Update progress stats
        const completedTopics = document.getElementById('completedTopics');
        const totalTopics = document.getElementById('totalTopics');

        if (completedTopics) {
            completedTopics.textContent = completedItems;
        }
        
        if (totalTopics) {
            totalTopics.textContent = totalItems;
        }
    }

    toggleBookmark(button) {
        console.log('Toggling bookmark');
        
        const topicCard = button.closest('.topic-card');
        if (!topicCard) return;

        const topicHeader = topicCard.querySelector('.topic-header h4');
        const topicText = topicHeader?.textContent?.trim();
        const section = topicCard.closest('.section');
        const sectionHeader = section?.querySelector('.section__header h2');
        const sectionTitle = sectionHeader?.textContent || '';

        if (!topicText) return;

        if (this.bookmarks.has(topicText)) {
            this.bookmarks.delete(topicText);
            button.classList.remove('bookmarked');
            button.title = 'Đánh dấu';
            this.showNotification('🔖 Đã bỏ bookmark', 'info');
        } else {
            this.bookmarks.set(topicText, {
                title: topicText,
                section: sectionTitle,
                sectionId: this.currentSection,
                timestamp: Date.now()
            });
            button.classList.add('bookmarked');
            button.title = 'Bỏ đánh dấu';
            this.showNotification('🔖 Đã thêm vào bookmark!', 'success');
        }

        this.updateBookmarksList();
        this.saveUserData();
    }

    updateBookmarksList() {
        const bookmarksList = document.getElementById('bookmarksList');
        if (!bookmarksList) return;
        
        if (this.bookmarks.size === 0) {
            bookmarksList.innerHTML = `
                <div class="empty-bookmarks">
                    <p>🔖 Chưa có bookmark nào</p>
                    <small>Click vào icon 🔖 để đánh dấu các chủ đề quan trọng</small>
                </div>
            `;
            return;
        }

        let bookmarksHTML = '';
        const sortedBookmarks = Array.from(this.bookmarks.values())
            .sort((a, b) => b.timestamp - a.timestamp);

        sortedBookmarks.forEach(bookmark => {
            bookmarksHTML += `
                <div class="bookmark-item" onclick="app.navigateToBookmark('${bookmark.sectionId}', '${bookmark.title.replace(/'/g, "\\'")}')">
                    <div class="bookmark-title">${bookmark.title}</div>
                    <div class="bookmark-section">${bookmark.section}</div>
                </div>
            `;
        });

        bookmarksList.innerHTML = bookmarksHTML;
    }

    navigateToBookmark(sectionId, title) {
        console.log('Navigating to bookmark:', sectionId, title);
        
        this.closeBookmarksPanel();
        this.showSection(sectionId);
        
        setTimeout(() => {
            const targetElement = this.findTopicByTitle(title);
            if (targetElement) {
                // Expand parent category if needed
                const category = targetElement.closest('.topic-category');
                if (category) {
                    const categoryTitle = category.querySelector('.category-title');
                    if (categoryTitle && !categoryTitle.classList.contains('expanded')) {
                        this.expandCategory(categoryTitle);
                    }
                }
                
                // Expand topic content
                const topicHeader = targetElement.querySelector('.topic-header');
                if (topicHeader) {
                    const content = targetElement.querySelector('.topic-content');
                    if (content && !content.classList.contains('expanded')) {
                        content.classList.add('expanded');
                    }
                }
                
                // Scroll to and highlight
                targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                targetElement.classList.add('highlight');
                
                setTimeout(() => {
                    targetElement.classList.remove('highlight');
                }, 3000);
            }
        }, 300);
    }

    findTopicByTitle(title) {
        const topics = document.querySelectorAll('.topic-card');
        for (const topic of topics) {
            const header = topic.querySelector('.topic-header h4');
            if (header && header.textContent.trim() === title) {
                return topic;
            }
        }
        return null;
    }

    toggleBookmarksPanel() {
        const panel = document.getElementById('bookmarksPanel');
        if (panel) {
            const isVisible = panel.classList.contains('visible');
            if (isVisible) {
                this.closeBookmarksPanel();
            } else {
                panel.classList.remove('hidden');
                setTimeout(() => panel.classList.add('visible'), 10);
            }
        }
    }

    closeBookmarksPanel() {
        const panel = document.getElementById('bookmarksPanel');
        if (panel) {
            panel.classList.remove('visible');
            setTimeout(() => panel.classList.add('hidden'), 300);
        }
    }

    clearAllBookmarks() {
        if (confirm('Bạn có chắc muốn xóa tất cả bookmarks?')) {
            this.bookmarks.clear();
            this.updateBookmarksList();
            this.saveUserData();
            
            // Update bookmark buttons
            document.querySelectorAll('.bookmark-btn').forEach(btn => {
                btn.classList.remove('bookmarked');
                btn.title = 'Đánh dấu';
            });
            
            this.showNotification('🗑️ Đã xóa tất cả bookmarks', 'info');
        }
    }

    filterBookmarks(query) {
        const bookmarkItems = document.querySelectorAll('.bookmark-item');
        const searchTerm = query.toLowerCase().trim();
        
        bookmarkItems.forEach(item => {
            const title = item.querySelector('.bookmark-title')?.textContent.toLowerCase() || '';
            const section = item.querySelector('.bookmark-section')?.textContent.toLowerCase() || '';
            
            if (title.includes(searchTerm) || section.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    buildSearchIndex() {
        console.log('Building search index...');
        this.searchData = [];

        // Index topic cards
        document.querySelectorAll('.topic-card').forEach(card => {
            const header = card.querySelector('.topic-header h4');
            const content = card.querySelector('.topic-content');
            
            if (header) {
                const title = header.textContent.trim();
                const section = card.closest('.section');
                const sectionHeader = section?.querySelector('.section__header h2');
                const sectionTitle = sectionHeader?.textContent || '';
                const sectionId = section?.id.replace('-section', '') || '';
                const contentText = content?.textContent?.trim() || '';

                this.searchData.push({
                    title: title,
                    content: contentText,
                    section: sectionTitle,
                    sectionId: sectionId,
                    type: 'topic',
                    element: card,
                    keywords: this.extractKeywords(title + ' ' + contentText)
                });
            }
        });

        // Index section headers
        document.querySelectorAll('.section__header h2').forEach(header => {
            const section = header.closest('.section');
            const sectionId = section?.id.replace('-section', '') || '';
            const description = section?.querySelector('.section__description')?.textContent || '';
            
            this.searchData.push({
                title: header.textContent.trim(),
                content: description,
                section: header.textContent.trim(),
                sectionId: sectionId,
                type: 'section',
                element: header,
                keywords: this.extractKeywords(header.textContent + ' ' + description)
            });
        });

        // Index code examples
        document.querySelectorAll('.code-example').forEach(codeBlock => {
            const title = codeBlock.querySelector('.code-header h5')?.textContent || 'Code Example';
            const code = codeBlock.querySelector('code')?.textContent || '';
            const section = codeBlock.closest('.section');
            const sectionId = section?.id.replace('-section', '') || '';
            const sectionTitle = section?.querySelector('.section__header h2')?.textContent || '';

            this.searchData.push({
                title: title,
                content: code,
                section: sectionTitle,
                sectionId: sectionId,
                type: 'code',
                element: codeBlock,
                keywords: this.extractKeywords(title + ' ' + code)
            });
        });

        console.log(`📊 Search index built with ${this.searchData.length} items`);
    }

    extractKeywords(text) {
        return text
            .toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 2)
            .slice(0, 20);
    }

    handleSearch() {
        console.log('Handling search...');
        
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;

        const query = searchInput.value.trim();
        if (!query) {
            this.showNotification('Vui lòng nhập từ khóa tìm kiếm', 'warning');
            return;
        }

        console.log('Searching for:', query);
        const results = this.searchContent(query);
        this.displaySearchResults(results, query);
        this.openModal('searchModal');
    }

    searchContent(query) {
        const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 1);
        
        return this.searchData
            .map(item => {
                let score = 0;
                const titleLower = item.title.toLowerCase();
                const contentLower = item.content.toLowerCase();
                
                searchTerms.forEach(term => {
                    // Exact title match (highest score)
                    if (titleLower === term) score += 100;
                    else if (titleLower.includes(term)) score += 50;
                    
                    // Content match
                    if (contentLower.includes(term)) score += 10;
                    
                    // Keywords match
                    if (item.keywords.includes(term)) score += 20;
                });
                
                return { ...item, score };
            })
            .filter(item => item.score > 0)
            .sort((a, b) => {
                if (b.score !== a.score) return b.score - a.score;
                
                const typePriority = { section: 3, topic: 2, code: 1 };
                return (typePriority[b.type] || 0) - (typePriority[a.type] || 0);
            })
            .slice(0, 20);
    }

    displaySearchResults(results, query) {
        console.log('Displaying search results:', results.length);
        
        const resultsContainer = document.getElementById('searchResults');
        if (!resultsContainer) return;
        
        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="search-placeholder">
                    <p>Không tìm thấy kết quả cho "<strong>${query}</strong>"</p>
                    <small>Thử tìm kiếm với từ khóa khác hoặc kiểm tra chính tả.</small>
                </div>
            `;
            return;
        }

        let resultsHTML = `
            <div class="search-summary">
                <p>Tìm thấy <strong>${results.length}</strong> kết quả cho "<strong>${query}</strong>"</p>
            </div>
        `;
        
        results.forEach((result) => {
            const highlightedTitle = this.highlightQuery(result.title, query);
            const highlightedContent = this.highlightQuery(
                result.content.substring(0, 200) + (result.content.length > 200 ? '...' : ''), 
                query
            );
            const typeIcon = this.getTypeIcon(result.type);
            
            resultsHTML += `
                <div class="search-result-item" onclick="app.navigateToResult('${result.sectionId}', '${result.title.replace(/'/g, "\\'")}')">
                    <div class="search-result-title">${typeIcon} ${highlightedTitle}</div>
                    <div class="search-result-snippet">${highlightedContent}</div>
                    <div class="search-result-meta">
                        <small>${result.section} • ${result.type}</small>
                    </div>
                </div>
            `;
        });

        resultsContainer.innerHTML = resultsHTML;
    }

    getTypeIcon(type) {
        const icons = {
            'section': '📂',
            'topic': '📝',
            'code': '💻'
        };
        return icons[type] || '📄';
    }

    highlightQuery(text, query) {
        const terms = query.split(/\s+/).filter(term => term.length > 1);
        let highlightedText = text;
        
        terms.forEach(term => {
            const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    navigateToResult(sectionId, targetTitle) {
        console.log('Navigating to search result:', sectionId, targetTitle);
        
        this.closeModal('searchModal');
        this.showSection(sectionId);
        
        setTimeout(() => {
            const targetElement = this.findTopicByTitle(targetTitle);
            
            if (targetElement) {
                // Expand parent category if needed
                const category = targetElement.closest('.topic-category');
                if (category) {
                    const categoryTitle = category.querySelector('.category-title');
                    if (categoryTitle && !categoryTitle.classList.contains('expanded')) {
                        this.expandCategory(categoryTitle);
                    }
                }
                
                // Expand topic content if it's a topic
                if (targetElement.classList.contains('topic-card')) {
                    const content = targetElement.querySelector('.topic-content');
                    if (content && !content.classList.contains('expanded')) {
                        content.classList.add('expanded');
                    }
                }
                
                // Scroll to and highlight element
                targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                targetElement.classList.add('highlight');
                
                setTimeout(() => {
                    targetElement.classList.remove('highlight');
                }, 3000);
            }
        }, 300);
    }

    copyCode(button) {
        const codeExample = button.closest('.code-example');
        const codeElement = codeExample?.querySelector('code');
        
        if (codeElement) {
            const code = codeElement.textContent;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(code).then(() => {
                    this.showCopySuccess(button);
                }).catch(() => {
                    this.fallbackCopyText(code, button);
                });
            } else {
                this.fallbackCopyText(code, button);
            }
        }
    }

    fallbackCopyText(text, button) {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        
        try {
            document.execCommand('copy');
            this.showCopySuccess(button);
        } catch (err) {
            console.error('Copy failed:', err);
            this.showNotification('Không thể copy code', 'error');
        }
        
        document.body.removeChild(textarea);
    }

    showCopySuccess(button) {
        const originalText = button.textContent;
        button.textContent = '✅ Copied!';
        button.classList.add('copied');
        
        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('copied');
        }, 2000);
        
        this.showNotification('📋 Code đã được copy!', 'success');
    }

    openModal(modalId) {
        console.log('Opening modal:', modalId);
        
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            setTimeout(() => modal.classList.add('visible'), 10);
            
            // Focus management
            const firstFocusable = modal.querySelector('button, input, select, textarea');
            if (firstFocusable) {
                firstFocusable.focus();
            }
        }
    }

    closeModal(modalId) {
        console.log('Closing modal:', modalId);
        
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('visible');
            setTimeout(() => modal.classList.add('hidden'), 300);
        }
    }

    toggleQuickReference() {
        const content = document.querySelector('.reference-content');
        const toggleBtn = document.getElementById('toggleReference');
        
        if (content && toggleBtn) {
            const isExpanded = content.classList.contains('expanded');
            if (isExpanded) {
                content.classList.remove('expanded');
                toggleBtn.textContent = '▼';
            } else {
                content.classList.add('expanded');
                toggleBtn.textContent = '▲';
            }
        }
    }

    exportToPDF() {
        this.showNotification('Xuất PDF...', 'info');
        globalThis.print();
        this.closeModal('exportModal');
    }

    exportToMarkdown() {
        const activeSection = document.querySelector('.section.active');
        if (!activeSection) return;

        const title = activeSection.querySelector('.section__header h2')?.textContent || 'Section';
        const content = this.convertToMarkdown(activeSection);
        
        const markdown = `# ${title}\n\n${content}`;
        
        this.downloadFile(`${title.replace(/[^a-zA-Z0-9]/g, '_')}.md`, markdown, 'text/markdown');
        this.closeModal('exportModal');
    }

    exportBookmarksToFile() {
        if (this.bookmarks.size === 0) {
            this.showNotification('Không có bookmarks để xuất', 'warning');
            return;
        }

        const bookmarksArray = Array.from(this.bookmarks.values());
        let content = '# Mobile Engineering Handbook - Bookmarks\n\n';
        
        bookmarksArray.forEach(bookmark => {
            content += `## ${bookmark.title}\n`;
            content += `**Section:** ${bookmark.section}\n`;
            content += `**Date:** ${new Date(bookmark.timestamp).toLocaleDateString()}\n\n`;
        });

        this.downloadFile('mobile_handbook_bookmarks.md', content, 'text/markdown');
        this.closeModal('exportModal');
    }

    convertToMarkdown(section) {
        const clone = section.cloneNode(true);
        const toRemove = clone.querySelectorAll('.topic-actions, .bookmark-btn, .copy-btn');
        toRemove.forEach(el => el.remove());
        
        return clone.textContent?.replace(/\s+/g, ' ').trim() || '';
    }

    downloadFile(filename, content, mimeType = 'text/plain') {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification(`Đã xuất: ${filename}`, 'success');
    }

    showProgressDetails() {
        const completedCount = this.checkedItems.size;
        const totalCount = document.querySelectorAll('.topic-checkbox').length;
        const percentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;
        const sectionsVisited = this.userProgress.sectionsVisited.size;
        const bookmarksCount = this.bookmarks.size;
        
        const message = `📊 Thống kê học tập:
• Hoàn thành: ${completedCount}/${totalCount} chủ đề (${percentage}%)
• Sections đã xem: ${sectionsVisited}
• Bookmarks: ${bookmarksCount}
• Lần truy cập cuối: ${new Date(this.userProgress.lastVisit).toLocaleString()}`;
        
        alert(message);
    }

    handleKeyboardShortcuts(e) {
        // ESC key closes modals
        if (e.key === 'Escape') {
            const visibleModal = document.querySelector('.modal.visible');
            if (visibleModal) {
                this.closeModal(visibleModal.id);
                return;
            }
            
            const visiblePanel = document.querySelector('.bookmarks-panel.visible');
            if (visiblePanel) {
                this.closeBookmarksPanel();
                return;
            }

            this.closeFABMenu();
        }

        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Ctrl/Cmd + B for bookmarks
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            this.toggleBookmarksPanel();
        }

        // Ctrl/Cmd + / for theme toggle - removed, using light theme only per project specification
    }

    setupKeyboardNavigation() {
        document.querySelectorAll('.nav-link, .category-title, .topic-header').forEach(el => {
            if (!el.hasAttribute('tabindex')) {
                el.setAttribute('tabindex', '0');
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.target.classList.contains('category-title')) {
                e.preventDefault();
                this.toggleCategory(e.target);
            }
            
            if (e.key === 'Enter' && e.target.classList.contains('topic-header')) {
                e.preventDefault();
                this.toggleTopicContent(e.target);
            }
        });
    }

    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification--${type}`;
        notification.innerHTML = `
            <div class="notification__content">
                <span>${message}</span>
                <button class="notification__close">✕</button>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-lg);
            padding: var(--space-16);
            box-shadow: var(--shadow-lg);
            z-index: 3000;
            max-width: 400px;
            transform: translateX(100%);
            transition: transform 0.3s ease-out;
        `;

        if (type === 'success') {
            notification.style.borderLeftColor = 'var(--color-success)';
            notification.style.borderLeftWidth = '4px';
        } else if (type === 'error') {
            notification.style.borderLeftColor = 'var(--color-error)';
            notification.style.borderLeftWidth = '4px';
        } else if (type === 'warning') {
            notification.style.borderLeftColor = 'var(--color-warning)';
            notification.style.borderLeftWidth = '4px';
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        const closeBtn = notification.querySelector('.notification__close');
        closeBtn.addEventListener('click', () => {
            this.hideNotification(notification);
        });

        setTimeout(() => {
            this.hideNotification(notification);
        }, duration);
    }

    hideNotification(notification) {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    loadUserData() {
        try {
            const savedProgress = localStorage.getItem('handbook-progress');
            if (savedProgress) {
                const data = JSON.parse(savedProgress);
                this.userProgress = {
                    ...this.userProgress,
                    ...data,
                    sectionsVisited: new Set(data.sectionsVisited || []),
                    topicsCompleted: new Set(data.topicsCompleted || [])
                };
                this.checkedItems = new Set(data.topicsCompleted || []);
            }

            const savedBookmarks = localStorage.getItem('handbook-bookmarks');
            if (savedBookmarks) {
                const bookmarksArray = JSON.parse(savedBookmarks);
                this.bookmarks = new Map(bookmarksArray);
            }

            setTimeout(() => {
                this.restoreUIState();
            }, 500);
        } catch (error) {
            console.warn('Failed to load user data:', error);
        }
    }

    saveUserData() {
        try {
            const progressData = {
                ...this.userProgress,
                sectionsVisited: Array.from(this.userProgress.sectionsVisited),
                topicsCompleted: Array.from(this.userProgress.topicsCompleted),
                lastVisit: Date.now()
            };
            
            localStorage.setItem('handbook-progress', JSON.stringify(progressData));
            localStorage.setItem('handbook-bookmarks', JSON.stringify(Array.from(this.bookmarks.entries())));
        } catch (error) {
            console.warn('Failed to save user data:', error);
        }
    }

    restoreUIState() {
        // Restore checked items
        document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
            const topicCard = checkbox.closest('.topic-card');
            const topicHeader = topicCard?.querySelector('.topic-header h4');
            const topicText = topicHeader?.textContent?.trim();
            
            if (topicText && this.checkedItems.has(topicText)) {
                checkbox.checked = true;
                topicCard?.classList.add('completed');
            }
        });

        // Restore bookmarks
        document.querySelectorAll('.bookmark-btn').forEach(btn => {
            const topicCard = btn.closest('.topic-card');
            const topicHeader = topicCard?.querySelector('.topic-header h4');
            const topicText = topicHeader?.textContent?.trim();
            
            if (topicText && this.bookmarks.has(topicText)) {
                btn.classList.add('bookmarked');
                btn.title = 'Bỏ đánh dấu';
            }
        });

        this.updateBookmarksList();
    }
}

// Global function for code copying (referenced in HTML)
globalThis.copyCode = function(button) {
    if (globalThis.app) {
        globalThis.app.copyCode(button);
    }
};

// Initialize the application
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new MobileEngineeringHandbook();
    globalThis.app = app; // Make globally accessible
    
    console.log('🚀 Mobile Engineering Handbook loaded successfully!');
    console.log('💡 Keyboard shortcuts:');
    console.log('  Ctrl/Cmd + K: Search');
    console.log('  Ctrl/Cmd + B: Toggle bookmarks');
    console.log('  Ctrl/Cmd + /: Toggle theme');
    console.log('  ESC: Close modals/panels');
});