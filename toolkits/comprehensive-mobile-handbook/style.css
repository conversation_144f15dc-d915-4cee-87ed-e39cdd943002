:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
    
    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-charcoal-700);
    --color-surface: var(--color-charcoal-800);
    --color-text: var(--color-gray-200);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
    --color-primary: var(--color-teal-300);
    --color-primary-hover: var(--color-teal-400);
    --color-primary-active: var(--color-teal-800);
    --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-teal-300);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-gray-300);
    --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
    --color-btn-primary-text: var(--color-slate-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  /* RGB versions for opacity control (dark mode) */
  --color-gray-400-rgb: 119, 124, 124;
  --color-teal-300-rgb: 50, 184, 198;
  --color-gray-300-rgb: 167, 169, 169;
  --color-gray-200-rgb: 245, 245, 245;

  /* Colorful background palette - Dark Mode */
  --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
  --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
  --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
  --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
  --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
  --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
  --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
  --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
  
  /* Semantic Color Tokens (Dark Mode) */
  --color-background: var(--color-charcoal-700);
  --color-surface: var(--color-charcoal-800);
  --color-text: var(--color-gray-200);
  --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
  --color-primary: var(--color-teal-300);
  --color-primary-hover: var(--color-teal-400);
  --color-primary-active: var(--color-teal-800);
  --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
  --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
  --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
  --color-border: rgba(var(--color-gray-400-rgb), 0.3);
  --color-error: var(--color-red-400);
  --color-success: var(--color-teal-300);
  --color-warning: var(--color-orange-400);
  --color-info: var(--color-gray-300);
  --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
  --color-btn-primary-text: var(--color-slate-900);
  --color-card-border: rgba(var(--color-gray-400-rgb), 0.15);
  --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
  --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: var(--color-teal-300-rgb);
  --color-error-rgb: var(--color-red-400-rgb);
  --color-warning-rgb: var(--color-orange-400-rgb);
  --color-info-rgb: var(--color-gray-300-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  
  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* Enhanced Mobile Engineering Handbook Styles */

/* Layout Structure */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
}

/* Header Styles */
.header {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  padding: var(--space-16) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header__container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-16);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-24);
}

.header__brand {
  flex: 1;
}

.header__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
  line-height: var(--line-height-tight);
}

.header__subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
  font-weight: var(--font-weight-medium);
}

.header__actions {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  flex-wrap: wrap;
}

.search-container {
  display: flex;
  gap: var(--space-8);
  align-items: center;
}

.search-input {
  min-width: 300px;
  font-size: var(--font-size-base);
}

.search-btn {
  padding: var(--space-8) var(--space-16);
  font-size: var(--font-size-lg);
}

.theme-toggle {
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-xl);
  min-width: 48px;
}

/* Main Container */
.main-container {
  display: flex;
  flex: 1;
  max-width: var(--container-xl);
  margin: 0 auto;
  width: 100%;
  gap: var(--space-24);
}

/* Enhanced Sidebar */
.sidebar {
  width: 350px;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  padding: var(--space-24);
  position: sticky;
  top: 100px;
  height: calc(100vh - 100px);
  overflow-y: auto;
  transition: transform var(--duration-normal) var(--ease-standard);
  box-shadow: var(--shadow-sm);
}

.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: var(--color-secondary);
  border-radius: var(--radius-full);
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: var(--radius-full);
}

.sidebar__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-24);
  padding-bottom: var(--space-16);
  border-bottom: 1px solid var(--color-border);
}

.sidebar__header h2 {
  font-size: var(--font-size-2xl);
  color: var(--color-text);
  margin: 0;
  font-weight: var(--font-weight-bold);
}

.sidebar-toggle {
  display: none;
  padding: var(--space-4) var(--space-8);
}

/* Navigation Sections */
.nav-section {
  margin-bottom: var(--space-24);
}

.nav-section-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-12) 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: var(--space-6);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  padding: var(--space-12) var(--space-16);
  border-radius: var(--radius-base);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: all var(--duration-fast) var(--ease-standard);
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: var(--color-primary);
  transform: scaleY(0);
  transition: transform var(--duration-fast) var(--ease-standard);
  transform-origin: bottom;
}

.nav-link:hover {
  background: var(--color-secondary);
  color: var(--color-text);
  transform: translateX(4px);
}

.nav-link:hover::before {
  transform: scaleY(1);
}

.nav-link.active {
  background: var(--color-bg-1);
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-sm);
}

.nav-link.active::before {
  transform: scaleY(1);
}

.nav-icon {
  font-size: var(--font-size-xl);
  width: 24px;
  text-align: center;
}

/* Enhanced Progress Summary */
.progress-summary {
  background: linear-gradient(135deg, var(--color-bg-1), var(--color-bg-3));
  padding: var(--space-20);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.progress-summary h3 {
  font-size: var(--font-size-lg);
  margin: 0 0 var(--space-16) 0;
  color: var(--color-text);
  text-align: center;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-16);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.progress-bar {
  background: var(--color-secondary);
  height: 12px;
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-12);
  position: relative;
}

.progress-fill {
  background: linear-gradient(90deg, var(--color-primary), var(--color-teal-400));
  height: 100%;
  width: 0%;
  transition: width var(--duration-normal) var(--ease-standard);
  border-radius: var(--radius-full);
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

#progressText {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  text-align: center;
  display: block;
  font-weight: var(--font-weight-medium);
}

/* Main Content Area */
.content {
  flex: 1;
  padding: var(--space-32);
  max-width: calc(100% - 350px - var(--space-24));
  min-height: calc(100vh - 100px);
}

.section {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.section.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Section Header */
.section__header {
  margin-bottom: var(--space-32);
  padding: var(--space-32);
  background: linear-gradient(135deg, var(--color-bg-1), var(--color-bg-2));
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  position: relative;
  overflow: hidden;
}

.section__header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-teal-400), var(--color-primary));
}

.section__header h2 {
  font-size: var(--font-size-4xl);
  margin: 0 0 var(--space-12) 0;
  color: var(--color-text);
  font-weight: var(--font-weight-bold);
}

.section__description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-20) 0;
  line-height: var(--line-height-normal);
}

.section__meta {
  display: flex;
  gap: var(--space-12);
  flex-wrap: wrap;
}

.meta-tag {
  background: var(--color-surface);
  color: var(--color-text-secondary);
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--color-border);
}

/* Topic Categories */
.topic-category {
  margin-bottom: var(--space-32);
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.category-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-20);
  margin: 0;
  background: var(--color-bg-3);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  border-bottom: 1px solid var(--color-border);
}

.category-title:hover {
  background: var(--color-bg-4);
}

.expand-icon {
  font-size: var(--font-size-base);
  transition: transform var(--duration-fast) var(--ease-standard);
  color: var(--color-text-secondary);
}

.category-title.expanded .expand-icon {
  transform: rotate(180deg);
}

.category-content {
  padding: 0;
  display: none;
  background: var(--color-surface);
}

.category-content.expanded {
  display: block;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 1000px;
    transform: translateY(0);
  }
}

/* Enhanced Topic Cards */
.topic-card {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-card-border-inner);
  transition: all var(--duration-fast) var(--ease-standard);
}

.topic-card:last-child {
  border-bottom: none;
}

.topic-card:hover {
  background: var(--color-bg-1);
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-20);
  cursor: pointer;
  border-bottom: 1px solid var(--color-card-border-inner);
}

.topic-header h4 {
  font-size: var(--font-size-lg);
  margin: 0;
  color: var(--color-text);
  font-weight: var(--font-weight-semibold);
}

.topic-actions {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

/* Custom Checkbox */
.topic-checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.topic-checkbox {
  display: none;
}

.custom-checkbox {
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-border);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-surface);
  transition: all var(--duration-fast) var(--ease-standard);
  position: relative;
}

.topic-checkbox:checked + .custom-checkbox {
  background: var(--color-primary);
  border-color: var(--color-primary);
  transform: scale(1.1);
}

.topic-checkbox:checked + .custom-checkbox::after {
  content: '✓';
  color: var(--color-btn-primary-text);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  animation: checkmark 0.2s ease-in-out;
}

@keyframes checkmark {
  0% { transform: scale(0) rotate(45deg); opacity: 0; }
  50% { transform: scale(1.2) rotate(45deg); opacity: 1; }
  100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.bookmark-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-xl);
  padding: var(--space-6);
  border-radius: var(--radius-sm);
  transition: all var(--duration-fast) var(--ease-standard);
  opacity: 0.6;
}

.bookmark-btn:hover {
  background: var(--color-secondary);
  opacity: 1;
  transform: scale(1.1);
}

.bookmark-btn.bookmarked {
  opacity: 1;
  color: var(--color-warning);
  animation: bookmark 0.3s ease-in-out;
}

@keyframes bookmark {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

.topic-content {
  padding: var(--space-24);
  display: none;
  line-height: var(--line-height-normal);
}

.topic-content.expanded {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

.topic-content p {
  margin: 0 0 var(--space-16) 0;
  color: var(--color-text);
}

.topic-content strong {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

/* Enhanced Code Examples */
.code-example {
  background: var(--color-bg-8);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin: var(--space-20) 0;
  box-shadow: var(--shadow-sm);
}

.code-header {
  background: var(--color-bg-4);
  padding: var(--space-12) var(--space-16);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code-header h5 {
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--color-text);
  font-weight: var(--font-weight-semibold);
}

.copy-btn {
  background: var(--color-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.copy-btn:hover {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  transform: translateY(-1px);
}

.copy-btn.copied {
  background: var(--color-success);
  color: var(--color-btn-primary-text);
}

.code-example pre {
  margin: 0;
  background: transparent;
  border: none;
  padding: var(--space-20);
  overflow-x: auto;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: 1.6;
}

.code-example code {
  background: transparent;
  padding: 0;
  color: var(--color-text);
  font-family: var(--font-family-mono);
}

/* Info Boxes */
.info-box {
  padding: var(--space-16);
  border-radius: var(--radius-base);
  border-left: 4px solid;
  margin: var(--space-20) 0;
}

.info-box.info {
  background: rgba(var(--color-info-rgb), 0.1);
  border-left-color: var(--color-info);
}

.info-box.success {
  background: rgba(var(--color-success-rgb), 0.1);
  border-left-color: var(--color-success);
}

.info-box.warning {
  background: rgba(var(--color-warning-rgb), 0.1);
  border-left-color: var(--color-warning);
}

.info-box.error {
  background: rgba(var(--color-error-rgb), 0.1);
  border-left-color: var(--color-error);
}

.info-box h5 {
  margin: 0 0 var(--space-8) 0;
  color: var(--color-text);
  font-weight: var(--font-weight-semibold);
}

.info-box ul {
  margin: 0;
  padding-left: var(--space-16);
}

.info-box li {
  margin-bottom: var(--space-4);
  color: var(--color-text);
}

/* Best Practices */
.best-practices {
  background: var(--color-bg-3);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  margin: var(--space-20) 0;
}

.best-practices h5 {
  margin: 0 0 var(--space-12) 0;
  color: var(--color-success);
  font-weight: var(--font-weight-semibold);
}

.best-practices ul {
  margin: 0;
  padding-left: var(--space-16);
}

.best-practices li {
  margin-bottom: var(--space-8);
  color: var(--color-text);
}

/* Comparison Table */
.comparison-table {
  margin: var(--space-20) 0;
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-surface);
  border-radius: var(--radius-base);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: var(--space-12);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
}

.table th {
  background: var(--color-bg-2);
  color: var(--color-text);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table td {
  color: var(--color-text);
  vertical-align: top;
}

.table tr:hover {
  background: var(--color-bg-1);
}

/* Decision Flowchart */
.decision-flowchart {
  margin: var(--space-20) 0;
}

.flowchart {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.flow-step {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--space-16);
}

.flow-question {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.flow-options {
  display: flex;
  gap: var(--space-16);
}

.flow-yes,
.flow-no {
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.flow-yes {
  background: var(--color-bg-3);
  color: var(--color-success);
  border: 1px solid rgba(var(--color-success-rgb), 0.3);
}

.flow-no {
  background: var(--color-bg-4);
  color: var(--color-error);
  border: 1px solid rgba(var(--color-error-rgb), 0.3);
}

/* Quick Reference Panel */
.quick-reference {
  position: fixed;
  bottom: var(--space-20);
  right: var(--space-20);
  width: 320px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: 50;
  transition: transform var(--duration-normal) var(--ease-standard);
}

.reference-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16);
  background: var(--color-bg-1);
  border-bottom: 1px solid var(--color-border);
  cursor: pointer;
}

.reference-header h3 {
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--color-text);
}

.reference-content {
  padding: var(--space-16);
  max-height: 300px;
  overflow-y: auto;
  display: none;
}

.reference-content.expanded {
  display: block;
}

.reference-section {
  margin-bottom: var(--space-16);
}

.reference-section:last-child {
  margin-bottom: 0;
}

.reference-section h4 {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6) 0;
  border-bottom: 1px solid var(--color-card-border-inner);
}

.reference-item:last-child {
  border-bottom: none;
}

.reference-item code {
  background: var(--color-secondary);
  color: var(--color-primary);
  padding: var(--space-2) var(--space-6);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
}

.reference-item span {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

/* Bookmarks Panel */
.bookmarks-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: var(--color-surface);
  border-left: 1px solid var(--color-border);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform var(--duration-normal) var(--ease-standard);
  padding: var(--space-24);
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

.bookmarks-panel.visible {
  transform: translateX(0);
}

.bookmarks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-20);
  padding-bottom: var(--space-16);
  border-bottom: 1px solid var(--color-border);
}

.bookmarks-header h3 {
  margin: 0;
  color: var(--color-text);
  font-size: var(--font-size-xl);
}

.bookmarks-actions {
  display: flex;
  gap: var(--space-8);
}

.bookmarks-filter {
  margin-bottom: var(--space-16);
}

.bookmarks-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.bookmark-item {
  background: var(--color-bg-1);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--space-12);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.bookmark-item:hover {
  background: var(--color-bg-2);
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

.bookmark-title {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.bookmark-section {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.empty-bookmarks {
  text-align: center;
  color: var(--color-text-secondary);
  padding: var(--space-32);
}

.empty-bookmarks p {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--font-size-lg);
}

.empty-bookmarks small {
  font-size: var(--font-size-sm);
  opacity: 0.7;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.modal.visible {
  opacity: 1;
  visibility: visible;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: var(--shadow-lg);
  animation: modalSlide 0.3s ease-out;
}

@keyframes modalSlide {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-20);
  border-bottom: 1px solid var(--color-border);
  background: var(--color-bg-1);
}

.modal-header h3 {
  margin: 0;
  color: var(--color-text);
  font-size: var(--font-size-xl);
}

/* Search Modal */
.search-filters {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-border);
  background: var(--color-bg-2);
}

.search-results {
  padding: var(--space-20);
  overflow-y: auto;
  flex: 1;
  max-height: 50vh;
}

.search-placeholder {
  text-align: center;
  color: var(--color-text-secondary);
  padding: var(--space-32);
}

.search-placeholder p {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--font-size-lg);
}

.search-placeholder small {
  font-size: var(--font-size-sm);
  opacity: 0.7;
}

.search-result-item {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-border);
  cursor: pointer;
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.search-result-item:hover {
  background: var(--color-bg-1);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-title {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.search-result-snippet {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/* Export Modal */
.export-options {
  padding: var(--space-24);
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.export-btn {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: var(--space-16);
  text-align: left;
  min-height: 60px;
  justify-content: center;
}

.export-btn small {
  margin-top: var(--space-4);
  opacity: 0.7;
  font-size: var(--font-size-xs);
}

/* Code Playground */
.playground-modal .modal-content {
  max-width: 1200px;
  max-height: 90vh;
}

.playground-actions {
  display: flex;
  gap: var(--space-8);
}

.playground-content {
  display: flex;
  flex: 1;
  min-height: 500px;
}

.playground-editor,
.playground-output {
  flex: 1;
  padding: var(--space-16);
}

.playground-editor {
  border-right: 1px solid var(--color-border);
}

.code-editor {
  width: 100%;
  height: 400px;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--space-12);
  background: var(--color-bg-8);
  color: var(--color-text);
  resize: none;
}

.output-area {
  background: var(--color-bg-8);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--space-12);
  height: 400px;
  overflow-y: auto;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  color: var(--color-text);
  white-space: pre-wrap;
}

/* Floating Action Button */
.fab-container {
  position: fixed;
  bottom: var(--space-24);
  right: var(--space-24);
  z-index: 100;
}

.fab {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--duration-fast) var(--ease-standard);
  box-shadow: var(--shadow-lg);
}

.fab-main {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.fab-main:hover {
  background: var(--color-primary-hover);
  transform: scale(1.1);
}

.fab-menu {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  margin-bottom: var(--space-12);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all var(--duration-normal) var(--ease-standard);
}

.fab-menu.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.fab-mini {
  width: 40px;
  height: 40px;
  background: var(--color-surface);
  color: var(--color-text);
  border: 1px solid var(--color-border);
  font-size: var(--font-size-base);
}

.fab-mini:hover {
  background: var(--color-secondary);
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header__container {
    flex-direction: column;
    gap: var(--space-16);
    padding: var(--space-12);
  }

  .header__actions {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .search-input {
    min-width: 250px;
  }

  .main-container {
    flex-direction: column;
    gap: 0;
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: 999;
    transform: translateX(-100%);
    background: var(--color-surface);
  }

  .sidebar.visible {
    transform: translateX(0);
  }

  .sidebar-toggle {
    display: block;
  }

  .content {
    max-width: 100%;
    padding: var(--space-16);
  }

  .section__header {
    padding: var(--space-20);
  }

  .section__header h2 {
    font-size: var(--font-size-3xl);
  }

  .topic-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-12);
  }

  .topic-actions {
    width: 100%;
    justify-content: space-between;
  }

  .bookmarks-panel {
    width: 100%;
  }

  .quick-reference {
    position: relative;
    bottom: auto;
    right: auto;
    width: 100%;
    margin: var(--space-16) 0;
  }

  .comparison-table {
    font-size: var(--font-size-sm);
  }

  .table th,
  .table td {
    padding: var(--space-8);
  }

  .playground-content {
    flex-direction: column;
  }

  .playground-editor {
    border-right: none;
    border-bottom: 1px solid var(--color-border);
  }

  .fab-container {
    bottom: var(--space-16);
    right: var(--space-16);
  }
}

@media (max-width: 480px) {
  .header__title {
    font-size: var(--font-size-2xl);
    text-align: center;
  }

  .search-container {
    flex-direction: column;
    width: 100%;
    gap: var(--space-8);
  }

  .search-input {
    min-width: unset;
    width: 100%;
  }

  .content {
    padding: var(--space-12);
  }

  .section__header {
    padding: var(--space-16);
  }

  .topic-card {
    font-size: var(--font-size-sm);
  }

  .modal-content {
    width: 95%;
    margin: var(--space-8);
  }

  .playground-modal .modal-content {
    max-height: 95vh;
  }

  .code-editor,
  .output-area {
    height: 200px;
  }
}

/* Print Styles */
@media print {
  .header,
  .sidebar,
  .bookmarks-panel,
  .modal,
  .fab-container,
  .quick-reference {
    display: none !important;
  }

  .main-container {
    display: block;
  }

  .content {
    max-width: 100%;
    padding: 0;
  }

  .section {
    display: block !important;
    page-break-inside: avoid;
  }

  .topic-category {
    page-break-inside: avoid;
    margin-bottom: var(--space-16);
  }

  .category-content {
    display: block !important;
  }

  .topic-content {
    display: block !important;
  }

  .code-example {
    page-break-inside: avoid;
  }

  .topic-actions,
  .copy-btn {
    display: none !important;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
.nav-link:focus-visible,
.category-title:focus-visible,
.bookmark-btn:focus-visible,
.topic-header:focus-visible,
.copy-btn:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading and Animation States */
.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Highlight Animation */
.highlight {
  background: rgba(var(--color-warning-rgb), 0.3) !important;
  animation: highlightPulse 3s ease-in-out;
  border-radius: var(--radius-base);
  padding: var(--space-8);
}

@keyframes highlightPulse {
  0%, 100% { background: rgba(var(--color-warning-rgb), 0.3) !important; }
  50% { background: rgba(var(--color-warning-rgb), 0.6) !important; }
}

/* Search Highlighting */
mark {
  background: rgba(var(--color-warning-rgb), 0.4);
  color: var(--color-text);
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: var(--font-weight-semibold);
}

/* Completed Topics */
.topic-card.completed {
  opacity: 0.7;
  background: var(--color-bg-3) !important;
}

.topic-card.completed .topic-header h4 {
  text-decoration: line-through;
  color: var(--color-text-secondary);
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
  .code-example pre {
    background: var(--color-charcoal-800);
  }
  
  .code-editor,
  .output-area {
    background: var(--color-charcoal-800);
  }
}

[data-color-scheme="dark"] .code-example pre,
[data-color-scheme="dark"] .code-editor,
[data-color-scheme="dark"] .output-area {
  background: var(--color-charcoal-800);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.p-0 {
  padding: 0 !important;
}