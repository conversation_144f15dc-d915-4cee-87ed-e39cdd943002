<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cẩm <PERSON>g Mobile Engineering Toàn <PERSON> - <PERSON>lutter, Dart & React Native</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="header__container">
                <div class="header__brand">
                    <h1 class="header__title">📱 Cẩm Nang Mobile Engineering Toàn Diện</h1>
                    <p class="header__subtitle">Flutter • Dart • React Native • Architecture • Testing • DevOps</p>
                </div>
                <div class="header__actions">
                    <div class="search-container">
                        <input type="text" id="searchInput" placeholder="Tìm kiếm nội dung..." class="form-control search-input">
                        <button id="searchBtn" class="btn btn--primary search-btn">🔍</button>
                    </div>
                    <button id="bookmarkToggle" class="btn btn--secondary">📚 Bookmarks</button>

                    <button id="exportBtn" class="btn btn--outline">📤 Xuất</button>
                </div>
            </div>
        </header>

        <div class="main-container">
            <!-- Sidebar Navigation -->
            <nav class="sidebar" id="sidebar">
                <div class="sidebar__header">
                    <h2>Danh Mục</h2>
                    <button id="sidebarToggle" class="btn btn--outline sidebar-toggle">☰</button>
                </div>
                
                <div class="nav-section">
                    <h3 class="nav-section-title">🎯 Kiến Thức Cơ Bản</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#foundations" class="nav-link active" data-section="foundations">
                                <span class="nav-icon">🏗️</span>
                                Nền Tảng Mobile Development
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#environment" class="nav-link" data-section="environment">
                                <span class="nav-icon">⚙️</span>
                                Môi Trường Phát Triển
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <h3 class="nav-section-title">🔧 Dart Programming</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#dart-basics" class="nav-link" data-section="dart-basics">
                                <span class="nav-icon">📝</span>
                                Dart Cơ Bản
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#dart-oop" class="nav-link" data-section="dart-oop">
                                <span class="nav-icon">🏗️</span>
                                Lập Trình Hướng Đối Tượng
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#dart-advanced" class="nav-link" data-section="dart-advanced">
                                <span class="nav-icon">⚡</span>
                                Dart Nâng Cao
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <h3 class="nav-section-title">📱 Flutter Development</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#flutter-basics" class="nav-link" data-section="flutter-basics">
                                <span class="nav-icon">🎨</span>
                                Flutter Cơ Bản
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#flutter-ui" class="nav-link" data-section="flutter-ui">
                                <span class="nav-icon">🎭</span>
                                Phát Triển UI
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#flutter-state" class="nav-link" data-section="flutter-state">
                                <span class="nav-icon">🔄</span>
                                State Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#flutter-navigation" class="nav-link" data-section="flutter-navigation">
                                <span class="nav-icon">🧭</span>
                                Navigation & Routing
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#flutter-backend" class="nav-link" data-section="flutter-backend">
                                <span class="nav-icon">🌐</span>
                                Backend Integration
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <h3 class="nav-section-title">⚛️ React Native</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#rn-core" class="nav-link" data-section="rn-core">
                                <span class="nav-icon">⚛️</span>
                                React Native Core
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#rn-ui" class="nav-link" data-section="rn-ui">
                                <span class="nav-icon">🎨</span>
                                UI Development
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#rn-native" class="nav-link" data-section="rn-native">
                                <span class="nav-icon">🔗</span>
                                Native Modules
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <h3 class="nav-section-title">🏛️ Architecture & Testing</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#architecture" class="nav-link" data-section="architecture">
                                <span class="nav-icon">🏗️</span>
                                Kiến Trúc Ứng Dụng
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#testing" class="nav-link" data-section="testing">
                                <span class="nav-icon">🧪</span>
                                Testing & QA
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#deployment" class="nav-link" data-section="deployment">
                                <span class="nav-icon">🚀</span>
                                Deployment & DevOps
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="progress-summary">
                    <h3>📊 Tiến Độ Học Tập</h3>
                    <div class="progress-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="completedTopics">0</span>
                            <span class="stat-label">Hoàn thành</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="totalTopics">0</span>
                            <span class="stat-label">Tổng cộng</span>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <span id="progressText">0% hoàn thành</span>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="content" id="content">
                <!-- Foundations Section -->
                <section id="foundations-section" class="section active">
                    <div class="section__header">
                        <h2>🏗️ Nền Tảng Mobile Development</h2>
                        <p class="section__description">Những kiến thức cơ bản và nguyên lý cốt lõi mà mọi Mobile Engineer cần nắm vững</p>
                        <div class="section__meta">
                            <span class="meta-tag">⏱️ Thời gian: 20 giờ</span>
                            <span class="meta-tag">📚 45 chủ đề</span>
                            <span class="meta-tag">🔥 Quan trọng</span>
                        </div>
                    </div>

                    <div class="topic-category">
                        <h3 class="category-title expandable" data-expanded="true">
                            <span>💡 Nguyên Lý Lập Trình Cơ Bản</span>
                            <span class="expand-icon">▼</span>
                        </h3>
                        <div class="category-content expanded">
                            <div class="topic-card">
                                <div class="topic-header expandable">
                                    <h4>🎯 Lập Trình Hướng Đối Tượng (OOP)</h4>
                                    <div class="topic-actions">
                                        <button class="bookmark-btn" title="Đánh dấu">🔖</button>
                                        <label class="topic-checkbox-label">
                                            <input type="checkbox" class="topic-checkbox">
                                            <span class="custom-checkbox"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="topic-content expanded">
                                    <p><strong>Encapsulation (Đóng gói):</strong> Che giấu chi tiết implementation và chỉ expose interface cần thiết. Trong mobile development, điều này giúp tạo ra các components độc lập, dễ maintain và test.</p>
                                    
                                    <div class="code-example">
                                        <div class="code-header">
                                            <h5>💡 Ví dụ: Encapsulation trong Dart</h5>
                                            <button class="copy-btn" onclick="copyCode(this)">📋 Copy</button>
                                        </div>
                                        <pre><code class="language-dart">class UserAccount {
  String _username;           // Private field
  String _password;           // Private field
  DateTime _lastLogin;        // Private field
  
  UserAccount(this._username, this._password);
  
  // Public getter with validation
  String get username => _username;
  
  // Public method with business logic
  bool authenticate(String password) {
    if (_password == password) {
      _lastLogin = DateTime.now();
      return true;
    }
    return false;
  }
  
  // Read-only property
  String get lastLoginFormatted {
    return _lastLogin?.toString() ?? 'Never logged in';
  }
  
  // Controlled modification
  void changePassword(String oldPassword, String newPassword) {
    if (authenticate(oldPassword)) {
      _password = newPassword;
      print('Password changed successfully');
    } else {
      throw Exception('Invalid old password');
    }
  }
}</code></pre>
                                    </div>

                                    <p><strong>Inheritance (Kế thừa):</strong> Tái sử dụng code và tạo hierarchy. Trong Flutter, mọi widget đều inherit từ Widget class.</p>

                                    <div class="code-example">
                                        <div class="code-header">
                                            <h5>💡 Ví dụ: Inheritance cho UI Components</h5>
                                            <button class="copy-btn" onclick="copyCode(this)">📋 Copy</button>
                                        </div>
                                        <pre><code class="language-dart">// Base widget class
abstract class BaseButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool enabled;
  
  const BaseButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.enabled = true,
  }) : super(key: key);
  
  // Template method pattern
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: enabled ? onPressed : null,
      child: Container(
        padding: getPadding(),
        decoration: getDecoration(context),
        child: Text(
          text,
          style: getTextStyle(context),
        ),
      ),
    );
  }
  
  // Abstract methods to be implemented by subclasses
  EdgeInsetsGeometry getPadding();
  BoxDecoration getDecoration(BuildContext context);
  TextStyle getTextStyle(BuildContext context);
}</code></pre>
                                    </div>

                                    <div class="best-practices">
                                        <h5>✅ Best Practices</h5>
                                        <ul>
                                            <li>Sử dụng composition thay vì inheritance khi có thể</li>
                                            <li>Implement interfaces để đảm bảo contract</li>
                                            <li>Áp dụng SOLID principles trong thiết kế class</li>
                                            <li>Sử dụng dependency injection để giảm coupling</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="topic-card">
                                <div class="topic-header expandable">
                                    <h4>📐 Design Patterns Cơ Bản</h4>
                                    <div class="topic-actions">
                                        <button class="bookmark-btn" title="Đánh dấu">🔖</button>
                                        <label class="topic-checkbox-label">
                                            <input type="checkbox" class="topic-checkbox">
                                            <span class="custom-checkbox"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="topic-content">
                                    <p><strong>Singleton Pattern:</strong> Đảm bảo chỉ có một instance duy nhất của class. Thường dùng cho services, configurations.</p>
                                    
                                    <div class="code-example">
                                        <div class="code-header">
                                            <h5>💡 Ví dụ: Singleton cho API Service</h5>
                                            <button class="copy-btn" onclick="copyCode(this)">📋 Copy</button>
                                        </div>
                                        <pre><code class="language-dart">class ApiService {
  static ApiService? _instance;
  static const String _baseUrl = 'https://api.example.com';
  late Dio _dio;
  
  // Private constructor
  ApiService._internal() {
    _dio = Dio(BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: 5000,
      receiveTimeout: 3000,
    ));
  }
  
  // Factory constructor returns the same instance
  factory ApiService() {
    _instance ??= ApiService._internal();
    return _instance!;
  }
  
  // API methods
  Future<Response> get(String endpoint) async {
    try {
      return await _dio.get(endpoint);
    } catch (e) {
      throw Exception('API Error: $e');
    }
  }
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="topic-category">
                        <h3 class="category-title expandable" data-expanded="false">
                            <span>🌐 Mobile Development Concepts</span>
                            <span class="expand-icon">▼</span>
                        </h3>
                        <div class="category-content">
                            <div class="topic-card">
                                <div class="topic-header expandable">
                                    <h4>📱 Native vs Cross-platform vs Hybrid</h4>
                                    <div class="topic-actions">
                                        <button class="bookmark-btn" title="Đánh dấu">🔖</button>
                                        <label class="topic-checkbox-label">
                                            <input type="checkbox" class="topic-checkbox">
                                            <span class="custom-checkbox"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="topic-content">
                                    <div class="comparison-table">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Phương Pháp</th>
                                                    <th>Ưu Điểm</th>
                                                    <th>Nhược Điểm</th>
                                                    <th>Khi Nào Dùng</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><strong>Native</strong><br><small>Swift/Kotlin</small></td>
                                                    <td>
                                                        • Performance tối ưu<br>
                                                        • Truy cập đầy đủ platform APIs<br>
                                                        • UI/UX native<br>
                                                        • Debugging tools tốt nhất
                                                    </td>
                                                    <td>
                                                        • Chi phí phát triển cao<br>
                                                        • Cần 2 team riêng biệt<br>
                                                        • Time-to-market chậm<br>
                                                        • Maintain 2 codebases
                                                    </td>
                                                    <td>
                                                        • Apps performance-critical<br>
                                                        • Heavy platform integration<br>
                                                        • Có budget và time lớn<br>
                                                        • Team chuyên môn cao
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Cross-platform</strong><br><small>Flutter/React Native</small></td>
                                                    <td>
                                                        • Single codebase<br>
                                                        • Fast development<br>
                                                        • Lower cost<br>
                                                        • Code reuse cao<br>
                                                        • Active communities
                                                    </td>
                                                    <td>
                                                        • Performance overhead nhỏ<br>
                                                        • Limited native features<br>
                                                        • Platform-specific issues<br>
                                                        • Framework dependency
                                                    </td>
                                                    <td>
                                                        • MVP/early stage products<br>
                                                        • Limited budget/timeline<br>
                                                        • Standard app requirements<br>
                                                        • Small team
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Dart Basics Section -->
                <section id="dart-basics-section" class="section">
                    <div class="section__header">
                        <h2>📝 Dart Programming Language - Cơ Bản</h2>
                        <p class="section__description">Nắm vững ngôn ngữ Dart từ cú pháp cơ bản đến các khái niệm nâng cao</p>
                        <div class="section__meta">
                            <span class="meta-tag">⏱️ Thời gian: 15 giờ</span>
                            <span class="meta-tag">📚 25 chủ đề</span>
                            <span class="meta-tag">🔥 Cốt lõi</span>
                        </div>
                    </div>

                    <div class="topic-category">
                        <h3 class="category-title expandable" data-expanded="true">
                            <span>🔤 Variables & Data Types</span>
                            <span class="expand-icon">▼</span>
                        </h3>
                        <div class="category-content expanded">
                            <div class="topic-card">
                                <div class="topic-header expandable">
                                    <h4>📊 Variables & Constants</h4>
                                    <div class="topic-actions">
                                        <button class="bookmark-btn" title="Đánh dấu">🔖</button>
                                        <label class="topic-checkbox-label">
                                            <input type="checkbox" class="topic-checkbox">
                                            <span class="custom-checkbox"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="topic-content expanded">
                                    <p>Dart cung cấp nhiều cách để khai báo variables với type safety và null safety.</p>

                                    <div class="code-example">
                                        <div class="code-header">
                                            <h5>💡 Variable Declaration Patterns</h5>
                                            <button class="copy-btn" onclick="copyCode(this)">📋 Copy</button>
                                        </div>
                                        <pre><code class="language-dart">void main() {
  // ============ EXPLICIT TYPE DECLARATION ============
  int age = 25;                    // Integer
  double height = 5.9;             // Double precision floating point
  String name = 'John Doe';        // String
  bool isActive = true;            // Boolean
  
  // ============ TYPE INFERENCE ============
  var userName = 'developer';      // String (inferred)
  var count = 10;                  // int (inferred)
  var price = 19.99;              // double (inferred)
  var isValid = false;            // bool (inferred)
  
  // ============ CONSTANTS ============
  // Compile-time constant
  const String appName = 'Mobile Engineering Handbook';
  const int maxRetries = 3;
  
  // Runtime constant
  final DateTime now = DateTime.now();
  final String sessionId = generateSessionId();
  
  // ============ LATE INITIALIZATION ============
  late String configValue;
  configValue = loadConfiguration();
  
  print('Config: $configValue');
}

String generateSessionId() {
  return 'session_${DateTime.now().millisecondsSinceEpoch}';
}

String loadConfiguration() {
  return 'production_config';
}</code></pre>
                                    </div>

                                    <div class="info-box info">
                                        <h5>💡 Key Concepts</h5>
                                        <ul>
                                            <li><strong>var:</strong> Type inference, type không thể thay đổi sau khi assign</li>
                                            <li><strong>dynamic:</strong> Type có thể thay đổi runtime, mất type safety</li>
                                            <li><strong>const:</strong> Compile-time constant, immutable</li>
                                            <li><strong>final:</strong> Runtime constant, chỉ assign một lần</li>
                                            <li><strong>late:</strong> Non-nullable nhưng initialize sau</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Flutter Basics Section -->
                <section id="flutter-basics-section" class="section">
                    <div class="section__header">
                        <h2>🎨 Flutter Development - Cơ Bản</h2>
                        <p class="section__description">Khám phá framework Flutter và cách xây dựng ứng dụng mobile đẹp mắt</p>
                        <div class="section__meta">
                            <span class="meta-tag">⏱️ Thời gian: 25 giờ</span>
                            <span class="meta-tag">📚 35 chủ đề</span>
                            <span class="meta-tag">🔥 Thực hành</span>
                        </div>
                    </div>

                    <div class="topic-category">
                        <h3 class="category-title expandable" data-expanded="true">
                            <span>🎨 Widget Architecture</span>
                            <span class="expand-icon">▼</span>
                        </h3>
                        <div class="category-content expanded">
                            <div class="topic-card">
                                <div class="topic-header expandable">
                                    <h4>🧩 StatelessWidget vs StatefulWidget</h4>
                                    <div class="topic-actions">
                                        <button class="bookmark-btn" title="Đánh dấu">🔖</button>
                                        <label class="topic-checkbox-label">
                                            <input type="checkbox" class="topic-checkbox">
                                            <span class="custom-checkbox"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="topic-content expanded">
                                    <p>Flutter có hai loại widget chính: StatelessWidget cho UI tĩnh và StatefulWidget cho UI có thể thay đổi trạng thái.</p>

                                    <div class="code-example">
                                        <div class="code-header">
                                            <h5>💡 StatelessWidget Example</h5>
                                            <button class="copy-btn" onclick="copyCode(this)">📋 Copy</button>
                                        </div>
                                        <pre><code class="language-dart">class WelcomeScreen extends StatelessWidget {
  final String userName;
  final String appVersion;
  
  const WelcomeScreen({
    Key? key,
    required this.userName,
    required this.appVersion,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Welcome'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.waving_hand,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'Chào mừng, $userName!',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            SizedBox(height: 8),
            Text(
              'Phiên bản: $appVersion',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}</code></pre>
                                    </div>

                                    <div class="code-example">
                                        <div class="code-header">
                                            <h5>💡 StatefulWidget Example</h5>
                                            <button class="copy-btn" onclick="copyCode(this)">📋 Copy</button>
                                        </div>
                                        <pre><code class="language-dart">class CounterWidget extends StatefulWidget {
  final int initialValue;
  final Function(int)? onValueChanged;
  
  const CounterWidget({
    Key? key,
    this.initialValue = 0,
    this.onValueChanged,
  }) : super(key: key);

  @override
  State<CounterWidget> createState() => _CounterWidgetState();
}

class _CounterWidgetState extends State<CounterWidget> {
  late int _counter;
  
  @override
  void initState() {
    super.initState();
    _counter = widget.initialValue;
  }
  
  void _incrementCounter() {
    setState(() {
      _counter++;
    });
    widget.onValueChanged?.call(_counter);
  }
  
  void _decrementCounter() {
    setState(() {
      _counter--;
    });
    widget.onValueChanged?.call(_counter);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Counter Value',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 16),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ElevatedButton(
                  onPressed: _decrementCounter,
                  child: Icon(Icons.remove),
                ),
                SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _incrementCounter,
                  child: Icon(Icons.add),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Other sections would be similar with expanded content -->
                <section id="environment-section" class="section">
                    <div class="section__header">
                        <h2>⚙️ Môi Trường Phát Triển</h2>
                        <p class="section__description">Thiết lập và tối ưu hóa môi trường phát triển mobile</p>
                    </div>
                    <div class="topic-category">
                        <h3 class="category-title expandable">
                            <span>💻 Development Tools</span>
                            <span class="expand-icon">▼</span>
                        </h3>
                        <div class="category-content">
                            <div class="topic-card">
                                <div class="topic-header expandable">
                                    <h4>🛠️ IDE Setup & Configuration</h4>
                                    <div class="topic-actions">
                                        <button class="bookmark-btn" title="Đánh dấu">🔖</button>
                                        <label class="topic-checkbox-label">
                                            <input type="checkbox" class="topic-checkbox">
                                            <span class="custom-checkbox"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="topic-content">
                                    <p>Hướng dẫn thiết lập IDE và các công cụ phát triển cần thiết.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Additional sections would be added here -->
                <section id="dart-oop-section" class="section">
                    <div class="section__header">
                        <h2>🏗️ Dart OOP</h2>
                        <p class="section__description">Lập trình hướng đối tượng với Dart</p>
                    </div>
                </section>

                <section id="dart-advanced-section" class="section">
                    <div class="section__header">
                        <h2>⚡ Dart Nâng Cao</h2>
                        <p class="section__description">Các tính năng nâng cao của Dart</p>
                    </div>
                </section>
            </main>
        </div>

        <!-- Quick Reference Panel -->
        <div class="quick-reference" id="quickReference">
            <div class="reference-header">
                <h3>📋 Quick Reference</h3>
                <button id="toggleReference" class="btn btn--outline">▼</button>
            </div>
            <div class="reference-content">
                <div class="reference-section">
                    <h4>🔤 Dart Syntax</h4>
                    <div class="reference-item">
                        <code>var name = 'value';</code>
                        <span>Type inference</span>
                    </div>
                    <div class="reference-item">
                        <code>final value = 'constant';</code>
                        <span>Runtime constant</span>
                    </div>
                    <div class="reference-item">
                        <code>const value = 'constant';</code>
                        <span>Compile-time constant</span>
                    </div>
                </div>
                
                <div class="reference-section">
                    <h4>📱 Flutter Shortcuts</h4>
                    <div class="reference-item">
                        <code>stl</code>
                        <span>StatelessWidget snippet</span>
                    </div>
                    <div class="reference-item">
                        <code>stf</code>
                        <span>StatefulWidget snippet</span>
                    </div>
                    <div class="reference-item">
                        <code>Ctrl/Cmd + Shift + P</code>
                        <span>Flutter hot reload</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bookmarks Panel -->
        <div id="bookmarksPanel" class="bookmarks-panel hidden">
            <div class="bookmarks-header">
                <h3>📚 Bookmarks</h3>
                <div class="bookmarks-actions">
                    <button id="clearBookmarks" class="btn btn--outline btn--sm">🗑️ Xóa tất cả</button>
                    <button id="closeBookmarks" class="btn btn--outline btn--sm">✕</button>
                </div>
            </div>
            <div class="bookmarks-filter">
                <input type="text" id="bookmarkSearch" placeholder="Tìm trong bookmarks..." class="form-control">
            </div>
            <div id="bookmarksList" class="bookmarks-list">
                <div class="empty-bookmarks">
                    <p>🔖 Chưa có bookmark nào</p>
                    <small>Click vào icon 🔖 để đánh dấu các chủ đề quan trọng</small>
                </div>
            </div>
        </div>

        <!-- Search Results Modal -->
        <div id="searchModal" class="modal hidden">
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🔍 Kết Quả Tìm Kiếm</h3>
                    <button id="closeSearch" class="btn btn--outline btn--sm">✕</button>
                </div>
                <div class="search-filters">
                    <select id="searchFilter" class="form-control">
                        <option value="all">Tất cả</option>
                        <option value="dart">Dart</option>
                        <option value="flutter">Flutter</option>
                        <option value="react-native">React Native</option>
                        <option value="architecture">Architecture</option>
                    </select>
                </div>
                <div id="searchResults" class="search-results">
                    <div class="search-placeholder">
                        <p>💡 Nhập từ khóa để tìm kiếm trong toàn bộ handbook</p>
                        <small>Hỗ trợ tìm kiếm theo chủ đề, code examples, và best practices</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options Modal -->
        <div id="exportModal" class="modal hidden">
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>📤 Xuất Nội Dung</h3>
                    <button id="closeExport" class="btn btn--outline btn--sm">✕</button>
                </div>
                <div class="export-options">
                    <button id="exportPDF" class="btn btn--primary export-btn">
                        📄 Xuất PDF
                        <small>Toàn bộ section hiện tại</small>
                    </button>
                    <button id="exportMarkdown" class="btn btn--secondary export-btn">
                        📝 Xuất Markdown
                        <small>Định dạng text thuần</small>
                    </button>
                    <button id="exportBookmarks" class="btn btn--outline export-btn">
                        🔖 Xuất Bookmarks
                        <small>Danh sách đã đánh dấu</small>
                    </button>
                </div>
            </div>
        </div>

        <!-- Floating Action Button -->
        <div class="fab-container">
            <button id="fabMain" class="fab fab-main">⚡</button>
            <div class="fab-menu">
                <button id="fabBookmarks" class="fab fab-mini" title="Bookmarks">📚</button>
                <button id="fabSearch" class="fab fab-mini" title="Tìm kiếm">🔍</button>
                <button id="fabProgress" class="fab fab-mini" title="Tiến độ">📊</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>