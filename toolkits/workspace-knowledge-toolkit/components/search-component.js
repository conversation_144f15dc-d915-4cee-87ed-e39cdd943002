/**
 * Advanced Search Component for Workspace Knowledge Toolkit
 * Provides fuzzy search, filtering, and intelligent ranking
 */

class SearchComponent {
    constructor(searchIndex, knowledgeIndex) {
        this.searchIndex = searchIndex;
        this.knowledgeIndex = knowledgeIndex;
        this.searchInput = document.getElementById('globalSearchInput');
        this.searchBtn = document.getElementById('globalSearchBtn');
        this.searchClear = document.getElementById('searchClear');
        this.searchResults = document.getElementById('searchResults');
        this.searchResultsContent = document.getElementById('searchResultsContent');
        this.searchResultsCount = document.getElementById('searchResultsCount');
        
        this.currentQuery = '';
        this.searchTimeout = null;
        this.isSearching = false;
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Search input events
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch(e.target.value);
            } else if (e.key === 'Escape') {
                this.clearSearch();
            }
        });

        // Search button
        this.searchBtn.addEventListener('click', () => {
            this.performSearch(this.searchInput.value);
        });

        // Clear button
        this.searchClear.addEventListener('click', () => {
            this.clearSearch();
        });

        // Click outside to close results
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                this.hideSearchResults();
            }
        });

        // Focus events
        this.searchInput.addEventListener('focus', () => {
            if (this.currentQuery) {
                this.showSearchResults();
            }
        });
    }

    handleSearchInput(query) {
        this.currentQuery = query.trim();
        
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // Show/hide clear button
        this.searchClear.style.display = this.currentQuery ? 'block' : 'none';

        if (this.currentQuery.length === 0) {
            this.hideSearchResults();
            return;
        }

        // Debounced search
        this.searchTimeout = setTimeout(() => {
            this.performSearch(this.currentQuery);
        }, 300);
    }

    async performSearch(query) {
        if (!query || query.length < 2) {
            this.hideSearchResults();
            return;
        }

        this.isSearching = true;
        this.showLoadingState();

        try {
            const results = await this.searchKnowledge(query);
            this.displaySearchResults(results, query);
        } catch (error) {
            console.error('Search error:', error);
            this.showErrorState(error.message);
        } finally {
            this.isSearching = false;
        }
    }

    async searchKnowledge(query) {
        const searchableContent = this.searchIndex.searchableContent;
        const config = this.searchIndex.searchConfig;
        const results = [];

        const queryLower = query.toLowerCase();
        const queryWords = queryLower.split(/\s+/).filter(word => word.length > 1);

        // Search through all searchable content
        for (const [id, content] of Object.entries(searchableContent)) {
            const score = this.calculateRelevanceScore(content, queryLower, queryWords, config);
            
            if (score > 0) {
                results.push({
                    id,
                    ...content,
                    score,
                    highlights: this.generateHighlights(content, queryWords)
                });
            }
        }

        // Sort by relevance score
        results.sort((a, b) => b.score - a.score);

        // Limit results
        return results.slice(0, config.maxResults || 20);
    }

    calculateRelevanceScore(content, query, queryWords, config) {
        let score = 0;
        const boostFactors = config.boostFactors;

        // Exact phrase match (highest priority)
        if (content.title.toLowerCase().includes(query)) {
            score += 100 * boostFactors.title;
        }
        if (content.content.toLowerCase().includes(query)) {
            score += 50 * boostFactors.content;
        }

        // Individual word matches
        queryWords.forEach(word => {
            // Title matches
            if (content.title.toLowerCase().includes(word)) {
                score += 20 * boostFactors.title;
            }

            // Keyword matches
            if (content.keywords.some(keyword => keyword.toLowerCase().includes(word))) {
                score += 15 * boostFactors.keywords;
            }

            // Content matches
            const contentMatches = (content.content.toLowerCase().match(new RegExp(word, 'g')) || []).length;
            score += contentMatches * 5 * boostFactors.content;

            // Tag matches
            if (content.tags && content.tags.some(tag => tag.toLowerCase().includes(word))) {
                score += 10 * boostFactors.tags;
            }
        });

        // Priority boost
        if (content.priority === 1) {
            score *= 1.5;
        } else if (content.priority === 2) {
            score *= 1.2;
        }

        // Fuzzy matching for typos
        if (config.fuzzySearch) {
            queryWords.forEach(word => {
                const fuzzyMatches = this.findFuzzyMatches(word, content);
                score += fuzzyMatches * 2;
            });
        }

        return score;
    }

    findFuzzyMatches(word, content) {
        let matches = 0;
        const allText = `${content.title} ${content.keywords.join(' ')} ${content.content}`.toLowerCase();
        const words = allText.split(/\s+/);

        words.forEach(textWord => {
            if (this.calculateLevenshteinDistance(word, textWord) <= 2 && textWord.length > 3) {
                matches++;
            }
        });

        return matches;
    }

    calculateLevenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    generateHighlights(content, queryWords) {
        const highlights = {};
        
        queryWords.forEach(word => {
            const regex = new RegExp(`(${word})`, 'gi');
            
            highlights.title = content.title.replace(regex, '<mark>$1</mark>');
            highlights.content = this.truncateWithHighlight(content.content, word, 150);
        });

        return highlights;
    }

    truncateWithHighlight(text, word, maxLength) {
        const regex = new RegExp(`(${word})`, 'gi');
        const match = text.match(regex);
        
        if (!match) {
            return text.substring(0, maxLength) + (text.length > maxLength ? '...' : '');
        }

        const index = text.toLowerCase().indexOf(word.toLowerCase());
        const start = Math.max(0, index - 50);
        const end = Math.min(text.length, start + maxLength);
        
        let excerpt = text.substring(start, end);
        if (start > 0) excerpt = '...' + excerpt;
        if (end < text.length) excerpt = excerpt + '...';
        
        return excerpt.replace(regex, '<mark>$1</mark>');
    }

    displaySearchResults(results, query) {
        this.searchResultsCount.textContent = `${results.length} result${results.length !== 1 ? 's' : ''}`;
        
        if (results.length === 0) {
            this.searchResultsContent.innerHTML = `
                <div class="search-no-results">
                    <div class="no-results-icon">🔍</div>
                    <div class="no-results-text">No results found for "${query}"</div>
                    <div class="no-results-suggestions">
                        Try different keywords or check spelling
                    </div>
                </div>
            `;
        } else {
            this.searchResultsContent.innerHTML = results.map(result => 
                this.createSearchResultItem(result)
            ).join('');
        }

        this.showSearchResults();
    }

    createSearchResultItem(result) {
        const resource = this.knowledgeIndex.resources[result.category]?.items?.find(item => item.id === result.id);
        const categoryInfo = this.knowledgeIndex.resources[result.category];
        
        return `
            <div class="search-result-item" data-id="${result.id}" data-category="${result.category}">
                <div class="search-result-header">
                    <div class="search-result-title">
                        ${result.highlights?.title || result.title}
                    </div>
                    <div class="search-result-category">
                        ${categoryInfo?.icon || '📄'} ${categoryInfo?.category || result.category}
                    </div>
                </div>
                <div class="search-result-content">
                    ${result.highlights?.content || result.content.substring(0, 150) + '...'}
                </div>
                <div class="search-result-footer">
                    <div class="search-result-tags">
                        ${result.tags ? result.tags.slice(0, 3).map(tag => 
                            `<span class="search-tag">${tag}</span>`
                        ).join('') : ''}
                    </div>
                    <div class="search-result-score">
                        Score: ${Math.round(result.score)}
                    </div>
                </div>
            </div>
        `;
    }

    showSearchResults() {
        this.searchResults.style.display = 'block';
        
        // Add click handlers to result items
        this.searchResultsContent.querySelectorAll('.search-result-item').forEach(item => {
            item.addEventListener('click', () => {
                const id = item.dataset.id;
                const category = item.dataset.category;
                this.handleResultClick(id, category);
            });
        });
    }

    hideSearchResults() {
        this.searchResults.style.display = 'none';
    }

    showLoadingState() {
        this.searchResultsContent.innerHTML = `
            <div class="search-loading">
                <div class="search-loading-spinner"></div>
                <div class="search-loading-text">Searching...</div>
            </div>
        `;
        this.searchResultsCount.textContent = 'Searching...';
        this.showSearchResults();
    }

    showErrorState(message) {
        this.searchResultsContent.innerHTML = `
            <div class="search-error">
                <div class="search-error-icon">⚠️</div>
                <div class="search-error-text">Search Error</div>
                <div class="search-error-message">${message}</div>
            </div>
        `;
        this.searchResultsCount.textContent = 'Error';
        this.showSearchResults();
    }

    clearSearch() {
        this.searchInput.value = '';
        this.currentQuery = '';
        this.searchClear.style.display = 'none';
        this.hideSearchResults();
        this.searchInput.focus();
    }

    handleResultClick(id, category) {
        // Emit custom event for result selection
        const event = new CustomEvent('searchResultSelected', {
            detail: { id, category }
        });
        document.dispatchEvent(event);
        
        this.hideSearchResults();
    }

    // Public API methods
    setSearchIndex(searchIndex) {
        this.searchIndex = searchIndex;
    }

    setKnowledgeIndex(knowledgeIndex) {
        this.knowledgeIndex = knowledgeIndex;
    }

    focus() {
        this.searchInput.focus();
    }

    getQuery() {
        return this.currentQuery;
    }
}

// Export for use in main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchComponent;
} else {
    window.SearchComponent = SearchComponent;
}
