{"metadata": {"version": "1.0.0", "lastUpdated": "2025-01-09", "totalResources": 154, "categories": ["documentation", "algorithms", "design-patterns"]}, "resources": {"documentation": {"category": "Core Documentation", "description": "Central documentation hub and enterprise guides", "icon": "📚", "items": [{"id": "ultimate-knowledge-base", "title": "Ultimate Knowledge Base", "path": "../../docs/ULTIMATE_KNOWLEDGE_BASE.md", "type": "markdown", "size": "439 lines", "description": "Master index and supreme documentation system", "tags": ["master-index", "enterprise", "ultimate"], "priority": 1}, {"id": "main-readme", "title": "Enterprise Platform README", "path": "../../README.md", "type": "markdown", "size": "774 lines", "description": "Complete enterprise architecture overview", "tags": ["overview", "architecture", "enterprise"], "priority": 1}, {"id": "docs-readme", "title": "Documentation Hub", "path": "../../docs/README.md", "type": "markdown", "size": "197 lines", "description": "Ultimate enterprise documentation hub", "tags": ["documentation", "hub", "navigation"], "priority": 1}, {"id": "learning-paths", "title": "Learning Paths", "path": "../../docs/LEARNING_PATHS.md", "type": "markdown", "size": "205 lines", "description": "Senior engineering career mastery paths", "tags": ["learning", "career", "mastery"], "priority": 2}, {"id": "quick-reference", "title": "Quick Reference", "path": "../../docs/QUICK_REFERENCE.md", "type": "markdown", "description": "Instant access tables and references", "tags": ["reference", "quick-access", "tables"], "priority": 2}, {"id": "deployment-guide", "title": "Deployment Guide", "path": "../../DEPLOYMENT.md", "type": "markdown", "size": "376 lines", "description": "Enterprise platform deployment guide", "tags": ["deployment", "production", "guide"], "priority": 2}]}, "algorithms": {"category": "Algorithm Mastery System", "description": "Comprehensive algorithms and data structures learning platform", "icon": "🎨", "items": [{"id": "algorithm-readme", "title": "Algorithm Mastery System", "path": "../../algorithm-mastery-system/README.md", "type": "markdown", "size": "373 lines", "description": "Complete algorithms learning platform with 100+ implementations", "tags": ["algorithms", "learning", "mastery"], "priority": 1}, {"id": "algorithm-index", "title": "Algorithm Index", "path": "../../algorithm-mastery-system/INDEX.md", "type": "markdown", "size": "191 lines", "description": "Complete cross-reference guide for algorithms", "tags": ["index", "cross-reference", "navigation"], "priority": 1}, {"id": "algorithm-learning-path", "title": "Algorithm Learning Path", "path": "../../algorithm-mastery-system/LEARNING-PATH.md", "type": "markdown", "description": "4-level mastery roadmap for algorithms", "tags": ["learning-path", "roadmap", "progression"], "priority": 2}, {"id": "algorithm-getting-started", "title": "Algorithm Getting Started", "path": "../../algorithm-mastery-system/GETTING-STARTED.md", "type": "markdown", "description": "30-day beginner program for algorithms", "tags": ["getting-started", "beginner", "30-day"], "priority": 2}, {"id": "algorithm-selection-guide", "title": "Algorithm Selection Guide", "path": "../../algorithm-mastery-system/ALGORITHM-SELECTION-GUIDE.md", "type": "markdown", "description": "Decision framework for algorithm selection", "tags": ["selection", "decision", "framework"], "priority": 3}]}, "design-patterns": {"category": "Design Patterns", "description": "Complete GoF patterns with multi-language implementations", "icon": "🎨", "items": [{"id": "patterns-readme", "title": "Design Patterns Overview", "path": "../../design-patterns/README.md", "type": "markdown", "size": "222 lines", "description": "Complete study materials for design patterns", "tags": ["design-patterns", "gof", "overview"], "priority": 1}, {"id": "patterns-index", "title": "Design Patterns Index", "path": "../../design-patterns/INDEX.md", "type": "markdown", "size": "306 lines", "description": "Comprehensive cross-reference guide for design patterns", "tags": ["index", "cross-reference", "patterns"], "priority": 1}, {"id": "patterns-learning-path", "title": "Design Patterns Learning Path", "path": "../../design-patterns/LEARNING-PATH.md", "type": "markdown", "description": "4-level structured roadmap for design patterns", "tags": ["learning-path", "roadmap", "structured"], "priority": 2}, {"id": "patterns-selection-guide", "title": "Pattern Selection Guide", "path": "../../design-patterns/PATTERN-SELECTION-GUIDE.md", "type": "markdown", "description": "Decision trees and pattern comparisons", "tags": ["selection", "decision-trees", "comparison"], "priority": 2}, {"id": "patterns-getting-started", "title": "Design Patterns Getting Started", "path": "../../design-patterns/GETTING-STARTED.md", "type": "markdown", "description": "Complete beginner guide to design patterns", "tags": ["getting-started", "beginner", "guide"], "priority": 2}]}}, "searchHints": {"gettingStarted": ["ultimate-knowledge-base", "learning-paths", "deployment-guide"], "learning": ["learning-paths", "algorithm-mastery-index", "design-patterns-index"], "reference": ["ultimate-knowledge-base", "algorithm-reference", "pattern-reference"], "implementation": ["algorithm-implementations", "pattern-examples", "deployment-guide"]}, "searchIndex": {"tags": ["master-index", "enterprise", "ultimate", "overview", "architecture", "documentation", "hub", "navigation", "learning", "career", "mastery", "reference", "quick-access", "tables", "deployment", "production", "guide", "algorithms", "index", "cross-reference", "learning-path", "roadmap", "progression", "getting-started", "beginner", "30-day", "selection", "decision", "framework", "design-patterns", "gof", "patterns", "structured", "decision-trees", "comparison"], "categories": ["documentation", "algorithms", "design-patterns"], "priorities": {"1": "Essential resources - start here", "2": "Important resources - core learning", "3": "Supporting resources - reference material"}}}