{"searchableContent": {"ultimate-knowledge-base": {"title": "Ultimate Knowledge Base", "keywords": ["ultimate", "knowledge", "base", "enterprise", "documentation", "supreme", "master", "index"], "content": "Supreme Documentation System Zero duplication maximum efficiency complete professional organization", "category": "documentation", "priority": 1}, "main-readme": {"title": "Enterprise Platform README", "keywords": ["enterprise", "platform", "architecture", "universal", "software", "career", "template"], "content": "Enterprise-Grade Universal Software Architecture Ultimate Career Template Complete enterprise architecture covering 100% of modern IT knowledge", "category": "documentation", "priority": 1}, "algorithm-readme": {"title": "Algorithm Mastery System", "keywords": ["algorithm", "mastery", "system", "computational", "thinking", "data", "structures"], "content": "Comprehensive learning platform for algorithms and data structures from beginner to expert level with hands-on JavaScript implementations", "category": "algorithms", "priority": 1}, "patterns-readme": {"title": "Design Patterns Overview", "keywords": ["design", "patterns", "gof", "gang", "four", "software", "design", "art"], "content": "Comprehensive study materials for Design Patterns từ cơ bản đến nâng cao từ lý thuyết đến thực hành", "category": "design-patterns", "priority": 1}}, "facets": {"categories": {"documentation": {"label": "Documentation", "count": 6, "description": "Core documentation and enterprise guides"}, "algorithms": {"label": "Algorithms", "count": 5, "description": "Algorithm mastery and data structures"}, "design-patterns": {"label": "Design Patterns", "count": 5, "description": "GoF patterns and software design"}}, "types": {"markdown": {"label": "Markdown Files", "count": 20, "description": "Documentation and guides"}, "html": {"label": "Interactive Tools", "count": 0, "description": "Web-based tools"}, "csv": {"label": "Data Files", "count": 2, "description": "Structured data references"}}, "priorities": {"1": {"label": "Essential", "count": 8, "description": "Must-read resources"}, "2": {"label": "Important", "count": 12, "description": "Core learning materials"}, "3": {"label": "Reference", "count": 3, "description": "Supporting documentation"}}}, "quickAccess": {"gettingStarted": ["ultimate-knowledge-base", "main-readme", "docs-readme"], "learning": ["learning-paths", "algorithm-learning-path", "patterns-learning-path"], "reference": ["algorithm-index", "patterns-index", "quick-reference"], "implementation": ["deployment-guide", "algorithm-implementations"]}, "relatedContent": {"ultimate-knowledge-base": ["main-readme", "docs-readme", "learning-paths"], "algorithm-readme": ["algorithm-index", "algorithm-learning-path", "algorithm-getting-started"], "patterns-readme": ["patterns-index", "patterns-learning-path", "patterns-selection-guide"]}, "searchConfig": {"fuzzySearch": true, "maxResults": 20, "highlightMatches": true, "searchFields": ["title", "keywords", "content", "tags"], "boostFactors": {"title": 3.0, "keywords": 2.0, "content": 1.0, "tags": 1.5}}}