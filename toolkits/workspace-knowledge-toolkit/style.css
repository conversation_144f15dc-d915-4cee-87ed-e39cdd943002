/* Workspace Knowledge Toolkit Styles */
:root {
    /* Light theme colors */
    --bg-color: #f8f9fa;
    --text-color: #212529;
    --header-bg: #ffffff;
    --sidebar-bg: #ffffff;
    --border-color: #dee2e6;
    --primary-color: #007d9c;
    --secondary-color: #6c757d;
    --hover-bg: #e9ecef;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* Shadows and effects */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
}

[data-theme="dark"] {
    --bg-color: #121212;
    --text-color: #e0e0e0;
    --header-bg: #1e1e1e;
    --sidebar-bg: #1e1e1e;
    --border-color: #333;
    --primary-color: #61dafb;
    --secondary-color: #adb5bd;
    --hover-bg: #2a2a2a;
    --success-color: #20c997;
    --warning-color: #ffc107;
    --danger-color: #e74c3c;
    --info-color: #3498db;
}

/* Base styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    margin: 0;
    padding: 0;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    line-height: 1.6;
}

/* Header */
.header {
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    gap: var(--spacing-md);
}

.header-title {
    margin: 0;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.header-icon {
    font-size: var(--font-size-3xl);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
    max-width: 600px;
}

/* Search container */
.search-container {
    display: flex;
    align-items: center;
    flex: 1;
    position: relative;
}

.search-input {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: 8px 0 0 8px;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: border-color var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 125, 156, 0.25);
}

.search-btn, .search-clear {
    border: 1px solid var(--border-color);
    background-color: var(--header-bg);
    color: var(--text-color);
    cursor: pointer;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
}

.search-btn {
    border-left: none;
    border-radius: 0;
}

.search-clear {
    border-left: none;
    border-radius: 0 8px 8px 0;
    opacity: 0.7;
}

.search-btn:hover, .search-clear:hover {
    background-color: var(--hover-bg);
}

/* Header actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.action-btn, .theme-toggle {
    border: 1px solid var(--border-color);
    background-color: transparent;
    color: var(--text-color);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 6px;
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover, .theme-toggle:hover {
    background-color: var(--hover-bg);
    border-color: var(--primary-color);
}

/* Search results dropdown */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--header-bg);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: var(--shadow-md);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1001;
}

.search-results-header {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--hover-bg);
}

.search-results-title {
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.search-results-count {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
}

.search-results-content {
    max-height: 350px;
    overflow-y: auto;
}

/* Main layout */
.main-layout {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 80px);
}

/* Sidebar */
.sidebar {
    width: 320px;
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-md);
    overflow-y: auto;
    height: calc(100vh - 80px);
    position: sticky;
    top: 80px;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--primary-color);
}

.sidebar-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.sidebar-toggle, .sidebar-refresh {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover, .sidebar-refresh:hover {
    background-color: var(--hover-bg);
}

/* Quick access */
.quick-access {
    margin-bottom: var(--spacing-lg);
}

.quick-access h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-base);
    color: var(--secondary-color);
}

.quick-access-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.quick-access-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 6px;
    font-size: var(--font-size-sm);
    text-align: left;
    transition: all var(--transition-fast);
}

.quick-access-btn:hover {
    background-color: var(--hover-bg);
    border-color: var(--primary-color);
}

/* Category filters */
.category-filters {
    margin-bottom: var(--spacing-lg);
}

.category-filters h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-base);
    color: var(--secondary-color);
}

.filter-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background-color var(--transition-fast);
}

.filter-item:hover {
    background-color: var(--hover-bg);
}

.filter-checkbox {
    margin: 0;
}

.filter-label {
    flex: 1;
    font-size: var(--font-size-sm);
}

.filter-count {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
    background-color: var(--hover-bg);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

/* Navigation tree */
.nav-tree h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-base);
    color: var(--secondary-color);
}

.nav-tree-content {
    font-size: var(--font-size-sm);
}

/* Main content */
.main-content {
    flex: 1;
    padding: var(--spacing-md);
    overflow-y: auto;
}

/* Breadcrumb */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background-color: var(--hover-bg);
    border-radius: 6px;
    font-size: var(--font-size-sm);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--secondary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 600;
}

.breadcrumb-item:not(.active):hover {
    color: var(--primary-color);
}

.breadcrumb-icon {
    font-size: var(--font-size-sm);
}

/* Content section */
.content-section {
    background-color: var(--header-bg);
    padding: var(--spacing-xl);
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    min-height: 600px;
}

/* Welcome screen */
.welcome-screen {
    text-align: center;
}

.welcome-header h2 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
}

.welcome-subtitle {
    font-size: var(--font-size-lg);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xl);
}

/* Welcome stats */
.welcome-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background-color: var(--hover-bg);
    padding: var(--spacing-lg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

/* Welcome actions */
.welcome-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.welcome-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: 8px;
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.welcome-btn.primary {
    background-color: var(--primary-color);
    color: white;
}

.welcome-btn.primary:hover {
    background-color: #006a87;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.welcome-btn.secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.welcome-btn.secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Recent resources */
.recent-resources h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
    text-align: left;
}

.resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
}

/* Right panel */
.right-panel {
    width: 300px;
    background-color: var(--sidebar-bg);
    border-left: 1px solid var(--border-color);
    padding: var(--spacing-md);
    overflow-y: auto;
    height: calc(100vh - 80px);
    position: sticky;
    top: 80px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.panel-close {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    font-size: var(--font-size-lg);
    padding: var(--spacing-xs);
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background-color: var(--header-bg);
    padding: var(--spacing-xl);
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-color);
    font-size: var(--font-size-base);
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 100px;
    right: var(--spacing-md);
    z-index: 9998;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* Navigation tree styles */
.nav-tree-root {
    font-size: var(--font-size-sm);
}

.nav-tree-node {
    margin-bottom: var(--spacing-xs);
}

.nav-node-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.nav-node-header:hover {
    background-color: var(--hover-bg);
}

.nav-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    font-size: var(--font-size-xs);
    padding: 2px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-spacer {
    width: 16px;
}

.nav-icon {
    font-size: var(--font-size-sm);
}

.nav-label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nav-count {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
    background-color: var(--hover-bg);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.nav-priority {
    color: var(--warning-color);
    font-size: var(--font-size-xs);
}

.nav-node-content {
    margin-left: var(--spacing-md);
    overflow: hidden;
    transition: max-height var(--transition-normal);
}

.nav-node-content.collapsed {
    max-height: 0;
}

.nav-node-content.expanded {
    max-height: 1000px;
}

.nav-item-node.selected .nav-node-header {
    background-color: var(--primary-color);
    color: white;
}

.nav-item-details {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-xs);
    padding-left: var(--spacing-lg);
}

.nav-detail-size {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
}

.nav-item-tags {
    display: flex;
    gap: var(--spacing-xs);
}

.nav-tag {
    font-size: var(--font-size-xs);
    background-color: var(--hover-bg);
    color: var(--secondary-color);
    padding: 2px 4px;
    border-radius: 3px;
}

/* Search result styles */
.search-result-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-result-item:hover {
    background-color: var(--hover-bg);
}

.search-result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.search-result-title {
    font-weight: 600;
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

.search-result-category {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
    background-color: var(--hover-bg);
    padding: 2px 6px;
    border-radius: 3px;
}

.search-result-content {
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.search-result-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-result-tags {
    display: flex;
    gap: var(--spacing-xs);
}

.search-tag {
    font-size: var(--font-size-xs);
    background-color: var(--hover-bg);
    color: var(--secondary-color);
    padding: 2px 4px;
    border-radius: 3px;
}

.search-result-score {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
}

.search-no-results, .search-loading, .search-error {
    padding: var(--spacing-xl);
    text-align: center;
}

.no-results-icon, .search-error-icon {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-sm);
}

.search-loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-sm);
}

/* Content display styles */
.content-title-section {
    margin-bottom: var(--spacing-lg);
}

.content-category {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.content-title {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--primary-color);
    font-size: var(--font-size-3xl);
}

.content-description {
    color: var(--secondary-color);
    font-size: var(--font-size-lg);
    margin: 0;
}

.content-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--hover-bg);
    border-radius: 8px;
}

.content-meta-items {
    display: flex;
    gap: var(--spacing-md);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.meta-icon {
    font-size: var(--font-size-sm);
}

.priority-1 {
    color: var(--warning-color);
    font-weight: 600;
}

.content-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.content-tags {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.content-tag {
    background-color: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Featured resource cards */
.featured-resource-card {
    background-color: var(--hover-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.featured-resource-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.featured-card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.featured-icon {
    font-size: var(--font-size-lg);
}

.featured-category {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
    text-transform: uppercase;
    font-weight: 600;
}

.featured-title {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.featured-description {
    color: var(--text-color);
    margin: 0 0 var(--spacing-md) 0;
    line-height: 1.4;
}

.featured-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
}

.featured-type {
    background-color: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 600;
}

/* Toast notifications */
.toast {
    background-color: var(--header-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-sm);
    animation: slideIn 0.3s ease-out;
}

.toast-info {
    border-left: 4px solid var(--info-color);
}

.toast-error {
    border-left: 4px solid var(--danger-color);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
}

.toast-icon {
    font-size: var(--font-size-lg);
}

.toast-message {
    flex: 1;
    color: var(--text-color);
}

.toast-close {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    font-size: var(--font-size-base);
    padding: var(--spacing-xs);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Content wrapper styles */
.content-wrapper {
    line-height: 1.6;
}

.content-wrapper h1,
.content-wrapper h2,
.content-wrapper h3,
.content-wrapper h4,
.content-wrapper h5,
.content-wrapper h6 {
    color: var(--primary-color);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.content-wrapper p {
    margin-bottom: var(--spacing-md);
}

.content-wrapper code {
    background-color: var(--hover-bg);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: var(--font-family-mono);
    font-size: 0.9em;
}

.content-wrapper pre {
    background-color: var(--hover-bg);
    padding: var(--spacing-md);
    border-radius: 6px;
    overflow-x: auto;
    margin: var(--spacing-md) 0;
}

.content-wrapper blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: var(--spacing-md);
    margin: var(--spacing-md) 0;
    color: var(--secondary-color);
    font-style: italic;
}

.content-wrapper table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--spacing-md) 0;
}

.content-wrapper th,
.content-wrapper td {
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm);
    text-align: left;
}

.content-wrapper th {
    background-color: var(--hover-bg);
    font-weight: 600;
}

/* Table of contents styles */
.table-of-contents {
    background-color: var(--hover-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-md);
    margin: var(--spacing-lg) 0;
}

.table-of-contents h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

.toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-item {
    margin-bottom: var(--spacing-xs);
}

.toc-item a {
    color: var(--text-color);
    text-decoration: none;
    display: block;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background-color var(--transition-fast);
}

.toc-item a:hover {
    background-color: var(--primary-color);
    color: white;
}

.toc-level-1 { margin-left: 0; }
.toc-level-2 { margin-left: var(--spacing-md); }
.toc-level-3 { margin-left: calc(var(--spacing-md) * 2); }
.toc-level-4 { margin-left: calc(var(--spacing-md) * 3); }
.toc-level-5 { margin-left: calc(var(--spacing-md) * 4); }
.toc-level-6 { margin-left: calc(var(--spacing-md) * 5); }

/* Responsive design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .main-layout {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: static;
    }

    .welcome-actions {
        flex-direction: column;
        align-items: center;
    }

    .resource-grid {
        grid-template-columns: 1fr;
    }

    .content-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .content-meta-items {
        flex-wrap: wrap;
    }
}
