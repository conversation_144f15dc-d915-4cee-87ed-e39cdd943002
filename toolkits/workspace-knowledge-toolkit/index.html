<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Workspace Knowledge Toolkit</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preload" href="data/knowledge-index.json" as="fetch" crossorigin>
    <link rel="preload" href="data/search-index.json" as="fetch" crossorigin>
    <link rel="preload" href="data/cross-references.json" as="fetch" crossorigin>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <h1 class="header-title">
                <span class="header-icon">🧠</span>
                Workspace Knowledge Toolkit
            </h1>
            <div class="header-controls">
                <div class="search-container">
                    <input type="text" 
                           id="globalSearchInput" 
                           class="search-input" 
                           placeholder="Search across all knowledge resources..."
                           autocomplete="off">
                    <button class="search-btn" id="globalSearchBtn" title="Search">🔍</button>
                    <button class="search-clear" id="searchClear" title="Clear search">✕</button>
                </div>
                <div class="header-actions">
                    <button id="bookmarksToggle" class="action-btn" title="Bookmarks">⭐</button>
                    <button id="historyToggle" class="action-btn" title="History">📚</button>

                </div>
            </div>
        </div>
        
        <!-- Search Results Dropdown -->
        <div class="search-results" id="searchResults" style="display: none;">
            <div class="search-results-header">
                <span class="search-results-title">Search Results</span>
                <span class="search-results-count" id="searchResultsCount">0 results</span>
            </div>
            <div class="search-results-content" id="searchResultsContent">
                <!-- Search results will be populated here -->
            </div>
        </div>
    </header>

    <div class="main-layout">
        <!-- Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3>📖 Knowledge Hub</h3>
                <div class="sidebar-controls">
                    <button class="sidebar-toggle" id="sidebarToggle" title="Toggle sidebar">⟨</button>
                    <button class="sidebar-refresh" id="sidebarRefresh" title="Refresh index">🔄</button>
                </div>
            </div>
            
            <!-- Quick Access -->
            <div class="quick-access">
                <h4>⚡ Quick Access</h4>
                <div class="quick-access-items">
                    <button class="quick-access-btn" data-category="gettingStarted">
                        🚀 Getting Started
                    </button>
                    <button class="quick-access-btn" data-category="learning">
                        🎓 Learning Paths
                    </button>
                    <button class="quick-access-btn" data-category="reference">
                        📚 Reference
                    </button>
                    <button class="quick-access-btn" data-category="implementation">
                        💻 Implementation
                    </button>
                </div>
            </div>

            <!-- Category Filters -->
            <div class="category-filters">
                <h4>🗂️ Categories</h4>
                <div class="filter-items">
                    <label class="filter-item">
                        <input type="checkbox" class="filter-checkbox" data-category="documentation" checked>
                        <span class="filter-label">📚 Documentation</span>
                        <span class="filter-count" id="count-documentation">6</span>
                    </label>
                    <label class="filter-item">
                        <input type="checkbox" class="filter-checkbox" data-category="algorithms" checked>
                        <span class="filter-label">🎨 Algorithms</span>
                        <span class="filter-count" id="count-algorithms">5</span>
                    </label>
                    <label class="filter-item">
                        <input type="checkbox" class="filter-checkbox" data-category="design-patterns" checked>
                        <span class="filter-label">🎨 Design Patterns</span>
                        <span class="filter-count" id="count-design-patterns">5</span>
                    </label>
                </div>
            </div>

            <!-- Navigation Tree -->
            <div class="nav-tree">
                <h4>🌳 Navigation</h4>
                <div class="nav-tree-content" id="navTreeContent">
                    <!-- Navigation tree will be populated here -->
                </div>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Breadcrumb Navigation -->
            <div class="breadcrumb" id="breadcrumb">
                <span class="breadcrumb-item active">
                    <span class="breadcrumb-icon">🏠</span>
                    Home
                </span>
            </div>

            <!-- Content Section -->
            <section class="content-section" id="contentSection">
                <!-- Welcome Screen -->
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="welcome-header">
                        <h2>🧠 Welcome to Workspace Knowledge Toolkit</h2>
                        <p class="welcome-subtitle">
                            Your unified gateway to all workspace knowledge resources
                        </p>
                    </div>

                    <div class="welcome-stats">
                        <div class="stat-card">
                            <div class="stat-number" id="totalResources">154</div>
                            <div class="stat-label">Total Resources</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalCategories">3</div>
                            <div class="stat-label">Categories</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalPatterns">32</div>
                            <div class="stat-label">Design Patterns</div>
                        </div>
                    </div>

                    <div class="welcome-actions">
                        <button class="welcome-btn primary" data-action="explore">
                            🚀 Start Exploring
                        </button>
                        <button class="welcome-btn secondary" data-action="search">
                            🔍 Search Knowledge
                        </button>
                        <button class="welcome-btn secondary" data-action="learning">
                            🎓 Learning Paths
                        </button>
                    </div>

                    <div class="recent-resources">
                        <h3>📖 Featured Resources</h3>
                        <div class="resource-grid" id="featuredResources">
                            <!-- Featured resources will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Content Display Area -->
                <div class="content-display" id="contentDisplay" style="display: none;">
                    <div class="content-header" id="contentHeader">
                        <!-- Content header will be populated here -->
                    </div>
                    <div class="content-body" id="contentBody">
                        <!-- Content body will be populated here -->
                    </div>
                    <div class="content-footer" id="contentFooter">
                        <!-- Content footer will be populated here -->
                    </div>
                </div>
            </section>
        </main>

        <!-- Right Panel (Optional) -->
        <aside class="right-panel" id="rightPanel" style="display: none;">
            <div class="panel-header">
                <h3 id="panelTitle">Panel</h3>
                <button class="panel-close" id="panelClose">✕</button>
            </div>
            <div class="panel-content" id="panelContent">
                <!-- Panel content will be populated here -->
            </div>
        </aside>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">Loading knowledge resources...</div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer">
        <!-- Toast notifications will be added here -->
    </div>

    <!-- Scripts -->
    <script src="parsers/markdown-parser.js"></script>
    <script src="parsers/csv-parser.js"></script>
    <script src="parsers/json-parser.js"></script>
    <script src="components/search-component.js"></script>
    <script src="components/navigation-tree.js"></script>
    <script src="components/content-viewer.js"></script>
    <script src="app.js"></script>
</body>
</html>
