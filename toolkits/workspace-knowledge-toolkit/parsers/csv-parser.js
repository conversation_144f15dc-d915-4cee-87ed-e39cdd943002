/**
 * Enhanced CSV Parser for Workspace Knowledge Toolkit
 * Provides advanced CSV parsing with sorting, filtering, and search capabilities
 */

class CsvParser {
    constructor(options = {}) {
        this.options = {
            delimiter: ',',
            hasHeader: true,
            escapeChar: '"',
            skipEmptyLines: true,
            ...options
        };
    }

    /**
     * Parse CSV text to interactive HTML table
     * @param {string} text - Raw CSV text
     * @param {string} sourceId - Source document ID
     * @returns {string} - Interactive HTML table
     */
    parse(text, sourceId = '') {
        if (!text) return '';

        const data = this.parseToArray(text);
        if (data.length === 0) return '<p>No data found</p>';

        return this.createInteractiveTable(data, sourceId);
    }

    /**
     * Parse CSV text to array of objects
     * @param {string} text - Raw CSV text
     * @returns {Array} - Array of row objects
     */
    parseToArray(text) {
        const lines = text.split('\n').filter(line => 
            this.options.skipEmptyLines ? line.trim() !== '' : true
        );

        if (lines.length === 0) return [];

        const headers = this.options.hasHeader 
            ? this.parseLine(lines[0])
            : this.generateHeaders(this.parseLine(lines[0]).length);

        const dataLines = this.options.hasHeader ? lines.slice(1) : lines;
        
        return dataLines.map((line, index) => {
            const values = this.parseLine(line);
            const row = { _rowIndex: index };
            
            headers.forEach((header, i) => {
                row[header] = values[i] || '';
            });
            
            return row;
        });
    }

    /**
     * Parse a single CSV line handling quotes and escapes
     * @param {string} line - CSV line
     * @returns {Array} - Array of cell values
     */
    parseLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        let i = 0;

        while (i < line.length) {
            const char = line[i];
            const nextChar = line[i + 1];

            if (char === this.options.escapeChar) {
                if (inQuotes && nextChar === this.options.escapeChar) {
                    // Escaped quote
                    current += this.options.escapeChar;
                    i += 2;
                } else {
                    // Toggle quote state
                    inQuotes = !inQuotes;
                    i++;
                }
            } else if (char === this.options.delimiter && !inQuotes) {
                // End of field
                result.push(current.trim());
                current = '';
                i++;
            } else {
                current += char;
                i++;
            }
        }

        // Add the last field
        result.push(current.trim());
        return result;
    }

    /**
     * Generate default headers for headerless CSV
     * @param {number} count - Number of columns
     * @returns {Array} - Array of header names
     */
    generateHeaders(count) {
        return Array.from({ length: count }, (_, i) => `Column ${i + 1}`);
    }

    /**
     * Create interactive HTML table with sorting and filtering
     * @param {Array} data - Parsed CSV data
     * @param {string} sourceId - Source document ID
     * @returns {string} - Interactive HTML table
     */
    createInteractiveTable(data, sourceId) {
        if (data.length === 0) return '<p>No data to display</p>';

        const headers = Object.keys(data[0]).filter(key => key !== '_rowIndex');
        const tableId = `csv-table-${sourceId}-${Date.now()}`;

        return `
            <div class="csv-table-container" data-source="${sourceId}">
                <div class="csv-table-controls">
                    <div class="csv-search-container">
                        <input type="text" 
                               class="csv-search-input" 
                               placeholder="Search in table..." 
                               data-table="${tableId}">
                        <button class="csv-search-clear" data-table="${tableId}">✕</button>
                    </div>
                    <div class="csv-table-info">
                        <span class="csv-row-count">${data.length} rows</span>
                        <span class="csv-col-count">${headers.length} columns</span>
                    </div>
                    <div class="csv-table-actions">
                        <button class="csv-export-btn" data-table="${tableId}" title="Export CSV">📥</button>
                        <button class="csv-fullscreen-btn" data-table="${tableId}" title="Fullscreen">⛶</button>
                    </div>
                </div>
                
                <div class="csv-table-wrapper">
                    <table id="${tableId}" class="csv-table" data-sortable="true">
                        <thead class="csv-table-header">
                            <tr>
                                ${headers.map(header => `
                                    <th class="csv-header-cell" data-column="${header}">
                                        <div class="csv-header-content">
                                            <span class="csv-header-text">${this.escapeHtml(header)}</span>
                                            <span class="csv-sort-indicator">⇅</span>
                                        </div>
                                    </th>
                                `).join('')}
                            </tr>
                        </thead>
                        <tbody class="csv-table-body">
                            ${data.map((row, index) => `
                                <tr class="csv-table-row" data-row-index="${index}">
                                    ${headers.map(header => `
                                        <td class="csv-table-cell" data-column="${header}">
                                            ${this.formatCellValue(row[header], header)}
                                        </td>
                                    `).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <div class="csv-table-footer">
                    <div class="csv-pagination">
                        <button class="csv-page-btn csv-prev-btn" data-table="${tableId}">‹ Previous</button>
                        <span class="csv-page-info">Page 1 of 1</span>
                        <button class="csv-page-btn csv-next-btn" data-table="${tableId}">Next ›</button>
                    </div>
                </div>
            </div>
            
            <script>
                // Initialize CSV table functionality
                (function() {
                    const tableId = '${tableId}';
                    const table = document.getElementById(tableId);
                    if (table && !table.dataset.initialized) {
                        initializeCsvTable(tableId);
                        table.dataset.initialized = 'true';
                    }
                })();
            </script>
        `;
    }

    /**
     * Format cell value based on content type
     * @param {string} value - Cell value
     * @param {string} header - Column header
     * @returns {string} - Formatted HTML
     */
    formatCellValue(value, header) {
        if (!value) return '<span class="csv-empty-cell">—</span>';

        const trimmedValue = value.toString().trim();
        
        // URL detection
        if (this.isUrl(trimmedValue)) {
            return `<a href="${trimmedValue}" class="csv-link" target="_blank" rel="noopener">
                ${this.escapeHtml(trimmedValue)}
                <span class="csv-link-icon">↗</span>
            </a>`;
        }

        // Email detection
        if (this.isEmail(trimmedValue)) {
            return `<a href="mailto:${trimmedValue}" class="csv-email">
                ${this.escapeHtml(trimmedValue)}
            </a>`;
        }

        // Number detection
        if (this.isNumber(trimmedValue)) {
            return `<span class="csv-number">${this.escapeHtml(trimmedValue)}</span>`;
        }

        // Boolean detection
        if (this.isBoolean(trimmedValue)) {
            const boolValue = trimmedValue.toLowerCase() === 'true';
            return `<span class="csv-boolean csv-boolean-${boolValue}">
                ${boolValue ? '✓' : '✗'} ${this.escapeHtml(trimmedValue)}
            </span>`;
        }

        // Code detection (simple heuristic)
        if (this.isCode(trimmedValue)) {
            return `<code class="csv-code">${this.escapeHtml(trimmedValue)}</code>`;
        }

        // Default text
        return `<span class="csv-text">${this.escapeHtml(trimmedValue)}</span>`;
    }

    /**
     * Helper methods for value type detection
     */
    isUrl(value) {
        try {
            new URL(value);
            return true;
        } catch {
            return /^https?:\/\//.test(value);
        }
    }

    isEmail(value) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
    }

    isNumber(value) {
        return !isNaN(value) && !isNaN(parseFloat(value)) && isFinite(value);
    }

    isBoolean(value) {
        return /^(true|false|yes|no|1|0)$/i.test(value);
    }

    isCode(value) {
        // Simple heuristic for code detection
        return /^[a-zA-Z_$][a-zA-Z0-9_$]*\(.*\)$/.test(value) || // function calls
               /^[A-Z_][A-Z0-9_]*$/.test(value) || // constants
               /^\w+\.\w+/.test(value); // object.property
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Export data as CSV
     * @param {Array} data - Table data
     * @returns {string} - CSV string
     */
    exportToCsv(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]).filter(key => key !== '_rowIndex');
        const csvLines = [];

        // Add headers
        csvLines.push(headers.map(header => this.escapeCsvValue(header)).join(this.options.delimiter));

        // Add data rows
        data.forEach(row => {
            const values = headers.map(header => this.escapeCsvValue(row[header] || ''));
            csvLines.push(values.join(this.options.delimiter));
        });

        return csvLines.join('\n');
    }

    /**
     * Escape CSV value for export
     * @param {string} value - Value to escape
     * @returns {string} - Escaped value
     */
    escapeCsvValue(value) {
        const stringValue = value.toString();
        if (stringValue.includes(this.options.delimiter) || 
            stringValue.includes('\n') || 
            stringValue.includes(this.options.escapeChar)) {
            return `${this.options.escapeChar}${stringValue.replace(
                new RegExp(this.options.escapeChar, 'g'), 
                this.options.escapeChar + this.options.escapeChar
            )}${this.options.escapeChar}`;
        }
        return stringValue;
    }
}

// Export for use in the main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CsvParser;
} else {
    window.CsvParser = CsvParser;
}
