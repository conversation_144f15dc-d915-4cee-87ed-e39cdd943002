/**
 * Enhanced JSO<PERSON> Parser for Workspace Knowledge Toolkit
 * Provides interactive JSON visualization with collapsible trees and search
 */

class JsonParser {
    constructor(options = {}) {
        this.options = {
            maxDepth: 10,
            showTypes: true,
            collapsible: true,
            searchable: true,
            ...options
        };
    }

    /**
     * Parse JSON text to interactive HTML
     * @param {string} text - Raw JSON text
     * @param {string} sourceId - Source document ID
     * @returns {string} - Interactive HTML representation
     */
    parse(text, sourceId = '') {
        if (!text) return '';

        try {
            const data = JSON.parse(text);
            return this.createInteractiveJson(data, sourceId);
        } catch (error) {
            return `<div class="json-error">
                <h4>JSON Parse Error</h4>
                <p>${this.escapeHtml(error.message)}</p>
                <pre class="json-error-text">${this.escapeHtml(text.substring(0, 500))}...</pre>
            </div>`;
        }
    }

    /**
     * Create interactive JSON visualization
     * @param {any} data - Parsed JSON data
     * @param {string} sourceId - Source document ID
     * @returns {string} - Interactive HTML
     */
    createInteractiveJson(data, sourceId) {
        const containerId = `json-container-${sourceId}-${Date.now()}`;
        
        return `
            <div class="json-container" id="${containerId}" data-source="${sourceId}">
                <div class="json-controls">
                    <div class="json-search-container">
                        <input type="text" 
                               class="json-search-input" 
                               placeholder="Search in JSON..." 
                               data-container="${containerId}">
                        <button class="json-search-clear" data-container="${containerId}">✕</button>
                    </div>
                    <div class="json-actions">
                        <button class="json-expand-all" data-container="${containerId}" title="Expand All">⊞</button>
                        <button class="json-collapse-all" data-container="${containerId}" title="Collapse All">⊟</button>
                        <button class="json-copy" data-container="${containerId}" title="Copy JSON">📋</button>
                        <button class="json-download" data-container="${containerId}" title="Download JSON">📥</button>
                    </div>
                </div>
                
                <div class="json-content">
                    ${this.renderJsonNode(data, '', 0)}
                </div>
                
                <div class="json-footer">
                    <span class="json-stats">${this.getJsonStats(data)}</span>
                </div>
            </div>
            
            <script>
                // Initialize JSON functionality
                (function() {
                    const containerId = '${containerId}';
                    const container = document.getElementById(containerId);
                    if (container && !container.dataset.initialized) {
                        initializeJsonViewer(containerId);
                        container.dataset.initialized = 'true';
                    }
                })();
            </script>
        `;
    }

    /**
     * Render a JSON node (recursive)
     * @param {any} value - JSON value
     * @param {string} key - Object key
     * @param {number} depth - Current depth
     * @returns {string} - HTML representation
     */
    renderJsonNode(value, key = '', depth = 0) {
        const type = this.getValueType(value);
        const hasKey = key !== '';
        const keyHtml = hasKey ? `<span class="json-key">"${this.escapeHtml(key)}"</span>: ` : '';
        
        if (depth > this.options.maxDepth) {
            return `<div class="json-node json-max-depth">
                ${keyHtml}<span class="json-value json-string">"[Max depth reached]"</span>
            </div>`;
        }

        switch (type) {
            case 'object':
                return this.renderObject(value, key, depth);
            case 'array':
                return this.renderArray(value, key, depth);
            case 'string':
                return this.renderPrimitive(value, key, type, depth);
            case 'number':
            case 'boolean':
            case 'null':
                return this.renderPrimitive(value, key, type, depth);
            default:
                return this.renderPrimitive(value, key, 'unknown', depth);
        }
    }

    /**
     * Render object node
     */
    renderObject(obj, key, depth) {
        const keys = Object.keys(obj);
        const hasKey = key !== '';
        const keyHtml = hasKey ? `<span class="json-key">"${this.escapeHtml(key)}"</span>: ` : '';
        const isEmpty = keys.length === 0;
        const nodeId = `json-node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        if (isEmpty) {
            return `<div class="json-node json-object json-empty">
                ${keyHtml}<span class="json-brackets">{}</span>
                ${this.options.showTypes ? `<span class="json-type">object</span>` : ''}
            </div>`;
        }

        const isCollapsible = this.options.collapsible && depth > 0;
        const collapseClass = isCollapsible ? 'json-collapsible' : '';
        const toggleHtml = isCollapsible ? 
            `<button class="json-toggle" data-target="${nodeId}">▼</button>` : '';

        return `<div class="json-node json-object ${collapseClass}">
            <div class="json-node-header">
                ${toggleHtml}
                ${keyHtml}
                <span class="json-bracket json-bracket-open">{</span>
                <span class="json-count">${keys.length} ${keys.length === 1 ? 'property' : 'properties'}</span>
                ${this.options.showTypes ? `<span class="json-type">object</span>` : ''}
            </div>
            <div class="json-node-content" id="${nodeId}">
                ${keys.map((objKey, index) => `
                    <div class="json-property" data-key="${objKey}">
                        ${this.renderJsonNode(obj[objKey], objKey, depth + 1)}
                        ${index < keys.length - 1 ? '<span class="json-comma">,</span>' : ''}
                    </div>
                `).join('')}
            </div>
            <div class="json-node-footer">
                <span class="json-bracket json-bracket-close">}</span>
            </div>
        </div>`;
    }

    /**
     * Render array node
     */
    renderArray(arr, key, depth) {
        const hasKey = key !== '';
        const keyHtml = hasKey ? `<span class="json-key">"${this.escapeHtml(key)}"</span>: ` : '';
        const isEmpty = arr.length === 0;
        const nodeId = `json-node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        if (isEmpty) {
            return `<div class="json-node json-array json-empty">
                ${keyHtml}<span class="json-brackets">[]</span>
                ${this.options.showTypes ? `<span class="json-type">array</span>` : ''}
            </div>`;
        }

        const isCollapsible = this.options.collapsible && depth > 0;
        const collapseClass = isCollapsible ? 'json-collapsible' : '';
        const toggleHtml = isCollapsible ? 
            `<button class="json-toggle" data-target="${nodeId}">▼</button>` : '';

        return `<div class="json-node json-array ${collapseClass}">
            <div class="json-node-header">
                ${toggleHtml}
                ${keyHtml}
                <span class="json-bracket json-bracket-open">[</span>
                <span class="json-count">${arr.length} ${arr.length === 1 ? 'item' : 'items'}</span>
                ${this.options.showTypes ? `<span class="json-type">array</span>` : ''}
            </div>
            <div class="json-node-content" id="${nodeId}">
                ${arr.map((item, index) => `
                    <div class="json-array-item" data-index="${index}">
                        <span class="json-array-index">[${index}]</span>
                        ${this.renderJsonNode(item, '', depth + 1)}
                        ${index < arr.length - 1 ? '<span class="json-comma">,</span>' : ''}
                    </div>
                `).join('')}
            </div>
            <div class="json-node-footer">
                <span class="json-bracket json-bracket-close">]</span>
            </div>
        </div>`;
    }

    /**
     * Render primitive value
     */
    renderPrimitive(value, key, type, depth) {
        const hasKey = key !== '';
        const keyHtml = hasKey ? `<span class="json-key">"${this.escapeHtml(key)}"</span>: ` : '';
        
        let valueHtml = '';
        let additionalClasses = '';

        switch (type) {
            case 'string':
                // Check for special string types
                if (this.isUrl(value)) {
                    valueHtml = `<a href="${value}" class="json-link" target="_blank" rel="noopener">
                        "${this.escapeHtml(value)}"
                    </a>`;
                    additionalClasses = 'json-url';
                } else if (this.isEmail(value)) {
                    valueHtml = `<a href="mailto:${value}" class="json-email">
                        "${this.escapeHtml(value)}"
                    </a>`;
                    additionalClasses = 'json-email-value';
                } else if (this.isDate(value)) {
                    valueHtml = `<span class="json-date" title="${new Date(value).toLocaleString()}">
                        "${this.escapeHtml(value)}"
                    </span>`;
                    additionalClasses = 'json-date-value';
                } else {
                    valueHtml = `"${this.escapeHtml(value)}"`;
                }
                break;
            case 'number':
                valueHtml = `<span class="json-number">${value}</span>`;
                break;
            case 'boolean':
                valueHtml = `<span class="json-boolean json-boolean-${value}">${value}</span>`;
                break;
            case 'null':
                valueHtml = `<span class="json-null">null</span>`;
                break;
            default:
                valueHtml = `<span class="json-unknown">${this.escapeHtml(String(value))}</span>`;
        }

        return `<div class="json-node json-primitive json-${type} ${additionalClasses}">
            ${keyHtml}
            <span class="json-value">${valueHtml}</span>
            ${this.options.showTypes ? `<span class="json-type">${type}</span>` : ''}
        </div>`;
    }

    /**
     * Get value type
     */
    getValueType(value) {
        if (value === null) return 'null';
        if (Array.isArray(value)) return 'array';
        return typeof value;
    }

    /**
     * Get JSON statistics
     */
    getJsonStats(data) {
        const stats = this.analyzeJson(data);
        return `${stats.totalNodes} nodes, ${stats.maxDepth} levels deep, ${stats.totalSize} bytes`;
    }

    /**
     * Analyze JSON structure
     */
    analyzeJson(data, depth = 0) {
        let totalNodes = 1;
        let maxDepth = depth;
        let totalSize = JSON.stringify(data).length;

        if (Array.isArray(data)) {
            data.forEach(item => {
                const childStats = this.analyzeJson(item, depth + 1);
                totalNodes += childStats.totalNodes;
                maxDepth = Math.max(maxDepth, childStats.maxDepth);
            });
        } else if (typeof data === 'object' && data !== null) {
            Object.values(data).forEach(value => {
                const childStats = this.analyzeJson(value, depth + 1);
                totalNodes += childStats.totalNodes;
                maxDepth = Math.max(maxDepth, childStats.maxDepth);
            });
        }

        return { totalNodes, maxDepth, totalSize };
    }

    /**
     * Helper methods for value detection
     */
    isUrl(value) {
        try {
            new URL(value);
            return true;
        } catch {
            return /^https?:\/\//.test(value);
        }
    }

    isEmail(value) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
    }

    isDate(value) {
        return !isNaN(Date.parse(value)) && /^\d{4}-\d{2}-\d{2}/.test(value);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Export for use in the main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = JsonParser;
} else {
    window.JsonParser = JsonParser;
}
