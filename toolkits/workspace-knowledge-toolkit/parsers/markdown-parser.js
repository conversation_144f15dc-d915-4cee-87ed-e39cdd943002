/**
 * Enhanced Markdown Parser for Workspace Knowledge Toolkit
 * Extends basic markdown parsing with cross-reference linking and enhanced formatting
 */

class MarkdownParser {
    constructor(crossReferences = {}) {
        this.crossReferences = crossReferences;
        this.linkPatterns = crossReferences.linkPatterns || {};
    }

    /**
     * Parse markdown text to HTML with enhanced features
     * @param {string} text - Raw markdown text
     * @param {string} sourceId - Source document ID for cross-referencing
     * @returns {string} - Parsed HTML
     */
    parse(text, sourceId = '') {
        if (!text) return '';

        let html = text;

        // Enhanced heading parsing with anchor links
        html = this.parseHeadings(html);
        
        // Code blocks with syntax highlighting hints
        html = this.parseCodeBlocks(html);
        
        // Enhanced link parsing with cross-references
        html = this.parseLinks(html, sourceId);
        
        // Text formatting
        html = this.parseTextFormatting(html);
        
        // Lists and tables
        html = this.parseLists(html);
        html = this.parseTables(html);
        
        // Special workspace-specific patterns
        html = this.parseWorkspacePatterns(html);
        
        // Convert to paragraphs
        html = this.convertToParagraphs(html);

        return html;
    }

    /**
     * Parse headings with anchor links for navigation
     */
    parseHeadings(text) {
        // H1 headings
        text = text.replace(/^# (.*$)/gim, (match, content) => {
            const id = this.createAnchorId(content);
            return `<h1 id="${id}" class="heading-1">
                <a href="#${id}" class="heading-anchor">#</a>
                ${content}
            </h1>`;
        });

        // H2 headings
        text = text.replace(/^## (.*$)/gim, (match, content) => {
            const id = this.createAnchorId(content);
            return `<h2 id="${id}" class="heading-2">
                <a href="#${id}" class="heading-anchor">#</a>
                ${content}
            </h2>`;
        });

        // H3 headings
        text = text.replace(/^### (.*$)/gim, (match, content) => {
            const id = this.createAnchorId(content);
            return `<h3 id="${id}" class="heading-3">
                <a href="#${id}" class="heading-anchor">#</a>
                ${content}
            </h3>`;
        });

        // H4-H6 headings
        text = text.replace(/^#### (.*$)/gim, '<h4 class="heading-4">$1</h4>');
        text = text.replace(/^##### (.*$)/gim, '<h5 class="heading-5">$1</h5>');
        text = text.replace(/^###### (.*$)/gim, '<h6 class="heading-6">$1</h6>');

        return text;
    }

    /**
     * Parse code blocks with language detection
     */
    parseCodeBlocks(text) {
        // Fenced code blocks with language
        text = text.replace(/```(\w+)?\n?([\s\S]*?)```/gim, (match, lang, code) => {
            const language = lang || 'text';
            return `<pre class="code-block" data-language="${language}">
                <div class="code-header">
                    <span class="code-language">${language}</span>
                    <button class="copy-code" title="Copy code">📋</button>
                </div>
                <code class="language-${language}">${this.escapeHtml(code.trim())}</code>
            </pre>`;
        });

        // Inline code
        text = text.replace(/`([^`]+)`/gim, '<code class="inline-code">$1</code>');

        return text;
    }

    /**
     * Enhanced link parsing with cross-reference detection
     */
    parseLinks(text, sourceId) {
        // Markdown links [text](url)
        text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/gim, (match, linkText, url) => {
            // Check if it's a cross-reference
            const crossRef = this.findCrossReference(url, sourceId);
            if (crossRef) {
                return `<a href="${crossRef.url}" class="cross-reference" 
                          data-type="${crossRef.type}" 
                          title="${crossRef.description}">
                    ${linkText}
                    <span class="cross-ref-icon">🔗</span>
                </a>`;
            }

            // External links
            if (url.startsWith('http')) {
                return `<a href="${url}" class="external-link" target="_blank" rel="noopener">
                    ${linkText}
                    <span class="external-icon">↗</span>
                </a>`;
            }

            // Internal links
            return `<a href="${url}" class="internal-link">${linkText}</a>`;
        });

        return text;
    }

    /**
     * Parse text formatting (bold, italic, etc.)
     */
    parseTextFormatting(text) {
        // Bold text
        text = text.replace(/\*\*(.*?)\*\*/gim, '<strong class="bold">$1</strong>');
        
        // Italic text
        text = text.replace(/\*(.*?)\*/gim, '<em class="italic">$1</em>');
        
        // Strikethrough
        text = text.replace(/~~(.*?)~~/gim, '<del class="strikethrough">$1</del>');
        
        // Highlight
        text = text.replace(/==(.*?)==/gim, '<mark class="highlight">$1</mark>');

        return text;
    }

    /**
     * Parse lists (ordered and unordered)
     */
    parseLists(text) {
        // Unordered lists
        text = text.replace(/^[\s]*[-*+]\s+(.+)$/gim, '<li class="list-item">$1</li>');
        text = text.replace(/(<li class="list-item">.*<\/li>)/gims, '<ul class="unordered-list">$1</ul>');

        // Ordered lists
        text = text.replace(/^[\s]*\d+\.\s+(.+)$/gim, '<li class="list-item-ordered">$1</li>');
        text = text.replace(/(<li class="list-item-ordered">.*<\/li>)/gims, '<ol class="ordered-list">$1</ol>');

        return text;
    }

    /**
     * Parse tables
     */
    parseTables(text) {
        // Simple table parsing (basic implementation)
        const tableRegex = /\|(.+)\|\n\|[-\s|]+\|\n((?:\|.+\|\n?)*)/gim;
        
        text = text.replace(tableRegex, (match, header, rows) => {
            const headerCells = header.split('|').map(cell => 
                `<th class="table-header">${cell.trim()}</th>`
            ).join('');
            
            const rowsHtml = rows.trim().split('\n').map(row => {
                const cells = row.split('|').map(cell => 
                    `<td class="table-cell">${cell.trim()}</td>`
                ).join('');
                return `<tr class="table-row">${cells}</tr>`;
            }).join('');

            return `<table class="markdown-table">
                <thead><tr class="table-header-row">${headerCells}</tr></thead>
                <tbody>${rowsHtml}</tbody>
            </table>`;
        });

        return text;
    }

    /**
     * Parse workspace-specific patterns (badges, alerts, etc.)
     */
    parseWorkspacePatterns(text) {
        // Badges [![text](url)](link)
        text = text.replace(/\[\!\[([^\]]+)\]\(([^)]+)\)\]\(([^)]+)\)/gim, 
            '<span class="badge" title="$1"><img src="$2" alt="$1"></span>');

        // Alerts/callouts > **Note**: content
        text = text.replace(/^>\s*\*\*(Note|Warning|Info|Tip)\*\*:\s*(.+)$/gim, 
            '<div class="alert alert-$1"><strong>$1:</strong> $2</div>');

        // Emoji shortcuts
        text = text.replace(/:([a-z_]+):/gim, (match, emoji) => {
            const emojiMap = {
                'rocket': '🚀',
                'book': '📚',
                'gear': '⚙️',
                'star': '⭐',
                'warning': '⚠️',
                'info': 'ℹ️',
                'tip': '💡'
            };
            return emojiMap[emoji] || match;
        });

        return text;
    }

    /**
     * Convert remaining text to paragraphs
     */
    convertToParagraphs(text) {
        const lines = text.split('\n');
        const paragraphs = [];
        let currentParagraph = '';

        for (const line of lines) {
            const trimmedLine = line.trim();
            
            // Skip empty lines and already processed HTML
            if (!trimmedLine || trimmedLine.startsWith('<')) {
                if (currentParagraph) {
                    paragraphs.push(`<p class="paragraph">${currentParagraph.trim()}</p>`);
                    currentParagraph = '';
                }
                if (trimmedLine.startsWith('<')) {
                    paragraphs.push(trimmedLine);
                }
                continue;
            }

            currentParagraph += (currentParagraph ? ' ' : '') + trimmedLine;
        }

        if (currentParagraph) {
            paragraphs.push(`<p class="paragraph">${currentParagraph.trim()}</p>`);
        }

        return paragraphs.join('\n');
    }

    /**
     * Helper methods
     */
    createAnchorId(text) {
        return text.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    findCrossReference(url, sourceId) {
        // Implementation would check cross-references data
        // This is a simplified version
        if (this.crossReferences.crossReferences && this.crossReferences.crossReferences[sourceId]) {
            const refs = this.crossReferences.crossReferences[sourceId].references;
            const ref = refs.find(r => url.includes(r.target));
            if (ref) {
                return {
                    url: url,
                    type: ref.type,
                    description: ref.description
                };
            }
        }
        return null;
    }
}

// Export for use in the main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MarkdownParser;
} else {
    window.MarkdownParser = MarkdownParser;
}
