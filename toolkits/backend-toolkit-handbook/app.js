// Backend Developer Toolkit Application
class BackendToolkitApp {
    constructor() {
        this.data = {
            "skillCategories": [
                {
                    "id": "architecture",
                    "title": "Kiến Trúc & <PERSON>ân Tích Hệ <PERSON>ống",
                    "icon": "🏗️",
                    "skills": [
                        {
                            "id": "software-archaeology",
                            "title": "Software Archaeology & Reverse Engineering",
                            "difficulty": "advanced",
                            "description": "Kỹ năng khám phá và hiểu code legacy thông qua phân tích tĩnh và động",
                            "examples": [
                                {
                                    "title": "Phân tích codebase chưa có documentation",
                                    "code": "// Bước 1: Tạo dependency graph\nconst fs = require('fs');\nconst path = require('path');\n\nfunction analyzeCodebase(rootDir) {\n  const dependencies = new Map();\n  const files = getAllFiles(rootDir, '.js');\n  \n  files.forEach(file => {\n    const content = fs.readFileSync(file, 'utf8');\n    const imports = extractImports(content);\n    const exports = extractExports(content);\n    \n    dependencies.set(file, {\n      imports,\n      exports,\n      functions: extractFunctions(content),\n      complexity: calculateComplexity(content)\n    });\n  });\n  \n  return generateDocumentation(dependencies);\n}",
                                    "language": "javascript"
                                },
                                {
                                    "title": "Legacy Database Schema Analysis",
                                    "code": "-- Query để phân tích relationships\nSELECT \n  tc.table_name,\n  kcu.column_name,\n  ccu.table_name AS foreign_table_name,\n  ccu.column_name AS foreign_column_name\nFROM information_schema.table_constraints AS tc\nJOIN information_schema.key_column_usage AS kcu\n  ON tc.constraint_name = kcu.constraint_name\nJOIN information_schema.constraint_column_usage AS ccu\n  ON ccu.constraint_name = tc.constraint_name\nWHERE tc.constraint_type = 'FOREIGN KEY'\nORDER BY tc.table_name;",
                                    "language": "sql"
                                }
                            ],
                            "tools": ["SonarQube", "CodeClimate", "Understand", "Doxygen"],
                            "bestPractices": [
                                "Bắt đầu từ entry points (main files, routes)",
                                "Vẽ dependency graph trước khi đọc code",
                                "Tìm hiểu business logic qua test cases",
                                "Document findings ngay trong quá trình phân tích"
                            ]
                        },
                        {
                            "id": "microservices",
                            "title": "Microservices Architecture",
                            "difficulty": "advanced",
                            "description": "Thiết kế và implement hệ thống microservices scalable",
                            "examples": [
                                {
                                    "title": "API Gateway với Circuit Breaker",
                                    "code": "const express = require('express');\nconst CircuitBreaker = require('opossum');\n\n// Circuit Breaker configuration\nconst options = {\n  timeout: 3000,\n  errorThresholdPercentage: 50,\n  resetTimeout: 30000\n};\n\n// Service calls with circuit breaker\nconst userServiceCall = new CircuitBreaker(callUserService, options);\nconst orderServiceCall = new CircuitBreaker(callOrderService, options);\n\n// API Gateway\nconst app = express();\n\napp.get('/api/user/:id', async (req, res) => {\n  try {\n    const user = await userServiceCall.fire(req.params.id);\n    res.json(user);\n  } catch (error) {\n    res.status(503).json({ error: 'User service unavailable' });\n  }\n});\n\n// Fallback responses\nuserServiceCall.fallback(() => ({ id: null, name: 'Guest User' }));",
                                    "language": "javascript"
                                }
                            ],
                            "patterns": [
                                "Circuit Breaker",
                                "API Gateway",
                                "Service Mesh",
                                "Database per Service",
                                "Saga Pattern",
                                "Event Sourcing",
                                "CQRS"
                            ],
                            "tools": ["Kubernetes", "Docker", "Consul", "Istio", "Kong", "Zuul"]
                        }
                    ]
                },
                {
                    "id": "debugging",
                    "title": "Debugging & Troubleshooting",
                    "icon": "🔍",
                    "skills": [
                        {
                            "id": "systematic-debugging",
                            "title": "Systematic Debugging",
                            "difficulty": "intermediate",
                            "description": "Phương pháp debug có hệ thống từ development đến production",
                            "examples": [
                                {
                                    "title": "Structured Logging with Winston",
                                    "code": "const winston = require('winston');\nconst { v4: uuidv4 } = require('uuid');\n\n// Logger configuration\nconst logger = winston.createLogger({\n  level: 'info',\n  format: winston.format.combine(\n    winston.format.timestamp(),\n    winston.format.errors({ stack: true }),\n    winston.format.json()\n  ),\n  transports: [\n    new winston.transports.File({ filename: 'error.log', level: 'error' }),\n    new winston.transports.File({ filename: 'combined.log' })\n  ]\n});\n\n// Request correlation middleware\nfunction correlationMiddleware(req, res, next) {\n  req.correlationId = uuidv4();\n  res.setHeader('X-Correlation-ID', req.correlationId);\n  \n  logger.info('Request started', {\n    correlationId: req.correlationId,\n    method: req.method,\n    url: req.url,\n    userAgent: req.get('User-Agent')\n  });\n  \n  next();\n}",
                                    "language": "javascript"
                                }
                            ],
                            "techniques": [
                                "Binary Search Debugging",
                                "Rubber Duck Debugging", 
                                "Breakpoint Analysis",
                                "Log Correlation",
                                "Performance Profiling",
                                "Memory Leak Detection"
                            ],
                            "tools": ["Winston", "Morgan", "Debug", "Node Inspector", "Chrome DevTools"]
                        }
                    ]
                },
                {
                    "id": "database",
                    "title": "Database & Data Management",
                    "icon": "🗄️",
                    "skills": [
                        {
                            "id": "zero-downtime-migration",
                            "title": "Zero-Downtime Migration",
                            "difficulty": "expert",
                            "description": "Thực hiện database migration không gây gián đoạn service",
                            "examples": [
                                {
                                    "title": "Blue-Green Database Migration",
                                    "code": "-- Bước 1: Tạo database mới (Green)\nCREATE DATABASE app_db_green;\n\n-- Bước 2: Copy schema và data\npg_dump app_db_blue | psql app_db_green\n\n-- Bước 3: Setup replication\n-- Master-Slave replication script\n#!/bin/bash\n\n# Enable WAL archiving on Blue (current)\npsql app_db_blue -c \"ALTER SYSTEM SET wal_level = replica;\"\npsql app_db_blue -c \"ALTER SYSTEM SET archive_mode = on;\"\n\n# Create replication user\npsql app_db_blue -c \"CREATE USER replicator REPLICATION LOGIN PASSWORD 'replica_pass';\"\n\n# Switch application connection\necho \"Switching to Green database...\"\nsystemctl reload nginx",
                                    "language": "bash"
                                }
                            ],
                            "strategies": [
                                "Blue-Green Deployment",
                                "Canary Releases", 
                                "Shadow Migration",
                                "Ghost Table Approach",
                                "Read Replica Promotion"
                            ],
                            "tools": ["gh-ost", "pt-online-schema-change", "Flyway", "Liquibase"]
                        }
                    ]
                },
                {
                    "id": "performance",
                    "title": "Performance Optimization",
                    "icon": "⚡",
                    "skills": [
                        {
                            "id": "caching-strategies",
                            "title": "Caching Strategies",
                            "difficulty": "intermediate",
                            "description": "Implement multi-layer caching để tối ưu performance",
                            "examples": [
                                {
                                    "title": "Redis Caching với Fallback Strategy",
                                    "code": "const redis = require('redis');\nconst client = redis.createClient();\n\nclass CacheManager {\n  constructor() {\n    this.redis = client;\n    this.localCache = new Map();\n    this.defaultTTL = 3600; // 1 hour\n  }\n\n  async get(key, fallbackFn, options = {}) {\n    const { ttl = this.defaultTTL, useLocalCache = true } = options;\n    \n    try {\n      // Layer 1: Local memory cache\n      if (useLocalCache && this.localCache.has(key)) {\n        const cached = this.localCache.get(key);\n        if (cached.expires > Date.now()) {\n          return cached.data;\n        }\n        this.localCache.delete(key);\n      }\n      \n      // Layer 2: Redis cache\n      const redisData = await this.redis.get(key);\n      if (redisData) {\n        const parsed = JSON.parse(redisData);\n        return parsed;\n      }\n      \n      // Layer 3: Fallback to original source\n      const freshData = await fallbackFn();\n      await this.set(key, freshData, { ttl, useLocalCache });\n      return freshData;\n      \n    } catch (error) {\n      console.error(`Cache error for key ${key}:`, error);\n      return await fallbackFn();\n    }\n  }\n}",
                                    "language": "javascript"
                                }
                            ],
                            "patterns": [
                                "Cache-Aside",
                                "Write-Through",
                                "Write-Behind", 
                                "Cache-First",
                                "Refresh-Ahead"
                            ],
                            "metrics": [
                                "Cache Hit Ratio (>90% good)",
                                "Cache Miss Latency (<100ms)",
                                "Memory Usage",
                                "Eviction Rate"
                            ]
                        }
                    ]
                },
                {
                    "id": "security",
                    "title": "Security & Authentication",
                    "icon": "🔐",
                    "skills": [
                        {
                            "id": "auth-authorization",
                            "title": "Authentication & Authorization",
                            "difficulty": "advanced", 
                            "description": "Implement secure authentication với JWT và OAuth 2.0",
                            "examples": [
                                {
                                    "title": "JWT với Refresh Token Strategy",
                                    "code": "const jwt = require('jsonwebtoken');\nconst bcrypt = require('bcrypt');\nconst crypto = require('crypto');\n\nclass AuthService {\n  constructor() {\n    this.accessTokenSecret = process.env.ACCESS_TOKEN_SECRET;\n    this.refreshTokenSecret = process.env.REFRESH_TOKEN_SECRET;\n    this.accessTokenExpiry = '15m';\n    this.refreshTokenExpiry = '7d';\n    this.refreshTokens = new Set();\n  }\n  \n  async login(email, password) {\n    const user = await User.findByEmail(email);\n    if (!user || !await bcrypt.compare(password, user.passwordHash)) {\n      throw new Error('Invalid credentials');\n    }\n    \n    const tokenPair = this.generateTokenPair(user);\n    this.refreshTokens.add(tokenPair.refreshToken);\n    \n    return {\n      accessToken: tokenPair.accessToken,\n      refreshToken: tokenPair.refreshToken,\n      expiresIn: 900,\n      user: {\n        id: user.id,\n        email: user.email,\n        roles: user.roles\n      }\n    };\n  }\n}",
                                    "language": "javascript"
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "monitoring",
                    "title": "Monitoring & Observability", 
                    "icon": "📊",
                    "skills": [
                        {
                            "id": "observability-setup",
                            "title": "Three Pillars of Observability",
                            "difficulty": "advanced",
                            "description": "Setup comprehensive monitoring với logs, metrics, và traces",
                            "examples": [
                                {
                                    "title": "Distributed Tracing với OpenTelemetry",
                                    "code": "const { NodeSDK } = require('@opentelemetry/sdk-node');\nconst { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');\nconst { JaegerExporter } = require('@opentelemetry/exporter-jaeger');\n\n// Tracing setup\nconst jaegerExporter = new JaegerExporter({\n  endpoint: 'http://localhost:14268/api/traces',\n  serviceName: 'user-service',\n  serviceVersion: '1.0.0'\n});\n\nconst sdk = new NodeSDK({\n  traceExporter: jaegerExporter,\n  instrumentations: [getNodeAutoInstrumentations()],\n  serviceName: 'user-service'\n});\n\nsdk.start();\n\n// Custom metrics\nconst requestCounter = meter.createCounter('http_requests_total');\nconst responseTime = meter.createHistogram('http_request_duration_seconds');\nconst activeConnections = meter.createUpDownCounter('active_connections');",
                                    "language": "javascript"
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        this.currentView = 'welcome';
        this.currentSkill = null;
        this.currentCategory = null;
        this.completedSkills = new Set();
        this.bookmarkedSkills = new Set();
        this.isDarkMode = false;
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeApp();
            });
        } else {
            this.initializeApp();
        }
    }

    initializeApp() {
        this.setupEventListeners();
        this.renderSidebar();
        this.updateProgress();
        this.applyTheme();
        this.showWelcome(); // Show welcome screen by default
    }

    setupEventListeners() {
        // Theme toggle removed - using light theme only per project specification

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
            
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.handleSearch(e.target.value);
                }
            });
        }
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                const searchValue = searchInput ? searchInput.value : '';
                this.handleSearch(searchValue);
            });
        }

        // Progress modal
        const progressBtn = document.getElementById('progressBtn');
        if (progressBtn) {
            progressBtn.addEventListener('click', () => {
                this.showProgressModal();
            });
        }

        // Modal close
        const modalClose = document.getElementById('modalClose');
        const modalBackdrop = document.getElementById('modalBackdrop');
        
        if (modalClose) {
            modalClose.addEventListener('click', () => {
                this.hideModal();
            });
        }
        
        if (modalBackdrop) {
            modalBackdrop.addEventListener('click', () => {
                this.hideModal();
            });
        }

        // Export functionality
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportProgress();
            });
        }

        // Bookmark functionality
        const bookmarkBtn = document.getElementById('bookmarkBtn');
        if (bookmarkBtn) {
            bookmarkBtn.addEventListener('click', () => {
                this.toggleBookmark();
            });
        }

        // Toast close
        const toastClose = document.getElementById('toastClose');
        if (toastClose) {
            toastClose.addEventListener('click', () => {
                this.hideToast();
            });
        }

        // Header title click to go home
        const headerTitle = document.querySelector('.header__title');
        if (headerTitle) {
            headerTitle.addEventListener('click', () => {
                this.showWelcome();
            });
            headerTitle.style.cursor = 'pointer';
        }

        // Search overlay close
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container') && !e.target.closest('.search-overlay')) {
                this.hideSearchOverlay();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'k':
                        e.preventDefault();
                        if (searchInput) searchInput.focus();
                        break;
                    case 's':
                        e.preventDefault();
                        this.exportProgress();
                        break;
                }
            }
            
            if (e.key === 'Escape') {
                this.hideModal();
                this.hideSearchOverlay();
            }
        });
    }

    renderSidebar() {
        const nav = document.getElementById('sidebarNav');
        if (!nav) return;
        
        nav.innerHTML = '';

        this.data.skillCategories.forEach(category => {
            const categoryEl = document.createElement('div');
            categoryEl.className = 'nav-category';
            
            const headerEl = document.createElement('div');
            headerEl.className = 'nav-category__header';
            headerEl.innerHTML = `
                <span class="nav-category__icon">${category.icon}</span>
                <span>${category.title}</span>
                <span class="nav-category__toggle">▶</span>
            `;
            
            const skillsEl = document.createElement('div');
            skillsEl.className = 'nav-category__skills';
            
            category.skills.forEach(skill => {
                const skillEl = document.createElement('div');
                skillEl.className = `nav-skill ${this.completedSkills.has(skill.id) ? 'completed' : ''}`;
                skillEl.innerHTML = `
                    <span>${skill.title}</span>
                    <span class="skill-difficulty difficulty-${skill.difficulty}">${skill.difficulty}</span>
                `;
                
                skillEl.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.showSkill(category, skill);
                });
                
                skillsEl.appendChild(skillEl);
            });

            headerEl.addEventListener('click', () => {
                this.toggleCategory(category.id, headerEl, skillsEl);
            });

            categoryEl.appendChild(headerEl);
            categoryEl.appendChild(skillsEl);
            nav.appendChild(categoryEl);
        });
    }

    toggleCategory(categoryId, headerEl, skillsEl) {
        const isExpanded = skillsEl.classList.contains('expanded');
        
        if (isExpanded) {
            skillsEl.classList.remove('expanded');
            headerEl.querySelector('.nav-category__toggle').style.transform = 'rotate(0deg)';
            headerEl.classList.remove('active');
        } else {
            skillsEl.classList.add('expanded');
            headerEl.querySelector('.nav-category__toggle').style.transform = 'rotate(90deg)';
            headerEl.classList.add('active');
        }
    }

    expandFirstCategory() {
        const firstCategory = document.querySelector('.nav-category__header');
        const firstSkills = document.querySelector('.nav-category__skills');
        if (firstCategory && firstSkills) {
            this.toggleCategory(this.data.skillCategories[0].id, firstCategory, firstSkills);
        }
    }

    showSkill(category, skill) {
        this.currentCategory = category;
        this.currentSkill = skill;
        this.currentView = 'skill';
        
        // Update active states
        document.querySelectorAll('.nav-skill').forEach(el => el.classList.remove('active'));
        document.querySelectorAll('.nav-skill').forEach(el => {
            if (el.querySelector('span').textContent === skill.title) {
                el.classList.add('active');
            }
        });
        
        this.renderSkillDetail();
        this.updateBreadcrumb();
    }

    renderSkillDetail() {
        const contentBody = document.getElementById('contentBody');
        if (!contentBody) return;
        
        contentBody.innerHTML = `
            <div class="skill-detail">
                <div class="skill-header">
                    <div class="skill-title">
                        <h1>${this.currentSkill.title}</h1>
                        <div class="skill-meta">
                            <span class="skill-difficulty difficulty-${this.currentSkill.difficulty}">
                                ${this.currentSkill.difficulty}
                            </span>
                            <div class="completion-toggle">
                                <div class="completion-checkbox ${this.completedSkills.has(this.currentSkill.id) ? 'checked' : ''}" 
                                     data-skill-id="${this.currentSkill.id}">
                                    ${this.completedSkills.has(this.currentSkill.id) ? '✓' : ''}
                                </div>
                                <span>Đã hoàn thành</span>
                            </div>
                        </div>
                        <p>${this.currentSkill.description}</p>
                    </div>
                    <div class="skill-actions">
                        <button class="btn btn--outline btn--sm" id="bookmarkCurrentBtn">
                            ${this.bookmarkedSkills.has(this.currentSkill.id) ? '⭐' : '☆'} Đánh dấu
                        </button>
                    </div>
                </div>

                <div class="skill-tabs">
                    <button class="tab-button active" data-tab="examples">Ví dụ</button>
                    <button class="tab-button" data-tab="practices">Best Practices</button>
                    <button class="tab-button" data-tab="tools">Tools & Resources</button>
                </div>

                <div class="tab-content active" id="examples-tab">
                    ${this.renderExamples()}
                </div>

                <div class="tab-content hidden" id="practices-tab">
                    ${this.renderBestPractices()}
                </div>

                <div class="tab-content hidden" id="tools-tab">
                    ${this.renderTools()}
                </div>
            </div>
        `;

        // Add event listeners for the skill detail elements
        this.setupSkillDetailEventListeners();

        // Apply syntax highlighting
        setTimeout(() => {
            if (window.Prism) {
                Prism.highlightAll();
            }
        }, 100);
    }

    setupSkillDetailEventListeners() {
        // Completion toggle
        const completionCheckbox = document.querySelector('.completion-checkbox');
        if (completionCheckbox) {
            completionCheckbox.addEventListener('click', () => {
                const skillId = completionCheckbox.getAttribute('data-skill-id');
                this.toggleCompletion(skillId);
            });
        }

        // Bookmark button
        const bookmarkCurrentBtn = document.getElementById('bookmarkCurrentBtn');
        if (bookmarkCurrentBtn) {
            bookmarkCurrentBtn.addEventListener('click', () => {
                this.toggleBookmark();
            });
        }

        // Tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                this.switchTab(tabName, button);
            });
        });

        // Copy buttons
        document.querySelectorAll('.copy-btn').forEach((btn, index) => {
            btn.addEventListener('click', () => {
                this.copyCode(index);
            });
        });
    }

    renderExamples() {
        if (!this.currentSkill.examples) return '<p>Chưa có ví dụ cho kỹ năng này.</p>';
        
        return `
            <div class="examples-grid">
                ${this.currentSkill.examples.map((example, index) => `
                    <div class="code-example">
                        <div class="code-example__header">
                            <h3 class="code-example__title">${example.title}</h3>
                            <div class="code-example__actions">
                                <button class="copy-btn" data-index="${index}">📋 Copy</button>
                            </div>
                        </div>
                        <div class="code-example__content">
                            <pre><code class="language-${example.language || 'javascript'}">${this.escapeHtml(example.code)}</code></pre>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    renderBestPractices() {
        const practices = this.currentSkill.bestPractices || this.currentSkill.techniques || [];
        if (!practices.length) return '<p>Chưa có best practices cho kỹ năng này.</p>';
        
        return `
            <div class="best-practices-list">
                ${practices.map(practice => `
                    <div class="practice-item">
                        <span class="practice-icon">✓</span>
                        <span>${practice}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    renderTools() {
        const tools = this.currentSkill.tools || [];
        const patterns = this.currentSkill.patterns || [];
        const metrics = this.currentSkill.metrics || [];
        const strategies = this.currentSkill.strategies || [];
        
        let content = '';
        
        if (tools.length) {
            content += `
                <h3>🛠️ Tools & Libraries</h3>
                <div class="tools-list">
                    ${tools.map(tool => `
                        <div class="tool-item">
                            <span class="practice-icon">🔧</span>
                            <span>${tool}</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        if (patterns.length) {
            content += `
                <h3>📐 Design Patterns</h3>
                <div class="tools-list">
                    ${patterns.map(pattern => `
                        <div class="tool-item">
                            <span class="practice-icon">🎯</span>
                            <span>${pattern}</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        if (strategies.length) {
            content += `
                <h3>⚡ Strategies</h3>
                <div class="tools-list">
                    ${strategies.map(strategy => `
                        <div class="tool-item">
                            <span class="practice-icon">📈</span>
                            <span>${strategy}</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        if (metrics.length) {
            content += `
                <h3>📊 Key Metrics</h3>
                <div class="tools-list">
                    ${metrics.map(metric => `
                        <div class="tool-item">
                            <span class="practice-icon">📈</span>
                            <span>${metric}</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        return content || '<p>Chưa có thông tin tools cho kỹ năng này.</p>';
    }

    switchTab(tabName, clickedButton) {
        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        clickedButton.classList.add('active');
        
        // Update tab content - hide all, then show the selected one
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
            content.classList.add('hidden');
        });
        
        const targetTab = document.getElementById(`${tabName}-tab`);
        if (targetTab) {
            targetTab.classList.add('active');
            targetTab.classList.remove('hidden');
        }
    }

    copyCode(exampleIndex) {
        const code = this.currentSkill.examples[exampleIndex].code;
        navigator.clipboard.writeText(code).then(() => {
            const btn = document.querySelector(`[data-index="${exampleIndex}"]`);
            if (btn) {
                const originalText = btn.textContent;
                btn.textContent = '✓ Copied!';
                btn.classList.add('copied');
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.classList.remove('copied');
                }, 2000);
            }
            
            this.showToast('Code đã được copy vào clipboard!');
        }).catch(err => {
            console.error('Failed to copy code:', err);
            this.showToast('Không thể copy code', 'error');
        });
    }

    toggleCompletion(skillId) {
        if (this.completedSkills.has(skillId)) {
            this.completedSkills.delete(skillId);
        } else {
            this.completedSkills.add(skillId);
        }
        
        this.updateProgress();
        this.renderSidebar();
        
        // Update completion checkbox
        const checkbox = document.querySelector('.completion-checkbox');
        if (checkbox) {
            const isCompleted = this.completedSkills.has(skillId);
            checkbox.classList.toggle('checked', isCompleted);
            checkbox.textContent = isCompleted ? '✓' : '';
        }
        
        this.showToast(this.completedSkills.has(skillId) ? 'Kỹ năng đã được đánh dấu hoàn thành!' : 'Đã bỏ đánh dấu hoàn thành');
    }

    toggleBookmark() {
        if (!this.currentSkill) return;
        
        const skillId = this.currentSkill.id;
        if (this.bookmarkedSkills.has(skillId)) {
            this.bookmarkedSkills.delete(skillId);
        } else {
            this.bookmarkedSkills.add(skillId);
        }
        
        // Update bookmark button
        const btn = document.getElementById('bookmarkCurrentBtn');
        if (btn) {
            btn.innerHTML = this.bookmarkedSkills.has(skillId) ? '⭐ Đánh dấu' : '☆ Đánh dấu';
        }
        
        this.showToast(this.bookmarkedSkills.has(skillId) ? 'Đã bookmark kỹ năng!' : 'Đã bỏ bookmark');
    }

    handleSearch(query) {
        if (!query.trim()) {
            this.hideSearchOverlay();
            return;
        }
        
        const results = this.searchSkills(query);
        this.showSearchResults(results);
    }

    searchSkills(query) {
        const results = [];
        const queryLower = query.toLowerCase();
        
        this.data.skillCategories.forEach(category => {
            category.skills.forEach(skill => {
                let score = 0;
                
                // Title match (highest priority)
                if (skill.title.toLowerCase().includes(queryLower)) {
                    score += 10;
                }
                
                // Description match
                if (skill.description.toLowerCase().includes(queryLower)) {
                    score += 5;
                }
                
                // Examples match
                if (skill.examples) {
                    skill.examples.forEach(example => {
                        if (example.title.toLowerCase().includes(queryLower) || 
                            example.code.toLowerCase().includes(queryLower)) {
                            score += 3;
                        }
                    });
                }
                
                // Tools match
                if (skill.tools && skill.tools.some(tool => tool.toLowerCase().includes(queryLower))) {
                    score += 2;
                }
                
                if (score > 0) {
                    results.push({
                        skill,
                        category,
                        score
                    });
                }
            });
        });
        
        return results.sort((a, b) => b.score - a.score);
    }

    showSearchResults(results) {
        const overlay = document.getElementById('searchOverlay');
        const resultsContainer = document.getElementById('searchResults');
        
        if (!overlay || !resultsContainer) return;
        
        if (results.length === 0) {
            resultsContainer.innerHTML = '<p style="padding: 16px; text-align: center; color: var(--color-text-secondary);">Không tìm thấy kết quả nào.</p>';
        } else {
            resultsContainer.innerHTML = results.map(result => `
                <div class="search-result" data-category-id="${result.category.id}" data-skill-id="${result.skill.id}">
                    <div class="search-result__title">${result.skill.title}</div>
                    <div class="search-result__category">${result.category.icon} ${result.category.title}</div>
                </div>
            `).join('');
            
            // Add event listeners to search results
            resultsContainer.querySelectorAll('.search-result').forEach(resultEl => {
                resultEl.addEventListener('click', () => {
                    const categoryId = resultEl.getAttribute('data-category-id');
                    const skillId = resultEl.getAttribute('data-skill-id');
                    this.selectSearchResult(categoryId, skillId);
                });
            });
        }
        
        overlay.classList.remove('hidden');
    }

    selectSearchResult(categoryId, skillId) {
        const category = this.data.skillCategories.find(c => c.id === categoryId);
        const skill = category.skills.find(s => s.id === skillId);
        
        this.hideSearchOverlay();
        this.showSkill(category, skill);
        
        // Clear search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
        }
    }

    hideSearchOverlay() {
        const overlay = document.getElementById('searchOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    updateProgress() {
        const totalSkills = this.data.skillCategories.reduce((sum, cat) => sum + cat.skills.length, 0);
        const completedCount = this.completedSkills.size;
        const percentage = totalSkills > 0 ? Math.round((completedCount / totalSkills) * 100) : 0;
        
        const progressFill = document.getElementById('overallProgress');
        const progressText = document.getElementById('progressText');
        
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${percentage}% hoàn thành (${completedCount}/${totalSkills})`;
        }
    }

    updateBreadcrumb() {
        const breadcrumb = document.getElementById('breadcrumb');
        if (!breadcrumb) return;
        
        if (this.currentView === 'welcome') {
            breadcrumb.innerHTML = '<span>Trang chủ</span>';
        } else if (this.currentView === 'skill') {
            breadcrumb.innerHTML = `
                <span onclick="app.showWelcome()" style="cursor: pointer; color: var(--color-primary); text-decoration: underline;">Trang chủ</span>
                <span>${this.currentCategory.title}</span>
                <span>${this.currentSkill.title}</span>
            `;
        }
    }

    showWelcome() {
        this.currentView = 'welcome';
        this.currentSkill = null;
        this.currentCategory = null;
        
        document.querySelectorAll('.nav-skill').forEach(el => el.classList.remove('active'));
        
        const contentBody = document.getElementById('contentBody');
        if (!contentBody) return;
        
        contentBody.innerHTML = `
            <div class="welcome-screen">
                <h1>Chào mừng đến với Backend Developer Toolkit</h1>
                <p>Một bộ công cụ toàn diện giúp bạn master các kỹ năng backend development cần thiết.</p>
                
                <div class="feature-grid">
                    ${this.data.skillCategories.map(category => `
                        <div class="feature-card" data-category-id="${category.id}">
                            <div class="feature-icon">${category.icon}</div>
                            <h3>${category.title}</h3>
                            <p>${category.skills.length} kỹ năng</p>
                        </div>
                    `).join('')}
                </div>

                <div class="quick-start">
                    <h2>Bắt đầu nhanh</h2>
                    <p>Chọn một danh mục từ sidebar để khám phá các kỹ năng và ví dụ cụ thể.</p>
                    <button class="btn btn--primary" id="getStartedBtnWelcome">Bắt đầu học →</button>
                </div>
            </div>
        `;
        
        // Add event listeners to feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('click', () => {
                const categoryId = card.getAttribute('data-category-id');
                this.expandCategoryAndShow(categoryId);
            });
        });
        
        // Add event listener to get started button
        const getStartedBtnWelcome = document.getElementById('getStartedBtnWelcome');
        if (getStartedBtnWelcome) {
            getStartedBtnWelcome.addEventListener('click', () => {
                this.expandFirstCategory();
            });
        }
        
        this.updateBreadcrumb();
    }

    expandCategoryAndShow(categoryId) {
        const category = this.data.skillCategories.find(c => c.id === categoryId);
        if (category && category.skills.length > 0) {
            // Find the category header in sidebar
            const categoryHeaders = document.querySelectorAll('.nav-category__header');
            const categoryIndex = this.data.skillCategories.findIndex(c => c.id === categoryId);
            
            if (categoryHeaders[categoryIndex]) {
                const skillsEl = categoryHeaders[categoryIndex].nextElementSibling;
                this.toggleCategory(categoryId, categoryHeaders[categoryIndex], skillsEl);
                
                // Show first skill in the category
                setTimeout(() => {
                    this.showSkill(category, category.skills[0]);
                }, 100);
            }
        }
    }

    showProgressModal() {
        const modal = document.getElementById('progressModal');
        const modalBody = document.getElementById('progressModalBody');
        
        if (!modal || !modalBody) return;
        
        modalBody.innerHTML = this.data.skillCategories.map(category => {
            const categoryCompletedCount = category.skills.filter(skill => 
                this.completedSkills.has(skill.id)
            ).length;
            const categoryPercentage = Math.round((categoryCompletedCount / category.skills.length) * 100);
            
            return `
                <div class="progress-category">
                    <h3>
                        <span>${category.icon}</span>
                        <span>${category.title}</span>
                        <span class="status status--info">${categoryCompletedCount}/${category.skills.length}</span>
                    </h3>
                    <div class="progress-bar" style="margin-bottom: 16px;">
                        <div class="progress-bar__fill" style="width: ${categoryPercentage}%"></div>
                    </div>
                    <div class="category-progress">
                        ${category.skills.map(skill => `
                            <div class="skill-progress ${this.completedSkills.has(skill.id) ? 'completed' : ''}">
                                <span>${skill.title}</span>
                                <span class="skill-difficulty difficulty-${skill.difficulty}">${skill.difficulty}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }).join('');
        
        modal.classList.remove('hidden');
    }

    hideModal() {
        const modal = document.getElementById('progressModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    exportProgress() {
        const exportData = {
            timestamp: new Date().toISOString(),
            totalSkills: this.data.skillCategories.reduce((sum, cat) => sum + cat.skills.length, 0),
            completedSkills: [...this.completedSkills],
            bookmarkedSkills: [...this.bookmarkedSkills],
            progress: this.data.skillCategories.map(category => ({
                category: category.title,
                skills: category.skills.map(skill => ({
                    title: skill.title,
                    difficulty: skill.difficulty,
                    completed: this.completedSkills.has(skill.id),
                    bookmarked: this.bookmarkedSkills.has(skill.id)
                }))
            }))
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backend-toolkit-progress-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showToast('Tiến độ đã được xuất thành công!');
    }

    // Theme toggle functionality removed - using light theme only per project specification

    applyTheme() {
        // Force light theme per project specification
        document.documentElement.setAttribute('data-color-scheme', 'light');
        
        // Listen for theme changes from parent (unified toolkit)
        if (!this.themeListenerAdded) {
            window.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'THEME_CHANGE') {
                    // Always use light theme
                    document.documentElement.setAttribute('data-color-scheme', 'light');
                }
            });
            this.themeListenerAdded = true;
        }
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');
        
        if (!toast || !toastMessage) return;
        
        toastMessage.textContent = message;
        toast.classList.remove('hidden');
        
        setTimeout(() => {
            this.hideToast();
        }, 3000);
    }

    hideToast() {
        const toast = document.getElementById('toast');
        if (toast) {
            toast.classList.add('hidden');
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the application
const app = new BackendToolkitApp();
app.init();

// Make app globally available for event handlers
window.app = app;