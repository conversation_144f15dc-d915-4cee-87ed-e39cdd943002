<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toolkit Handbook - <PERSON><PERSON> Sư <PERSON>ần Mềm <PERSON>t Đời</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1 class="header-title">🚀 Toolkit Handbook</h1>
            <p class="header-subtitle">Cẩm nang toàn diện cho Kỹ sư Phần mềm</p>
            <div class="header-controls">
                <div class="search-container">
                    <input type="text" id="searchInput" class="search-input" placeholder="Tìm kiếm kiến thức...">
                    <button class="search-btn">🔍</button>
                </div>

            </div>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="main-layout">
        <!-- Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3>📚 <PERSON><PERSON><PERSON> lục</h3>
                <div class="progress-indicator">
                    <span class="progress-text">Tiến độ: 0%</span>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            
            <div class="nav-sections">
                <div class="nav-item" data-section="enterprise_platform">
                    <div class="nav-header">
                        <span class="nav-icon">🏗️</span>
                        <span class="nav-title">Kiến Trúc Nền Tảng Doanh Nghiệp</span>
                        <span class="nav-arrow">▼</span>
                    </div>
                    <div class="nav-submenu">
                        <div class="nav-subitem" data-topic="system-design">Thiết Kế Hệ Thống</div>
                        <div class="nav-subitem" data-topic="clean-architecture">Clean Architecture + DDD</div>
                        <div class="nav-subitem" data-topic="microservices">Microservices</div>
                        <div class="nav-subitem" data-topic="ai-native">Thiết Kế AI-Native</div>
                        <div class="nav-subitem" data-topic="production-templates">Mẫu Production</div>
                    </div>
                </div>

                <div class="nav-item" data-section="programming_languages">
                    <div class="nav-header">
                        <span class="nav-icon">💻</span>
                        <span class="nav-title">Ngôn Ngữ Lập Trình</span>
                        <span class="nav-arrow">▼</span>
                    </div>
                    <div class="nav-submenu">
                        <div class="nav-subitem" data-topic="javascript">JavaScript/TypeScript</div>
                        <div class="nav-subitem" data-topic="python">Python</div>
                        <div class="nav-subitem" data-topic="go">Go</div>
                        <div class="nav-subitem" data-topic="cpp-rust">C++/Rust</div>
                        <div class="nav-subitem" data-topic="language-comparison">So Sánh Ngôn Ngữ</div>
                    </div>
                </div>

                <div class="nav-item" data-section="oop_patterns">
                    <div class="nav-header">
                        <span class="nav-icon">🎨</span>
                        <span class="nav-title">OOP & Design Patterns</span>
                        <span class="nav-arrow">▼</span>
                    </div>
                    <div class="nav-submenu">
                        <div class="nav-subitem" data-topic="solid">Nguyên Lý SOLID</div>
                        <div class="nav-subitem" data-topic="gof">Gang of Four Patterns</div>
                        <div class="nav-subitem" data-topic="arch-patterns">Architectural Patterns</div>
                        <div class="nav-subitem" data-topic="best-practices">Thực Hành Tốt Nhất</div>
                    </div>
                </div>

                <div class="nav-item" data-section="data_structures">
                    <div class="nav-header">
                        <span class="nav-icon">🔗</span>
                        <span class="nav-title">Cấu Trúc Dữ Liệu & Thuật Toán</span>
                        <span class="nav-arrow">▼</span>
                    </div>
                    <div class="nav-submenu">
                        <div class="nav-subitem" data-topic="linear">Cấu Trúc Tuyến Tính</div>
                        <div class="nav-subitem" data-topic="nonlinear">Cấu Trúc Phi Tuyến</div>
                        <div class="nav-subitem" data-topic="algorithms">Thuật Toán</div>
                        <div class="nav-subitem" data-topic="complexity">Phân Tích Độ Phức Tạp</div>
                    </div>
                </div>

                <div class="nav-item" data-section="database">
                    <div class="nav-header">
                        <span class="nav-icon">💾</span>
                        <span class="nav-title">Cơ Sở Dữ Liệu & SQL</span>
                        <span class="nav-arrow">▼</span>
                    </div>
                    <div class="nav-submenu">
                        <div class="nav-subitem" data-topic="relational">Cơ Sở Dữ Liệu Quan Hệ</div>
                        <div class="nav-subitem" data-topic="nosql">NoSQL</div>
                        <div class="nav-subitem" data-topic="db-design">Thiết Kế Cơ Sở Dữ Liệu</div>
                        <div class="nav-subitem" data-topic="db-optimization">Tối Ưu Hiệu Suất</div>
                    </div>
                </div>

                <div class="nav-item" data-section="devops">
                    <div class="nav-header">
                        <span class="nav-icon">☁️</span>
                        <span class="nav-title">DevOps & Cloud</span>
                        <span class="nav-arrow">▼</span>
                    </div>
                    <div class="nav-submenu">
                        <div class="nav-subitem" data-topic="cloud-fundamentals">Cơ Bản Cloud</div>
                        <div class="nav-subitem" data-topic="cicd">CI/CD</div>
                        <div class="nav-subitem" data-topic="monitoring">Giám Sát</div>
                        <div class="nav-subitem" data-topic="security">Bảo Mật</div>
                    </div>
                </div>

                <div class="nav-item" data-section="apis">
                    <div class="nav-header">
                        <span class="nav-icon">🌐</span>
                        <span class="nav-title">APIs & Giao Tiếp</span>
                        <span class="nav-arrow">▼</span>
                    </div>
                    <div class="nav-submenu">
                        <div class="nav-subitem" data-topic="rest">REST APIs</div>
                        <div class="nav-subitem" data-topic="graphql">GraphQL</div>
                        <div class="nav-subitem" data-topic="grpc">gRPC</div>
                        <div class="nav-subitem" data-topic="realtime">Giao Tiếp Thời Gian Thực</div>
                    </div>
                </div>

                <div class="nav-item" data-section="ai_ml">
                    <div class="nav-header">
                        <span class="nav-icon">🤖</span>
                        <span class="nav-title">Machine Learning/AI</span>
                        <span class="nav-arrow">▼</span>
                    </div>
                    <div class="nav-submenu">
                        <div class="nav-subitem" data-topic="math-foundations">Nền Tảng Toán Học</div>
                        <div class="nav-subitem" data-topic="ml-algorithms">Thuật Toán ML</div>
                        <div class="nav-subitem" data-topic="deep-learning">Deep Learning</div>
                        <div class="nav-subitem" data-topic="mlops">MLOps</div>
                    </div>
                </div>

                <div class="nav-item" data-section="soft_skills">
                    <div class="nav-header">
                        <span class="nav-icon">🧠</span>
                        <span class="nav-title">Tư Duy & Kỹ Năng Mềm</span>
                        <span class="nav-arrow">▼</span>
                    </div>
                    <div class="nav-submenu">
                        <div class="nav-subitem" data-topic="systems-thinking">Tư Duy Hệ Thống</div>
                        <div class="nav-subitem" data-topic="problem-solving">Giải Quyết Vấn Đề</div>
                        <div class="nav-subitem" data-topic="communication">Giao Tiếp</div>
                        <div class="nav-subitem" data-topic="leadership">Lãnh Đạo</div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" id="breadcrumb">
                <span class="breadcrumb-item active">🏠 Trang chủ</span>
            </nav>

            <!-- Home Page -->
            <section class="content-section active" id="home">
                <div class="hero">
                    <h1>Toolkit Handbook cho Kỹ Sư Phần Mềm</h1>
                    <p>Cẩm nang toàn diện để học tập và tham khảo trong suốt sự nghiệp phần mềm</p>
                </div>

                <div class="overview-grid">
                    <div class="overview-card" data-section="enterprise_platform">
                        <div class="card-icon">🏗️</div>
                        <h3>Enterprise Platform Architecture</h3>
                        <p>System Design, Clean Architecture, Microservices, AI-Native Design</p>
                        <div class="difficulty-badge">Nâng cao</div>
                    </div>

                    <div class="overview-card" data-section="programming_languages">
                        <div class="card-icon">💻</div>
                        <h3>Programming Languages</h3>
                        <p>JavaScript, Python, Go, C++/Rust và so sánh ngôn ngữ</p>
                        <div class="difficulty-badge">Cơ bản</div>
                    </div>

                    <div class="overview-card" data-section="oop_patterns">
                        <div class="card-icon">🎨</div>
                        <h3>OOP & Design Patterns</h3>
                        <p>SOLID, Gang of Four, Architectural Patterns</p>
                        <div class="difficulty-badge">Trung cấp</div>
                    </div>

                    <div class="overview-card" data-section="data_structures">
                        <div class="card-icon">🔗</div>
                        <h3>Data Structures & Algorithms</h3>
                        <p>Cấu trúc dữ liệu, thuật toán và phân tích độ phức tạp</p>
                        <div class="difficulty-badge">Cơ bản</div>
                    </div>

                    <div class="overview-card" data-section="database">
                        <div class="card-icon">💾</div>
                        <h3>Database & SQL</h3>
                        <p>Relational, NoSQL, thiết kế và tối ưu hóa database</p>
                        <div class="difficulty-badge">Trung cấp</div>
                    </div>

                    <div class="overview-card" data-section="devops">
                        <div class="card-icon">☁️</div>
                        <h3>DevOps & Cloud</h3>
                        <p>Cloud Computing, CI/CD, Monitoring, Security</p>
                        <div class="difficulty-badge">Nâng cao</div>
                    </div>

                    <div class="overview-card" data-section="apis">
                        <div class="card-icon">🌐</div>
                        <h3>APIs & Communication</h3>
                        <p>REST, GraphQL, gRPC, Real-time Communication</p>
                        <div class="difficulty-badge">Trung cấp</div>
                    </div>

                    <div class="overview-card" data-section="ai_ml">
                        <div class="card-icon">🤖</div>
                        <h3>Machine Learning/AI</h3>
                        <p>Nền tảng toán học, ML Algorithms, Deep Learning</p>
                        <div class="difficulty-badge">Nâng cao</div>
                    </div>

                    <div class="overview-card" data-section="soft_skills">
                        <div class="card-icon">🧠</div>
                        <h3>Thinking & Soft Skills</h3>
                        <p>Systems Thinking, Problem Solving, Leadership</p>
                        <div class="difficulty-badge">Cơ bản</div>
                    </div>
                </div>

                <div class="quick-references">
                    <h2>📋 Tham khảo nhanh</h2>
                    <div class="quick-ref-grid">
                        <div class="quick-ref-card">
                            <h4>Big O Notation</h4>
                            <div class="quick-items">
                                <span class="quick-item">O(1)</span>
                                <span class="quick-item">O(log n)</span>
                                <span class="quick-item">O(n)</span>
                                <span class="quick-item">O(n log n)</span>
                                <span class="quick-item">O(n²)</span>
                                <span class="quick-item">O(2^n)</span>
                            </div>
                        </div>

                        <div class="quick-ref-card">
                            <h4>HTTP Status Codes</h4>
                            <div class="quick-items">
                                <span class="quick-item">200 OK</span>
                                <span class="quick-item">201 Created</span>
                                <span class="quick-item">400 Bad Request</span>
                                <span class="quick-item">401 Unauthorized</span>
                                <span class="quick-item">404 Not Found</span>
                                <span class="quick-item">500 Internal Server Error</span>
                            </div>
                        </div>

                        <div class="quick-ref-card">
                            <h4>SOLID Principles</h4>
                            <div class="quick-items">
                                <span class="quick-item">Single Responsibility</span>
                                <span class="quick-item">Open/Closed</span>
                                <span class="quick-item">Liskov Substitution</span>
                                <span class="quick-item">Interface Segregation</span>
                                <span class="quick-item">Dependency Inversion</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Dynamic Content Sections -->
            <section class="content-section" id="section-content">
                <div class="section-header">
                    <h1 id="section-title"></h1>
                    <div class="section-controls">
                        <div class="view-tabs">
                            <button class="tab-btn active" data-view="overview">Tổng quan</button>
                            <button class="tab-btn" data-view="details">Chi tiết</button>
                            <button class="tab-btn" data-view="examples">Ví dụ</button>
                            <button class="tab-btn" data-view="practice">Thực hành</button>
                        </div>
                        <button class="bookmark-btn" id="bookmarkBtn">⭐</button>
                    </div>
                </div>

                <div class="section-content" id="dynamic-content">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </section>
        </main>
    </div>

    <!-- Modal for code examples -->
    <div class="modal hidden" id="codeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Code Example</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <pre><code id="modal-code"></code></pre>
                <button class="copy-btn" id="copyBtn">📋 Copy</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>