<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Golang Handbook Toolkit</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <h1 class="header-title">🐢 Golang Handbook Toolkit</h1>
            <div class="header-controls">
                <div class="search-container">
                    <input type="text" id="searchInput" class="search-input" placeholder="Search...">
                    <button class="search-btn">🔍</button>
                </div>

            </div>
        </div>
    </header>

    <div class="main-layout">
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3>📖 Mục lục</h3>
            </div>
            <div class="nav-sections">
                <div class="nav-item" data-topic="handbook">Handbook</div>
                <div class="nav-item" data-topic="data_types">Data Types</div>
                <div class="nav-item" data-topic="best_practices">Best Practices</div>
            </div>
        </nav>

        <main class="main-content">
            <section class="content-section" id="section-content">
                <!-- Content will be populated by JavaScript -->
            </section>
        </main>
    </div>

    <script src="app.js"></script>
</body>
</html>
