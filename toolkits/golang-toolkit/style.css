/* Golang Handbook Toolkit Styles */
:root {
    --bg-color: #f8f9fa;
    --text-color: #212529;
    --header-bg: #ffffff;
    --sidebar-bg: #ffffff;
    --border-color: #dee2e6;
    --primary-color: #007d9c;
    --hover-bg: #e9ecef;
}

[data-theme="dark"] {
    --bg-color: #121212;
    --text-color: #e0e0e0;
    --header-bg: #1e1e1e;
    --sidebar-bg: #1e1e1e;
    --border-color: #333;
    --primary-color: #61dafb;
    --hover-bg: #2a2a2a;
}

body {
    font-family: sans-serif;
    margin: 0;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s, color 0.3s;
}

.header {
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.header-title {
    margin: 0;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.header-controls {
    display: flex;
    align-items: center;
}

.search-container {
    display: flex;
    margin-right: 1rem;
}

.search-input {
    border: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px;
    padding: 0.5rem;
}

.search-btn, .theme-toggle {
    border: 1px solid var(--border-color);
    background-color: transparent;
    cursor: pointer;
    padding: 0.5rem;
}

.search-btn {
    border-left: none;
    border-radius: 0 4px 4px 0;
}

.theme-toggle {
    border-radius: 4px;
}

.main-layout {
    display: flex;
    max-width: 1200px;
    margin: 1rem auto;
}

.sidebar {
    width: 250px;
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
}

.sidebar-header h3 {
    margin-top: 0;
}

.nav-item {
    padding: 0.75rem;
    cursor: pointer;
    border-radius: 4px;
}

.nav-item:hover, .nav-item.active {
    background-color: var(--hover-bg);
    color: var(--primary-color);
}

.main-content {
    flex-grow: 1;
    padding: 1rem;
}

.content-section {
    background-color: var(--header-bg);
    padding: 2rem;
    border-radius: 8px;
}

pre {
    background-color: var(--hover-bg);
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
}

code {
    font-family: 'Courier New', Courier, monospace;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

th, td {
    border: 1px solid var(--border-color);
    padding: 0.5rem;
    text-align: left;
}

th {
    background-color: var(--hover-bg);
}

/* Loading and Error States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.loading::before {
    content: "⏳";
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

.error-message {
    background-color: #fee;
    border: 1px solid #fcc;
    border-radius: 8px;
    padding: 2rem;
    margin: 1rem 0;
}

.error-message h3 {
    color: #d63384;
    margin-top: 0;
}

.error-message p {
    margin: 0.5rem 0;
}

.error-message strong {
    color: #6f42c1;
}

/* Enhanced code blocks */
.code-block {
    background-color: var(--hover-bg);
    border-left: 4px solid var(--primary-color);
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.9rem;
}

.code-block.go {
    border-left-color: #00add8;
}

.inline-code {
    background-color: var(--hover-bg);
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.9em;
}

/* CSV Table styling */
.csv-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.csv-table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: bold;
    padding: 0.75rem;
}

.csv-table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.csv-table tr:nth-child(even) {
    background-color: var(--hover-bg);
}

.csv-table tr:hover {
    background-color: rgba(0, 125, 156, 0.1);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

