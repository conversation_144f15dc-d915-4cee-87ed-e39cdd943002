// Software Refactoring Toolkit - Vietnamese Interface (Fixed)
class SoftwareRefactoringToolkit {
    constructor() {
        this.currentSection = 'getting_started';
        this.bookmarks = this.loadBookmarks();
        this.searchIndex = [];
        
        this.init();
        this.loadData();
    }

    init() {
        this.setupEventListeners();
        this.setupSearch();
        this.setupMobileNavigation();
        this.setupExpandableSections();
        this.loadBookmarkStates();
        this.initTheme();
    }

    loadData() {
        // Vietnamese refactoring data based on provided JSON structure
        this.refactoringData = {
            surgeon_mindset: {
                title: "T<PERSON> Duy Phẫu Thuật Viên",
                icon: "🏥",
                content: {
                    core_principles: [
                        {
                            name: "An Toàn Là Ưu Tiên",
                            description: "Không bao giờ thay đổi hành vi bên ngoài. Mọi refactoring phải bảo toàn chức năng.",
                            analogy: "<PERSON><PERSON><PERSON> bác sĩ phẫu thuật không bao giờ mổ mà không hiểu giải phẫu"
                        },
                        {
                            name: "Từng Bước Nhỏ",
                            description: "Thực hiện những thay đổi nhỏ nhất có thể hướng tới mục tiêu.",
                            benefit: "Giảm rủi ro, cho phép phát hiện lỗi nhanh, duy trì niềm tin của team"
                        },
                        {
                            name: "An Toàn Dựa Trên Test",
                            description: "Test là lưới an toàn của bạn. Test xanh = an toàn để tiếp tục.",
                            rule: "Không bao giờ refactor khi test đang fail"
                        },
                        {
                            name: "Ẩn Dụ Hai Chiếc Mũ",
                            description: "Bạn chỉ có thể thêm chức năng HOẶC refactor, không bao giờ làm cả hai cùng lúc.",
                            reason: "Trộn lẫn mục đích tạo ra phức tạp và che giấu nguồn gốc vấn đề"
                        }
                    ],
                    surgeon_workflow: [
                        "1. Chẩn đoán: Xác định những gì cần sửa (code smells)",
                        "2. Lập kế hoạch: Chọn kỹ thuật phù hợp với vấn đề",
                        "3. Chuẩn bị: Đảm bảo test cover khu vực cần sửa",
                        "4. Phẫu thuật: Thực hiện thay đổi chính xác, từng bước nhỏ",
                        "5. Kiểm tra: Chạy test sau mỗi thay đổi",
                        "6. Ghi chép: Ghi lại những gì đã thay đổi và tại sao"
                    ],
                    risk_assessment: {
                        low_risk: ["Đổi tên biến", "Trích xuất hằng số", "Sắp xếp lại tham số"],
                        medium_risk: ["Trích xuất phương thức", "Di chuyển trường", "Thay thế temp bằng query"],
                        high_risk: ["Trích xuất lớp", "Thay thế kế thừa", "Thay đổi kiến trúc"]
                    }
                }
            },
            safety_methodology: {
                title: "Phương Pháp An Toàn",
                icon: "🛡️",
                content: {
                    red_green_refactor: {
                        red: {
                            phase: "ĐỎ - Viết Test Fail",
                            goal: "Ghi lại hành vi mong muốn dưới dạng thực thi được",
                            actions: ["Viết test cho chức năng mới", "Xác minh test fail như mong đợi", "Tập trung vào cái gì, không phải như thế nào"]
                        },
                        green: {
                            phase: "XANH - Làm Test Pass",
                            goal: "Implement giải pháp đơn giản nhất có thể",
                            actions: ["Viết code tối thiểu để pass test", "Chưa cần lo về tính thanh lịch", "Tập trung làm sao cho hoạt động"]
                        },
                        refactor: {
                            phase: "REFACTOR - Cải thiện chất lượng code",
                            goal: "Dọn dẹp implementation mà vẫn giữ test xanh",
                            actions: ["Loại bỏ trùng lặp", "Cải thiện tên gọi", "Trích xuất method/class", "Áp dụng design pattern"]
                        }
                    },
                    characterization_testing: {
                        purpose: "Tạo lưới an toàn cho legacy code không có test hiện tại",
                        process: [
                            "1. Xác định đơn vị code cần characterize",
                            "2. Viết test ghi lại hành vi hiện tại (dù có thể sai)",
                            "3. Chạy test để xác minh pass với implementation hiện tại",
                            "4. Bây giờ bạn có lưới an toàn để refactor",
                            "5. Refactor từng bước nhỏ, giữ characterization test xanh"
                        ],
                        example: "Nếu method legacy trả về 42 với input 'foo', viết test expect 42, dù 42 có thể sai"
                    },
                    version_control_strategy: {
                        branching: "Tạo branch riêng cho công việc refactoring",
                        commits: "Commit sau mỗi micro-refactoring thành công",
                        messages: "Dùng commit message mô tả rõ ràng về transformation",
                        rollback: "Dễ dàng revert những thay đổi cụ thể nếu có vấn đề"
                    }
                }
            },
            code_smells: {
                title: "Phát Hiện Code Smell",
                icon: "👃",
                content: {
                    smell_categories: {
                        bloaters: [
                            {
                                name: "Method Dài",
                                description: "Method đã phát triển quá lớn và làm quá nhiều việc",
                                symptoms: ["Hơn 20-30 dòng", "Nhiều tầng abstraction", "Khó hiểu chỉ trong một cái nhìn"],
                                refactoring: ["Extract Method", "Replace Method with Method Object"],
                                example: "Một method validate input, xử lý data, format output, và log kết quả"
                            },
                            {
                                name: "Class Lớn",
                                description: "Class đã phát triển để làm quá nhiều việc",
                                symptoms: ["Quá nhiều biến instance", "Quá nhiều method", "Cohesion thấp"],
                                refactoring: ["Extract Class", "Extract Subclass", "Extract Interface"]
                            },
                            {
                                name: "Primitive Obsession",
                                description: "Dùng primitive thay vì object nhỏ cho các tác vụ đơn giản",
                                symptoms: ["String/int dùng cho khái niệm phức tạp", "Logic validation lặp lại", "Type checking với primitive"],
                                refactoring: ["Replace Data Value with Object", "Replace Type Code with Class"]
                            }
                        ],
                        change_preventers: [
                            {
                                name: "Divergent Change",
                                description: "Một class thường xuyên bị thay đổi vì những lý do khác nhau",
                                symptoms: ["Class bị modify cho các feature không liên quan", "Thay đổi ảnh hưởng đến nhiều method"],
                                refactoring: ["Extract Class", "Move Method"]
                            },
                            {
                                name: "Shotgun Surgery",
                                description: "Thực hiện thay đổi cần modify nhiều class",
                                symptoms: ["Thay đổi nhỏ ảnh hưởng nhiều file", "Code liên quan rải rác khắp các class"],
                                refactoring: ["Move Method", "Inline Class"]
                            }
                        ]
                    }
                }
            },
            workflow_integration: {
                title: "Tích Hợp Workflow",
                icon: "🔄",
                content: {
                    description: "Tích hợp refactoring vào quy trình làm việc hàng ngày để đạt hiệu quả tối đa.",
                    workflows: [
                        {
                            name: "TDD Refactoring",
                            description: "Refactoring là phần không thể thiếu của chu trình test-driven development",
                            steps: ["RED: Viết test fail", "GREEN: Làm test pass (thô sơ nếu cần)", "REFACTOR: Dọn dẹp code mà vẫn giữ test xanh"]
                        },
                        {
                            name: "Preparatory Refactoring",
                            description: "Refactor cấu trúc code trước khi thêm tính năng mới",
                            when_to_use: ["Trước khi thêm tính năng khó với cấu trúc hiện tại", "Khi phát hiện cách tổ chức code tốt hơn", "Để làm rõ thay đổi dự định"]
                        },
                        {
                            name: "Comprehension Refactoring", 
                            description: "Refactor để hiểu rõ hơn về code không quen",
                            techniques: ["Đổi tên variable/method để làm rõ mục đích", "Extract method để bộc lộ cấu trúc thuật toán", "Thêm comment rồi extract thành method"]
                        }
                    ]
                }
            },
            tech_debt_management: {
                title: "Quản Lý Technical Debt",
                icon: "💳",
                content: {
                    description: "Quản lý technical debt một cách có hệ thống để duy trì sức khỏe của codebase.",
                    identification: {
                        symptoms: [
                            "Tính năng mất nhiều thời gian hơn dự kiến để implement",
                            "Bug tập trung ở một số khu vực của code",
                            "Developer tránh làm việc với một số module nhất định",
                            "Comment trong code review lặp lại về cùng những vấn đề"
                        ],
                        measurement: ["SQALE method cho tác động tài chính", "Code complexity metrics", "Phân tích mật độ defect", "Xu hướng velocity của developer"]
                    },
                    prioritization: {
                        high_impact_high_effort: "Lập kế hoạch cẩn thận, chia thành các giai đoạn",
                        high_impact_low_effort: "Làm ngay lập tức - quick wins",
                        low_impact_high_effort: "Cân nhắc không làm - có thể không đáng",
                        low_impact_low_effort: "Tốt cho litter-pickup refactoring"
                    }
                }
            }
        };

        this.buildSearchIndex();
        this.loadDynamicSections();
        
        // Re-setup event listeners after content is loaded
        setTimeout(() => {
            this.setupDynamicEventListeners();
        }, 100);
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.navigateToSection(section);
            });
        });

        // Navigation buttons
        document.querySelectorAll('[data-navigate]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.navigate;
                this.navigateToSection(section);
            });
        });

        // Footer navigation
        document.querySelectorAll('.footer-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.getAttribute('href').substring(1);
                this.navigateToSection(section);
            });
        });
    }

    setupDynamicEventListeners() {
        // Bookmarks
        document.querySelectorAll('.bookmark-btn').forEach(btn => {
            btn.replaceWith(btn.cloneNode(true));
        });
        
        document.querySelectorAll('.bookmark-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const section = e.currentTarget.dataset.section;
                this.toggleBookmark(section);
            });
        });

        // Print buttons
        document.querySelectorAll('.print-btn').forEach(btn => {
            btn.replaceWith(btn.cloneNode(true));
        });
        
        document.querySelectorAll('.print-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                window.print();
            });
        });

        this.loadBookmarkStates();
    }

    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.querySelector('.search-btn');

        if (!searchInput || !searchBtn) return;

        let searchTimeout;
        
        const performSearchHandler = (query) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        };

        searchInput.addEventListener('input', (e) => {
            performSearchHandler(e.target.value);
        });

        searchBtn.addEventListener('click', () => {
            this.performSearch(searchInput.value);
        });

        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch(searchInput.value);
            }
        });
    }

    setupMobileNavigation() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-open');
            });
        }
    }

    setupExpandableSections() {
        // Use event delegation for both existing and future expandable sections
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('expandable-header') || 
                e.target.closest('.expandable-header')) {
                
                const header = e.target.classList.contains('expandable-header') 
                    ? e.target 
                    : e.target.closest('.expandable-header');
                
                const section = header ? header.closest('.expandable-section') : null;
                if (section) {
                    section.classList.toggle('expanded');
                    e.preventDefault();
                    e.stopPropagation();
                }
            }
        });
    }

    navigateToSection(sectionId) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.section === sectionId) {
                item.classList.add('active');
            }
        });

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionId;
            
            // Close mobile sidebar
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.remove('mobile-open');
            }
            
            // Scroll to top
            const content = document.getElementById('content');
            if (content) {
                content.scrollTop = 0;
            }
        }
    }

    toggleBookmark(section) {
        const btn = document.querySelector(`[data-section="${section}"]`);
        
        if (this.bookmarks.includes(section)) {
            this.bookmarks = this.bookmarks.filter(b => b !== section);
            if (btn) {
                btn.textContent = '🔖';
                btn.title = 'Thêm bookmark';
            }
        } else {
            this.bookmarks.push(section);
            if (btn) {
                btn.textContent = '📌';
                btn.title = 'Xóa bookmark';
            }
        }
        
        this.saveBookmarks();
    }

    loadBookmarks() {
        try {
            return JSON.parse(localStorage.getItem('refactoring-bookmarks') || '[]');
        } catch (e) {
            return [];
        }
    }

    saveBookmarks() {
        try {
            localStorage.setItem('refactoring-bookmarks', JSON.stringify(this.bookmarks));
        } catch (e) {
            // LocalStorage not available, continue without saving
        }
    }

    loadBookmarkStates() {
        this.bookmarks.forEach(section => {
            const btn = document.querySelector(`[data-section="${section}"] .bookmark-btn`);
            if (btn) {
                btn.textContent = '📌';
                btn.title = 'Xóa bookmark';
            }
        });
    }

    initTheme() {
        // Set light theme as default per project specification
        this.setTheme('light');
        
        // Listen for theme changes from parent (unified toolkit)
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'THEME_CHANGE') {
                this.setTheme('light'); // Always use light theme
            }
        });
    }

    setTheme(theme) {
        // Force light theme per project specification
        document.documentElement.setAttribute('data-color-scheme', 'light');
        localStorage.setItem('unifiedToolkit.theme', 'light');
    }

    // Theme toggle functionality removed - using light theme only per project specification

    buildSearchIndex() {
        this.searchIndex = [];
        
        // Index static content
        document.querySelectorAll('.content-section').forEach(section => {
            const sectionId = section.id;
            const text = section.textContent.toLowerCase();
            const title = section.querySelector('h2')?.textContent || '';
            
            this.searchIndex.push({
                section: sectionId,
                title,
                content: text,
                element: section
            });
        });

        // Index dynamic content
        Object.entries(this.refactoringData).forEach(([key, data]) => {
            const title = data.title;
            const content = JSON.stringify(data.content).toLowerCase();
            
            this.searchIndex.push({
                section: key,
                title,
                content,
                data: data.content
            });
        });
    }

    performSearch(query) {
        if (!query || query.length < 2) {
            this.clearSearchHighlights();
            return;
        }

        const results = this.searchIndex.filter(item => 
            item.content.includes(query.toLowerCase()) ||
            item.title.toLowerCase().includes(query.toLowerCase())
        );

        this.displaySearchResults(results, query);
    }

    displaySearchResults(results, query) {
        this.clearSearchHighlights();
        
        if (results.length === 0) {
            this.showSearchMessage('Không tìm thấy kết quả');
            return;
        }

        // Navigate to first result
        if (results.length > 0) {
            this.navigateToSection(results[0].section);
            
            // Highlight search terms after a delay
            setTimeout(() => {
                this.highlightSearchTerms(query);
            }, 300);
        }

        this.showSearchMessage(`Tìm thấy ${results.length} kết quả cho "${query}"`);
    }

    highlightSearchTerms(query) {
        const activeSection = document.querySelector('.content-section.active');
        if (!activeSection) return;

        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        
        const walker = document.createTreeWalker(
            activeSection,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    if (node.parentNode.tagName === 'SCRIPT' || 
                        node.parentNode.tagName === 'STYLE' ||
                        node.parentNode.classList?.contains('search-highlight')) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    return NodeFilter.FILTER_ACCEPT;
                }
            },
            false
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            if (regex.test(node.textContent)) {
                textNodes.push(node);
            }
        }

        textNodes.forEach(textNode => {
            const text = textNode.textContent;
            if (regex.test(text)) {
                const highlightedText = text.replace(regex, `<span class="search-highlight">$1</span>`);
                const span = document.createElement('span');
                span.innerHTML = highlightedText;
                
                const parent = textNode.parentNode;
                while (span.firstChild) {
                    parent.insertBefore(span.firstChild, textNode);
                }
                parent.removeChild(textNode);
            }
        });
    }

    clearSearchHighlights() {
        document.querySelectorAll('.search-highlight').forEach(highlight => {
            const parent = highlight.parentNode;
            const text = document.createTextNode(highlight.textContent);
            parent.replaceChild(text, highlight);
        });
    }

    showSearchMessage(message) {
        const existingMessage = document.querySelector('.search-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        const messageEl = document.createElement('div');
        messageEl.className = 'search-message';
        messageEl.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 1000;
            box-shadow: var(--shadow-md);
            color: var(--color-text);
        `;
        messageEl.textContent = message;
        document.body.appendChild(messageEl);

        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 3000);
    }

    loadDynamicSections() {
        const dynamicContent = document.getElementById('dynamicContent');
        if (!dynamicContent) return;
        
        // Clear existing content first
        dynamicContent.innerHTML = '';
        
        Object.entries(this.refactoringData).forEach(([sectionId, sectionData]) => {
            const section = this.createSectionElement(sectionId, sectionData);
            dynamicContent.appendChild(section);
        });

        // Load additional sections with Vietnamese content
        this.loadAdditionalSections(dynamicContent);
    }

    loadAdditionalSections(container) {
        // SOLID Principles section
        const solidSection = this.createSolidPrinciplesSection();
        container.appendChild(solidSection);

        // Refactoring Catalog section
        const catalogSection = this.createRefactoringCatalogSection();
        container.appendChild(catalogSection);

        // Interactive Labs section
        const labsSection = this.createInteractiveLabsSection();
        container.appendChild(labsSection);

        // Quick Reference section
        const quickRefSection = this.createQuickReferenceSection();
        container.appendChild(quickRefSection);
    }

    createSectionElement(sectionId, sectionData) {
        const section = document.createElement('section');
        section.id = sectionId;
        section.className = 'content-section';
        
        let content = `
            <div class="section-header">
                <h2><span class="section-icon">${sectionData.icon}</span> ${sectionData.title}</h2>
                <div class="section-actions">
                    <button class="bookmark-btn" data-section="${sectionId}">🔖</button>
                    <button class="print-btn">🖨️</button>
                </div>
            </div>
        `;

        content += this.generateSectionContent(sectionId, sectionData.content);
        
        section.innerHTML = content;
        return section;
    }

    generateSectionContent(sectionId, content) {
        switch (sectionId) {
            case 'surgeon_mindset':
                return this.generateSurgeonMindsetContent(content);
            case 'safety_methodology':
                return this.generateSafetyMethodologyContent(content);
            case 'code_smells':
                return this.generateCodeSmellsContent(content);
            case 'workflow_integration':
                return this.generateWorkflowContent(content);
            case 'tech_debt_management':
                return this.generateTechDebtContent(content);
            default:
                return '<p>Đang tải nội dung...</p>';
        }
    }

    generateSurgeonMindsetContent(content) {
        return `
            <div class="principle-quote">
                <blockquote>
                    <p>"Trước tiên hãy tìm hiểu, sau đó mới thay đổi."</p>
                    <cite>Nguyên lý tối thượng của Software Refactoring</cite>
                </blockquote>
            </div>

            <div class="principles-grid">
                ${content.core_principles.map(principle => `
                    <div class="principle-card">
                        <h3>${principle.name}</h3>
                        <p class="description">${principle.description}</p>
                        ${principle.analogy ? `<div class="example"><strong>Ví dụ:</strong> ${principle.analogy}</div>` : ''}
                        ${principle.benefit ? `<div class="benefit"><strong>Lợi ích:</strong> ${principle.benefit}</div>` : ''}
                        ${principle.rule ? `<div class="rule"><strong>Quy tắc:</strong> ${principle.rule}</div>` : ''}
                        ${principle.reason ? `<div class="reason"><strong>Lý do:</strong> ${principle.reason}</div>` : ''}
                    </div>
                `).join('')}
            </div>

            <div class="expandable-section">
                <h3 class="expandable-header">Quy trình của Phẫu thuật viên <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <ol class="workflow-steps">
                        ${content.surgeon_workflow.map(step => `<li>${step}</li>`).join('')}
                    </ol>
                </div>
            </div>

            <div class="expandable-section">
                <h3 class="expandable-header">Đánh giá rủi ro <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="risk-levels">
                        <div class="risk-item risk-low">
                            <span class="risk-label">Rủi ro thấp:</span>
                            <span>${content.risk_assessment.low_risk.join(', ')}</span>
                        </div>
                        <div class="risk-item risk-medium">
                            <span class="risk-label">Rủi ro trung bình:</span>
                            <span>${content.risk_assessment.medium_risk.join(', ')}</span>
                        </div>
                        <div class="risk-item risk-high">
                            <span class="risk-label">Rủi ro cao:</span>
                            <span>${content.risk_assessment.high_risk.join(', ')}</span>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .benefit, .rule, .reason {
                    margin-top: var(--space-8);
                    font-size: var(--font-size-sm);
                    color: var(--color-text-secondary);
                }
                .workflow-steps {
                    color: var(--color-text-secondary);
                    padding-left: var(--space-20);
                }
                .workflow-steps li {
                    margin-bottom: var(--space-8);
                }
            </style>
        `;
    }

    generateSafetyMethodologyContent(content) {
        return `
            <div class="intro-card">
                <h3>Phương pháp Red-Green-Refactor</h3>
                <p>Chu trình cốt lõi của Test-Driven Development và refactoring an toàn</p>
            </div>

            <div class="cycle-phases">
                ${Object.entries(content.red_green_refactor).map(([phase, data]) => `
                    <div class="phase-card ${phase}">
                        <h3>${data.phase}</h3>
                        <p class="goal"><strong>Mục tiêu:</strong> ${data.goal}</p>
                        <div class="actions">
                            <strong>Hành động:</strong>
                            <ul>
                                ${data.actions.map(action => `<li>${action}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `).join('')}
            </div>

            <div class="expandable-section">
                <h3 class="expandable-header">Characterization Testing <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <p><strong>Mục đích:</strong> ${content.characterization_testing.purpose}</p>
                    <div class="process-steps">
                        <strong>Quy trình:</strong>
                        <ol>
                            ${content.characterization_testing.process.map(step => `<li>${step}</li>`).join('')}
                        </ol>
                    </div>
                    <div class="example">
                        <strong>Ví dụ:</strong> ${content.characterization_testing.example}
                    </div>
                </div>
            </div>

            <div class="expandable-section">
                <h3 class="expandable-header">Chiến lược Version Control <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="strategy-grid">
                        ${Object.entries(content.version_control_strategy).map(([key, value]) => `
                            <div class="strategy-item">
                                <h4>${this.translateStrategyKey(key)}</h4>
                                <p>${value}</p>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <style>
                .cycle-phases {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: var(--space-16);
                    margin: var(--space-24) 0;
                }
                .phase-card {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-20);
                }
                .phase-card.red {
                    border-left: 4px solid var(--color-error);
                }
                .phase-card.green {
                    border-left: 4px solid var(--color-success);
                }
                .phase-card.refactor {
                    border-left: 4px solid var(--color-primary);
                }
                .goal {
                    color: var(--color-text-secondary);
                    margin: var(--space-12) 0;
                }
                .actions ul {
                    margin-top: var(--space-8);
                    color: var(--color-text-secondary);
                }
                .strategy-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: var(--space-16);
                    margin-top: var(--space-16);
                }
                .strategy-item {
                    background: var(--color-bg-2);
                    padding: var(--space-12);
                    border-radius: var(--radius-base);
                }
                .strategy-item h4 {
                    color: var(--color-primary);
                    margin-bottom: var(--space-8);
                }
            </style>
        `;
    }

    generateCodeSmellsContent(content) {
        return `
            <div class="intro-card">
                <h3>Code Smells - Dấu hiệu của code cần refactoring</h3>
                <p>Học cách nhận diện các dấu hiệu cho thấy code cần được tái cấu trúc để duy trì chất lượng và khả năng bảo trì.</p>
            </div>

            <div class="smell-categories">
                ${Object.entries(content.smell_categories).map(([category, smells]) => `
                    <div class="expandable-section expanded">
                        <h3 class="expandable-header">${this.translateSmellCategory(category)} <span class="expand-icon">▼</span></h3>
                        <div class="expandable-content">
                            <div class="smell-list">
                                ${smells.map(smell => `
                                    <div class="smell-item">
                                        <h4>🚨 ${smell.name}</h4>
                                        <p>${smell.description}</p>
                                        <div class="smell-symptoms">
                                            <strong>Triệu chứng:</strong> ${smell.symptoms.join(', ')}
                                        </div>
                                        <div class="smell-refactoring">
                                            <strong>Kỹ thuật refactoring:</strong> ${smell.refactoring.join(', ')}
                                        </div>
                                        ${smell.example ? `<div class="example"><strong>Ví dụ:</strong> ${smell.example}</div>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>

            <div class="detection-tips">
                <h3>🔍 Mẹo phát hiện Code Smell</h3>
                <div class="tips-grid">
                    <div class="tip-card">
                        <h4>Thủ công</h4>
                        <ul>
                            <li>Code review tập trung vào complexity metrics</li>
                            <li>Tìm pattern lặp lại trong codebase</li>
                            <li>Xác định method/class khó hiểu</li>
                            <li>Chú ý khu vực thường xuyên có bug</li>
                        </ul>
                    </div>
                    <div class="tip-card">
                        <h4>Công cụ tự động</h4>
                        <ul>
                            <li>SonarQube - phát hiện duplication, complexity</li>
                            <li>PMD - unused variables, empty catch blocks</li>
                            <li>SpotBugs - bug patterns, performance issues</li>
                            <li>ESLint - JavaScript code quality</li>
                        </ul>
                    </div>
                </div>
            </div>

            <style>
                .tips-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: var(--space-16);
                    margin-top: var(--space-16);
                }
                .tip-card {
                    background: var(--color-bg-3);
                    padding: var(--space-16);
                    border-radius: var(--radius-base);
                    border: 1px solid var(--color-border);
                }
                .tip-card h4 {
                    color: var(--color-primary);
                    margin-bottom: var(--space-12);
                }
                .tip-card ul {
                    color: var(--color-text-secondary);
                    padding-left: var(--space-16);
                }
            </style>
        `;
    }

    generateWorkflowContent(content) {
        return `
            <div class="intro-card">
                <h3>${content.description}</h3>
            </div>

            <div class="workflow-sections">
                ${content.workflows.map((workflow, index) => `
                    <div class="expandable-section ${index === 0 ? 'expanded' : ''}">
                        <h3 class="expandable-header">${workflow.name} <span class="expand-icon">▼</span></h3>
                        <div class="expandable-content">
                            <p class="workflow-description">${workflow.description}</p>
                            ${workflow.steps ? `
                                <div class="workflow-steps">
                                    <strong>Các bước:</strong>
                                    <ul>
                                        ${workflow.steps.map(step => `<li>${step}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                            ${workflow.when_to_use ? `
                                <div class="when-to-use">
                                    <strong>Khi nào sử dụng:</strong>
                                    <ul>
                                        ${workflow.when_to_use.map(when => `<li>${when}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                            ${workflow.techniques ? `
                                <div class="techniques">
                                    <strong>Kỹ thuật:</strong>
                                    <ul>
                                        ${workflow.techniques.map(technique => `<li>${technique}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>

            <style>
                .workflow-sections {
                    margin-top: var(--space-24);
                }
                .workflow-description {
                    color: var(--color-text-secondary);
                    margin-bottom: var(--space-16);
                }
                .workflow-steps, .when-to-use, .techniques {
                    margin-top: var(--space-12);
                    background: var(--color-bg-1);
                    padding: var(--space-12);
                    border-radius: var(--radius-base);
                }
                .workflow-steps ul, .when-to-use ul, .techniques ul {
                    color: var(--color-text-secondary);
                    margin-top: var(--space-8);
                    padding-left: var(--space-16);
                }
            </style>
        `;
    }

    generateTechDebtContent(content) {
        return `
            <div class="intro-card">
                <h3>${content.description}</h3>
            </div>

            <div class="expandable-section expanded">
                <h3 class="expandable-header">Xác định Technical Debt <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="debt-symptoms">
                        <h4>Triệu chứng:</h4>
                        <ul>
                            ${content.identification.symptoms.map(symptom => `<li>${symptom}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="debt-measurement">
                        <h4>Kỹ thuật đo lường:</h4>
                        <ul>
                            ${content.identification.measurement.map(measure => `<li>${measure}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>

            <div class="expandable-section">
                <h3 class="expandable-header">Ma trận ưu tiên <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="prioritization-matrix">
                        ${Object.entries(content.prioritization).map(([key, value]) => `
                            <div class="priority-item">
                                <strong>${this.translatePriorityKey(key)}:</strong> ${value}
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <style>
                .debt-symptoms, .debt-measurement {
                    background: var(--color-bg-4);
                    padding: var(--space-12);
                    border-radius: var(--radius-base);
                    margin-bottom: var(--space-12);
                }
                .debt-symptoms h4, .debt-measurement h4 {
                    color: var(--color-primary);
                    margin-bottom: var(--space-8);
                }
                .debt-symptoms ul, .debt-measurement ul {
                    color: var(--color-text-secondary);
                    padding-left: var(--space-16);
                }
                .prioritization-matrix {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: var(--space-12);
                    margin-top: var(--space-16);
                }
                .priority-item {
                    background: var(--color-bg-5);
                    padding: var(--space-12);
                    border-radius: var(--radius-base);
                    font-size: var(--font-size-sm);
                }
            </style>
        `;
    }

    createSolidPrinciplesSection() {
        const section = document.createElement('section');
        section.id = 'solid_principles';
        section.className = 'content-section';
        
        section.innerHTML = `
            <div class="section-header">
                <h2><span class="section-icon">🏗️</span> Nguyên Lý SOLID</h2>
                <div class="section-actions">
                    <button class="bookmark-btn" data-section="solid_principles">🔖</button>
                    <button class="print-btn">🖨️</button>
                </div>
            </div>

            <div class="intro-card">
                <h3>SOLID - 5 nguyên lý thiết kế cốt lõi</h3>
                <p>Các nguyên lý SOLID giúp tạo ra code dễ bảo trì, mở rộng và kiểm thử. Đây là nền tảng của refactoring hiệu quả.</p>
            </div>

            <div class="principles-list">
                <div class="principle-item">
                    <div class="principle-header">
                        <h3>🎯 S - Single Responsibility Principle</h3>
                        <p class="principle-definition">Một class chỉ nên có một lý do để thay đổi</p>
                    </div>
                    <div class="principle-content">
                        <div class="violation-signs">
                            <h4>Dấu hiệu vi phạm:</h4>
                            <ul>
                                <li>Tên class chứa 'And', 'Or', 'Manager'</li>
                                <li>Class có method không liên quan đến mục đích chính</li>
                                <li>Thay đổi các tính năng không liên quan ảnh hưởng cùng class</li>
                            </ul>
                        </div>
                        <div class="refactoring-techniques">
                            <h4>Kỹ thuật refactoring:</h4>
                            <ul>
                                <li>Extract Class cho các trách nhiệm riêng biệt</li>
                                <li>Move Method đến class phù hợp hơn</li>
                                <li>Tạo interface tập trung</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="principle-item">
                    <div class="principle-header">
                        <h3>🔓 O - Open/Closed Principle</h3>
                        <p class="principle-definition">Các thực thể phần mềm nên mở cho mở rộng nhưng đóng cho sửa đổi</p>
                    </div>
                    <div class="principle-content">
                        <div class="implementation-strategies">
                            <h4>Chiến lược thực hiện:</h4>
                            <ul>
                                <li>Sử dụng abstract base class và kế thừa</li>
                                <li>Áp dụng Strategy pattern cho thuật toán</li>
                                <li>Sử dụng Template Method pattern cho workflow</li>
                                <li>Thực hiện kiến trúc Plugin</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="principle-item">
                    <div class="principle-header">
                        <h3>🔄 L - Liskov Substitution Principle</h3>
                        <p class="principle-definition">Object của superclass phải có thể thay thế bằng object của subclass</p>
                    </div>
                    <div class="principle-content">
                        <div class="violation-signs">
                            <h4>Dấu hiệu vi phạm:</h4>
                            <ul>
                                <li>Subclass throw exception cho inherited method</li>
                                <li>Subclass có implementation rỗng cho inherited method</li>
                                <li>Client code kiểm tra object type trước khi gọi method</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="principle-item">
                    <div class="principle-header">
                        <h3>🔧 I - Interface Segregation Principle</h3>
                        <p class="principle-definition">Không client nào nên bị buộc phụ thuộc vào method mà nó không sử dụng</p>
                    </div>
                    <div class="principle-content">
                        <div class="refactoring-techniques">
                            <h4>Kỹ thuật refactoring:</h4>
                            <ul>
                                <li>Tách interface lớn thành những interface nhỏ, tập trung</li>
                                <li>Sử dụng role interface thay vì header interface</li>
                                <li>Áp dụng Interface Adapter pattern khi cần</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="principle-item">
                    <div class="principle-header">
                        <h3>🔀 D - Dependency Inversion Principle</h3>
                        <p class="principle-definition">Module cấp cao không nên phụ thuộc vào module cấp thấp. Cả hai nên phụ thuộc vào abstraction</p>
                    </div>
                    <div class="principle-content">
                        <div class="implementation">
                            <h4>Thực hiện:</h4>
                            <ul>
                                <li>Định nghĩa interface cho external dependencies</li>
                                <li>Sử dụng dependency injection để cung cấp implementation</li>
                                <li>Phụ thuộc vào abstraction, không phải concretion</li>
                            </ul>
                        </div>
                        <div class="benefits">
                            <h4>Lợi ích:</h4>
                            <ul>
                                <li>Dễ dàng unit testing với mock</li>
                                <li>Cấu hình hệ thống linh hoạt</li>
                                <li>Giảm coupling giữa các module</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .principles-list {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-20);
                }
                .principle-item {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-20);
                }
                .principle-header h3 {
                    color: var(--color-primary);
                    margin-bottom: var(--space-8);
                }
                .principle-definition {
                    font-style: italic;
                    color: var(--color-text-secondary);
                    margin-bottom: var(--space-16);
                }
                .principle-content {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: var(--space-16);
                }
                .violation-signs, .refactoring-techniques, .implementation-strategies, .implementation, .benefits {
                    background: var(--color-bg-1);
                    padding: var(--space-12);
                    border-radius: var(--radius-base);
                }
                .violation-signs h4, .refactoring-techniques h4, .implementation-strategies h4, .implementation h4, .benefits h4 {
                    color: var(--color-primary);
                    margin-bottom: var(--space-8);
                }
                .violation-signs ul, .refactoring-techniques ul, .implementation-strategies ul, .implementation ul, .benefits ul {
                    color: var(--color-text-secondary);
                    padding-left: var(--space-16);
                    margin: 0;
                }
            </style>
        `;

        return section;
    }

    createRefactoringCatalogSection() {
        const section = document.createElement('section');
        section.id = 'refactoring_catalog';
        section.className = 'content-section';
        
        section.innerHTML = `
            <div class="section-header">
                <h2><span class="section-icon">📚</span> Thư Viện Refactoring</h2>
                <div class="section-actions">
                    <button class="bookmark-btn" data-section="refactoring_catalog">🔖</button>
                    <button class="print-btn">🖨️</button>
                </div>
            </div>

            <div class="intro-card">
                <h3>Bộ sưu tập kỹ thuật Refactoring</h3>
                <p>Catalog đầy đủ các kỹ thuật refactoring được chứng minh, với cơ chế từng bước và ví dụ thực tế.</p>
            </div>

            <div class="refactoring-categories">
                <div class="expandable-section expanded">
                    <h3 class="expandable-header">🔧 Composing Methods <span class="expand-icon">▼</span></h3>
                    <div class="expandable-content">
                        <div class="refactoring-list">
                            <div class="refactoring-item">
                                <h4>Extract Method</h4>
                                <p><strong>Vấn đề:</strong> Bạn có một đoạn code có thể được nhóm lại với nhau</p>
                                <p><strong>Giải pháp:</strong> Biến đoạn code thành method với tên giải thích mục đích</p>
                                <div class="refactoring-mechanics">
                                    <h5>Cơ chế:</h5>
                                    <ol>
                                        <li>Tạo method mới với tên mô tả</li>
                                        <li>Copy code được trích xuất vào method mới</li>
                                        <li>Quét tìm biến local được sử dụng</li>
                                        <li>Truyền biến local như tham số</li>
                                        <li>Thay thế code được trích xuất bằng gọi method mới</li>
                                        <li>Test</li>
                                    </ol>
                                </div>
                                <div class="code-example">
                                    <div class="code-block code-before">
void printOwing() {
  printBanner();
  // print details
  System.out.println("name: " + name);
  System.out.println("amount: " + getOutstanding());
}</div>
                                    <div class="code-block code-after">
void printOwing() {
  printBanner();
  printDetails(getOutstanding());
}

void printDetails(double outstanding) {
  System.out.println("name: " + name);
  System.out.println("amount: " + outstanding);
}</div>
                                </div>
                            </div>

                            <div class="refactoring-item">
                                <h4>Replace Temp with Query</h4>
                                <p><strong>Vấn đề:</strong> Sử dụng biến temporary để giữ kết quả của expression</p>
                                <p><strong>Giải pháp:</strong> Trích xuất expression thành method và thay thế temp reference bằng method call</p>
                                <div class="benefits">
                                    <strong>Lợi ích:</strong>
                                    <ul>
                                        <li>Loại bỏ biến temp</li>
                                        <li>Làm code dễ đọc hơn</li>
                                        <li>Cho phép tái sử dụng method</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <h3 class="expandable-header">🔄 Moving Features Between Objects <span class="expand-icon">▼</span></h3>
                    <div class="expandable-content">
                        <div class="refactoring-list">
                            <div class="refactoring-item">
                                <h4>Move Method</h4>
                                <p><strong>Vấn đề:</strong> Method được sử dụng nhiều bởi class khác hơn class của chính nó</p>
                                <p><strong>Giải pháp:</strong> Tạo method mới trong class sử dụng nó nhiều nhất, biến method cũ thành delegation</p>
                            </div>

                            <div class="refactoring-item">
                                <h4>Extract Class</h4>
                                <p><strong>Vấn đề:</strong> Class đang làm công việc của hai class</p>
                                <p><strong>Giải pháp:</strong> Tạo class mới và di chuyển field và method liên quan</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <h3 class="expandable-header">🧠 Simplifying Conditionals <span class="expand-icon">▼</span></h3>
                    <div class="expandable-content">
                        <div class="refactoring-list">
                            <div class="refactoring-item">
                                <h4>Decompose Conditional</h4>
                                <p><strong>Vấn đề:</strong> Conditional (if-then-else) statement phức tạp</p>
                                <p><strong>Giải pháp:</strong> Trích xuất method từ condition, then part, và else part</p>
                                <div class="benefits">
                                    <strong>Lợi ích:</strong>
                                    <ul>
                                        <li>Làm nổi bật logic condition</li>
                                        <li>Làm rõ intention</li>
                                        <li>Dễ dàng modify hơn</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="refactoring-item">
                                <h4>Replace Conditional with Polymorphism</h4>
                                <p><strong>Vấn đề:</strong> Conditional chọn hành vi khác nhau tùy thuộc vào object type</p>
                                <p><strong>Giải pháp:</strong> Di chuyển mỗi nhánh của conditional đến subclass, làm method gốc abstract</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .benefits {
                    margin-top: var(--space-12);
                    background: var(--color-bg-3);
                    padding: var(--space-8);
                    border-radius: var(--radius-sm);
                    font-size: var(--font-size-sm);
                }
                .benefits ul {
                    margin-top: var(--space-4);
                    padding-left: var(--space-16);
                    color: var(--color-text-secondary);
                }
            </style>
        `;

        return section;
    }

    createInteractiveLabsSection() {
        const section = document.createElement('section');
        section.id = 'interactive_labs';
        section.className = 'content-section';
        
        section.innerHTML = `
            <div class="section-header">
                <h2><span class="section-icon">🧪</span> Phòng Lab Tương Tác</h2>
                <div class="section-actions">
                    <button class="bookmark-btn" data-section="interactive_labs">🔖</button>
                    <button class="print-btn">🖨️</button>
                </div>
            </div>

            <div class="intro-card">
                <h3>Thực hành kỹ năng Refactoring</h3>
                <p>Rèn luyện kỹ năng của bạn với các bài tập tương tác. Mỗi lab tập trung vào một khía cạnh cụ thể của refactoring.</p>
            </div>

            <div class="labs-grid">
                <div class="lab-item">
                    <div class="lab-header">
                        <h3>🎯 Code Smell Identification</h3>
                        <span class="difficulty beginner">Cơ bản</span>
                    </div>
                    <p>Học cách nhận diện các code smell phổ biến trong code examples thực tế.</p>
                    <div class="lab-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0/10 bài tập hoàn thành</span>
                    </div>
                    <button class="btn btn--primary lab-start" data-lab="code-smell">Bắt đầu Lab</button>
                </div>

                <div class="lab-item">
                    <div class="lab-header">
                        <h3>🔧 Extract Method Challenge</h3>
                        <span class="difficulty intermediate">Trung cấp</span>
                    </div>
                    <p>Thực hành kỹ thuật Extract Method với các scenario phức tạp dần.</p>
                    <div class="lab-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0/8 bài tập hoàn thành</span>
                    </div>
                    <button class="btn btn--primary lab-start" data-lab="extract-method">Bắt đầu Lab</button>
                </div>

                <div class="lab-item">
                    <div class="lab-header">
                        <h3>🏗️ SOLID Principles Workout</h3>
                        <span class="difficulty advanced">Nâng cao</span>
                    </div>
                    <p>Áp dụng nguyên lý SOLID để refactor code vi phạm các nguyên lý thiết kế.</p>
                    <div class="lab-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0/12 bài tập hoàn thành</span>
                    </div>
                    <button class="btn btn--primary lab-start" data-lab="solid-principles">Bắt đầu Lab</button>
                </div>

                <div class="lab-item">
                    <div class="lab-header">
                        <h3>🛡️ Safety-First Refactoring</h3>
                        <span class="difficulty intermediate">Trung cấp</span>
                    </div>
                    <p>Thực hành refactoring an toàn với legacy code không có test coverage.</p>
                    <div class="lab-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0/6 bài tập hoàn thành</span>
                    </div>
                    <button class="btn btn--primary lab-start" data-lab="safety-first">Bắt đầu Lab</button>
                </div>
            </div>

            <div class="lab-workspace" id="labWorkspace" style="display: none;">
                <div class="workspace-header">
                    <h3 id="currentLabTitle">Lab Workspace</h3>
                    <button class="btn btn--secondary" id="closeLab">Đóng Lab</button>
                </div>
                <div class="workspace-content">
                    <div class="exercise-area">
                        <h4>Bài tập hiện tại:</h4>
                        <div id="exerciseContent">
                            <p>Đang tải nội dung bài tập...</p>
                        </div>
                    </div>
                    <div class="controls-area">
                        <button class="btn btn--outline" id="getHint">Gợi ý</button>
                        <button class="btn btn--outline" id="showSolution">Xem đáp án</button>
                        <button class="btn btn--primary" id="checkAnswer">Kiểm tra</button>
                        <button class="btn btn--primary" id="nextExercise">Bài tiếp theo</button>
                    </div>
                </div>
            </div>

            <style>
                .labs-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                    gap: var(--space-20);
                    margin-top: var(--space-24);
                }
                .lab-item {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-20);
                    transition: all var(--duration-fast) var(--ease-standard);
                }
                .lab-item:hover {
                    border-color: var(--color-primary);
                    box-shadow: var(--shadow-md);
                }
                .lab-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: var(--space-12);
                }
                .lab-header h3 {
                    color: var(--color-primary);
                    margin: 0;
                }
                .difficulty {
                    padding: var(--space-4) var(--space-8);
                    border-radius: var(--radius-full);
                    font-size: var(--font-size-xs);
                    font-weight: var(--font-weight-medium);
                }
                .difficulty.beginner {
                    background: rgba(var(--color-success-rgb), 0.15);
                    color: var(--color-success);
                }
                .difficulty.intermediate {
                    background: rgba(var(--color-warning-rgb), 0.15);
                    color: var(--color-warning);
                }
                .difficulty.advanced {
                    background: rgba(var(--color-error-rgb), 0.15);
                    color: var(--color-error);
                }
                .lab-progress {
                    margin: var(--space-16) 0;
                }
                .progress-text {
                    font-size: var(--font-size-sm);
                    color: var(--color-text-secondary);
                    margin-top: var(--space-4);
                    display: block;
                }
                .lab-start {
                    width: 100%;
                }
                .lab-workspace {
                    margin-top: var(--space-32);
                    background: var(--color-bg-1);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-24);
                }
                .workspace-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: var(--space-20);
                    padding-bottom: var(--space-12);
                    border-bottom: 1px solid var(--color-border);
                }
                .workspace-content {
                    display: grid;
                    grid-template-columns: 2fr 1fr;
                    gap: var(--space-20);
                }
                .exercise-area, .controls-area {
                    background: var(--color-surface);
                    padding: var(--space-16);
                    border-radius: var(--radius-base);
                    border: 1px solid var(--color-border);
                }
                .controls-area {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-8);
                    height: fit-content;
                }
                @media (max-width: 768px) {
                    .workspace-content {
                        grid-template-columns: 1fr;
                    }
                }
            </style>
        `;

        return section;
    }

    createQuickReferenceSection() {
        const section = document.createElement('section');
        section.id = 'quick_reference';
        section.className = 'content-section';
        
        section.innerHTML = `
            <div class="section-header">
                <h2><span class="section-icon">⚡</span> Tham Khảo Nhanh</h2>
                <div class="section-actions">
                    <button class="bookmark-btn" data-section="quick_reference">🔖</button>
                    <button class="print-btn">🖨️</button>
                </div>
            </div>

            <div class="intro-card">
                <h3>Truy cập nhanh các công thức và checklist thiết yếu</h3>
                <p>Bookmark phần này để tham khảo nhanh trong quá trình refactoring hàng ngày.</p>
            </div>

            <div class="quick-ref-sections">
                <div class="ref-section emergency">
                    <h3>🚨 Checklist Khẩn Cấp</h3>
                    <p class="emergency-intro">Khi bạn cần refactor ngay lập tức:</p>
                    <div class="emergency-checklist">
                        <label class="emergency-item">
                            <input type="checkbox">
                            <span class="item-number">1</span>
                            Test có đang pass không?
                        </label>
                        <label class="emergency-item">
                            <input type="checkbox">
                            <span class="item-number">2</span>
                            Có backup/branch riêng chưa?
                        </label>
                        <label class="emergency-item">
                            <input type="checkbox">
                            <span class="item-number">3</span>
                            Xác định code smell cụ thể
                        </label>
                        <label class="emergency-item">
                            <input type="checkbox">
                            <span class="item-number">4</span>
                            Chọn kỹ thuật refactoring phù hợp
                        </label>
                        <label class="emergency-item">
                            <input type="checkbox">
                            <span class="item-number">5</span>
                            Thực hiện từng bước nhỏ
                        </label>
                        <label class="emergency-item">
                            <input type="checkbox">
                            <span class="item-number">6</span>
                            Chạy test sau mỗi thay đổi
                        </label>
                    </div>
                </div>

                <div class="ref-section">
                    <h3>🔍 Code Smell Quick ID</h3>
                    <div class="smell-quick-list">
                        <div class="smell-quick-item">
                            <strong>Method dài:</strong> > 30 dòng, nhiều abstraction level
                        </div>
                        <div class="smell-quick-item">
                            <strong>Class lớn:</strong> > 300 dòng, quá nhiều responsibility
                        </div>
                        <div class="smell-quick-item">
                            <strong>Duplicate Code:</strong> Logic giống nhau ở nhiều nơi
                        </div>
                        <div class="smell-quick-item">
                            <strong>Switch Statements:</strong> Cùng switch xuất hiện nhiều nơi
                        </div>
                        <div class="smell-quick-item">
                            <strong>Primitive Obsession:</strong> String/int cho khái niệm phức tạp
                        </div>
                    </div>
                </div>

                <div class="ref-section">
                    <h3>🏗️ SOLID Cheat Sheet</h3>
                    <div class="solid-quick">
                        <div class="solid-item">
                            <strong>S</strong> - Một class, một trách nhiệm
                        </div>
                        <div class="solid-item">
                            <strong>O</strong> - Mở để mở rộng, đóng để sửa đổi
                        </div>
                        <div class="solid-item">
                            <strong>L</strong> - Subclass phải thay thế được superclass
                        </div>
                        <div class="solid-item">
                            <strong>I</strong> - Interface nhỏ, tập trung
                        </div>
                        <div class="solid-item">
                            <strong>D</strong> - Phụ thuộc vào abstraction
                        </div>
                    </div>
                </div>

                <div class="ref-section">
                    <h3>⚙️ Refactoring Recipes</h3>
                    <div class="recipes-list">
                        <div class="recipe-item">
                            <h4>🔧 Extract Method</h4>
                            <ol>
                                <li>Tạo method mới với tên mô tả</li>
                                <li>Copy code cần trích xuất</li>
                                <li>Xử lý local variables</li>
                                <li>Thay thế bằng method call</li>
                                <li>Test</li>
                            </ol>
                        </div>
                        <div class="recipe-item">
                            <h4>🔄 Move Method</h4>
                            <ol>
                                <li>Tìm class sử dụng method nhiều nhất</li>
                                <li>Copy method sang class đích</li>
                                <li>Điều chỉnh để hoạt động trong môi trường mới</li>
                                <li>Biến method cũ thành delegation</li>
                                <li>Test và xem xét xóa method cũ</li>
                            </ol>
                        </div>
                        <div class="recipe-item">
                            <h4>🏗️ Extract Class</h4>
                            <ol>
                                <li>Tạo class mới</li>
                                <li>Di chuyển field liên quan</li>
                                <li>Di chuyển method liên quan</li>
                                <li>Tạo link giữa các class</li>
                                <li>Test từng bước</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .quick-ref-sections {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-24);
                    margin-top: var(--space-24);
                }
                .ref-section {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-20);
                }
                .ref-section.emergency {
                    border-color: var(--color-error);
                    background: rgba(var(--color-error-rgb), 0.05);
                }
                .emergency-intro {
                    color: var(--color-error);
                    font-weight: var(--font-weight-medium);
                    margin-bottom: var(--space-16);
                }
                .emergency-checklist {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-8);
                }
                .emergency-item {
                    display: flex;
                    align-items: center;
                    gap: var(--space-12);
                    padding: var(--space-8) var(--space-12);
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-base);
                    cursor: pointer;
                    transition: all var(--duration-fast) var(--ease-standard);
                }
                .emergency-item:hover {
                    border-color: var(--color-error);
                }
                .item-number {
                    background: var(--color-error);
                    color: white;
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: var(--font-size-sm);
                    font-weight: var(--font-weight-bold);
                    flex-shrink: 0;
                }
                .smell-quick-list, .solid-quick {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-8);
                    margin-top: var(--space-16);
                }
                .smell-quick-item, .solid-item {
                    padding: var(--space-8) var(--space-12);
                    background: var(--color-bg-2);
                    border-radius: var(--radius-base);
                    font-size: var(--font-size-sm);
                }
                .recipes-list {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: var(--space-16);
                    margin-top: var(--space-16);
                }
                .recipe-item {
                    background: var(--color-bg-3);
                    padding: var(--space-12);
                    border-radius: var(--radius-base);
                }
                .recipe-item h4 {
                    color: var(--color-primary);
                    margin-bottom: var(--space-8);
                }
                .recipe-item ol {
                    color: var(--color-text-secondary);
                    font-size: var(--font-size-sm);
                    padding-left: var(--space-16);
                    margin: 0;
                }
            </style>
        `;

        return section;
    }

    translateSmellCategory(category) {
        const translations = {
            'bloaters': '🎈 Bloaters (Thổi phồng)',
            'change_preventers': '🚧 Change Preventers (Cản trở thay đổi)'
        };
        return translations[category] || category;
    }

    translateStrategyKey(key) {
        const translations = {
            'branching': 'Phân nhánh',
            'commits': 'Commit',
            'messages': 'Thông điệp',
            'rollback': 'Rollback'
        };
        return translations[key] || key;
    }

    translatePriorityKey(key) {
        const translations = {
            'high_impact_high_effort': 'Tác động cao, Nỗ lực cao',
            'high_impact_low_effort': 'Tác động cao, Nỗ lực thấp',
            'low_impact_high_effort': 'Tác động thấp, Nỗ lực cao',
            'low_impact_low_effort': 'Tác động thấp, Nỗ lực thấp'
        };
        return translations[key] || key;
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SoftwareRefactoringToolkit();
});

// Lab functionality
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('lab-start')) {
        const labType = e.target.dataset.lab;
        startLab(labType);
    }

    if (e.target.id === 'closeLab') {
        closeLab();
    }

    if (e.target.id === 'getHint') {
        showHint();
    }

    if (e.target.id === 'showSolution') {
        showSolution();
    }

    if (e.target.id === 'checkAnswer') {
        checkAnswer();
    }

    if (e.target.id === 'nextExercise') {
        nextExercise();
    }
});

function startLab(labType) {
    const workspace = document.getElementById('labWorkspace');
    const title = document.getElementById('currentLabTitle');
    
    const labTitles = {
        'code-smell': '🎯 Code Smell Identification Lab',
        'extract-method': '🔧 Extract Method Challenge',
        'solid-principles': '🏗️ SOLID Principles Workout',
        'safety-first': '🛡️ Safety-First Refactoring'
    };
    
    title.textContent = labTitles[labType] || 'Lab Workspace';
    workspace.style.display = 'block';
    workspace.scrollIntoView({ behavior: 'smooth' });
    
    // Load exercise content
    loadExercise(labType, 1);
}

function closeLab() {
    document.getElementById('labWorkspace').style.display = 'none';
}

function loadExercise(labType, exerciseNumber) {
    const exerciseContent = document.getElementById('exerciseContent');
    
    const exercises = {
        'code-smell': [
            {
                title: 'Nhận diện Long Method',
                code: `
public void processOrder(Order order) {
    // Validate order
    if (order == null) throw new IllegalArgumentException("Order cannot be null");
    if (order.getItems().isEmpty()) throw new IllegalArgumentException("Order must have items");
    
    // Calculate total
    double total = 0;
    for (OrderItem item : order.getItems()) {
        total += item.getPrice() * item.getQuantity();
    }
    
    // Apply discounts
    if (order.getCustomer().isPremium()) {
        total *= 0.9; // 10% discount
    }
    
    // Add tax
    total *= 1.1; // 10% tax
    
    // Update inventory
    for (OrderItem item : order.getItems()) {
        inventory.decrementStock(item.getProductId(), item.getQuantity());
    }
    
    // Send email
    String emailBody = "Thank you for your order of " + total;
    emailService.sendEmail(order.getCustomer().getEmail(), "Order Confirmation", emailBody);
    
    // Log transaction
    logger.info("Order processed: " + order.getId() + " for customer: " + order.getCustomer().getId());
}`,
                question: 'Method này vi phạm code smell nào và tại sao?',
                hint: 'Hãy đếm số dòng code và số trách nhiệm khác nhau của method này.',
                answer: 'Long Method - Method này có quá nhiều trách nhiệm: validation, calculation, discount, tax, inventory, email, logging.'
            }
        ]
    };
    
    const exercise = exercises[labType]?.[exerciseNumber - 1];
    if (exercise) {
        exerciseContent.innerHTML = `
            <h4>${exercise.title}</h4>
            <pre class="code-block">${exercise.code}</pre>
            <p><strong>Câu hỏi:</strong> ${exercise.question}</p>
            <textarea id="userAnswer" class="form-control" placeholder="Nhập câu trả lời của bạn..." rows="4"></textarea>
        `;
    }
}

function showHint() {
    alert('Gợi ý: Hãy đếm số trách nhiệm khác nhau mà method này đang thực hiện. Mỗi comment block có thể là một trách nhiệm riêng biệt.');
}

function showSolution() {
    alert('Đáp án: Đây là Long Method smell. Method processOrder() đang thực hiện quá nhiều việc: validation, tính toán total, áp dụng discount, thêm tax, cập nhật inventory, gửi email, và logging. Nên tách thành các method nhỏ hơn như validateOrder(), calculateTotal(), applyDiscounts(), updateInventory(), sendConfirmationEmail().');
}

function checkAnswer() {
    const userAnswer = document.getElementById('userAnswer')?.value;
    if (userAnswer?.toLowerCase().includes('long method')) {
        alert('✅ Chính xác! Bạn đã nhận diện đúng Long Method smell.');
    } else {
        alert('❌ Chưa chính xác. Hãy thử lại hoặc xem gợi ý.');
    }
}

function nextExercise() {
    alert('Chức năng này sẽ được phát triển trong phiên bản đầy đủ.');
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Alt + number keys for quick section navigation
    if (e.altKey && e.key >= '1' && e.key <= '9') {
        const sections = ['getting_started', 'surgeon_mindset', 'safety_methodology', 'code_smells', 'refactoring_catalog', 'solid_principles', 'workflow_integration', 'tech_debt_management', 'interactive_labs'];
        const index = parseInt(e.key) - 1;
        if (sections[index] && window.app) {
            window.app.navigateToSection(sections[index]);
        }
    }
    
    // Ctrl/Cmd + K for search focus
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    }
});