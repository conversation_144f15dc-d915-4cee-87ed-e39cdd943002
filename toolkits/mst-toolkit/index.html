<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MST Framework Handbook - T<PERSON><PERSON>p <PERSON></title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header & Navigation -->
    <header class="header">
        <div class="container">
            <div class="header__content">
                <div class="logo">
                    <h1>MST Framework</h1>
                    <span>Mindset • Skillset • Toolset</span>
                </div>
                <nav class="nav">
                    <button class="nav__toggle" id="navToggle">☰</button>
                    <ul class="nav__list" id="navList">
                        <li><button class="nav__item active" data-section="dashboard">Dashboard</button></li>
                        <li><button class="nav__item" data-section="framework">Framework Overview</button></li>
                        <li><button class="nav__item" data-section="mapping">Problem Mapping</button></li>
                        <li><button class="nav__item" data-section="software">Software Engineering</button></li>
                        <li><button class="nav__item" data-section="assessment">Self-Assessment</button></li>
                        <li><button class="nav__item" data-section="career">Career Planner</button></li>
                        <li><button class="nav__item" data-section="library">Reference Library</button></li>
                        <li><button class="nav__item" data-section="practice">Practice</button></li>
                    </ul>
                </nav>
                <div class="header__actions">

                    <button class="btn btn--outline btn--sm" id="languageToggle">EN/VI</button>
                    <button class="btn btn--primary btn--sm" id="exportData">Xuất dữ liệu</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Dashboard Section -->
        <section class="section active" id="dashboard">
            <div class="container">
                <div class="dashboard">
                    <div class="dashboard__welcome">
                        <h2>Chào mừng đến với MST Framework Handbook</h2>
                        <p>Framework tích hợp <strong>Mindset - Skillset - Toolset</strong> giúp bạn áp dụng tư duy hệ thống để giải quyết mọi thách thức trong sự nghiệp kỹ sư phần mềm.</p>
                    </div>
                    
                    <div class="dashboard__grid">
                        <div class="card dashboard__card">
                            <div class="card__body">
                                <h3>Tiến độ học tập</h3>
                                <div class="progress-widget">
                                    <div class="progress-item">
                                        <span>Framework Overview</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0%" id="frameworkProgress"></div>
                                        </div>
                                    </div>
                                    <div class="progress-item">
                                        <span>Assessments</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0%" id="assessmentProgress"></div>
                                        </div>
                                    </div>
                                    <div class="progress-item">
                                        <span>Problem Mapping</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0%" id="mappingProgress"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card dashboard__card">
                            <div class="card__body">
                                <h3>Quick Problem Mapping</h3>
                                <p>Áp dụng framework ngay cho thách thức hiện tại</p>
                                <form class="quick-mapping-form" id="quickMappingForm">
                                    <input type="text" class="form-control" placeholder="Mô tả ngắn vấn đề của bạn..." id="quickProblem">
                                    <button type="submit" class="btn btn--primary btn--full-width">Phân tích ngay</button>
                                </form>
                            </div>
                        </div>

                        <div class="card dashboard__card">
                            <div class="card__body">
                                <h3>Hoạt động gần đây</h3>
                                <ul class="activity-list" id="recentActivities">
                                    <li>Chưa có hoạt động nào</li>
                                </ul>
                            </div>
                        </div>

                        <div class="card dashboard__card">
                            <div class="card__body">
                                <h3>Truy cập nhanh</h3>
                                <div class="quick-actions">
                                    <button class="btn btn--outline btn--sm" data-action="framework">Học Framework</button>
                                    <button class="btn btn--outline btn--sm" data-action="assessment">Đánh giá bản thân</button>
                                    <button class="btn btn--outline btn--sm" data-action="career">Lên kế hoạch</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Framework Overview Section -->
        <section class="section" id="framework">
            <div class="container">
                <div class="framework-overview">
                    <h2>Framework Overview - Tổng quan MST</h2>
                    
                    <div class="framework-diagram">
                        <div class="mst-triangle">
                            <div class="mst-component mst-mindset">
                                <h3>MINDSET</h3>
                                <p>Tư duy định hướng</p>
                            </div>
                            <div class="mst-component mst-skillset">
                                <h3>SKILLSET</h3>
                                <p>Kỹ năng thực thi</p>
                            </div>
                            <div class="mst-component mst-toolset">
                                <h3>TOOLSET</h3>
                                <p>Công cụ khuếch đại</p>
                            </div>
                            <div class="mst-center">
                                <span>Problem Solving</span>
                            </div>
                        </div>
                    </div>

                    <div class="framework-tabs">
                        <div class="tabs">
                            <button class="tab-btn active" data-tab="components">3 Thành phần</button>
                            <button class="tab-btn" data-tab="process">6 Giai đoạn</button>
                            <button class="tab-btn" data-tab="principles">Nguyên tắc cốt lõi</button>
                        </div>

                        <div class="tab-content active" id="components">
                            <div class="components-grid">
                                <div class="card component-card">
                                    <div class="card__body">
                                        <h3>Mindset - Tư duy định hướng</h3>
                                        <p>Nền tảng định hướng bao gồm hệ thống niềm tin, giá trị, thái độ và cách tiếp cận vấn đề</p>
                                        <ul class="component-features">
                                            <li>Tư duy phát triển</li>
                                            <li>Tư duy hệ thống</li>
                                            <li>Tư duy thích nghi</li>
                                            <li>Tư duy đạo đức</li>
                                        </ul>
                                        <div class="software-parallel">
                                            <strong>Software Engineering:</strong> Architectural design principles (modularity, maintainability, scalability)
                                        </div>
                                    </div>
                                </div>

                                <div class="card component-card">
                                    <div class="card__body">
                                        <h3>Skillset - Kỹ năng thực thi</h3>
                                        <p>Tập hợp năng lực thực thi gồm các kỹ năng cứng và mềm cần thiết để chuyển hóa tư duy thành hành động</p>
                                        <ul class="component-features">
                                            <li>Kỹ năng bất hủ</li>
                                            <li>Kỹ năng thích nghi</li>
                                            <li>Kỹ năng cảm xúc</li>
                                            <li>Kỹ năng nhận thức</li>
                                        </ul>
                                        <div class="software-parallel">
                                            <strong>Software Engineering:</strong> System design skills, algorithms, API design, business logic development
                                        </div>
                                    </div>
                                </div>

                                <div class="card component-card">
                                    <div class="card__body">
                                        <h3>Toolset - Công cụ khuếch đại</h3>
                                        <p>Phương tiện khuếch đại gồm các công cụ, phương pháp, công nghệ được lựa chọn để tối ưu hóa hiệu quả</p>
                                        <ul class="component-features">
                                            <li>Tính linh hoạt</li>
                                            <li>Khả năng tương tác</li>
                                            <li>Tính bền vững</li>
                                            <li>Khả năng mở rộng</li>
                                        </ul>
                                        <div class="software-parallel">
                                            <strong>Software Engineering:</strong> Programming frameworks, cloud infrastructure, DevOps tools, libraries
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-content" id="process">
                            <div class="process-flow">
                                <h3>6 Giai đoạn áp dụng MST Framework</h3>
                                <div class="process-steps">
                                    <div class="process-step">
                                        <div class="step-number">1</div>
                                        <div class="step-content">
                                            <h4>Nhận diện vấn đề</h4>
                                            <p>Xác định rõ ràng thách thức cần giải quyết</p>
                                        </div>
                                    </div>
                                    <div class="process-step">
                                        <div class="step-number">2</div>
                                        <div class="step-content">
                                            <h4>Định vị mindset cần thiết</h4>
                                            <p>Xác định tư duy và thái độ phù hợp</p>
                                        </div>
                                    </div>
                                    <div class="process-step">
                                        <div class="step-number">3</div>
                                        <div class="step-content">
                                            <h4>Xác định skillset liên quan</h4>
                                            <p>Đánh giá kỹ năng hiện có và cần phát triển</p>
                                        </div>
                                    </div>
                                    <div class="process-step">
                                        <div class="step-number">4</div>
                                        <div class="step-content">
                                            <h4>Tìm toolset phù hợp</h4>
                                            <p>Lựa chọn công cụ và phương pháp tối ưu</p>
                                        </div>
                                    </div>
                                    <div class="process-step">
                                        <div class="step-number">5</div>
                                        <div class="step-content">
                                            <h4>Triển khai tích hợp</h4>
                                            <p>Thực hiện giải pháp với sự kết hợp MST</p>
                                        </div>
                                    </div>
                                    <div class="process-step">
                                        <div class="step-number">6</div>
                                        <div class="step-content">
                                            <h4>Đánh giá và cải tiến</h4>
                                            <p>Theo dõi kết quả và điều chỉnh</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-content" id="principles">
                            <div class="principles-grid">
                                <div class="card">
                                    <div class="card__body">
                                        <h3>Giá trị bất biến</h3>
                                        <div class="values-grid">
                                            <span class="value-tag">Đồng cảm</span>
                                            <span class="value-tag">Tôn trọng</span>
                                            <span class="value-tag">Chính trực</span>
                                            <span class="value-tag">Công bằng</span>
                                            <span class="value-tag">Học tập suốt đời</span>
                                            <span class="value-tag">Cải tiến liên tục</span>
                                            <span class="value-tag">Đổi mới sáng tạo</span>
                                            <span class="value-tag">Bền vững</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card">
                                    <div class="card__body">
                                        <h3>Kỹ năng cốt lõi</h3>
                                        <div class="skills-categories">
                                            <div class="skill-category">
                                                <h4>Tư duy</h4>
                                                <ul>
                                                    <li>Tư duy phản biện</li>
                                                    <li>Tư duy hệ thống</li>
                                                    <li>Tư duy sáng tạo</li>
                                                    <li>Tư duy chiến lược</li>
                                                </ul>
                                            </div>
                                            <div class="skill-category">
                                                <h4>Giao tiếp</h4>
                                                <ul>
                                                    <li>Lắng nghe tích cực</li>
                                                    <li>Truyền đạt rõ ràng</li>
                                                    <li>Thuyết phục</li>
                                                    <li>Giao tiếp đa văn hóa</li>
                                                </ul>
                                            </div>
                                            <div class="skill-category">
                                                <h4>Thích nghi</h4>
                                                <ul>
                                                    <li>Học nhanh</li>
                                                    <li>Linh hoạt</li>
                                                    <li>Phục hồi</li>
                                                    <li>Quản lý thay đổi</li>
                                                </ul>
                                            </div>
                                            <div class="skill-category">
                                                <h4>Xã hội</h4>
                                                <ul>
                                                    <li>Làm việc nhóm</li>
                                                    <li>Lãnh đạo</li>
                                                    <li>Giải quyết xung đột</li>
                                                    <li>Xây dựng mối quan hệ</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Problem Mapping Section -->
        <section class="section" id="mapping">
            <div class="container">
                <div class="problem-mapping">
                    <h2>Problem Mapping Guide - Hướng dẫn ánh xạ vấn đề</h2>
                    <p>Sử dụng wizard tương tác để áp dụng MST Framework vào thách thức cụ thể của bạn</p>

                    <div class="mapping-wizard" id="mappingWizard">
                        <div class="wizard-progress">
                            <div class="progress-steps">
                                <div class="step active" data-step="1">Xác định vấn đề</div>
                                <div class="step" data-step="2">Phân tích Mindset</div>
                                <div class="step" data-step="3">Đánh giá Skillset</div>
                                <div class="step" data-step="4">Chọn Toolset</div>
                                <div class="step" data-step="5">Kế hoạch thực hiện</div>
                                <div class="step" data-step="6">Đánh giá</div>
                            </div>
                        </div>

                        <form class="wizard-form" id="wizardForm">
                            <!-- Step 1: Problem Identification -->
                            <div class="wizard-step active" id="step1">
                                <div class="card">
                                    <div class="card__body">
                                        <h3>Bước 1: Xác định và phân tích vấn đề</h3>
                                        <div class="form-group">
                                            <label class="form-label">Mô tả vấn đề/thách thức:</label>
                                            <textarea class="form-control" name="problemDescription" rows="3" placeholder="Mô tả chi tiết vấn đề bạn đang gặp phải..."></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Loại vấn đề:</label>
                                            <select class="form-control" name="problemType">
                                                <option value="">Chọn loại vấn đề</option>
                                                <option value="technical">Kỹ thuật</option>
                                                <option value="management">Quản lý</option>
                                                <option value="career">Nghề nghiệp</option>
                                                <option value="team">Nhóm/Team</option>
                                                <option value="process">Quy trình</option>
                                                <option value="personal">Cá nhân</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Mức độ ưu tiên:</label>
                                            <div class="priority-levels">
                                                <label><input type="radio" name="priority" value="low"> Thấp</label>
                                                <label><input type="radio" name="priority" value="medium"> Trung bình</label>
                                                <label><input type="radio" name="priority" value="high"> Cao</label>
                                                <label><input type="radio" name="priority" value="urgent"> Khẩn cấp</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 2: Mindset Analysis -->
                            <div class="wizard-step" id="step2">
                                <div class="card">
                                    <div class="card__body">
                                        <h3>Bước 2: Phân tích Mindset cần thiết</h3>
                                        <div class="form-group">
                                            <label class="form-label">Tư duy hiện tại của bạn:</label>
                                            <textarea class="form-control" name="currentMindset" rows="2" placeholder="Bạn đang nghĩ gì về vấn đề này?"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Tư duy cần thiết:</label>
                                            <div class="mindset-options">
                                                <label><input type="checkbox" name="neededMindset" value="growth"> Tư duy phát triển</label>
                                                <label><input type="checkbox" name="neededMindset" value="system"> Tư duy hệ thống</label>
                                                <label><input type="checkbox" name="neededMindset" value="adaptive"> Tư duy thích nghi</label>
                                                <label><input type="checkbox" name="neededMindset" value="ethical"> Tư duy đạo đức</label>
                                                <label><input type="checkbox" name="neededMindset" value="creative"> Tư duy sáng tạo</label>
                                                <label><input type="checkbox" name="neededMindset" value="strategic"> Tư duy chiến lược</label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Rào cản tư duy:</label>
                                            <textarea class="form-control" name="mindsetBarriers" rows="2" placeholder="Những gì có thể cản trở tư duy tích cực của bạn?"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3: Skillset Assessment -->
                            <div class="wizard-step" id="step3">
                                <div class="card">
                                    <div class="card__body">
                                        <h3>Bước 3: Đánh giá Skillset</h3>
                                        <div class="form-group">
                                            <label class="form-label">Kỹ năng hiện có liên quan:</label>
                                            <textarea class="form-control" name="currentSkills" rows="2" placeholder="Liệt kê các kỹ năng bạn đã có..."></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Kỹ năng cần phát triển:</label>
                                            <textarea class="form-control" name="neededSkills" rows="2" placeholder="Kỹ năng nào bạn cần học thêm?"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Khoảng cách kỹ năng:</label>
                                            <div class="skill-gap-analysis">
                                                <div class="skill-item">
                                                    <span>Kỹ năng kỹ thuật</span>
                                                    <select name="technicalGap">
                                                        <option value="none">Không thiếu</option>
                                                        <option value="small">Thiếu ít</option>
                                                        <option value="medium">Thiếu trung bình</option>
                                                        <option value="large">Thiếu nhiều</option>
                                                    </select>
                                                </div>
                                                <div class="skill-item">
                                                    <span>Kỹ năng quản lý</span>
                                                    <select name="managementGap">
                                                        <option value="none">Không thiếu</option>
                                                        <option value="small">Thiếu ít</option>
                                                        <option value="medium">Thiếu trung bình</option>
                                                        <option value="large">Thiếu nhiều</option>
                                                    </select>
                                                </div>
                                                <div class="skill-item">
                                                    <span>Kỹ năng giao tiếp</span>
                                                    <select name="communicationGap">
                                                        <option value="none">Không thiếu</option>
                                                        <option value="small">Thiếu ít</option>
                                                        <option value="medium">Thiếu trung bình</option>
                                                        <option value="large">Thiếu nhiều</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 4: Toolset Selection -->
                            <div class="wizard-step" id="step4">
                                <div class="card">
                                    <div class="card__body">
                                        <h3>Bước 4: Lựa chọn Toolset</h3>
                                        <div class="form-group">
                                            <label class="form-label">Công cụ hiện tại:</label>
                                            <textarea class="form-control" name="currentTools" rows="2" placeholder="Các công cụ/phương pháp bạn đang sử dụng..."></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Tiêu chí lựa chọn công cụ:</label>
                                            <div class="tool-criteria">
                                                <label><input type="checkbox" name="toolCriteria" value="flexibility"> Tính linh hoạt</label>
                                                <label><input type="checkbox" name="toolCriteria" value="compatibility"> Khả năng tương tác</label>
                                                <label><input type="checkbox" name="toolCriteria" value="sustainability"> Tính bền vững</label>
                                                <label><input type="checkbox" name="toolCriteria" value="scalability"> Khả năng mở rộng</label>
                                                <label><input type="checkbox" name="toolCriteria" value="cost"> Chi phí hợp lý</label>
                                                <label><input type="checkbox" name="toolCriteria" value="ease"> Dễ sử dụng</label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Công cụ được đề xuất:</label>
                                            <textarea class="form-control" name="recommendedTools" rows="2" placeholder="Các công cụ/phương pháp bạn đang cân nhắc..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 5: Implementation Plan -->
                            <div class="wizard-step" id="step5">
                                <div class="card">
                                    <div class="card__body">
                                        <h3>Bước 5: Kế hoạch thực hiện</h3>
                                        <div class="form-group">
                                            <label class="form-label">Mục tiêu cụ thể:</label>
                                            <textarea class="form-control" name="objectives" rows="2" placeholder="Bạn muốn đạt được gì?"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Thời gian dự kiến:</label>
                                            <select class="form-control" name="timeline">
                                                <option value="1week">1 tuần</option>
                                                <option value="2weeks">2 tuần</option>
                                                <option value="1month">1 tháng</option>
                                                <option value="3months">3 tháng</option>
                                                <option value="6months">6 tháng</option>
                                                <option value="1year">1 năm</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Các bước thực hiện:</label>
                                            <textarea class="form-control" name="actionSteps" rows="3" placeholder="Liệt kê các bước cụ thể..."></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Tài nguyên cần thiết:</label>
                                            <textarea class="form-control" name="resources" rows="2" placeholder="Thời gian, ngân sách, con người..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 6: Evaluation -->
                            <div class="wizard-step" id="step6">
                                <div class="card">
                                    <div class="card__body">
                                        <h3>Bước 6: Đánh giá và theo dõi</h3>
                                        <div class="form-group">
                                            <label class="form-label">Chỉ số đo lường thành công:</label>
                                            <textarea class="form-control" name="successMetrics" rows="2" placeholder="Làm sao biết bạn đã thành công?"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Tần suất đánh giá:</label>
                                            <select class="form-control" name="reviewFrequency">
                                                <option value="daily">Hàng ngày</option>
                                                <option value="weekly">Hàng tuần</option>
                                                <option value="biweekly">2 tuần/lần</option>
                                                <option value="monthly">Hàng tháng</option>
                                                <option value="quarterly">Hàng quý</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Kế hoạch điều chỉnh:</label>
                                            <textarea class="form-control" name="adjustmentPlan" rows="2" placeholder="Khi nào và như thế nào bạn sẽ điều chỉnh?"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="wizard-controls">
                                <button type="button" class="btn btn--outline" id="prevStep" disabled>Quay lại</button>
                                <button type="button" class="btn btn--primary" id="nextStep">Tiếp tục</button>
                                <button type="submit" class="btn btn--primary hidden" id="submitMapping">Hoàn thành</button>
                            </div>
                        </form>
                    </div>

                    <div class="saved-mappings">
                        <h3>Các phân tích đã lưu</h3>
                        <div class="mapping-list" id="savedMappings">
                            <p>Chưa có phân tích nào được lưu</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Software Engineering Applications -->
        <section class="section" id="software">
            <div class="container">
                <div class="software-applications">
                    <h2>Ứng dụng trong Software Engineering</h2>
                    <p>Các ví dụ cụ thể về áp dụng MST Framework trong công việc kỹ sư phần mềm</p>

                    <div class="applications-grid">
                        <div class="card application-card" data-scenario="architecture">
                            <div class="card__body">
                                <h3>Thiết kế System Architecture</h3>
                                <div class="mst-mapping">
                                    <div class="mst-item">
                                        <strong>Mindset:</strong> Modular thinking, scalability-first approach
                                    </div>
                                    <div class="mst-item">
                                        <strong>Skillset:</strong> System design patterns, API design, performance optimization
                                    </div>
                                    <div class="mst-item">
                                        <strong>Toolset:</strong> Cloud platforms, containerization, microservices frameworks
                                    </div>
                                </div>
                                <button class="btn btn--outline" onclick="openScenario('architecture')">Xem chi tiết</button>
                            </div>
                        </div>

                        <div class="card application-card" data-scenario="debt">
                            <div class="card__body">
                                <h3>Quản lý Technical Debt</h3>
                                <div class="mst-mapping">
                                    <div class="mst-item">
                                        <strong>Mindset:</strong> Long-term sustainability over short-term gains
                                    </div>
                                    <div class="mst-item">
                                        <strong>Skillset:</strong> Code review, refactoring techniques, technical communication
                                    </div>
                                    <div class="mst-item">
                                        <strong>Toolset:</strong> Static analysis tools, testing frameworks, documentation systems
                                    </div>
                                </div>
                                <button class="btn btn--outline" onclick="openScenario('debt')">Xem chi tiết</button>
                            </div>
                        </div>

                        <div class="card application-card" data-scenario="transition">
                            <div class="card__body">
                                <h3>Chuyển đổi vai trò (Developer → Architect)</h3>
                                <div class="mst-mapping">
                                    <div class="mst-item">
                                        <strong>Mindset:</strong> Growth mindset, leadership orientation
                                    </div>
                                    <div class="mst-item">
                                        <strong>Skillset:</strong> Team management, strategic thinking, business alignment
                                    </div>
                                    <div class="mst-item">
                                        <strong>Toolset:</strong> Project management tools, communication platforms, metrics dashboards
                                    </div>
                                </div>
                                <button class="btn btn--outline" onclick="openScenario('transition')">Xem chi tiết</button>
                            </div>
                        </div>
                    </div>

                    <div class="parallels-section">
                        <h3>Sự tương đồng giữa MST Framework và Software Engineering</h3>
                        <div class="parallels-table">
                            <div class="parallel-item">
                                <div class="framework-side">
                                    <h4>MST Framework</h4>
                                    <p><strong>Mindset:</strong> Tư duy định hướng và nguyên tắc</p>
                                </div>
                                <div class="arrow">⟷</div>
                                <div class="software-side">
                                    <h4>Software Engineering</h4>
                                    <p><strong>Architecture Principles:</strong> Modularity, maintainability, scalability</p>
                                </div>
                            </div>
                            <div class="parallel-item">
                                <div class="framework-side">
                                    <p><strong>Skillset:</strong> Kỹ năng và năng lực</p>
                                </div>
                                <div class="arrow">⟷</div>
                                <div class="software-side">
                                    <p><strong>Technical Skills:</strong> Algorithms, data structures, system design</p>
                                </div>
                            </div>
                            <div class="parallel-item">
                                <div class="framework-side">
                                    <p><strong>Toolset:</strong> Công cụ và phương pháp</p>
                                </div>
                                <div class="arrow">⟷</div>
                                <div class="software-side">
                                    <p><strong>Technology Stack:</strong> Languages, frameworks, platforms, tools</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Assessment Section -->
        <section class="section" id="assessment">
            <div class="container">
                <div class="assessment-tools">
                    <h2>Self-Assessment Tools - Công cụ đánh giá bản thân</h2>
                    <p>Đánh giá hiện trạng và xác định hướng phát triển của bạn</p>

                    <div class="assessment-tabs">
                        <div class="tabs">
                            <button class="tab-btn active" data-tab="mindset-assess">Mindset</button>
                            <button class="tab-btn" data-tab="skillset-assess">Skillset</button>
                            <button class="tab-btn" data-tab="toolset-assess">Toolset</button>
                            <button class="tab-btn" data-tab="results">Kết quả</button>
                        </div>

                        <div class="tab-content active" id="mindset-assess">
                            <div class="assessment-form">
                                <h3>Đánh giá Mindset</h3>
                                <form id="mindsetAssessment">
                                    <div class="question-group">
                                        <label>Bạn có tin rằng năng lực của mình có thể được phát triển qua nỗ lực?</label>
                                        <div class="rating-scale">
                                            <input type="radio" name="q1" value="1"><span>Hoàn toàn không</span>
                                            <input type="radio" name="q1" value="2"><span>Ít</span>
                                            <input type="radio" name="q1" value="3"><span>Trung bình</span>
                                            <input type="radio" name="q1" value="4"><span>Nhiều</span>
                                            <input type="radio" name="q1" value="5"><span>Hoàn toàn đồng ý</span>
                                        </div>
                                    </div>
                                    <div class="question-group">
                                        <label>Khi gặp thất bại, bạn thường phản ứng như thế nào?</label>
                                        <select name="q2">
                                            <option value="">Chọn câu trả lời</option>
                                            <option value="1">Bỏ cuộc ngay lập tức</option>
                                            <option value="2">Cảm thấy nản lòng và tránh thử lại</option>
                                            <option value="3">Tạm dừng và xem xét lại</option>
                                            <option value="4">Phân tích nguyên nhân và điều chỉnh</option>
                                            <option value="5">Coi đó là cơ hội học hỏi và cải tiến</option>
                                        </select>
                                    </div>
                                    <div class="question-group">
                                        <label>Bạn có chủ động tìm kiếm phản hồi để cải thiện không?</label>
                                        <div class="rating-scale">
                                            <input type="radio" name="q3" value="1"><span>Không bao giờ</span>
                                            <input type="radio" name="q3" value="2"><span>Hiếm khi</span>
                                            <input type="radio" name="q3" value="3"><span>Đôi khi</span>
                                            <input type="radio" name="q3" value="4"><span>Thường xuyên</span>
                                            <input type="radio" name="q3" value="5"><span>Luôn luôn</span>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn--primary">Hoàn thành đánh giá Mindset</button>
                                </form>
                            </div>
                        </div>

                        <div class="tab-content" id="skillset-assess">
                            <div class="assessment-form">
                                <h3>Đánh giá Skillset</h3>
                                <form id="skillsetAssessment">
                                    <div class="skills-matrix">
                                        <div class="skill-category">
                                            <h4>Kỹ năng kỹ thuật</h4>
                                            <div class="skill-items">
                                                <div class="skill-item">
                                                    <span>Programming Languages</span>
                                                    <select name="programming">
                                                        <option value="1">Cơ bản</option>
                                                        <option value="2">Trung cấp</option>
                                                        <option value="3">Khá</option>
                                                        <option value="4">Giỏi</option>
                                                        <option value="5">Chuyên gia</option>
                                                    </select>
                                                </div>
                                                <div class="skill-item">
                                                    <span>System Design</span>
                                                    <select name="systemDesign">
                                                        <option value="1">Cơ bản</option>
                                                        <option value="2">Trung cấp</option>
                                                        <option value="3">Khá</option>
                                                        <option value="4">Giỏi</option>
                                                        <option value="5">Chuyên gia</option>
                                                    </select>
                                                </div>
                                                <div class="skill-item">
                                                    <span>Database Management</span>
                                                    <select name="database">
                                                        <option value="1">Cơ bản</option>
                                                        <option value="2">Trung cấp</option>
                                                        <option value="3">Khá</option>
                                                        <option value="4">Giỏi</option>
                                                        <option value="5">Chuyên gia</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="skill-category">
                                            <h4>Kỹ năng mềm</h4>
                                            <div class="skill-items">
                                                <div class="skill-item">
                                                    <span>Communication</span>
                                                    <select name="communication">
                                                        <option value="1">Cơ bản</option>
                                                        <option value="2">Trung cấp</option>
                                                        <option value="3">Khá</option>
                                                        <option value="4">Giỏi</option>
                                                        <option value="5">Chuyên gia</option>
                                                    </select>
                                                </div>
                                                <div class="skill-item">
                                                    <span>Problem Solving</span>
                                                    <select name="problemSolving">
                                                        <option value="1">Cơ bản</option>
                                                        <option value="2">Trung cấp</option>
                                                        <option value="3">Khá</option>
                                                        <option value="4">Giỏi</option>
                                                        <option value="5">Chuyên gia</option>
                                                    </select>
                                                </div>
                                                <div class="skill-item">
                                                    <span>Leadership</span>
                                                    <select name="leadership">
                                                        <option value="1">Cơ bản</option>
                                                        <option value="2">Trung cấp</option>
                                                        <option value="3">Khá</option>
                                                        <option value="4">Giỏi</option>
                                                        <option value="5">Chuyên gia</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="question-group">
                                        <label>Kỹ năng nào bạn cần phát triển trong 6 tháng tới?</label>
                                        <textarea name="skillsToImprove" class="form-control" rows="3"></textarea>
                                    </div>
                                    <button type="submit" class="btn btn--primary">Hoàn thành đánh giá Skillset</button>
                                </form>
                            </div>
                        </div>

                        <div class="tab-content" id="toolset-assess">
                            <div class="assessment-form">
                                <h3>Đánh giá Toolset</h3>
                                <form id="toolsetAssessment">
                                    <div class="question-group">
                                        <label>Bạn lựa chọn công cụ/công nghệ dựa trên tiêu chí gì?</label>
                                        <div class="checkbox-group">
                                            <label><input type="checkbox" name="toolCriteria" value="performance"> Hiệu suất</label>
                                            <label><input type="checkbox" name="toolCriteria" value="ease"> Dễ sử dụng</label>
                                            <label><input type="checkbox" name="toolCriteria" value="community"> Cộng đồng hỗ trợ</label>
                                            <label><input type="checkbox" name="toolCriteria" value="cost"> Chi phí</label>
                                            <label><input type="checkbox" name="toolCriteria" value="scalability"> Khả năng mở rộng</label>
                                            <label><input type="checkbox" name="toolCriteria" value="compatibility"> Tương thích</label>
                                        </div>
                                    </div>
                                    <div class="question-group">
                                        <label>Bạn có thường xuyên đánh giá lại hiệu quả của công cụ đang dùng?</label>
                                        <div class="rating-scale">
                                            <input type="radio" name="toolReview" value="1"><span>Không bao giờ</span>
                                            <input type="radio" name="toolReview" value="2"><span>Hiếm khi</span>
                                            <input type="radio" name="toolReview" value="3"><span>Đôi khi</span>
                                            <input type="radio" name="toolReview" value="4"><span>Thường xuyên</span>
                                            <input type="radio" name="toolReview" value="5"><span>Luôn luôn</span>
                                        </div>
                                    </div>
                                    <div class="question-group">
                                        <label>Khi nào bạn quyết định thay đổi công cụ/công nghệ?</label>
                                        <select name="toolChange">
                                            <option value="1">Khi bắt buộc phải thay đổi</option>
                                            <option value="2">Khi có vấn đề nghiêm trọng</option>
                                            <option value="3">Khi có lựa chọn tốt hơn đáng kể</option>
                                            <option value="4">Khi lên kế hoạch nâng cấp</option>
                                            <option value="5">Thường xuyên để tối ưu hóa</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn--primary">Hoàn thành đánh giá Toolset</button>
                                </form>
                            </div>
                        </div>

                        <div class="tab-content" id="results">
                            <div class="assessment-results" id="assessmentResults">
                                <h3>Kết quả đánh giá</h3>
                                <p>Hoàn thành các đánh giá để xem kết quả chi tiết</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Career Development Planner -->
        <section class="section" id="career">
            <div class="container">
                <div class="career-planner">
                    <h2>Career Development Planner - Kế hoạch phát triển nghề nghiệp</h2>
                    <p>Xây dựng lộ trình phát triển dựa trên MST Framework</p>

                    <div class="planner-sections">
                        <div class="card">
                            <div class="card__body">
                                <h3>Thiết lập mục tiêu</h3>
                                <form id="goalSettingForm">
                                    <div class="form-group">
                                        <label class="form-label">Mục tiêu nghề nghiệp (1-2 năm):</label>
                                        <textarea class="form-control" name="careerGoal" rows="2" placeholder="Ví dụ: Trở thành Senior Software Architect..."></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Vị trí hiện tại:</label>
                                        <input type="text" class="form-control" name="currentPosition" placeholder="Ví dụ: Senior Developer">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Kỹ năng cần phát triển:</label>
                                        <textarea class="form-control" name="skillsToLearn" rows="3" placeholder="Liệt kê các kỹ năng cụ thể..."></textarea>
                                    </div>
                                    <button type="submit" class="btn btn--primary">Lưu mục tiêu</button>
                                </form>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card__body">
                                <h3>Lộ trình học tập</h3>
                                <div class="learning-path" id="learningPath">
                                    <div class="path-item">
                                        <div class="path-step">1</div>
                                        <div class="path-content">
                                            <h4>Mindset Development</h4>
                                            <p>Phát triển tư duy lãnh đạo và strategic thinking</p>
                                            <div class="path-resources">
                                                <span class="resource-tag">Books</span>
                                                <span class="resource-tag">Courses</span>
                                                <span class="resource-tag">Mentorship</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="path-item">
                                        <div class="path-step">2</div>
                                        <div class="path-content">
                                            <h4>Advanced Technical Skills</h4>
                                            <p>Nâng cao kỹ năng system design và architecture</p>
                                            <div class="path-resources">
                                                <span class="resource-tag">Practice</span>
                                                <span class="resource-tag">Projects</span>
                                                <span class="resource-tag">Certification</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="path-item">
                                        <div class="path-step">3</div>
                                        <div class="path-content">
                                            <h4>Tool Mastery</h4>
                                            <p>Làm chủ các công cụ enterprise và cloud platforms</p>
                                            <div class="path-resources">
                                                <span class="resource-tag">Hands-on</span>
                                                <span class="resource-tag">Labs</span>
                                                <span class="resource-tag">Projects</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card__body">
                                <h3>Theo dõi tiến độ</h3>
                                <div class="progress-tracker">
                                    <div class="milestone">
                                        <input type="checkbox" id="milestone1">
                                        <label for="milestone1">Hoàn thành đánh giá MST Framework</label>
                                    </div>
                                    <div class="milestone">
                                        <input type="checkbox" id="milestone2">
                                        <label for="milestone2">Xác định 3 kỹ năng cần phát triển</label>
                                    </div>
                                    <div class="milestone">
                                        <input type="checkbox" id="milestone3">
                                        <label for="milestone3">Bắt đầu khóa học/tài liệu đầu tiên</label>
                                    </div>
                                    <div class="milestone">
                                        <input type="checkbox" id="milestone4">
                                        <label for="milestone4">Áp dụng framework vào dự án thực tế</label>
                                    </div>
                                    <div class="milestone">
                                        <input type="checkbox" id="milestone5">
                                        <label for="milestone5">Tìm mentor hoặc coaching</label>
                                    </div>
                                </div>
                                <div class="progress-summary">
                                    <span>Tiến độ: <strong id="progressPercentage">0%</strong></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reference Library -->
        <section class="section" id="library">
            <div class="container">
                <div class="reference-library">
                    <h2>Reference Library - Thư viện tham khảo</h2>
                    
                    <div class="library-search">
                        <input type="text" class="form-control" id="librarySearch" placeholder="Tìm kiếm khái niệm, kỹ năng, công cụ...">
                    </div>

                    <div class="library-tabs">
                        <div class="tabs">
                            <button class="tab-btn active" data-tab="concepts">Khái niệm</button>
                            <button class="tab-btn" data-tab="skills">Kỹ năng</button>
                            <button class="tab-btn" data-tab="tools">Công cụ</button>
                            <button class="tab-btn" data-tab="practices">Best Practices</button>
                        </div>

                        <div class="tab-content active" id="concepts">
                            <div class="concept-library">
                                <div class="concept-group">
                                    <h3>MST Framework</h3>
                                    <div class="concept-items">
                                        <div class="concept-item">
                                            <h4>Mindset</h4>
                                            <p>Nền tảng định hướng bao gồm hệ thống niềm tin, giá trị, thái độ và cách tiếp cận vấn đề. Đây là yếu tố quyết định cách bạn nhìn nhận và phản ứng với thách thức.</p>
                                        </div>
                                        <div class="concept-item">
                                            <h4>Skillset</h4>
                                            <p>Tập hợp năng lực thực thi gồm các kỹ năng cứng và mềm cần thiết để chuyển hóa tư duy thành hành động hiệu quả.</p>
                                        </div>
                                        <div class="concept-item">
                                            <h4>Toolset</h4>
                                            <p>Phương tiện khuếch đại gồm các công cụ, phương pháp, công nghệ được lựa chọn để tối ưu hóa hiệu quả thực thi.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="concept-group">
                                    <h3>Software Engineering Concepts</h3>
                                    <div class="concept-items">
                                        <div class="concept-item">
                                            <h4>System Architecture</h4>
                                            <p>Thiết kế tổng thể của hệ thống bao gồm các thành phần, mối quan hệ và nguyên tắc quản lý sự phát triển.</p>
                                        </div>
                                        <div class="concept-item">
                                            <h4>Technical Debt</h4>
                                            <p>Chi phí bổ sung phát sinh từ việc chọn giải pháp nhanh thay vì phương pháp tốt hơn nhưng mất nhiều thời gian hơn.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-content" id="skills">
                            <div class="skills-library">
                                <div class="skill-category">
                                    <h3>Kỹ năng tư duy</h3>
                                    <ul>
                                        <li><strong>Tư duy phản biện:</strong> Khả năng phân tích, đánh giá và tổng hợp thông tin một cách khách quan</li>
                                        <li><strong>Tư duy hệ thống:</strong> Nhìn nhận vấn đề trong bối cảnh tổng thể và mối liên hệ</li>
                                        <li><strong>Tư duy sáng tạo:</strong> Tạo ra ý tưởng mới và giải pháp độc đáo</li>
                                        <li><strong>Tư duy chiến lược:</strong> Lập kế hoạch dài hạn và định hướng tương lai</li>
                                    </ul>
                                </div>
                                <div class="skill-category">
                                    <h3>Kỹ năng giao tiếp</h3>
                                    <ul>
                                        <li><strong>Lắng nghe tích cực:</strong> Thu nhận thông tin một cách toàn diện và empathy</li>
                                        <li><strong>Truyền đạt rõ ràng:</strong> Diễn đạt ý tưởng chính xác và dễ hiểu</li>
                                        <li><strong>Thuyết phục:</strong> Ảnh hưởng và tạo sự đồng thuận</li>
                                        <li><strong>Giao tiếp đa văn hóa:</strong> Làm việc hiệu quả với người từ nhiều nền văn hóa</li>
                                    </ul>
                                </div>
                                <div class="skill-category">
                                    <h3>Kỹ năng thích nghi</h3>
                                    <ul>
                                        <li><strong>Học nhanh:</strong> Tiếp thu kiến thức và kỹ năng mới một cách hiệu quả</li>
                                        <li><strong>Linh hoạt:</strong> Điều chỉnh phương pháp theo hoàn cảnh</li>
                                        <li><strong>Phục hồi:</strong> Vượt qua thất bại và khó khăn</li>
                                        <li><strong>Quản lý thay đổi:</strong> Dẫn dắt và thích ứng với sự thay đổi</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="tab-content" id="tools">
                            <div class="tools-library">
                                <div class="tool-category">
                                    <h3>Development Tools</h3>
                                    <div class="tool-items">
                                        <div class="tool-item">
                                            <h4>IDEs & Editors</h4>
                                            <p>Visual Studio Code, IntelliJ IDEA, Eclipse</p>
                                            <div class="tool-criteria">
                                                <span class="criterion">✓ Flexibility</span>
                                                <span class="criterion">✓ Plugin Support</span>
                                            </div>
                                        </div>
                                        <div class="tool-item">
                                            <h4>Version Control</h4>
                                            <p>Git, GitHub, GitLab, Bitbucket</p>
                                            <div class="tool-criteria">
                                                <span class="criterion">✓ Collaboration</span>
                                                <span class="criterion">✓ History Tracking</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="tool-category">
                                    <h3>Cloud Platforms</h3>
                                    <div class="tool-items">
                                        <div class="tool-item">
                                            <h4>AWS</h4>
                                            <p>Comprehensive cloud services platform</p>
                                            <div class="tool-criteria">
                                                <span class="criterion">✓ Scalability</span>
                                                <span class="criterion">✓ Reliability</span>
                                            </div>
                                        </div>
                                        <div class="tool-item">
                                            <h4>Azure</h4>
                                            <p>Microsoft's cloud platform with enterprise focus</p>
                                            <div class="tool-criteria">
                                                <span class="criterion">✓ Integration</span>
                                                <span class="criterion">✓ Hybrid Support</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-content" id="practices">
                            <div class="practices-library">
                                <div class="practice-group">
                                    <h3>MST Framework Best Practices</h3>
                                    <ul class="practice-list">
                                        <li>Luôn bắt đầu với Mindset - xác định rõ mục đích và giá trị cốt lõi</li>
                                        <li>Phát triển Skillset dựa trên nhu cầu thực tế, không theo trend</li>
                                        <li>Chọn Toolset phù hợp với bối cảnh, không phụ thuộc vào một công nghệ</li>
                                        <li>Thường xuyên đánh giá và điều chỉnh cả 3 thành phần</li>
                                        <li>Ưu tiên việc phát triển kỹ năng lâu dài hơn là công cụ tạm thời</li>
                                    </ul>
                                </div>
                                <div class="practice-group">
                                    <h3>Common Pitfalls</h3>
                                    <ul class="pitfall-list">
                                        <li><strong>Tool-first thinking:</strong> Chọn công cụ trước khi hiểu vấn đề</li>
                                        <li><strong>Skill silos:</strong> Phát triển kỹ năng riêng lẻ mà không tích hợp</li>
                                        <li><strong>Fixed mindset:</strong> Tin rằng khả năng là cố định và không thể thay đổi</li>
                                        <li><strong>Technology dependency:</strong> Phụ thuộc quá mức vào một công nghệ cụ thể</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Practice Section -->
        <section class="section" id="practice">
            <div class="container">
                <div class="practice-scenarios">
                    <h2>Practice Scenarios - Tình huống thực hành</h2>
                    <p>Luyện tập áp dụng MST Framework với các tình huống thực tế</p>

                    <div class="scenarios-grid">
                        <div class="card scenario-card" data-scenario="api-design">
                            <div class="card__body">
                                <h3>🔧 API Design Challenge</h3>
                                <p>Thiết kế API cho một hệ thống e-commerce với millions users</p>
                                <div class="scenario-meta">
                                    <span class="difficulty">Trung bình</span>
                                    <span class="time">45 phút</span>
                                </div>
                                <button class="btn btn--primary" onclick="startScenario('api-design')">Bắt đầu</button>
                            </div>
                        </div>

                        <div class="card scenario-card" data-scenario="legacy-migration">
                            <div class="card__body">
                                <h3>🔄 Legacy System Migration</h3>
                                <p>Migrate hệ thống legacy 10 năm tuổi sang microservices</p>
                                <div class="scenario-meta">
                                    <span class="difficulty">Khó</span>
                                    <span class="time">60 phút</span>
                                </div>
                                <button class="btn btn--primary" onclick="startScenario('legacy-migration')">Bắt đầu</button>
                            </div>
                        </div>

                        <div class="card scenario-card" data-scenario="team-conflict">
                            <div class="card__body">
                                <h3>👥 Team Conflict Resolution</h3>
                                <p>Giải quyết xung đột giữa dev team và product team về priorities</p>
                                <div class="scenario-meta">
                                    <span class="difficulty">Trung bình</span>
                                    <span class="time">30 phút</span>
                                </div>
                                <button class="btn btn--primary" onclick="startScenario('team-conflict')">Bắt đầu</button>
                            </div>
                        </div>

                        <div class="card scenario-card" data-scenario="performance-crisis">
                            <div class="card__body">
                                <h3>⚡ Performance Crisis</h3>
                                <p>Hệ thống production bị chậm 300%, customer phàn nàn nhiều</p>
                                <div class="scenario-meta">
                                    <span class="difficulty">Khó</span>
                                    <span class="time">45 phút</span>
                                </div>
                                <button class="btn btn--primary" onclick="startScenario('performance-crisis')">Bắt đầu</button>
                            </div>
                        </div>
                    </div>

                    <div class="practice-results">
                        <h3>Kết quả luyện tập</h3>
                        <div class="results-summary" id="practiceResults">
                            <p>Chưa có kết quả luyện tập nào</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Scenario Modal -->
    <div class="modal hidden" id="scenarioModal">
        <div class="modal-overlay" onclick="closeModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Scenario Details</h3>
                <button class="modal-close" onclick="closeModal()">×</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 MST Framework Handbook. Designed for Software Engineers' Lifelong Learning.</p>
        </div>
    </footer>

    <script src="app.js"></script>
</body>
</html>