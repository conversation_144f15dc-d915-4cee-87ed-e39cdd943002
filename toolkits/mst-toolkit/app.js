// MST Framework Handbook - Interactive Application
class MSTFrameworkApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.currentLanguage = 'vi';
        this.wizardStep = 1;
        this.assessmentData = {
            mindset: null,
            skillset: null,
            toolset: null
        };
        this.userProgress = this.loadProgress();
        this.savedMappings = this.loadMappings();
        this.careerGoals = this.loadCareerGoals();
        
        // Initialize immediately if DOM is ready, otherwise wait for DOMContentLoaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            // Add small delay to ensure all elements are rendered
            setTimeout(() => this.init(), 50);
        }
    }

    init() {
        console.log('Initializing MST Framework App...');
        try {
            this.initTheme();
            this.setupNavigation();
            this.setupDashboard();
            this.setupFrameworkOverview();
            this.setupProblemMapping();
            this.setupSoftwareApplications();
            this.setupAssessments();
            this.setupCareerPlanner();
            this.setupLibrary();
            this.setupPracticeScenarios();
            this.setupModals();
            this.setupLanguageToggle();
            this.setupDataExport();
            this.updateProgressBars();
            this.loadRecentActivities();
            this.loadAssessmentData();
            this.updatePracticeResults();
            console.log('MST Framework App initialized successfully');
        } catch (error) {
            console.error('Error initializing app:', error);
        }
    }

    // Theme functionality
    initTheme() {
        // Set light theme as default per project specification
        this.setTheme('light');
        
        // Listen for theme changes from parent (unified toolkit)
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'THEME_CHANGE') {
                this.setTheme('light'); // Always use light theme
            }
        });
        
        console.log('Theme system initialized with light theme');
    }

    setTheme(theme) {
        // Force light theme per project specification
        document.documentElement.setAttribute('data-color-scheme', 'light');
        localStorage.setItem('unifiedToolkit.theme', 'light');
    }

    toggleTheme() {
        // Theme toggle functionality removed - using light theme only per project specification
    }

    // Navigation System
    setupNavigation() {
        console.log('Setting up navigation...');
        
        // Main navigation items
        const navItems = document.querySelectorAll('.nav__item[data-section]');
        console.log('Found navigation items:', navItems.length);
        
        if (navItems.length === 0) {
            console.error('No navigation items found with data-section attribute');
            return;
        }

        navItems.forEach((item, index) => {
            const section = item.getAttribute('data-section');
            console.log(`Setting up nav item ${index}: ${section}`);
            
            // Remove any existing listeners
            item.replaceWith(item.cloneNode(true));
            const newItem = document.querySelectorAll('.nav__item[data-section]')[index];
            
            newItem.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Navigation clicked:', section);
                this.switchSection(section);
                this.addActivity(`Đã xem ${this.getSectionName(section)}`);
            });
        });

        // Mobile navigation toggle
        const navToggle = document.getElementById('navToggle');
        const navList = document.getElementById('navList');
        
        if (navToggle && navList) {
            navToggle.addEventListener('click', (e) => {
                e.preventDefault();
                navList.classList.toggle('open');
                console.log('Mobile nav toggled');
            });
        }

        // Quick actions on dashboard
        const quickActions = document.querySelectorAll('[data-action]');
        console.log('Found quick actions:', quickActions.length);
        
        quickActions.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const action = e.target.getAttribute('data-action');
                console.log('Quick action clicked:', action);
                this.switchSection(action);
            });
        });

        // Make sure dashboard is visible by default
        this.switchSection('dashboard');
        
        console.log('Navigation setup completed');
    }

    switchSection(sectionId) {
        console.log('Switching to section:', sectionId);
        
        try {
            // Hide all sections
            const allSections = document.querySelectorAll('.section');
            console.log('Found sections:', allSections.length);
            
            allSections.forEach(section => {
                section.classList.remove('active');
            });
            
            // Show target section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
                console.log('Section switched successfully to:', sectionId);
                
                // Add fade animation
                targetSection.style.opacity = '0';
                setTimeout(() => {
                    targetSection.style.opacity = '1';
                }, 10);
            } else {
                console.error('Target section not found:', sectionId);
                console.log('Available section IDs:', Array.from(allSections).map(s => s.id));
                return;
            }

            // Update navigation highlighting
            const navItems = document.querySelectorAll('.nav__item');
            navItems.forEach(item => {
                item.classList.remove('active');
            });
            
            const activeNavItem = document.querySelector(`[data-section="${sectionId}"]`);
            if (activeNavItem) {
                activeNavItem.classList.add('active');
                console.log('Navigation item highlighted:', sectionId);
            }

            this.currentSection = sectionId;
            
            // Close mobile menu if open
            const navList = document.getElementById('navList');
            if (navList && navList.classList.contains('open')) {
                navList.classList.remove('open');
            }

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
            
        } catch (error) {
            console.error('Error switching section:', error);
        }
    }

    getSectionName(sectionId) {
        const names = {
            'dashboard': 'Dashboard',
            'framework': 'Framework Overview',
            'mapping': 'Problem Mapping',
            'software': 'Software Engineering',
            'assessment': 'Self-Assessment',
            'career': 'Career Planner',
            'library': 'Reference Library',
            'practice': 'Practice Scenarios'
        };
        return names[sectionId] || sectionId;
    }

    // Dashboard functionality
    setupDashboard() {
        console.log('Setting up dashboard...');
        const quickMappingForm = document.getElementById('quickMappingForm');
        
        if (quickMappingForm) {
            quickMappingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const problemInput = document.getElementById('quickProblem');
                const problem = problemInput ? problemInput.value.trim() : '';
                console.log('Quick mapping submitted:', problem);
                
                if (problem) {
                    // Switch to mapping section first
                    this.switchSection('mapping');
                    
                    // Pre-fill problem mapping wizard after a short delay
                    setTimeout(() => {
                        const problemDescInput = document.querySelector('[name="problemDescription"]');
                        if (problemDescInput) {
                            problemDescInput.value = problem;
                            console.log('Pre-filled problem description');
                        }
                    }, 200);
                    
                    this.addActivity(`Bắt đầu phân tích: "${problem.substring(0, 30)}..."`);
                    if (problemInput) problemInput.value = '';
                }
            });
            console.log('Dashboard form setup completed');
        }
    }

    // Framework Overview
    setupFrameworkOverview() {
        console.log('Setting up framework overview...');
        
        // Tab switching
        const tabBtns = document.querySelectorAll('.framework-overview .tab-btn');
        const tabContents = document.querySelectorAll('.framework-overview .tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = e.target.getAttribute('data-tab');
                console.log('Framework tab clicked:', tabId);
                
                // Remove active from all tabs
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active to clicked tab
                e.target.classList.add('active');
                const targetTab = document.getElementById(tabId);
                if (targetTab) {
                    targetTab.classList.add('active');
                    console.log('Tab activated:', tabId);
                }
                
                this.updateProgress('framework', 25);
            });
        });

        // Interactive MST components
        const mstComponents = document.querySelectorAll('.mst-component');
        console.log('Found MST components:', mstComponents.length);
        
        mstComponents.forEach(component => {
            component.addEventListener('click', (e) => {
                console.log('MST component clicked');
                this.showComponentDetails(e.target.closest('.mst-component'));
            });
        });
        
        console.log('Framework overview setup completed');
    }

    showComponentDetails(component) {
        if (!component) return;
        
        const componentType = component.classList.contains('mst-mindset') ? 'mindset' :
                            component.classList.contains('mst-skillset') ? 'skillset' : 'toolset';
        
        console.log('Showing details for:', componentType);
        
        const details = {
            mindset: {
                title: 'Mindset - Tư duy định hướng',
                content: `
                    <h4>Đặc điểm chính:</h4>
                    <ul>
                        <li>Tư duy phát triển (Growth Mindset)</li>
                        <li>Tư duy hệ thống (Systems Thinking)</li>
                        <li>Tư duy thích nghi (Adaptive Mindset)</li>
                        <li>Tư duy đạo đức (Ethical Thinking)</li>
                    </ul>
                    <h4>Trong Software Engineering:</h4>
                    <p>Architectural design principles như modularity, maintainability, và scalability là biểu hiện của mindset trong việc thiết kế hệ thống.</p>
                `
            },
            skillset: {
                title: 'Skillset - Kỹ năng thực thi',
                content: `
                    <h4>Các loại kỹ năng:</h4>
                    <ul>
                        <li>Kỹ năng bất hủ (Timeless skills)</li>
                        <li>Kỹ năng thích nghi (Adaptive skills)</li>
                        <li>Kỹ năng cảm xúc (Emotional skills)</li>
                        <li>Kỹ năng nhận thức (Cognitive skills)</li>
                    </ul>
                    <h4>Trong Software Engineering:</h4>
                    <p>System design patterns, API design, algorithms và business logic development là những kỹ năng cụ thể cần phát triển.</p>
                `
            },
            toolset: {
                title: 'Toolset - Công cụ khuếch đại',
                content: `
                    <h4>Tiêu chí lựa chọn:</h4>
                    <ul>
                        <li>Tính linh hoạt (Flexibility)</li>
                        <li>Khả năng tương tác (Interoperability)</li>
                        <li>Tính bền vững (Sustainability)</li>
                        <li>Khả năng mở rộng (Scalability)</li>
                    </ul>
                    <h4>Trong Software Engineering:</h4>
                    <p>Programming frameworks, cloud infrastructure, DevOps tools và libraries được chọn lựa dựa trên các tiêu chí này.</p>
                `
            }
        };

        if (details[componentType]) {
            this.showModal(details[componentType].title, details[componentType].content);
        }
    }

    // Problem Mapping Wizard
    setupProblemMapping() {
        console.log('Setting up problem mapping...');
        
        const wizardForm = document.getElementById('wizardForm');
        const nextBtn = document.getElementById('nextStep');
        const prevBtn = document.getElementById('prevStep');
        const submitBtn = document.getElementById('submitMapping');

        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Next step clicked');
                this.nextWizardStep();
            });
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Previous step clicked');
                this.prevWizardStep();
            });
        }
        
        if (wizardForm) {
            wizardForm.addEventListener('submit', (e) => {
                e.preventDefault();
                console.log('Wizard form submitted');
                this.saveMappingAnalysis();
            });
        }

        this.updateWizardUI();
        console.log('Problem mapping setup completed');
    }

    nextWizardStep() {
        if (this.wizardStep < 6) {
            this.wizardStep++;
            console.log('Moving to wizard step:', this.wizardStep);
            this.updateWizardUI();
            this.updateProgress('mapping', this.wizardStep * 16.67);
        }
    }

    prevWizardStep() {
        if (this.wizardStep > 1) {
            this.wizardStep--;
            console.log('Moving to wizard step:', this.wizardStep);
            this.updateWizardUI();
        }
    }

    updateWizardUI() {
        console.log('Updating wizard UI for step:', this.wizardStep);
        
        // Hide all steps
        document.querySelectorAll('.wizard-step').forEach(step => {
            step.classList.remove('active');
        });

        // Show current step
        const currentStep = document.getElementById(`step${this.wizardStep}`);
        if (currentStep) {
            currentStep.classList.add('active');
            console.log('Activated wizard step:', this.wizardStep);
        }

        // Update progress indicators
        document.querySelectorAll('.progress-steps .step').forEach((step, index) => {
            step.classList.remove('active', 'completed');
            if (index + 1 === this.wizardStep) {
                step.classList.add('active');
            } else if (index + 1 < this.wizardStep) {
                step.classList.add('completed');
            }
        });

        // Update buttons
        const prevBtn = document.getElementById('prevStep');
        const nextBtn = document.getElementById('nextStep');
        const submitBtn = document.getElementById('submitMapping');

        if (prevBtn) prevBtn.disabled = this.wizardStep === 1;
        
        if (this.wizardStep === 6) {
            if (nextBtn) nextBtn.classList.add('hidden');
            if (submitBtn) submitBtn.classList.remove('hidden');
        } else {
            if (nextBtn) nextBtn.classList.remove('hidden');
            if (submitBtn) submitBtn.classList.add('hidden');
        }
    }

    saveMappingAnalysis() {
        console.log('Saving mapping analysis...');
        const wizardForm = document.getElementById('wizardForm');
        if (!wizardForm) {
            console.error('Wizard form not found');
            return;
        }

        const formData = new FormData(wizardForm);
        const analysis = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            problem: formData.get('problemDescription') || 'Chưa mô tả',
            type: formData.get('problemType') || 'Chưa xác định',
            priority: formData.get('priority') || 'medium',
            mindset: {
                current: formData.get('currentMindset') || '',
                needed: formData.getAll('neededMindset') || [],
                barriers: formData.get('mindsetBarriers') || ''
            },
            skillset: {
                current: formData.get('currentSkills') || '',
                needed: formData.get('neededSkills') || '',
                gaps: {
                    technical: formData.get('technicalGap') || 'none',
                    management: formData.get('managementGap') || 'none',
                    communication: formData.get('communicationGap') || 'none'
                }
            },
            toolset: {
                current: formData.get('currentTools') || '',
                criteria: formData.getAll('toolCriteria') || [],
                recommended: formData.get('recommendedTools') || ''
            },
            implementation: {
                objectives: formData.get('objectives') || '',
                timeline: formData.get('timeline') || '1month',
                steps: formData.get('actionSteps') || '',
                resources: formData.get('resources') || ''
            },
            evaluation: {
                metrics: formData.get('successMetrics') || '',
                frequency: formData.get('reviewFrequency') || 'weekly',
                adjustmentPlan: formData.get('adjustmentPlan') || ''
            }
        };

        this.savedMappings.push(analysis);
        this.saveMappings();
        this.displaySavedMappings();
        this.resetWizard();
        this.addActivity(`Hoàn thành phân tích: "${analysis.problem.substring(0, 30)}..."`);
        
        alert('Phân tích đã được lưu thành công!');
        console.log('Mapping analysis saved successfully');
    }

    displaySavedMappings() {
        const container = document.getElementById('savedMappings');
        
        if (!container) {
            console.log('Saved mappings container not found');
            return;
        }

        if (this.savedMappings.length === 0) {
            container.innerHTML = '<p>Chưa có phân tích nào được lưu</p>';
            return;
        }

        const mappingsHTML = this.savedMappings.map(mapping => `
            <div class="mapping-item">
                <h4>${mapping.problem}</h4>
                <p><strong>Loại:</strong> ${mapping.type} | <strong>Ưu tiên:</strong> ${mapping.priority}</p>
                <p><strong>Thời gian:</strong> ${new Date(mapping.timestamp).toLocaleDateString('vi-VN')}</p>
                <button class="btn btn--outline btn--sm" onclick="window.app.viewMappingDetails(${mapping.id})">Xem chi tiết</button>
                <button class="btn btn--outline btn--sm" onclick="window.app.deleteMappingAnalysis(${mapping.id})">Xóa</button>
            </div>
        `).join('');

        container.innerHTML = mappingsHTML;
        console.log('Displayed saved mappings:', this.savedMappings.length);
    }

    viewMappingDetails(id) {
        const mapping = this.savedMappings.find(m => m.id === id);
        if (mapping) {
            const content = `
                <h4>Phân tích vấn đề: ${mapping.problem}</h4>
                <div class="mapping-details">
                    <h5>Mindset</h5>
                    <p><strong>Hiện tại:</strong> ${mapping.mindset.current}</p>
                    <p><strong>Cần thiết:</strong> ${mapping.mindset.needed.join(', ')}</p>
                    
                    <h5>Skillset</h5>
                    <p><strong>Hiện có:</strong> ${mapping.skillset.current}</p>
                    <p><strong>Cần phát triển:</strong> ${mapping.skillset.needed}</p>
                    
                    <h5>Toolset</h5>
                    <p><strong>Hiện tại:</strong> ${mapping.toolset.current}</p>
                    <p><strong>Đề xuất:</strong> ${mapping.toolset.recommended}</p>
                    
                    <h5>Kế hoạch thực hiện</h5>
                    <p><strong>Mục tiêu:</strong> ${mapping.implementation.objectives}</p>
                    <p><strong>Thời gian:</strong> ${mapping.implementation.timeline}</p>
                </div>
            `;
            this.showModal('Chi tiết phân tích', content);
        }
    }

    deleteMappingAnalysis(id) {
        if (confirm('Bạn có chắc chắn muốn xóa phân tích này?')) {
            this.savedMappings = this.savedMappings.filter(m => m.id !== id);
            this.saveMappings();
            this.displaySavedMappings();
            console.log('Deleted mapping analysis:', id);
        }
    }

    resetWizard() {
        this.wizardStep = 1;
        this.updateWizardUI();
        const wizardForm = document.getElementById('wizardForm');
        if (wizardForm) {
            wizardForm.reset();
        }
        console.log('Wizard reset');
    }

    // Software Engineering Applications
    setupSoftwareApplications() {
        console.log('Setting up software applications...');
        // Make openScenario globally available
        window.openScenario = (scenarioType) => {
            console.log('Opening scenario:', scenarioType);
            this.openScenario(scenarioType);
        };
    }

    openScenario(scenarioType) {
        const scenarios = {
            'architecture': {
                title: 'System Architecture Design',
                content: `
                    <h4>Tình huống:</h4>
                    <p>Bạn được giao nhiệm vụ thiết kế kiến trúc cho một hệ thống e-commerce với yêu cầu xử lý hàng triệu user đồng thời.</p>
                    
                    <h4>Áp dụng MST Framework:</h4>
                    <div class="scenario-analysis">
                        <h5>Mindset cần thiết:</h5>
                        <ul>
                            <li>Tư duy modular - phân chia hệ thống thành các thành phần độc lập</li>
                            <li>Scalability-first approach - ưu tiên khả năng mở rộng từ đầu</li>
                            <li>Performance-oriented thinking - tối ưu hiệu suất</li>
                        </ul>
                        
                        <h5>Skillset cần áp dụng:</h5>
                        <ul>
                            <li>System design patterns (Microservices, Event-driven architecture)</li>
                            <li>API design và versioning</li>
                            <li>Performance optimization techniques</li>
                            <li>Load balancing và caching strategies</li>
                        </ul>
                        
                        <h5>Toolset phù hợp:</h5>
                        <ul>
                            <li>Cloud platforms (AWS, Azure, GCP)</li>
                            <li>Containerization (Docker, Kubernetes)</li>
                            <li>Message queues (RabbitMQ, Apache Kafka)</li>
                            <li>Databases (NoSQL, Distributed databases)</li>
                        </ul>
                    </div>
                    
                    <h4>Thực hành:</h4>
                    <p>Hãy vẽ sơ đồ kiến trúc cho hệ thống này và giải thích lựa chọn của bạn dựa trên MST framework.</p>
                `
            },
            'debt': {
                title: 'Technical Debt Management',
                content: `
                    <h4>Tình huống:</h4>
                    <p>Dự án của bạn đã tích lũy nhiều technical debt qua 2 năm phát triển. Performance giảm, bugs tăng, và team khó implement features mới.</p>
                    
                    <h4>Áp dụng MST Framework:</h4>
                    <div class="scenario-analysis">
                        <h5>Mindset cần thiết:</h5>
                        <ul>
                            <li>Long-term sustainability over short-term gains</li>
                            <li>Quality-first approach</li>
                            <li>Collaborative problem-solving</li>
                        </ul>
                        
                        <h5>Skillset cần áp dụng:</h5>
                        <ul>
                            <li>Code review và quality assessment</li>
                            <li>Refactoring techniques</li>
                            <li>Technical communication với stakeholders</li>
                            <li>Priority management</li>
                        </ul>
                        
                        <h5>Toolset phù hợp:</h5>
                        <ul>
                            <li>Static analysis tools (SonarQube, CodeClimate)</li>
                            <li>Testing frameworks (Unit, Integration tests)</li>
                            <li>Documentation systems (Confluence, GitBook)</li>
                            <li>Monitoring tools (Performance tracking)</li>
                        </ul>
                    </div>
                    
                    <h4>Hành động cụ thể:</h4>
                    <ol>
                        <li>Đánh giá và ưu tiên technical debt</li>
                        <li>Lên kế hoạch refactoring từng bước</li>
                        <li>Thiết lập quy trình code quality</li>
                        <li>Đào tạo team về best practices</li>
                    </ol>
                `
            },
            'transition': {
                title: 'Career Transition: Developer → Architect',
                content: `
                    <h4>Tình huống:</h4>
                    <p>Bạn là Senior Developer muốn chuyển sang vai trò Software Architect. Cần chuẩn bị những gì?</p>
                    
                    <h4>Áp dụng MST Framework:</h4>
                    <div class="scenario-analysis">
                        <h5>Mindset transformation:</h5>
                        <ul>
                            <li>Từ "code-first" sang "design-first" thinking</li>
                            <li>Leadership orientation - dẫn dắt technical decisions</li>
                            <li>Business alignment - hiểu impact của technical choices</li>
                            <li>Mentoring mindset - hỗ trợ team phát triển</li>
                        </ul>
                        
                        <h5>Skillset cần phát triển:</h5>
                        <ul>
                            <li>High-level system design</li>
                            <li>Technology evaluation và decision making</li>
                            <li>Team management và communication</li>
                            <li>Strategic thinking và planning</li>
                        </ul>
                        
                        <h5>Toolset mới cần học:</h5>
                        <ul>
                            <li>Architecture documentation tools</li>
                            <li>Project management platforms</li>
                            <li>Communication và collaboration tools</li>
                            <li>Metrics và monitoring dashboards</li>
                        </ul>
                    </div>
                    
                    <h4>Lộ trình 6 tháng:</h4>
                    <ol>
                        <li>Tháng 1-2: Học system design patterns</li>
                        <li>Tháng 3-4: Practice với real-world architecture problems</li>
                        <li>Tháng 5-6: Lead một technical project nhỏ</li>
                    </ol>
                `
            }
        };

        const scenario = scenarios[scenarioType];
        if (scenario) {
            this.showModal(scenario.title, scenario.content);
            this.addActivity(`Học tình huống: ${scenario.title}`);
        }
    }

    // Assessment Tools
    setupAssessments() {
        console.log('Setting up assessments...');
        
        // Assessment tabs
        const assessmentTabBtns = document.querySelectorAll('.assessment-tabs .tab-btn');
        const assessmentTabContents = document.querySelectorAll('.assessment-tabs .tab-content');

        assessmentTabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = e.target.getAttribute('data-tab');
                console.log('Assessment tab clicked:', tabId);
                
                assessmentTabBtns.forEach(b => b.classList.remove('active'));
                assessmentTabContents.forEach(c => c.classList.remove('active'));
                
                e.target.classList.add('active');
                const targetTab = document.getElementById(tabId);
                if (targetTab) {
                    targetTab.classList.add('active');
                    console.log('Assessment tab activated:', tabId);
                }
            });
        });

        // Assessment forms
        const mindsetForm = document.getElementById('mindsetAssessment');
        const skillsetForm = document.getElementById('skillsetAssessment');
        const toolsetForm = document.getElementById('toolsetAssessment');

        if (mindsetForm) {
            mindsetForm.addEventListener('submit', (e) => {
                e.preventDefault();
                console.log('Mindset assessment submitted');
                this.processMindsetAssessment(e.target);
            });
        }

        if (skillsetForm) {
            skillsetForm.addEventListener('submit', (e) => {
                e.preventDefault();
                console.log('Skillset assessment submitted');
                this.processSkillsetAssessment(e.target);
            });
        }

        if (toolsetForm) {
            toolsetForm.addEventListener('submit', (e) => {
                e.preventDefault();
                console.log('Toolset assessment submitted');
                this.processToolsetAssessment(e.target);
            });
        }
        
        console.log('Assessments setup completed');
    }

    processMindsetAssessment(form) {
        const formData = new FormData(form);
        
        const scores = {
            growthMindset: parseInt(formData.get('q1')) || 3,
            resilience: parseInt(formData.get('q2')) || 3,
            feedbackSeeking: parseInt(formData.get('q3')) || 3
        };

        const averageScore = (scores.growthMindset + scores.resilience + scores.feedbackSeeking) / 3;
        
        this.assessmentData.mindset = {
            scores,
            average: averageScore,
            level: this.getAssessmentLevel(averageScore),
            timestamp: new Date().toISOString()
        };

        this.saveAssessmentData();
        this.updateAssessmentResults();
        this.updateProgress('assessment', 33);
        this.addActivity('Hoàn thành đánh giá Mindset');
        
        // Switch to results tab
        this.switchToResultsTab();
        console.log('Mindset assessment processed');
    }

    processSkillsetAssessment(form) {
        const formData = new FormData(form);
        
        const technicalSkills = {
            programming: parseInt(formData.get('programming')) || 3,
            systemDesign: parseInt(formData.get('systemDesign')) || 3,
            database: parseInt(formData.get('database')) || 3
        };

        const softSkills = {
            communication: parseInt(formData.get('communication')) || 3,
            problemSolving: parseInt(formData.get('problemSolving')) || 3,
            leadership: parseInt(formData.get('leadership')) || 3
        };

        const technicalAvg = Object.values(technicalSkills).reduce((a, b) => a + b, 0) / 3;
        const softAvg = Object.values(softSkills).reduce((a, b) => a + b, 0) / 3;
        const overallAvg = (technicalAvg + softAvg) / 2;

        this.assessmentData.skillset = {
            technical: { skills: technicalSkills, average: technicalAvg },
            soft: { skills: softSkills, average: softAvg },
            overall: overallAvg,
            level: this.getAssessmentLevel(overallAvg),
            improvementAreas: formData.get('skillsToImprove') || '',
            timestamp: new Date().toISOString()
        };

        this.saveAssessmentData();
        this.updateAssessmentResults();
        this.updateProgress('assessment', 67);
        this.addActivity('Hoàn thành đánh giá Skillset');
        
        // Switch to results tab
        this.switchToResultsTab();
        console.log('Skillset assessment processed');
    }

    processToolsetAssessment(form) {
        const formData = new FormData(form);
        
        const criteria = formData.getAll('toolCriteria');
        const reviewFrequency = parseInt(formData.get('toolReview')) || 3;
        const changeStrategy = parseInt(formData.get('toolChange')) || 3;
        
        const averageScore = (reviewFrequency + changeStrategy) / 2;

        this.assessmentData.toolset = {
            criteria,
            reviewFrequency,
            changeStrategy,
            average: averageScore,
            level: this.getAssessmentLevel(averageScore),
            timestamp: new Date().toISOString()
        };

        this.saveAssessmentData();
        this.updateAssessmentResults();
        this.updateProgress('assessment', 100);
        this.addActivity('Hoàn thành đánh giá Toolset');
        
        // Switch to results tab
        this.switchToResultsTab();
        console.log('Toolset assessment processed');
    }

    switchToResultsTab() {
        const resultsTab = document.querySelector('.assessment-tabs [data-tab="results"]');
        if (resultsTab) {
            resultsTab.click();
            console.log('Switched to results tab');
        }
    }

    getAssessmentLevel(score) {
        if (score >= 4.5) return 'Xuất sắc';
        if (score >= 3.5) return 'Khá';
        if (score >= 2.5) return 'Trung bình';
        if (score >= 1.5) return 'Cần cải thiện';
        return 'Cần phát triển mạnh';
    }

    updateAssessmentResults() {
        const resultsContainer = document.getElementById('assessmentResults');
        
        if (!resultsContainer) {
            console.log('Assessment results container not found');
            return;
        }

        if (!this.assessmentData.mindset && !this.assessmentData.skillset && !this.assessmentData.toolset) {
            resultsContainer.innerHTML = '<h3>Kết quả đánh giá</h3><p>Hoàn thành các đánh giá để xem kết quả chi tiết</p>';
            return;
        }

        let resultsHTML = '<h3>Kết quả đánh giá MST Framework</h3>';
        
        if (this.assessmentData.mindset) {
            resultsHTML += `
                <div class="result-item">
                    <span>Mindset (Tư duy)</span>
                    <span class="result-score">${this.assessmentData.mindset.average.toFixed(1)}/5 - ${this.assessmentData.mindset.level}</span>
                </div>
            `;
        }

        if (this.assessmentData.skillset) {
            resultsHTML += `
                <div class="result-item">
                    <span>Skillset (Kỹ năng)</span>
                    <span class="result-score">${this.assessmentData.skillset.overall.toFixed(1)}/5 - ${this.assessmentData.skillset.level}</span>
                </div>
            `;
        }

        if (this.assessmentData.toolset) {
            resultsHTML += `
                <div class="result-item">
                    <span>Toolset (Công cụ)</span>
                    <span class="result-score">${this.assessmentData.toolset.average.toFixed(1)}/5 - ${this.assessmentData.toolset.level}</span>
                </div>
            `;
        }

        // Add recommendations
        resultsHTML += '<div class="recommendations"><h4>Khuyến nghị phát triển:</h4><ul>';
        
        if (this.assessmentData.mindset && this.assessmentData.mindset.average < 3.5) {
            resultsHTML += '<li>Tập trung phát triển tư duy tăng trưởng và khả năng thích ứng</li>';
        }

        if (this.assessmentData.skillset && this.assessmentData.skillset.technical.average < 3.5) {
            resultsHTML += '<li>Nâng cao kỹ năng kỹ thuật, đặc biệt là system design</li>';
        }

        if (this.assessmentData.skillset && this.assessmentData.skillset.soft.average < 3.5) {
            resultsHTML += '<li>Phát triển kỹ năng mềm như giao tiếp và leadership</li>';
        }

        if (this.assessmentData.toolset && this.assessmentData.toolset.average < 3.5) {
            resultsHTML += '<li>Xây dựng quy trình đánh giá và lựa chọn công cụ có hệ thống</li>';
        }

        resultsHTML += '</ul></div>';

        resultsContainer.innerHTML = resultsHTML;
        console.log('Assessment results updated');
    }

    // Career Development Planner
    setupCareerPlanner() {
        console.log('Setting up career planner...');
        const goalForm = document.getElementById('goalSettingForm');
        if (goalForm) {
            goalForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveCareerGoals(e.target);
            });
        }

        // Milestone tracking
        document.querySelectorAll('.milestone input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateMilestoneProgress();
            });
        });

        this.loadCareerGoalsUI();
        this.updateMilestoneProgress();
        console.log('Career planner setup completed');
    }

    saveCareerGoals(form) {
        const formData = new FormData(form);
        
        this.careerGoals = {
            goal: formData.get('careerGoal') || '',
            currentPosition: formData.get('currentPosition') || '',
            skillsToLearn: formData.get('skillsToLearn') || '',
            timestamp: new Date().toISOString()
        };

        localStorage.setItem('mst_career_goals', JSON.stringify(this.careerGoals));
        this.addActivity('Đã thiết lập mục tiêu nghề nghiệp');
        alert('Mục tiêu nghề nghiệp đã được lưu!');
        console.log('Career goals saved');
    }

    loadCareerGoalsUI() {
        if (this.careerGoals) {
            const form = document.getElementById('goalSettingForm');
            if (form) {
                const goalInput = form.querySelector('[name="careerGoal"]');
                const positionInput = form.querySelector('[name="currentPosition"]');
                const skillsInput = form.querySelector('[name="skillsToLearn"]');

                if (goalInput) goalInput.value = this.careerGoals.goal || '';
                if (positionInput) positionInput.value = this.careerGoals.currentPosition || '';
                if (skillsInput) skillsInput.value = this.careerGoals.skillsToLearn || '';
                console.log('Career goals loaded into UI');
            }
        }
    }

    updateMilestoneProgress() {
        const checkboxes = document.querySelectorAll('.milestone input[type="checkbox"]');
        const completed = Array.from(checkboxes).filter(cb => cb.checked).length;
        const total = checkboxes.length;
        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
        
        const progressElement = document.getElementById('progressPercentage');
        if (progressElement) {
            progressElement.textContent = `${percentage}%`;
        }
        
        // Save milestone state
        const milestoneState = Array.from(checkboxes).map(cb => cb.checked);
        localStorage.setItem('mst_milestones', JSON.stringify(milestoneState));
        console.log('Milestone progress updated:', percentage + '%');
    }

    // Reference Library
    setupLibrary() {
        console.log('Setting up library...');
        
        // Library search
        const searchInput = document.getElementById('librarySearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchLibrary(e.target.value);
            });
        }

        // Library tabs
        const libraryTabBtns = document.querySelectorAll('.library-tabs .tab-btn');
        const libraryTabContents = document.querySelectorAll('.library-tabs .tab-content');

        libraryTabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = e.target.getAttribute('data-tab');
                console.log('Library tab clicked:', tabId);
                
                libraryTabBtns.forEach(b => b.classList.remove('active'));
                libraryTabContents.forEach(c => c.classList.remove('active'));
                
                e.target.classList.add('active');
                const targetTab = document.getElementById(tabId);
                if (targetTab) {
                    targetTab.classList.add('active');
                    console.log('Library tab activated:', tabId);
                }
            });
        });
        
        console.log('Library setup completed');
    }

    searchLibrary(query) {
        if (!query.trim()) {
            // Show all content
            document.querySelectorAll('.concept-item, .skill-category, .tool-item, .practice-group').forEach(item => {
                item.style.display = 'block';
            });
            return;
        }

        const searchTerm = query.toLowerCase();
        document.querySelectorAll('.concept-item, .skill-category, .tool-item, .practice-group').forEach(item => {
            const text = item.textContent.toLowerCase();
            item.style.display = text.includes(searchTerm) ? 'block' : 'none';
        });
        console.log('Library search performed:', query);
    }

    // Practice Scenarios
    setupPracticeScenarios() {
        console.log('Setting up practice scenarios...');
        // Make startScenario globally available
        window.startScenario = (scenarioType) => {
            console.log('Starting scenario:', scenarioType);
            this.startPracticeScenario(scenarioType);
        };
    }

    startPracticeScenario(scenarioType) {
        const scenarios = {
            'api-design': {
                title: 'API Design Challenge',
                content: `
                    <h4>Thử thách: Thiết kế API cho E-commerce</h4>
                    <p><strong>Yêu cầu:</strong> Thiết kế RESTful API cho hệ thống e-commerce có thể xử lý millions users đồng thời.</p>
                    
                    <h5>Các câu hỏi cần trả lời:</h5>
                    <ol>
                        <li>Mindset: Bạn sẽ ưu tiên yếu tố gì khi thiết kế? (Performance, Security, Usability?)</li>
                        <li>Skillset: Kỹ năng nào cần áp dụng? (REST principles, Caching, Rate limiting?)</li>
                        <li>Toolset: Công nghệ nào phù hợp? (Node.js, Python, Go? Database? Caching?)</li>
                    </ol>
                    
                    <div class="practice-form">
                        <h5>Giải pháp của bạn:</h5>
                        <textarea id="practiceTextarea" placeholder="Mô tả cách tiếp cận theo MST Framework..." rows="6" style="width: 100%; padding: 12px; border-radius: 6px; border: 1px solid #ccc;"></textarea>
                        <button class="btn btn--primary" onclick="window.app.submitPracticeResult('api-design', document.getElementById('practiceTextarea').value)">Nộp bài</button>
                    </div>
                `
            },
            'legacy-migration': {
                title: 'Legacy System Migration',
                content: `
                    <h4>Thử thách: Migration hệ thống Legacy</h4>
                    <p><strong>Tình huống:</strong> Hệ thống monolithic 10 năm tuổi cần migrate sang microservices. Database lớn, nhiều dependencies.</p>
                    
                    <h5>Áp dụng MST Framework:</h5>
                    <ol>
                        <li>Mindset: Tư duy gì cần có? (Risk management, Gradual transition, Business continuity?)</li>
                        <li>Skillset: Kỹ năng nào quan trọng? (Migration strategies, Data modeling, System integration?)</li>
                        <li>Toolset: Công cụ hỗ trợ? (Containerization, API gateways, Monitoring tools?)</li>
                    </ol>
                    
                    <div class="practice-form">
                        <h5>Kế hoạch migration của bạn:</h5>
                        <textarea id="practiceTextarea" placeholder="Mô tả strategy và các bước thực hiện..." rows="6" style="width: 100%; padding: 12px; border-radius: 6px; border: 1px solid #ccc;"></textarea>
                        <button class="btn btn--primary" onclick="window.app.submitPracticeResult('legacy-migration', document.getElementById('practiceTextarea').value)">Nộp bài</button>
                    </div>
                `
            },
            'team-conflict': {
                title: 'Team Conflict Resolution',
                content: `
                    <h4>Thử thách: Giải quyết xung đột nhóm</h4>
                    <p><strong>Tình huống:</strong> Dev team muốn refactor code để improve quality, nhưng Product team pressure deliver features nhanh cho deadline.</p>
                    
                    <h5>MST Framework approach:</h5>
                    <ol>
                        <li>Mindset: Tư duy gì cần có? (Collaboration, Win-win solutions, Long-term thinking?)</li>
                        <li>Skillset: Kỹ năng gì áp dụng? (Communication, Negotiation, Stakeholder management?)</li>
                        <li>Toolset: Phương pháp nào hiệu quả? (Meeting facilitation, Data presentation, Compromise frameworks?)</li>
                    </ol>
                    
                    <div class="practice-form">
                        <h5>Cách giải quyết của bạn:</h5>
                        <textarea id="practiceTextarea" placeholder="Describe your approach to resolve this conflict..." rows="6" style="width: 100%; padding: 12px; border-radius: 6px; border: 1px solid #ccc;"></textarea>
                        <button class="btn btn--primary" onclick="window.app.submitPracticeResult('team-conflict', document.getElementById('practiceTextarea').value)">Nộp bài</button>
                    </div>
                `
            },
            'performance-crisis': {
                title: 'Performance Crisis',
                content: `
                    <h4>Thử thách: Performance Crisis</h4>
                    <p><strong>Tình huống:</strong> Production system response time tăng 300% trong 2 ngày. Customer complaints nhiều, revenue bị impact.</p>
                    
                    <h5>Crisis management với MST:</h5>
                    <ol>
                        <li>Mindset: Tư duy emergency vs systematic approach? Pressure handling?</li>
                        <li>Skillset: Troubleshooting, Performance analysis, Communication under pressure?</li>
                        <li>Toolset: Monitoring tools, Profiling tools, Communication channels?</li>
                    </ol>
                    
                    <div class="practice-form">
                        <h5>Action plan của bạn:</h5>
                        <textarea id="practiceTextarea" placeholder="Step-by-step crisis response plan..." rows="6" style="width: 100%; padding: 12px; border-radius: 6px; border: 1px solid #ccc;"></textarea>
                        <button class="btn btn--primary" onclick="window.app.submitPracticeResult('performance-crisis', document.getElementById('practiceTextarea').value)">Nộp bài</button>
                    </div>
                `
            }
        };

        const scenario = scenarios[scenarioType];
        if (scenario) {
            this.showModal(scenario.title, scenario.content);
        }
    }

    submitPracticeResult(scenarioType, solution) {
        if (!solution.trim()) {
            alert('Vui lòng nhập giải pháp của bạn!');
            return;
        }

        const result = {
            scenario: scenarioType,
            solution: solution,
            timestamp: new Date().toISOString(),
            score: Math.floor(Math.random() * 30) + 70 // Simulated score 70-100
        };

        // Save to practice results
        let practiceResults = JSON.parse(localStorage.getItem('mst_practice_results')) || [];
        practiceResults.push(result);
        localStorage.setItem('mst_practice_results', JSON.stringify(practiceResults));

        this.updatePracticeResults();
        this.closeModal();
        this.addActivity(`Hoàn thành scenario: ${scenarioType}`);
        
        alert(`Bài làm đã được lưu! Điểm đánh giá: ${result.score}/100`);
        console.log('Practice result submitted:', scenarioType);
    }

    updatePracticeResults() {
        const practiceResults = JSON.parse(localStorage.getItem('mst_practice_results')) || [];
        const container = document.getElementById('practiceResults');

        if (!container) {
            console.log('Practice results container not found');
            return;
        }

        if (practiceResults.length === 0) {
            container.innerHTML = '<p>Chưa có kết quả luyện tập nào</p>';
            return;
        }

        const resultsHTML = practiceResults.map(result => `
            <div class="practice-result-item" style="margin-bottom: 12px; padding: 12px; background: var(--color-bg-1); border-radius: 6px;">
                <h5>${result.scenario}</h5>
                <p>Điểm: ${result.score}/100 | ${new Date(result.timestamp).toLocaleDateString('vi-VN')}</p>
            </div>
        `).join('');

        container.innerHTML = resultsHTML;
        console.log('Practice results updated:', practiceResults.length);
    }

    // Modal System
    setupModals() {
        console.log('Setting up modals...');
        const modal = document.getElementById('scenarioModal');
        if (!modal) {
            console.log('Modal element not found');
            return;
        }

        const overlay = modal.querySelector('.modal-overlay');
        const closeBtn = modal.querySelector('.modal-close');

        if (overlay) {
            overlay.addEventListener('click', () => this.closeModal());
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeModal());
        }

        // ESC key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                this.closeModal();
            }
        });

        // Make closeModal globally available
        window.closeModal = () => this.closeModal();
        console.log('Modals setup completed');
    }

    showModal(title, content) {
        const modal = document.getElementById('scenarioModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        if (modal && modalTitle && modalBody) {
            modalTitle.textContent = title;
            modalBody.innerHTML = content;
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            console.log('Modal shown:', title);
        }
    }

    closeModal() {
        const modal = document.getElementById('scenarioModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
            console.log('Modal closed');
        }
    }

    // Language Toggle
    setupLanguageToggle() {
        const langBtn = document.getElementById('languageToggle');
        if (langBtn) {
            langBtn.addEventListener('click', () => {
                this.toggleLanguage();
            });
            console.log('Language toggle setup completed');
        }
    }

    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'vi' ? 'en' : 'vi';
        // In a real implementation, this would switch content language
        // For now, we'll just show a notification
        alert(`Language switched to: ${this.currentLanguage.toUpperCase()}`);
        console.log('Language toggled to:', this.currentLanguage);
    }

    // Data Export
    setupDataExport() {
        const exportBtn = document.getElementById('exportData');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportAllData();
            });
            console.log('Data export setup completed');
        }
    }

    exportAllData() {
        const exportData = {
            progress: this.userProgress,
            assessments: this.assessmentData,
            mappings: this.savedMappings,
            careerGoals: this.careerGoals,
            practiceResults: JSON.parse(localStorage.getItem('mst_practice_results')) || [],
            milestones: JSON.parse(localStorage.getItem('mst_milestones')) || [],
            exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `MST_Framework_Data_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        this.addActivity('Đã xuất dữ liệu');
        console.log('Data exported successfully');
    }

    // Data Management
    loadProgress() {
        return JSON.parse(localStorage.getItem('mst_progress')) || {
            framework: 0,
            mapping: 0,
            assessment: 0
        };
    }

    updateProgress(section, percentage) {
        this.userProgress[section] = Math.max(this.userProgress[section] || 0, percentage);
        localStorage.setItem('mst_progress', JSON.stringify(this.userProgress));
        this.updateProgressBars();
        console.log('Progress updated:', section, percentage + '%');
    }

    updateProgressBars() {
        const frameworkProgress = document.getElementById('frameworkProgress');
        const assessmentProgress = document.getElementById('assessmentProgress');
        const mappingProgress = document.getElementById('mappingProgress');

        if (frameworkProgress) frameworkProgress.style.width = `${this.userProgress.framework || 0}%`;
        if (assessmentProgress) assessmentProgress.style.width = `${this.userProgress.assessment || 0}%`;
        if (mappingProgress) mappingProgress.style.width = `${this.userProgress.mapping || 0}%`;
    }

    loadMappings() {
        const saved = JSON.parse(localStorage.getItem('mst_mappings')) || [];
        setTimeout(() => this.displaySavedMappings(), 100);
        return saved;
    }

    saveMappings() {
        localStorage.setItem('mst_mappings', JSON.stringify(this.savedMappings));
    }

    loadCareerGoals() {
        return JSON.parse(localStorage.getItem('mst_career_goals')) || null;
    }

    saveAssessmentData() {
        localStorage.setItem('mst_assessment_data', JSON.stringify(this.assessmentData));
    }

    loadAssessmentData() {
        const saved = localStorage.getItem('mst_assessment_data');
        if (saved) {
            this.assessmentData = JSON.parse(saved);
            this.updateAssessmentResults();
        }
    }

    // Activity Tracking
    addActivity(activity) {
        let activities = JSON.parse(localStorage.getItem('mst_activities')) || [];
        activities.unshift({
            text: activity,
            timestamp: new Date().toISOString()
        });
        
        // Keep only last 10 activities
        activities = activities.slice(0, 10);
        localStorage.setItem('mst_activities', JSON.stringify(activities));
        this.loadRecentActivities();
    }

    loadRecentActivities() {
        const activities = JSON.parse(localStorage.getItem('mst_activities')) || [];
        const container = document.getElementById('recentActivities');
        
        if (!container) return;

        if (activities.length === 0) {
            container.innerHTML = '<li>Chưa có hoạt động nào</li>';
            return;
        }

        const activitiesHTML = activities.map(activity => 
            `<li>${activity.text} <span style="color: var(--color-text-secondary); font-size: var(--font-size-xs);">${new Date(activity.timestamp).toLocaleTimeString('vi-VN')}</span></li>`
        ).join('');

        container.innerHTML = activitiesHTML;
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing MST Framework App...');
    window.app = new MSTFrameworkApp();
    
    // Load milestone state
    setTimeout(() => {
        const milestoneState = JSON.parse(localStorage.getItem('mst_milestones')) || [];
        document.querySelectorAll('.milestone input[type="checkbox"]').forEach((checkbox, index) => {
            if (milestoneState[index]) {
                checkbox.checked = true;
            }
        });
        
        if (window.app && window.app.updateMilestoneProgress) {
            window.app.updateMilestoneProgress();
        }
    }, 100);
});