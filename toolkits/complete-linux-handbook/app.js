// Comprehensive Linux Handbook Application - Enhanced Version

// Complete Linux Commands Database
const commandsData = {
  "file_directory": {
    "name": "<PERSON><PERSON>ập Tin & <PERSON><PERSON><PERSON>",
    "icon": "📁",
    "description": "<PERSON>á<PERSON> lệnh cơ bản để điều hướng, t<PERSON><PERSON>, sao ch<PERSON><PERSON>, di chuyển và xóa tập tin và thư mục",
    "commands": {
      "ls": {
        "name": "ls",
        "description": "Liệt kê nội dung thư mục",
        "syntax": "ls [tùy_chọn] [tập_tin/thư_mục]",
        "category": "basic",
        "common_options": [
          "-l: định dạng dài với chi tiết",
          "-a: hiển thị tập tin ẩn (bao gồm . và ..)",
          "-h: kích thước dễ đọc (KB, MB, GB)",
          "-R: liệt kê đệ quy tất cả thư mục con",
          "-t: sắp xếp theo thời gian sửa đổi",
          "-r: thứ tự ngược",
          "-i: hiển thị inode numbers",
          "-S: sắp xếp theo kích thước tập tin"
        ],
        "examples": [
          "ls -la # Liệt kê tất cả tập tin với chi tiết đầy đủ",
          "ls -lh # Liệt kê với kích thước dễ đọc (1K, 2M, 3G)",
          "ls -R # Liệt kê đệ quy tất cả thư mục con",
          "ls *.txt # Chỉ liệt kê tập tin có đuôi .txt",
          "ls -lt # Sắp xếp theo thời gian mới nhất trước"
        ],
        "related": ["cd", "pwd", "find", "tree", "du"]
      },
      "cd": {
        "name": "cd",
        "description": "Thay đổi thư mục làm việc hiện tại",
        "syntax": "cd [thư_mục]",
        "category": "basic",
        "common_options": [
          "cd ~ : chuyển về thư mục home",
          "cd .. : lên một cấp thư mục cha",
          "cd - : quay về thư mục trước đó",
          "cd / : chuyển về thư mục gốc",
          "cd : về home directory (không tham số)"
        ],
        "examples": [
          "cd /etc # Chuyển đến thư mục /etc",
          "cd ~/Documents # Chuyển đến thư mục Documents trong home",
          "cd .. # Lên một cấp thư mục",
          "cd - # Quay lại thư mục vừa rời khỏi",
          "cd ../../../ # Lên ba cấp thư mục"
        ],
        "related": ["pwd", "ls", "pushd", "popd"]
      },
      "pwd": {
        "name": "pwd",
        "description": "In đường dẫn thư mục làm việc hiện tại",
        "syntax": "pwd [tùy_chọn]",
        "category": "basic",
        "common_options": [
          "-P: hiển thị đường dẫn vật lý thực",
          "-L: hiển thị đường dẫn logic (mặc định)"
        ],
        "examples": [
          "pwd # Hiển thị thư mục hiện tại",
          "pwd -P # Hiển thị đường dẫn vật lý (không qua symlink)"
        ],
        "related": ["cd", "ls", "realpath"]
      },
      "mkdir": {
        "name": "mkdir",
        "description": "Tạo thư mục mới",
        "syntax": "mkdir [tùy_chọn] thư_mục...",
        "category": "basic",
        "common_options": [
          "-p: tạo thư mục cha nếu chưa tồn tại",
          "-m: đặt quyền truy cập cho thư mục",
          "-v: hiển thị thông báo khi tạo"
        ],
        "examples": [
          "mkdir newdir # Tạo thư mục mới",
          "mkdir -p path/to/deep/dir # Tạo cấu trúc thư mục sâu",
          "mkdir -m 755 mydir # Tạo thư mục với quyền 755",
          "mkdir dir1 dir2 dir3 # Tạo nhiều thư mục cùng lúc"
        ],
        "related": ["rmdir", "rm", "ls", "chmod"]
      },
      "find": {
        "name": "find",
        "description": "Tìm kiếm tập tin và thư mục",
        "syntax": "find [đường_dẫn] [điều_kiện] [hành_động]",
        "category": "intermediate",
        "common_options": [
          "-name: tìm theo tên tập tin (có thể dùng wildcard)",
          "-type: tìm theo loại (f=tập tin, d=thư mục, l=link)",
          "-size: tìm theo kích thước (+1M, -500k)",
          "-mtime: tìm theo thời gian sửa đổi (-7 = 7 ngày qua)",
          "-exec: thực thi lệnh trên kết quả tìm được",
          "-perm: tìm theo quyền truy cập"
        ],
        "examples": [
          "find . -name '*.txt' # Tìm tất cả tập tin .txt",
          "find /home -type d -name 'Documents' # Tìm thư mục tên Documents",
          "find . -size +100M # Tìm tập tin lớn hơn 100MB",
          "find . -name '*.log' -exec rm {} \\; # Tìm và xóa file log",
          "find . -mtime -7 # Tìm file được sửa trong 7 ngày qua"
        ],
        "related": ["locate", "which", "whereis", "grep"]
      }
    }
  },
  "permissions": {
    "name": "Quyền Truy Cập & Sở Hữu",
    "icon": "🔐",
    "description": "Quản lý quyền truy cập và sở hữu tập tin trong Linux",
    "commands": {
      "chmod": {
        "name": "chmod",
        "description": "Thay đổi quyền truy cập tập tin",
        "syntax": "chmod [tùy_chọn] quyền tập_tin...",
        "category": "intermediate",
        "common_options": [
          "Bát phân: 755 (rwxr-xr-x), 644 (rw-r--r--), 777 (rwxrwxrwx)",
          "Ký hiệu: u+x (user thêm execute), g-w (group bỏ write), o=r (other chỉ read)",
          "-R: áp dụng đệ quy cho thư mục con",
          "-v: hiển thị chi tiết thay đổi",
          "--reference=file: sao chép quyền từ file khác"
        ],
        "examples": [
          "chmod 755 script.sh # Đặt quyền executable cho script",
          "chmod u+x file.txt # Thêm quyền thực thi cho owner",
          "chmod -R 644 directory/ # Đặt quyền đệ quy",
          "chmod go-rwx private.txt # Bỏ tất cả quyền cho group và other",
          "chmod a+r public.txt # Cho phép tất cả đọc"
        ],
        "related": ["chown", "chgrp", "ls", "stat", "umask"]
      },
      "chown": {
        "name": "chown",
        "description": "Thay đổi chủ sở hữu tập tin",
        "syntax": "chown [tùy_chọn] owner[:group] tập_tin...",
        "category": "intermediate",
        "common_options": [
          "-R: thay đổi đệ quy cho thư mục con",
          "--reference=file: sao chép ownership từ file khác",
          "-v: hiển thị chi tiết thay đổi"
        ],
        "examples": [
          "chown user file.txt # Đổi owner",
          "chown user:group file.txt # Đổi cả owner và group",
          "chown -R user:group directory/ # Đổi đệ quy",
          "chown :group file.txt # Chỉ đổi group"
        ],
        "related": ["chmod", "chgrp", "ls", "id", "groups"]
      }
    }
  },
  "text_processing": {
    "name": "Xử Lý & Tìm Kiếm Văn Bản",
    "icon": "🔍",
    "description": "Công cụ mạnh mẽ để tìm kiếm, chỉnh sửa và xử lý văn bản",
    "commands": {
      "grep": {
        "name": "grep",
        "description": "Tìm kiếm mẫu văn bản trong tập tin",
        "syntax": "grep [tùy_chọn] mẫu [tập_tin...]",
        "category": "intermediate",
        "common_options": [
          "-i: bỏ qua chữ hoa/thường",
          "-r: tìm kiếm đệ quy trong thư mục",
          "-n: hiển thị số dòng",
          "-v: đảo ngược kết quả (hiển thị dòng không khớp)",
          "-c: đếm số dòng khớp",
          "-l: chỉ hiển thị tên tập tin có khớp"
        ],
        "examples": [
          "grep 'error' logfile.txt # Tìm từ 'error' trong file log",
          "grep -i 'warning' *.log # Tìm 'warning' không phân biệt hoa thường",
          "grep -r 'TODO' /src/ # Tìm kiếm đệ quy từ 'TODO'",
          "grep -n 'function' script.js # Hiển thị số dòng chứa 'function'",
          "ps aux | grep nginx # Tìm process nginx"
        ],
        "related": ["egrep", "fgrep", "sed", "awk", "find"]
      },
      "sed": {
        "name": "sed",
        "description": "Stream editor - chỉnh sửa văn bản theo luồng",
        "syntax": "sed [tùy_chọn] 'lệnh' [tập_tin...]",
        "category": "advanced",
        "common_options": [
          "s/old/new/g: thay thế toàn cục",
          "-i: chỉnh sửa tại chỗ (in-place)",
          "-n: chỉ in dòng được chỉ định",
          "d: xóa dòng"
        ],
        "examples": [
          "sed 's/old/new/g' file.txt # Thay thế 'old' thành 'new'",
          "sed -i 's/foo/bar/g' *.txt # Thay thế trong file gốc",
          "sed -n '1,10p' file.txt # In dòng 1-10",
          "sed '/pattern/d' file.txt # Xóa dòng chứa pattern"
        ],
        "related": ["awk", "grep", "tr", "cut"]
      }
    }
  },
  "networking": {
    "name": "Lệnh Mạng & Kết Nối",
    "icon": "🌐",
    "description": "Công cụ mạng, SSH, và chẩn đoán kết nối",
    "commands": {
      "ssh": {
        "name": "ssh",
        "description": "Secure Shell - đăng nhập từ xa bảo mật",
        "syntax": "ssh [tùy_chọn] [user@]hostname [lệnh]",
        "category": "intermediate",
        "common_options": [
          "-p cổng: chỉ định cổng kết nối",
          "-i keyfile: sử dụng tập tin private key",
          "-X: bật X11 forwarding",
          "-L port:host:hostport: port forwarding local",
          "-v: verbose output để debug"
        ],
        "examples": [
          "ssh <EMAIL> # Kết nối đến server",
          "ssh -p 2222 user@host # Kết nối qua cổng tùy chỉnh",
          "ssh -i ~/.ssh/mykey user@host # Sử dụng key riêng",
          "ssh user@host 'ls -la' # Chạy lệnh từ xa",
          "ssh -L 8080:localhost:80 user@host # Port forwarding"
        ],
        "related": ["scp", "rsync", "sftp", "ssh-keygen"]
      },
      "ping": {
        "name": "ping",
        "description": "Kiểm tra kết nối mạng đến host",
        "syntax": "ping [tùy_chọn] destination",
        "category": "basic",
        "common_options": [
          "-c count: số lượng packet gửi",
          "-i interval: khoảng thời gian giữa các packet",
          "-s size: kích thước packet",
          "-W timeout: timeout cho mỗi packet"
        ],
        "examples": [
          "ping google.com # Ping đến Google",
          "ping -c 4 ******* # Ping 4 lần đến DNS Google",
          "ping -i 2 localhost # Ping mỗi 2 giây"
        ],
        "related": ["traceroute", "netstat", "ss", "nmap"]
      }
    }
  },
  "system_monitoring": {
    "name": "Giám Sát Hệ Thống",
    "icon": "📊",
    "description": "Theo dõi hiệu suất, tiến trình và tài nguyên hệ thống",
    "commands": {
      "top": {
        "name": "top",
        "description": "Hiển thị tiến trình đang chạy theo thời gian thực",
        "syntax": "top [tùy_chọn]",
        "category": "basic",
        "common_options": [
          "-d delay: khoảng thời gian cập nhật",
          "-p pid: chỉ hiển thị process cụ thể",
          "-u user: chỉ hiển thị process của user",
          "-n iterations: số lần cập nhật rồi thoát"
        ],
        "examples": [
          "top # Hiển thị top processes",
          "top -d 2 # Cập nhật mỗi 2 giây",
          "top -u apache # Chỉ hiển thị process của user apache"
        ],
        "related": ["htop", "ps", "kill", "pgrep"]
      },
      "ps": {
        "name": "ps",
        "description": "Hiển thị danh sách tiến trình đang chạy",
        "syntax": "ps [tùy_chọn]",
        "category": "basic",
        "common_options": [
          "aux: hiển thị tất cả process chi tiết",
          "-ef: hiển thị full format",
          "-u user: process của user cụ thể",
          "--forest: hiển thị dạng cây process"
        ],
        "examples": [
          "ps aux # Tất cả process với thông tin chi tiết",
          "ps -ef | grep nginx # Tìm process nginx",
          "ps -u root # Process của user root",
          "ps --forest # Hiển thị parent-child relationship"
        ],
        "related": ["top", "pgrep", "pkill", "jobs"]
      }
    }
  }
};

// Filesystem Hierarchy Standard
const filesystemData = {
  "/": {
    "name": "Root Directory",
    "description": "Thư mục gốc của hệ thống tập tin, chứa tất cả thư mục khác",
    "purpose": "Điểm bắt đầu của cây thư mục Linux",
    "permissions": "Chỉ root user có quyền ghi vào thư mục này",
    "examples": ["Tất cả đường dẫn tuyệt đối bắt đầu từ /"]
  },
  "/bin": {
    "name": "Essential User Binaries",
    "description": "Các chương trình thực thi thiết yếu cho người dùng",
    "purpose": "Chứa các lệnh cơ bản cần thiết cho hệ thống hoạt động",
    "examples": ["ls", "cp", "mv", "cat", "chmod", "bash", "sh", "grep", "tar"]
  },
  "/boot": {
    "name": "Static Boot Files",
    "description": "Các tập tin cần thiết để khởi động hệ thống",
    "purpose": "Chứa kernel Linux, initial ramdisk và bootloader",
    "examples": ["vmlinuz-*", "initrd.img-*", "grub/", "System.map"]
  },
  "/dev": {
    "name": "Device Files",
    "description": "Các tập tin thiết bị đại diện cho phần cứng",
    "purpose": "Giao diện với hardware thông qua các tập tin đặc biệt",
    "examples": ["/dev/sda (ổ cứng)", "/dev/null (null device)", "/dev/zero", "/dev/random"]
  },
  "/etc": {
    "name": "Configuration Files",
    "description": "Tập tin cấu hình hệ thống và ứng dụng",
    "purpose": "Chứa tất cả cấu hình toàn hệ thống",
    "examples": ["passwd (user accounts)", "shadow (passwords)", "hosts", "fstab", "crontab"],
    "important_files": {
      "/etc/passwd": "Thông tin tài khoản người dùng",
      "/etc/shadow": "Mật khẩu đã được mã hóa",
      "/etc/group": "Thông tin các nhóm người dùng",
      "/etc/fstab": "Bảng hệ thống tập tin (filesystem table)",
      "/etc/hosts": "Ánh xạ tên máy chủ với địa chỉ IP",
      "/etc/crontab": "Lịch trình tác vụ hệ thống"
    }
  },
  "/home": {
    "name": "User Home Directories",
    "description": "Thư mục cá nhân của từng người dùng",
    "purpose": "Lưu trữ dữ liệu và cấu hình cá nhân của user",
    "examples": ["/home/<USER>/", "/home/<USER>/Documents/", "/home/<USER>/.bashrc"]
  },
  "/lib": {
    "name": "Essential Shared Libraries",
    "description": "Thư viện chia sẻ cần thiết cho các chương trình trong /bin và /sbin",
    "purpose": "Chứa các thư viện động (shared libraries) và modules kernel",
    "examples": ["libc.so.6", "ld-linux.so.2", "modules/"]
  },
  "/var": {
    "name": "Variable Data Files",
    "description": "Dữ liệu thay đổi trong quá trình hoạt động của hệ thống",
    "purpose": "Log files, mail, databases, cache và dữ liệu runtime",
    "examples": ["/var/log/ (log files)", "/var/mail/ (email)", "/var/cache/", "/var/tmp/"]
  },
  "/usr": {
    "name": "User Programs & Data",
    "description": "Chương trình và dữ liệu cho người dùng (không thiết yếu cho boot)",
    "purpose": "Phần lớn các ứng dụng và tiện ích được cài đặt ở đây",
    "examples": ["/usr/bin/", "/usr/lib/", "/usr/share/", "/usr/local/"]
  },
  "/tmp": {
    "name": "Temporary Files",
    "description": "Tập tin tạm thời, thường bị xóa khi reboot",
    "purpose": "Lưu trữ tạm thời cho các ứng dụng",
    "examples": ["Tập tin tạm của ứng dụng", "Session files", "Temporary downloads"]
  }
};

// Keyboard Shortcuts Database
const keyboardShortcuts = {
  "navigation": {
    "name": "Điều Hướng Terminal",
    "icon": "⌨️",
    "shortcuts": {
      "Ctrl + A": "Di chuyển con trỏ về đầu dòng",
      "Ctrl + E": "Di chuyển con trỏ về cuối dòng", 
      "Ctrl + U": "Cắt từ con trỏ về đầu dòng",
      "Ctrl + K": "Cắt từ con trỏ đến cuối dòng",
      "Ctrl + W": "Xóa từ phía trước con trỏ",
      "Ctrl + Y": "Dán văn bản vừa cắt",
      "Alt + B": "Di chuyển con trỏ lùi một từ",
      "Alt + F": "Di chuyển con trỏ tiến một từ",
      "Ctrl + L": "Xóa màn hình terminal"
    }
  },
  "history": {
    "name": "Lịch Sử Lệnh",
    "icon": "📜",
    "shortcuts": {
      "Ctrl + R": "Tìm kiếm ngược trong lịch sử lệnh",
      "Ctrl + G": "Thoát khỏi chế độ tìm kiếm",
      "!!": "Lặp lại lệnh cuối cùng",
      "!n": "Chạy lệnh thứ n từ lịch sử",
      "!string": "Chạy lệnh cuối bắt đầu với 'string'",
      "Up Arrow": "Lệnh trước đó trong lịch sử",
      "Down Arrow": "Lệnh tiếp theo trong lịch sử",
      "Alt + .": "Chèn argument cuối của lệnh trước"
    }
  },
  "process_control": {
    "name": "Điều Khiển Tiến Trình",
    "icon": "⚡",
    "shortcuts": {
      "Ctrl + C": "Dừng tiến trình hiện tại (SIGINT)",
      "Ctrl + Z": "Tạm dừng tiến trình (SIGTSTP)",
      "Ctrl + D": "EOF - đăng xuất hoặc kết thúc input",
      "Ctrl + S": "Đóng băng đầu ra terminal (XOFF)",
      "Ctrl + Q": "Tiếp tục đầu ra terminal (XON)",
      "Ctrl + \\": "Dừng cứng tiến trình (SIGQUIT)"
    }
  }
};

// Environment Variables
const environmentVariables = {
  "PATH": {
    "description": "Danh sách đường dẫn tìm kiếm lệnh thực thi",
    "example": "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",
    "usage": "export PATH=$PATH:/new/path"
  },
  "HOME": {
    "description": "Đường dẫn đến thư mục home của user hiện tại",
    "example": "/home/<USER>",
    "usage": "cd $HOME hoặc cd ~"
  },
  "USER": {
    "description": "Tên người dùng hiện tại",
    "example": "john",
    "usage": "echo \"Hello $USER\""
  },
  "SHELL": {
    "description": "Shell mặc định của user hiện tại",
    "example": "/bin/bash",
    "usage": "echo $SHELL"
  },
  "PWD": {
    "description": "Thư mục làm việc hiện tại",
    "example": "/home/<USER>/Documents",
    "usage": "echo $PWD"
  },
  "OLDPWD": {
    "description": "Thư mục làm việc trước đó",
    "example": "/home/<USER>",
    "usage": "cd $OLDPWD hoặc cd -"
  }
};

const configFiles = {
  "~/.bashrc": "Cấu hình Bash cho non-login shells",
  "~/.bash_profile": "Cấu hình Bash cho login shells",
  "~/.profile": "Cấu hình shell-agnostic cho tất cả shells",
  "/etc/environment": "Biến môi trường toàn hệ thống",
  "~/.bash_aliases": "Định nghĩa các alias cá nhân"
};

// Package Managers Data
const packageManagers = {
  "apt": {
    "name": "APT (Advanced Package Tool)",
    "distributions": ["Debian", "Ubuntu", "Linux Mint", "Kali Linux"],
    "commands": {
      "Cập nhật hệ thống": {
        "apt update": "Cập nhật danh sách gói từ repositories",
        "apt upgrade": "Nâng cấp các gói đã cài đặt",
        "apt full-upgrade": "Nâng cấp toàn bộ hệ thống"
      },
      "Quản lý gói": {
        "apt install package": "Cài đặt gói mới",
        "apt remove package": "Gỡ bỏ gói (giữ lại config)",
        "apt purge package": "Gỡ bỏ hoàn toàn gói và config",
        "apt autoremove": "Xóa các dependencies không cần thiết"
      },
      "Tìm kiếm & thông tin": {
        "apt search keyword": "Tìm kiếm gói theo từ khóa",
        "apt show package": "Hiển thị thông tin chi tiết gói",
        "apt list --installed": "Liệt kê các gói đã cài đặt",
        "apt list --upgradable": "Liệt kê gói có thể nâng cấp"
      }
    }
  },
  "yum": {
    "name": "YUM/DNF (Yellowdog Updater Modified)",
    "distributions": ["RHEL", "CentOS", "Fedora", "Rocky Linux"],
    "commands": {
      "Cập nhật hệ thống": {
        "dnf update": "Cập nhật toàn bộ hệ thống",
        "dnf upgrade": "Nâng cấp các gói (tương tự update)",
        "dnf check-update": "Kiểm tra gói có thể cập nhật"
      },
      "Quản lý gói": {
        "dnf install package": "Cài đặt gói mới",
        "dnf remove package": "Gỡ bỏ gói",
        "dnf reinstall package": "Cài đặt lại gói",
        "dnf autoremove": "Xóa dependencies không dùng"
      },
      "Tìm kiếm & thông tin": {
        "dnf search keyword": "Tìm kiếm gói",
        "dnf info package": "Thông tin chi tiết gói",
        "dnf list installed": "Danh sách gói đã cài",
        "dnf history": "Lịch sử cài đặt gói"
      }
    }
  },
  "pacman": {
    "name": "Pacman (Package Manager)",
    "distributions": ["Arch Linux", "Manjaro", "EndeavourOS"],
    "commands": {
      "Cập nhật hệ thống": {
        "pacman -Syu": "Cập nhật toàn bộ hệ thống",
        "pacman -Sy": "Đồng bộ cơ sở dữ liệu gói",
        "pacman -Su": "Nâng cấp các gói đã cài"
      },
      "Quản lý gói": {
        "pacman -S package": "Cài đặt gói",
        "pacman -R package": "Gỡ bỏ gói",
        "pacman -Rs package": "Gỡ bỏ gói và dependencies",
        "pacman -Sc": "Dọn dẹp cache gói"
      },
      "Tìm kiếm & thông tin": {
        "pacman -Ss keyword": "Tìm kiếm gói",
        "pacman -Si package": "Thông tin gói",
        "pacman -Q": "Liệt kê gói đã cài",
        "pacman -Qe": "Gói được cài explicitly"
      }
    }
  }
};

// Pro Tips Database
const proTips = {
  "network_engineering": {
    "name": "Network Engineering",
    "icon": "🌐",
    "tips": [
      {
        "title": "Phân tích log hiệu quả",
        "content": "Sử dụng less hoặc grep khi phân tích log files lớn. VD: tail -f /var/log/syslog | grep ERROR"
      },
      {
        "title": "Tạo alias cho lệnh dài", 
        "content": "alias bgp='sudo vtysh -c \"show ip bgp summary\"' - Lưu vào ~/.bashrc để dùng lâu dài"
      },
      {
        "title": "Pipeline mạnh mẽ",
        "content": "Kết hợp công cụ: netstat -tunap | grep :22 | awk '{print $5}' | cut -d: -f1 | sort | uniq"
      }
    ]
  },
  "system_admin": {
    "name": "System Administration",
    "icon": "⚙️",
    "tips": [
      {
        "title": "Backup trước khi sửa config",
        "content": "sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup.$(date +%Y%m%d)"
      },
      {
        "title": "Theo dõi resource usage",
        "content": "Dùng htop, iotop, nethogs để giám sát CPU, I/O và network theo thời gian thực"
      }
    ]
  }
};

// Application State
let _currentView = 'welcome';
let currentCategory = null;
let _currentCommand = null;
let bookmarks = JSON.parse(localStorage.getItem('linuxHandbookBookmarks') || '[]');

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing comprehensive Linux Handbook...');
    initializeApp();
});

function initializeApp() {
    renderCategories();
    renderPopularCommands();
    renderFilesystemTree();
    setupEventListeners();
    setupSearch();
    setupTools();
    updateBookmarksCount();
    console.log('Linux Handbook initialized successfully');
}

// Event Listeners Setup
function setupEventListeners() {
    console.log('Setting up event listeners...');
    
    // Mobile menu
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', toggleMobileMenu);
    }
    
    // App title click
    const appTitle = document.querySelector('.app-title');
    if (appTitle) {
        appTitle.addEventListener('click', () => showView('welcome'));
        appTitle.style.cursor = 'pointer';
    }
    
    // Navigation buttons
    setupNavigationButtons();
    
    // Tool navigation
    setupToolNavigation();
    
    // Bookmarks
    const bookmarkBtn = document.getElementById('bookmarkBtn');
    if (bookmarkBtn) {
        bookmarkBtn.addEventListener('click', toggleBookmarksPanel);
    }
    
    // Quick actions
    setupQuickActions();
    
    // Copy buttons - delegate to document
    document.addEventListener('click', handleCopyClick);
    
    // Close search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-container')) {
            hideSearchResults();
        }
    });
}

function setupNavigationButtons() {
    const buttons = [
        { id: 'backBtn', action: () => showView('welcome') },
        { id: 'backToCategory', action: () => currentCategory ? showCategoryView(currentCategory) : showView('welcome') }
    ];
    
    buttons.forEach(({ id, action }) => {
        const btn = document.getElementById(id);
        if (btn) {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                action();
            });
        }
    });
    
    // Back buttons with data-back attribute
    document.querySelectorAll('[data-back]').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            showView('welcome');
        });
    });
}

function setupToolNavigation() {
    document.querySelectorAll('[data-tool]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const tool = this.getAttribute('data-tool');
            console.log(`Tool clicked: ${tool}`);
            showToolView(tool);
        });
    });
}

function setupQuickActions() {
    document.querySelectorAll('[data-action]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const action = this.getAttribute('data-action');
            
            if (action === 'explore') {
                // Show first category
                const firstCategory = Object.keys(commandsData)[0];
                showCategoryView(firstCategory);
            } else if (action === 'search') {
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
    });
}

// Mobile Menu
function toggleMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        sidebar.classList.toggle('open');
    }
}

// View Management
function showView(viewName) {
    console.log(`Showing view: ${viewName}`);
    const views = ['welcomeScreen', 'categoryView', 'commandView'];
    const toolViews = ['chmodCalculator', 'cronBuilder', 'filesystemView', 'shortcutsView', 
                      'packageManagerView', 'environmentView', 'shellScriptingView', 
                      'proTipsView', 'troubleshootingView'];
    
    // Hide all views
    [...views, ...toolViews].forEach(view => {
        const element = document.getElementById(view);
        if (element) {
            element.classList.add('hidden');
        }
    });
    
    _currentView = viewName;
    
    // Show the requested view
    const viewMap = {
        'welcome': 'welcomeScreen',
        'category': 'categoryView', 
        'command': 'commandView'
    };
    
    const targetElement = document.getElementById(viewMap[viewName] || viewName);
    if (targetElement) {
        targetElement.classList.remove('hidden');
        console.log(`Successfully showed view: ${viewName}`);
    }
    
    // Close mobile menu
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        sidebar.classList.remove('open');
    }
}

function showToolView(toolName) {
    console.log(`Showing tool view: ${toolName}`);
    
    // Load tool content based on tool name
    switch (toolName) {
        case 'filesystem':
            showView('filesystemView');
            break;
        case 'shortcuts':
            loadKeyboardShortcuts();
            showView('shortcutsView');
            break;
        case 'package-manager':
            loadPackageManagerContent();
            showView('packageManagerView');
            break;
        case 'environment':
            loadEnvironmentContent();
            showView('environmentView');
            break;
        case 'shell-scripting':
            loadShellScriptingContent();
            showView('shellScriptingView');
            break;
        case 'pro-tips':
            loadProTipsContent();
            showView('proTipsView');
            break;
        case 'troubleshooting':
            loadTroubleshootingContent();
            showView('troubleshootingView');
            break;
        case 'chmod-calculator':
            showView('chmodCalculator');
            break;
        case 'cron-builder':
            showView('cronBuilder');
            break;
    }
}

// Categories Rendering
function renderCategories() {
    const categoriesList = document.getElementById('categoriesList');
    if (!categoriesList) return;
    
    categoriesList.innerHTML = '';
    
    Object.keys(commandsData).forEach(categoryKey => {
        const category = commandsData[categoryKey];
        const li = document.createElement('li');
        li.className = 'nav-item';
        
        li.innerHTML = `
            <button class="nav-link" data-category="${categoryKey}">
                <span class="nav-icon">${category.icon}</span>
                ${category.name}
                <span class="command-count">${Object.keys(category.commands || {}).length}</span>
            </button>
        `;
        
        const navLink = li.querySelector('.nav-link');
        navLink.addEventListener('click', function(e) {
            e.preventDefault();
            showCategoryView(categoryKey);
        });
        
        categoriesList.appendChild(li);
    });
    
    console.log(`Rendered ${Object.keys(commandsData).length} categories`);
}

// Popular Commands
function renderPopularCommands() {
    const popularCommandsContainer = document.getElementById('popularCommands');
    if (!popularCommandsContainer) return;
    
    const popularCommands = ['ls', 'chmod', 'grep', 'ssh', 'top', 'find'];
    popularCommandsContainer.innerHTML = '';
    
    popularCommands.forEach(commandName => {
        let foundCommand = null;
        let foundCategory = null;
        
        // Find command in categories
        Object.keys(commandsData).forEach(categoryKey => {
            const category = commandsData[categoryKey];
            if (category.commands && category.commands[commandName]) {
                foundCommand = category.commands[commandName];
                foundCategory = categoryKey;
            }
        });
        
        if (foundCommand) {
            const card = document.createElement('div');
            card.className = 'quick-command-card';
            card.innerHTML = `
                <div class="quick-command-name">${foundCommand.name}</div>
                <div class="quick-command-desc">${foundCommand.description}</div>
            `;
            
            card.addEventListener('click', function() {
                showCommandView(foundCategory, commandName);
            });
            
            popularCommandsContainer.appendChild(card);
        }
    });
}

// Category View
function showCategoryView(categoryKey) {
    currentCategory = categoryKey;
    const category = commandsData[categoryKey];
    if (!category) return;
    
    document.getElementById('categoryTitle').textContent = category.name;
    document.getElementById('categoryDescription').textContent = category.description;
    
    const commandsGrid = document.getElementById('commandsGrid');
    commandsGrid.innerHTML = '';
    
    Object.keys(category.commands).forEach(commandKey => {
        const command = category.commands[commandKey];
        const card = document.createElement('div');
        card.className = 'command-card';
        card.innerHTML = `
            <div class="command-card-category">${command.category || 'general'}</div>
            <div class="command-card-title">${command.name}</div>
            <div class="command-card-description">${command.description}</div>
        `;
        
        card.addEventListener('click', () => showCommandView(categoryKey, commandKey));
        commandsGrid.appendChild(card);
    });
    
    showView('category');
}

// Command Detail View
function showCommandView(categoryKey, commandKey) {
    currentCategory = categoryKey;
    _currentCommand = commandKey;
    
    const command = commandsData[categoryKey]?.commands?.[commandKey];
    if (!command) return;
    
    // Update command details
    document.getElementById('commandName').textContent = command.name;
    document.getElementById('commandDescription').textContent = command.description;
    document.getElementById('commandSyntax').textContent = command.syntax;
    
    // Render options
    const optionsContainer = document.getElementById('commandOptions');
    optionsContainer.innerHTML = '';
    if (command.common_options) {
        command.common_options.forEach(option => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-item';
            optionDiv.textContent = option;
            optionsContainer.appendChild(optionDiv);
        });
    }
    
    // Render examples
    const examplesContainer = document.getElementById('commandExamples');
    examplesContainer.innerHTML = '';
    if (command.examples) {
        command.examples.forEach(example => {
            const exampleDiv = document.createElement('div');
            exampleDiv.className = 'example-item';
            
            const parts = example.split(' # ');
            const code = parts[0];
            const explanation = parts[1] || '';
            
            exampleDiv.innerHTML = `
                <div class="example-code">
                    <code>${code}</code>
                    <button class="copy-btn" data-copy="example" data-text="${code}">📋</button>
                </div>
                ${explanation ? `<div class="example-explanation">${explanation}</div>` : ''}
            `;
            
            examplesContainer.appendChild(exampleDiv);
        });
    }
    
    // Render related commands
    if (command.related) {
        const relatedContainer = document.getElementById('relatedCommands');
        relatedContainer.innerHTML = '';
        command.related.forEach(relatedCmd => {
            const relatedSpan = document.createElement('span');
            relatedSpan.className = 'related-command';
            relatedSpan.textContent = relatedCmd;
            relatedSpan.addEventListener('click', () => {
                // Find and show related command
                Object.keys(commandsData).forEach(catKey => {
                    const cat = commandsData[catKey];
                    if (cat.commands && cat.commands[relatedCmd]) {
                        showCommandView(catKey, relatedCmd);
                    }
                });
            });
            relatedContainer.appendChild(relatedSpan);
        });
    }
    
    showView('command');
}

// Search Functionality
function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');
    
    if (!searchInput || !searchResults) return;
    
    let searchTimeout;
    searchInput.addEventListener('input', function(e) {
        clearTimeout(searchTimeout);
        const query = e.target.value.trim().toLowerCase();
        
        if (query.length < 2) {
            hideSearchResults();
            return;
        }
        
        searchTimeout = setTimeout(() => {
            const results = performSearch(query);
            renderSearchResults(results);
            if (results.length > 0) {
                searchResults.style.display = 'block';
            }
        }, 200);
    });
}

function performSearch(query) {
    const results = [];
    
    // Search in commands
    Object.keys(commandsData).forEach(categoryKey => {
        const category = commandsData[categoryKey];
        if (category.commands) {
            Object.keys(category.commands).forEach(commandKey => {
                const command = category.commands[commandKey];
                
                if (command.name.toLowerCase().includes(query) ||
                    command.description.toLowerCase().includes(query) ||
                    command.syntax.toLowerCase().includes(query)) {
                    results.push({
                        type: 'command',
                        category: categoryKey,
                        command: commandKey,
                        data: command
                    });
                }
            });
        }
    });
    
    // Search in filesystem
    Object.keys(filesystemData).forEach(path => {
        const fsItem = filesystemData[path];
        if (path.toLowerCase().includes(query) || 
            fsItem.description.toLowerCase().includes(query)) {
            results.push({
                type: 'filesystem',
                path: path,
                data: fsItem
            });
        }
    });
    
    return results.slice(0, 10);
}

function renderSearchResults(results) {
    const searchResults = document.getElementById('searchResults');
    searchResults.innerHTML = '';
    
    results.forEach(result => {
        const item = document.createElement('div');
        item.className = 'search-result-item';
        
        if (result.type === 'command') {
            item.innerHTML = `
                <div class="search-result-title">${result.data.name}</div>
                <div class="search-result-description">${result.data.description}</div>
            `;
            item.addEventListener('click', () => {
                showCommandView(result.category, result.command);
                hideSearchResults();
                document.getElementById('searchInput').value = '';
            });
        } else if (result.type === 'filesystem') {
            item.innerHTML = `
                <div class="search-result-title">${result.path}</div>
                <div class="search-result-description">${result.data.description}</div>
            `;
            item.addEventListener('click', () => {
                showToolView('filesystem');
                hideSearchResults();
                document.getElementById('searchInput').value = '';
            });
        }
        
        searchResults.appendChild(item);
    });
}

function hideSearchResults() {
    const searchResults = document.getElementById('searchResults');
    if (searchResults) {
        searchResults.style.display = 'none';
    }
}

// Tools Setup
function setupTools() {
    setupChmodCalculator();
    setupCronBuilder();
}

// Chmod Calculator
function setupChmodCalculator() {
    const checkboxes = document.querySelectorAll('#chmodCalculator input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateChmodCalculator);
    });
    
    // Preset buttons
    document.querySelectorAll('#chmodCalculator .preset-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const preset = this.getAttribute('data-preset');
            applyChmodPreset(preset);
        });
    });
    
    updateChmodCalculator();
}

function updateChmodCalculator() {
    const groups = ['owner', 'group', 'others'];
    let octalResult = '';
    let symbolicResult = '';
    
    groups.forEach(group => {
        const checkboxes = document.querySelectorAll(`input[data-group="${group}"]`);
        let total = 0;
        let symbolic = '';
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                total += parseInt(checkbox.value);
                const perm = checkbox.getAttribute('data-perm');
                symbolic += perm === 'read' ? 'r' : perm === 'write' ? 'w' : 'x';
            }
        });
        
        // Fill in missing permissions with dashes
        const perms = ['read', 'write', 'execute'];
        let fullSymbolic = '';
        perms.forEach(perm => {
            const checkbox = document.querySelector(`input[data-group="${group}"][data-perm="${perm}"]`);
            fullSymbolic += checkbox && checkbox.checked ? 
                (perm === 'read' ? 'r' : perm === 'write' ? 'w' : 'x') : '-';
        });
        
        symbolicResult += fullSymbolic;
        
        const totalElement = document.getElementById(`${group}Total`);
        if (totalElement) {
            totalElement.textContent = total;
        }
        octalResult += total;
    });
    
    const octalResultElement = document.getElementById('octalResult');
    const symbolicResultElement = document.getElementById('symbolicResult');
    
    if (octalResultElement) {
        octalResultElement.textContent = octalResult;
    }
    if (symbolicResultElement) {
        symbolicResultElement.textContent = symbolicResult;
    }
}

function applyChmodPreset(preset) {
    // Clear all checkboxes first
    document.querySelectorAll('#chmodCalculator input[type="checkbox"]').forEach(cb => {
        cb.checked = false;
    });
    
    const groups = ['owner', 'group', 'others'];
    for (let i = 0; i < preset.length; i++) {
        const digit = parseInt(preset[i]);
        const group = groups[i];
        
        if (digit & 4) document.querySelector(`input[data-group="${group}"][data-perm="read"]`).checked = true;
        if (digit & 2) document.querySelector(`input[data-group="${group}"][data-perm="write"]`).checked = true;
        if (digit & 1) document.querySelector(`input[data-group="${group}"][data-perm="execute"]`).checked = true;
    }
    
    updateChmodCalculator();
}

// Cron Builder
function setupCronBuilder() {
    const cronFields = ['cronMinute', 'cronHour', 'cronDay', 'cronMonth', 'cronWeekday'];
    
    cronFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', updateCronExpression);
        }
    });
    
    document.querySelectorAll('#cronBuilder .preset-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const preset = this.getAttribute('data-preset');
            setCronPreset(preset);
        });
    });
    
    updateCronExpression();
}

function setCronPreset(preset) {
    const parts = preset.split(' ');
    const fields = ['cronMinute', 'cronHour', 'cronDay', 'cronMonth', 'cronWeekday'];
    
    fields.forEach((fieldId, index) => {
        const field = document.getElementById(fieldId);
        if (field && parts[index]) {
            field.value = parts[index];
        }
    });
    
    updateCronExpression();
}

function updateCronExpression() {
    const minute = document.getElementById('cronMinute')?.value || '*';
    const hour = document.getElementById('cronHour')?.value || '*';
    const day = document.getElementById('cronDay')?.value || '*';
    const month = document.getElementById('cronMonth')?.value || '*';
    const weekday = document.getElementById('cronWeekday')?.value || '*';
    
    const expression = `${minute} ${hour} ${day} ${month} ${weekday}`;
    
    const cronExpression = document.getElementById('cronExpression');
    if (cronExpression) {
        cronExpression.textContent = expression;
    }
    
    const explanation = generateCronExplanation(minute, hour, day, month, weekday);
    const cronExplanation = document.getElementById('cronExplanation');
    if (cronExplanation) {
        cronExplanation.textContent = explanation;
    }
}

function generateCronExplanation(minute, hour, day, month, weekday) {
    const parts = [];
    
    if (minute === '*') parts.push('mỗi phút');
    else if (minute.includes('*/')) parts.push(`mỗi ${minute.replace('*/', '')} phút`);
    else parts.push(`lúc phút ${minute}`);
    
    if (hour === '*') parts.push('mỗi giờ');
    else if (hour.includes('*/')) parts.push(`mỗi ${hour.replace('*/', '')} giờ`);
    else parts.push(`lúc ${hour} giờ`);
    
    if (day === '*') parts.push('mỗi ngày');
    else parts.push(`ngày ${day}`);
    
    if (month === '*') parts.push('mỗi tháng');
    else parts.push(`tháng ${month}`);
    
    if (weekday === '*') parts.push('mọi ngày trong tuần');
    else parts.push(`thứ ${weekday}`);
    
    return `Chạy ${parts.join(', ')}`;
}

// Additional Tool Content Loaders
function renderFilesystemTree() {
    const filesystemTree = document.getElementById('filesystemTree');
    const _filesystemDetails = document.getElementById('filesystemDetails');
    
    if (!filesystemTree) return;
    
    filesystemTree.innerHTML = '';
    
    Object.keys(filesystemData).forEach(path => {
        const fsData = filesystemData[path];
        const item = document.createElement('div');
        item.className = 'fs-item';
        item.innerHTML = `
            <div class="fs-path">${path}</div>
            <div class="fs-description">${fsData.description}</div>
        `;
        
        item.addEventListener('click', () => {
            document.querySelectorAll('.fs-item').forEach(el => el.classList.remove('active'));
            item.classList.add('active');
            showFilesystemDetails(path, fsData);
        });
        
        filesystemTree.appendChild(item);
    });
}

function showFilesystemDetails(_path, fsData) {
    const detailsContainer = document.getElementById('directoryInfo');
    if (!detailsContainer) return;
    
    let html = `
        <h4>${fsData.name}</h4>
        <p><strong>Mô tả:</strong> ${fsData.description}</p>
        <p><strong>Mục đích:</strong> ${fsData.purpose}</p>
    `;
    
    if (fsData.examples) {
        html += `<p><strong>Ví dụ:</strong> ${fsData.examples.join(', ')}</p>`;
    }
    
    if (fsData.important_files) {
        html += '<h5>Tập tin quan trọng:</h5><ul>';
        Object.keys(fsData.important_files).forEach(file => {
            html += `<li><code>${file}</code>: ${fsData.important_files[file]}</li>`;
        });
        html += '</ul>';
    }
    
    detailsContainer.innerHTML = html;
}

function loadKeyboardShortcuts() {
    const container = document.getElementById('shortcutsCategories');
    if (!container) return;
    
    container.innerHTML = '';
    
    Object.keys(keyboardShortcuts).forEach(categoryKey => {
        const category = keyboardShortcuts[categoryKey];
        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'shortcuts-category';
        
        let html = `
            <h3>${category.icon} ${category.name}</h3>
            <div class="shortcuts-list">
        `;
        
        Object.keys(category.shortcuts).forEach(shortcut => {
            html += `
                <div class="shortcut-item">
                    <div class="shortcut-keys">${shortcut}</div>
                    <div class="shortcut-description">${category.shortcuts[shortcut]}</div>
                </div>
            `;
        });
        
        html += '</div>';
        categoryDiv.innerHTML = html;
        container.appendChild(categoryDiv);
    });
}

function loadPackageManagerContent() {
    setupPackageManagerTabs();
    showPackageManagerDistro('apt');
}

function setupPackageManagerTabs() {
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const distro = this.getAttribute('data-distro');
            
            document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            showPackageManagerDistro(distro);
        });
    });
}

function showPackageManagerDistro(distro) {
    const content = document.getElementById('packageManagerContent');
    if (!content || !packageManagers[distro]) return;
    
    const pm = packageManagers[distro];
    let html = `
        <h3>${pm.name}</h3>
        <p><strong>Distributions:</strong> ${pm.distributions.join(', ')}</p>
    `;
    
    Object.keys(pm.commands).forEach(groupName => {
        const group = pm.commands[groupName];
        html += `<div class="pm-command-group">
            <h4>${groupName}</h4>
        `;
        
        Object.keys(group).forEach(cmd => {
            html += `
                <div class="pm-command">
                    <div class="pm-command-syntax">${cmd}</div>
                    <div class="pm-command-desc">${group[cmd]}</div>
                </div>
            `;
        });
        
        html += '</div>';
    });
    
    content.innerHTML = html;
}

function loadEnvironmentContent() {
    const commonVars = document.getElementById('commonVariables');
    const configFilesContainer = document.getElementById('configFiles');
    
    if (commonVars) {
        commonVars.innerHTML = '';
        Object.keys(environmentVariables).forEach(varName => {
            const varData = environmentVariables[varName];
            const varDiv = document.createElement('div');
            varDiv.className = 'variable-item';
            varDiv.innerHTML = `
                <div class="variable-name">$${varName}</div>
                <div class="variable-description">${varData.description}</div>
                <div class="variable-example">${varData.example}</div>
                <div class="variable-usage"><strong>Sử dụng:</strong> ${varData.usage}</div>
            `;
            commonVars.appendChild(varDiv);
        });
    }
    
    if (configFilesContainer) {
        configFilesContainer.innerHTML = '';
        Object.keys(configFiles).forEach(fileName => {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'config-file-item';
            fileDiv.innerHTML = `
                <div class="config-file-name">${fileName}</div>
                <div class="config-file-description">${configFiles[fileName]}</div>
            `;
            configFilesContainer.appendChild(fileDiv);
        });
    }
}

function loadShellScriptingContent() {
    // Implement shell scripting content loader
    const content = document.getElementById('scriptingContent');
    if (content) {
        content.innerHTML = `
            <div class="scripting-section">
                <h3>Bash Scripting Cơ Bản</h3>
                <p>Shell scripting là một kỹ năng quan trọng trong Linux administration...</p>
            </div>
        `;
    }
}

function loadProTipsContent() {
    const container = document.getElementById('tipsCategories');
    if (!container) return;
    
    container.innerHTML = '';
    
    Object.keys(proTips).forEach(categoryKey => {
        const category = proTips[categoryKey];
        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'tips-category';
        
        let html = `
            <h3>${category.icon} ${category.name}</h3>
            <div class="tips-list">
        `;
        
        category.tips.forEach(tip => {
            html += `
                <div class="tip-item">
                    <h4>${tip.title}</h4>
                    <p>${tip.content}</p>
                </div>
            `;
        });
        
        html += '</div>';
        categoryDiv.innerHTML = html;
        container.appendChild(categoryDiv);
    });
}

function loadTroubleshootingContent() {
    // Implement troubleshooting content
    const content = document.getElementById('troubleCategories');
    if (content) {
        content.innerHTML = `
            <div class="trouble-category">
                <h3>🔧 Hệ Thống</h3>
                <p>Hướng dẫn xử lý các vấn đề hệ thống phổ biến...</p>
            </div>
        `;
    }
}

// Copy to Clipboard
function handleCopyClick(e) {
    if (!e.target.classList.contains('copy-btn')) return;
    
    e.preventDefault();
    const copyType = e.target.getAttribute('data-copy');
    let textToCopy = '';
    
    switch (copyType) {
        case 'syntax': {
            textToCopy = document.getElementById('commandSyntax')?.textContent || '';
            break;
        }
        case 'example': {
            textToCopy = e.target.getAttribute('data-text') || '';
            break;
        }
        case 'chmod': {
            const octalResult = document.getElementById('octalResult');
            textToCopy = octalResult ? `chmod ${octalResult.textContent}` : '';
            break;
        }
        case 'cron': {
            textToCopy = document.getElementById('cronExpression')?.textContent || '';
            break;
        }
    }
    
    if (textToCopy) {
        copyToClipboard(textToCopy, e.target);
    }
}

function copyToClipboard(text, button) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showCopySuccess(button);
        }).catch(() => {
            fallbackCopyText(text, button);
        });
    } else {
        fallbackCopyText(text, button);
    }
}

function fallbackCopyText(text, button) {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    textarea.select();
    
    try {
        document.execCommand('copy');
        showCopySuccess(button);
    } catch (err) {
        console.error('Copy failed:', err);
    } finally {
        document.body.removeChild(textarea);
    }
}

function showCopySuccess(button) {
    const originalText = button.textContent;
    button.textContent = '✅';
    setTimeout(() => {
        button.textContent = originalText;
    }, 1500);
}

// Bookmarks System
function toggleBookmarksPanel() {
    const panel = document.getElementById('bookmarksPanel');
    if (panel) {
        panel.classList.toggle('show');
        if (panel.classList.contains('show')) {
            renderBookmarks();
        }
    }
}

function _addBookmark(type, data) {
    const bookmark = {
        id: Date.now(),
        type: type,
        data: data,
        timestamp: new Date().toISOString()
    };
    
    bookmarks.push(bookmark);
    localStorage.setItem('linuxHandbookBookmarks', JSON.stringify(bookmarks));
    updateBookmarksCount();
}

function _removeBookmark(id) {
    bookmarks = bookmarks.filter(b => b.id !== id);
    localStorage.setItem('linuxHandbookBookmarks', JSON.stringify(bookmarks));
    updateBookmarksCount();
    renderBookmarks();
}

function renderBookmarks() {
    const content = document.getElementById('bookmarksContent');
    if (!content) return;
    
    if (bookmarks.length === 0) {
        content.innerHTML = '<p>Chưa có mục nào được đánh dấu</p>';
        return;
    }
    
    content.innerHTML = '';
    bookmarks.forEach(bookmark => {
        const item = document.createElement('div');
        item.className = 'bookmark-item';
        item.innerHTML = `
            <div>${bookmark.data.name || bookmark.data}</div>
            <button onclick="removeBookmark(${bookmark.id})">×</button>
        `;
        content.appendChild(item);
    });
}

function updateBookmarksCount() {
    const _count = bookmarks.length;
    // Update UI with bookmark count if needed
}

// Set light theme as default per project specification
document.body.setAttribute('data-color-scheme', 'light');

console.log('Comprehensive Linux Handbook JavaScript loaded successfully');