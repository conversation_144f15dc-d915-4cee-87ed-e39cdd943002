<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linux Handbook - Cẩ<PERSON> Di<PERSON>n Về Linux</title>
    <link rel="stylesheet" href="style.css">
</head>
<body data-color-scheme="dark">
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="terminal-icon">$</span>
                    Linux Handbook
                    <span class="version">v2.0</span>
                </h1>
                <div class="header-actions">
                    <div class="search-container">
                        <input type="text" id="searchInput" class="search-input" placeholder="Tìm kiếm lệnh, khái niệm...">
                        <div class="search-results" id="searchResults"></div>
                    </div>
                    <button id="bookmarkBtn" class="btn btn--outline btn--sm">
                        <span class="bookmark-icon">⭐</span>
                    </button>

                </div>
            </div>
        </header>

        <div class="main-layout">
            <!-- Sidebar -->
            <aside class="sidebar" id="sidebar">
                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <h3 class="nav-section-title">Danh Mục Lệnh</h3>
                        <ul class="nav-list" id="categoriesList">
                            <!-- Categories will be populated by JS -->
                        </ul>
                    </div>
                    
                    <div class="nav-section">
                        <h3 class="nav-section-title">Hệ Thống & Cấu Trúc</h3>
                        <ul class="nav-list">
                            <li class="nav-item">
                                <button class="nav-link" data-tool="filesystem">
                                    <span class="nav-icon">📁</span>
                                    Hệ Thống Tập Tin
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-tool="environment">
                                    <span class="nav-icon">🔧</span>
                                    Biến Môi Trường
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-tool="shell-scripting">
                                    <span class="nav-icon">📜</span>
                                    Shell Scripting
                                </button>
                            </li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3 class="nav-section-title">Công Cụ Tương Tác</h3>
                        <ul class="nav-list">
                            <li class="nav-item">
                                <button class="nav-link" data-tool="chmod-calculator">
                                    <span class="nav-icon">🔐</span>
                                    Chmod Calculator
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-tool="cron-builder">
                                    <span class="nav-icon">⏰</span>
                                    Cron Builder
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-tool="shortcuts">
                                    <span class="nav-icon">⌨️</span>
                                    Phím Tắt
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-tool="package-manager">
                                    <span class="nav-icon">📦</span>
                                    Quản Lý Gói
                                </button>
                            </li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3 class="nav-section-title">Kiến Thức Nâng Cao</h3>
                        <ul class="nav-list">
                            <li class="nav-item">
                                <button class="nav-link" data-tool="pro-tips">
                                    <span class="nav-icon">💡</span>
                                    Mẹo Pro
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-tool="troubleshooting">
                                    <span class="nav-icon">🔧</span>
                                    Xử Lý Sự Cố
                                </button>
                            </li>
                        </ul>
                    </div>
                </nav>
                
                <div class="sidebar-stats">
                    <div class="stats-item">
                        <span class="stats-number" id="totalCommands">200+</span>
                        <span class="stats-label">Lệnh</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-number">15</span>
                        <span class="stats-label">Danh Mục</span>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content" id="mainContent">
                <!-- Welcome Screen -->
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="welcome-hero">
                        <h1>Linux Handbook</h1>
                        <p class="hero-subtitle">Cẩm nang toàn diện về Linux với hơn 200+ lệnh, công cụ tương tác và kiến thức chuyên sâu</p>
                        
                        <div class="quick-actions">
                            <button class="btn btn--primary btn--lg" data-action="explore">
                                🚀 Khám Phá Ngay
                            </button>
                            <button class="btn btn--outline btn--lg" data-action="search">
                                🔍 Tìm Kiếm
                            </button>
                        </div>
                    </div>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-icon">📚</div>
                            <h3>15 Danh Mục</h3>
                            <p>Từ cơ bản đến nâng cao, tất cả được phân loại chi tiết</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">🔧</div>
                            <h3>Công Cụ Tương Tác</h3>
                            <p>Calculator, Builder và nhiều công cụ hữu ích khác</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">💡</div>
                            <h3>Mẹo Pro</h3>
                            <p>Kinh nghiệm từ các chuyên gia Linux và Network Engineering</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">📖</div>
                            <h3>Kiến Thức Sâu</h3>
                            <p>Shell scripting, system administration, và nhiều hơn nữa</p>
                        </div>
                    </div>

                    <div class="popular-commands">
                        <h2>Lệnh Phổ Biến</h2>
                        <div class="command-quick-grid" id="popularCommands">
                            <!-- Popular commands will be populated by JS -->
                        </div>
                    </div>
                </div>

                <!-- Category View -->
                <div class="content-view hidden" id="categoryView">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" id="backBtn">← Quay Lại</button>
                        <div class="header-info">
                            <h1 class="content-title" id="categoryTitle"></h1>
                            <p class="content-description" id="categoryDescription"></p>
                        </div>
                        <div class="header-actions">
                            <select id="sortFilter" class="form-control">
                                <option value="name">Sắp xếp theo tên</option>
                                <option value="difficulty">Độ khó</option>
                                <option value="category">Loại</option>
                            </select>
                        </div>
                    </div>
                    <div class="commands-grid" id="commandsGrid">
                        <!-- Commands will be populated by JS -->
                    </div>
                </div>

                <!-- Command Detail View -->
                <div class="content-view hidden" id="commandView">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" id="backToCategory">← Danh Mục</button>
                        <div class="header-info">
                            <h1 class="command-name" id="commandName"></h1>
                            <p class="command-description" id="commandDescription"></p>
                        </div>
                        <div class="header-actions">
                            <button class="btn btn--outline btn--sm" id="bookmarkCommand">⭐ Đánh dấu</button>
                        </div>
                    </div>
                    
                    <div class="command-content">
                        <div class="command-section">
                            <h3>Cú Pháp</h3>
                            <div class="code-block">
                                <code id="commandSyntax"></code>
                                <button class="copy-btn" data-copy="syntax">📋</button>
                            </div>
                        </div>
                        
                        <div class="command-section">
                            <h3>Tùy Chọn Phổ Biến</h3>
                            <div id="commandOptions" class="options-container"></div>
                        </div>
                        
                        <div class="command-section">
                            <h3>Ví Dụ Thực Tế</h3>
                            <div id="commandExamples" class="examples-container"></div>
                        </div>
                        
                        <div class="command-section" id="relatedSection">
                            <h3>Lệnh Liên Quan</h3>
                            <div id="relatedCommands" class="related-commands"></div>
                        </div>
                    </div>
                </div>

                <!-- Tools Views -->
                <div class="content-view hidden" id="chmodCalculator">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" data-back="chmod">← Quay Lại</button>
                        <div class="header-info">
                            <h1>Chmod Permission Calculator</h1>
                            <p>Tính toán và hiểu quyền truy cập tập tin trong Linux</p>
                        </div>
                    </div>
                    
                    <div class="chmod-calculator">
                        <div class="calculator-main">
                            <div class="permission-groups">
                                <div class="permission-group">
                                    <h3>Owner (Chủ sở hữu)</h3>
                                    <div class="permissions">
                                        <label><input type="checkbox" data-group="owner" data-perm="read" value="4"> Read (4)</label>
                                        <label><input type="checkbox" data-group="owner" data-perm="write" value="2"> Write (2)</label>
                                        <label><input type="checkbox" data-group="owner" data-perm="execute" value="1"> Execute (1)</label>
                                    </div>
                                    <div class="group-total" id="ownerTotal">0</div>
                                </div>
                                <div class="permission-group">
                                    <h3>Group (Nhóm)</h3>
                                    <div class="permissions">
                                        <label><input type="checkbox" data-group="group" data-perm="read" value="4"> Read (4)</label>
                                        <label><input type="checkbox" data-group="group" data-perm="write" value="2"> Write (2)</label>
                                        <label><input type="checkbox" data-group="group" data-perm="execute" value="1"> Execute (1)</label>
                                    </div>
                                    <div class="group-total" id="groupTotal">0</div>
                                </div>
                                <div class="permission-group">
                                    <h3>Others (Khác)</h3>
                                    <div class="permissions">
                                        <label><input type="checkbox" data-group="others" data-perm="read" value="4"> Read (4)</label>
                                        <label><input type="checkbox" data-group="others" data-perm="write" value="2"> Write (2)</label>
                                        <label><input type="checkbox" data-group="others" data-perm="execute" value="1"> Execute (1)</label>
                                    </div>
                                    <div class="group-total" id="othersTotal">0</div>
                                </div>
                            </div>
                            
                            <div class="chmod-result">
                                <h3>Kết Quả</h3>
                                <div class="result-display">
                                    <div class="octal-result">chmod <span id="octalResult">000</span></div>
                                    <div class="symbolic-result" id="symbolicResult">---------</div>
                                    <button class="copy-btn" data-copy="chmod">📋</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="chmod-presets">
                            <h3>Cấu Hình Phổ Biến</h3>
                            <div class="presets-grid">
                                <button class="preset-btn" data-preset="755">755 - Thực thi (rwxr-xr-x)</button>
                                <button class="preset-btn" data-preset="644">644 - Tập tin thường (rw-r--r--)</button>
                                <button class="preset-btn" data-preset="777">777 - Toàn quyền (rwxrwxrwx)</button>
                                <button class="preset-btn" data-preset="600">600 - Riêng tư (rw-------)</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-view hidden" id="cronBuilder">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" data-back="cron">← Quay Lại</button>
                        <div class="header-info">
                            <h1>Cron Expression Builder</h1>
                            <p>Tạo biểu thức cron để lập lịch tác vụ tự động</p>
                        </div>
                    </div>
                    
                    <div class="cron-builder">
                        <div class="cron-visual">
                            <div class="cron-fields">
                                <div class="field-group">
                                    <label>Phút (0-59)</label>
                                    <input type="text" id="cronMinute" placeholder="*" value="*">
                                    <span class="field-help">* = mỗi phút</span>
                                </div>
                                <div class="field-group">
                                    <label>Giờ (0-23)</label>
                                    <input type="text" id="cronHour" placeholder="*" value="*">
                                    <span class="field-help">* = mỗi giờ</span>
                                </div>
                                <div class="field-group">
                                    <label>Ngày (1-31)</label>
                                    <input type="text" id="cronDay" placeholder="*" value="*">
                                    <span class="field-help">* = mỗi ngày</span>
                                </div>
                                <div class="field-group">
                                    <label>Tháng (1-12)</label>
                                    <input type="text" id="cronMonth" placeholder="*" value="*">
                                    <span class="field-help">* = mỗi tháng</span>
                                </div>
                                <div class="field-group">
                                    <label>Thứ (0-7)</label>
                                    <input type="text" id="cronWeekday" placeholder="*" value="*">
                                    <span class="field-help">0,7 = CN</span>
                                </div>
                            </div>
                            
                            <div class="cron-result">
                                <h3>Biểu Thức Cron</h3>
                                <div class="result-display">
                                    <code id="cronExpression">* * * * *</code>
                                    <button class="copy-btn" data-copy="cron">📋</button>
                                </div>
                                <div class="cron-explanation" id="cronExplanation"></div>
                            </div>
                        </div>
                        
                        <div class="cron-presets">
                            <h3>Lịch Có Sẵn</h3>
                            <div class="presets-grid">
                                <button class="preset-btn" data-preset="0 0 * * *">Hàng ngày lúc nửa đêm</button>
                                <button class="preset-btn" data-preset="0 */6 * * *">Mỗi 6 tiếng</button>
                                <button class="preset-btn" data-preset="0 0 * * 0">Hàng tuần Chủ nhật</button>
                                <button class="preset-btn" data-preset="0 0 1 * *">Hàng tháng ngày 1</button>
                                <button class="preset-btn" data-preset="*/5 * * * *">Mỗi 5 phút</button>
                                <button class="preset-btn" data-preset="0 9 * * 1-5">9h sáng các ngày làm việc</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- New comprehensive tools -->
                <div class="content-view hidden" id="filesystemView">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" data-back="filesystem">← Quay Lại</button>
                        <div class="header-info">
                            <h1>Hệ Thống Tập Tin Linux (FHS)</h1>
                            <p>Cấu trúc thư mục chuẩn trong Linux Filesystem Hierarchy Standard</p>
                        </div>
                    </div>
                    
                    <div class="filesystem-explorer">
                        <div class="filesystem-tree" id="filesystemTree"></div>
                        <div class="filesystem-details" id="filesystemDetails">
                            <h3>Chi Tiết Thư Mục</h3>
                            <div class="directory-info" id="directoryInfo">
                                <p>Chọn một thư mục để xem chi tiết</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-view hidden" id="shortcutsView">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" data-back="shortcuts">← Quay Lại</button>
                        <div class="header-info">
                            <h1>Phím Tắt Terminal</h1>
                            <p>Làm chủ các phím tắt để tăng tốc độ làm việc với terminal</p>
                        </div>
                    </div>
                    
                    <div class="shortcuts-container">
                        <div class="shortcuts-categories" id="shortcutsCategories"></div>
                    </div>
                </div>

                <div class="content-view hidden" id="packageManagerView">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" data-back="package-manager">← Quay Lại</button>
                        <div class="header-info">
                            <h1>Quản Lý Gói</h1>
                            <p>Hướng dẫn sử dụng package manager trên các distro khác nhau</p>
                        </div>
                    </div>
                    
                    <div class="package-manager-content">
                        <div class="distro-tabs">
                            <button class="tab-btn active" data-distro="apt">Debian/Ubuntu (APT)</button>
                            <button class="tab-btn" data-distro="yum">RedHat/CentOS (YUM/DNF)</button>
                            <button class="tab-btn" data-distro="pacman">Arch (Pacman)</button>
                        </div>
                        <div class="distro-content" id="packageManagerContent"></div>
                    </div>
                </div>

                <div class="content-view hidden" id="environmentView">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" data-back="environment">← Quay Lại</button>
                        <div class="header-info">
                            <h1>Biến Môi Trường</h1>
                            <p>Hiểu và cấu hình biến môi trường trong Linux</p>
                        </div>
                    </div>
                    
                    <div class="environment-content">
                        <div class="env-sections">
                            <div class="env-section">
                                <h3>Biến Môi Trường Thường Dùng</h3>
                                <div id="commonVariables" class="variables-list"></div>
                            </div>
                            <div class="env-section">
                                <h3>Tập Tin Cấu Hình</h3>
                                <div id="configFiles" class="config-files-list"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-view hidden" id="shellScriptingView">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" data-back="shell-scripting">← Quay Lại</button>
                        <div class="header-info">
                            <h1>Shell Scripting</h1>
                            <p>Hướng dẫn toàn diện về Bash shell scripting</p>
                        </div>
                    </div>
                    
                    <div class="scripting-content">
                        <div class="scripting-nav">
                            <button class="script-tab active" data-tab="basics">Cơ Bản</button>
                            <button class="script-tab" data-tab="variables">Biến</button>
                            <button class="script-tab" data-tab="control">Điều Khiển</button>
                            <button class="script-tab" data-tab="functions">Hàm</button>
                            <button class="script-tab" data-tab="advanced">Nâng Cao</button>
                        </div>
                        <div class="scripting-content-area" id="scriptingContent"></div>
                    </div>
                </div>

                <div class="content-view hidden" id="proTipsView">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" data-back="pro-tips">← Quay Lại</button>
                        <div class="header-info">
                            <h1>Mẹo Pro & Best Practices</h1>
                            <p>Kinh nghiệm và mẹo hay từ các chuyên gia Linux</p>
                        </div>
                    </div>
                    
                    <div class="pro-tips-content">
                        <div class="tips-categories" id="tipsCategories"></div>
                    </div>
                </div>

                <div class="content-view hidden" id="troubleshootingView">
                    <div class="content-header">
                        <button class="btn btn--outline btn--sm back-btn" data-back="troubleshooting">← Quay Lại</button>
                        <div class="header-info">
                            <h1>Xử Lý Sự Cố</h1>
                            <p>Hướng dẫn chẩn đoán và khắc phục các vấn đề thường gặp</p>
                        </div>
                    </div>
                    
                    <div class="troubleshooting-content">
                        <div class="trouble-categories" id="troubleCategories"></div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Mobile menu toggle -->
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
        <span></span>
        <span></span>
        <span></span>
    </button>

    <!-- Bookmarks Panel -->
    <div class="bookmarks-panel hidden" id="bookmarksPanel">
        <div class="bookmarks-header">
            <h3>Đánh Dấu</h3>
            <button class="close-btn" id="closeBookmarks">×</button>
        </div>
        <div class="bookmarks-content" id="bookmarksContent">
            <p>Chưa có mục nào được đánh dấu</p>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>