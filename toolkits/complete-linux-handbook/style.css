:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
    
    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-charcoal-700);
    --color-surface: var(--color-charcoal-800);
    --color-text: var(--color-gray-200);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
    --color-primary: var(--color-teal-300);
    --color-primary-hover: var(--color-teal-400);
    --color-primary-active: var(--color-teal-800);
    --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-teal-300);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-gray-300);
    --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
    --color-btn-primary-text: var(--color-slate-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}



[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  
  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* Enhanced Linux Handbook Styles */

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
}

.header {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-16) var(--space-24);
  max-width: var(--container-xl);
  margin: 0 auto;
}

.app-title {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.app-title:hover {
  color: var(--color-primary);
}

.terminal-icon {
  color: var(--color-primary);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-3xl);
}

.version {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  background: var(--color-bg-1);
  padding: var(--space-2) var(--space-6);
  border-radius: var(--radius-full);
  margin-left: var(--space-8);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.search-container {
  position: relative;
}

.search-input {
  width: 320px;
  padding: var(--space-8) var(--space-12);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--font-size-base);
  transition: all var(--duration-fast) var(--ease-standard);
}

.search-input:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: var(--focus-ring);
  width: 380px;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-top: none;
  border-radius: 0 0 var(--radius-base) var(--radius-base);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
  box-shadow: var(--shadow-lg);
}

.search-result-item {
  padding: var(--space-12) var(--space-16);
  cursor: pointer;
  border-bottom: 1px solid var(--color-card-border);
  transition: background var(--duration-fast) var(--ease-standard);
}

.search-result-item:hover {
  background: var(--color-secondary);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-title {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-4);
  font-family: var(--font-family-mono);
}

.search-result-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.bookmark-icon,
.theme-icon {
  font-size: var(--font-size-lg);
}

.main-layout {
  display: flex;
  flex: 1;
  max-width: var(--container-xl);
  margin: 0 auto;
  width: 100%;
}

.sidebar {
  width: 300px;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  padding: var(--space-24);
  height: calc(100vh - 80px);
  overflow-y: auto;
  position: sticky;
  top: 80px;
  display: flex;
  flex-direction: column;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
  flex: 1;
}

.nav-section-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-12);
  padding-left: var(--space-4);
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  padding: var(--space-10) var(--space-12);
  border-radius: var(--radius-base);
  color: var(--color-text);
  text-decoration: none;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-fast) var(--ease-standard);
  border: none;
  background: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

.nav-link:hover {
  background: var(--color-secondary);
  color: var(--color-text);
  transform: translateX(4px);
}

.nav-link.active {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.nav-icon {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  min-width: 20px;
}

.sidebar-stats {
  border-top: 1px solid var(--color-border);
  padding-top: var(--space-16);
  margin-top: var(--space-16);
  display: flex;
  justify-content: space-around;
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.stats-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  text-transform: uppercase;
}

.main-content {
  flex: 1;
  padding: var(--space-32);
  overflow-y: auto;
  min-height: calc(100vh - 80px);
}

/* Welcome Screen */
.welcome-screen {
  max-width: 1000px;
  margin: 0 auto;
}

.welcome-hero {
  text-align: center;
  margin-bottom: var(--space-32);
  padding: var(--space-32) 0;
  background: linear-gradient(135deg, var(--color-bg-1), var(--color-bg-2));
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
}

.welcome-hero h1 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--space-16);
  color: var(--color-text);
  font-weight: var(--font-weight-bold);
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-32);
  line-height: var(--line-height-normal);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.quick-actions {
  display: flex;
  gap: var(--space-16);
  justify-content: center;
  align-items: center;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--space-20);
  margin-bottom: var(--space-32);
}

.feature-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  text-align: center;
  transition: all var(--duration-normal) var(--ease-standard);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.feature-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--space-16);
  display: block;
}

.feature-card h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-12);
  color: var(--color-text);
}

.feature-card p {
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin: 0;
}

.popular-commands h2 {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-24);
  color: var(--color-text);
  text-align: center;
}

.command-quick-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-12);
}

.quick-command-card {
  background: var(--color-bg-3);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  padding: var(--space-16);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  text-align: center;
}

.quick-command-card:hover {
  background: var(--color-bg-4);
  transform: translateY(-2px);
}

.quick-command-name {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.quick-command-desc {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Content Views */
.content-view {
  max-width: 1200px;
  margin: 0 auto;
}

.content-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--space-32);
  padding-bottom: var(--space-20);
  border-bottom: 2px solid var(--color-border);
  gap: var(--space-20);
}

.header-info {
  flex: 1;
}

.content-title,
.command-name {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-12);
  color: var(--color-text);
  font-weight: var(--font-weight-bold);
}

.content-description,
.command-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--space-12);
  align-items: flex-start;
}

.commands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-20);
}

.command-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
  overflow: hidden;
}

.command-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--color-primary);
  transform: scaleX(0);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.command-card:hover::before {
  transform: scaleX(1);
}

.command-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-4px);
}

.command-card-title {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-8);
}

.command-card-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  margin: 0;
}

.command-card-category {
  position: absolute;
  top: var(--space-12);
  right: var(--space-12);
  font-size: var(--font-size-xs);
  background: var(--color-bg-5);
  color: var(--color-text-secondary);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  font-weight: var(--font-weight-medium);
}

/* Command Content */
.command-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-32);
}

.command-section h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-16);
  color: var(--color-text);
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.command-section h3::before {
  content: '▶';
  color: var(--color-primary);
  font-size: var(--font-size-sm);
}

.code-block {
  position: relative;
  background: var(--color-bg-2);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  padding: var(--space-16);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  color: var(--color-text);
  overflow-x: auto;
  box-shadow: var(--shadow-inset-sm);
}

.copy-btn {
  position: absolute;
  top: var(--space-8);
  right: var(--space-8);
  background: var(--color-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  color: var(--color-text);
}

.copy-btn:hover {
  background: var(--color-secondary-hover);
  transform: scale(1.05);
}

.options-container {
  display: grid;
  gap: var(--space-12);
}

.option-item {
  background: var(--color-bg-3);
  padding: var(--space-12) var(--space-16);
  border-radius: var(--radius-base);
  border-left: 4px solid var(--color-primary);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  color: var(--color-text);
}

.examples-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.example-item {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.example-code {
  background: var(--color-bg-4);
  padding: var(--space-12) var(--space-16);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  color: var(--color-text);
  border-bottom: 1px solid var(--color-card-border);
  position: relative;
}

.example-explanation {
  padding: var(--space-12) var(--space-16);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  background: var(--color-surface);
}

.related-commands {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-8);
}

.related-command {
  background: var(--color-bg-6);
  color: var(--color-text);
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  border: 1px solid var(--color-card-border);
}

.related-command:hover {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  transform: translateY(-2px);
}

/* Tools Styles */
.chmod-calculator {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
}

.calculator-main {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-24);
}

.permission-groups {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-20);
}

.permission-group {
  background: var(--color-bg-1);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
}

.permission-group h3 {
  margin-bottom: var(--space-16);
  color: var(--color-text);
  font-size: var(--font-size-lg);
  text-align: center;
}

.permissions {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
  margin-bottom: var(--space-16);
}

.permissions label {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  font-size: var(--font-size-base);
  color: var(--color-text);
  cursor: pointer;
  padding: var(--space-6);
  border-radius: var(--radius-sm);
  transition: background var(--duration-fast) var(--ease-standard);
}

.permissions label:hover {
  background: var(--color-secondary);
}

.permissions input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--color-primary);
  cursor: pointer;
}

.group-total {
  text-align: center;
  padding: var(--space-12);
  background: var(--color-surface);
  border-radius: var(--radius-base);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}

.chmod-result {
  background: var(--color-bg-2);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
}

.chmod-result h3 {
  margin-bottom: var(--space-16);
  text-align: center;
}

.result-display {
  position: relative;
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  padding: var(--space-16);
  text-align: center;
}

.octal-result {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.symbolic-result {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.chmod-presets,
.cron-presets {
  background: var(--color-bg-3);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
}

.chmod-presets h3,
.cron-presets h3 {
  margin-bottom: var(--space-16);
  color: var(--color-text);
}

.presets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-12);
}

.preset-btn {
  display: block;
  width: 100%;
  padding: var(--space-10) var(--space-12);
  background: var(--color-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  color: var(--color-text);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  text-align: left;
}

.preset-btn:hover {
  background: var(--color-secondary-hover);
  transform: translateY(-2px);
}

/* Cron Builder */
.cron-builder {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
}

.cron-visual {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-24);
}

.cron-fields {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--space-16);
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.field-group label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.field-group input {
  padding: var(--space-8) var(--space-12);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  background: var(--color-surface);
  color: var(--color-text);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  text-align: center;
}

.field-help {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  text-align: center;
}

.cron-result {
  background: var(--color-bg-4);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
}

.cron-explanation {
  margin-top: var(--space-12);
  padding: var(--space-12);
  background: var(--color-surface);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  text-align: center;
}

/* Filesystem Explorer */
.filesystem-explorer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-24);
}

.filesystem-tree {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  max-height: 600px;
  overflow-y: auto;
}

.fs-item {
  padding: var(--space-8) var(--space-12);
  border-bottom: 1px solid var(--color-card-border-inner);
  display: flex;
  align-items: center;
  gap: var(--space-12);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  border-radius: var(--radius-sm);
}

.fs-item:hover {
  background: var(--color-secondary);
}

.fs-item.active {
  background: var(--color-bg-1);
  border-color: var(--color-primary);
}

.fs-path {
  font-family: var(--font-family-mono);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  min-width: 120px;
}

.fs-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.filesystem-details {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
}

.directory-info {
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

.directory-info h4 {
  color: var(--color-text);
  margin-bottom: var(--space-12);
}

/* Tabs System */
.distro-tabs,
.scripting-nav {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-24);
  border-bottom: 2px solid var(--color-border);
}

.tab-btn,
.script-tab {
  padding: var(--space-12) var(--space-20);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  border-radius: var(--radius-base) var(--radius-base) 0 0;
  position: relative;
}

.tab-btn:hover,
.script-tab:hover {
  color: var(--color-text);
  background: var(--color-secondary);
}

.tab-btn.active,
.script-tab.active {
  color: var(--color-primary);
  background: var(--color-bg-1);
}

.tab-btn.active::after,
.script-tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
}

/* Shortcuts Display */
.shortcuts-categories {
  display: grid;
  gap: var(--space-24);
}

.shortcuts-category {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
}

.shortcuts-category h3 {
  margin-bottom: var(--space-16);
  color: var(--color-text);
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.shortcuts-list {
  display: grid;
  gap: var(--space-12);
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-10) var(--space-12);
  background: var(--color-bg-1);
  border-radius: var(--radius-base);
}

.shortcut-keys {
  font-family: var(--font-family-mono);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  background: var(--color-surface);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-card-border);
}

.shortcut-description {
  color: var(--color-text-secondary);
  flex: 1;
  margin-left: var(--space-16);
}

/* Package Manager Content */
.distro-content {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
}

.pm-command-group {
  margin-bottom: var(--space-20);
}

.pm-command-group h4 {
  color: var(--color-text);
  margin-bottom: var(--space-12);
  font-size: var(--font-size-lg);
}

.pm-command {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-10) var(--space-12);
  background: var(--color-bg-2);
  border-radius: var(--radius-base);
  margin-bottom: var(--space-8);
}

.pm-command-syntax {
  font-family: var(--font-family-mono);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.pm-command-desc {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Environment Variables */
.env-sections {
  display: grid;
  gap: var(--space-24);
}

.env-section {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
}

.variables-list,
.config-files-list {
  display: grid;
  gap: var(--space-12);
}

.variable-item,
.config-file-item {
  background: var(--color-bg-3);
  padding: var(--space-12) var(--space-16);
  border-radius: var(--radius-base);
  border-left: 4px solid var(--color-primary);
}

.variable-name,
.config-file-name {
  font-family: var(--font-family-mono);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.variable-description,
.config-file-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-8);
}

.variable-example {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  background: var(--color-surface);
  padding: var(--space-6) var(--space-8);
  border-radius: var(--radius-sm);
  color: var(--color-text);
}

/* Bookmarks Panel */
.bookmarks-panel {
  position: fixed;
  top: 80px;
  right: 0;
  width: 300px;
  height: calc(100vh - 80px);
  background: var(--color-surface);
  border-left: 1px solid var(--color-border);
  z-index: 150;
  transform: translateX(100%);
  transition: transform var(--duration-normal) var(--ease-standard);
  display: flex;
  flex-direction: column;
}

.bookmarks-panel.show {
  transform: translateX(0);
}

.bookmarks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16) var(--space-20);
  border-bottom: 1px solid var(--color-border);
}

.bookmarks-header h3 {
  margin: 0;
  color: var(--color-text);
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-4);
  border-radius: var(--radius-sm);
  transition: all var(--duration-fast) var(--ease-standard);
}

.close-btn:hover {
  background: var(--color-secondary);
  color: var(--color-text);
}

.bookmarks-content {
  flex: 1;
  padding: var(--space-16) var(--space-20);
  overflow-y: auto;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  position: fixed;
  top: var(--space-20);
  right: var(--space-20);
  z-index: 200;
  background: var(--color-primary);
  border: none;
  border-radius: var(--radius-base);
  width: 48px;
  height: 48px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
  cursor: pointer;
  box-shadow: var(--shadow-lg);
}

.mobile-menu-toggle span {
  display: block;
  width: 20px;
  height: 2px;
  background: var(--color-btn-primary-text);
  transition: all var(--duration-fast) var(--ease-standard);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.back-btn {
  align-self: flex-start;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-layout {
    flex-direction: column;
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: -300px;
    width: 300px;
    height: 100vh;
    z-index: 150;
    transition: left var(--duration-normal) var(--ease-standard);
    box-shadow: var(--shadow-lg);
  }

  .sidebar.open {
    left: 0;
  }

  .main-content {
    padding: var(--space-24);
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .calculator-main,
  .cron-visual,
  .filesystem-explorer {
    grid-template-columns: 1fr;
  }

  .permission-groups {
    grid-template-columns: 1fr;
  }

  .cron-fields {
    grid-template-columns: repeat(2, 1fr);
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    align-self: stretch;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: var(--space-12) var(--space-16);
    flex-wrap: wrap;
    gap: var(--space-12);
  }

  .app-title {
    font-size: var(--font-size-xl);
  }

  .version {
    display: none;
  }

  .search-input {
    width: 200px;
  }

  .search-input:focus {
    width: 240px;
  }

  .main-content {
    padding: var(--space-16);
  }

  .welcome-hero h1 {
    font-size: var(--font-size-3xl);
  }

  .quick-actions {
    flex-direction: column;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .command-quick-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .commands-grid {
    grid-template-columns: 1fr;
  }

  .distro-tabs,
  .scripting-nav {
    flex-wrap: wrap;
  }

  .shortcuts-list {
    gap: var(--space-8);
  }

  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-8);
  }

  .shortcut-description {
    margin-left: 0;
  }

  .bookmarks-panel {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .search-input {
    width: 100%;
    max-width: 300px;
  }

  .search-input:focus {
    width: 100%;
  }

  .app-title {
    font-size: var(--font-size-lg);
    justify-content: center;
  }

  .welcome-hero h1 {
    font-size: var(--font-size-2xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
  }

  .command-quick-grid {
    grid-template-columns: 1fr;
  }

  .cron-fields {
    grid-template-columns: 1fr;
  }

  .presets-grid {
    grid-template-columns: 1fr;
  }

  .content-title,
  .command-name {
    font-size: var(--font-size-2xl);
  }
}

/* Theme-specific adjustments */
[data-color-scheme="light"] .terminal-icon {
  color: var(--color-primary);
}

[data-color-scheme="light"] .theme-icon::after {
  content: '☀️';
}

[data-color-scheme="dark"] .theme-icon::after {
  content: '🌙';
}

/* Print styles */
@media print {
  .header,
  .sidebar,
  .mobile-menu-toggle,
  .bookmarks-panel {
    display: none;
  }

  .main-content {
    padding: 0;
  }

  .copy-btn {
    display: none;
  }

  .command-card {
    break-inside: avoid;
  }
}