<!DOCTYPE html>
<html lang="vi" data-color-scheme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cẩm <PERSON>ần M<PERSON> - B<PERSON>ng <PERSON>ụ Tương <PERSON>c <PERSON> Trình Viên</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏛️</text></svg>">
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🏛️</span>
                    <h1>Cẩ<PERSON>ầ<PERSON>ề<PERSON></h1>
                    <span class="tagline">Bộ <PERSON>ông <PERSON>ơng <PERSON>c <PERSON>p Trình Viên</span>
                </div>
                <div class="search-container">
                    <input type="text" id="searchInput" placeholder="Tìm kiếm trong cẩm nang..." class="search-input">
                    <button class="search-btn">🔍</button>
                </div>

            </div>
        </header>

        <div class="main-container">
            <!-- Sidebar Navigation -->
            <nav class="sidebar" id="sidebar">
                <div class="sidebar-toggle" id="sidebarToggle">
                    <span>☰</span>
                </div>
                <div class="nav-sections">
                    <div class="nav-item active" data-section="getting_started">
                        <span class="nav-icon">🚀</span>
                        <span class="nav-text">Bắt Đầu</span>
                    </div>
                    <div class="nav-item" data-section="mindset">
                        <span class="nav-icon">🧠</span>
                        <span class="nav-text">Tư Duy Nhà Khảo Cổ</span>
                    </div>
                    <div class="nav-item" data-section="process">
                        <span class="nav-icon">🔄</span>
                        <span class="nav-text">Quy Trình Phổ Quát</span>
                    </div>
                    <div class="nav-item" data-section="tools">
                        <span class="nav-icon">🛠️</span>
                        <span class="nav-text">Công Cụ & Kỹ Thuật</span>
                    </div>
                    <div class="nav-item" data-section="patterns">
                        <span class="nav-icon">🔍</span>
                        <span class="nav-text">Nhận Dạng Mẫu</span>
                    </div>
                    <div class="nav-item" data-section="labs">
                        <span class="nav-icon">🧪</span>
                        <span class="nav-text">Phòng Lab Tương Tác</span>
                    </div>
                    <div class="nav-item" data-section="checklists">
                        <span class="nav-icon">✅</span>
                        <span class="nav-text">Danh Sách Kiểm Tra & Mẫu</span>
                    </div>
                    <div class="nav-item" data-section="case_studies">
                        <span class="nav-icon">📚</span>
                        <span class="nav-text">Nghiên Cứu Tình Huống</span>
                    </div>
                    <div class="nav-item" data-section="quick_reference">
                        <span class="nav-icon">⚡</span>
                        <span class="nav-text">Tham Khảo Nhanh</span>
                    </div>
                    <div class="nav-item" data-section="resources">
                        <span class="nav-icon">🔗</span>
                        <span class="nav-text">Tài Nguyên & Liên Kết</span>
                    </div>
                </div>
            </nav>

            <!-- Main Content Area -->
            <main class="content" id="content">
                <!-- Getting Started Section -->
                <section id="getting_started" class="content-section active">
                    <div class="section-header">
                        <h2><span class="section-icon">🚀</span> Bắt Đầu</h2>
                        <div class="section-actions">
                            <button class="bookmark-btn" data-section="getting_started">🔖</button>
                            <button class="print-btn">🖨️</button>
                        </div>
                    </div>
                    
                    <div class="intro-card">
                        <h3>Khảo Cổ Phần Mềm là gì?</h3>
                        <p>Khảo Cổ Phần Mềm là việc nghiên cứu có hệ thống các triển khai phần mềm legacy được tài liệu hóa kém hoặc không có tài liệu. Được đặt tên theo phép loại suy với khảo cổ học, nó bao gồm reverse engineering các module phần mềm và áp dụng các công cụ và quy trình khác nhau để trích xuất và hiểu cấu trúc chương trình.</p>
                    </div>

                    <div class="expandable-section">
                        <h3 class="expandable-header">Tại sao Kỹ năng này Quan trọng cho Sự nghiệp của Bạn <span class="expand-icon">▼</span></h3>
                        <div class="expandable-content">
                            <ul class="feature-list">
                                <li>Hệ thống legacy điều khiển các hoạt động kinh doanh quan trọng trên toàn thế giới</li>
                                <li>Những nhà phát triển ban đầu thường không còn có sẵn</li>
                                <li>Tài liệu thường xuyên bị thiếu hoặc lỗi thời</li>
                                <li>Tính liên tục kinh doanh phụ thuộc vào việc hiểu các hệ thống này</li>
                                <li>Thăng tiến nghề nghiệp đòi hỏi khả năng làm việc với codebase hiện có</li>
                            </ul>
                        </div>
                    </div>

                    <div class="quick-start-guide">
                        <h3>Hướng Dẫn Bắt Đầu Nhanh</h3>
                        <div class="steps-container">
                            <div class="step-item">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h4>Đảm bảo bạn có thể build và chạy hệ thống</h4>
                                    <p>Ưu tiên đầu tiên: thiết lập môi trường phát triển hoạt động</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h4>Xác định các điểm vào chính</h4>
                                    <p>Tìm main(), index.js, routes.ts, Program.cs - nơi thực thi bắt đầu</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h4>Lập bản đồ kiến trúc cấp cao</h4>
                                    <p>Hiểu cấu trúc tổng thể trước khi đi sâu vào chi tiết</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h4>Chọn một tính năng để theo dõi</h4>
                                    <p>Bắt đầu nhỏ - chọn một luồng nghiệp vụ để hiểu hoàn toàn</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">5</div>
                                <div class="step-content">
                                    <h4>Ghi chép những phát hiện của bạn</h4>
                                    <p>Xây dựng mô hình tinh thần và chia sẻ kiến thức với nhóm</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="call-to-action">
                        <h3>Sẵn sàng Bắt đầu?</h3>
                        <p>Điều hướng đến phần <strong>Quy Trình Phổ Quát</strong> để học phương pháp 5 bước có hệ thống hoạt động với bất kỳ codebase, ngôn ngữ hoặc framework nào.</p>
                        <button class="btn btn--primary" data-navigate="process">Bắt đầu với Quy Trình →</button>
                    </div>
                </section>

                <!-- Mindset Section -->
                <section id="mindset" class="content-section">
                    <div class="section-header">
                        <h2><span class="section-icon">🧠</span> The Archaeologist's Mindset</h2>
                        <div class="section-actions">
                            <button class="bookmark-btn" data-section="mindset">🔖</button>
                            <button class="print-btn">🖨️</button>
                        </div>
                    </div>

                    <div class="principle-quote">
                        <blockquote>
                            <p>"Seek first to understand, then to be understood (or to change)."</p>
                            <cite>The Supreme Principle of Software Archaeology</cite>
                        </blockquote>
                    </div>

                    <div class="principles-grid">
                        <div class="principle-card">
                            <h3>🕵️ Detective Approach</h3>
                            <p>Everything is a clue - logs, variables, configs, comments. Start with testable hypotheses.</p>
                            <div class="example">
                                <strong>Example Hypothesis:</strong> "When user clicks Login, system calls AuthController.authenticate()"
                            </div>
                        </div>

                        <div class="principle-card">
                            <h3>🌊 Follow the Data Flow</h3>
                            <p>Data is the red thread through any system. Track: Input → Transformation → Output</p>
                            <div class="questions">
                                <strong>Key Questions:</strong>
                                <ul>
                                    <li>Where does data come from?</li>
                                    <li>How is it transformed?</li>
                                    <li>Where does it end up?</li>
                                </ul>
                            </div>
                        </div>

                        <div class="principle-card">
                            <h3>🧅 Layer by Layer</h3>
                            <p>Systems are built in layers. Start from outside (UI/API) and work inward.</p>
                            <div class="layers">
                                <div class="layer">Presentation</div>
                                <div class="layer">Controllers</div>
                                <div class="layer">Services</div>
                                <div class="layer">Business Logic</div>
                                <div class="layer">Data Access</div>
                                <div class="layer">Database</div>
                            </div>
                        </div>

                        <div class="principle-card">
                            <h3>🎯 Pattern Recognition</h3>
                            <p>Look for design patterns and architectural patterns, not individual lines of code.</p>
                            <div class="patterns-list">
                                <span class="pattern-tag">MVC</span>
                                <span class="pattern-tag">Repository</span>
                                <span class="pattern-tag">Factory</span>
                                <span class="pattern-tag">Observer</span>
                                <span class="pattern-tag">Microservices</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Process Section -->
                <section id="process" class="content-section">
                    <div class="section-header">
                        <h2><span class="section-icon">🔄</span> Universal Process</h2>
                        <div class="section-actions">
                            <button class="bookmark-btn" data-section="process">🔖</button>
                            <button class="print-btn">🖨️</button>
                        </div>
                    </div>

                    <div class="process-intro">
                        <p>A systematic 5-step methodology that works with any codebase, language, or framework. Each step builds on the previous one.</p>
                    </div>

                    <div class="process-steps">
                        <div class="process-step">
                            <div class="step-header">
                                <div class="step-badge">1</div>
                                <h3>Orient</h3>
                                <span class="step-question">"Where am I?"</span>
                                <button class="expand-step">▼</button>
                            </div>
                            <div class="step-content">
                                <p><strong>Objective:</strong> Get 30,000-foot view of the project. <em>Absolutely no code reading at this step.</em></p>
                                <div class="actions-checklist">
                                    <h4>Actions:</h4>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Build and run the system
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Find entry points (main(), index.js, routes)
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Identify technology stack (package.json, pom.xml)
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Read documentation (README, docs/)
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Map directory structure
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="process-step">
                            <div class="step-header">
                                <div class="step-badge">2</div>
                                <h3>Hypothesize</h3>
                                <span class="step-question">"What do I want to know?"</span>
                                <button class="expand-step">▼</button>
                            </div>
                            <div class="step-content">
                                <p><strong>Objective:</strong> Choose specific business flow to investigate</p>
                                <div class="example-hypothesis">
                                    <h4>Example Hypothesis:</h4>
                                    <p>"When user registers, request goes to /register, handled by RegisterController, calls UserService, saves to users table"</p>
                                </div>
                                <div class="actions-checklist">
                                    <h4>Actions:</h4>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Pick one feature (e.g., 'User Registration')
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> State hypothesis about the flow
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Make predictions about components involved
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="process-step">
                            <div class="step-header">
                                <div class="step-badge">3</div>
                                <h3>Investigate</h3>
                                <span class="step-question">"What's the evidence?"</span>
                                <button class="expand-step">▼</button>
                            </div>
                            <div class="step-content">
                                <p><strong>Objective:</strong> Trace the hypothesis through the code</p>
                                <div class="techniques-grid">
                                    <div class="technique">
                                        <h4>🔽 Top-Down</h4>
                                        <p>Start from route, follow to controller, then services</p>
                                    </div>
                                    <div class="technique">
                                        <h4>🔼 Bottom-Up</h4>
                                        <p>Start from model, find all references</p>
                                    </div>
                                    <div class="technique">
                                        <h4>🔍 Global Search</h4>
                                        <p>Use grep/rg for keywords</p>
                                    </div>
                                    <div class="technique">
                                        <h4>🐛 Debug</h4>
                                        <p>Set breakpoints and step through</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="process-step">
                            <div class="step-header">
                                <div class="step-badge">4</div>
                                <h3>Synthesize</h3>
                                <span class="step-question">"What did I learn?"</span>
                                <button class="expand-step">▼</button>
                            </div>
                            <div class="step-content">
                                <p><strong>Objective:</strong> Build mental model of the flow</p>
                                <div class="actions-checklist">
                                    <h4>Actions:</h4>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Draw sequence diagram
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Write flow description in natural language
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Ask 'why' questions about design decisions
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Update your notes
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="process-step">
                            <div class="step-header">
                                <div class="step-badge">5</div>
                                <h3>Iterate</h3>
                                <span class="step-question">"What's next?"</span>
                                <button class="expand-step">▼</button>
                            </div>
                            <div class="step-content">
                                <p><strong>Objective:</strong> Expand knowledge to adjacent areas</p>
                                <div class="actions-checklist">
                                    <h4>Actions:</h4>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Form new hypothesis based on learnings
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Investigate related flows
                                    </label>
                                    <label class="checklist-item">
                                        <input type="checkbox"> Build bigger picture gradually
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Additional sections will be dynamically loaded -->
                <div id="dynamicContent"></div>
            </main>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="#quick_reference" class="footer-link">Quick Reference</a>
                    <a href="#checklists" class="footer-link">Checklists</a>
                    <a href="#resources" class="footer-link">Resources</a>
                </div>
                <div class="footer-info">
                    <p>Software Archaeology Handbook - Your Career-Long Reference</p>
                </div>
            </div>
        </footer>
    </div>

    <script src="app.js"></script>
</body>
</html>