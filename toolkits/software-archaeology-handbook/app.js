// Software Archaeology Handbook - Interactive Application (Fixed Version)
class SoftwareArchaeologyHandbook {
    constructor() {
        this.currentSection = 'getting_started';
        this.bookmarks = this.loadBookmarks();
        this.searchIndex = [];
        
        this.init();
        this.loadData();
    }

    init() {
        this.setupEventListeners();
        this.setupSearch();
        this.setupMobileNavigation();
        this.setupExpandableSections();
        this.setupProcessSteps();
        this.loadBookmarkStates();
        this.initTheme();
    }

    loadData() {
        // Complete handbook data
        this.handbookData = {
            tools: {
                title: "Công Cụ & Kỹ Thuật",
                icon: "🛠️",
                content: {
                    ide_tools: [
                        {
                            name: "Go to Definition (F12)",
                            description: "<PERSON><PERSON> năng điều hướng quan trọng nhất - nh<PERSON>y đến nơi function/class được định nghĩa"
                        },
                        {
                            name: "Find All References",
                            description: "Xem mọi nơi một function/variable được sử dụng"
                        },
                        {
                            name: "Call Hierarchy",
                            description: "Trực quan hóa cái gì gọi cái gì theo cả hai hướng"
                        },
                        {
                            name: "Integrated Debugger",
                            description: "Vũ khí tối thượng - bước qua thực thi code trực tiếp"
                        }
                    ],
                    command_line: [
                        {
                            name: "ripgrep (rg)",
                            command: "rg 'function_name' --type js",
                            description: "Nhanh hơn grep, tôn trọng .gitignore theo mặc định"
                        },
                        {
                            name: "git blame",
                            command: "git blame filename.js",
                            description: "Xem ai đã viết mỗi dòng và khi nào - tìm đúng người để hỏi"
                        },
                        {
                            name: "git log",
                            command: "git log -p filename.js",
                            description: "Xem lịch sử tiến hóa của file để hiểu các quyết định"
                        }
                    ],
                    advanced_techniques: [
                        {
                            name: "Characterization Tests",
                            description: "Viết test ghi lại hành vi hiện tại, không phải để test tính đúng đắn",
                            purpose: "Lưới an toàn trước khi thực hiện thay đổi"
                        },
                        {
                            name: "Strategic Logging",
                            description: "Thêm các câu lệnh print tạm thời để theo dõi luồng thực thi",
                            warning: "Xóa trước khi commit!"
                        },
                        {
                            name: "Static Analysis Tools",
                            description: "Phân tích code tự động cho dependencies, complexity và issues",
                            examples: ["SonarQube", "ESLint", "CodeQL", "Semgrep"]
                        }
                    ]
                }
            },
            patterns: {
                title: "Nhận Dạng Mẫu",
                icon: "🔍",
                content: {
                    architectural_patterns: [
                        {
                            name: "MVC (Model-View-Controller)",
                            structure: "Tách biệt data, presentation và control logic",
                            clues: ["controllers/", "models/", "views/", "templates/"]
                        },
                        {
                            name: "Microservices",
                            structure: "Nhiều service nhỏ giao tiếp qua mạng",
                            clues: ["Multiple repos", "API gateways", "Service discovery", "Docker"]
                        },
                        {
                            name: "Event-Driven Architecture",
                            structure: "Các component giao tiếp thông qua events/messages",
                            clues: ["Event handlers", "Message queues", "Publishers/Subscribers"]
                        },
                        {
                            name: "Layered Architecture",
                            structure: "Được tổ chức theo các layer ngang với dependencies chảy xuống dưới",
                            clues: ["presentation/", "business/", "data/", "infrastructure/"]
                        }
                    ],
                    design_patterns: [
                        {
                            name: "Repository Pattern",
                            purpose: "Trừu tượng hóa logic truy cập dữ liệu",
                            clues: ["Repository classes", "Interface definitions", "Data access abstraction"]
                        },
                        {
                            name: "Factory Pattern",
                            purpose: "Tạo objects mà không cần chỉ định class cụ thể",
                            clues: ["Factory classes", "Object creation methods", "Type-based instantiation"]
                        },
                        {
                            name: "Observer Pattern",
                            purpose: "Thông báo cho nhiều objects về thay đổi trạng thái",
                            clues: ["Event handlers", "Listeners", "Notification systems"]
                        },
                        {
                            name: "Singleton Pattern",
                            purpose: "Đảm bảo chỉ có một instance của class tồn tại",
                            clues: ["Private constructors", "Static getInstance methods", "Global state"]
                        }
                    ],
                    code_smells: [
                        {
                            name: "God Object",
                            description: "Class làm quá nhiều việc",
                            red_flags: ["Hàng nghìn dòng", "Nhiều trách nhiệm", "Khó test"]
                        },
                        {
                            name: "Spaghetti Code",
                            description: "Luồng điều khiển phức tạp, rối rắm",
                            red_flags: ["Nesting sâu", "Goto statements", "Không có cấu trúc rõ ràng"]
                        },
                        {
                            name: "Magic Numbers",
                            description: "Các giá trị số được hard-code mà không có giải thích",
                            red_flags: ["Constants không giải thích", "Không có tên symbolic", "Giá trị lặp lại"]
                        }
                    ]
                }
            },
            labs: {
                title: "Interactive Labs",
                icon: "🧪",
                content: {
                    exercises: [
                        {
                            name: "Flow Tracing Challenge",
                            description: "Given a user action, trace through the system to find all affected components",
                            difficulty: "Beginner",
                            time: "15 minutes",
                            objective: "Practice following data flow through a simulated system"
                        },
                        {
                            name: "Pattern Identification",
                            description: "Analyze code snippets and identify the design patterns used",
                            difficulty: "Intermediate",
                            time: "20 minutes",
                            objective: "Develop pattern recognition skills"
                        },
                        {
                            name: "Architecture Recovery",
                            description: "Build a high-level architecture diagram from a legacy codebase",
                            difficulty: "Advanced",
                            time: "45 minutes",
                            objective: "Practice systematic architecture analysis"
                        },
                        {
                            name: "Dependency Mapping",
                            description: "Create a dependency graph showing relationships between modules",
                            difficulty: "Intermediate",
                            time: "30 minutes",
                            objective: "Understand system interconnections"
                        }
                    ]
                }
            },
            checklists: {
                title: "Checklists & Templates",
                icon: "✅",
                content: {
                    pre_investigation: [
                        "Can I build the system?",
                        "Can I run the system?",
                        "Can I run the tests?",
                        "Do I have access to documentation?",
                        "Do I have access to version control history?",
                        "Have I identified the main entry points?",
                        "Do I understand the technology stack?",
                        "Have I mapped the directory structure?"
                    ],
                    investigation_tracking: [
                        "What is my current hypothesis?",
                        "What evidence supports/contradicts it?",
                        "What tools am I using?",
                        "What have I learned so far?",
                        "What questions do I still have?",
                        "Who can I ask for help?",
                        "What should I investigate next?"
                    ],
                    architecture_documentation: [
                        "High-level system overview",
                        "Key components and their responsibilities",
                        "Data flow diagrams",
                        "Integration points",
                        "Technology stack details",
                        "Deployment architecture",
                        "Known limitations or technical debt"
                    ]
                }
            },
            case_studies: {
                title: "Case Studies",
                icon: "📚",
                content: {
                    examples: [
                        {
                            title: "Legacy E-commerce System",
                            scenario: "15-year-old PHP application with no documentation",
                            challenge: "Add new payment method integration",
                            approach: "Follow the data flow of existing payment processing",
                            outcome: "Successfully identified payment abstraction layer",
                            lessons: ["Always start with working code", "Payment systems have clear patterns", "Legacy doesn't mean bad design"]
                        },
                        {
                            title: "Microservices Architecture",
                            scenario: "50+ services with complex inter-dependencies",
                            challenge: "Understand user authentication flow",
                            approach: "Trace JWT token through service calls using logs",
                            outcome: "Mapped complete authentication chain",
                            lessons: ["Distributed tracing is essential", "JWT tokens tell a story", "Service boundaries matter"]
                        },
                        {
                            title: "Legacy Java Enterprise App",
                            scenario: "Monolithic Spring application from 2010",
                            challenge: "Extract user management into separate service",
                            approach: "Use dependency analysis to identify boundaries",
                            outcome: "Clean extraction with minimal business logic changes",
                            lessons: ["Static analysis reveals hidden dependencies", "Domain boundaries exist in legacy code", "Gradual extraction is safer"]
                        }
                    ]
                }
            },
            quick_reference: {
                title: "Quick Reference",
                icon: "⚡",
                content: {
                    essential_commands: {
                        "Search": ["grep -r 'pattern' .", "rg 'pattern'", "find . -name '*.js' | xargs grep 'pattern'"],
                        "Git": ["git blame file.js", "git log --oneline", "git log -p file.js"],
                        "IDE": ["Ctrl/Cmd+Click (Go to Definition)", "F12 (Go to Definition)", "Shift+F12 (Find References)"]
                    },
                    pattern_quick_ref: {
                        "MVC": "controllers/, models/, views/",
                        "Repository": "*Repository.java, data access abstraction",
                        "Factory": "create(), getInstance(), builder pattern",
                        "Observer": "addEventListener, observers list, notify()"
                    },
                    emergency_checklist: [
                        "Can you build and run the system?",
                        "What's the main entry point?",
                        "What framework/language is this?",
                        "Where are the tests?",
                        "Who was the last person to work on this?",
                        "What does this feature actually do?",
                        "Where is the data stored?",
                        "How do I deploy this?"
                    ]
                }
            },
            resources: {
                title: "Resources & Links",
                icon: "🔗",
                content: {
                    books: [
                        {
                            title: "Working Effectively with Legacy Code",
                            author: "Michael Feathers",
                            description: "The definitive guide to understanding and modifying legacy systems"
                        },
                        {
                            title: "Clean Code",
                            author: "Robert Martin",
                            description: "Principles for writing maintainable code"
                        },
                        {
                            title: "Refactoring",
                            author: "Martin Fowler",
                            description: "Systematic approach to improving code structure"
                        },
                        {
                            title: "Software Architecture in Practice",
                            author: "Bass, Clements, Kazman",
                            description: "Comprehensive guide to software architecture"
                        }
                    ],
                    tools: [
                        {
                            name: "Visual Studio Code",
                            type: "IDE",
                            description: "Free, powerful IDE with excellent extension ecosystem"
                        },
                        {
                            name: "IntelliJ IDEA",
                            type: "IDE",
                            description: "Professional IDE with advanced code analysis"
                        },
                        {
                            name: "ripgrep",
                            type: "Search",
                            description: "Fast text search tool that respects .gitignore"
                        },
                        {
                            name: "SonarQube",
                            type: "Static Analysis",
                            description: "Comprehensive code quality and security analysis"
                        },
                        {
                            name: "Lattix",
                            type: "Architecture Analysis",
                            description: "Advanced dependency structure analysis"
                        }
                    ],
                    online_resources: [
                        {
                            title: "Software Archaeology - Wikipedia",
                            url: "https://en.wikipedia.org/wiki/Software_archaeology",
                            description: "Comprehensive overview of the discipline"
                        },
                        {
                            title: "Feathers' Legacy Code Techniques",
                            url: "#",
                            description: "Practical techniques for working with legacy code"
                        },
                        {
                            title: "Architecture Recovery Research Papers",
                            url: "#",
                            description: "Academic research on reverse engineering"
                        }
                    ]
                }
            }
        };

        this.buildSearchIndex();
        this.loadDynamicSections();
        
        // Re-setup event listeners after content is loaded
        setTimeout(() => {
            this.setupDynamicEventListeners();
        }, 100);
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.navigateToSection(section);
            });
        });

        // Navigation buttons
        document.querySelectorAll('[data-navigate]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.navigate;
                this.navigateToSection(section);
            });
        });

        // Footer navigation
        document.querySelectorAll('.footer-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.getAttribute('href').substring(1);
                this.navigateToSection(section);
            });
        });
    }

    setupDynamicEventListeners() {
        // Bookmarks for both static and dynamic content
        document.querySelectorAll('.bookmark-btn').forEach(btn => {
            btn.replaceWith(btn.cloneNode(true)); // Remove existing listeners
        });
        
        document.querySelectorAll('.bookmark-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const section = e.currentTarget.dataset.section;
                this.toggleBookmark(section);
            });
        });

        // Print buttons
        document.querySelectorAll('.print-btn').forEach(btn => {
            btn.replaceWith(btn.cloneNode(true)); // Remove existing listeners
        });
        
        document.querySelectorAll('.print-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                window.print();
            });
        });

        this.loadBookmarkStates(); // Refresh bookmark states
    }

    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.querySelector('.search-btn');

        let searchTimeout;
        
        const performSearchHandler = (query) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        };

        searchInput.addEventListener('input', (e) => {
            performSearchHandler(e.target.value);
        });

        searchBtn.addEventListener('click', () => {
            this.performSearch(searchInput.value);
        });

        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch(searchInput.value);
            }
        });
    }

    setupMobileNavigation() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-open');
            });
        }
    }

    setupExpandableSections() {
        // Use event delegation for both existing and future expandable sections
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('expandable-header') || 
                e.target.closest('.expandable-header')) {
                
                const header = e.target.classList.contains('expandable-header') 
                    ? e.target 
                    : e.target.closest('.expandable-header');
                
                const section = header.closest('.expandable-section');
                if (section) {
                    section.classList.toggle('expanded');
                    e.preventDefault();
                    e.stopPropagation();
                }
            }
        });
    }

    setupProcessSteps() {
        // Use event delegation for process steps
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('expand-step') || 
                e.target.closest('.step-header')) {
                
                const step = e.target.closest('.process-step');
                if (step) {
                    step.classList.toggle('expanded');
                    e.preventDefault();
                    e.stopPropagation();
                }
            }
        });
    }

    navigateToSection(sectionId) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.section === sectionId) {
                item.classList.add('active');
            }
        });

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionId;
            
            // Close mobile sidebar
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.remove('mobile-open');
            }
            
            // Scroll to top
            const content = document.getElementById('content');
            if (content) {
                content.scrollTop = 0;
            }
        }
    }

    toggleBookmark(section) {
        const btn = document.querySelector(`[data-section="${section}"]`);
        
        if (this.bookmarks.includes(section)) {
            this.bookmarks = this.bookmarks.filter(b => b !== section);
            if (btn) {
                btn.textContent = '🔖';
                btn.title = 'Add bookmark';
            }
        } else {
            this.bookmarks.push(section);
            if (btn) {
                btn.textContent = '📌';
                btn.title = 'Remove bookmark';
            }
        }
        
        this.saveBookmarks();
    }

    loadBookmarks() {
        try {
            return JSON.parse(localStorage.getItem('sa-bookmarks') || '[]');
        } catch (e) {
            return [];
        }
    }

    saveBookmarks() {
        try {
            localStorage.setItem('sa-bookmarks', JSON.stringify(this.bookmarks));
        } catch (e) {
            // LocalStorage not available, continue without saving
        }
    }

    loadBookmarkStates() {
        this.bookmarks.forEach(section => {
            const btn = document.querySelector(`[data-section="${section}"] .bookmark-btn`);
            if (btn) {
                btn.textContent = '📌';
                btn.title = 'Remove bookmark';
            }
        });
    }

    initTheme() {
        // Set light theme as default per project specification
        this.setTheme('light');
        
        // Listen for theme changes from parent (unified toolkit)
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'THEME_CHANGE') {
                this.setTheme('light'); // Always use light theme
            }
        });
    }

    setTheme(theme) {
        // Force light theme per project specification
        document.documentElement.setAttribute('data-color-scheme', 'light');
        localStorage.setItem('unifiedToolkit.theme', 'light');
    }

    // Theme toggle functionality removed - using light theme only per project specification

    buildSearchIndex() {
        this.searchIndex = [];
        
        // Index static content
        document.querySelectorAll('.content-section').forEach(section => {
            const sectionId = section.id;
            const text = section.textContent.toLowerCase();
            const title = section.querySelector('h2')?.textContent || '';
            
            this.searchIndex.push({
                section: sectionId,
                title,
                content: text,
                element: section
            });
        });

        // Index dynamic content
        Object.entries(this.handbookData).forEach(([key, data]) => {
            const title = data.title;
            const content = JSON.stringify(data.content).toLowerCase();
            
            this.searchIndex.push({
                section: key,
                title,
                content,
                data: data.content
            });
        });
    }

    performSearch(query) {
        if (!query || query.length < 2) {
            this.clearSearchHighlights();
            return;
        }

        const results = this.searchIndex.filter(item => 
            item.content.includes(query.toLowerCase()) ||
            item.title.toLowerCase().includes(query.toLowerCase())
        );

        this.displaySearchResults(results, query);
    }

    displaySearchResults(results, query) {
        this.clearSearchHighlights();
        
        if (results.length === 0) {
            this.showSearchMessage('No results found');
            return;
        }

        // Navigate to first result
        if (results.length > 0) {
            this.navigateToSection(results[0].section);
            
            // Highlight search terms after a delay to ensure content is loaded
            setTimeout(() => {
                this.highlightSearchTerms(query);
            }, 300);
        }

        this.showSearchMessage(`Found ${results.length} result(s) for "${query}"`);
    }

    highlightSearchTerms(query) {
        const activeSection = document.querySelector('.content-section.active');
        if (!activeSection) return;

        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        
        const walker = document.createTreeWalker(
            activeSection,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    // Skip if parent is a script, style, or already highlighted
                    if (node.parentNode.tagName === 'SCRIPT' || 
                        node.parentNode.tagName === 'STYLE' ||
                        node.parentNode.classList?.contains('search-highlight')) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    return NodeFilter.FILTER_ACCEPT;
                }
            },
            false
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            if (regex.test(node.textContent)) {
                textNodes.push(node);
            }
        }

        textNodes.forEach(textNode => {
            const text = textNode.textContent;
            if (regex.test(text)) {
                const highlightedText = text.replace(regex, `<span class="search-highlight">$1</span>`);
                const span = document.createElement('span');
                span.innerHTML = highlightedText;
                
                // Replace the text node with the highlighted version
                const parent = textNode.parentNode;
                while (span.firstChild) {
                    parent.insertBefore(span.firstChild, textNode);
                }
                parent.removeChild(textNode);
            }
        });
    }

    clearSearchHighlights() {
        document.querySelectorAll('.search-highlight').forEach(highlight => {
            const parent = highlight.parentNode;
            const text = document.createTextNode(highlight.textContent);
            parent.replaceChild(text, highlight);
        });
    }

    showSearchMessage(message) {
        // Remove existing message
        const existingMessage = document.querySelector('.search-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Show new message
        const messageEl = document.createElement('div');
        messageEl.className = 'search-message';
        messageEl.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 1000;
            box-shadow: var(--shadow-md);
            color: var(--color-text);
        `;
        messageEl.textContent = message;
        document.body.appendChild(messageEl);

        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 3000);
    }

    loadDynamicSections() {
        const dynamicContent = document.getElementById('dynamicContent');
        if (!dynamicContent) return;
        
        Object.entries(this.handbookData).forEach(([sectionId, sectionData]) => {
            const section = this.createSectionElement(sectionId, sectionData);
            dynamicContent.appendChild(section);
        });
    }

    createSectionElement(sectionId, sectionData) {
        const section = document.createElement('section');
        section.id = sectionId;
        section.className = 'content-section';
        
        let content = `
            <div class="section-header">
                <h2><span class="section-icon">${sectionData.icon}</span> ${sectionData.title}</h2>
                <div class="section-actions">
                    <button class="bookmark-btn" data-section="${sectionId}">🔖</button>
                    <button class="print-btn">🖨️</button>
                </div>
            </div>
        `;

        // Generate content based on section type
        content += this.generateSectionContent(sectionId, sectionData.content);
        
        section.innerHTML = content;
        return section;
    }

    generateSectionContent(sectionId, content) {
        switch (sectionId) {
            case 'tools':
                return this.generateToolsContent(content);
            case 'patterns':
                return this.generatePatternsContent(content);
            case 'labs':
                return this.generateLabsContent(content);
            case 'checklists':
                return this.generateChecklistsContent(content);
            case 'case_studies':
                return this.generateCaseStudiesContent(content);
            case 'quick_reference':
                return this.generateQuickReferenceContent(content);
            case 'resources':
                return this.generateResourcesContent(content);
            default:
                return '<p>Content loading...</p>';
        }
    }

    generateToolsContent(content) {
        return `
            <div class="tools-intro">
                <p>Master these tools to become an effective software archaeologist. Each tool serves a specific purpose in your investigation.</p>
            </div>

            <div class="expandable-section expanded">
                <h3 class="expandable-header">IDE Navigation Tools <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="tools-grid">
                        ${content.ide_tools.map(tool => `
                            <div class="tool-card">
                                <h4>${tool.name}</h4>
                                <p>${tool.description}</p>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <div class="expandable-section">
                <h3 class="expandable-header">Command Line Tools <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="command-tools">
                        ${content.command_line.map(tool => `
                            <div class="command-card">
                                <h4>${tool.name}</h4>
                                <code class="command">${tool.command}</code>
                                <p>${tool.description}</p>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <div class="expandable-section">
                <h3 class="expandable-header">Advanced Techniques <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="techniques-list">
                        ${content.advanced_techniques.map(technique => `
                            <div class="technique-card">
                                <h4>${technique.name}</h4>
                                <p>${technique.description}</p>
                                ${technique.purpose ? `<div class="purpose"><strong>Purpose:</strong> ${technique.purpose}</div>` : ''}
                                ${technique.warning ? `<div class="warning"><strong>⚠️ Warning:</strong> ${technique.warning}</div>` : ''}
                                ${technique.examples ? `<div class="examples"><strong>Examples:</strong> ${technique.examples.join(', ')}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <style>
                .tools-grid, .command-tools, .techniques-list {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: var(--space-16);
                    margin-top: var(--space-16);
                }
                
                .tool-card, .command-card, .technique-card {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-16);
                }
                
                .command {
                    display: block;
                    background: var(--color-bg-8);
                    padding: var(--space-8);
                    border-radius: var(--radius-sm);
                    margin: var(--space-8) 0;
                    font-family: var(--font-family-mono);
                }
                
                .warning {
                    color: var(--color-warning);
                    margin-top: var(--space-8);
                    font-size: var(--font-size-sm);
                }
                
                .purpose, .examples {
                    margin-top: var(--space-8);
                    font-size: var(--font-size-sm);
                }
            </style>
        `;
    }

    generatePatternsContent(content) {
        return `
            <div class="patterns-intro">
                <p>Recognizing patterns is key to understanding any codebase quickly. These patterns are universal across languages and frameworks.</p>
            </div>

            <div class="expandable-section expanded">
                <h3 class="expandable-header">Architectural Patterns <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="patterns-grid">
                        ${content.architectural_patterns.map(pattern => `
                            <div class="pattern-card">
                                <h4>${pattern.name}</h4>
                                <p>${pattern.structure}</p>
                                <div class="clues">
                                    <strong>Look for:</strong>
                                    <ul>
                                        ${pattern.clues.map(clue => `<li>${clue}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <div class="expandable-section">
                <h3 class="expandable-header">Design Patterns <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="patterns-grid">
                        ${content.design_patterns.map(pattern => `
                            <div class="pattern-card">
                                <h4>${pattern.name}</h4>
                                <p><strong>Purpose:</strong> ${pattern.purpose}</p>
                                <div class="clues">
                                    <strong>Look for:</strong>
                                    <ul>
                                        ${pattern.clues.map(clue => `<li>${clue}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <div class="expandable-section">
                <h3 class="expandable-header">Code Smells & Anti-patterns <span class="expand-icon">▼</span></h3>
                <div class="expandable-content">
                    <div class="patterns-grid">
                        ${content.code_smells.map(smell => `
                            <div class="smell-card">
                                <h4>🚨 ${smell.name}</h4>
                                <p>${smell.description}</p>
                                <div class="red-flags">
                                    <strong>Red flags:</strong>
                                    <ul>
                                        ${smell.red_flags.map(flag => `<li>${flag}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <style>
                .patterns-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: var(--space-16);
                    margin-top: var(--space-16);
                }
                
                .pattern-card, .smell-card {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-16);
                }
                
                .smell-card {
                    border-color: var(--color-warning);
                    background: rgba(var(--color-warning-rgb), 0.05);
                }
                
                .clues, .red-flags {
                    margin-top: var(--space-12);
                }
                
                .clues ul, .red-flags ul {
                    margin-top: var(--space-8);
                    padding-left: var(--space-16);
                }
            </style>
        `;
    }

    generateLabsContent(content) {
        return `
            <div class="labs-intro">
                <p>Practice your software archaeology skills with these interactive exercises. Each lab focuses on a specific aspect of code investigation.</p>
            </div>

            <div class="exercises-grid">
                ${content.exercises.map((exercise, index) => `
                    <div class="exercise-card">
                        <div class="exercise-header">
                            <h3>${exercise.name}</h3>
                            <div class="exercise-meta">
                                <span class="difficulty difficulty-${exercise.difficulty.toLowerCase()}">${exercise.difficulty}</span>
                                <span class="time">⏱️ ${exercise.time}</span>
                            </div>
                        </div>
                        <p class="description">${exercise.description}</p>
                        ${exercise.objective ? `<div class="objective"><strong>Objective:</strong> ${exercise.objective}</div>` : ''}
                        <div class="exercise-actions">
                            <button class="btn btn--primary start-exercise" data-exercise="${index}">Start Exercise</button>
                            <button class="btn btn--outline view-solution" data-exercise="${index}">View Solution</button>
                        </div>
                        <div class="exercise-content" style="display: none;">
                            <div class="exercise-workspace">
                                <p><strong>Exercise Instructions:</strong></p>
                                <p>This would be an interactive workspace for the exercise.</p>
                                <p>In a full implementation, this would include:</p>
                                <ul>
                                    <li>Sample code snippets to analyze</li>
                                    <li>Interactive elements for drawing diagrams</li>
                                    <li>Progress tracking</li>
                                    <li>Hints and guidance</li>
                                </ul>
                                <div class="exercise-controls">
                                    <button class="btn btn--secondary">Get Hint</button>
                                    <button class="btn btn--primary">Check Answer</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>

            <style>
                .exercises-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                    gap: var(--space-20);
                    margin-top: var(--space-24);
                }
                
                .exercise-card {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-20);
                    transition: all var(--duration-fast) var(--ease-standard);
                }
                
                .exercise-card:hover {
                    border-color: var(--color-primary);
                    box-shadow: var(--shadow-md);
                }
                
                .exercise-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: var(--space-16);
                }
                
                .exercise-meta {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-4);
                    align-items: flex-end;
                }
                
                .difficulty {
                    padding: var(--space-2) var(--space-8);
                    border-radius: var(--radius-full);
                    font-size: var(--font-size-xs);
                    font-weight: var(--font-weight-medium);
                }
                
                .difficulty-beginner {
                    background: rgba(var(--color-success-rgb), 0.15);
                    color: var(--color-success);
                }
                
                .difficulty-intermediate {
                    background: rgba(var(--color-warning-rgb), 0.15);
                    color: var(--color-warning);
                }
                
                .difficulty-advanced {
                    background: rgba(var(--color-error-rgb), 0.15);
                    color: var(--color-error);
                }
                
                .time {
                    font-size: var(--font-size-sm);
                    color: var(--color-text-secondary);
                }
                
                .objective {
                    background: var(--color-bg-3);
                    padding: var(--space-12);
                    border-radius: var(--radius-base);
                    margin: var(--space-12) 0;
                    font-size: var(--font-size-sm);
                }
                
                .exercise-actions {
                    display: flex;
                    gap: var(--space-8);
                    margin-top: var(--space-16);
                }
                
                .exercise-workspace {
                    margin-top: var(--space-16);
                    padding: var(--space-16);
                    background: var(--color-bg-1);
                    border-radius: var(--radius-base);
                }
                
                .exercise-controls {
                    margin-top: var(--space-16);
                    display: flex;
                    gap: var(--space-8);
                }
            </style>
        `;
    }

    generateChecklistsContent(content) {
        return `
            <div class="checklists-intro">
                <p>Use these checklists to ensure systematic and thorough investigation. Check off items as you complete them.</p>
            </div>

            <div class="checklist-sections">
                <div class="expandable-section expanded">
                    <h3 class="expandable-header">Pre-Investigation Checklist <span class="expand-icon">▼</span></h3>
                    <div class="expandable-content">
                        <div class="checklist">
                            ${content.pre_investigation.map((item, index) => `
                                <label class="checklist-item">
                                    <input type="checkbox" id="pre_${index}">
                                    ${item}
                                </label>
                            `).join('')}
                        </div>
                        <div class="checklist-actions">
                            <button class="btn btn--secondary" onclick="app.clearChecklist('pre')">Clear All</button>
                            <button class="btn btn--primary" onclick="app.exportChecklist('pre')">Export Progress</button>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <h3 class="expandable-header">Investigation Tracking <span class="expand-icon">▼</span></h3>
                    <div class="expandable-content">
                        <div class="checklist">
                            ${content.investigation_tracking.map((item, index) => `
                                <label class="checklist-item">
                                    <input type="checkbox" id="tracking_${index}">
                                    ${item}
                                </label>
                            `).join('')}
                        </div>
                        <div class="checklist-actions">
                            <button class="btn btn--secondary" onclick="app.clearChecklist('tracking')">Clear All</button>
                            <button class="btn btn--primary" onclick="app.exportChecklist('tracking')">Export Progress</button>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <h3 class="expandable-header">Architecture Documentation Template <span class="expand-icon">▼</span></h3>
                    <div class="expandable-content">
                        <div class="checklist">
                            ${content.architecture_documentation.map((item, index) => `
                                <label class="checklist-item">
                                    <input type="checkbox" id="arch_${index}">
                                    ${item}
                                </label>
                            `).join('')}
                        </div>
                        <div class="checklist-actions">
                            <button class="btn btn--secondary" onclick="app.clearChecklist('arch')">Clear All</button>
                            <button class="btn btn--primary" onclick="app.exportChecklist('arch')">Export Progress</button>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .checklist {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-8);
                    margin: var(--space-16) 0;
                }
                
                .checklist-item {
                    display: flex;
                    align-items: center;
                    gap: var(--space-12);
                    padding: var(--space-8) var(--space-12);
                    border-radius: var(--radius-base);
                    cursor: pointer;
                    transition: background-color var(--duration-fast) var(--ease-standard);
                    position: relative;
                }
                
                .checklist-item:hover {
                    background: var(--color-secondary);
                }
                
                .checklist-item input[type="checkbox"] {
                    margin: 0;
                    transform: scale(1.2);
                }
                
                .checklist-item input[type="checkbox"]:checked + span {
                    text-decoration: line-through;
                    opacity: 0.6;
                }
                
                .checklist-actions {
                    display: flex;
                    gap: var(--space-8);
                    margin-top: var(--space-16);
                    padding-top: var(--space-16);
                    border-top: 1px solid var(--color-border);
                }
            </style>
        `;
    }

    generateCaseStudiesContent(content) {
        return `
            <div class="case-studies-intro">
                <p>Learn from real-world examples of software archaeology in action. Each case study shows the systematic approach in practice.</p>
            </div>

            <div class="case-studies-list">
                ${content.examples.map((study, index) => `
                    <div class="case-study-card">
                        <div class="study-header">
                            <h3>${study.title}</h3>
                            <span class="study-number">#${index + 1}</span>
                        </div>
                        
                        <div class="study-content">
                            <div class="study-section">
                                <h4>🎯 Scenario</h4>
                                <p>${study.scenario}</p>
                            </div>
                            
                            <div class="study-section">
                                <h4>⚡ Challenge</h4>
                                <p>${study.challenge}</p>
                            </div>
                            
                            <div class="study-section">
                                <h4>🔧 Approach</h4>
                                <p>${study.approach}</p>
                            </div>
                            
                            <div class="study-section">
                                <h4>✅ Outcome</h4>
                                <p>${study.outcome}</p>
                            </div>
                            
                            ${study.lessons ? `
                                <div class="study-section lessons">
                                    <h4>💡 Key Lessons</h4>
                                    <ul>
                                        ${study.lessons.map(lesson => `<li>${lesson}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>

            <style>
                .case-studies-list {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-24);
                    margin-top: var(--space-24);
                }
                
                .case-study-card {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-24);
                    transition: all var(--duration-fast) var(--ease-standard);
                }
                
                .case-study-card:hover {
                    border-color: var(--color-primary);
                    box-shadow: var(--shadow-md);
                }
                
                .study-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: var(--space-20);
                    padding-bottom: var(--space-12);
                    border-bottom: 2px solid var(--color-border);
                }
                
                .study-number {
                    background: var(--color-primary);
                    color: var(--color-btn-primary-text);
                    padding: var(--space-4) var(--space-12);
                    border-radius: var(--radius-full);
                    font-weight: var(--font-weight-bold);
                    font-size: var(--font-size-sm);
                }
                
                .study-content {
                    display: grid;
                    grid-template-columns: 1fr;
                    gap: var(--space-16);
                }
                
                .study-section {
                    background: var(--color-bg-1);
                    padding: var(--space-16);
                    border-radius: var(--radius-base);
                }
                
                .study-section h4 {
                    margin-bottom: var(--space-8);
                    color: var(--color-primary);
                    font-size: var(--font-size-base);
                }
                
                .lessons {
                    background: var(--color-bg-3);
                }
                
                .lessons ul {
                    margin-top: var(--space-8);
                    padding-left: var(--space-16);
                }
                
                @media (min-width: 768px) {
                    .study-content {
                        grid-template-columns: 1fr 1fr;
                    }
                    
                    .lessons {
                        grid-column: 1 / -1;
                    }
                }
            </style>
        `;
    }

    generateQuickReferenceContent(content) {
        return `
            <div class="quick-ref-intro">
                <p>Quick access to essential commands, patterns, and emergency procedures. Bookmark this section for rapid reference during investigations.</p>
            </div>

            <div class="quick-ref-sections">
                <div class="ref-section">
                    <h3>🔍 Essential Commands</h3>
                    <div class="commands-grid">
                        ${Object.entries(content.essential_commands).map(([category, commands]) => `
                            <div class="command-category">
                                <h4>${category}</h4>
                                <div class="command-list">
                                    ${commands.map(cmd => `<code class="command-item">${cmd}</code>`).join('')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="ref-section">
                    <h3>🎯 Pattern Quick Reference</h3>
                    <div class="patterns-ref">
                        ${Object.entries(content.pattern_quick_ref).map(([pattern, description]) => `
                            <div class="pattern-ref-item">
                                <strong>${pattern}:</strong> <span>${description}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="ref-section emergency">
                    <h3>🚨 Emergency Checklist</h3>
                    <p class="emergency-intro">When you're dropped into an unknown codebase with urgent issues:</p>
                    <div class="emergency-checklist">
                        ${content.emergency_checklist.map((item, index) => `
                            <label class="emergency-item">
                                <input type="checkbox" id="emergency_${index}">
                                <span class="item-number">${index + 1}</span>
                                ${item}
                            </label>
                        `).join('')}
                    </div>
                </div>
            </div>

            <style>
                .quick-ref-sections {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-32);
                    margin-top: var(--space-24);
                }
                
                .ref-section {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-24);
                }
                
                .ref-section.emergency {
                    border-color: var(--color-error);
                    background: rgba(var(--color-error-rgb), 0.05);
                }
                
                .commands-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: var(--space-16);
                    margin-top: var(--space-16);
                }
                
                .command-category h4 {
                    color: var(--color-primary);
                    margin-bottom: var(--space-8);
                }
                
                .command-list {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-4);
                }
                
                .command-item {
                    display: block;
                    background: var(--color-bg-8);
                    padding: var(--space-6) var(--space-8);
                    border-radius: var(--radius-sm);
                    font-family: var(--font-family-mono);
                    font-size: var(--font-size-sm);
                }
                
                .patterns-ref {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-8);
                    margin-top: var(--space-16);
                }
                
                .pattern-ref-item {
                    padding: var(--space-8) var(--space-12);
                    background: var(--color-bg-2);
                    border-radius: var(--radius-base);
                    font-size: var(--font-size-sm);
                }
                
                .emergency-intro {
                    color: var(--color-error);
                    font-weight: var(--font-weight-medium);
                    margin-bottom: var(--space-16);
                }
                
                .emergency-checklist {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-8);
                }
                
                .emergency-item {
                    display: flex;
                    align-items: center;
                    gap: var(--space-12);
                    padding: var(--space-8) var(--space-12);
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-base);
                    cursor: pointer;
                    transition: all var(--duration-fast) var(--ease-standard);
                }
                
                .emergency-item:hover {
                    border-color: var(--color-error);
                }
                
                .item-number {
                    background: var(--color-error);
                    color: white;
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: var(--font-size-sm);
                    font-weight: var(--font-weight-bold);
                    flex-shrink: 0;
                }
                
                .emergency-item input[type="checkbox"]:checked ~ span {
                    opacity: 0.6;
                    text-decoration: line-through;
                }
            </style>
        `;
    }

    generateResourcesContent(content) {
        return `
            <div class="resources-intro">
                <p>Expand your software archaeology knowledge with these carefully curated resources. Each resource has been selected for its practical value.</p>
            </div>

            <div class="resources-sections">
                <div class="expandable-section expanded">
                    <h3 class="expandable-header">📚 Essential Books <span class="expand-icon">▼</span></h3>
                    <div class="expandable-content">
                        <div class="books-list">
                            ${content.books.map(book => `
                                <div class="resource-card book-card">
                                    <div class="resource-header">
                                        <h4>${book.title}</h4>
                                        <span class="author">by ${book.author}</span>
                                    </div>
                                    <p class="description">${book.description}</p>
                                    <div class="resource-meta">
                                        <span class="resource-type">📖 Book</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <h3 class="expandable-header">🛠️ Recommended Tools <span class="expand-icon">▼</span></h3>
                    <div class="expandable-content">
                        <div class="tools-list">
                            ${content.tools.map(tool => `
                                <div class="resource-card tool-card">
                                    <div class="resource-header">
                                        <h4>${tool.name}</h4>
                                        <span class="tool-type">${tool.type}</span>
                                    </div>
                                    <p class="description">${tool.description}</p>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <h3 class="expandable-header">🌐 Online Resources <span class="expand-icon">▼</span></h3>
                    <div class="expandable-content">
                        <div class="online-resources">
                            ${content.online_resources.map(resource => `
                                <div class="resource-card online-card">
                                    <div class="resource-header">
                                        <h4>${resource.title}</h4>
                                        ${resource.url !== '#' ? `<a href="${resource.url}" target="_blank" class="external-link">🔗</a>` : ''}
                                    </div>
                                    <p class="description">${resource.description}</p>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>

            <div class="contribution-section">
                <h3>🤝 Contribute to This Handbook</h3>
                <p>Help make this handbook better by contributing your own experiences, tools, and techniques. Software archaeology is a community effort!</p>
                <div class="contribution-ideas">
                    <div class="idea-item">
                        <strong>Share Case Studies:</strong> Document your successful archaeological expeditions
                    </div>
                    <div class="idea-item">
                        <strong>Add Tools:</strong> Recommend tools that have helped you understand complex codebases
                    </div>
                    <div class="idea-item">
                        <strong>Improve Checklists:</strong> Suggest additional items based on your experience
                    </div>
                </div>
            </div>

            <style>
                .resources-sections {
                    margin-top: var(--space-24);
                }
                
                .books-list, .tools-list, .online-resources {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: var(--space-16);
                    margin-top: var(--space-16);
                }
                
                .resource-card {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: var(--radius-lg);
                    padding: var(--space-16);
                    transition: all var(--duration-fast) var(--ease-standard);
                }
                
                .resource-card:hover {
                    border-color: var(--color-primary);
                    box-shadow: var(--shadow-sm);
                }
                
                .resource-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: var(--space-12);
                }
                
                .resource-header h4 {
                    margin: 0;
                    color: var(--color-primary);
                    flex: 1;
                }
                
                .author, .tool-type {
                    font-size: var(--font-size-sm);
                    color: var(--color-text-secondary);
                    font-style: italic;
                }
                
                .external-link {
                    text-decoration: none;
                    font-size: var(--font-size-lg);
                    padding: var(--space-4);
                    transition: transform var(--duration-fast) var(--ease-standard);
                }
                
                .external-link:hover {
                    transform: scale(1.1);
                }
                
                .description {
                    margin: 0;
                    color: var(--color-text-secondary);
                    font-size: var(--font-size-sm);
                }
                
                .resource-meta {
                    margin-top: var(--space-12);
                    padding-top: var(--space-8);
                    border-top: 1px solid var(--color-border);
                }
                
                .resource-type {
                    font-size: var(--font-size-xs);
                    background: var(--color-bg-5);
                    padding: var(--space-2) var(--space-6);
                    border-radius: var(--radius-full);
                }
                
                .contribution-section {
                    margin-top: var(--space-32);
                    background: var(--color-bg-6);
                    padding: var(--space-24);
                    border-radius: var(--radius-lg);
                    border: 1px solid var(--color-border);
                }
                
                .contribution-section h3 {
                    color: var(--color-primary);
                    margin-bottom: var(--space-16);
                }
                
                .contribution-ideas {
                    display: flex;
                    flex-direction: column;
                    gap: var(--space-8);
                    margin-top: var(--space-16);
                }
                
                .idea-item {
                    background: var(--color-surface);
                    padding: var(--space-12);
                    border-radius: var(--radius-base);
                    border-left: 3px solid var(--color-primary);
                }
            </style>
        `;
    }

    // Utility methods for checklists
    clearChecklist(type) {
        document.querySelectorAll(`input[id^="${type}_"]`).forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    exportChecklist(type) {
        const items = document.querySelectorAll(`input[id^="${type}_"]`);
        const checkedItems = Array.from(items).map((checkbox, index) => ({
            index,
            text: checkbox.parentNode.textContent.trim(),
            checked: checkbox.checked
        }));

        const blob = new Blob([JSON.stringify(checkedItems, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `archaeology-checklist-${type}-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SoftwareArchaeologyHandbook();
});

// Setup event listeners for dynamically created elements
document.addEventListener('click', (e) => {
    // Handle exercise interactions
    if (e.target.classList.contains('start-exercise')) {
        const exerciseIndex = e.target.dataset.exercise;
        const exerciseCard = e.target.closest('.exercise-card');
        const content = exerciseCard.querySelector('.exercise-content');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            e.target.textContent = 'Hide Exercise';
        } else {
            content.style.display = 'none';
            e.target.textContent = 'Start Exercise';
        }
    }

    // Handle solution viewing
    if (e.target.classList.contains('view-solution')) {
        alert('In a full implementation, this would show detailed solutions and explanations for the exercise.');
    }
});

// Add keyboard navigation support
document.addEventListener('keydown', (e) => {
    // Alt + number keys for quick section navigation
    if (e.altKey && e.key >= '1' && e.key <= '9') {
        const sections = ['getting_started', 'mindset', 'process', 'tools', 'patterns', 'labs', 'checklists', 'case_studies', 'quick_reference'];
        const index = parseInt(e.key) - 1;
        if (sections[index] && window.app) {
            window.app.navigateToSection(sections[index]);
        }
    }
    
    // Ctrl/Cmd + K for search focus
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    }
});