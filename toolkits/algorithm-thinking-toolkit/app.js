// Algorithm Thinking Toolkit JavaScript - Fixed Version
class AlgoThinkApp {
    constructor() {
        this.currentSection = 'home';
        this.userData = this.loadUserData();
        this.activityLog = this.userData.activityLog || [];
        this.frameworksData = {
            "frameworks": [
                {
                    "id": "four-step",
                    "name": "4-Step Methodology",
                    "description": "Core framework with mountain exploration metaphor",
                    "steps": [
                        {
                            "step": 1,
                            "title": "Phân Tích & Phân Tách",
                            "description": "Khảo sát lãnh thổ và chia nhỏ vấn đề",
                            "metaphor": "Khám phá và lập bản đồ địa hình",
                            "activities": ["Đọc đề bài", "Xác định ràng buộc", "Tạo test cases", "Chia nhỏ thành các bài toán con"],
                            "questions": ["Chính xác cần giải quyết gì?", "Format input/output là gì?", "<PERSON><PERSON><PERSON> ràng buộc là gì?"]
                        },
                        {
                            "step": 2,
                            "title": "<PERSON>ậ<PERSON> & <PERSON>",
                            "description": "Chọn đường đi và công cụ phù hợp cho cuộc hành trình",
                            "metaphor": "Lập kế hoạch tuyến đường và chọn thiết bị", 
                            "activities": ["Chọn cấu trúc dữ liệu", "Lựa chọn thuật toán", "So sánh các cách tiếp cận", "Ước tính độ phức tạp"],
                            "questions": ["Cách tiếp cận nào phù hợp nhất?", "Cần những cấu trúc dữ liệu gì?", "Trade-offs là gì?"]
                        },
                        {
                            "step": 3,
                            "title": "Thực Hiện & Giám Sát",
                            "description": "Thực thi kế hoạch trong khi luôn cảnh giác với các chướng ngại",
                            "metaphor": "Đi theo đường đã định và thích ứng với thử thách",
                            "activities": ["Viết code từng bước", "Test thường xuyên", "Debug các vấn đề", "Xử lý edge cases"],
                            "questions": ["Implementation có đúng không?", "Có bug nào không?", "Làm sao test được?"]
                        },
                        {
                            "step": 4,
                            "title": "Đánh Giá & Tối Ưu", 
                            "description": "Phản ánh về cuộc hành trình và chuẩ bị cho những cuộc phiêu lưu tương lai",
                            "metaphor": "Xem xét chuyến thám hiểm và lập kế hoạch cải tiến",
                            "activities": ["Xác minh tính chính xác", "Phân tích hiệu suất", "Tối ưu hóa giải pháp", "Ghi chép insights"],
                            "questions": ["Đây có phải giải pháp tối ưu?", "Tôi đã học được gì?", "Làm sao cải thiện được?"]
                        }
                    ]
                }
            ]
        };
        
        this.init();
    }

    init() {
        // Set to light mode only
        document.documentElement.setAttribute('data-color-scheme', 'light');
        
        this.setupEventListeners();
        this.updateProgressDisplay();
        this.displayRecentActivity();
    }

    setupEventListeners() {
        // Navigation - Fixed event handling
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const section = e.target.getAttribute('data-section');
                if (section) {
                    this.navigateToSection(section);
                }
            });
        });

        // Framework cards - Fixed navigation
        document.querySelectorAll('.framework-card').forEach(card => {
            card.addEventListener('click', (e) => {
                e.preventDefault();
                const framework = card.getAttribute('data-framework');
                if (framework) {
                    this.navigateToFramework(framework);
                }
            });
        });

        // Step cards interaction - Fixed detail toggling
        document.querySelectorAll('.step-card').forEach(card => {
            card.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleStepDetails(card);
            });
        });

        // 5W1H wheel interaction - Fixed content display
        document.querySelectorAll('.w-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const wType = item.querySelector('h4').textContent.trim();
                this.showWDetails(wType);
            });
        });

        // Search functionality
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }
    }

    navigateToSection(sectionId) {
        console.log('Navigating to:', sectionId); // Debug log
        
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.add('active');
        } else {
            console.error('Section not found:', sectionId);
            return;
        }

        // Update navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const activeBtn = document.querySelector(`[data-section="${sectionId}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        this.currentSection = sectionId;
        this.logActivity(`Điều hướng đến ${this.getSectionName(sectionId)}`);
    }

    navigateToFramework(frameworkId) {
        console.log('Navigating to framework:', frameworkId); // Debug log
        this.navigateToSection('frameworks');
        
        // Scroll to specific framework
        setTimeout(() => {
            const frameworkElement = document.getElementById(`${frameworkId}-framework`);
            if (frameworkElement) {
                frameworkElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                this.logActivity(`Khám phá ${this.getFrameworkName(frameworkId)}`);
            }
        }, 100);
    }

    toggleStepDetails(stepCard) {
        const details = stepCard.querySelector('.step-details');
        if (details) {
            const isHidden = details.classList.contains('hidden');
            if (isHidden) {
                details.classList.remove('hidden');
            } else {
                details.classList.add('hidden');
            }
            
            const stepNumber = stepCard.getAttribute('data-step');
            this.logActivity(`${isHidden ? 'Mở' : 'Đóng'} chi tiết Bước ${stepNumber} - Phương Pháp 4 Bước`);
        }
    }

    showWDetails(wType) {
        console.log('Showing W details for:', wType); // Debug log
        const content = this.getWContent(wType);
        this.showModal('w-details', `Chi tiết ${wType}`, content);
        this.logActivity(`Tìm hiểu ${wType} trong khung 5W1H`);
    }

    getWContent(wType) {
        const wContents = {
            'What': {
                questions: ['Chính xác cần giải quyết gì?', 'Format input/output là gì?', 'Các ràng buộc là gì?', 'Thế nào được coi là thành công?'],
                description: 'Định nghĩa rõ ràng vấn đề cần giải quyết',
                color: '#FF6B6B'
            },
            'Why': {
                questions: ['Tại sao vấn đề này quan trọng?', 'Có những ứng dụng thực tế nào?', 'Tại sao công ty hay hỏi điều này?', 'Nó dạy những khái niệm gì?'],
                description: 'Hiểu ý nghĩa và tầm quan trọng của vấn đề',
                color: '#4ECDC4'
            },
            'Who': {
                questions: ['Ai được lợi từ giải pháp này?', 'Ai là người dùng cuối?', 'Ai nên học pattern này?', 'Ai quan tâm đến tối ưu hóa?'],
                description: 'Xác định các bên liên quan và người dùng',
                color: '#45B7D1'
            },
            'When': {
                questions: ['Khi nào nên dùng cách tiếp cận này?', 'Khi nào các phương án khác tốt hơn?', 'Khi nào hiệu suất quan trọng?', 'Khi nào trade-offs có thể chấp nhận được?'],
                description: 'Hiểu bối cảnh và thời điểm phù hợp',
                color: '#96CEB4'
            },
            'Where': {
                questions: ['Điều này phù hợp ở đâu trong kiến trúc hệ thống?', 'Nên implement ở đâu?', 'Ở đâu trong data flow?', 'Nó kết nối với các thành phần khác như thế nào?'],
                description: 'Xác định vị trí trong hệ thống tổng thể',
                color: '#FFEAA7'
            },
            'How': {
                questions: ['Cách tiếp cận high-level là gì?', 'Cần những cấu trúc dữ liệu nào?', 'Các bước chi tiết là gì?', 'Làm sao tối ưu hiệu suất?'],
                description: 'Thực hiện cụ thể giải pháp',
                color: '#FD79A8'
            }
        };

        const content = wContents[wType] || { questions: [], description: '', color: '#ccc' };
        return `
            <div class="w-detail-content" style="border-left: 4px solid ${content.color}; padding-left: 16px;">
                <p><strong>Mô tả:</strong> ${content.description}</p>
                <h4>Câu hỏi chìa khóa:</h4>
                <ul>
                    ${content.questions.map(q => `<li>${q}</li>`).join('')}
                </ul>
                <div class="w-example" style="background: ${content.color}15; padding: 12px; border-radius: 8px; margin-top: 16px;">
                    <strong>Ví dụ áp dụng:</strong>
                    <p>Áp dụng ${wType} vào bài toán Two Sum để hiểu rõ hơn cách sử dụng framework này.</p>
                </div>
            </div>
        `;
    }



    handleSearch(query) {
        if (query.length < 2) return;
        
        // Simple search implementation
        const searchableContent = [
            { term: 'phân tích', section: 'frameworks', description: 'Bước 1 của phương pháp 4 bước' },
            { term: '5w1h', section: 'frameworks', description: 'Khung phân tích toàn diện' },
            { term: 'polya', section: 'frameworks', description: 'Phương pháp 4 bước cổ điển' },
            { term: 'duck', section: 'toolkit', description: 'Rubber Duck Debugging' },
            { term: 'feynman', section: 'toolkit', description: 'Kỹ thuật học bằng cách dạy' }
        ];

        const results = searchableContent.filter(item => 
            item.term.toLowerCase().includes(query.toLowerCase())
        );

        this.showSearchResults(results);
    }

    showSearchResults(results) {
        if (results.length === 0) return;
        
        const resultsHtml = results.map(result => `
            <div class="search-result" onclick="app.navigateToSection('${result.section}')" style="padding: 8px; border-bottom: 1px solid var(--color-border); cursor: pointer;">
                <strong>${result.term}</strong> - ${result.description}
            </div>
        `).join('');

        this.showModal('search-results', 'Kết Quả Tìm Kiếm', `<div class="search-results">${resultsHtml}</div>`);
    }

    showModal(id, title, content) {
        // Remove existing modal
        const existingModal = document.getElementById(`${id}-modal`);
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal
        const modal = document.createElement('div');
        modal.id = `${id}-modal`;
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                <h2>${title}</h2>
                <div class="modal-body">${content}</div>
            </div>
        `;
        document.body.appendChild(modal);

        // Show modal
        modal.classList.remove('hidden');
    }

    // Duck Debugger functionality
    openDuckDebugger() {
        document.getElementById('duck-modal').classList.remove('hidden');
        this.logActivity('Mở Duck Debugger');
    }

    closeDuckDebugger() {
        document.getElementById('duck-modal').classList.add('hidden');
    }

    talkToDuck() {
        const input = document.getElementById('duck-input');
        const messages = document.getElementById('duck-messages');
        const userText = input.value.trim();

        if (!userText) return;

        // Add user message
        messages.innerHTML += `<div class="message user-message" style="margin-bottom: 8px; padding: 8px; background: var(--color-bg-1); border-radius: 8px;"><strong>Bạn:</strong> ${userText}</div>`;

        // Generate duck response
        const duckResponse = this.generateDuckResponse(userText);
        setTimeout(() => {
            messages.innerHTML += `<div class="message duck-message" style="margin-bottom: 8px; padding: 8px; background: var(--color-bg-2); border-radius: 8px;"><strong>🦆 Vịt:</strong> ${duckResponse}</div>`;
            messages.scrollTop = messages.scrollHeight;
        }, 1000);

        input.value = '';
        this.logActivity('Thảo luận với Rubber Duck');
    }

    generateDuckResponse(userInput) {
        const responses = [
            "Thú vị! Bạn có thể giải thích thêm về phần này không?",
            "Tôi hiểu. Vậy điều gì xảy ra khi input là edge case?",
            "Có vẻ như bạn đã suy nghĩ kỹ. Còn về time complexity thì sao?",
            "Hmm, bạn có chắc logic này đúng trong mọi trường hợp?",
            "Tốt! Bây giờ hãy thử trace qua một ví dụ cụ thể xem.",
            "Có cách nào tối ưu hơn không? Hãy suy nghĩ về space-time tradeoff.",
            "Rất hay! Bạn đã test với các input khác chưa?"
        ];

        return responses[Math.floor(Math.random() * responses.length)];
    }

    // Feynman Technique functionality
    openFeynmanTool() {
        document.getElementById('feynman-modal').classList.remove('hidden');
        this.logActivity('Mở Feynman Technique Tool');
    }

    closeFeynmanTool() {
        document.getElementById('feynman-modal').classList.add('hidden');
    }

    analyzeFeynmanExplanation() {
        const concept = document.getElementById('feynman-concept').value;
        const explanation = document.getElementById('feynman-explanation').value;

        if (!concept || !explanation) {
            alert('Vui lòng điền đầy đủ thông tin!');
            return;
        }

        // Analyze complexity and suggest improvements
        const analysis = this.analyzeTextComplexity(explanation);
        const gapsDiv = document.getElementById('knowledge-gaps');
        
        gapsDiv.innerHTML = `
            <div class="analysis-result" style="background: var(--color-bg-3); padding: 16px; border-radius: 8px;">
                <h4>Phân Tích:</h4>
                <p><strong>Độ phức tạp:</strong> ${analysis.complexity}</p>
                <p><strong>Đề xuất:</strong> ${analysis.suggestion}</p>
                <div class="gaps">
                    <h5>Có thể cần làm rõ thêm:</h5>
                    <ul>
                        ${analysis.gaps.map(gap => `<li>${gap}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;

        this.logActivity(`Phân tích khái niệm: ${concept}`);
    }

    analyzeTextComplexity(text) {
        const wordCount = text.split(' ').length;
        const complexWords = text.match(/\b\w{8,}\b/g) || [];
        const technicalTerms = text.match(/\b(algorithm|complexity|optimization|recursion|iteration|array|hash|tree|graph)\b/gi) || [];

        let complexity, suggestion;
        const gaps = [];

        if (wordCount < 50) {
            complexity = 'Quá ngắn';
            suggestion = 'Hãy giải thích chi tiết hơn với nhiều ví dụ cụ thể.';
            gaps.push('Cần thêm ví dụ minh họa', 'Giải thích các bước chi tiết hơn');
        } else if (complexWords.length > wordCount * 0.2) {
            complexity = 'Quá phức tạp';
            suggestion = 'Hãy sử dụng từ ngữ đơn giản hơn, như thể bạn đang nói với trẻ em.';
            gaps.push('Thay thế thuật ngữ khó bằng từ đơn giản', 'Thêm ví dụ sinh động');
        } else {
            complexity = 'Phù hợp';
            suggestion = 'Giải thích của bạn khá tốt! Hãy thử giải thích cho người khác nghe.';
            gaps.push('Có thể thêm sơ đồ minh họa', 'Test với các câu hỏi khó hơn');
        }

        if (technicalTerms.length > 0) {
            gaps.push(`Giải thích các thuật ngữ: ${technicalTerms.slice(0, 3).join(', ')}`);
        }

        return { complexity, suggestion, gaps };
    }

    // First Principles functionality
    openFirstPrinciples() {
        document.getElementById('first-principles-modal').classList.remove('hidden');
        this.logActivity('Mở First Principles Tool');
    }

    closeFirstPrinciples() {
        document.getElementById('first-principles-modal').classList.add('hidden');
    }

    analyzePrinciples() {
        const assumptions = document.getElementById('assumptions-input').value;
        
        if (!assumptions) {
            alert('Vui lòng nhập các giả định!');
            return;
        }

        const treeDiv = document.getElementById('fundamentals-tree');
        const breakdown = this.breakdownToPrinciples(assumptions);
        
        treeDiv.innerHTML = `
            <div class="principles-breakdown">
                <h4>Phân Tích Nguyên Lý Đầu:</h4>
                <div class="principle-level" style="background: var(--color-bg-4); padding: 12px; border-radius: 8px; margin-bottom: 12px;">
                    <h5>Giả định ban đầu:</h5>
                    <p>${assumptions}</p>
                </div>
                <div class="principle-level" style="background: var(--color-bg-5); padding: 12px; border-radius: 8px; margin-bottom: 12px;">
                    <h5>Câu hỏi thách thức:</h5>
                    <ul>
                        ${breakdown.challenges.map(c => `<li>${c}</li>`).join('')}
                    </ul>
                </div>
                <div class="principle-level" style="background: var(--color-bg-6); padding: 12px; border-radius: 8px;">
                    <h5>Nguyên lý cơ bản:</h5>
                    <ul>
                        ${breakdown.fundamentals.map(f => `<li>${f}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;

        this.logActivity('Phân tích First Principles');
    }

    breakdownToPrinciples(assumptions) {
        return {
            challenges: [
                'Tại sao giả định này lại đúng?',
                'Có bằng chứng nào chứng minh điều này?',
                'Điều gì xảy ra nếu giả định này sai?',
                'Có cách tiếp cận nào khác không?'
            ],
            fundamentals: [
                'Data cần được lưu trữ và truy xuất',
                'Thuật toán cần thời gian để thực thi',
                'Memory có giới hạn',
                'Trade-off giữa time và space luôn tồn tại',
                'Đơn giản thường tốt hơn phức tạp'
            ]
        };
    }

    // Problem solving functionality
    startProblemSolving() {
        const problemStatement = document.getElementById('problem-statement').value;
        const selectedFramework = document.getElementById('framework-select').value;

        if (!problemStatement.trim()) {
            alert('Vui lòng nhập đề bài!');
            return;
        }

        this.showProblemSolvingWizard(problemStatement, selectedFramework);
        this.logActivity(`Bắt đầu giải bài toán với ${this.getFrameworkName(selectedFramework)}`);
        this.updateProgress('problems-solved');
        this.updateProgress('frameworks-used');
    }

    showProblemSolvingWizard(problem, framework) {
        const wizardContent = this.generateWizardContent(framework, problem);
        this.showModal('problem-wizard', 'Hướng Dẫn Giải Bài Toán', wizardContent);
    }

    generateWizardContent(framework, problem) {
        switch(framework) {
            case 'four-step':
                return `
                    <div class="problem-wizard">
                        <h3>Áp dụng Phương Pháp 4 Bước</h3>
                        <div class="wizard-problem" style="background: var(--color-bg-1); padding: 16px; border-radius: 8px; margin-bottom: 24px;">
                            <strong>Đề bài:</strong>
                            <p>${problem}</p>
                        </div>
                        
                        <div class="wizard-steps">
                            <div class="wizard-step" style="margin-bottom: 24px; padding: 16px; background: var(--color-bg-5); border-radius: 8px;">
                                <h4>🏔️ Bước 1: Phân Tích & Phân Tách</h4>
                                <ul>
                                    <li>Đọc kỹ đề bài và xác định input/output</li>
                                    <li>Xác định các ràng buộc</li>
                                    <li>Tạo các test cases cơ bản</li>
                                    <li>Chia nhỏ thành các bài toán con nếu cần</li>
                                </ul>
                                <textarea placeholder="Ghi chép phân tích của bạn..." class="form-control" style="margin-top: 8px;"></textarea>
                            </div>
                            
                            <div class="wizard-step" style="margin-bottom: 24px; padding: 16px; background: var(--color-bg-6); border-radius: 8px;">
                                <h4>🎯 Bước 2: Lập Kế Hoạch</h4>
                                <ul>
                                    <li>Chọn cấu trúc dữ liệu phù hợp</li>
                                    <li>Quyết định thuật toán sử dụng</li>
                                    <li>So sánh các phương án</li>
                                    <li>Ước tính độ phức tạp</li>
                                </ul>
                                <textarea placeholder="Kế hoạch của bạn..." class="form-control" style="margin-top: 8px;"></textarea>
                            </div>
                            
                            <div class="wizard-step" style="margin-bottom: 24px; padding: 16px; background: var(--color-bg-7); border-radius: 8px;">
                                <h4>🚶‍♂️ Bước 3: Thực Hiện</h4>
                                <ul>
                                    <li>Code từng bước một cách cẩn thận</li>
                                    <li>Test với các cases đã chuẩn bị</li>
                                    <li>Debug khi gặp vấn đề</li>
                                    <li>Xử lý các edge cases</li>
                                </ul>
                                <textarea placeholder="Pseudocode hoặc implementation..." class="form-control" style="margin-top: 8px;"></textarea>
                            </div>
                            
                            <div class="wizard-step" style="padding: 16px; background: var(--color-bg-8); border-radius: 8px;">
                                <h4>🏆 Bước 4: Đánh Giá</h4>
                                <ul>
                                    <li>Kiểm tra tính đúng đắn</li>
                                    <li>Phân tích hiệu suất</li>
                                    <li>Tìm cách tối ưu nếu có thể</li>
                                    <li>Ghi chép những gì học được</li>
                                </ul>
                                <textarea placeholder="Đánh giá và insights..." class="form-control" style="margin-top: 8px;"></textarea>
                            </div>
                        </div>
                    </div>
                `;
            
            case '5w1h':
                return `
                    <div class="problem-wizard">
                        <h3>Áp dụng Khung 5W1H</h3>
                        <div class="wizard-problem" style="background: var(--color-bg-1); padding: 16px; border-radius: 8px; margin-bottom: 24px;">
                            <strong>Đề bài:</strong>
                            <p>${problem}</p>
                        </div>
                        
                        <div class="w-analysis">
                            <div class="w-section" style="margin-bottom: 16px; padding: 16px; background: var(--color-bg-2); border-radius: 8px;">
                                <h4>❓ What - Cái gì?</h4>
                                <p>Vấn đề cần giải quyết chính xác là gì?</p>
                                <textarea placeholder="Định nghĩa vấn đề..." class="form-control"></textarea>
                            </div>
                            
                            <div class="w-section" style="margin-bottom: 16px; padding: 16px; background: var(--color-bg-3); border-radius: 8px;">
                                <h4>🤔 Why - Tại sao?</h4>
                                <p>Tại sao vấn đề này quan trọng?</p>
                                <textarea placeholder="Ý nghĩa và ứng dụng..." class="form-control"></textarea>
                            </div>
                            
                            <div class="w-section" style="margin-bottom: 16px; padding: 16px; background: var(--color-bg-4); border-radius: 8px;">
                                <h4>👥 Who - Ai?</h4>
                                <p>Ai sẽ sử dụng giải pháp này?</p>
                                <textarea placeholder="Người dùng và stakeholders..." class="form-control"></textarea>
                            </div>
                            
                            <div class="w-section" style="margin-bottom: 16px; padding: 16px; background: var(--color-bg-5); border-radius: 8px;">
                                <h4>⏰ When - Khi nào?</h4>
                                <p>Khi nào nên áp dụng giải pháp này?</p>
                                <textarea placeholder="Bối cảnh sử dụng..." class="form-control"></textarea>
                            </div>
                            
                            <div class="w-section" style="margin-bottom: 16px; padding: 16px; background: var(--color-bg-6); border-radius: 8px;">
                                <h4>📍 Where - Ở đâu?</h4>
                                <p>Giải pháp này fit vào hệ thống như thế nào?</p>
                                <textarea placeholder="Vị trí trong architecture..." class="form-control"></textarea>
                            </div>
                            
                            <div class="w-section" style="padding: 16px; background: var(--color-bg-7); border-radius: 8px;">
                                <h4>🔧 How - Làm sao?</h4>
                                <p>Cách triển khai cụ thể?</p>
                                <textarea placeholder="Phương pháp thực hiện..." class="form-control"></textarea>
                            </div>
                        </div>
                    </div>
                `;
            
            default:
                return `<p>Framework này đang được phát triển...</p>`;
        }
    }

    loadExample(exampleName) {
        const examples = {
            'two-sum': {
                problem: 'Cho một mảng các số nguyên nums và một số target, trả về indices của hai số sao cho tổng của chúng bằng target.',
                solution: 'Có 3 cách chính: Brute force O(n²), Hash map O(n), Two pointers O(n log n)',
                frameworks: ['5W1H', 'Polya', 'Computational Thinking']
            }
        };

        const example = examples[exampleName];
        if (example) {
            const problemInput = document.getElementById('problem-statement');
            if (problemInput) {
                problemInput.value = example.problem;
            }
            this.logActivity(`Tải ví dụ: ${exampleName}`);
        }
    }

    // Progress and data management
    updateProgress(type) {
        if (!this.userData[type]) {
            this.userData[type] = 0;
        }
        this.userData[type]++;
        this.saveUserData();
        this.updateProgressDisplay();
    }

    updateProgressDisplay() {
        const problemsSolved = document.getElementById('problems-solved');
        const frameworksUsed = document.getElementById('frameworks-used');
        const learningStreak = document.getElementById('learning-streak');
        
        if (problemsSolved) problemsSolved.textContent = this.userData['problems-solved'] || 0;
        if (frameworksUsed) frameworksUsed.textContent = this.userData['frameworks-used'] || 0;
        if (learningStreak) learningStreak.textContent = this.userData['learning-streak'] || 0;
    }

    logActivity(activity) {
        const now = new Date();
        this.activityLog.unshift({
            activity,
            timestamp: now.toLocaleString('vi-VN'),
            date: now.toDateString()
        });

        // Keep only last 10 activities
        this.activityLog = this.activityLog.slice(0, 10);
        this.userData.activityLog = this.activityLog;
        this.saveUserData();
        this.displayRecentActivity();
    }

    displayRecentActivity() {
        const activityList = document.getElementById('activity-list');
        if (!activityList) return;

        if (this.activityLog.length === 0) {
            activityList.innerHTML = '<p class="no-activity">Chưa có hoạt động nào. Hãy bắt đầu khám phá!</p>';
            return;
        }

        const activitiesHtml = this.activityLog.map(activity => `
            <div class="activity-item" style="padding: 8px; border-bottom: 1px solid var(--color-border); margin-bottom: 8px;">
                <div class="activity-text" style="color: var(--color-text); margin-bottom: 4px;">${activity.activity}</div>
                <div class="activity-time" style="color: var(--color-text-secondary); font-size: 12px;">${activity.timestamp}</div>
            </div>
        `).join('');

        activityList.innerHTML = activitiesHtml;
    }

    saveJournalEntry() {
        const input = document.getElementById('journal-input');
        const entriesDiv = document.getElementById('journal-entries');
        if (!input || !entriesDiv) return;
        
        const entry = input.value.trim();
        if (!entry) return;

        const timestamp = new Date().toLocaleString('vi-VN');
        const entryHtml = `
            <div class="journal-entry" style="padding: 12px; background: var(--color-bg-1); border-radius: 8px; margin-bottom: 12px;">
                <div class="entry-content" style="color: var(--color-text); margin-bottom: 8px;">${entry}</div>
                <div class="entry-time" style="color: var(--color-text-secondary); font-size: 12px;">${timestamp}</div>
            </div>
        `;

        entriesDiv.insertAdjacentHTML('afterbegin', entryHtml);
        input.value = '';
        
        this.logActivity('Thêm ghi chú vào nhật ký học tập');
    }

    loadUserData() {
        try {
            return JSON.parse(localStorage.getItem('algoThinkUserData') || '{}');
        } catch {
            return {};
        }
    }

    saveUserData() {
        localStorage.setItem('algoThinkUserData', JSON.stringify(this.userData));
    }

    // Utility functions
    getSectionName(sectionId) {
        const sectionNames = {
            'home': 'Trang Chủ',
            'frameworks': 'Phương Pháp',
            'toolkit': 'Công Cụ',
            'practice': 'Luyện Tập',
            'progress': 'Tiến Độ',
            'reference': 'Tham Khảo'
        };
        return sectionNames[sectionId] || sectionId;
    }

    getFrameworkName(frameworkId) {
        const frameworkNames = {
            'four-step': 'Phương Pháp 4 Bước',
            '5w1h': 'Khung 5W1H',
            'polya': 'Phương Pháp Polya',
            'computational': 'Tư Duy Tính Toán'
        };
        return frameworkNames[frameworkId] || frameworkId;
    }
}

// Global functions for modal and interaction controls
function openDuckDebugger() {
    if (window.app) {
        window.app.openDuckDebugger();
    }
}

function closeDuckDebugger() {
    if (window.app) {
        window.app.closeDuckDebugger();
    }
}

function talkToDuck() {
    if (window.app) {
        window.app.talkToDuck();
    }
}

function openFeynmanTool() {
    if (window.app) {
        window.app.openFeynmanTool();
    }
}

function closeFeynmanTool() {
    if (window.app) {
        window.app.closeFeynmanTool();
    }
}

function analyzeFeynmanExplanation() {
    if (window.app) {
        window.app.analyzeFeynmanExplanation();
    }
}

function openFirstPrinciples() {
    if (window.app) {
        window.app.openFirstPrinciples();
    }
}

function closeFirstPrinciples() {
    if (window.app) {
        window.app.closeFirstPrinciples();
    }
}

function analyzePrinciples() {
    if (window.app) {
        window.app.analyzePrinciples();
    }
}

function startProblemSolving() {
    if (window.app) {
        window.app.startProblemSolving();
    }
}

function loadExample(exampleName) {
    if (window.app) {
        window.app.loadExample(exampleName);
    }
}

function saveJournalEntry() {
    if (window.app) {
        window.app.saveJournalEntry();
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AlgoThinkApp();
    
    // Add some welcome activity
    setTimeout(() => {
        if (window.app) {
            window.app.logActivity('Chào mừng đến với AlgoThink Toolkit! 🎉');
        }
    }, 1000);

    // Set up additional event listeners for toolkit buttons
    setTimeout(() => {
        // Add event listeners for toolkit buttons
        const duckBtn = document.querySelector('button[onclick="openDuckDebugger()"]');
        if (duckBtn) {
            duckBtn.addEventListener('click', openDuckDebugger);
        }

        const feynmanBtn = document.querySelector('button[onclick="openFeynmanTool()"]');
        if (feynmanBtn) {
            feynmanBtn.addEventListener('click', openFeynmanTool);
        }

        const principlesBtn = document.querySelector('button[onclick="openFirstPrinciples()"]');
        if (principlesBtn) {
            principlesBtn.addEventListener('click', openFirstPrinciples);
        }

        const problemBtn = document.querySelector('button[onclick="startProblemSolving()"]');
        if (problemBtn) {
            problemBtn.addEventListener('click', startProblemSolving);
        }

        const exampleBtn = document.querySelector('button[onclick="loadExample(\'two-sum\')"]');
        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => loadExample('two-sum'));
        }
    }, 500);
});

// Handle modal clicks outside content
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal')) {
        e.target.remove();
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case '1':
                e.preventDefault();
                if (window.app) window.app.navigateToSection('home');
                break;
            case '2':
                e.preventDefault();
                if (window.app) window.app.navigateToSection('frameworks');
                break;
            case '3':
                e.preventDefault();
                if (window.app) window.app.navigateToSection('toolkit');
                break;
            case '4':
                e.preventDefault();
                if (window.app) window.app.navigateToSection('practice');
                break;
            case '/':
                e.preventDefault();
                const searchInput = document.getElementById('search-input');
                if (searchInput) searchInput.focus();
                break;
        }
    }
    
    // ESC to close modals
    if (e.key === 'Escape') {
        document.querySelectorAll('.modal:not(.hidden)').forEach(modal => {
            modal.remove();
        });
    }
});