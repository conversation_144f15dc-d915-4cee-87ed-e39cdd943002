<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Algorithm Thinking & Problem Solving Toolkit</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">🧠</div>
                    <h1>AlgoThink</h1>
                </div>
                <nav class="nav">
                    <button class="nav-btn active" data-section="home">Home</button>
                    <button class="nav-btn" data-section="frameworks">Frameworks</button>
                    <button class="nav-btn" data-section="toolkit">Toolkit</button>
                    <button class="nav-btn" data-section="practice">Practice</button>
                    <button class="nav-btn" data-section="progress">Progress</button>
                    <button class="nav-btn" data-section="reference">Reference</button>
                </nav>
                <div class="header-actions">
                    <div class="search-box">
                        <input type="text" placeholder="Search..." id="search-input">
                        <button>🔍</button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Home Section -->
        <section id="home" class="section active">
            <div class="container">
                <div class="hero">
                    <h2>Welcome to Algorithm Thinking Toolkit</h2>
                    <p>A comprehensive toolkit to help you develop systematic and effective algorithmic problem-solving thinking.</p>
                </div>

                <div class="dashboard-grid">
                    <div class="quick-access">
                        <h3>Quick Access</h3>
                        <div class="framework-cards">
                            <div class="framework-card" data-framework="four-step">
                                <div class="card-icon">🏔️</div>
                                <h4>4-Step Method</h4>
                                <p>Core thinking framework with mountain exploration metaphor</p>
                            </div>
                            <div class="framework-card" data-framework="5w1h">
                                <div class="card-icon">❓</div>
                                <h4>5W1H Framework</h4>
                                <p>Comprehensive problem analysis</p>
                            </div>
                            <div class="framework-card" data-framework="polya">
                                <div class="card-icon">📋</div>
                                <h4>Polya Method</h4>
                                <p>4-step classical problem solving approach</p>
                            </div>
                            <div class="framework-card" data-framework="computational">
                                <div class="card-icon">⚙️</div>
                                <h4>Computational Thinking</h4>
                                <p>4 pillars of fundamental thinking</p>
                            </div>
                        </div>
                    </div>

                    <div class="daily-tip">
                        <h3>Daily Tip</h3>
                        <div class="tip-content">
                            <p><strong>Today:</strong> Try explaining your algorithm to a 5-year-old. If you can't do that, you might not understand it well enough!</p>
                        </div>
                    </div>

                    <div class="progress-overview">
                        <h3>Progress Overview</h3>
                        <div class="progress-stats">
                            <div class="stat">
                                <div class="stat-number" id="problems-solved">0</div>
                                <div class="stat-label">Problems Solved</div>
                            </div>
                            <div class="stat">
                                <div class="stat-number" id="frameworks-used">0</div>
                                <div class="stat-label">Frameworks Used</div>
                            </div>
                            <div class="stat">
                                <div class="stat-number" id="learning-streak">0</div>
                                <div class="stat-label">Learning Streak</div>
                            </div>
                        </div>
                    </div>

                    <div class="recent-activity">
                        <h3>Recent Activity</h3>
                        <div class="activity-list" id="activity-list">
                            <p class="no-activity">No activities yet. Start exploring!</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Frameworks Section -->
        <section id="frameworks" class="section">
            <div class="container">
                <h2>Thinking Frameworks</h2>
                
                <!-- 4-Step Methodology -->
                <div class="framework-detail" id="four-step-framework">
                    <div class="framework-header">
                        <h3>🏔️ 4-Step Method - Mountain Exploration Metaphor</h3>
                        <p>Core thinking framework that approaches problems like a mountain expedition</p>
                    </div>
                    <div class="steps-container">
                        <div class="step-card" data-step="1">
                            <div class="step-number">1</div>
                            <h4>Analyze & Decompose</h4>
                            <p class="step-metaphor">🗺️ Explore and map the terrain</p>
                            <p>Survey the territory and break down the problem</p>
                            <div class="step-details hidden">
                                <h5>Activities:</h5>
                                <ul>
                                    <li>Read the problem statement</li>
                                    <li>Identify constraints</li>
                                    <li>Create test cases</li>
                                    <li>Break down into sub-problems</li>
                                </ul>
                                <h5>Key questions:</h5>
                                <ul>
                                    <li>What exactly needs to be solved?</li>
                                    <li>What is the input/output format?</li>
                                    <li>What are the constraints?</li>
                                </ul>
                            </div>
                        </div>

                        <div class="step-card" data-step="2">
                            <div class="step-number">2</div>
                            <h4>Plan & Strategy</h4>
                            <p class="step-metaphor">🎯 Plan the route and choose equipment</p>
                            <p>Select the path and appropriate tools for the journey</p>
                            <div class="step-details hidden">
                                <h5>Activities:</h5>
                                <ul>
                                    <li>Choose data structures</li>
                                    <li>Select algorithms</li>
                                    <li>Compare approaches</li>
                                    <li>Estimate complexity</li>
                                </ul>
                                <h5>Key questions:</h5>
                                <ul>
                                    <li>Which approach is most suitable?</li>
                                    <li>What data structures are needed?</li>
                                    <li>What are the trade-offs?</li>
                                </ul>
                            </div>
                        </div>

                        <div class="step-card" data-step="3">
                            <div class="step-number">3</div>
                            <h4>Execute & Monitor</h4>
                            <p class="step-metaphor">🚶‍♂️ Follow the planned route and adapt to challenges</p>
                            <p>Execute the plan while staying alert to obstacles</p>
                            <div class="step-details hidden">
                                <h5>Activities:</h5>
                                <ul>
                                    <li>Write code step by step</li>
                                    <li>Test frequently</li>
                                    <li>Debug issues</li>
                                    <li>Handle edge cases</li>
                                </ul>
                                <h5>Key questions:</h5>
                                <ul>
                                    <li>Is the implementation correct?</li>
                                    <li>Are there any bugs?</li>
                                    <li>How can we test this?</li>
                                </ul>
                            </div>
                        </div>

                        <div class="step-card" data-step="4">
                            <div class="step-number">4</div>
                            <h4>Đánh Giá & Tối Ưu</h4>
                            <p class="step-metaphor">🏆 Xem xét chuyến thám hiểm và lập kế hoạch cải tiến</p>
                            <p>Phản ánh về cuộc hành trình và chuẩn bị cho những cuộc phiêu lưu tương lai</p>
                            <div class="step-details hidden">
                                <h5>Hoạt động:</h5>
                                <ul>
                                    <li>Xác minh tính chính xác</li>
                                    <li>Phân tích hiệu suất</li>
                                    <li>Tối ưu hóa giải pháp</li>
                                    <li>Ghi chép insights</li>
                                </ul>
                                <h5>Câu hỏi chìa khóa:</h5>
                                <ul>
                                    <li>Đây có phải giải pháp tối ưu?</li>
                                    <li>Tôi đã học được gì?</li>
                                    <li>Làm sao cải thiện được?</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 5W1H Framework -->
                <div class="framework-detail" id="5w1h-framework">
                    <div class="framework-header">
                        <h3>❓ Khung 5W1H - Phân Tích Toàn Diện</h3>
                        <p>Công cụ phân tích vấn đề một cách toàn diện và có hệ thống</p>
                    </div>
                    <div class="w-wheel">
                        <div class="w-item" data-color="#FF6B6B">
                            <h4>What</h4>
                            <p>Định nghĩa vấn đề</p>
                        </div>
                        <div class="w-item" data-color="#4ECDC4">
                            <h4>Why</h4>
                            <p>Ý nghĩa & ứng dụng</p>
                        </div>
                        <div class="w-item" data-color="#45B7D1">
                            <h4>Who</h4>
                            <p>Stakeholders & users</p>
                        </div>
                        <div class="w-item" data-color="#96CEB4">
                            <h4>When</h4>
                            <p>Bối cảnh & thời điểm</p>
                        </div>
                        <div class="w-item" data-color="#FFEAA7">
                            <h4>Where</h4>
                            <p>Tích hợp hệ thống</p>
                        </div>
                        <div class="w-item" data-color="#FD79A8">
                            <h4>How</h4>
                            <p>Các bước thực hiện</p>
                        </div>
                    </div>
                </div>

                <!-- Other frameworks will be similar... -->
                <div class="framework-detail" id="polya-framework">
                    <div class="framework-header">
                        <h3>📋 Phương Pháp Polya</h3>
                        <p>4 bước giải quyết vấn đề cổ điển và hiệu quả</p>
                    </div>
                    <div class="polya-steps">
                        <div class="polya-step">
                            <h4>1. Hiểu Vấn Đề</h4>
                            <p>Đọc kỹ, xác định những gì đã biết và chưa biết, tạo ví dụ</p>
                        </div>
                        <div class="polya-step">
                            <h4>2. Lập Kế Hoạch</h4>
                            <p>Chọn chiến lược, xem xét các vấn đề tương tự, chia nhỏ nếu cần</p>
                        </div>
                        <div class="polya-step">
                            <h4>3. Thực Hiện Kế Hoạch</h4>
                            <p>Triển khai từng bước, kiểm tra mỗi bước, kiên nhẫn</p>
                        </div>
                        <div class="polya-step">
                            <h4>4. Nhìn Lại</h4>
                            <p>Kiểm tra kết quả, tìm giải pháp khác, phản ánh về quá trình</p>
                        </div>
                    </div>
                </div>

                <div class="framework-detail" id="computational-framework">
                    <div class="framework-header">
                        <h3>⚙️ Tư Duy Tính Toán</h3>
                        <p>Bốn khái niệm cơ bản để giải quyết vấn đề</p>
                    </div>
                    <div class="pillars-grid">
                        <div class="pillar">
                            <h4>Phân Tách</h4>
                            <p>Chia nhỏ vấn đề phức tạp thành các phần dễ quản lý</p>
                        </div>
                        <div class="pillar">
                            <h4>Nhận Dạng Mẫu</h4>
                            <p>Tìm điểm tương đồng và xu hướng trong dữ liệu và vấn đề</p>
                        </div>
                        <div class="pillar">
                            <h4>Trừu Tượng Hóa</h4>
                            <p>Tập trung vào đặc điểm thiết yếu và bỏ qua chi tiết không cần thiết</p>
                        </div>
                        <div class="pillar">
                            <h4>Thuật Toán</h4>
                            <p>Tạo hướng dẫn từng bước để giải quyết vấn đề</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Toolkit Section -->
        <section id="toolkit" class="section">
            <div class="container">
                <h2>Bộ Công Cụ Hỗ Trợ</h2>
                
                <div class="techniques-grid">
                    <div class="technique-card">
                        <div class="technique-icon">🦆</div>
                        <h3>Rubber Duck Debugging</h3>
                        <p>Giải thích code của bạn cho con vịt cao su để tìm bug</p>
                        <button class="btn btn--primary" onclick="openDuckDebugger()">Mở Duck Debugger</button>
                    </div>

                    <div class="technique-card">
                        <div class="technique-icon">🎓</div>
                        <h3>Kỹ Thuật Feynman</h3>
                        <p>Học bằng cách giảng dạy khái niệm một cách đơn giản</p>
                        <button class="btn btn--primary" onclick="openFeynmanTool()">Mở Feynman Tool</button>
                    </div>

                    <div class="technique-card">
                        <div class="technique-icon">🔬</div>
                        <h3>Tư Duy Nguyên Lý Đầu</h3>
                        <p>Phân tích vấn đề về những sự thật cơ bản</p>
                        <button class="btn btn--primary" onclick="openFirstPrinciples()">Mở First Principles</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Practice Section -->
        <section id="practice" class="section">
            <div class="container">
                <h2>Luyện Tập</h2>
                
                <div class="practice-tools">
                    <div class="problem-solver">
                        <h3>Công Cụ Giải Bài Toán</h3>
                        <div class="problem-input">
                            <textarea id="problem-statement" placeholder="Nhập đề bài..." class="form-control"></textarea>
                        </div>
                        <div class="framework-selector">
                            <label class="form-label">Chọn phương pháp:</label>
                            <select id="framework-select" class="form-control">
                                <option value="four-step">Phương Pháp 4 Bước</option>
                                <option value="5w1h">Khung 5W1H</option>
                                <option value="polya">Phương Pháp Polya</option>
                                <option value="computational">Tư Duy Tính Toán</option>
                            </select>
                        </div>
                        <button class="btn btn--primary" onclick="startProblemSolving()">Bắt Đầu Giải</button>
                    </div>

                    <div class="example-problems">
                        <h3>Ví Dụ Mẫu</h3>
                        <div class="problem-examples">
                            <div class="example-card">
                                <h4>Two Sum</h4>
                                <p>Tìm hai số trong mảng có tổng bằng target</p>
                                <div class="example-meta">
                                    <span class="difficulty easy">Dễ</span>
                                    <span class="approaches">3 cách tiếp cận</span>
                                </div>
                                <button class="btn btn--outline" onclick="loadExample('two-sum')">Xem Chi Tiết</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Progress Section -->
        <section id="progress" class="section">
            <div class="container">
                <h2>Tiến Độ Học Tập</h2>
                
                <div class="progress-dashboard">
                    <div class="analytics-grid">
                        <div class="analytics-card">
                            <h3>Thống Kê Sử Dụng Phương Pháp</h3>
                            <div class="framework-usage" id="framework-usage">
                                <!-- Chart will be rendered here -->
                            </div>
                        </div>

                        <div class="analytics-card">
                            <h3>Biểu Đồ Kỹ Năng</h3>
                            <div class="skill-radar" id="skill-radar">
                                <!-- Radar chart will be rendered here -->
                            </div>
                        </div>

                        <div class="analytics-card">
                            <h3>Huy Hiệu Thành Tựu</h3>
                            <div class="badges-grid" id="badges-grid">
                                <div class="badge locked">
                                    <div class="badge-icon">🥇</div>
                                    <div class="badge-name">First Problem</div>
                                </div>
                                <div class="badge locked">
                                    <div class="badge-icon">🔥</div>
                                    <div class="badge-name">7-Day Streak</div>
                                </div>
                                <div class="badge locked">
                                    <div class="badge-icon">🧠</div>
                                    <div class="badge-name">Framework Master</div>
                                </div>
                            </div>
                        </div>

                        <div class="analytics-card">
                            <h3>Nhật Ký Học Tập</h3>
                            <div class="learning-journal">
                                <textarea id="journal-input" placeholder="Ghi chép suy nghĩ, insights..." class="form-control"></textarea>
                                <button class="btn btn--primary" onclick="saveJournalEntry()">Lưu Ghi Chú</button>
                                <div class="journal-entries" id="journal-entries"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reference Section -->
        <section id="reference" class="section">
            <div class="container">
                <h2>Tài Liệu Tham Khảo</h2>
                
                <div class="reference-grid">
                    <div class="reference-card">
                        <h3>🃏 Thẻ Tham Khảo Nhanh</h3>
                        <ul>
                            <li>Patterns thuật toán thông dụng</li>
                            <li>Hướng dẫn chọn cấu trúc dữ liệu</li>
                            <li>Phân tích độ phức tạp nhanh</li>
                            <li>Kỹ thuật debug phổ biến</li>
                        </ul>
                    </div>

                    <div class="reference-card">
                        <h3>📚 Thư Viện Tài Nguyên</h3>
                        <ul>
                            <li>Templates có thể tải xuống</li>
                            <li>Checklists có thể in</li>
                            <li>Thẻ tóm tắt framework</li>
                            <li>Tài nguyên học tập bên ngoài</li>
                        </ul>
                    </div>

                    <div class="reference-card">
                        <h3>🎯 Patterns Thuật Toán</h3>
                        <div class="patterns-list">
                            <div class="pattern-item">
                                <strong>Sliding Window:</strong> Cho các bài toán subarray/substring
                            </div>
                            <div class="pattern-item">
                                <strong>Two Pointers:</strong> Cho các bài toán mảng đã sắp xếp
                            </div>
                            <div class="pattern-item">
                                <strong>Dynamic Programming:</strong> Cho các bài toán tối ưu hóa
                            </div>
                            <div class="pattern-item">
                                <strong>Graph Traversal:</strong> Cho các bài toán mạng/cây
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <div id="duck-modal" class="modal hidden">
        <div class="modal-content">
            <span class="close" onclick="closeDuckDebugger()">&times;</span>
            <h2>🦆 Rubber Duck Debugger</h2>
            <div class="duck-container">
                <div class="duck-image">🦆</div>
                <div class="duck-conversation">
                    <div class="duck-messages" id="duck-messages"></div>
                    <textarea id="duck-input" placeholder="Giải thích code của bạn cho vịt..." class="form-control"></textarea>
                    <button class="btn btn--primary" onclick="talkToDuck()">Nói Với Vịt</button>
                </div>
            </div>
        </div>
    </div>

    <div id="feynman-modal" class="modal hidden">
        <div class="modal-content">
            <span class="close" onclick="closeFeynmanTool()">&times;</span>
            <h2>🎓 Kỹ Thuật Feynman</h2>
            <div class="feynman-steps">
                <div class="feynman-step">
                    <h3>1. Chọn khái niệm:</h3>
                    <input type="text" id="feynman-concept" placeholder="Nhập khái niệm cần học..." class="form-control">
                </div>
                <div class="feynman-step">
                    <h3>2. Giải thích đơn giản:</h3>
                    <textarea id="feynman-explanation" placeholder="Giải thích như thể bạn đang dạy cho một đứa trẻ..." class="form-control"></textarea>
                </div>
                <div class="feynman-step">
                    <h3>3. Xác định khoảng trống kiến thức:</h3>
                    <div id="knowledge-gaps"></div>
                </div>
                <button class="btn btn--primary" onclick="analyzeFeynmanExplanation()">Phân Tích</button>
            </div>
        </div>
    </div>

    <div id="first-principles-modal" class="modal hidden">
        <div class="modal-content">
            <span class="close" onclick="closeFirstPrinciples()">&times;</span>
            <h2>🔬 Tư Duy Nguyên Lý Đầu</h2>
            <div class="first-principles-tool">
                <div class="assumption-challenger">
                    <h3>Thách thức các giả định:</h3>
                    <textarea id="assumptions-input" placeholder="Liệt kê các giả định về vấn đề..." class="form-control"></textarea>
                </div>
                <div class="fundamentals-breakdown">
                    <h3>Phân tích về cơ bản:</h3>
                    <div id="fundamentals-tree"></div>
                </div>
                <button class="btn btn--primary" onclick="analyzePrinciples()">Phân Tích Nguyên Lý</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>