// Application Data
const APP_DATA = {
  "roles": {
    "software_architect": {
      "title": "Software Architect",
      "responsibilities": [
        "Thi<PERSON>t kế Hệ thống và <PERSON>ến trúc",
        "<PERSON><PERSON><PERSON> đạo <PERSON> thuậ<PERSON>", 
        "<PERSON> quyết định",
        "<PERSON><PERSON><PERSON> gi<PERSON> ro",
        "<PERSON><PERSON><PERSON> bả<PERSON> lượ<PERSON>",
        "Hướng dẫn và <PERSON>ợ<PERSON> tác",
        "Tài liệu hóa"
      ],
      "skills": [
        "Tư duy Hệ thống",
        "Thành thạo <PERSON> thuật",
        "Giải quyết Vấn đề",
        "<PERSON>iao tiếp",
        "<PERSON><PERSON><PERSON> đạo",
        "Kế hoạch Chiến lược"
      ],
      "levels": ["Junior", "Senior", "Principal", "Distinguished"]
    },
    "enterprise_application_designer": {
      "title": "Enterprise Application Designer",
      "responsibilities": [
        "Thiết kế Kiến trúc Ứng dụng",
        "<PERSON><PERSON> hoạch Tích hợp",
        "Lựa chọn <PERSON>h<PERSON>", 
        "<PERSON><PERSON><PERSON> su<PERSON>",
        "<PERSON><PERSON><PERSON> trú<PERSON> mậ<PERSON>",
        "<PERSON>ế hoạch Mở rộng"
      ],
      "skills": [
        "Enterprise Patterns",
        "Kiến trúc Tích hợp",
        "Mô hình hóa Domain",
        "Đánh giá Công nghệ",
        "Phân tích Business"
      ]
    }
  },
  
  "design_levels": {
    "strategic": {
      "title": "Strategic Design",
      "description": "Hiểu biết cấp cao về business và domain",
      "techniques": [
        {
          "name": "Domain-Driven Design",
          "description": "Tập trung vào business domain và độ phức tạp của model",
          "when_to_use": "Domains phức tạp với logic nghiệp vụ phong phú",
          "patterns": ["Bounded Context", "Context Mapping", "Ubiquitous Language"]
        },
        {
          "name": "Event Storming",
          "description": "Mô hình hóa domain cộng tác thông qua events",
          "when_to_use": "Hiểu các quy trình nghiệp vụ phức tạp",
          "patterns": ["Domain Events", "Command Events", "Process Flow"]
        },
        {
          "name": "Context Mapping",
          "description": "Định nghĩa mối quan hệ giữa các bounded contexts",
          "when_to_use": "Hệ thống lớn với nhiều teams",
          "patterns": ["Shared Kernel", "Customer-Supplier", "Anti-Corruption Layer"]
        }
      ]
    },
    
    "tactical": {
      "title": "Tactical Design",
      "description": "Implementation patterns trong bounded contexts",
      "techniques": [
        {
          "name": "Domain Modeling",
          "description": "Tạo domain models phong phú với behavior",
          "when_to_use": "Logic nghiệp vụ và quy tắc phức tạp",
          "patterns": ["Entity", "Value Object", "Aggregate", "Domain Service"]
        },
        {
          "name": "Repository Pattern",
          "description": "Trừu tượng hóa data access layer",
          "when_to_use": "Tách domain khỏi infrastructure",
          "patterns": ["Repository", "Unit of Work", "Specification"]
        }
      ]
    }
  },
  
  "architecture_patterns": {
    "structural": [
      {
        "name": "Layered Architecture",
        "description": "Tổ chức code thành các tầng ngang",
        "pros": ["Dễ hiểu", "Tách biệt rõ ràng", "Dễ test"],
        "cons": ["Có thể trở thành monolithic", "Overhead hiệu suất", "Tight coupling"],
        "use_cases": ["Web apps truyền thống", "Hệ thống vừa và nhỏ", "CRUD applications"],
        "layers": ["Presentation", "Business", "Persistence", "Database"]
      },
      {
        "name": "Microservices",
        "description": "Phân tách thành các services độc lập nhỏ",
        "pros": ["Khả năng mở rộng", "Đa dạng công nghệ", "Tự chủ team", "Cô lập lỗi"],
        "cons": ["Độ phức tạp", "Network overhead", "Nhất quán dữ liệu", "Khó test"],
        "use_cases": ["Hệ thống quy mô lớn", "Nhiều teams", "Yêu cầu high availability"],
        "principles": ["Single Responsibility", "Decentralized", "Failure Isolation", "Smart Endpoints"]
      },
      {
        "name": "Event-Driven Architecture",
        "description": "Components giao tiếp thông qua events",
        "pros": ["Loose coupling", "Khả năng mở rộng", "Responsiveness", "Mở rộng dễ"],
        "cons": ["Độ phức tạp", "Khó debug", "Eventual consistency"],
        "use_cases": ["Hệ thống real-time", "Tích hợp nặng", "Reactive systems"],
        "components": ["Event Producers", "Event Consumers", "Event Store", "Event Bus"]
      }
    ],
    
    "integration": [
      {
        "name": "Service-Oriented Architecture (SOA)",
        "description": "Services giao tiếp qua interfaces được định nghĩa rõ",
        "pros": ["Tái sử dụng", "Modularity", "Platform independence"],
        "cons": ["Performance overhead", "Độ phức tạp", "Governance"],
        "use_cases": ["Enterprise integration", "Modernization legacy"],
        "principles": ["Service Contract", "Loose Coupling", "Abstraction", "Reusability"]
      },
      {
        "name": "Enterprise Service Bus (ESB)",
        "description": "Infrastructure tích hợp tập trung",
        "pros": ["Quản lý tập trung", "Protocol translation", "Routing"],
        "cons": ["Single point of failure", "Bottleneck", "Độ phức tạp"],
        "use_cases": ["Enterprise integration", "Legacy systems"],
        "components": ["Message Broker", "Service Registry", "Transformation Engine"]
      }
    ]
  },
  
  "design_patterns": {
    "creational": [
      {
        "name": "Factory Pattern",
        "description": "Tạo objects mà không chỉ định exact classes",
        "use_case": "Khi logic tạo object phức tạp",
        "example": "Database connection factory cho các providers khác nhau"
      },
      {
        "name": "Singleton Pattern",
        "description": "Đảm bảo chỉ có một instance tồn tại",
        "use_case": "Shared resources như configurations",
        "example": "Application configuration manager"
      },
      {
        "name": "Builder Pattern", 
        "description": "Xây dựng complex objects từng bước",
        "use_case": "Objects với nhiều optional parameters",
        "example": "SQL query builder, HTTP request builder"
      }
    ],
    
    "structural": [
      {
        "name": "Adapter Pattern",
        "description": "Cho phép incompatible interfaces làm việc cùng nhau",
        "use_case": "Tích hợp với legacy systems",
        "example": "Payment gateway adapter cho các providers khác nhau"
      },
      {
        "name": "Decorator Pattern",
        "description": "Thêm behavior vào objects dynamically",
        "use_case": "Cross-cutting concerns như logging, caching",
        "example": "Middleware pipeline, method interceptors"
      },
      {
        "name": "Facade Pattern",
        "description": "Cung cấp interface đơn giản cho complex subsystem",
        "use_case": "Ẩn complexity khỏi clients",
        "example": "API gateway, service facade"
      }
    ],
    
    "behavioral": [
      {
        "name": "Observer Pattern",
        "description": "Thông báo cho multiple objects về state changes",
        "use_case": "Event handling, model-view synchronization",
        "example": "Event emitters, reactive programming"
      },
      {
        "name": "Strategy Pattern",
        "description": "Định nghĩa family of algorithms và làm chúng interchangeable",
        "use_case": "Nhiều cách để thực hiện cùng operation",
        "example": "Payment processing, sorting algorithms"
      },
      {
        "name": "Command Pattern",
        "description": "Đóng gói requests thành objects",
        "use_case": "Undo/redo, queuing operations",
        "example": "Job queues, database transactions"
      }
    ]
  },
  
  "enterprise_patterns": {
    "domain_logic": [
      {
        "name": "Transaction Script",
        "description": "Tổ chức business logic theo procedures",
        "pros": ["Đơn giản", "Trực tiếp", "Phát triển nhanh"],
        "cons": ["Code duplication", "Khó maintain", "Không có OOP benefits"],
        "use_case": "Business logic đơn giản, CRUD operations"
      },
      {
        "name": "Domain Model",
        "description": "Rich object model với behavior và data",
        "pros": ["Rich behavior", "Tái sử dụng", "Testability"],
        "cons": ["Độ phức tạp", "Learning curve", "ORM mapping"],
        "use_case": "Business logic phức tạp, rich domains"
      },
      {
        "name": "Service Layer",
        "description": "Định nghĩa application boundary với services",
        "pros": ["API rõ ràng", "Transaction control", "Security boundary"],
        "cons": ["Additional layer", "Có thể trở thành anemic"],
        "use_case": "Remote interfaces, transaction boundaries"
      }
    ],
    
    "data_source": [
      {
        "name": "Active Record",
        "description": "Object wraps database row với domain logic",
        "pros": ["Đơn giản", "Direct mapping", "Dễ hiểu"],
        "cons": ["Tight coupling", "Khó test", "Database dependent"],
        "use_case": "Domains đơn giản, rapid development"
      },
      {
        "name": "Data Mapper",
        "description": "Tách in-memory objects khỏi database",
        "pros": ["Loose coupling", "Rich domain model", "Testable"],
        "cons": ["Độ phức tạp", "Mapping overhead", "Nhiều code hơn"],
        "use_case": "Domains phức tạp, rich business logic"
      },
      {
        "name": "Repository Pattern",
        "description": "Đóng gói logic để truy cập data sources",
        "pros": ["Testable", "Flexible", "Clean separation"],
        "cons": ["Additional abstraction", "Có thể over-engineered"],
        "use_case": "Complex queries, multiple data sources"
      }
    ]
  },
  
  "integration_patterns": {
    "messaging": [
      {
        "name": "Message Channel",
        "description": "Kết nối applications bằng messaging",
        "types": ["Point-to-Point", "Publish-Subscribe", "Request-Reply"],
        "use_case": "Giao tiếp bất đồng bộ"
      },
      {
        "name": "Message Router",
        "description": "Route messages dựa trên content hoặc rules",
        "types": ["Content-Based", "Message Filter", "Recipient List"],
        "use_case": "Logic routing phức tạp"
      },
      {
        "name": "Message Translator",
        "description": "Transform message giữa các formats khác nhau",
        "types": ["Message Mapper", "Envelope Wrapper", "Content Enricher"],
        "use_case": "Tích hợp hệ thống với formats khác nhau"
      }
    ],
    
    "endpoints": [
      {
        "name": "Gateway",
        "description": "Single entry point cho multiple services",
        "types": ["API Gateway", "Message Gateway", "Service Gateway"],
        "use_case": "Microservices, external API access"
      },
      {
        "name": "Adapter",
        "description": "Kết nối với external systems",
        "types": ["Channel Adapter", "Messaging Gateway", "Service Activator"],
        "use_case": "Legacy system integration"
      }
    ]
  },
  
  "implementation_guidance": {
    "framework_lifecycle": {
      "boot_sequence": [
        "Configuration Loading",
        "Dependency Registration",
        "Service Initialization", 
        "Middleware Registration",
        "Route Registration",
        "Hook Registration",
        "Application Ready"
      ],
      "request_lifecycle": [
        "Request Received",
        "Middleware Pipeline",
        "Route Resolution",
        "Controller Execution",
        "Model Operations",
        "Response Generation",
        "Cleanup"
      ],
      "hooks_and_events": [
        "Before Request",
        "After Request",
        "Before Save",
        "After Save", 
        "Before Delete",
        "After Delete",
        "Error Handling"
      ]
    },
    
    "extension_points": {
      "middleware": {
        "description": "Intercept và modify request/response",
        "examples": ["Authentication", "Logging", "Rate Limiting", "CORS"],
        "implementation": "Pipeline pattern với next() calls"
      },
      "hooks": {
        "description": "Execute custom logic tại các điểm cụ thể",
        "examples": ["Model lifecycle", "Route handlers", "Application events"],
        "implementation": "Observer pattern với event emission"
      },
      "providers": {
        "description": "Mở rộng framework với custom services",
        "examples": ["Custom database drivers", "Third-party integrations"],
        "implementation": "Dependency injection container"
      }
    },
    
    "best_practices": [
      {
        "category": "Design",
        "practices": [
          "Tách biệt concerns rõ ràng",
          "Program to interfaces",
          "Ưu tiên composition over inheritance",
          "Keep it simple (KISS)",
          "Don't repeat yourself (DRY)"
        ]
      },
      {
        "category": "Architecture",
        "practices": [
          "Thiết kế cho sự thay đổi",
          "Document decisions",
          "Sử dụng proven patterns",
          "Xem xét non-functional requirements",
          "Plan for evolution"
        ]
      },
      {
        "category": "Implementation",
        "practices": [
          "Viết tests trước",
          "Sử dụng version control",
          "Implement monitoring",
          "Handle errors gracefully",
          "Optimize for readability"
        ]
      }
    ]
  },
  
  "decision_framework": {
    "pattern_selection": {
      "factors": [
        "Độ phức tạp vấn đề",
        "Kinh nghiệm team",
        "Yêu cầu hiệu suất",
        "Nhu cầu mở rộng",
        "Gánh nặng maintenance",
        "Ràng buộc thời gian"
      ],
      "process": [
        "Xác định vấn đề",
        "Phân tích constraints",
        "Đánh giá options",
        "Prototype nếu cần",
        "Document decision",
        "Review và adapt"
      ]
    },
    
    "architecture_evaluation": {
      "criteria": [
        "Functional requirements",
        "Quality attributes",
        "Technical constraints",
        "Business constraints",
        "Stakeholder concerns"
      ],
      "methods": [
        "Architecture tradeoff analysis",
        "Scenario-based evaluation",
        "Prototype validation",
        "Risk assessment"
      ]
    }
  }
};

class ArchitectureApp {
  constructor() {
    this.currentSection = 'overview';
    this.searchIndex = this.buildSearchIndex();
    this.init();
  }

  init() {
    this.setupNavigation();
    this.setupSearch();
    this.setupTabs();
    this.setupModal();
    this.renderAllContent();
  }

  setupNavigation() {
    const navButtons = document.querySelectorAll('.nav-button');
    navButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const section = e.target.closest('.nav-button').getAttribute('data-section');
        this.navigateToSection(section);
      });
    });
  }

  navigateToSection(section) {
    // Update active nav button
    document.querySelectorAll('.nav-button').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${section}"]`).classList.add('active');

    // Hide all sections
    document.querySelectorAll('.content-section').forEach(sec => {
      sec.classList.remove('active');
    });

    // Show target section
    const targetSection = document.getElementById(section);
    if (targetSection) {
      targetSection.classList.add('active');
    }
    
    this.currentSection = section;
    
    // Render specific content if needed
    if (section === 'architecture-patterns') {
      this.activateTab('structural', document.querySelector('#architecture-patterns'));
    } else if (section === 'design-patterns') {
      this.activateTab('creational', document.querySelector('#design-patterns'));
    } else if (section === 'enterprise-patterns') {
      this.activateTab('domain-logic', document.querySelector('#enterprise-patterns'));
    } else if (section === 'integration-patterns') {
      this.activateTab('messaging', document.querySelector('#integration-patterns'));
    } else if (section === 'implementation') {
      this.activateTab('lifecycle', document.querySelector('#implementation'));
    } else if (section === 'decision-framework') {
      this.activateTab('pattern-selection', document.querySelector('#decision-framework'));
    }
  }

  setupSearch() {
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');

    const performSearch = () => {
      const query = searchInput.value.trim();
      if (query.length > 0) {
        this.performSearch(query);
      }
    };

    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        performSearch();
      }
    });
  }

  setupTabs() {
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('tab-button')) {
        e.preventDefault();
        const tab = e.target.getAttribute('data-tab');
        const container = e.target.closest('.content-section');
        
        this.activateTab(tab, container);
      }
    });
  }

  activateTab(tab, container) {
    if (!container) return;

    // Update active tab button
    container.querySelectorAll('.tab-button').forEach(btn => {
      btn.classList.remove('active');
    });
    const tabButton = container.querySelector(`[data-tab="${tab}"]`);
    if (tabButton) {
      tabButton.classList.add('active');
    }

    // Update active tab pane
    container.querySelectorAll('.tab-pane').forEach(pane => {
      pane.classList.remove('active');
    });
    
    const targetPane = container.querySelector(`#${tab}`) || 
                      container.querySelector(`#${tab}-patterns`) ||
                      container.querySelector(`#${tab}-patterns-tab`);
    if (targetPane) {
      targetPane.classList.add('active');
    }
    
    this.renderTabContent(tab);
  }

  setupModal() {
    const modal = document.getElementById('pattern-modal');
    const closeBtn = document.getElementById('modal-close');
    const backdrop = document.querySelector('.modal-backdrop');

    const closeModal = () => {
      modal.classList.add('hidden');
    };

    closeBtn.addEventListener('click', closeModal);
    backdrop.addEventListener('click', closeModal);
    
    // Close modal on Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
        closeModal();
      }
    });

    // Handle pattern card clicks
    document.addEventListener('click', (e) => {
      if (e.target.closest('.pattern-card, .technique-card, .search-result-item')) {
        const card = e.target.closest('.pattern-card, .technique-card, .search-result-item');
        const dataAttr = card.getAttribute('data-pattern');
        if (dataAttr) {
          try {
            const data = JSON.parse(dataAttr);
            this.showPatternModal(data);
          } catch (err) {
            console.error('Error parsing pattern data:', err);
          }
        }
      }
    });
  }

  buildSearchIndex() {
    const index = [];
    
    // Index roles
    Object.values(APP_DATA.roles).forEach(role => {
      index.push({
        title: role.title,
        description: role.responsibilities.join(', '),
        category: 'Vai trò',
        data: role
      });
    });

    // Index design levels
    Object.values(APP_DATA.design_levels).forEach(level => {
      level.techniques.forEach(technique => {
        index.push({
          title: technique.name,
          description: technique.description,
          category: 'Mức độ Thiết kế',
          data: technique
        });
      });
    });

    // Index architecture patterns
    ['structural', 'integration'].forEach(type => {
      APP_DATA.architecture_patterns[type]?.forEach(pattern => {
        index.push({
          title: pattern.name,
          description: pattern.description,
          category: 'Architecture Patterns',
          data: pattern
        });
      });
    });

    // Index design patterns
    ['creational', 'structural', 'behavioral'].forEach(type => {
      APP_DATA.design_patterns[type]?.forEach(pattern => {
        index.push({
          title: pattern.name,
          description: pattern.description,
          category: 'Design Patterns',
          data: pattern
        });
      });
    });

    // Index enterprise patterns
    ['domain_logic', 'data_source'].forEach(type => {
      APP_DATA.enterprise_patterns[type]?.forEach(pattern => {
        index.push({
          title: pattern.name,
          description: pattern.description,
          category: 'Enterprise Patterns',
          data: pattern
        });
      });
    });

    // Index integration patterns
    ['messaging', 'endpoints'].forEach(type => {
      APP_DATA.integration_patterns[type]?.forEach(pattern => {
        index.push({
          title: pattern.name,
          description: pattern.description,
          category: 'Integration Patterns',
          data: pattern
        });
      });
    });

    return index;
  }

  performSearch(query) {
    const results = this.searchIndex.filter(item => 
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    );

    this.navigateToSection('search-results');
    this.renderSearchResults(results, query);
  }

  renderSearchResults(results, query) {
    const container = document.getElementById('search-results-container');
    
    if (results.length === 0) {
      container.innerHTML = `
        <div class="no-results">
          <p>Không tìm thấy kết quả cho "${query}"</p>
        </div>
      `;
      return;
    }

    container.innerHTML = results.map(result => `
      <div class="search-result-item" data-pattern='${JSON.stringify(result.data)}'>
        <div class="search-result-title">${result.title}</div>
        <div class="search-result-category">${result.category}</div>
        <div class="search-result-description">${result.description}</div>
      </div>
    `).join('');
  }

  renderAllContent() {
    this.renderRoles();
    this.renderDesignLevels();
    this.renderArchitecturePatterns();
    this.renderDesignPatterns();
    this.renderEnterprisePatterns();
    this.renderIntegrationPatterns();
    this.renderImplementation();
    this.renderDecisionFramework();
  }

  renderRoles() {
    const architect = APP_DATA.roles.software_architect;
    const designer = APP_DATA.roles.enterprise_application_designer;

    const archRespContainer = document.getElementById('architect-responsibilities');
    const archSkillsContainer = document.getElementById('architect-skills');
    const archLevelsContainer = document.getElementById('architect-levels');
    const designRespContainer = document.getElementById('designer-responsibilities');
    const designSkillsContainer = document.getElementById('designer-skills');

    if (archRespContainer) {
      archRespContainer.innerHTML = architect.responsibilities.map(r => `<li>${r}</li>`).join('');
    }
    if (archSkillsContainer) {
      archSkillsContainer.innerHTML = architect.skills.map(s => `<li>${s}</li>`).join('');
    }
    if (archLevelsContainer) {
      archLevelsContainer.innerHTML = architect.levels.map(l => `<span class="level-badge">${l}</span>`).join('');
    }
    if (designRespContainer) {
      designRespContainer.innerHTML = designer.responsibilities.map(r => `<li>${r}</li>`).join('');
    }
    if (designSkillsContainer) {
      designSkillsContainer.innerHTML = designer.skills.map(s => `<li>${s}</li>`).join('');
    }
  }

  renderDesignLevels() {
    const strategic = APP_DATA.design_levels.strategic;
    const tactical = APP_DATA.design_levels.tactical;

    const strategicContainer = document.getElementById('strategic-techniques');
    const tacticalContainer = document.getElementById('tactical-techniques');

    if (strategicContainer) {
      strategicContainer.innerHTML = strategic.techniques.map(technique => this.createTechniqueCard(technique)).join('');
    }
    if (tacticalContainer) {
      tacticalContainer.innerHTML = tactical.techniques.map(technique => this.createTechniqueCard(technique)).join('');
    }
  }

  createTechniqueCard(technique) {
    return `
      <div class="technique-card" data-pattern='${JSON.stringify(technique)}'>
        <h4>${technique.name}</h4>
        <p>${technique.description}</p>
        <div class="technique-patterns">
          ${technique.patterns.map(p => `<span class="pattern-badge">${p}</span>`).join('')}
        </div>
      </div>
    `;
  }

  renderArchitecturePatterns() {
    this.renderTabContent('structural');
  }

  renderDesignPatterns() {
    this.renderTabContent('creational');
  }

  renderEnterprisePatterns() {
    this.renderTabContent('domain-logic');
  }

  renderIntegrationPatterns() {
    this.renderTabContent('messaging');
  }

  renderTabContent(tab) {
    let patterns = [];
    let container = null;

    switch(tab) {
      case 'structural':
        patterns = APP_DATA.architecture_patterns.structural;
        container = document.querySelector('#structural-patterns .patterns-grid');
        break;
      case 'integration':
        patterns = APP_DATA.architecture_patterns.integration;
        container = document.querySelector('#integration-patterns-tab .patterns-grid');
        break;
      case 'creational':
        patterns = APP_DATA.design_patterns.creational;
        container = document.querySelector('#creational-patterns .patterns-grid');
        break;
      case 'structural-design':
        patterns = APP_DATA.design_patterns.structural;
        container = document.querySelector('#structural-design-patterns .patterns-grid');
        break;
      case 'behavioral':
        patterns = APP_DATA.design_patterns.behavioral;
        container = document.querySelector('#behavioral-patterns .patterns-grid');
        break;
      case 'domain-logic':
        patterns = APP_DATA.enterprise_patterns.domain_logic;
        container = document.querySelector('#domain-logic-patterns .patterns-grid');
        break;
      case 'data-source':
        patterns = APP_DATA.enterprise_patterns.data_source;
        container = document.querySelector('#data-source-patterns .patterns-grid');
        break;
      case 'messaging':
        patterns = APP_DATA.integration_patterns.messaging;
        container = document.querySelector('#messaging-patterns .patterns-grid');
        break;
      case 'endpoints':
        patterns = APP_DATA.integration_patterns.endpoints;
        container = document.querySelector('#endpoints-patterns .patterns-grid');
        break;
      case 'lifecycle':
        this.renderLifecycle();
        return;
      case 'extensions':
        this.renderExtensions();
        return;
      case 'best-practices':
        this.renderBestPractices();
        return;
      case 'pattern-selection':
        this.renderPatternSelection();
        return;
      case 'architecture-evaluation':
        this.renderArchitectureEvaluation();
        return;
    }

    if (container && patterns) {
      container.innerHTML = patterns.map(pattern => this.createPatternCard(pattern)).join('');
    }
  }

  createPatternCard(pattern) {
    return `
      <div class="pattern-card" data-pattern='${JSON.stringify(pattern)}'>
        <h3>${pattern.name}</h3>
        <div class="pattern-description">${pattern.description}</div>
        <div class="pattern-meta">
          ${pattern.use_case ? `
            <div class="pattern-meta-item">
              <div class="pattern-meta-label">Khi nào dùng:</div>
              <div class="pattern-meta-value">${pattern.use_case}</div>
            </div>
          ` : ''}
          ${pattern.use_cases ? `
            <div class="pattern-meta-item">
              <div class="pattern-meta-label">Use cases:</div>
              <div class="pattern-meta-value">${pattern.use_cases.slice(0, 2).join(', ')}</div>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }

  renderLifecycle() {
    const guidance = APP_DATA.implementation_guidance.framework_lifecycle;
    
    const bootContainer = document.getElementById('boot-sequence');
    const requestContainer = document.getElementById('request-lifecycle');
    const hooksContainer = document.getElementById('hooks-events');
    
    if (bootContainer) {
      bootContainer.innerHTML = this.createSequenceFlow(guidance.boot_sequence);
    }
    if (requestContainer) {
      requestContainer.innerHTML = this.createSequenceFlow(guidance.request_lifecycle);
    }
    if (hooksContainer) {
      hooksContainer.innerHTML = this.createSequenceFlow(guidance.hooks_and_events);
    }
  }

  createSequenceFlow(steps) {
    return steps.map((step, index) => {
      const isLast = index === steps.length - 1;
      return `
        <span class="sequence-item">${step}</span>
        ${!isLast ? '<span class="sequence-arrow">→</span>' : ''}
      `;
    }).join('');
  }

  renderExtensions() {
    const extensions = APP_DATA.implementation_guidance.extension_points;
    const container = document.getElementById('extension-points');
    
    if (container) {
      container.innerHTML = Object.entries(extensions).map(([key, ext]) => `
        <div class="extension-card">
          <h3>${key.charAt(0).toUpperCase() + key.slice(1)}</h3>
          <p>${ext.description}</p>
          <div class="extension-examples">
            <h4>Ví dụ:</h4>
            <ul>
              ${ext.examples.map(ex => `<li>${ex}</li>`).join('')}
            </ul>
          </div>
          <p><strong>Implementation:</strong> ${ext.implementation}</p>
        </div>
      `).join('');
    }
  }

  renderBestPractices() {
    const practices = APP_DATA.implementation_guidance.best_practices;
    const container = document.getElementById('best-practices-content');
    
    if (container) {
      container.innerHTML = practices.map(category => `
        <div class="practice-category">
          <h3>${category.category}</h3>
          <ul class="practice-list">
            ${category.practices.map(practice => `<li>${practice}</li>`).join('')}
          </ul>
        </div>
      `).join('');
    }
  }

  renderPatternSelection() {
    const selection = APP_DATA.decision_framework.pattern_selection;
    
    const factorsContainer = document.getElementById('selection-factors');
    const processContainer = document.getElementById('selection-process');
    
    if (factorsContainer) {
      factorsContainer.innerHTML = selection.factors.map(factor => `<div class="factor-item">${factor}</div>`).join('');
    }
    if (processContainer) {
      processContainer.innerHTML = this.createProcessFlow(selection.process);
    }
  }

  renderArchitectureEvaluation() {
    const evaluation = APP_DATA.decision_framework.architecture_evaluation;
    
    const criteriaContainer = document.getElementById('evaluation-criteria');
    const methodsContainer = document.getElementById('evaluation-methods');
    
    if (criteriaContainer) {
      criteriaContainer.innerHTML = evaluation.criteria.map(criteria => `<div class="criteria-item">${criteria}</div>`).join('');
    }
    if (methodsContainer) {
      methodsContainer.innerHTML = evaluation.methods.map(method => `<div class="method-item">${method}</div>`).join('');
    }
  }

  createProcessFlow(steps) {
    return steps.map((step, index) => {
      const isLast = index === steps.length - 1;
      return `
        <span class="process-step">${index + 1}. ${step}</span>
        ${!isLast ? '<span class="sequence-arrow">→</span>' : ''}
      `;
    }).join('');
  }

  renderImplementation() {
    this.renderLifecycle();
  }

  renderDecisionFramework() {
    this.renderPatternSelection();
  }

  showPatternModal(data) {
    const modal = document.getElementById('pattern-modal');
    const title = document.getElementById('modal-title');
    const body = document.getElementById('modal-body');
    
    title.textContent = data.name || data.title || 'Chi tiết Pattern';
    
    let content = `<div class="modal-section">
      <p>${data.description}</p>
    </div>`;
    
    if (data.when_to_use || data.use_case) {
      content += `<div class="modal-section">
        <h4>Khi nào nên sử dụng:</h4>
        <p>${data.when_to_use || data.use_case}</p>
      </div>`;
    }
    
    if (data.use_cases) {
      content += `<div class="modal-section">
        <h4>Use Cases:</h4>
        <ul class="modal-list">
          ${data.use_cases.map(uc => `<li>${uc}</li>`).join('')}
        </ul>
      </div>`;
    }
    
    if (data.pros && data.cons) {
      content += `<div class="modal-section">
        <h4>Ưu nhược điểm:</h4>
        <div class="modal-pros-cons">
          <div class="pros-section">
            <h5>Ưu điểm:</h5>
            <ul class="modal-list">
              ${data.pros.map(pro => `<li>${pro}</li>`).join('')}
            </ul>
          </div>
          <div class="cons-section">
            <h5>Nhược điểm:</h5>
            <ul class="modal-list">
              ${data.cons.map(con => `<li>${con}</li>`).join('')}
            </ul>
          </div>
        </div>
      </div>`;
    }
    
    if (data.patterns) {
      content += `<div class="modal-section">
        <h4>Patterns liên quan:</h4>
        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
          ${data.patterns.map(p => `<span class="pattern-badge">${p}</span>`).join('')}
        </div>
      </div>`;
    }
    
    if (data.example) {
      content += `<div class="modal-section">
        <h4>Ví dụ:</h4>
        <p>${data.example}</p>
      </div>`;
    }
    
    if (data.layers) {
      content += `<div class="modal-section">
        <h4>Các tầng:</h4>
        <ul class="modal-list">
          ${data.layers.map(layer => `<li>${layer}</li>`).join('')}
        </ul>
      </div>`;
    }
    
    if (data.components) {
      content += `<div class="modal-section">
        <h4>Components:</h4>
        <ul class="modal-list">
          ${data.components.map(comp => `<li>${comp}</li>`).join('')}
        </ul>
      </div>`;
    }
    
    if (data.principles) {
      content += `<div class="modal-section">
        <h4>Nguyên tắc:</h4>
        <ul class="modal-list">
          ${data.principles.map(principle => `<li>${principle}</li>`).join('')}
        </ul>
      </div>`;
    }
    
    if (data.types) {
      content += `<div class="modal-section">
        <h4>Các loại:</h4>
        <ul class="modal-list">
          ${data.types.map(type => `<li>${type}</li>`).join('')}
        </ul>
      </div>`;
    }
    
    if (data.responsibilities) {
      content += `<div class="modal-section">
        <h4>Trách nhiệm:</h4>
        <ul class="modal-list">
          ${data.responsibilities.map(resp => `<li>${resp}</li>`).join('')}
        </ul>
      </div>`;
    }
    
    if (data.skills) {
      content += `<div class="modal-section">
        <h4>Kỹ năng:</h4>
        <ul class="modal-list">
          ${data.skills.map(skill => `<li>${skill}</li>`).join('')}
        </ul>
      </div>`;
    }
    
    body.innerHTML = content;
    modal.classList.remove('hidden');
  }
}

// Theme synchronization with parent window
function syncThemeWithParent(theme) {
  if (globalThis.parent && globalThis.parent !== globalThis) {
    try {
      globalThis.parent.postMessage({
        type: 'THEME_CHANGE',
        theme: theme
      }, '*');
    } catch (_e) {
      // Ignore cross-origin errors
    }
  }
}

// Listen for theme changes from parent window
globalThis.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'THEME_CHANGE') {
    const theme = event.data.theme;
    document.documentElement.setAttribute('data-color-scheme', theme);
    localStorage.setItem('architectureExplorerTheme', theme);
  }
});

// Initialize theme from localStorage - use light theme as default
document.documentElement.setAttribute('data-color-scheme', 'light');

// Theme toggle functionality removed - using light theme only per project specification

// Initialize the app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new ArchitectureApp();
  
  // Set light theme as default per project specification
  document.documentElement.setAttribute('data-color-scheme', 'light');
});