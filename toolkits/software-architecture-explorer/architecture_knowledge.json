{"roles": {"software_architect": {"title": "Software Architect", "responsibilities": ["System Design and Architecture", "Technical Leadership", "Decision Making", "Risk Assessment", "Quality Assurance", "Mentoring and Collaboration", "Documentation"], "skills": ["Systems Thinking", "Technical Proficiency", "Problem Solving", "Communication", "Leadership", "Strategic Planning"], "levels": ["Junior", "Senior", "Principal", "Distinguished"]}, "enterprise_application_designer": {"title": "Enterprise Application Designer", "responsibilities": ["Application Architecture Design", "Integration Planning", "Technology Selection", "Performance Optimization", "Security Architecture", "Scalability Planning"], "skills": ["Enterprise Patterns", "Integration Architecture", "Domain Modeling", "Technology Evaluation", "Business Analysis"]}}, "design_levels": {"strategic": {"title": "Strategic Design", "description": "High-level business and domain understanding", "techniques": [{"name": "Domain-Driven Design", "description": "Focus on business domain and model complexity", "when_to_use": "Complex business domains with rich logic", "patterns": ["Bounded Context", "Context Mapping", "Ubiquitous Language"]}, {"name": "Event Storming", "description": "Collaborative domain modeling through events", "when_to_use": "Understanding complex business processes", "patterns": ["Domain Events", "Command Events", "Process Flow"]}, {"name": "Context Mapping", "description": "Define relationships between bounded contexts", "when_to_use": "Large systems with multiple teams", "patterns": ["Shared Kernel", "Customer-Supplier", "Anti-Corruption Layer"]}]}, "tactical": {"title": "Tactical Design", "description": "Implementation patterns within bounded contexts", "techniques": [{"name": "Domain Modeling", "description": "Create rich domain models with behavior", "when_to_use": "Complex business logic and rules", "patterns": ["Entity", "Value Object", "Aggregate", "Domain Service"]}, {"name": "Repository Pattern", "description": "Abstract data access layer", "when_to_use": "Decouple domain from infrastructure", "patterns": ["Repository", "Unit of Work", "Specification"]}]}}, "architecture_patterns": {"structural": [{"name": "Layered Architecture", "description": "Organize code into horizontal layers", "pros": ["Simple to understand", "Clear separation", "Easy testing"], "cons": ["Can become monolithic", "Performance overhead", "Tight coupling"], "use_cases": ["Traditional web apps", "Small to medium systems", "CRUD applications"], "layers": ["Presentation", "Business", "Persistence", "Database"]}, {"name": "Microservices", "description": "Decompose into small independent services", "pros": ["Scalability", "Technology diversity", "Team autonomy", "Fault isolation"], "cons": ["Complexity", "Network overhead", "Data consistency", "Testing difficulty"], "use_cases": ["Large scale systems", "Multiple teams", "High availability needs"], "principles": ["Single Responsibility", "Decentralized", "Failure Isolation", "Smart Endpoints"]}, {"name": "Event-Driven Architecture", "description": "Components communicate through events", "pros": ["Loose coupling", "Scalability", "Responsiveness", "Extensibility"], "cons": ["Complexity", "Debugging difficulty", "Eventual consistency"], "use_cases": ["Real-time systems", "Integration heavy", "Reactive systems"], "components": ["Event Producers", "Event Consumers", "Event Store", "Event Bus"]}], "integration": [{"name": "Service-Oriented Architecture (SOA)", "description": "Services communicate through well-defined interfaces", "pros": ["Reusability", "Modularity", "Platform independence"], "cons": ["Performance overhead", "Complexity", "Governance"], "use_cases": ["Enterprise integration", "Legacy modernization"], "principles": ["Service Contract", "<PERSON><PERSON>", "Abstraction", "Reusability"]}, {"name": "Enterprise Service Bus (ESB)", "description": "Centralized integration infrastructure", "pros": ["Centralized management", "Protocol translation", "Routing"], "cons": ["Single point of failure", "Bottleneck", "Complexity"], "use_cases": ["Enterprise integration", "Legacy systems"], "components": ["Message Broker", "Service Registry", "Transformation Engine"]}]}, "design_patterns": {"creational": [{"name": "Factory Pattern", "description": "Create objects without specifying exact classes", "use_case": "When object creation logic is complex", "example": "Database connection factory for different providers"}, {"name": "<PERSON><PERSON>", "description": "Ensure only one instance exists", "use_case": "Shared resources like configurations", "example": "Application configuration manager"}, {"name": "Builder Pattern", "description": "Construct complex objects step by step", "use_case": "Objects with many optional parameters", "example": "SQL query builder, HTTP request builder"}], "structural": [{"name": "<PERSON><PERSON><PERSON> Pattern", "description": "Allow incompatible interfaces to work together", "use_case": "Integrating with legacy systems", "example": "Payment gateway adapter for different providers"}, {"name": "Decorator Pattern", "description": "Add behavior to objects dynamically", "use_case": "Cross-cutting concerns like logging, caching", "example": "Middleware pipeline, method interceptors"}, {"name": "Facade Pattern", "description": "Provide simplified interface to complex subsystem", "use_case": "Hide complexity from clients", "example": "API gateway, service facade"}], "behavioral": [{"name": "Observer Pattern", "description": "Notify multiple objects about state changes", "use_case": "Event handling, model-view synchronization", "example": "Event emitters, reactive programming"}, {"name": "Strategy Pattern", "description": "Define family of algorithms and make them interchangeable", "use_case": "Multiple ways to perform same operation", "example": "Payment processing, sorting algorithms"}, {"name": "Command Pattern", "description": "Encapsulate requests as objects", "use_case": "Undo/redo, queuing operations", "example": "Job queues, database transactions"}]}, "enterprise_patterns": {"domain_logic": [{"name": "Transaction Script", "description": "Organize business logic by procedures", "pros": ["Simple", "Direct", "Fast development"], "cons": ["Code duplication", "Hard to maintain", "No OOP benefits"], "use_case": "Simple business logic, CRUD operations"}, {"name": "Domain Model", "description": "Rich object model with behavior and data", "pros": ["Rich behavior", "Reusability", "Testability"], "cons": ["Complexity", "Learning curve", "ORM mapping"], "use_case": "Complex business logic, rich domains"}, {"name": "Service Layer", "description": "Define application boundary with services", "pros": ["Clear API", "Transaction control", "Security boundary"], "cons": ["Additional layer", "Can become anemic"], "use_case": "Remote interfaces, transaction boundaries"}], "data_source": [{"name": "Active Record", "description": "Object wraps database row with domain logic", "pros": ["Simple", "Direct mapping", "Easy to understand"], "cons": ["Tight coupling", "Hard to test", "Database dependent"], "use_case": "Simple domains, rapid development"}, {"name": "Data Mapper", "description": "Separate in-memory objects from database", "pros": ["Loose coupling", "Rich domain model", "Testable"], "cons": ["Complexity", "Mapping overhead", "More code"], "use_case": "Complex domains, rich business logic"}, {"name": "Repository Pattern", "description": "Encapsulate logic to access data sources", "pros": ["Testable", "Flexible", "Clean separation"], "cons": ["Additional abstraction", "Can be over-engineered"], "use_case": "Complex queries, multiple data sources"}]}, "integration_patterns": {"messaging": [{"name": "Message Channel", "description": "Connect applications using messaging", "types": ["Point-to-Point", "Publish-Subscribe", "Request-Reply"], "use_case": "Asynchronous communication"}, {"name": "Message Router", "description": "Route messages based on content or rules", "types": ["Content-Based", "Message Filter", "Recipient List"], "use_case": "Complex routing logic"}, {"name": "Message Translator", "description": "Transform message between different formats", "types": ["Message Mapper", "Envelope Wrapper", "Content Enricher"], "use_case": "System integration with different formats"}], "endpoints": [{"name": "Gateway", "description": "Single entry point for multiple services", "types": ["API Gateway", "Message Gateway", "Service Gateway"], "use_case": "Microservices, external API access"}, {"name": "Adapter", "description": "Connect to external systems", "types": ["Channel Adapter", "Messaging Gateway", "Service Activator"], "use_case": "Legacy system integration"}]}, "implementation_guidance": {"framework_lifecycle": {"boot_sequence": ["Configuration Loading", "Dependency Registration", "Service Initialization", "Middleware Registration", "Route Registration", "Hook Registration", "Application Ready"], "request_lifecycle": ["Request Received", "Middleware Pipeline", "Route Resolution", "Controller Execution", "Model Operations", "Response Generation", "Cleanup"], "hooks_and_events": ["Before Request", "After Request", "Before Save", "After Save", "Before Delete", "After Delete", "Erro<PERSON>"]}, "extension_points": {"middleware": {"description": "Intercept and modify request/response", "examples": ["Authentication", "Logging", "Rate Limiting", "CORS"], "implementation": "Pipeline pattern with next() calls"}, "hooks": {"description": "Execute custom logic at specific points", "examples": ["Model lifecycle", "Route handlers", "Application events"], "implementation": "Observer pattern with event emission"}, "providers": {"description": "Extend framework with custom services", "examples": ["Custom database drivers", "Third-party integrations"], "implementation": "Dependency injection container"}}, "best_practices": [{"category": "Design", "practices": ["Separate concerns clearly", "Program to interfaces", "Favor composition over inheritance", "Keep it simple (KISS)", "Don't repeat yourself (DRY)"]}, {"category": "Architecture", "practices": ["Design for change", "Document decisions", "Use proven patterns", "Consider non-functional requirements", "Plan for evolution"]}, {"category": "Implementation", "practices": ["Write tests first", "Use version control", "Implement monitoring", "<PERSON><PERSON> errors gracefully", "Optimize for readability"]}]}, "decision_framework": {"pattern_selection": {"factors": ["Problem complexity", "Team experience", "Performance requirements", "Scalability needs", "Maintenance burden", "Time constraints"], "process": ["Identify the problem", "Analyze constraints", "Evaluate options", "Prototype if needed", "Document decision", "Review and adapt"]}, "architecture_evaluation": {"criteria": ["Functional requirements", "Quality attributes", "Technical constraints", "Business constraints", "Stakeholder concerns"], "methods": ["Architecture tradeoff analysis", "Scenario-based evaluation", "Prototype validation", "Risk assessment"]}}}