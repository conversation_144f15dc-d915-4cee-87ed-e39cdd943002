<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B<PERSON> công cụ tư duy sáng tạo liên tục</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2>T<PERSON> duy sáng tạo</h2>

            </div>
            <ul class="nav-menu">
                <li><a href="#" data-view="home" class="nav-link active">Trang chủ</a></li>
                <li><a href="#" data-view="morning-pages" class="nav-link">Morning Pages</a></li>
                <li><a href="#" data-view="idea-capture" class="nav-link">Thu thập ý tưởng</a></li>
                <li><a href="#" data-view="connection-practice" class="nav-link">Thực hành kết nối</a></li>
                <li><a href="#" data-view="zettelkasten" class="nav-link">Zettelkasten Mini</a></li>
                <li><a href="#" data-view="spaced-repetition" class="nav-link">Lặp lại ngắt quãng</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Home View -->
            <div id="home-view" class="view active">
                <header class="page-header">
                    <h1>Bộ công cụ tư duy sáng tạo liên tục</h1>
                    <p class="subtitle">Áp dụng triết lý "kết nối đúng dữ liệu" vào cuộc sống hàng ngày</p>
                </header>

                <div class="intro-section">
                    <div class="philosophy-card card">
                        <div class="card__body">
                            <h3>Triết lý "Kết nối đúng dữ liệu"</h3>
                            <p>Tư duy sáng tạo liên tục không phải là về việc sinh ra những ý tưởng hoàn toàn mới, mà là về việc kết nối những thông tin, trải nghiệm và kiến thức có sẵn một cách sáng tạo.</p>
                            
                            <div class="process-flow">
                                <img src="https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/feabeeaa8b7c59f0a96efe64fc9a0c43/893c089e-9444-4704-aeb6-bcdee0050e56/81ac0e1c.png" alt="Quy trình 3 bước kết nối đúng dữ liệu" class="process-image">
                            </div>

                            <div class="key-principles">
                                <h4>Nguyên tắc cốt lõi:</h4>
                                <ul>
                                    <li><strong>Thu thập:</strong> Ghi chép mọi thứ mà không phân biệt</li>
                                    <li><strong>Đặt câu hỏi:</strong> "Cái này gợi ra vấn đề gì?" "Ai từng trải qua điều này?"</li>
                                    <li><strong>Kết nối:</strong> Tìm điểm chung giữa trải nghiệm cá nhân và vấn đề của người khác</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="mindmap-section">
                        <img src="https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/feabeeaa8b7c59f0a96efe64fc9a0c43/17ca3b5f-50fb-4dbb-8012-4586521d9bf7/a837700d.png" alt="Mind Map: Hệ thống tư duy sáng tạo" class="mindmap-image">
                    </div>

                    <div class="daily-timeline">
                        <h3>Quy trình thực hành hàng ngày</h3>
                        <img src="https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/feabeeaa8b7c59f0a96efe64fc9a0c43/3cfe136b-d5e2-47e1-b425-54b7ab47aeb7/44e1a6cf.png" alt="Timeline thực hành hàng ngày" class="timeline-image">
                    </div>

                    <div class="getting-started card">
                        <div class="card__body">
                            <h3>Bắt đầu hành trình của bạn</h3>
                            <p>Chọn một công cụ từ menu bên trái để bắt đầu. Chúng tôi khuyến nghị bạn bắt đầu với Morning Pages để "làm sạch" tâm trí.</p>
                            <button class="btn btn--primary" onclick="switchView('morning-pages')">Bắt đầu với Morning Pages</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Morning Pages View -->
            <div id="morning-pages-view" class="view">
                <header class="page-header">
                    <h1>Morning Pages</h1>
                    <p class="subtitle">Viết liên tục trong 25 phút để làm sạch tâm trí và chuẩn bị cho ngày mới</p>
                </header>

                <div class="morning-pages-container">
                    <div class="timer-section card">
                        <div class="card__body">
                            <div class="timer-display">
                                <span id="timer">25:00</span>
                            </div>
                            <div class="timer-controls">
                                <button id="start-timer" class="btn btn--primary">Bắt đầu</button>
                                <button id="pause-timer" class="btn btn--secondary">Tạm dừng</button>
                                <button id="reset-timer" class="btn btn--outline">Đặt lại</button>
                            </div>
                            <div class="word-count">
                                Số từ: <span id="word-count">0</span>
                            </div>
                        </div>
                    </div>

                    <div class="writing-area">
                        <textarea id="morning-pages-text" placeholder="Bắt đầu viết... Đừng dừng lại, hãy để tâm trí chảy tự do trong 25 phút..." class="form-control morning-pages-textarea"></textarea>
                    </div>
                </div>
            </div>

            <!-- Idea Capture View -->
            <div id="idea-capture-view" class="view">
                <header class="page-header">
                    <h1>Thu thập ý tưởng</h1>
                    <p class="subtitle">Ghi lại mọi ý tưởng, quan sát và suy nghĩ trong ngày</p>
                </header>

                <div class="idea-capture-container">
                    <div class="idea-form card">
                        <div class="card__body">
                            <div class="form-group">
                                <label class="form-label">Ý tưởng của bạn:</label>
                                <textarea id="idea-text" placeholder="Nhập ý tưởng, quan sát hoặc suy nghĩ..." class="form-control" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Tags (phân cách bằng dấu phẩy):</label>
                                <input type="text" id="idea-tags" placeholder="psychology, creativity, life..." class="form-control">
                            </div>
                            <button id="save-idea" class="btn btn--primary">Lưu ý tưởng</button>
                        </div>
                    </div>

                    <div class="ideas-list">
                        <h3>Ý tưởng đã thu thập</h3>
                        <div id="ideas-container"></div>
                    </div>
                </div>
            </div>

            <!-- Connection Practice View -->
            <div id="connection-practice-view" class="view">
                <header class="page-header">
                    <h1>Thực hành kết nối</h1>
                    <p class="subtitle">Tìm mối liên hệ giữa các ý tưởng để tạo ra những insight mới</p>
                </header>

                <div class="connection-practice-container">
                    <div class="random-ideas card">
                        <div class="card__body">
                            <h3>Hai ý tưởng ngẫu nhiên:</h3>
                            <div class="idea-pair">
                                <div class="idea-item" id="idea-1">
                                    <strong>Ý tưởng 1:</strong>
                                    <p id="idea-1-text">Nhấn "Lấy ý tưởng mới" để bắt đầu</p>
                                </div>
                                <div class="idea-item" id="idea-2">
                                    <strong>Ý tưởng 2:</strong>
                                    <p id="idea-2-text"></p>
                                </div>
                            </div>
                            <button id="get-random-ideas" class="btn btn--secondary">Lấy ý tưởng mới</button>
                        </div>
                    </div>

                    <div class="connection-form card">
                        <div class="card__body">
                            <h3>Tạo kết nối</h3>
                            <div class="guiding-question">
                                <strong id="current-question">Cái này gợi ra vấn đề gì?</strong>
                                <button id="next-question" class="btn btn--outline btn--sm">Câu hỏi khác</button>
                            </div>
                            <div class="form-group">
                                <textarea id="connection-text" placeholder="Viết về mối liên hệ giữa hai ý tưởng này..." class="form-control" rows="4"></textarea>
                            </div>
                            <button id="save-connection" class="btn btn--primary">Lưu kết nối</button>
                        </div>
                    </div>

                    <div class="connections-list">
                        <h3>Kết nối đã tạo</h3>
                        <div id="connections-container"></div>
                    </div>
                </div>
            </div>

            <!-- Zettelkasten View -->
            <div id="zettelkasten-view" class="view">
                <header class="page-header">
                    <h1>Zettelkasten Mini</h1>
                    <p class="subtitle">Tạo và liên kết các ghi chú atomic để xây dựng mạng kiến thức</p>
                </header>

                <div class="zettelkasten-container">
                    <div class="zettel-form card">
                        <div class="card__body">
                            <div class="form-group">
                                <label class="form-label">Tiêu đề ghi chú:</label>
                                <input type="text" id="zettel-title" placeholder="Nhập tiêu đề ngắn gọn..." class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Nội dung:</label>
                                <textarea id="zettel-content" placeholder="Một ý tưởng, một khái niệm..." class="form-control" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Liên kết đến (ID ghi chú, phân cách bằng dấu phẩy):</label>
                                <input type="text" id="zettel-links" placeholder="1, 3, 5..." class="form-control">
                            </div>
                            <button id="save-zettel" class="btn btn--primary">Tạo ghi chú</button>
                        </div>
                    </div>

                    <div class="search-section card">
                        <div class="card__body">
                            <div class="form-group">
                                <label class="form-label">Tìm kiếm:</label>
                                <input type="text" id="search-zettels" placeholder="Tìm trong tiêu đề và nội dung..." class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="zettel-network">
                        <img src="https://user-gen-media-assets.s3.amazonaws.com/gpt4o_images/2bb3ae43-ca12-4e7f-9a27-868544d80b39.png" alt="Sơ đồ mạng Zettelkasten" class="network-image">
                    </div>

                    <div class="zettels-list">
                        <h3>Ghi chú của bạn</h3>
                        <div id="zettels-container"></div>
                    </div>
                </div>
            </div>

            <!-- Spaced Repetition View -->
            <div id="spaced-repetition-view" class="view">
                <header class="page-header">
                    <h1>Lặp lại ngắt quãng</h1>
                    <p class="subtitle">Ôn tập và củng cố các ý tưởng và kết nối đã tạo</p>
                </header>

                <div class="spaced-repetition-container">
                    <div class="progress-section card">
                        <div class="card__body">
                            <h3>Tiến độ học tập</h3>
                            <div class="progress-stats">
                                <div class="stat">
                                    <span class="stat-value" id="total-cards">0</span>
                                    <span class="stat-label">Tổng thẻ</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-value" id="due-cards">0</span>
                                    <span class="stat-label">Cần ôn</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-value" id="mastered-cards">0</span>
                                    <span class="stat-label">Đã thành thạo</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="quiz-card" class="quiz-section card">
                        <div class="card__body">
                            <div class="quiz-question">
                                <h3 id="quiz-question-text">Bắt đầu quiz để ôn tập kiến thức</h3>
                            </div>
                            <div id="quiz-answer" class="quiz-answer hidden">
                                <p id="quiz-answer-text"></p>
                            </div>
                            <div class="quiz-controls">
                                <button id="start-quiz" class="btn btn--primary">Bắt đầu Quiz</button>
                                <button id="show-answer" class="btn btn--secondary hidden">Xem đáp án</button>
                                <div id="difficulty-buttons" class="difficulty-buttons hidden">
                                    <button class="btn btn--outline difficulty-btn" data-difficulty="hard">Khó (1 ngày)</button>
                                    <button class="btn btn--outline difficulty-btn" data-difficulty="medium">Trung bình (3 ngày)</button>
                                    <button class="btn btn--outline difficulty-btn" data-difficulty="easy">Dễ (7 ngày)</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="app.js"></script>
</body>
</html>