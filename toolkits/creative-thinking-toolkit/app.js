// Application state
const appState = {
    currentView: 'home',
    timer: {
        minutes: 25,
        seconds: 0,
        isRunning: false,
        interval: null
    },
    ideas: [
        {id: 1, text: "<PERSON><PERSON><PERSON> người đều sợ thất bại", tags: ["psychology", "fear"], timestamp: "2025-01-01 09:00"},
        {id: 2, text: "<PERSON><PERSON>y cối phát triển chậm nhưng bền vững", tags: ["nature", "growth"], timestamp: "2025-01-01 10:30"},
        {id: 3, text: "Â<PERSON> nhạc làm người ta cảm thấy kết nối", tags: ["music", "connection"], timestamp: "2025-01-01 14:15"},
        {id: 4, text: "Những câu chuyện ngắn có sức mạnh lớn", tags: ["storytelling", "impact"], timestamp: "2025-01-01 16:45"}
    ],
    connections: [],
    zettels: [
        {id: 1, title: "<PERSON>uy luật 10,000 giờ", content: "Cần 10,000 giờ luyện tập để thành thạo một kỹ năng", links: []},
        {id: 2, title: "Growth Mindset", content: "Tin rằng khả năng có thể phát triển qua nỗ lực", links: [1]},
        {id: 3, title: "Deliberate Practice", content: "Luyện tập có mục đích với feedback", links: [1, 2]}
    ],
    quizCards: [],
    connectionQuestions: [
        "Cái này gợi ra vấn đề gì?",
        "Cái này ai từng trải qua rồi?", 
        "Cái này nếu mình nói ra, có ai gật đầu không?",
        "Hai ý tưởng này có điểm chung nào?",
        "Làm thế nào để kết hợp chúng thành một insight mới?"
    ],
    currentQuestionIndex: 0,
    currentQuiz: null,
    currentConnectionIdeas: []
};

// Theme functionality
function initTheme() {
    // Set light theme as default per project specification
    setTheme('light');
    
    // Listen for theme changes from parent (unified toolkit)
    window.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'THEME_CHANGE') {
            setTheme('light'); // Always use light theme
        }
    });
}

function setTheme(theme) {
    // Force light theme per project specification
    document.documentElement.setAttribute('data-color-scheme', 'light');
    localStorage.setItem('unifiedToolkit.theme', 'light');
}

// Theme toggle functionality removed - using light theme only per project specification

// Utility functions
function generateId() {
    return Date.now() + Math.random();
}

function formatTimestamp() {
    const now = new Date();
    return now.toLocaleString('vi-VN');
}

function countWords(text) {
    return text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
}

function getRandomElements(array, count = 2) {
    const shuffled = array.slice().sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
}

// View switching
function switchView(viewName) {
    console.log('Switching to view:', viewName);
    
    // Hide all views
    document.querySelectorAll('.view').forEach(view => {
        view.classList.remove('active');
    });
    
    // Show target view
    const targetView = document.getElementById(`${viewName}-view`);
    if (targetView) {
        targetView.classList.add('active');
        console.log('View switched successfully to:', viewName);
    } else {
        console.error('View not found:', `${viewName}-view`);
        return;
    }
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    const activeNavLink = document.querySelector(`[data-view="${viewName}"]`);
    if (activeNavLink) {
        activeNavLink.classList.add('active');
    }
    
    appState.currentView = viewName;
    
    // Initialize view-specific functionality
    initializeView(viewName);
}

function initializeView(viewName) {
    console.log('Initializing view:', viewName);
    switch(viewName) {
        case 'idea-capture':
            renderIdeas();
            break;
        case 'connection-practice':
            renderConnections();
            break;
        case 'zettelkasten':
            renderZettels();
            break;
        case 'spaced-repetition':
            updateQuizStats();
            generateQuizCards();
            break;
    }
}

// Morning Pages functionality
function startTimer() {
    console.log('Starting timer...');
    if (!appState.timer.isRunning) {
        appState.timer.isRunning = true;
        appState.timer.interval = setInterval(updateTimer, 1000);
        const startBtn = document.getElementById('start-timer');
        if (startBtn) {
            startBtn.textContent = 'Đang chạy...';
            startBtn.disabled = true;
        }
    }
}

function pauseTimer() {
    console.log('Pausing timer...');
    if (appState.timer.isRunning) {
        appState.timer.isRunning = false;
        clearInterval(appState.timer.interval);
        const startBtn = document.getElementById('start-timer');
        if (startBtn) {
            startBtn.textContent = 'Tiếp tục';
            startBtn.disabled = false;
        }
    }
}

function resetTimer() {
    console.log('Resetting timer...');
    appState.timer.isRunning = false;
    clearInterval(appState.timer.interval);
    appState.timer.minutes = 25;
    appState.timer.seconds = 0;
    updateTimerDisplay();
    
    const startBtn = document.getElementById('start-timer');
    const textarea = document.getElementById('morning-pages-text');
    
    if (startBtn) {
        startBtn.textContent = 'Bắt đầu';
        startBtn.disabled = false;
    }
    if (textarea) {
        textarea.value = '';
    }
    updateWordCount();
}

function updateTimer() {
    if (appState.timer.seconds > 0) {
        appState.timer.seconds--;
    } else if (appState.timer.minutes > 0) {
        appState.timer.minutes--;
        appState.timer.seconds = 59;
    } else {
        // Timer finished
        appState.timer.isRunning = false;
        clearInterval(appState.timer.interval);
        alert('Hoàn thành! Bạn đã viết xong Morning Pages.');
        const startBtn = document.getElementById('start-timer');
        if (startBtn) {
            startBtn.textContent = 'Bắt đầu';
            startBtn.disabled = false;
        }
    }
    updateTimerDisplay();
}

function updateTimerDisplay() {
    const display = document.getElementById('timer');
    if (display) {
        const minutes = appState.timer.minutes.toString().padStart(2, '0');
        const seconds = appState.timer.seconds.toString().padStart(2, '0');
        display.textContent = `${minutes}:${seconds}`;
    }
}

function updateWordCount() {
    const textarea = document.getElementById('morning-pages-text');
    const wordCountDisplay = document.getElementById('word-count');
    
    if (textarea && wordCountDisplay) {
        const wordCount = countWords(textarea.value);
        wordCountDisplay.textContent = wordCount;
    }
}

// Idea Capture functionality
function saveIdea() {
    console.log('Saving idea...');
    const ideaText = document.getElementById('idea-text').value.trim();
    const tagsText = document.getElementById('idea-tags').value.trim();
    
    if (!ideaText) {
        alert('Vui lòng nhập ý tưởng của bạn');
        return;
    }
    
    const tags = tagsText ? tagsText.split(',').map(tag => tag.trim()) : [];
    const newIdea = {
        id: generateId(),
        text: ideaText,
        tags: tags,
        timestamp: formatTimestamp()
    };
    
    appState.ideas.unshift(newIdea);
    
    // Clear form
    document.getElementById('idea-text').value = '';
    document.getElementById('idea-tags').value = '';
    
    renderIdeas();
    console.log('Idea saved successfully');
}

function renderIdeas() {
    const container = document.getElementById('ideas-container');
    if (!container) return;
    
    container.innerHTML = '';
    
    appState.ideas.forEach(idea => {
        const ideaElement = document.createElement('div');
        ideaElement.className = 'idea-item';
        
        const tagsHtml = idea.tags.map(tag => 
            `<span class="tag">${tag}</span>`
        ).join('');
        
        ideaElement.innerHTML = `
            <h4>${idea.text}</h4>
            <div class="idea-tags">${tagsHtml}</div>
            <div class="idea-meta">${idea.timestamp}</div>
        `;
        
        container.appendChild(ideaElement);
    });
}

// Connection Practice functionality
function getRandomIdeas() {
    console.log('Getting random ideas...');
    const randomIdeas = getRandomElements(appState.ideas, 2);
    
    if (randomIdeas.length >= 2) {
        const idea1Text = document.getElementById('idea-1-text');
        const idea2Text = document.getElementById('idea-2-text');
        
        if (idea1Text) idea1Text.textContent = randomIdeas[0].text;
        if (idea2Text) idea2Text.textContent = randomIdeas[1].text;
        
        // Store current ideas for connection
        appState.currentConnectionIdeas = randomIdeas;
        console.log('Random ideas loaded');
    }
}

function nextQuestion() {
    console.log('Getting next question...');
    appState.currentQuestionIndex = (appState.currentQuestionIndex + 1) % appState.connectionQuestions.length;
    const questionElement = document.getElementById('current-question');
    if (questionElement) {
        questionElement.textContent = appState.connectionQuestions[appState.currentQuestionIndex];
    }
}

function saveConnection() {
    console.log('Saving connection...');
    const connectionText = document.getElementById('connection-text').value.trim();
    
    if (!connectionText) {
        alert('Vui lòng viết về mối liên hệ giữa hai ý tưởng');
        return;
    }
    
    if (!appState.currentConnectionIdeas || appState.currentConnectionIdeas.length < 2) {
        alert('Vui lòng lấy hai ý tưởng trước khi tạo kết nối');
        return;
    }
    
    const newConnection = {
        id: generateId(),
        ideas: [...appState.currentConnectionIdeas],
        question: appState.connectionQuestions[appState.currentQuestionIndex],
        connection: connectionText,
        timestamp: formatTimestamp()
    };
    
    appState.connections.unshift(newConnection);
    
    // Clear form
    document.getElementById('connection-text').value = '';
    
    renderConnections();
    console.log('Connection saved successfully');
}

function renderConnections() {
    const container = document.getElementById('connections-container');
    if (!container) return;
    
    container.innerHTML = '';
    
    appState.connections.forEach(connection => {
        const connectionElement = document.createElement('div');
        connectionElement.className = 'connection-item';
        
        connectionElement.innerHTML = `
            <div class="connection-ideas">
                <strong>Ý tưởng 1:</strong> ${connection.ideas[0].text}<br>
                <strong>Ý tưởng 2:</strong> ${connection.ideas[1].text}
            </div>
            <div><strong>Câu hỏi:</strong> ${connection.question}</div>
            <div><strong>Kết nối:</strong> ${connection.connection}</div>
            <div class="idea-meta">${connection.timestamp}</div>
        `;
        
        container.appendChild(connectionElement);
    });
}

// Zettelkasten functionality
function saveZettel() {
    console.log('Saving zettel...');
    const title = document.getElementById('zettel-title').value.trim();
    const content = document.getElementById('zettel-content').value.trim();
    const linksText = document.getElementById('zettel-links').value.trim();
    
    if (!title || !content) {
        alert('Vui lòng nhập tiêu đề và nội dung');
        return;
    }
    
    const links = linksText ? 
        linksText.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
    
    const newZettel = {
        id: appState.zettels.length + 1,
        title: title,
        content: content,
        links: links
    };
    
    appState.zettels.push(newZettel);
    
    // Clear form
    document.getElementById('zettel-title').value = '';
    document.getElementById('zettel-content').value = '';
    document.getElementById('zettel-links').value = '';
    
    renderZettels();
    console.log('Zettel saved successfully');
}

function searchZettels() {
    const query = document.getElementById('search-zettels').value.toLowerCase();
    const filteredZettels = query ? 
        appState.zettels.filter(zettel => 
            zettel.title.toLowerCase().includes(query) || 
            zettel.content.toLowerCase().includes(query)
        ) : appState.zettels;
    
    renderZettels(filteredZettels);
}

function renderZettels(zettels = appState.zettels) {
    const container = document.getElementById('zettels-container');
    if (!container) return;
    
    container.innerHTML = '';
    
    zettels.forEach(zettel => {
        const zettelElement = document.createElement('div');
        zettelElement.className = 'zettel-item';
        
        const linksHtml = zettel.links.map(linkId => 
            `<a href="#" class="zettel-link" onclick="highlightZettel(${linkId}); return false;">#${linkId}</a>`
        ).join('');
        
        zettelElement.innerHTML = `
            <div class="zettel-id">#${zettel.id}</div>
            <h4 class="zettel-title">${zettel.title}</h4>
            <p class="zettel-content">${zettel.content}</p>
            <div class="zettel-links">${linksHtml}</div>
        `;
        
        container.appendChild(zettelElement);
    });
}

function highlightZettel(id) {
    const zettelElements = document.querySelectorAll('.zettel-item');
    zettelElements.forEach(el => el.style.backgroundColor = '');
    
    const targetZettel = document.querySelector(`.zettel-item:nth-child(${id})`);
    if (targetZettel) {
        targetZettel.style.backgroundColor = 'var(--color-bg-2)';
        targetZettel.scrollIntoView({ behavior: 'smooth' });
    }
}

// Spaced Repetition functionality
function generateQuizCards() {
    appState.quizCards = [];
    
    // Add ideas as quiz cards
    appState.ideas.forEach(idea => {
        appState.quizCards.push({
            id: generateId(),
            type: 'idea',
            question: `Bạn có nhớ ý tưởng này không: "${idea.text}"?`,
            answer: `Tags: ${idea.tags.join(', ')}\nThời gian: ${idea.timestamp}`,
            dueDate: new Date(),
            interval: 1,
            easiness: 2.5
        });
    });
    
    // Add connections as quiz cards
    appState.connections.forEach(connection => {
        appState.quizCards.push({
            id: generateId(),
            type: 'connection',
            question: `Bạn đã tạo kết nối gì giữa "${connection.ideas[0].text}" và "${connection.ideas[1].text}"?`,
            answer: connection.connection,
            dueDate: new Date(),
            interval: 1,
            easiness: 2.5
        });
    });
    
    // Add zettels as quiz cards
    appState.zettels.forEach(zettel => {
        appState.quizCards.push({
            id: generateId(),
            type: 'zettel',
            question: `Nội dung của ghi chú "${zettel.title}" là gì?`,
            answer: zettel.content,
            dueDate: new Date(),
            interval: 1,
            easiness: 2.5
        });
    });
}

function startQuiz() {
    console.log('Starting quiz...');
    const dueCards = appState.quizCards.filter(card => card.dueDate <= new Date());
    
    if (dueCards.length === 0) {
        alert('Không có thẻ nào cần ôn tập ngay lúc này!');
        return;
    }
    
    appState.currentQuiz = dueCards[Math.floor(Math.random() * dueCards.length)];
    
    document.getElementById('quiz-question-text').textContent = appState.currentQuiz.question;
    document.getElementById('quiz-answer-text').textContent = appState.currentQuiz.answer;
    
    // Show/hide appropriate buttons
    document.getElementById('start-quiz').classList.add('hidden');
    document.getElementById('show-answer').classList.remove('hidden');
    document.getElementById('quiz-answer').classList.add('hidden');
    document.getElementById('difficulty-buttons').classList.add('hidden');
}

function showAnswer() {
    console.log('Showing answer...');
    document.getElementById('quiz-answer').classList.remove('hidden');
    document.getElementById('show-answer').classList.add('hidden');
    document.getElementById('difficulty-buttons').classList.remove('hidden');
}

function handleDifficulty(event) {
    const difficulty = event.target.dataset.difficulty;
    const card = appState.currentQuiz;
    
    if (!card) return;
    
    console.log('Handling difficulty:', difficulty);
    
    // Update card based on difficulty
    let multiplier = 1;
    switch(difficulty) {
        case 'easy':
            multiplier = 2.5;
            break;
        case 'medium':
            multiplier = 1.3;
            break;
        case 'hard':
            multiplier = 0.8;
            break;
    }
    
    card.interval = Math.max(1, Math.round(card.interval * multiplier));
    card.dueDate = new Date(Date.now() + card.interval * 24 * 60 * 60 * 1000);
    
    // Reset quiz interface
    document.getElementById('start-quiz').classList.remove('hidden');
    document.getElementById('show-answer').classList.add('hidden');
    document.getElementById('quiz-answer').classList.add('hidden');
    document.getElementById('difficulty-buttons').classList.add('hidden');
    document.getElementById('quiz-question-text').textContent = 'Bắt đầu quiz để ôn tập kiến thức';
    
    appState.currentQuiz = null;
    updateQuizStats();
}

function updateQuizStats() {
    const totalCards = appState.quizCards.length;
    const dueCards = appState.quizCards.filter(card => card.dueDate <= new Date()).length;
    const masteredCards = appState.quizCards.filter(card => card.interval >= 7).length;
    
    const totalElement = document.getElementById('total-cards');
    const dueElement = document.getElementById('due-cards');
    const masteredElement = document.getElementById('mastered-cards');
    
    if (totalElement) totalElement.textContent = totalCards;
    if (dueElement) dueElement.textContent = dueCards;
    if (masteredElement) masteredElement.textContent = masteredCards;
}

// Initialize application
function initApp() {
    console.log('Initializing application...');
    
    // Initialize theme
    initTheme();
    
    // Set up navigation with proper event handling
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const viewName = this.dataset.view;
            console.log('Navigation clicked:', viewName);
            switchView(viewName);
            return false;
        });
    });
    
    // Initialize Morning Pages functionality
    const startBtn = document.getElementById('start-timer');
    const pauseBtn = document.getElementById('pause-timer');
    const resetBtn = document.getElementById('reset-timer');
    const textarea = document.getElementById('morning-pages-text');
    
    if (startBtn) startBtn.addEventListener('click', startTimer);
    if (pauseBtn) pauseBtn.addEventListener('click', pauseTimer);
    if (resetBtn) resetBtn.addEventListener('click', resetTimer);
    if (textarea) textarea.addEventListener('input', updateWordCount);
    
    // Initialize Idea Capture functionality
    const saveIdeaBtn = document.getElementById('save-idea');
    if (saveIdeaBtn) saveIdeaBtn.addEventListener('click', saveIdea);
    
    // Initialize Connection Practice functionality
    const getIdeasBtn = document.getElementById('get-random-ideas');
    const nextQuestionBtn = document.getElementById('next-question');
    const saveConnectionBtn = document.getElementById('save-connection');
    
    if (getIdeasBtn) getIdeasBtn.addEventListener('click', getRandomIdeas);
    if (nextQuestionBtn) nextQuestionBtn.addEventListener('click', nextQuestion);
    if (saveConnectionBtn) saveConnectionBtn.addEventListener('click', saveConnection);
    
    // Initialize Zettelkasten functionality
    const saveZettelBtn = document.getElementById('save-zettel');
    const searchInput = document.getElementById('search-zettels');
    
    if (saveZettelBtn) saveZettelBtn.addEventListener('click', saveZettel);
    if (searchInput) searchInput.addEventListener('input', searchZettels);
    
    // Initialize Spaced Repetition functionality
    const startQuizBtn = document.getElementById('start-quiz');
    const showAnswerBtn = document.getElementById('show-answer');
    const difficultyBtns = document.querySelectorAll('.difficulty-btn');
    
    if (startQuizBtn) startQuizBtn.addEventListener('click', startQuiz);
    if (showAnswerBtn) showAnswerBtn.addEventListener('click', showAnswer);
    difficultyBtns.forEach(btn => {
        btn.addEventListener('click', handleDifficulty);
    });
    
    // Set up home page start button
    document.addEventListener('click', function(e) {
        if (e.target && e.target.textContent === 'Bắt đầu với Morning Pages') {
            e.preventDefault();
            switchView('morning-pages');
        }
    });
    
    console.log('Application initialized successfully');
    
    // Initialize first view
    switchView('home');
}

// Global functions for onclick handlers
window.switchView = switchView;
window.highlightZettel = highlightZettel;

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', initApp);