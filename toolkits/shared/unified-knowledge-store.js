/**
 * Unified Data Management Layer for All Toolkits
 * Provides centralized knowledge management, progress tracking, and cross-toolkit integration
 */

class UnifiedKnowledgeStore {
    constructor() {
        this.knowledgeIndex = new Map();
        this.crossReferences = new Map();
        this.progressTracker = new Map();
        this.searchIndex = new Map();
        this.userPreferences = new Map();
        this.initialized = false;
        
        this.initializeStore();
    }

    async initializeStore() {
        try {
            // Load existing data from localStorage
            await this.loadStoredData();
            
            // Build cross-references between toolkits
            this.buildCrossReferences();
            
            // Initialize search index
            this.buildSearchIndex();
            
            this.initialized = true;
            this.notifyInitialization();
            
        } catch (error) {
            console.error('Failed to initialize unified knowledge store:', error);
        }
    }

    // Knowledge Management
    registerToolkit(toolkitId, metadata) {
        this.knowledgeIndex.set(toolkitId, {
            id: toolkitId,
            metadata,
            content: new Map(),
            lastAccessed: new Date(),
            accessCount: 0
        });
        
        this.saveToStorage();
    }

    addKnowledgeItem(toolkitId, itemId, content) {
        const toolkit = this.knowledgeIndex.get(toolkitId);
        if (toolkit) {
            toolkit.content.set(itemId, {
                id: itemId,
                content,
                createdAt: new Date(),
                updatedAt: new Date(),
                tags: this.extractTags(content),
                relatedItems: new Set()
            });
            
            this.updateSearchIndex(toolkitId, itemId, content);
            this.saveToStorage();
        }
    }

    // Cross-References and Knowledge Graph
    buildCrossReferences() {
        const knowledgeMappings = {
            'algorithm-thinking': {
                'four-step': ['problem-solving', 'computational-thinking', 'systems-thinking'],
                '5w1h': ['problem-analysis', 'critical-thinking'],
                'polya': ['mathematical-thinking', 'problem-solving'],
                'computational-thinking': ['abstraction', 'pattern-recognition', 'decomposition']
            },
            'software-engineer-handbook': {
                'clean-architecture': ['architecture-patterns', 'design-patterns', 'domain-driven-design'],
                'microservices': ['distributed-systems', 'system-design', 'architecture-patterns'],
                'javascript': ['programming-languages', 'web-development'],
                'python': ['programming-languages', 'machine-learning', 'data-science']
            },
            'software-architecture-explorer': {
                'design-patterns': ['clean-architecture', 'enterprise-patterns'],
                'domain-driven-design': ['clean-architecture', 'strategic-design'],
                'microservices': ['system-design', 'distributed-systems']
            },
            'thinking-os-toolkit': {
                'meta-principles': ['systems-thinking', 'first-principles'],
                'cognitive-architecture': ['computational-thinking', 'problem-solving'],
                'universal-process': ['problem-solving', 'decision-making']
            }
        };

        for (const [toolkitId, mappings] of Object.entries(knowledgeMappings)) {
            for (const [conceptId, relatedConcepts] of Object.entries(mappings)) {
                const key = `${toolkitId}:${conceptId}`;
                this.crossReferences.set(key, {
                    sourceToolkit: toolkitId,
                    sourceConcept: conceptId,
                    relatedConcepts: relatedConcepts.map(concept => ({
                        concept,
                        strength: this.calculateRelationStrength(conceptId, concept),
                        targetToolkits: this.findToolkitsForConcept(concept)
                    }))
                });
            }
        }
    }

    getRelatedConcepts(toolkitId, conceptId) {
        const key = `${toolkitId}:${conceptId}`;
        return this.crossReferences.get(key) || { relatedConcepts: [] };
    }

    // Progress Tracking
    trackProgress(toolkitId, activityType, activityData) {
        const userKey = 'default-user'; // In future, use actual user ID
        const progressKey = `${userKey}:${toolkitId}`;
        
        if (!this.progressTracker.has(progressKey)) {
            this.progressTracker.set(progressKey, {
                userId: userKey,
                toolkitId,
                activitiesCompleted: new Set(),
                conceptsMastered: new Set(),
                timeSpent: 0,
                lastActivity: null,
                streak: 0,
                achievements: new Set()
            });
        }

        const progress = this.progressTracker.get(progressKey);
        
        // Update progress based on activity type
        switch (activityType) {
            case 'concept-viewed':
                progress.activitiesCompleted.add(activityData.conceptId);
                this.updateLearningStreak(progressKey);
                break;
            case 'problem-solved':
                progress.conceptsMastered.add(activityData.conceptId);
                this.checkAchievements(progressKey, 'problem-solved');
                break;
            case 'time-spent':
                progress.timeSpent += activityData.duration;
                break;
        }

        progress.lastActivity = {
            type: activityType,
            data: activityData,
            timestamp: new Date()
        };

        this.saveToStorage();
        this.notifyProgressUpdate(progressKey);
    }

    getProgress(toolkitId, userId = 'default-user') {
        const progressKey = `${userId}:${toolkitId}`;
        return this.progressTracker.get(progressKey) || this.createEmptyProgress(userId, toolkitId);
    }

    getOverallProgress(userId = 'default-user') {
        const allProgress = [];
        for (const [key, progress] of this.progressTracker.entries()) {
            if (key.startsWith(`${userId}:`)) {
                allProgress.push(progress);
            }
        }
        
        return {
            totalActivities: allProgress.reduce((sum, p) => sum + p.activitiesCompleted.size, 0),
            totalConcepts: allProgress.reduce((sum, p) => sum + p.conceptsMastered.size, 0),
            totalTimeSpent: allProgress.reduce((sum, p) => sum + p.timeSpent, 0),
            currentStreak: Math.max(...allProgress.map(p => p.streak), 0),
            achievements: new Set([...allProgress.flatMap(p => [...p.achievements])])
        };
    }

    // Search and Discovery
    buildSearchIndex() {
        this.searchIndex.clear();
        
        for (const [toolkitId, toolkit] of this.knowledgeIndex.entries()) {
            for (const [itemId, item] of toolkit.content.entries()) {
                const searchableText = this.extractSearchableText(item.content);
                const keywords = this.extractKeywords(searchableText);
                
                this.searchIndex.set(`${toolkitId}:${itemId}`, {
                    toolkitId,
                    itemId,
                    title: item.content.title || itemId,
                    description: item.content.description || '',
                    keywords,
                    searchableText,
                    popularity: toolkit.accessCount,
                    lastAccessed: toolkit.lastAccessed
                });
            }
        }
    }

    globalSearch(query, options = {}) {
        const {
            toolkits = null,
            limit = 20,
            includeRelated = true
        } = options;

        const results = [];
        const queryLower = query.toLowerCase();

        for (const [key, item] of this.searchIndex.entries()) {
            if (toolkits && !toolkits.includes(item.toolkitId)) {
                continue;
            }

            const relevanceScore = this.calculateRelevance(queryLower, item);
            if (relevanceScore > 0) {
                results.push({
                    ...item,
                    relevanceScore,
                    relatedConcepts: includeRelated ? 
                        this.getRelatedConcepts(item.toolkitId, item.itemId).relatedConcepts : []
                });
            }
        }

        return results
            .sort((a, b) => b.relevanceScore - a.relevanceScore)
            .slice(0, limit);
    }

    // User Preferences and Personalization
    setUserPreference(key, value, userId = 'default-user') {
        const userKey = `${userId}:${key}`;
        this.userPreferences.set(userKey, {
            key,
            value,
            updatedAt: new Date()
        });
        this.saveToStorage();
    }

    getUserPreference(key, defaultValue = null, userId = 'default-user') {
        const userKey = `${userId}:${key}`;
        const preference = this.userPreferences.get(userKey);
        return preference ? preference.value : defaultValue;
    }

    // Learning Path Recommendations
    getRecommendedLearningPath(toolkitId, currentConcept) {
        const progress = this.getProgress(toolkitId);
        const relatedConcepts = this.getRelatedConcepts(toolkitId, currentConcept);
        
        // Calculate next best concepts to learn
        const recommendations = relatedConcepts.relatedConcepts
            .filter(concept => !progress.conceptsMastered.has(concept.concept))
            .sort((a, b) => b.strength - a.strength)
            .slice(0, 5);

        return {
            currentConcept,
            nextRecommendations: recommendations,
            prerequisitesMissing: this.findMissingPrerequisites(toolkitId, currentConcept),
            estimatedTimeToComplete: this.estimateCompletionTime(recommendations)
        };
    }

    // Event System for Cross-Toolkit Communication
    addEventListener(eventType, callback) {
        if (!this.eventListeners) {
            this.eventListeners = new Map();
        }
        
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, new Set());
        }
        
        this.eventListeners.get(eventType).add(callback);
    }

    removeEventListener(eventType, callback) {
        if (this.eventListeners && this.eventListeners.has(eventType)) {
            this.eventListeners.get(eventType).delete(callback);
        }
    }

    dispatchEvent(eventType, data) {
        if (this.eventListeners && this.eventListeners.has(eventType)) {
            for (const callback of this.eventListeners.get(eventType)) {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${eventType}:`, error);
                }
            }
        }
    }

    // Utility Methods
    calculateRelationStrength(concept1, concept2) {
        // Simple semantic similarity - in production, use more sophisticated NLP
        const sharedTerms = this.findSharedTerms(concept1, concept2);
        return Math.min(sharedTerms.length * 0.2, 1.0);
    }

    findToolkitsForConcept(concept) {
        const toolkits = [];
        for (const [toolkitId, toolkit] of this.knowledgeIndex.entries()) {
            for (const [itemId, item] of toolkit.content.entries()) {
                if (this.conceptMatches(concept, item.content)) {
                    toolkits.push(toolkitId);
                    break;
                }
            }
        }
        return toolkits;
    }

    extractTags(content) {
        // Extract meaningful tags from content
        const text = JSON.stringify(content).toLowerCase();
        const commonTags = [
            'algorithm', 'data-structure', 'problem-solving', 'design-pattern',
            'architecture', 'programming', 'thinking', 'framework', 'methodology'
        ];
        
        return commonTags.filter(tag => text.includes(tag));
    }

    extractSearchableText(content) {
        if (typeof content === 'string') return content;
        
        const extractText = (obj) => {
            let text = '';
            for (const [key, value] of Object.entries(obj)) {
                if (typeof value === 'string') {
                    text += value + ' ';
                } else if (typeof value === 'object' && value !== null) {
                    text += extractText(value) + ' ';
                }
            }
            return text;
        };
        
        return extractText(content).trim();
    }

    calculateRelevance(query, item) {
        let score = 0;
        
        // Title match (highest weight)
        if (item.title.toLowerCase().includes(query)) {
            score += 3;
        }
        
        // Keyword match
        for (const keyword of item.keywords) {
            if (keyword.includes(query)) {
                score += 2;
            }
        }
        
        // Description match
        if (item.description.toLowerCase().includes(query)) {
            score += 1;
        }
        
        // Full text match (lowest weight)
        if (item.searchableText.toLowerCase().includes(query)) {
            score += 0.5;
        }
        
        // Boost by popularity
        score *= (1 + Math.log(item.popularity + 1) * 0.1);
        
        return score;
    }

    // Storage Management
    async loadStoredData() {
        try {
            const stored = localStorage.getItem('unifiedKnowledgeStore');
            if (stored) {
                const data = JSON.parse(stored);
                
                // Restore Maps from arrays
                this.progressTracker = new Map(data.progressTracker || []);
                this.userPreferences = new Map(data.userPreferences || []);
                
                // Restore Sets within progress data
                for (const [key, progress] of this.progressTracker.entries()) {
                    progress.activitiesCompleted = new Set(progress.activitiesCompleted || []);
                    progress.conceptsMastered = new Set(progress.conceptsMastered || []);
                    progress.achievements = new Set(progress.achievements || []);
                }
            }
        } catch (error) {
            console.error('Failed to load stored data:', error);
        }
    }

    saveToStorage() {
        try {
            // Convert Maps and Sets to arrays for JSON serialization
            const progressData = [];
            for (const [key, progress] of this.progressTracker.entries()) {
                progressData.push([key, {
                    ...progress,
                    activitiesCompleted: [...progress.activitiesCompleted],
                    conceptsMastered: [...progress.conceptsMastered],
                    achievements: [...progress.achievements]
                }]);
            }

            const data = {
                progressTracker: progressData,
                userPreferences: [...this.userPreferences.entries()],
                lastSaved: new Date().toISOString()
            };

            localStorage.setItem('unifiedKnowledgeStore', JSON.stringify(data));
        } catch (error) {
            console.error('Failed to save data to storage:', error);
        }
    }

    // Notification Methods
    notifyInitialization() {
        this.dispatchEvent('store-initialized', { timestamp: new Date() });
    }

    notifyProgressUpdate(progressKey) {
        const progress = this.progressTracker.get(progressKey);
        this.dispatchEvent('progress-updated', { progressKey, progress });
    }

    dispatchEvent(eventType, data) {
        try {
            const event = new CustomEvent(eventType, { detail: data });
            window.dispatchEvent(event);
        } catch (error) {
            console.warn('Failed to dispatch event:', eventType, error);
        }
    }

    // Missing utility methods
    calculateRelationStrength(concept1, concept2) {
        // Simple implementation based on string similarity
        const similarity = this.stringSimilarity(concept1, concept2);
        return Math.max(0.1, similarity);
    }

    stringSimilarity(str1, str2) {
        const a = str1.toLowerCase();
        const b = str2.toLowerCase();
        const maxLen = Math.max(a.length, b.length);
        const distance = this.levenshteinDistance(a, b);
        return 1 - distance / maxLen;
    }

    levenshteinDistance(str1, str2) {
        const matrix = [];
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        return matrix[str2.length][str1.length];
    }

    findToolkitsForConcept(concept) {
        const toolkits = [];
        for (const [toolkitId, toolkit] of this.knowledgeIndex.entries()) {
            for (const [itemId, item] of toolkit.content.entries()) {
                if (itemId.includes(concept) || 
                    (item.content.title && item.content.title.toLowerCase().includes(concept.toLowerCase()))) {
                    toolkits.push(toolkitId);
                    break;
                }
            }
        }
        return toolkits;
    }

    updateLearningStreak(progressKey) {
        const progress = this.progressTracker.get(progressKey);
        if (progress) {
            const today = new Date().toDateString();
            const lastActivity = progress.lastActivity?.timestamp?.toDateString();
            
            if (lastActivity === today) {
                // Same day, maintain streak
                return;
            } else if (this.isConsecutiveDay(lastActivity, today)) {
                progress.streak += 1;
            } else {
                progress.streak = 1;
            }
        }
    }

    isConsecutiveDay(lastDate, currentDate) {
        if (!lastDate) return false;
        const last = new Date(lastDate);
        const current = new Date(currentDate);
        const dayDiff = (current - last) / (1000 * 60 * 60 * 24);
        return dayDiff === 1;
    }

    checkAchievements(progressKey, activityType) {
        const progress = this.progressTracker.get(progressKey);
        if (!progress) return;

        const achievements = [];
        
        // Check various achievement conditions
        if (progress.conceptsMastered.size >= 10 && !progress.achievements.has('concepts-10')) {
            achievements.push('concepts-10');
        }
        if (progress.streak >= 7 && !progress.achievements.has('streak-7')) {
            achievements.push('streak-7');
        }
        
        achievements.forEach(achievement => progress.achievements.add(achievement));
        
        if (achievements.length > 0) {
            this.dispatchEvent('achievements-unlocked', { achievements, progressKey });
        }
    }

    createEmptyProgress(userId, toolkitId) {
        return {
            userId,
            toolkitId,
            activitiesCompleted: new Set(),
            conceptsMastered: new Set(),
            timeSpent: 0,
            lastActivity: null,
            streak: 0,
            achievements: new Set()
        };
    }

    extractTags(content) {
        // Simple tag extraction
        const tags = [];
        if (content.keywords) {
            tags.push(...content.keywords);
        }
        if (content.category) {
            tags.push(content.category);
        }
        return tags;
    }

    updateSearchIndex(toolkitId, itemId, content) {
        const searchableText = this.extractSearchableText(content);
        const keywords = this.extractKeywords(searchableText);
        
        this.searchIndex.set(`${toolkitId}:${itemId}`, {
            toolkitId,
            itemId,
            title: content.title || itemId,
            description: content.description || '',
            keywords,
            searchableText,
            popularity: 0,
            lastAccessed: new Date()
        });
    }

    extractSearchableText(content) {
        const parts = [];
        if (content.title) parts.push(content.title);
        if (content.description) parts.push(content.description);
        if (content.body) parts.push(content.body);
        return parts.join(' ').toLowerCase();
    }

    extractKeywords(text) {
        // Simple keyword extraction - split by spaces and filter common words
        const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
        return text.split(/\s+/)
            .filter(word => word.length > 2 && !commonWords.has(word.toLowerCase()))
            .slice(0, 10); // Limit to 10 keywords
    }

    calculateRelevance(query, item) {
        let score = 0;
        const queryWords = query.split(/\s+/);
        
        queryWords.forEach(word => {
            if (item.title.toLowerCase().includes(word)) score += 3;
            if (item.description.toLowerCase().includes(word)) score += 2;
            if (item.searchableText.includes(word)) score += 1;
            if (item.keywords.some(k => k.toLowerCase().includes(word))) score += 2;
        });
        
        return score;
    }

    // Public API
    isInitialized() {
        return this.initialized;
    }

    getKnowledgeOverview() {
        return {
            totalToolkits: this.knowledgeIndex.size,
            totalConcepts: Array.from(this.knowledgeIndex.values())
                .reduce((sum, toolkit) => sum + toolkit.content.size, 0),
            crossReferences: this.crossReferences.size,
            searchableItems: this.searchIndex.size
        };
    }
}

// Global instance
window.UnifiedKnowledgeStore = UnifiedKnowledgeStore;

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
    window.unifiedKnowledgeStore = new UnifiedKnowledgeStore();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedKnowledgeStore;
}