/**
 * Performance Optimization Module
 * Implements lazy loading, code splitting, and performance monitoring for all toolkits
 */

class PerformanceOptimizer {
    constructor() {
        this.loadedModules = new Map();
        this.pendingLoads = new Map();
        this.performanceMetrics = new Map();
        this.resourceCache = new Map();
        this.intersectionObserver = null;
        this.memoryMonitor = null;
        this.cacheManager = new Map();
        this.resourceQueue = [];
        this.optimizationLevel = 'balanced'; // 'aggressive', 'balanced', 'conservative'
        this.memoryThreshold = 100 * 1024 * 1024; // 100MB
        this.cleanupTimer = null;
        
        this.initializeOptimizer();
    }

    initializeOptimizer() {
        this.setupIntersectionObserver();
        this.setupPerformanceMonitoring();
        this.setupResourceCaching();
        this.preloadCriticalResources();
        this.setupMemoryMonitoring();
        this.setupAdvancedCaching();
        this.setupIdleOptimization();
        this.setupAutoCleanup();
    }

    // Lazy Loading Implementation
    async lazyLoadToolkit(toolkitId) {
        if (this.loadedModules.has(toolkitId)) {
            return this.loadedModules.get(toolkitId);
        }

        if (this.pendingLoads.has(toolkitId)) {
            return this.pendingLoads.get(toolkitId);
        }

        const startTime = performance.now();
        
        const loadPromise = this.loadToolkitModule(toolkitId)
            .then(module => {
                const endTime = performance.now();
                this.recordMetric('toolkit-load-time', toolkitId, endTime - startTime);
                
                this.loadedModules.set(toolkitId, module);
                this.pendingLoads.delete(toolkitId);
                
                // Initialize the toolkit if it has an init method
                if (module.init && typeof module.init === 'function') {
                    module.init();
                }
                
                return module;
            })
            .catch(error => {
                this.pendingLoads.delete(toolkitId);
                console.error(`Failed to load toolkit ${toolkitId}:`, error);
                throw error;
            });

        this.pendingLoads.set(toolkitId, loadPromise);
        return loadPromise;
    }

    async loadToolkitModule(toolkitId) {
        const toolkitPaths = {
            'algorithm': '../algorithm-thinking-toolkit/app.js',
            'handbook': '../software-engineer-handbook/app.js',
            'architecture-explorer': '../software-architecture-explorer/app.js',
            'thinkingos': '../thinking-os-toolkit/app.js',
            'workspace-knowledge': '../workspace-knowledge-toolkit/app.js',
            'backend': '../backend-toolkit-handbook/app.js',
            'creative': '../creative-thinking-toolkit/app.js',
            'master-skill': '../master-any-skill-toolkit/app.js',
            'refactoring': '../software-refactoring-toolkit/app.js',
            'archaeology': '../software-archaeology-handbook/app.js',
            'archaeology-toolkit': '../software-archaeology-toolkit/app.js',
            'software-toolkit': '../software-toolkit-handbook/app.js',
            'golang': '../golang-toolkit/app.js',
            'linux-handbook': '../complete-linux-handbook/app.js',
            'frontend-handbook': '../frontend-handbook/app.js',
            'mobile-handbook': '../comprehensive-mobile-handbook/app.js',
            'mst': '../mst-toolkit/app.js'
        };

        const modulePath = toolkitPaths[toolkitId];
        if (!modulePath) {
            console.warn(`Unknown toolkit: ${toolkitId}, skipping load`);
            return {}; // Return empty object instead of throwing error
        }

        // Dynamic import with fallback
        try {
            return await import(modulePath);
        } catch (error) {
            // Fallback to script tag loading for environments that don't support dynamic imports
            console.warn(`Dynamic import failed for ${toolkitId}, trying fallback:`, error);
            return this.loadScriptFallback(modulePath);
        }
    }

    loadScriptFallback(scriptPath) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = scriptPath;
            script.async = true;
            
            script.onload = () => {
                // Assuming the script exports to window
                const moduleName = scriptPath.split('/').pop().replace('.js', '');
                resolve(window[moduleName] || {});
            };
            
            script.onerror = () => {
                console.warn(`Failed to load script: ${scriptPath}`);
                resolve({}); // Return empty object instead of rejecting
            };
            
            document.head.appendChild(script);
        });
    }

    // Code Splitting for Large Components
    async loadComponentChunk(toolkitId, componentName) {
        const chunkKey = `${toolkitId}:${componentName}`;
        
        if (this.loadedModules.has(chunkKey)) {
            return this.loadedModules.get(chunkKey);
        }

        const startTime = performance.now();
        
        try {
            const component = await this.loadComponentModule(toolkitId, componentName);
            const endTime = performance.now();
            
            this.recordMetric('component-load-time', chunkKey, endTime - startTime);
            this.loadedModules.set(chunkKey, component);
            
            return component;
        } catch (error) {
            console.error(`Failed to load component ${componentName} from ${toolkitId}:`, error);
            throw error;
        }
    }

    async loadComponentModule(toolkitId, componentName) {
        const componentPaths = {
            'workspace-knowledge-toolkit': {
                'search-component': '../workspace-knowledge-toolkit/components/search-component.js',
                'navigation-tree': '../workspace-knowledge-toolkit/components/navigation-tree.js',
                'content-viewer': '../workspace-knowledge-toolkit/components/content-viewer.js'
            },
            'software-architecture-explorer': {
                'pattern-viewer': '../software-architecture-explorer/components/pattern-viewer.js',
                'decision-framework': '../software-architecture-explorer/components/decision-framework.js'
            }
        };

        const toolkitComponents = componentPaths[toolkitId];
        if (!toolkitComponents || !toolkitComponents[componentName]) {
            throw new Error(`Component ${componentName} not found in toolkit ${toolkitId}`);
        }

        return await import(toolkitComponents[componentName]);
    }

    // Resource Caching and Prefetching
    setupResourceCaching() {
        // Cache frequently accessed resources
        this.cacheResource = (key, resource, ttl = 3600000) => { // 1 hour default TTL
            this.resourceCache.set(key, {
                resource,
                timestamp: Date.now(),
                ttl
            });
        };

        this.getCachedResource = (key) => {
            const cached = this.resourceCache.get(key);
            if (!cached) return null;
            
            if (Date.now() - cached.timestamp > cached.ttl) {
                this.resourceCache.delete(key);
                return null;
            }
            
            return cached.resource;
        };
    }

    preloadCriticalResources() {
        // Preload essential resources that are likely to be needed
        const criticalResources = [
            '../shared/unified-knowledge-store.js',
            '../algorithm-thinking-toolkit/app.js', // Most commonly used
            '../software-engineer-handbook/app.js'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = resource;
            document.head.appendChild(link);
        });
    }

    // Intersection Observer for Viewport-based Loading
    setupIntersectionObserver() {
        if (!('IntersectionObserver' in window)) {
            return; // Fallback for older browsers
        }

        this.intersectionObserver = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const toolkitId = element.dataset.toolkitId;
                        
                        if (toolkitId && !this.loadedModules.has(toolkitId)) {
                            this.lazyLoadToolkit(toolkitId);
                        }
                    }
                });
            },
            {
                rootMargin: '50px', // Start loading 50px before element enters viewport
                threshold: 0.1
            }
        );
    }

    observeElement(element) {
        if (this.intersectionObserver) {
            this.intersectionObserver.observe(element);
        }
    }

    unobserveElement(element) {
        if (this.intersectionObserver) {
            this.intersectionObserver.unobserve(element);
        }
    }

    // Performance Monitoring
    setupPerformanceMonitoring() {
        // Monitor Core Web Vitals
        this.observeWebVitals();
        
        // Monitor toolkit-specific metrics
        this.startMetricsCollection();
    }

    observeWebVitals() {
        // Largest Contentful Paint (LCP)
        if ('PerformanceObserver' in window) {
            const lcpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.recordMetric('lcp', 'global', lastEntry.startTime);
            });
            
            try {
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            } catch (e) {
                // Fallback for browsers that don't support LCP
            }

            // First Input Delay (FID)
            const fidObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordMetric('fid', 'global', entry.processingStart - entry.startTime);
                }
            });
            
            try {
                fidObserver.observe({ entryTypes: ['first-input'] });
            } catch (e) {
                // Fallback for browsers that don't support FID
            }
        }
    }

    startMetricsCollection() {
        // Collect metrics every 30 seconds
        setInterval(() => {
            this.collectMemoryMetrics();
            this.collectRenderingMetrics();
        }, 30000);
    }

    collectMemoryMetrics() {
        if ('memory' in performance) {
            const memory = performance.memory;
            this.recordMetric('memory-used', 'global', memory.usedJSHeapSize);
            this.recordMetric('memory-total', 'global', memory.totalJSHeapSize);
            this.recordMetric('memory-limit', 'global', memory.jsHeapSizeLimit);
        }
    }

    collectRenderingMetrics() {
        // Measure frame rate
        let lastTime = performance.now();
        let frames = 0;
        
        const measureFPS = () => {
            frames++;
            const currentTime = performance.now();
            
            if (currentTime >= lastTime + 1000) {
                this.recordMetric('fps', 'global', frames);
                frames = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
    }

    recordMetric(metricName, source, value) {
        const key = `${metricName}:${source}`;
        
        if (!this.performanceMetrics.has(key)) {
            this.performanceMetrics.set(key, []);
        }
        
        const metrics = this.performanceMetrics.get(key);
        metrics.push({
            value,
            timestamp: Date.now()
        });
        
        // Keep only last 100 measurements
        if (metrics.length > 100) {
            metrics.shift();
        }
    }

    getMetrics(metricName, source = null) {
        if (source) {
            return this.performanceMetrics.get(`${metricName}:${source}`) || [];
        }
        
        // Return all metrics matching the name
        const allMetrics = {};
        for (const [key, metrics] of this.performanceMetrics.entries()) {
            if (key.startsWith(`${metricName}:`)) {
                const sourceKey = key.substring(metricName.length + 1);
                allMetrics[sourceKey] = metrics;
            }
        }
        return allMetrics;
    }

    // Bundle Size Optimization
    getLoadedBundleInfo() {
        const info = {
            totalModules: this.loadedModules.size,
            modules: []
        };

        for (const [key, module] of this.loadedModules.entries()) {
            info.modules.push({
                id: key,
                size: this.estimateModuleSize(module),
                loadTime: this.getMetrics('toolkit-load-time', key)[0]?.value || 0
            });
        }

        return info;
    }

    estimateModuleSize(module) {
        // Rough estimation of module size
        try {
            return JSON.stringify(module).length;
        } catch (e) {
            return 0;
        }
    }

    // Public API for Optimization
    async optimizedLoad(toolkitId, priority = 'normal') {
        if (priority === 'high') {
            return this.lazyLoadToolkit(toolkitId);
        } else {
            // Defer loading until next tick
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve(this.lazyLoadToolkit(toolkitId));
                }, 0);
            });
        }
    }

    preloadToolkit(toolkitId) {
        // Start loading in background without waiting
        this.lazyLoadToolkit(toolkitId).catch(error => {
            console.warn(`Preload failed for ${toolkitId}:`, error);
        });
    }

    getPerformanceReport() {
        const report = {
            loadedModules: this.loadedModules.size,
            cachedResources: this.resourceCache.size,
            memoryUsage: this.getCurrentMemoryUsage(),
            optimizationLevel: this.optimizationLevel,
            metrics: {}
        };

        // Aggregate key metrics
        const keyMetrics = ['lcp', 'fid', 'fps', 'memory-used', 'toolkit-load-time'];
        for (const metric of keyMetrics) {
            const metricData = this.getMetrics(metric);
            if (Object.keys(metricData).length > 0) {
                report.metrics[metric] = metricData;
            }
        }

        return report;
    }

    // Enhanced Memory Monitoring
    setupMemoryMonitoring() {
        if (!performance.memory) {
            console.warn('Memory monitoring not available in this environment');
            return;
        }

        this.memoryMonitor = setInterval(() => {
            const memInfo = this.getCurrentMemoryUsage();
            
            this.recordMetric('memory-usage', 'heap-used', memInfo.usedJSHeapSize);
            this.recordMetric('memory-usage', 'heap-total', memInfo.totalJSHeapSize);
            this.recordMetric('memory-usage', 'heap-limit', memInfo.jsHeapSizeLimit);
            
            // Trigger cleanup if memory usage is high
            if (memInfo.usedJSHeapSize > this.memoryThreshold) {
                this.triggerMemoryCleanup();
            }
        }, 10000); // Check every 10 seconds
    }

    getCurrentMemoryUsage() {
        if (performance.memory) {
            return {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
                usagePercentage: (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100
            };
        }
        return { usagePercentage: 0 };
    }

    triggerMemoryCleanup() {
        console.log('Triggering memory cleanup due to high usage');
        
        // Clear old cache entries
        this.cleanupResourceCache();
        
        // Unload least recently used modules
        this.unloadLRUModules();
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
        
        // Emit memory pressure event
        window.dispatchEvent(new CustomEvent('memory-pressure', {
            detail: { threshold: this.memoryThreshold }
        }));
    }

    cleanupResourceCache() {
        const now = Date.now();
        let cleanedCount = 0;
        
        for (const [key, cached] of this.resourceCache.entries()) {
            if (now - cached.timestamp > cached.ttl) {
                this.resourceCache.delete(key);
                cleanedCount++;
            }
        }
        
        console.log(`Cleaned ${cleanedCount} expired cache entries`);
    }

    unloadLRUModules() {
        // Keep only the 5 most recently used modules
        const moduleEntries = Array.from(this.loadedModules.entries());
        if (moduleEntries.length > 5) {
            const toRemove = moduleEntries.slice(0, moduleEntries.length - 5);
            toRemove.forEach(([key]) => {
                this.loadedModules.delete(key);
            });
            console.log(`Unloaded ${toRemove.length} LRU modules`);
        }
    }

    // Advanced Caching System
    setupAdvancedCaching() {
        // Service Worker registration for advanced caching
        if ('serviceWorker' in navigator) {
            this.registerServiceWorker();
        }
        
        // IndexedDB for large data caching
        this.setupIndexedDBCache();
    }

    async registerServiceWorker() {
        try {
            const registration = await navigator.serviceWorker.register('/sw.js');
            console.log('Service Worker registered:', registration);
        } catch (error) {
            console.log('Service Worker registration failed:', error);
        }
    }

    setupIndexedDBCache() {
        // Setup IndexedDB for caching large resources
        if (!window.indexedDB) {
            console.warn('IndexedDB not available');
            return;
        }

        const request = indexedDB.open('ToolkitCache', 1);
        
        request.onerror = () => {
            console.warn('IndexedDB initialization failed');
        };
        
        request.onsuccess = (event) => {
            this.idbCache = event.target.result;
        };
        
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains('resources')) {
                db.createObjectStore('resources', { keyPath: 'key' });
            }
        };
    }

    // Idle Time Optimization
    setupIdleOptimization() {
        if ('requestIdleCallback' in window) {
            this.scheduleIdleTasks();
        } else {
            // Fallback for browsers without requestIdleCallback
            setTimeout(() => this.scheduleIdleTasks(), 1000);
        }
    }

    scheduleIdleTasks() {
        const idleTasks = [
            () => this.preloadNextLikelyResources(),
            () => this.optimizeImages(),
            () => this.compressCache(),
            () => this.validateCacheIntegrity()
        ];

        const runTask = () => {
            if (idleTasks.length === 0) return;
            
            const task = idleTasks.shift();
            const runIdleTask = (deadline) => {
                if (deadline.timeRemaining() > 0) {
                    try {
                        task();
                    } catch (error) {
                        console.warn('Idle task failed:', error);
                    }
                }
                
                if (idleTasks.length > 0) {
                    if ('requestIdleCallback' in window) {
                        requestIdleCallback(runIdleTask);
                    } else {
                        setTimeout(runTask, 1000);
                    }
                }
            };
            
            if ('requestIdleCallback' in window) {
                requestIdleCallback(runIdleTask);
            } else {
                setTimeout(() => runIdleTask({ timeRemaining: () => 5 }), 1000);
            }
        };
        
        runTask();
    }

    preloadNextLikelyResources() {
        // Predict and preload resources user is likely to access next
        const currentToolkit = this.getCurrentToolkit();
        const relatedToolkits = this.getRelatedToolkits(currentToolkit);
        
        relatedToolkits.forEach(toolkit => {
            if (!this.loadedModules.has(toolkit)) {
                this.preloadToolkit(toolkit);
            }
        });
    }

    getCurrentToolkit() {
        // Get currently active toolkit from URL or state
        const hash = window.location.hash;
        if (hash.includes('toolkit=')) {
            return hash.split('toolkit=')[1].split('&')[0];
        }
        return 'algorithm'; // default
    }

    getRelatedToolkits(toolkit) {
        const relationships = {
            'algorithm': ['handbook', 'thinkingos'],
            'handbook': ['architecture-explorer', 'backend'],
            'architecture-explorer': ['handbook', 'refactoring'],
            'thinkingos': ['algorithm', 'creative']
        };
        
        return relationships[toolkit] || [];
    }

    optimizeImages() {
        // Lazy load and optimize images
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            if (this.isInViewport(img)) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            }
        });
    }

    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= window.innerHeight &&
            rect.right <= window.innerWidth
        );
    }

    compressCache() {
        // Compress cached resources to save memory
        for (const [key, cached] of this.resourceCache.entries()) {
            if (typeof cached.resource === 'string' && cached.resource.length > 1000) {
                try {
                    // Simple compression using built-in methods
                    const compressed = this.compressString(cached.resource);
                    if (compressed.length < cached.resource.length) {
                        cached.resource = compressed;
                        cached.compressed = true;
                    }
                } catch (error) {
                    console.warn('Compression failed for cache key:', key);
                }
            }
        }
    }

    compressString(str) {
        // Simple compression using btoa/atob
        return btoa(unescape(encodeURIComponent(str)));
    }

    decompressString(str) {
        return decodeURIComponent(escape(atob(str)));
    }

    validateCacheIntegrity() {
        // Validate cache integrity and remove corrupted entries
        let corruptedCount = 0;
        
        for (const [key, cached] of this.resourceCache.entries()) {
            try {
                if (cached.compressed && typeof cached.resource === 'string') {
                    this.decompressString(cached.resource);
                }
            } catch (error) {
                console.warn(`Corrupted cache entry detected: ${key}`);
                this.resourceCache.delete(key);
                corruptedCount++;
            }
        }
        
        if (corruptedCount > 0) {
            console.log(`Removed ${corruptedCount} corrupted cache entries`);
        }
    }

    // Auto Cleanup System
    setupAutoCleanup() {
        // Clean up resources periodically
        this.cleanupTimer = setInterval(() => {
            this.performMaintenanceCleanup();
        }, 5 * 60 * 1000); // Every 5 minutes
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.performEmergencyCleanup();
        });
    }

    performMaintenanceCleanup() {
        this.cleanupResourceCache();
        
        // Clear old performance metrics
        const now = Date.now();
        const maxAge = 10 * 60 * 1000; // 10 minutes
        
        for (const [category, metrics] of this.performanceMetrics.entries()) {
            for (const [key, data] of Object.entries(metrics)) {
                if (Array.isArray(data)) {
                    const filtered = data.filter(entry => 
                        now - (entry.timestamp || 0) < maxAge
                    );
                    metrics[key] = filtered;
                }
            }
        }
    }

    performEmergencyCleanup() {
        // Quick cleanup before page unload
        this.resourceCache.clear();
        if (this.memoryMonitor) {
            clearInterval(this.memoryMonitor);
        }
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
    }

    // Enhanced API
    setOptimizationLevel(level) {
        this.optimizationLevel = level;
        
        switch (level) {
            case 'aggressive':
                this.memoryThreshold = 50 * 1024 * 1024; // 50MB
                break;
            case 'conservative':
                this.memoryThreshold = 200 * 1024 * 1024; // 200MB
                break;
            default: // balanced
                this.memoryThreshold = 100 * 1024 * 1024; // 100MB
        }
    }

    getOptimizationReport() {
        return {
            optimizationLevel: this.optimizationLevel,
            memoryThreshold: this.memoryThreshold,
            currentMemory: this.getCurrentMemoryUsage(),
            cacheStats: {
                resourceCache: this.resourceCache.size,
                loadedModules: this.loadedModules.size,
                pendingLoads: this.pendingLoads.size
            },
            performanceMetrics: this.getPerformanceReport()
        };
    }

    // Enhanced Cleanup
    destroy() {
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }
        
        if (this.memoryMonitor) {
            clearInterval(this.memoryMonitor);
        }
        
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        
        this.loadedModules.clear();
        this.pendingLoads.clear();
        this.performanceMetrics.clear();
        this.resourceCache.clear();
        this.cacheManager.clear();
        
        console.log('Performance optimizer destroyed and cleaned up');
    }
}

// Global instance
window.PerformanceOptimizer = PerformanceOptimizer;

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
    window.performanceOptimizer = new PerformanceOptimizer();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
}