/**
 * Content Validation System for All Toolkits
 * Provides comprehensive data validation, integrity checks, and content sanitization
 */

class ContentValidator {
    constructor(toolkitId = 'unknown') {
        this.toolkitId = toolkitId;
        this.validationRules = new Map();
        this.sanitizationRules = new Map();
        this.validationCache = new Map();
        this.validationHistory = [];
        this.initialized = false;
        
        this.initializeValidator();
    }

    initializeValidator() {
        try {
            // Setup default validation rules
            this.setupDefaultValidationRules();
            
            // Setup sanitization rules
            this.setupSanitizationRules();
            
            // Setup content integrity monitoring
            this.setupContentIntegrityMonitoring();
            
            // Setup real-time validation
            this.setupRealTimeValidation();
            
            this.initialized = true;
            console.log(`Content validator initialized for toolkit: ${this.toolkitId}`);
            
        } catch (error) {
            console.error('Failed to initialize content validator:', error);
        }
    }

    setupDefaultValidationRules() {
        // Common validation rules
        this.addValidationRule('email', {
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            message: 'Please enter a valid email address',
            sanitize: (value) => value.trim().toLowerCase()
        });

        this.addValidationRule('url', {
            pattern: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
            message: 'Please enter a valid URL',
            sanitize: (value) => value.trim()
        });

        this.addValidationRule('required', {
            validator: (value) => value !== null && value !== undefined && String(value).trim() !== '',
            message: 'This field is required',
            sanitize: (value) => String(value).trim()
        });

        this.addValidationRule('minLength', {
            validator: (value, params) => String(value).length >= params.min,
            message: (params) => `Minimum length is ${params.min} characters`,
            sanitize: (value) => String(value).trim()
        });

        this.addValidationRule('maxLength', {
            validator: (value, params) => String(value).length <= params.max,
            message: (params) => `Maximum length is ${params.max} characters`,
            sanitize: (value) => String(value).trim()
        });

        this.addValidationRule('numeric', {
            pattern: /^-?\d*\.?\d+$/,
            message: 'Please enter a valid number',
            sanitize: (value) => String(value).trim()
        });

        this.addValidationRule('integer', {
            pattern: /^-?\d+$/,
            message: 'Please enter a valid integer',
            sanitize: (value) => String(value).trim()
        });

        this.addValidationRule('alphanumeric', {
            pattern: /^[a-zA-Z0-9]+$/,
            message: 'Only letters and numbers are allowed',
            sanitize: (value) => String(value).trim()
        });

        this.addValidationRule('noScript', {
            validator: (value) => !/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(value),
            message: 'Script tags are not allowed',
            sanitize: (value) => this.removeScriptTags(value)
        });

        this.addValidationRule('safeHtml', {
            validator: (value) => this.isSafeHtml(value),
            message: 'Contains potentially unsafe HTML',
            sanitize: (value) => this.sanitizeHtml(value)
        });

        this.addValidationRule('jsonFormat', {
            validator: (value) => {
                try {
                    JSON.parse(value);
                    return true;
                } catch {
                    return false;
                }
            },
            message: 'Please enter valid JSON',
            sanitize: (value) => String(value).trim()
        });
    }

    setupSanitizationRules() {
        // HTML sanitization
        this.sanitizationRules.set('html', {
            allowedTags: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'a', 'img'],
            allowedAttributes: {
                'a': ['href', 'title'],
                'img': ['src', 'alt', 'title', 'width', 'height']
            },
            allowedProtocols: ['http', 'https', 'mailto']
        });

        // Code sanitization
        this.sanitizationRules.set('code', {
            allowedLanguages: ['javascript', 'typescript', 'python', 'go', 'rust', 'html', 'css', 'sql'],
            maxLength: 10000,
            removeComments: false
        });

        // Text sanitization
        this.sanitizationRules.set('text', {
            maxLength: 5000,
            removeLineBreaks: false,
            normalizeWhitespace: true
        });
    }

    setupContentIntegrityMonitoring() {
        // Monitor for content changes
        if (window.MutationObserver) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' || mutation.type === 'characterData') {
                        this.validateDynamicContent(mutation.target);
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
                characterData: true
            });
        }

        // Periodic integrity checks
        setInterval(() => {
            this.performIntegrityCheck();
        }, 60000); // Every minute
    }

    setupRealTimeValidation() {
        // Validate form inputs in real-time
        document.addEventListener('input', (event) => {
            if (event.target.matches('input, textarea, select')) {
                this.validateFormField(event.target);
            }
        });

        // Validate on form submission
        document.addEventListener('submit', (event) => {
            if (!this.validateForm(event.target)) {
                event.preventDefault();
            }
        });
    }

    // Validation methods
    addValidationRule(name, rule) {
        this.validationRules.set(name, rule);
    }

    validate(value, rules, params = {}) {
        const results = {
            isValid: true,
            errors: [],
            sanitizedValue: value,
            warnings: []
        };

        if (!Array.isArray(rules)) {
            rules = [rules];
        }

        for (const ruleName of rules) {
            const rule = this.validationRules.get(ruleName);
            if (!rule) {
                results.warnings.push(`Unknown validation rule: ${ruleName}`);
                continue;
            }

            // Sanitize value if sanitizer exists
            if (rule.sanitize) {
                results.sanitizedValue = rule.sanitize(results.sanitizedValue);
            }

            // Validate using pattern
            if (rule.pattern && !rule.pattern.test(results.sanitizedValue)) {
                results.isValid = false;
                results.errors.push(typeof rule.message === 'function' ? rule.message(params) : rule.message);
                continue;
            }

            // Validate using custom validator
            if (rule.validator && !rule.validator(results.sanitizedValue, params)) {
                results.isValid = false;
                results.errors.push(typeof rule.message === 'function' ? rule.message(params) : rule.message);
            }
        }

        // Cache validation result
        this.cacheValidationResult(value, rules, params, results);

        return results;
    }

    validateFormField(field) {
        const rules = this.getFieldValidationRules(field);
        const value = field.value;
        const params = this.getFieldValidationParams(field);

        const result = this.validate(value, rules, params);

        // Update field state
        this.updateFieldValidationState(field, result);

        return result;
    }

    validateForm(form) {
        let isValid = true;
        const results = new Map();

        const fields = form.querySelectorAll('input, textarea, select');
        fields.forEach(field => {
            const result = this.validateFormField(field);
            results.set(field.name || field.id, result);
            if (!result.isValid) {
                isValid = false;
            }
        });

        // Emit validation event
        form.dispatchEvent(new CustomEvent('form-validated', {
            detail: { isValid, results }
        }));

        return isValid;
    }

    validateDynamicContent(element) {
        // Validate dynamically added content
        if (element.nodeType === Node.TEXT_NODE) {
            const textContent = element.textContent;
            if (textContent && textContent.length > 0) {
                const result = this.validate(textContent, ['safeHtml', 'noScript']);
                if (!result.isValid) {
                    console.warn('Unsafe content detected:', result.errors);
                    element.textContent = result.sanitizedValue;
                }
            }
        } else if (element.nodeType === Node.ELEMENT_NODE) {
            // Validate element attributes
            this.validateElementAttributes(element);
            
            // Validate element content
            this.validateElementContent(element);
        }
    }

    validateElementAttributes(element) {
        const dangerousAttributes = ['onload', 'onerror', 'onclick', 'onmouseover', 'onfocus'];
        
        dangerousAttributes.forEach(attr => {
            if (element.hasAttribute(attr)) {
                console.warn(`Removing dangerous attribute ${attr} from element`);
                element.removeAttribute(attr);
            }
        });

        // Validate href attributes
        if (element.hasAttribute('href')) {
            const href = element.getAttribute('href');
            const result = this.validate(href, ['url']);
            if (!result.isValid && !href.startsWith('#')) {
                console.warn('Invalid href detected:', href);
                element.removeAttribute('href');
            }
        }

        // Validate src attributes
        if (element.hasAttribute('src')) {
            const src = element.getAttribute('src');
            if (!this.isAllowedProtocol(src)) {
                console.warn('Unsafe src protocol detected:', src);
                element.removeAttribute('src');
            }
        }
    }

    validateElementContent(element) {
        // Validate script elements
        if (element.tagName === 'SCRIPT') {
            console.warn('Script element detected and removed');
            element.remove();
            return;
        }

        // Validate style elements with dangerous CSS
        if (element.tagName === 'STYLE') {
            const content = element.textContent;
            if (this.containsDangerousCSS(content)) {
                console.warn('Dangerous CSS detected in style element');
                element.remove();
            }
        }
    }

    performIntegrityCheck() {
        const checkResults = {
            timestamp: new Date(),
            toolkitId: this.toolkitId,
            issues: [],
            warnings: []
        };

        // Check for script injections
        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach(script => {
            const src = script.getAttribute('src');
            if (!this.isAllowedScript(src)) {
                checkResults.issues.push(`Unauthorized script detected: ${src}`);
            }
        });

        // Check for suspicious iframes
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach(iframe => {
            const src = iframe.getAttribute('src');
            if (src && !this.isAllowedIframeSrc(src)) {
                checkResults.issues.push(`Suspicious iframe detected: ${src}`);
            }
        });

        // Check form action URLs
        const forms = document.querySelectorAll('form[action]');
        forms.forEach(form => {
            const action = form.getAttribute('action');
            if (action && !this.isAllowedFormAction(action)) {
                checkResults.warnings.push(`External form action detected: ${action}`);
            }
        });

        // Store integrity check results
        this.validationHistory.push(checkResults);

        // Keep only last 100 checks
        if (this.validationHistory.length > 100) {
            this.validationHistory.shift();
        }

        return checkResults;
    }

    // Sanitization methods
    sanitizeHtml(html) {
        const rules = this.sanitizationRules.get('html');
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        this.sanitizeElement(tempDiv, rules);
        return tempDiv.innerHTML;
    }

    sanitizeElement(element, rules) {
        // Remove disallowed tags
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_ELEMENT,
            null,
            false
        );

        const elementsToRemove = [];
        
        while (walker.nextNode()) {
            const el = walker.currentNode;
            if (!rules.allowedTags.includes(el.tagName.toLowerCase())) {
                elementsToRemove.push(el);
            } else {
                // Sanitize attributes
                this.sanitizeElementAttributes(el, rules);
            }
        }

        elementsToRemove.forEach(el => el.remove());
    }

    sanitizeElementAttributes(element, rules) {
        const tagName = element.tagName.toLowerCase();
        const allowedAttrs = rules.allowedAttributes[tagName] || [];

        // Remove disallowed attributes
        Array.from(element.attributes).forEach(attr => {
            if (!allowedAttrs.includes(attr.name)) {
                element.removeAttribute(attr.name);
            } else {
                // Validate attribute values
                this.validateAttributeValue(element, attr.name, attr.value, rules);
            }
        });
    }

    validateAttributeValue(element, attrName, attrValue, rules) {
        if (attrName === 'href' || attrName === 'src') {
            if (!this.isAllowedUrl(attrValue, rules.allowedProtocols)) {
                element.removeAttribute(attrName);
            }
        }
    }

    removeScriptTags(content) {
        return content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    }

    // Utility methods
    getFieldValidationRules(field) {
        const rules = [];
        
        // Required validation
        if (field.required) {
            rules.push('required');
        }

        // Type-based validation
        switch (field.type) {
            case 'email':
                rules.push('email');
                break;
            case 'url':
                rules.push('url');
                break;
            case 'number':
                rules.push('numeric');
                break;
        }

        // Pattern validation
        if (field.pattern) {
            this.addValidationRule(`pattern_${field.name}`, {
                pattern: new RegExp(field.pattern),
                message: 'Please match the required format'
            });
            rules.push(`pattern_${field.name}`);
        }

        // Length validation
        if (field.minLength || field.getAttribute('minlength')) {
            rules.push('minLength');
        }
        if (field.maxLength || field.getAttribute('maxlength')) {
            rules.push('maxLength');
        }

        // Security validation for text inputs
        if (field.type === 'text' || field.tagName === 'TEXTAREA') {
            rules.push('noScript', 'safeHtml');
        }

        return rules;
    }

    getFieldValidationParams(field) {
        return {
            min: field.minLength || field.getAttribute('minlength') || 0,
            max: field.maxLength || field.getAttribute('maxlength') || Infinity
        };
    }

    updateFieldValidationState(field, result) {
        // Remove existing validation classes
        field.classList.remove('valid', 'invalid');
        
        // Add appropriate class
        field.classList.add(result.isValid ? 'valid' : 'invalid');

        // Update or create error message
        this.updateFieldErrorMessage(field, result);

        // Update field value with sanitized version
        if (result.sanitizedValue !== field.value) {
            field.value = result.sanitizedValue;
        }
    }

    updateFieldErrorMessage(field, result) {
        const errorId = `${field.id || field.name}-error`;
        let errorElement = document.getElementById(errorId);

        if (result.isValid) {
            if (errorElement) {
                errorElement.remove();
            }
            field.removeAttribute('aria-describedby');
        } else {
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.id = errorId;
                errorElement.className = 'validation-error';
                errorElement.setAttribute('role', 'alert');
                field.parentNode.insertBefore(errorElement, field.nextSibling);
            }

            errorElement.textContent = result.errors.join(', ');
            field.setAttribute('aria-describedby', errorId);
        }
    }

    cacheValidationResult(value, rules, params, result) {
        const cacheKey = this.generateCacheKey(value, rules, params);
        this.validationCache.set(cacheKey, {
            result,
            timestamp: Date.now()
        });

        // Clean old cache entries
        this.cleanValidationCache();
    }

    generateCacheKey(value, rules, params) {
        return btoa(JSON.stringify({ value, rules, params }));
    }

    cleanValidationCache() {
        const maxAge = 5 * 60 * 1000; // 5 minutes
        const now = Date.now();

        for (const [key, entry] of this.validationCache.entries()) {
            if (now - entry.timestamp > maxAge) {
                this.validationCache.delete(key);
            }
        }
    }

    isSafeHtml(html) {
        const dangerousPatterns = [
            /<script/i,
            /javascript:/i,
            /on\w+\s*=/i,
            /<iframe/i,
            /<object/i,
            /<embed/i,
            /<form/i
        ];

        return !dangerousPatterns.some(pattern => pattern.test(html));
    }

    containsDangerousCSS(css) {
        const dangerousPatterns = [
            /expression\s*\(/i,
            /javascript\s*:/i,
            /behavior\s*:/i,
            /@import/i,
            /binding\s*:/i
        ];

        return dangerousPatterns.some(pattern => pattern.test(css));
    }

    isAllowedProtocol(url) {
        const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:', 'data:'];
        try {
            const urlObj = new URL(url);
            return allowedProtocols.includes(urlObj.protocol);
        } catch {
            return false;
        }
    }

    isAllowedUrl(url, allowedProtocols) {
        try {
            const urlObj = new URL(url);
            return allowedProtocols.includes(urlObj.protocol.replace(':', ''));
        } catch {
            return false;
        }
    }

    isAllowedScript(src) {
        // Allow scripts from same origin and trusted CDNs
        const trustedDomains = [
            window.location.origin,
            'https://cdnjs.cloudflare.com',
            'https://unpkg.com',
            'https://cdn.jsdelivr.net'
        ];

        return trustedDomains.some(domain => src.startsWith(domain));
    }

    isAllowedIframeSrc(src) {
        // Allow iframes from same origin and trusted domains
        const trustedDomains = [
            window.location.origin,
            'https://www.youtube.com',
            'https://codepen.io'
        ];

        return trustedDomains.some(domain => src.startsWith(domain));
    }

    isAllowedFormAction(action) {
        // Allow forms posting to same origin or trusted endpoints
        return action.startsWith(window.location.origin) || action.startsWith('/');
    }

    // Public API
    getValidationHistory() {
        return this.validationHistory;
    }

    getValidationCache() {
        return Array.from(this.validationCache.entries());
    }

    clearValidationCache() {
        this.validationCache.clear();
    }

    exportValidationReport() {
        return {
            toolkitId: this.toolkitId,
            validationRules: Array.from(this.validationRules.keys()),
            sanitizationRules: Array.from(this.sanitizationRules.keys()),
            validationHistory: this.validationHistory,
            cacheSize: this.validationCache.size,
            initialized: this.initialized,
            exportedAt: new Date().toISOString()
        };
    }
}

// Add CSS for validation styling
const validationStyles = document.createElement('style');
validationStyles.textContent = `
    /* Validation styling */
    .valid {
        border-color: var(--color-success, #28a745) !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }
    
    .invalid {
        border-color: var(--color-error, #dc3545) !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
    
    .validation-error {
        color: var(--color-error, #dc3545);
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }
    
    .validation-error:before {
        content: "⚠ ";
    }
`;
document.head.appendChild(validationStyles);

// Global instance and export
window.ContentValidator = ContentValidator;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ContentValidator;
}