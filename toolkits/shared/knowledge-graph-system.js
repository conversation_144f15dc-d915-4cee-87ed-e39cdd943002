/**
 * Knowledge Graph System for Cross-Toolkit Concept Relationships
 * Creates semantic connections between concepts across all toolkits
 */

class KnowledgeGraphSystem {
    constructor() {
        this.conceptNodes = new Map();
        this.relationships = new Map();
        this.conceptCategories = new Map();
        this.knowledgeStore = null;
        this.initialized = false;
        
        this.initializeKnowledgeGraph();
    }

    async initializeKnowledgeGraph() {
        try {
            // Connect to unified knowledge store
            await this.connectToKnowledgeStore();
            
            // Build concept nodes
            this.buildConceptNodes();
            
            // Create semantic relationships
            this.createSemanticRelationships();
            
            // Build category mappings
            this.buildCategoryMappings();
            
            this.initialized = true;
            console.log('Knowledge Graph System initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize Knowledge Graph System:', error);
        }
    }

    async connectToKnowledgeStore() {
        // Wait for knowledge store to be available
        let attempts = 0;
        while (!window.unifiedKnowledgeStore && attempts < 50) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
        
        if (window.unifiedKnowledgeStore) {
            this.knowledgeStore = window.unifiedKnowledgeStore;
        } else {
            console.warn('Unified Knowledge Store not available, using standalone graph');
        }
    }

    buildConceptNodes() {
        // Define all concepts across toolkits with their properties
        const conceptDefinitions = {
            // Algorithm Thinking Toolkit Concepts
            'algorithm-thinking:problem-solving': {
                id: 'algorithm-thinking:problem-solving',
                title: 'Problem Solving Methodology',
                toolkit: 'algorithm-thinking-toolkit',
                category: 'methodology',
                keywords: ['problem-solving', 'methodology', 'systematic', 'approach'],
                description: 'Systematic approaches to solving complex problems',
                difficulty: 'beginner',
                prerequisites: [],
                applications: ['software-development', 'research', 'business-analysis']
            },
            'algorithm-thinking:computational-thinking': {
                id: 'algorithm-thinking:computational-thinking',
                title: 'Computational Thinking',
                toolkit: 'algorithm-thinking-toolkit',
                category: 'thinking-framework',
                keywords: ['computational', 'decomposition', 'pattern', 'abstraction'],
                description: 'Breaking down problems using computational principles',
                difficulty: 'intermediate',
                prerequisites: ['algorithm-thinking:problem-solving'],
                applications: ['programming', 'system-design', 'data-analysis']
            },
            'algorithm-thinking:four-step-method': {
                id: 'algorithm-thinking:four-step-method',
                title: '4-Step Method',
                toolkit: 'algorithm-thinking-toolkit',
                category: 'framework',
                keywords: ['four-step', 'mountain', 'exploration', 'methodology'],
                description: 'Mountain exploration metaphor for systematic problem solving',
                difficulty: 'beginner',
                prerequisites: [],
                applications: ['project-management', 'research', 'troubleshooting']
            },

            // Software Engineering Concepts
            'software-engineering:clean-architecture': {
                id: 'software-engineering:clean-architecture',
                title: 'Clean Architecture',
                toolkit: 'software-engineer-handbook',
                category: 'architecture',
                keywords: ['clean', 'architecture', 'ddd', 'domain-driven', 'layered'],
                description: 'Maintainable software architecture principles',
                difficulty: 'advanced',
                prerequisites: ['software-engineering:design-patterns'],
                applications: ['enterprise-software', 'web-applications', 'microservices']
            },
            'software-engineering:design-patterns': {
                id: 'software-engineering:design-patterns',
                title: 'Design Patterns',
                toolkit: 'software-engineer-handbook',
                category: 'patterns',
                keywords: ['design', 'patterns', 'gof', 'reusable', 'solutions'],
                description: 'Reusable solutions to common programming problems',
                difficulty: 'intermediate',
                prerequisites: ['software-engineering:oop'],
                applications: ['software-design', 'code-reusability', 'maintainability']
            },
            'software-engineering:oop': {
                id: 'software-engineering:oop',
                title: 'Object-Oriented Programming',
                toolkit: 'software-engineer-handbook',
                category: 'paradigm',
                keywords: ['oop', 'objects', 'classes', 'inheritance', 'polymorphism'],
                description: 'Programming paradigm based on objects and classes',
                difficulty: 'intermediate',
                prerequisites: [],
                applications: ['software-development', 'system-modeling', 'code-organization']
            },

            // Architecture Explorer Concepts
            'architecture:enterprise-patterns': {
                id: 'architecture:enterprise-patterns',
                title: 'Enterprise Patterns',
                toolkit: 'software-architecture-explorer',
                category: 'patterns',
                keywords: ['enterprise', 'patterns', 'scalability', 'integration'],
                description: 'Patterns for large-scale enterprise applications',
                difficulty: 'advanced',
                prerequisites: ['software-engineering:design-patterns'],
                applications: ['enterprise-systems', 'distributed-architecture', 'integration']
            },
            'architecture:microservices': {
                id: 'architecture:microservices',
                title: 'Microservices Architecture',
                toolkit: 'software-architecture-explorer',
                category: 'architecture',
                keywords: ['microservices', 'distributed', 'scalability', 'independence'],
                description: 'Architectural approach using small, independent services',
                difficulty: 'expert',
                prerequisites: ['software-engineering:clean-architecture', 'architecture:enterprise-patterns'],
                applications: ['cloud-applications', 'scalable-systems', 'devops']
            },
            'architecture:domain-driven-design': {
                id: 'architecture:domain-driven-design',
                title: 'Domain-Driven Design',
                toolkit: 'software-architecture-explorer',
                category: 'methodology',
                keywords: ['ddd', 'domain', 'modeling', 'bounded-context'],
                description: 'Approach to software design focused on business domain',
                difficulty: 'advanced',
                prerequisites: ['software-engineering:clean-architecture'],
                applications: ['complex-business-logic', 'enterprise-modeling', 'team-organization']
            },

            // Thinking OS Concepts
            'thinking-os:meta-principles': {
                id: 'thinking-os:meta-principles',
                title: 'Meta Principles',
                toolkit: 'thinking-os-toolkit',
                category: 'philosophy',
                keywords: ['meta', 'principles', 'universal', 'thinking', 'philosophy'],
                description: 'Universal principles underlying all thinking processes',
                difficulty: 'expert',
                prerequisites: [],
                applications: ['decision-making', 'problem-solving', 'strategic-thinking']
            },
            'thinking-os:cognitive-architecture': {
                id: 'thinking-os:cognitive-architecture',
                title: 'Cognitive Architecture',
                toolkit: 'thinking-os-toolkit',
                category: 'framework',
                keywords: ['cognitive', 'architecture', 'mental', 'processing'],
                description: 'Framework for understanding mental processing',
                difficulty: 'advanced',
                prerequisites: ['thinking-os:meta-principles'],
                applications: ['learning', 'decision-making', 'problem-solving']
            },
            'thinking-os:universal-process': {
                id: 'thinking-os:universal-process',
                title: 'Universal 12-Step Process',
                toolkit: 'thinking-os-toolkit',
                category: 'methodology',
                keywords: ['universal', 'process', 'systematic', 'methodology'],
                description: 'Universal methodology for complex problem solving',
                difficulty: 'expert',
                prerequisites: ['thinking-os:meta-principles', 'algorithm-thinking:problem-solving'],
                applications: ['complex-problems', 'strategic-planning', 'research']
            },

            // Knowledge Management Concepts
            'knowledge:information-architecture': {
                id: 'knowledge:information-architecture',
                title: 'Information Architecture',
                toolkit: 'workspace-knowledge-toolkit',
                category: 'organization',
                keywords: ['information', 'architecture', 'organization', 'structure'],
                description: 'Organizing and structuring information for accessibility',
                difficulty: 'intermediate',
                prerequisites: [],
                applications: ['knowledge-management', 'system-design', 'user-experience']
            },
            'knowledge:semantic-search': {
                id: 'knowledge:semantic-search',
                title: 'Semantic Search',
                toolkit: 'workspace-knowledge-toolkit',
                category: 'technology',
                keywords: ['semantic', 'search', 'meaning', 'context'],
                description: 'Search technology that understands meaning and context',
                difficulty: 'advanced',
                prerequisites: ['knowledge:information-architecture'],
                applications: ['knowledge-discovery', 'content-management', 'ai-systems']
            }
        };

        // Build concept nodes
        for (const [conceptId, concept] of Object.entries(conceptDefinitions)) {
            this.conceptNodes.set(conceptId, {
                ...concept,
                connections: new Set(),
                relatedConcepts: new Map(),
                learningPath: [],
                applications: new Set(concept.applications)
            });
        }

        console.log(`Built ${this.conceptNodes.size} concept nodes`);
    }

    createSemanticRelationships() {
        // Define relationship types
        const relationshipTypes = {
            PREREQUISITE: 'prerequisite',
            BUILDS_UPON: 'builds_upon',
            APPLIES_TO: 'applies_to',
            SIMILAR_TO: 'similar_to',
            COMPLEMENTARY: 'complementary',
            IMPLEMENTS: 'implements',
            ENABLES: 'enables'
        };

        // Define specific relationships between concepts
        const relationships = [
            // Problem Solving Foundations
            {
                from: 'algorithm-thinking:problem-solving',
                to: 'thinking-os:universal-process',
                type: relationshipTypes.BUILDS_UPON,
                strength: 0.9,
                description: 'Universal process builds upon basic problem-solving principles'
            },
            {
                from: 'algorithm-thinking:computational-thinking',
                to: 'software-engineering:design-patterns',
                type: relationshipTypes.ENABLES,
                strength: 0.8,
                description: 'Computational thinking enables effective use of design patterns'
            },
            {
                from: 'algorithm-thinking:four-step-method',
                to: 'thinking-os:universal-process',
                type: relationshipTypes.SIMILAR_TO,
                strength: 0.7,
                description: 'Both are systematic methodologies for problem solving'
            },

            // Software Architecture Relationships
            {
                from: 'software-engineering:design-patterns',
                to: 'software-engineering:clean-architecture',
                type: relationshipTypes.PREREQUISITE,
                strength: 0.8,
                description: 'Design patterns are fundamental to clean architecture'
            },
            {
                from: 'software-engineering:clean-architecture',
                to: 'architecture:microservices',
                type: relationshipTypes.ENABLES,
                strength: 0.9,
                description: 'Clean architecture principles enable effective microservices design'
            },
            {
                from: 'architecture:domain-driven-design',
                to: 'architecture:microservices',
                type: relationshipTypes.COMPLEMENTARY,
                strength: 0.85,
                description: 'DDD and microservices work together for bounded contexts'
            },
            {
                from: 'software-engineering:clean-architecture',
                to: 'architecture:domain-driven-design',
                type: relationshipTypes.IMPLEMENTS,
                strength: 0.8,
                description: 'Clean architecture implements DDD principles'
            },

            // Thinking Frameworks
            {
                from: 'thinking-os:meta-principles',
                to: 'algorithm-thinking:problem-solving',
                type: relationshipTypes.APPLIES_TO,
                strength: 0.9,
                description: 'Meta principles apply to all problem-solving approaches'
            },
            {
                from: 'thinking-os:cognitive-architecture',
                to: 'algorithm-thinking:computational-thinking',
                type: relationshipTypes.SIMILAR_TO,
                strength: 0.7,
                description: 'Both model cognitive processing approaches'
            },

            // Knowledge Management
            {
                from: 'knowledge:information-architecture',
                to: 'software-engineering:clean-architecture',
                type: relationshipTypes.SIMILAR_TO,
                strength: 0.6,
                description: 'Both focus on organizing complex structures'
            },
            {
                from: 'knowledge:semantic-search',
                to: 'algorithm-thinking:computational-thinking',
                type: relationshipTypes.IMPLEMENTS,
                strength: 0.7,
                description: 'Semantic search implements computational thinking principles'
            },

            // Cross-Domain Applications
            {
                from: 'thinking-os:universal-process',
                to: 'architecture:enterprise-patterns',
                type: relationshipTypes.APPLIES_TO,
                strength: 0.8,
                description: 'Universal process applies to enterprise architecture decisions'
            },
            {
                from: 'algorithm-thinking:problem-solving',
                to: 'software-engineering:design-patterns',
                type: relationshipTypes.ENABLES,
                strength: 0.7,
                description: 'Problem-solving skills enable effective pattern selection'
            }
        ];

        // Create bidirectional relationships
        relationships.forEach(rel => {
            // Forward relationship
            if (!this.relationships.has(rel.from)) {
                this.relationships.set(rel.from, new Map());
            }
            this.relationships.get(rel.from).set(rel.to, {
                type: rel.type,
                strength: rel.strength,
                description: rel.description,
                direction: 'outgoing'
            });

            // Reverse relationship (for easier querying)
            if (!this.relationships.has(rel.to)) {
                this.relationships.set(rel.to, new Map());
            }
            this.relationships.get(rel.to).set(rel.from, {
                type: this.getReverseRelationType(rel.type),
                strength: rel.strength,
                description: this.getReverseDescription(rel.description),
                direction: 'incoming'
            });

            // Update concept connections
            const fromConcept = this.conceptNodes.get(rel.from);
            const toConcept = this.conceptNodes.get(rel.to);
            
            if (fromConcept && toConcept) {
                fromConcept.connections.add(rel.to);
                toConcept.connections.add(rel.from);
                
                fromConcept.relatedConcepts.set(rel.to, rel.strength);
                toConcept.relatedConcepts.set(rel.from, rel.strength);
            }
        });

        console.log(`Created ${relationships.length} semantic relationships`);
    }

    getReverseRelationType(type) {
        const reverseTypes = {
            'prerequisite': 'enables',
            'builds_upon': 'foundation_for',
            'applies_to': 'applied_by',
            'similar_to': 'similar_to',
            'complementary': 'complementary',
            'implements': 'implemented_by',
            'enables': 'enabled_by'
        };
        return reverseTypes[type] || type;
    }

    getReverseDescription(description) {
        // Simple reverse description logic
        return description.replace('enables', 'is enabled by')
                         .replace('builds upon', 'is foundation for')
                         .replace('applies to', 'is applied by');
    }

    buildCategoryMappings() {
        const categories = {
            'methodology': {
                concepts: [],
                description: 'Systematic approaches and frameworks',
                icon: '📋'
            },
            'architecture': {
                concepts: [],
                description: 'System and software architecture patterns',
                icon: '🏗️'
            },
            'patterns': {
                concepts: [],
                description: 'Reusable design and architectural patterns',
                icon: '🔄'
            },
            'thinking-framework': {
                concepts: [],
                description: 'Cognitive and thinking frameworks',
                icon: '🧠'
            },
            'philosophy': {
                concepts: [],
                description: 'Fundamental principles and philosophies',
                icon: '💭'
            },
            'technology': {
                concepts: [],
                description: 'Technical implementations and tools',
                icon: '⚙️'
            },
            'organization': {
                concepts: [],
                description: 'Information and knowledge organization',
                icon: '📚'
            }
        };

        // Categorize concepts
        for (const [conceptId, concept] of this.conceptNodes.entries()) {
            if (categories[concept.category]) {
                categories[concept.category].concepts.push(conceptId);
            }
        }

        this.conceptCategories = new Map(Object.entries(categories));
        console.log(`Built ${this.conceptCategories.size} concept categories`);
    }

    // Query Methods
    getRelatedConcepts(conceptId, options = {}) {
        const {
            maxResults = 10,
            minStrength = 0.5,
            includeTransitive = false,
            excludeToolkits = []
        } = options;

        const concept = this.conceptNodes.get(conceptId);
        if (!concept) return [];

        const related = [];
        const relationships = this.relationships.get(conceptId) || new Map();

        // Direct relationships
        for (const [relatedId, relationship] of relationships.entries()) {
            const relatedConcept = this.conceptNodes.get(relatedId);
            if (!relatedConcept) continue;
            
            if (excludeToolkits.includes(relatedConcept.toolkit)) continue;
            if (relationship.strength < minStrength) continue;

            related.push({
                concept: relatedConcept,
                relationship: relationship,
                distance: 1
            });
        }

        // Transitive relationships (2-hop)
        if (includeTransitive) {
            const visited = new Set([conceptId]);
            
            for (const directRelated of related) {
                if (visited.has(directRelated.concept.id)) continue;
                visited.add(directRelated.concept.id);
                
                const transitiveRels = this.relationships.get(directRelated.concept.id) || new Map();
                
                for (const [transitiveId, transitiveRel] of transitiveRels.entries()) {
                    if (visited.has(transitiveId)) continue;
                    
                    const transitiveConcept = this.conceptNodes.get(transitiveId);
                    if (!transitiveConcept) continue;
                    
                    if (excludeToolkits.includes(transitiveConcept.toolkit)) continue;
                    
                    const combinedStrength = directRelated.relationship.strength * transitiveRel.strength * 0.7;
                    if (combinedStrength < minStrength) continue;

                    related.push({
                        concept: transitiveConcept,
                        relationship: {
                            ...transitiveRel,
                            strength: combinedStrength,
                            description: `Related via ${directRelated.concept.title}`
                        },
                        distance: 2,
                        via: directRelated.concept.id
                    });
                }
            }
        }

        return related
            .sort((a, b) => b.relationship.strength - a.relationship.strength)
            .slice(0, maxResults);
    }

    getLearningPath(fromConceptId, toConceptId) {
        // Find the shortest learning path between two concepts
        const queue = [{ conceptId: fromConceptId, path: [fromConceptId], visited: new Set([fromConceptId]) }];
        const maxDepth = 5;

        while (queue.length > 0) {
            const { conceptId, path, visited } = queue.shift();
            
            if (path.length > maxDepth) continue;
            if (conceptId === toConceptId) {
                return this.buildLearningPathDetails(path);
            }

            const relationships = this.relationships.get(conceptId) || new Map();
            
            for (const [relatedId, relationship] of relationships.entries()) {
                if (visited.has(relatedId)) continue;
                
                // Prioritize prerequisite and builds_upon relationships for learning paths
                if (!['prerequisite', 'builds_upon', 'enables'].includes(relationship.type)) continue;
                
                queue.push({
                    conceptId: relatedId,
                    path: [...path, relatedId],
                    visited: new Set([...visited, relatedId])
                });
            }
        }

        return null; // No path found
    }

    buildLearningPathDetails(conceptIds) {
        return conceptIds.map((conceptId, index) => {
            const concept = this.conceptNodes.get(conceptId);
            const nextConceptId = conceptIds[index + 1];
            
            const pathStep = {
                concept: concept,
                order: index + 1,
                isComplete: false
            };

            if (nextConceptId) {
                const relationship = this.relationships.get(conceptId)?.get(nextConceptId);
                if (relationship) {
                    pathStep.nextStep = {
                        conceptId: nextConceptId,
                        relationship: relationship
                    };
                }
            }

            return pathStep;
        });
    }

    getConceptsByCategory(category) {
        const categoryData = this.conceptCategories.get(category);
        if (!categoryData) return [];

        return categoryData.concepts.map(conceptId => this.conceptNodes.get(conceptId))
                                   .filter(concept => concept != null);
    }

    searchConcepts(query, options = {}) {
        const {
            categories = null,
            toolkits = null,
            difficulty = null,
            limit = 20
        } = options;

        const normalizedQuery = query.toLowerCase();
        const results = [];

        for (const [conceptId, concept] of this.conceptNodes.entries()) {
            // Apply filters
            if (categories && !categories.includes(concept.category)) continue;
            if (toolkits && !toolkits.includes(concept.toolkit)) continue;
            if (difficulty && concept.difficulty !== difficulty) continue;

            // Calculate relevance score
            let score = 0;
            
            if (concept.title.toLowerCase().includes(normalizedQuery)) score += 10;
            if (concept.description.toLowerCase().includes(normalizedQuery)) score += 5;
            
            concept.keywords.forEach(keyword => {
                if (keyword.toLowerCase().includes(normalizedQuery)) score += 3;
            });

            if (score > 0) {
                results.push({
                    concept: concept,
                    relevanceScore: score
                });
            }
        }

        return results
            .sort((a, b) => b.relevanceScore - a.relevanceScore)
            .slice(0, limit)
            .map(result => result.concept);
    }

    // Analytics and Insights
    getKnowledgeGraphStatistics() {
        const stats = {
            totalConcepts: this.conceptNodes.size,
            totalRelationships: Array.from(this.relationships.values())
                .reduce((sum, relMap) => sum + relMap.size, 0) / 2, // Divide by 2 for bidirectional
            categories: this.conceptCategories.size,
            toolkits: new Set(Array.from(this.conceptNodes.values()).map(c => c.toolkit)).size,
            difficultyDistribution: {},
            categoryDistribution: {},
            avgConnectionsPerConcept: 0
        };

        // Calculate distributions
        let totalConnections = 0;
        for (const concept of this.conceptNodes.values()) {
            // Difficulty distribution
            stats.difficultyDistribution[concept.difficulty] = 
                (stats.difficultyDistribution[concept.difficulty] || 0) + 1;
            
            // Category distribution  
            stats.categoryDistribution[concept.category] = 
                (stats.categoryDistribution[concept.category] || 0) + 1;
            
            totalConnections += concept.connections.size;
        }

        stats.avgConnectionsPerConcept = totalConnections / this.conceptNodes.size;

        return stats;
    }

    exportKnowledgeGraph() {
        return {
            concepts: Array.from(this.conceptNodes.entries()),
            relationships: Array.from(this.relationships.entries()).map(([from, rels]) => ({
                from,
                relationships: Array.from(rels.entries())
            })),
            categories: Array.from(this.conceptCategories.entries()),
            statistics: this.getKnowledgeGraphStatistics(),
            exportedAt: new Date().toISOString()
        };
    }

    // Public API
    isInitialized() {
        return this.initialized;
    }

    getConcept(conceptId) {
        return this.conceptNodes.get(conceptId);
    }

    getAllConcepts() {
        return Array.from(this.conceptNodes.values());
    }

    getCategories() {
        return Array.from(this.conceptCategories.entries());
    }
}

// Global instance
window.KnowledgeGraphSystem = KnowledgeGraphSystem;

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
    window.knowledgeGraphSystem = new KnowledgeGraphSystem();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = KnowledgeGraphSystem;
}