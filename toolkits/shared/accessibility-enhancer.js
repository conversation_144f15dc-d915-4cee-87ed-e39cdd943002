/**
 * Accessibility Enhancement System for All Toolkits
 * Provides comprehensive accessibility features including screen reader support,
 * keyboard navigation, ARIA labels, and WCAG 2.1 compliance
 */

class AccessibilityEnhancer {
    constructor(toolkitId = 'unknown') {
        this.toolkitId = toolkitId;
        this.screenReaderEnabled = false;
        this.keyboardNavigationEnabled = true;
        this.highContrastMode = false;
        this.focusManagement = new Map();
        this.announcements = [];
        this.initialized = false;
        
        this.initializeAccessibility();
    }

    initializeAccessibility() {
        try {
            // Detect accessibility preferences
            this.detectAccessibilityPreferences();
            
            // Setup screen reader support
            this.setupScreenReaderSupport();
            
            // Enhance keyboard navigation
            this.enhanceKeyboardNavigation();
            
            // Add ARIA labels and roles
            this.enhanceAriaLabels();
            
            // Setup focus management
            this.setupFocusManagement();
            
            // Add accessibility shortcuts
            this.addAccessibilityShortcuts();
            
            this.initialized = true;
            this.announceToScreenReader(`${this.toolkitId} accessibility features enabled`);
            
        } catch (error) {
            console.error('Failed to initialize accessibility enhancements:', error);
        }
    }

    detectAccessibilityPreferences() {
        // Detect if user prefers reduced motion
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--animation-duration', '0.01ms');
            document.documentElement.setAttribute('data-reduced-motion', 'true');
        }

        // Detect high contrast preference
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            this.enableHighContrastMode();
        }

        // Detect if screen reader is likely being used
        this.screenReaderEnabled = this.detectScreenReader();
        
        // Store preferences
        this.storeAccessibilityPreferences();
    }

    detectScreenReader() {
        // Check for common screen reader indicators
        const indicators = [
            window.navigator.userAgent.includes('NVDA'),
            window.navigator.userAgent.includes('JAWS'),
            window.speechSynthesis && window.speechSynthesis.getVoices().length > 0,
            document.querySelector('[aria-live]') !== null
        ];
        
        return indicators.some(indicator => indicator);
    }

    setupScreenReaderSupport() {
        // Create live region for announcements
        this.createLiveRegion();
        
        // Enhance existing elements with screen reader support
        this.enhanceForScreenReaders();
        
        // Setup navigation landmarks
        this.addNavigationLandmarks();
    }

    createLiveRegion() {
        // Create polite live region for general announcements
        const politeLiveRegion = document.createElement('div');
        politeLiveRegion.id = `${this.toolkitId}-live-region-polite`;
        politeLiveRegion.setAttribute('aria-live', 'polite');
        politeLiveRegion.setAttribute('aria-atomic', 'true');
        politeLiveRegion.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(politeLiveRegion);

        // Create assertive live region for urgent announcements
        const assertiveLiveRegion = document.createElement('div');
        assertiveLiveRegion.id = `${this.toolkitId}-live-region-assertive`;
        assertiveLiveRegion.setAttribute('aria-live', 'assertive');
        assertiveLiveRegion.setAttribute('aria-atomic', 'true');
        assertiveLiveRegion.style.cssText = politeLiveRegion.style.cssText;
        document.body.appendChild(assertiveLiveRegion);
    }

    enhanceForScreenReaders() {
        // Add skip links
        this.addSkipLinks();
        
        // Enhance form labels
        this.enhanceFormLabels();
        
        // Add descriptions to interactive elements
        this.addElementDescriptions();
        
        // Enhance data tables
        this.enhanceDataTables();
    }

    addSkipLinks() {
        const skipLinksContainer = document.createElement('div');
        skipLinksContainer.className = 'skip-links';
        skipLinksContainer.setAttribute('role', 'navigation');
        skipLinksContainer.setAttribute('aria-label', 'Skip links');
        
        const skipLinks = [
            { href: '#main-content', text: 'Skip to main content' },
            { href: '#navigation', text: 'Skip to navigation' },
            { href: '#search', text: 'Skip to search' }
        ];
        
        skipLinks.forEach(link => {
            const skipLink = document.createElement('a');
            skipLink.href = link.href;
            skipLink.textContent = link.text;
            skipLink.className = 'skip-link';
            skipLink.style.cssText = `
                position: absolute;
                left: -10000px;
                top: auto;
                width: 1px;
                height: 1px;
                overflow: hidden;
                background: var(--color-primary, #007acc);
                color: white;
                padding: 8px 16px;
                text-decoration: none;
                border-radius: 4px;
                z-index: 10001;
            `;
            
            // Show on focus
            skipLink.addEventListener('focus', () => {
                skipLink.style.cssText = `
                    position: fixed;
                    top: 10px;
                    left: 10px;
                    width: auto;
                    height: auto;
                    overflow: visible;
                    background: var(--color-primary, #007acc);
                    color: white;
                    padding: 8px 16px;
                    text-decoration: none;
                    border-radius: 4px;
                    z-index: 10001;
                `;
            });
            
            skipLink.addEventListener('blur', () => {
                skipLink.style.cssText = `
                    position: absolute;
                    left: -10000px;
                    top: auto;
                    width: 1px;
                    height: 1px;
                    overflow: hidden;
                `;
            });
            
            skipLinksContainer.appendChild(skipLink);
        });
        
        document.body.insertBefore(skipLinksContainer, document.body.firstChild);
    }

    enhanceFormLabels() {
        // Enhance input labels
        document.querySelectorAll('input, select, textarea').forEach(input => {
            if (!input.getAttribute('aria-label') && !input.getAttribute('aria-labelledby')) {
                const label = document.querySelector(`label[for="${input.id}"]`) || 
                             input.closest('label') ||
                             input.previousElementSibling?.tagName === 'LABEL' ? input.previousElementSibling : null;
                
                if (label) {
                    input.setAttribute('aria-labelledby', label.id || this.generateId('label'));
                    if (!label.id) {
                        label.id = input.getAttribute('aria-labelledby');
                    }
                } else {
                    // Add generic label based on input type or placeholder
                    const placeholder = input.placeholder || input.type || 'input';
                    input.setAttribute('aria-label', placeholder);
                }
            }
            
            // Add required indication
            if (input.required && !input.getAttribute('aria-required')) {
                input.setAttribute('aria-required', 'true');
            }
        });
    }

    addElementDescriptions() {
        // Add descriptions to buttons without accessible names
        document.querySelectorAll('button').forEach(button => {
            if (!button.textContent.trim() && !button.getAttribute('aria-label') && !button.getAttribute('aria-labelledby')) {
                const icon = button.querySelector('[class*="icon"], i, svg');
                if (icon) {
                    button.setAttribute('aria-label', this.inferButtonPurpose(button, icon));
                }
            }
        });

        // Add descriptions to links
        document.querySelectorAll('a').forEach(link => {
            if (!link.textContent.trim() && !link.getAttribute('aria-label')) {
                const icon = link.querySelector('[class*="icon"], i, svg');
                if (icon) {
                    link.setAttribute('aria-label', this.inferLinkPurpose(link, icon));
                }
            }
        });
    }

    enhanceDataTables() {
        document.querySelectorAll('table').forEach(table => {
            // Add table role if missing
            if (!table.getAttribute('role')) {
                table.setAttribute('role', 'table');
            }
            
            // Add caption if missing
            if (!table.querySelector('caption') && !table.getAttribute('aria-label')) {
                const caption = document.createElement('caption');
                caption.textContent = 'Data table';
                caption.style.cssText = `
                    position: absolute;
                    left: -10000px;
                    width: 1px;
                    height: 1px;
                    overflow: hidden;
                `;
                table.insertBefore(caption, table.firstChild);
            }
            
            // Enhance table headers
            table.querySelectorAll('th').forEach(th => {
                if (!th.getAttribute('scope')) {
                    // Determine if it's a column or row header
                    const isColumnHeader = th.parentElement === table.querySelector('thead tr') ||
                                         th.cellIndex === 0;
                    th.setAttribute('scope', isColumnHeader ? 'col' : 'row');
                }
            });
        });
    }

    enhanceKeyboardNavigation() {
        // Add tab index management
        this.setupTabIndexManagement();
        
        // Add keyboard event handlers
        this.addKeyboardEventHandlers();
        
        // Enhance focus indicators
        this.enhanceFocusIndicators();
        
        // Setup roving tab index for complex widgets
        this.setupRovingTabIndex();
    }

    setupTabIndexManagement() {
        // Ensure interactive elements are keyboard accessible
        document.querySelectorAll('div[onclick], span[onclick]').forEach(element => {
            if (!element.getAttribute('tabindex')) {
                element.setAttribute('tabindex', '0');
                element.setAttribute('role', 'button');
            }
        });
        
        // Remove negative tab indexes from visible elements
        document.querySelectorAll('[tabindex="-1"]:not([aria-hidden="true"])').forEach(element => {
            if (this.isVisible(element)) {
                element.removeAttribute('tabindex');
            }
        });
    }

    addKeyboardEventHandlers() {
        document.addEventListener('keydown', (event) => {
            // Handle global keyboard shortcuts
            this.handleGlobalKeyboardShortcuts(event);
            
            // Handle space and enter for clickable elements
            if (event.key === ' ' || event.key === 'Enter') {
                const target = event.target;
                if (target.getAttribute('role') === 'button' || 
                    target.tagName === 'DIV' && target.hasAttribute('onclick')) {
                    event.preventDefault();
                    target.click();
                }
            }
            
            // Handle escape key
            if (event.key === 'Escape') {
                this.handleEscapeKey(event);
            }
        });
    }

    enhanceFocusIndicators() {
        // Add high-visibility focus styles
        const focusStyle = document.createElement('style');
        focusStyle.textContent = `
            /* Enhanced focus indicators */
            *:focus {
                outline: 2px solid var(--color-focus, #005fcc) !important;
                outline-offset: 2px !important;
            }
            
            .focus-visible {
                outline: 2px solid var(--color-focus, #005fcc) !important;
                outline-offset: 2px !important;
            }
            
            /* Skip link styles */
            .skip-link:focus {
                outline: 2px solid white !important;
                outline-offset: 2px !important;
            }
            
            /* High contrast mode styles */
            [data-high-contrast="true"] *:focus {
                outline: 3px solid yellow !important;
                outline-offset: 2px !important;
                background: black !important;
                color: yellow !important;
            }
        `;
        document.head.appendChild(focusStyle);
    }

    setupRovingTabIndex() {
        // Setup for tab panels and similar widgets
        document.querySelectorAll('[role="tablist"]').forEach(tablist => {
            this.setupTabListNavigation(tablist);
        });
        
        // Setup for menu navigation
        document.querySelectorAll('[role="menu"], [role="menubar"]').forEach(menu => {
            this.setupMenuNavigation(menu);
        });
    }

    setupTabListNavigation(tablist) {
        const tabs = tablist.querySelectorAll('[role="tab"]');
        let currentIndex = 0;
        
        tabs.forEach((tab, index) => {
            tab.setAttribute('tabindex', index === 0 ? '0' : '-1');
            
            tab.addEventListener('keydown', (event) => {
                let newIndex = currentIndex;
                
                switch (event.key) {
                    case 'ArrowLeft':
                    case 'ArrowUp':
                        newIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
                        break;
                    case 'ArrowRight':
                    case 'ArrowDown':
                        newIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
                        break;
                    case 'Home':
                        newIndex = 0;
                        break;
                    case 'End':
                        newIndex = tabs.length - 1;
                        break;
                    default:
                        return;
                }
                
                event.preventDefault();
                this.moveFocusToTab(tabs, currentIndex, newIndex);
                currentIndex = newIndex;
            });
        });
    }

    setupFocusManagement() {
        // Track focus history for restoration
        this.focusHistory = [];
        
        document.addEventListener('focusin', (event) => {
            this.focusHistory.push(event.target);
            if (this.focusHistory.length > 10) {
                this.focusHistory.shift();
            }
        });
        
        // Setup modal focus trap
        this.setupModalFocusTrap();
    }

    setupModalFocusTrap() {
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Tab') {
                const modal = document.querySelector('[role="dialog"]:not([hidden])');
                if (modal) {
                    this.trapFocusInModal(event, modal);
                }
            }
        });
    }

    trapFocusInModal(event, modal) {
        const focusableElements = modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];
        
        if (event.shiftKey) {
            if (document.activeElement === firstFocusable) {
                event.preventDefault();
                lastFocusable.focus();
            }
        } else {
            if (document.activeElement === lastFocusable) {
                event.preventDefault();
                firstFocusable.focus();
            }
        }
    }

    enhanceAriaLabels() {
        // Add missing ARIA labels to common elements
        this.labelNavigationElements();
        this.labelFormElements();
        this.labelInteractiveElements();
        this.addLandmarkRoles();
    }

    labelNavigationElements() {
        // Label navigation elements
        document.querySelectorAll('nav').forEach((nav, index) => {
            if (!nav.getAttribute('aria-label') && !nav.getAttribute('aria-labelledby')) {
                nav.setAttribute('aria-label', `Navigation ${index + 1}`);
            }
        });
        
        // Label breadcrumbs
        document.querySelectorAll('.breadcrumb, [class*="breadcrumb"]').forEach(breadcrumb => {
            if (!breadcrumb.getAttribute('aria-label')) {
                breadcrumb.setAttribute('aria-label', 'Breadcrumb navigation');
            }
        });
    }

    labelFormElements() {
        // Label search forms
        document.querySelectorAll('form').forEach(form => {
            const searchInput = form.querySelector('input[type="search"], input[placeholder*="search" i]');
            if (searchInput && !form.getAttribute('role')) {
                form.setAttribute('role', 'search');
            }
        });
        
        // Label fieldsets
        document.querySelectorAll('fieldset').forEach(fieldset => {
            const legend = fieldset.querySelector('legend');
            if (!legend) {
                const firstLabel = fieldset.querySelector('label');
                if (firstLabel) {
                    const legend = document.createElement('legend');
                    legend.textContent = firstLabel.textContent;
                    legend.style.cssText = `
                        position: absolute;
                        left: -10000px;
                        width: 1px;
                        height: 1px;
                        overflow: hidden;
                    `;
                    fieldset.insertBefore(legend, fieldset.firstChild);
                }
            }
        });
    }

    labelInteractiveElements() {
        // Label close buttons
        document.querySelectorAll('[class*="close"], [class*="dismiss"]').forEach(button => {
            if (!button.getAttribute('aria-label')) {
                button.setAttribute('aria-label', 'Close');
            }
        });
        
        // Label toggle buttons
        document.querySelectorAll('[class*="toggle"]').forEach(button => {
            if (!button.getAttribute('aria-pressed') && button.tagName === 'BUTTON') {
                button.setAttribute('aria-pressed', 'false');
            }
        });
    }

    addLandmarkRoles() {
        // Add main landmark
        const main = document.querySelector('main');
        if (main && !main.getAttribute('role')) {
            main.setAttribute('role', 'main');
        }
        
        // Add banner role to header
        const header = document.querySelector('header');
        if (header && !header.getAttribute('role')) {
            header.setAttribute('role', 'banner');
        }
        
        // Add contentinfo role to footer
        const footer = document.querySelector('footer');
        if (footer && !footer.getAttribute('role')) {
            footer.setAttribute('role', 'contentinfo');
        }
    }

    addAccessibilityShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Alt + 1: Focus main content
            if (event.altKey && event.key === '1') {
                event.preventDefault();
                this.focusMainContent();
            }
            
            // Alt + 2: Focus navigation
            if (event.altKey && event.key === '2') {
                event.preventDefault();
                this.focusNavigation();
            }
            
            // Alt + 3: Focus search
            if (event.altKey && event.key === '3') {
                event.preventDefault();
                this.focusSearch();
            }
            
            // Alt + H: Show accessibility help
            if (event.altKey && event.key === 'h') {
                event.preventDefault();
                this.showAccessibilityHelp();
            }
        });
    }

    // Public API methods
    announceToScreenReader(message, priority = 'polite') {
        const liveRegion = document.getElementById(`${this.toolkitId}-live-region-${priority}`);
        if (liveRegion) {
            liveRegion.textContent = message;
            
            // Clear after announcement
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
        
        // Store announcement
        this.announcements.push({
            message,
            priority,
            timestamp: new Date()
        });
    }

    enableHighContrastMode() {
        this.highContrastMode = true;
        document.documentElement.setAttribute('data-high-contrast', 'true');
        this.announceToScreenReader('High contrast mode enabled');
    }

    disableHighContrastMode() {
        this.highContrastMode = false;
        document.documentElement.removeAttribute('data-high-contrast');
        this.announceToScreenReader('High contrast mode disabled');
    }

    focusMainContent() {
        const main = document.querySelector('main, [role="main"], #main-content');
        if (main) {
            main.focus();
            this.announceToScreenReader('Focused main content');
        }
    }

    focusNavigation() {
        const nav = document.querySelector('nav, [role="navigation"]');
        if (nav) {
            nav.focus();
            this.announceToScreenReader('Focused navigation');
        }
    }

    focusSearch() {
        const search = document.querySelector('input[type="search"], [role="search"] input');
        if (search) {
            search.focus();
            this.announceToScreenReader('Focused search');
        }
    }

    showAccessibilityHelp() {
        const helpText = `
Accessibility keyboard shortcuts:
- Alt + 1: Focus main content
- Alt + 2: Focus navigation  
- Alt + 3: Focus search
- Alt + H: Show this help
- Tab: Navigate forward
- Shift + Tab: Navigate backward
- Enter/Space: Activate buttons
- Escape: Close dialogs
        `.trim();
        
        this.announceToScreenReader(helpText, 'assertive');
    }

    // Utility methods
    generateId(prefix) {
        return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    isVisible(element) {
        return element.offsetWidth > 0 && element.offsetHeight > 0;
    }

    inferButtonPurpose(button, icon) {
        const className = icon.className || '';
        const parent = button.closest('[class]');
        
        if (className.includes('close') || className.includes('x')) return 'Close';
        if (className.includes('menu') || className.includes('hamburger')) return 'Menu';
        if (className.includes('search')) return 'Search';
        if (className.includes('play')) return 'Play';
        if (className.includes('pause')) return 'Pause';
        if (className.includes('edit')) return 'Edit';
        if (className.includes('delete')) return 'Delete';
        if (parent?.className.includes('modal')) return 'Close dialog';
        
        return 'Button';
    }

    inferLinkPurpose(link, icon) {
        const href = link.href || '';
        const className = icon.className || '';
        
        if (href.includes('mailto:')) return 'Email link';
        if (href.includes('tel:')) return 'Phone link';
        if (className.includes('external')) return 'External link';
        if (className.includes('download')) return 'Download link';
        
        return 'Link';
    }

    moveFocusToTab(tabs, oldIndex, newIndex) {
        tabs[oldIndex].setAttribute('tabindex', '-1');
        tabs[newIndex].setAttribute('tabindex', '0');
        tabs[newIndex].focus();
    }

    handleGlobalKeyboardShortcuts(event) {
        // Handle toolkit-specific shortcuts here
        // This can be overridden by individual toolkits
    }

    handleEscapeKey(event) {
        // Close any open modals
        const modal = document.querySelector('[role="dialog"]:not([hidden])');
        if (modal) {
            const closeButton = modal.querySelector('[data-dismiss], .close, .modal-close');
            if (closeButton) {
                closeButton.click();
            }
        }
    }

    storeAccessibilityPreferences() {
        const preferences = {
            screenReaderEnabled: this.screenReaderEnabled,
            highContrastMode: this.highContrastMode,
            keyboardNavigationEnabled: this.keyboardNavigationEnabled
        };
        
        try {
            localStorage.setItem(`accessibility_preferences_${this.toolkitId}`, JSON.stringify(preferences));
        } catch (error) {
            console.warn('Failed to store accessibility preferences:', error);
        }
    }

    getAccessibilityReport() {
        return {
            toolkitId: this.toolkitId,
            screenReaderEnabled: this.screenReaderEnabled,
            highContrastMode: this.highContrastMode,
            keyboardNavigationEnabled: this.keyboardNavigationEnabled,
            announcements: this.announcements.length,
            focusHistory: this.focusHistory.length,
            initialized: this.initialized
        };
    }
}

// Global instance and export
window.AccessibilityEnhancer = AccessibilityEnhancer;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AccessibilityEnhancer;
}