/**
 * Unified Progress Tracking System
 * Tracks learning progress, achievements, and provides analytics across all toolkits
 */

class UnifiedProgressTracker {
    constructor() {
        this.userId = this.getUserId();
        this.progressData = new Map();
        this.achievements = new Map();
        this.learningPaths = new Map();
        this.sessionData = new Map();
        this.streakData = new Map();
        this.knowledgeStore = null;
        
        this.initializeTracker();
    }

    async initializeTracker() {
        try {
            // Load existing progress data
            await this.loadProgressData();
            
            // Initialize achievement system
            this.initializeAchievements();
            
            // Setup learning paths
            this.setupLearningPaths();
            
            // Start session tracking
            this.startSession();
            
            // Connect to unified knowledge store
            this.connectToKnowledgeStore();
        } catch (error) {
            console.warn('Error initializing progress tracker:', error);
        }
    }

    // User Management
    getUserId() {
        let userId = localStorage.getItem('unifiedToolkits.userId');
        if (!userId) {
            userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('unifiedToolkits.userId', userId);
        }
        return userId;
    }

    // Core Progress Tracking
    trackActivity(toolkitId, activityType, activityData) {
        try {
            const timestamp = new Date();
            const activityId = this.generateActivityId(toolkitId, activityType, timestamp);
            
            const activity = {
                id: activityId,
                toolkitId,
                type: activityType,
                data: activityData || {},
                timestamp,
                duration: (activityData && activityData.duration) || 0,
                userId: this.userId
            };

            // Store the activity
            this.storeActivity(activity);
            
            // Update progress metrics
            this.updateProgressMetrics(activity);
            
            // Check for achievements
            this.checkAchievements(activity);
            
            // Update learning streak
            this.updateLearningStreak(toolkitId, timestamp);
            
            // Emit progress event
            this.emitProgressEvent('activity-tracked', activity);
            
            // Save to storage
            this.saveProgressData();
            
            return activity;
        } catch (error) {
            console.error('Error tracking activity:', error);
            return null;
        }
    }

    storeActivity(activity) {
        const key = `${activity.toolkitId}:activities`;
        
        if (!this.progressData.has(key)) {
            this.progressData.set(key, []);
        }
        
        const activities = this.progressData.get(key);
        activities.push(activity);
        
        // Keep only last 1000 activities per toolkit
        if (activities.length > 1000) {
            activities.shift();
        }
    }

    updateProgressMetrics(activity) {
        const { toolkitId, type, data, timestamp, duration } = activity;
        const metricsKey = `${toolkitId}:metrics`;
        
        let metrics = this.progressData.get(metricsKey);
        if (!metrics) {
            metrics = this.createEmptyMetrics();
            this.progressData.set(metricsKey, metrics);
        }

        // Update based on activity type
        switch (type) {
            case 'concept-viewed':
                metrics.conceptsViewed.add(data.conceptId);
                metrics.totalTimeSpent += duration;
                break;
                
            case 'problem-solved':
                metrics.problemsSolved.add(data.problemId);
                metrics.totalProblemsSolved++;
                if (data.difficulty) {
                    metrics.difficultyLevels[data.difficulty] = (metrics.difficultyLevels[data.difficulty] || 0) + 1;
                }
                break;
                
            case 'framework-used':
                metrics.frameworksUsed.add(data.frameworkId);
                break;
                
            case 'toolkit-visited':
                metrics.toolkitVisits++;
                metrics.lastVisit = timestamp;
                break;
                
            case 'knowledge-item-completed':
                metrics.completedItems.add(data.itemId);
                break;
                
            case 'assessment-completed':
                metrics.assessments.push({
                    id: data.assessmentId,
                    score: data.score,
                    timestamp
                });
                break;
        }

        metrics.lastActivity = timestamp;
        metrics.totalActivities++;
    }

    createEmptyMetrics() {
        return {
            conceptsViewed: new Set(),
            problemsSolved: new Set(),
            frameworksUsed: new Set(),
            completedItems: new Set(),
            totalProblemsSolved: 0,
            totalTimeSpent: 0,
            totalActivities: 0,
            toolkitVisits: 0,
            difficultyLevels: {},
            assessments: [],
            lastActivity: null,
            lastVisit: null,
            createdAt: new Date()
        };
    }

    // Learning Streaks
    updateLearningStreak(toolkitId, timestamp) {
        const streakKey = `${toolkitId}:streak`;
        let streakData = this.streakData.get(streakKey);
        
        if (!streakData) {
            streakData = {
                currentStreak: 0,
                longestStreak: 0,
                lastActivity: null,
                activeDays: new Set()
            };
            this.streakData.set(streakKey, streakData);
        }

        // Ensure timestamp is a valid Date object
        const currentDate = timestamp instanceof Date ? timestamp : new Date(timestamp);
        if (isNaN(currentDate.getTime())) {
            console.warn('Invalid timestamp provided to updateLearningStreak:', timestamp);
            return;
        }

        const today = this.getDateString(currentDate);
        const lastActivityDate = streakData.lastActivity ? this.getDateString(streakData.lastActivity) : null;
        
        // Check if this is a new day
        if (lastActivityDate !== today) {
            streakData.activeDays.add(today);
            
            if (lastActivityDate) {
                const daysDiff = this.getDaysDifference(new Date(lastActivityDate), currentDate);
                
                if (daysDiff === 1) {
                    // Consecutive day - continue streak
                    streakData.currentStreak++;
                } else if (daysDiff > 1) {
                    // Gap in learning - reset streak
                    streakData.currentStreak = 1;
                }
            } else {
                // First activity
                streakData.currentStreak = 1;
            }
            
            // Update longest streak
            if (streakData.currentStreak > streakData.longestStreak) {
                streakData.longestStreak = streakData.currentStreak;
            }
        }
        
        streakData.lastActivity = currentDate;
    }

    // Achievement System
    initializeAchievements() {
        const achievementDefinitions = [
            {
                id: 'first-problem',
                name: 'Problem Solver',
                description: 'Solved your first problem',
                condition: (metrics) => metrics.totalProblemsSolved >= 1,
                icon: '🎯',
                points: 10
            },
            {
                id: 'problem-streak-7',
                name: 'Week Warrior',
                description: 'Maintained a 7-day learning streak',
                condition: (_, streaks) => Math.max(...Object.values(streaks).map(s => s.currentStreak)) >= 7,
                icon: '🔥',
                points: 50
            },
            {
                id: 'toolkit-explorer',
                name: 'Toolkit Explorer',
                description: 'Visited all available toolkits',
                condition: (allMetrics) => Object.keys(allMetrics).length >= 10,
                icon: '🧭',
                points: 100
            },
            {
                id: 'framework-master',
                name: 'Framework Master',
                description: 'Used all thinking frameworks',
                condition: (metrics) => metrics.frameworksUsed?.size >= 4,
                icon: '🧠',
                points: 75
            },
            {
                id: 'problem-solver-100',
                name: 'Century Solver',
                description: 'Solved 100 problems',
                condition: (metrics) => metrics.totalProblemsSolved >= 100,
                icon: '💯',
                points: 200
            },
            {
                id: 'time-warrior',
                name: 'Time Warrior',
                description: 'Spent 10 hours learning',
                condition: (metrics) => metrics.totalTimeSpent >= 36000000, // 10 hours in ms
                icon: '⏰',
                points: 150
            },
            {
                id: 'knowledge-collector',
                name: 'Knowledge Collector',
                description: 'Completed 50 knowledge items',
                condition: (metrics) => metrics.completedItems?.size >= 50,
                icon: '📚',
                points: 125
            }
        ];

        achievementDefinitions.forEach(achievement => {
            this.achievements.set(achievement.id, {
                ...achievement,
                unlocked: false,
                unlockedAt: null
            });
        });
    }

    checkAchievements(activity) {
        const allMetrics = this.getAllMetrics();
        const allStreaks = this.getAllStreaks();
        const newAchievements = [];

        for (const [achievementId, achievement] of this.achievements.entries()) {
            if (!achievement.unlocked) {
                const metricsForToolkit = allMetrics[activity.toolkitId] || this.createEmptyMetrics();
                
                let conditionMet = false;
                try {
                    conditionMet = achievement.condition(metricsForToolkit, allStreaks, allMetrics);
                } catch (error) {
                    console.error(`Error checking achievement ${achievementId}:`, error);
                    continue;
                }

                if (conditionMet) {
                    achievement.unlocked = true;
                    achievement.unlockedAt = new Date();
                    newAchievements.push(achievement);
                }
            }
        }

        if (newAchievements.length > 0) {
            this.emitProgressEvent('achievements-unlocked', newAchievements);
        }

        return newAchievements;
    }

    // Learning Paths
    setupLearningPaths() {
        const pathDefinitions = {
            'algorithm-mastery': {
                name: 'Algorithm Mastery Path',
                description: 'Master algorithmic thinking and problem solving',
                toolkits: ['algorithm-thinking-toolkit'],
                milestones: [
                    { name: 'Understanding Frameworks', concepts: ['four-step', '5w1h', 'polya', 'computational-thinking'] },
                    { name: 'Problem Solving Practice', problems: 10 },
                    { name: 'Advanced Techniques', concepts: ['dynamic-programming', 'greedy-algorithms'] }
                ],
                estimatedHours: 20
            },
            'software-architecture-mastery': {
                name: 'Software Architecture Mastery',
                description: 'Become proficient in software architecture and design',
                toolkits: ['software-architecture-explorer', 'software-engineer-handbook'],
                milestones: [
                    { name: 'Design Patterns', concepts: ['creational', 'structural', 'behavioral'] },
                    { name: 'Architecture Patterns', concepts: ['clean-architecture', 'microservices', 'event-driven'] },
                    { name: 'Enterprise Patterns', concepts: ['domain-logic', 'data-source'] }
                ],
                estimatedHours: 40
            },
            'thinking-excellence': {
                name: 'Thinking Excellence Path',
                description: 'Develop superior thinking and problem-solving skills',
                toolkits: ['thinking-os-toolkit', 'creative-thinking-toolkit', 'algorithm-thinking-toolkit'],
                milestones: [
                    { name: 'Meta Principles', concepts: ['subject-principle', 'contradiction-principle'] },
                    { name: 'Cognitive Architecture', concepts: ['perception', 'reasoning', 'decision'] },
                    { name: 'Universal Process', concepts: ['12-step-process'] }
                ],
                estimatedHours: 30
            }
        };

        for (const [pathId, pathData] of Object.entries(pathDefinitions)) {
            this.learningPaths.set(pathId, {
                ...pathData,
                progress: {
                    currentMilestone: 0,
                    completedMilestones: [],
                    startedAt: null,
                    estimatedCompletion: null
                }
            });
        }
    }

    // Progress Queries and Analytics
    getToolkitProgress(toolkitId) {
        const metricsKey = `${toolkitId}:metrics`;
        const streakKey = `${toolkitId}:streak`;
        const activitiesKey = `${toolkitId}:activities`;

        const metrics = this.progressData.get(metricsKey) || this.createEmptyMetrics();
        const streak = this.streakData.get(streakKey) || { currentStreak: 0, longestStreak: 0 };
        const activities = this.progressData.get(activitiesKey) || [];

        return {
            toolkitId,
            metrics: this.serializeMetrics(metrics),
            streak,
            recentActivities: activities.slice(-10),
            totalActivities: activities.length
        };
    }

    getOverallProgress() {
        const allMetrics = this.getAllMetrics();
        const allStreaks = this.getAllStreaks();
        const unlockedAchievements = Array.from(this.achievements.values()).filter(a => a.unlocked);

        // Aggregate totals
        const totals = {
            totalConceptsViewed: 0,
            totalProblemsSolved: 0,
            totalTimeSpent: 0,
            totalActivities: 0,
            toolkitsVisited: Object.keys(allMetrics).length,
            currentStreak: Math.max(...Object.values(allStreaks).map(s => s.currentStreak), 0),
            longestStreak: Math.max(...Object.values(allStreaks).map(s => s.longestStreak), 0),
            achievementPoints: unlockedAchievements.reduce((sum, a) => sum + a.points, 0)
        };

        for (const metrics of Object.values(allMetrics)) {
            totals.totalConceptsViewed += metrics.conceptsViewed?.size || 0;
            totals.totalProblemsSolved += metrics.totalProblemsSolved || 0;
            totals.totalTimeSpent += metrics.totalTimeSpent || 0;
            totals.totalActivities += metrics.totalActivities || 0;
        }

        return {
            userId: this.userId,
            totals,
            achievements: unlockedAchievements.length,
            learningPaths: this.getLearningPathsProgress(),
            lastActivity: this.getLastActivity()
        };
    }

    getLearningPathsProgress() {
        const pathProgress = {};
        
        for (const [pathId, path] of this.learningPaths.entries()) {
            const progress = this.calculatePathProgress(pathId);
            pathProgress[pathId] = {
                name: path.name,
                progress: progress.percentage,
                currentMilestone: progress.currentMilestone,
                estimatedTimeRemaining: progress.estimatedTimeRemaining
            };
        }
        
        return pathProgress;
    }

    calculatePathProgress(pathId) {
        const path = this.learningPaths.get(pathId);
        if (!path) return { percentage: 0, currentMilestone: 0, estimatedTimeRemaining: 0 };

        let completedMilestones = 0;
        let currentMilestone = 0;

        for (let i = 0; i < path.milestones.length; i++) {
            const milestone = path.milestones[i];
            const isCompleted = this.isMilestoneCompleted(milestone, path.toolkits);
            
            if (isCompleted) {
                completedMilestones++;
            } else if (currentMilestone === 0) {
                currentMilestone = i;
            }
        }

        const percentage = (completedMilestones / path.milestones.length) * 100;
        const remainingMilestones = path.milestones.length - completedMilestones;
        const estimatedTimeRemaining = (remainingMilestones / path.milestones.length) * path.estimatedHours;

        return {
            percentage,
            currentMilestone,
            estimatedTimeRemaining
        };
    }

    // Session Management
    startSession() {
        const sessionId = this.generateSessionId();
        const sessionData = {
            id: sessionId,
            userId: this.userId,
            startTime: new Date(),
            activities: [],
            toolkitsVisited: new Set(),
            timeSpent: 0
        };

        this.sessionData.set('current', sessionData);
        
        // Track session end on page unload
        window.addEventListener('beforeunload', () => {
            this.endSession();
        });

        // Update session periodically
        this.sessionUpdateInterval = setInterval(() => {
            this.updateCurrentSession();
        }, 60000); // Every minute
    }

    updateCurrentSession() {
        const session = this.sessionData.get('current');
        if (session) {
            session.timeSpent = Date.now() - session.startTime.getTime();
            this.saveSessionData();
        }
    }

    endSession() {
        const session = this.sessionData.get('current');
        if (session) {
            session.endTime = new Date();
            session.timeSpent = session.endTime.getTime() - session.startTime.getTime();
            
            // Save completed session
            const completedSessions = this.sessionData.get('completed') || [];
            completedSessions.push(session);
            this.sessionData.set('completed', completedSessions);
            
            // Clear current session
            this.sessionData.delete('current');
            
            this.saveSessionData();
        }

        if (this.sessionUpdateInterval) {
            clearInterval(this.sessionUpdateInterval);
        }
    }

    // Data Persistence
    async loadProgressData() {
        try {
            const stored = localStorage.getItem(`unifiedProgress.${this.userId}`);
            if (stored) {
                const data = JSON.parse(stored);
                
                // Restore Maps and Sets
                this.progressData = new Map(data.progressData || []);
                this.streakData = new Map(data.streakData || []);
                
                // Restore Sets within metrics
                for (const [key, metrics] of this.progressData.entries()) {
                    if (key.endsWith(':metrics')) {
                        if (metrics.conceptsViewed && Array.isArray(metrics.conceptsViewed)) {
                            metrics.conceptsViewed = new Set(metrics.conceptsViewed);
                        }
                        if (metrics.problemsSolved && Array.isArray(metrics.problemsSolved)) {
                            metrics.problemsSolved = new Set(metrics.problemsSolved);
                        }
                        if (metrics.frameworksUsed && Array.isArray(metrics.frameworksUsed)) {
                            metrics.frameworksUsed = new Set(metrics.frameworksUsed);
                        }
                        if (metrics.completedItems && Array.isArray(metrics.completedItems)) {
                            metrics.completedItems = new Set(metrics.completedItems);
                        }
                    }
                }

                // Restore achievement states
                if (data.achievements) {
                    for (const [id, achievement] of Object.entries(data.achievements)) {
                        if (this.achievements.has(id)) {
                            this.achievements.get(id).unlocked = achievement.unlocked;
                            this.achievements.get(id).unlockedAt = achievement.unlockedAt ? new Date(achievement.unlockedAt) : null;
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Failed to load progress data:', error);
        }
    }

    saveProgressData() {
        try {
            // Convert Maps and Sets for JSON serialization
            const progressArray = [];
            for (const [key, value] of this.progressData.entries()) {
                if (key.endsWith(':metrics')) {
                    progressArray.push([key, this.serializeMetrics(value)]);
                } else {
                    progressArray.push([key, value]);
                }
            }

            const streakArray = [];
            for (const [key, value] of this.streakData.entries()) {
                streakArray.push([key, {
                    ...value,
                    activeDays: [...value.activeDays]
                }]);
            }

            const achievementStates = {};
            for (const [id, achievement] of this.achievements.entries()) {
                achievementStates[id] = {
                    unlocked: achievement.unlocked,
                    unlockedAt: achievement.unlockedAt
                };
            }

            const data = {
                progressData: progressArray,
                streakData: streakArray,
                achievements: achievementStates,
                lastSaved: new Date().toISOString()
            };

            localStorage.setItem(`unifiedProgress.${this.userId}`, JSON.stringify(data));
        } catch (error) {
            console.error('Failed to save progress data:', error);
        }
    }

    // Utility Methods
    serializeMetrics(metrics) {
        return {
            ...metrics,
            conceptsViewed: [...(metrics.conceptsViewed || [])],
            problemsSolved: [...(metrics.problemsSolved || [])],
            frameworksUsed: [...(metrics.frameworksUsed || [])],
            completedItems: [...(metrics.completedItems || [])]
        };
    }

    getAllMetrics() {
        const allMetrics = {};
        for (const [key, metrics] of this.progressData.entries()) {
            if (key.endsWith(':metrics')) {
                const toolkitId = key.replace(':metrics', '');
                allMetrics[toolkitId] = metrics;
            }
        }
        return allMetrics;
    }

    getAllStreaks() {
        const allStreaks = {};
        for (const [key, streak] of this.streakData.entries()) {
            if (key.endsWith(':streak')) {
                const toolkitId = key.replace(':streak', '');
                allStreaks[toolkitId] = streak;
            }
        }
        return allStreaks;
    }

    getDateString(date) {
        if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
            return new Date().toISOString().split('T')[0];
        }
        return date.toISOString().split('T')[0];
    }

    getDaysDifference(date1, date2) {
        if (!date1 || !date2 || !(date1 instanceof Date) || !(date2 instanceof Date)) {
            return 0;
        }
        const timeDiff = Math.abs(date2.getTime() - date1.getTime());
        return Math.ceil(timeDiff / (1000 * 3600 * 24));
    }

    generateActivityId(toolkitId, activityType, timestamp) {
        return `${toolkitId}_${activityType}_${timestamp.getTime()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Event System
    emitProgressEvent(eventType, data) {
        const event = new CustomEvent('unified-progress', {
            detail: {
                type: eventType,
                data,
                timestamp: new Date(),
                userId: this.userId
            }
        });
        
        window.dispatchEvent(event);
    }

    // Public API
    getAchievements() {
        return Array.from(this.achievements.values());
    }

    getUnlockedAchievements() {
        return Array.from(this.achievements.values()).filter(a => a.unlocked);
    }

    exportProgress() {
        return {
            userId: this.userId,
            progress: this.getOverallProgress(),
            achievements: this.getUnlockedAchievements(),
            sessions: this.sessionData.get('completed') || [],
            exportedAt: new Date().toISOString()
        };
    }

    // Additional utility methods for compatibility
    getLastActivity() {
        let lastActivity = null;
        let latestTimestamp = 0;
        
        for (const [key, activities] of this.progressData.entries()) {
            if (key.endsWith(':activities') && Array.isArray(activities) && activities.length > 0) {
                const latest = activities[activities.length - 1];
                if (latest.timestamp && latest.timestamp.getTime() > latestTimestamp) {
                    latestTimestamp = latest.timestamp.getTime();
                    lastActivity = latest;
                }
            }
        }
        
        return lastActivity;
    }

    isMilestoneCompleted(milestone, toolkits) {
        // Simple implementation - can be enhanced based on specific requirements
        if (milestone.concepts) {
            // Check if concepts have been viewed
            for (const toolkitId of toolkits) {
                const metrics = this.progressData.get(`${toolkitId}:metrics`);
                if (metrics && metrics.conceptsViewed) {
                    const hasAllConcepts = milestone.concepts.every(concept => 
                        Array.from(metrics.conceptsViewed).some(viewed => viewed.includes(concept))
                    );
                    if (hasAllConcepts) return true;
                }
            }
        }
        
        if (milestone.problems) {
            // Check if enough problems have been solved
            let totalSolved = 0;
            for (const toolkitId of toolkits) {
                const metrics = this.progressData.get(`${toolkitId}:metrics`);
                if (metrics && metrics.totalProblemsSolved) {
                    totalSolved += metrics.totalProblemsSolved;
                }
            }
            return totalSolved >= milestone.problems;
        }
        
        return false;
    }

    saveSessionData() {
        try {
            const sessionData = {
                current: this.sessionData.get('current'),
                completed: this.sessionData.get('completed') || []
            };
            localStorage.setItem(`unifiedSessions.${this.userId}`, JSON.stringify(sessionData));
        } catch (error) {
            console.error('Failed to save session data:', error);
        }
    }

    // Integration with Knowledge Store
    connectToKnowledgeStore() {
        if (window.unifiedKnowledgeStore) {
            this.knowledgeStore = window.unifiedKnowledgeStore;
            
            // Listen for knowledge store events
            window.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'knowledge-interaction') {
                    this.trackActivity(
                        event.data.toolkitId,
                        'concept-viewed',
                        { conceptId: event.data.conceptId, duration: event.data.duration }
                    );
                }
            });
        }
    }
}

// Global instance
window.UnifiedProgressTracker = UnifiedProgressTracker;

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
    try {
        window.unifiedProgressTracker = new UnifiedProgressTracker();
    } catch (error) {
        console.warn('Failed to initialize unified progress tracker:', error);
        // Create a minimal fallback
        window.unifiedProgressTracker = {
            trackActivity: () => {},
            getToolkitProgress: () => ({ metrics: {}, streak: {}, recentActivities: [] }),
            getOverallProgress: () => ({ totals: {}, achievements: 0 })
        };
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedProgressTracker;
}