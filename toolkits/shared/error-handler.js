/**
 * Standardized Error Handling System for All Toolkits
 * Provides consistent error reporting, logging, and user feedback patterns
 */

class StandardizedErrorHandler {
    constructor(toolkitId = 'unknown') {
        this.toolkitId = toolkitId;
        this.errorQueue = [];
        this.errorReporting = true;
        this.userNotifications = true;
        this.initialized = false;
        
        this.initializeErrorHandler();
    }

    initializeErrorHandler() {
        try {
            // Setup global error handling
            this.setupGlobalErrorHandlers();
            
            // Initialize error reporting
            this.initializeErrorReporting();
            
            // Setup performance monitoring
            this.setupPerformanceMonitoring();
            
            this.initialized = true;
            console.log(`Error handler initialized for toolkit: ${this.toolkitId}`);
            
        } catch (error) {
            console.error('Failed to initialize error handler:', error);
        }
    }

    setupGlobalErrorHandlers() {
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, {
                type: 'unhandled_promise_rejection',
                source: 'global',
                timestamp: new Date()
            });
        });

        // Handle general JavaScript errors
        window.addEventListener('error', (event) => {
            this.handleError(event.error, {
                type: 'javascript_error',
                source: event.filename,
                line: event.lineno,
                column: event.colno,
                timestamp: new Date()
            });
        });

        // Handle resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleError(new Error(`Resource loading failed: ${event.target.src || event.target.href}`), {
                    type: 'resource_loading_error',
                    source: event.target.tagName,
                    timestamp: new Date()
                });
            }
        }, true);
    }

    // Standardized error handling method
    handleError(error, context = {}) {
        const errorInfo = this.createErrorInfo(error, context);
        
        // Log the error
        this.logError(errorInfo);
        
        // Queue error for reporting
        if (this.errorReporting) {
            this.queueErrorReport(errorInfo);
        }
        
        // Show user notification if appropriate
        if (this.userNotifications && this.shouldShowUserNotification(errorInfo)) {
            this.showUserNotification(errorInfo);
        }
        
        // Emit error event for other systems
        this.emitErrorEvent(errorInfo);
        
        return errorInfo;
    }

    createErrorInfo(error, context) {
        const baseInfo = {
            id: this.generateErrorId(),
            toolkitId: this.toolkitId,
            timestamp: new Date(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            ...context
        };

        if (error instanceof Error) {
            return {
                ...baseInfo,
                name: error.name,
                message: error.message,
                stack: error.stack,
                severity: this.determineSeverity(error, context)
            };
        } else {
            return {
                ...baseInfo,
                name: 'UnknownError',
                message: String(error),
                stack: null,
                severity: 'medium'
            };
        }
    }

    determineSeverity(error, context) {
        // Critical errors that break functionality
        if (error.name === 'TypeError' && error.message.includes('Cannot read properties of null')) {
            return 'high';
        }
        
        if (context.type === 'unhandled_promise_rejection') {
            return 'high';
        }
        
        if (context.type === 'resource_loading_error') {
            return 'medium';
        }
        
        // Network errors
        if (error.message.includes('fetch') || error.message.includes('network')) {
            return 'medium';
        }
        
        // Default to low severity
        return 'low';
    }

    logError(errorInfo) {
        const logMethod = this.getLogMethod(errorInfo.severity);
        const logMessage = this.formatLogMessage(errorInfo);
        
        logMethod(logMessage, errorInfo);
        
        // Store in local storage for debugging
        this.storeErrorLocally(errorInfo);
    }

    getLogMethod(severity) {
        switch (severity) {
            case 'high':
                return console.error;
            case 'medium':
                return console.warn;
            case 'low':
            default:
                return console.log;
        }
    }

    formatLogMessage(errorInfo) {
        return `[${errorInfo.toolkitId}] ${errorInfo.severity.toUpperCase()}: ${errorInfo.name} - ${errorInfo.message}`;
    }

    storeErrorLocally(errorInfo) {
        try {
            const storageKey = `toolkit_errors_${this.toolkitId}`;
            const existingErrors = JSON.parse(localStorage.getItem(storageKey) || '[]');
            
            // Keep only last 50 errors per toolkit
            const updatedErrors = [errorInfo, ...existingErrors].slice(0, 50);
            
            localStorage.setItem(storageKey, JSON.stringify(updatedErrors));
        } catch (error) {
            console.warn('Failed to store error locally:', error);
        }
    }

    shouldShowUserNotification(errorInfo) {
        // Don't show notifications for low severity errors
        if (errorInfo.severity === 'low') {
            return false;
        }
        
        // Don't show notifications for resource loading errors
        if (errorInfo.type === 'resource_loading_error') {
            return false;
        }
        
        // Don't spam users with notifications
        const recentNotifications = this.getRecentNotifications();
        return recentNotifications.length < 3;
    }

    showUserNotification(errorInfo) {
        const notification = this.createNotificationElement(errorInfo);
        document.body.appendChild(notification);
        
        // Track notification
        this.trackNotification(errorInfo);
        
        // Auto-remove after delay
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    createNotificationElement(errorInfo) {
        const notification = document.createElement('div');
        notification.className = `error-notification severity-${errorInfo.severity}`;
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-live', 'assertive');
        
        const icon = this.getSeverityIcon(errorInfo.severity);
        const message = this.getUserFriendlyMessage(errorInfo);
        
        notification.innerHTML = `
            <div class="error-notification-content">
                <span class="error-icon">${icon}</span>
                <div class="error-text">
                    <div class="error-title">Something went wrong</div>
                    <div class="error-message">${message}</div>
                </div>
                <button class="error-close" aria-label="Close notification">✕</button>
            </div>
        `;
        
        // Add styling
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-bg-2, #f8f9fa);
            border: 1px solid var(--color-border, #e1e5e9);
            border-left: 4px solid ${this.getSeverityColor(errorInfo.severity)};
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease-out;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        // Close button functionality
        notification.querySelector('.error-close').addEventListener('click', () => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        });
        
        return notification;
    }

    getSeverityIcon(severity) {
        switch (severity) {
            case 'high':
                return '⚠️';
            case 'medium':
                return '⚡';
            case 'low':
            default:
                return 'ℹ️';
        }
    }

    getSeverityColor(severity) {
        switch (severity) {
            case 'high':
                return '#dc3545';
            case 'medium':
                return '#ffc107';
            case 'low':
            default:
                return '#17a2b8';
        }
    }

    getUserFriendlyMessage(errorInfo) {
        // Convert technical errors to user-friendly messages
        if (errorInfo.message.includes('Cannot read properties of null')) {
            return 'A component failed to load properly. Please refresh the page.';
        }
        
        if (errorInfo.message.includes('fetch')) {
            return 'Network connection issue. Please check your connection and try again.';
        }
        
        if (errorInfo.message.includes('Resource loading failed')) {
            return 'Some resources failed to load. Functionality may be limited.';
        }
        
        // Default message for unknown errors
        return 'An unexpected error occurred. The issue has been logged for investigation.';
    }

    // Error reporting and analytics
    queueErrorReport(errorInfo) {
        this.errorQueue.push(errorInfo);
        
        // Send reports in batches
        if (this.errorQueue.length >= 5) {
            this.sendErrorReports();
        }
    }

    async sendErrorReports() {
        if (this.errorQueue.length === 0) return;
        
        const reports = [...this.errorQueue];
        this.errorQueue = [];
        
        try {
            // In a real implementation, this would send to your error reporting service
            console.log('Sending error reports:', reports);
            
            // Emit event for other systems to handle
            window.dispatchEvent(new CustomEvent('error-reports-sent', {
                detail: { reports, toolkitId: this.toolkitId }
            }));
            
        } catch (error) {
            console.warn('Failed to send error reports:', error);
            // Re-queue the reports
            this.errorQueue.push(...reports);
        }
    }

    // Performance monitoring
    setupPerformanceMonitoring() {
        // Monitor memory usage
        setInterval(() => {
            if (performance.memory) {
                const memoryUsage = {
                    usedJSHeapSize: performance.memory.usedJSHeapSize,
                    totalJSHeapSize: performance.memory.totalJSHeapSize,
                    jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                };
                
                // Warn if memory usage is high
                const usagePercentage = (memoryUsage.usedJSHeapSize / memoryUsage.jsHeapSizeLimit) * 100;
                if (usagePercentage > 80) {
                    this.handleError(new Error('High memory usage detected'), {
                        type: 'performance_warning',
                        memoryUsage,
                        usagePercentage
                    });
                }
            }
        }, 30000); // Check every 30 seconds
    }

    // Utility methods
    generateErrorId() {
        return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    getRecentNotifications() {
        const notificationKey = `toolkit_notifications_${this.toolkitId}`;
        const recent = JSON.parse(localStorage.getItem(notificationKey) || '[]');
        const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
        return recent.filter(timestamp => timestamp > fiveMinutesAgo);
    }

    trackNotification(errorInfo) {
        const notificationKey = `toolkit_notifications_${this.toolkitId}`;
        const recent = this.getRecentNotifications();
        recent.push(Date.now());
        localStorage.setItem(notificationKey, JSON.stringify(recent));
    }

    emitErrorEvent(errorInfo) {
        window.dispatchEvent(new CustomEvent('toolkit-error', {
            detail: errorInfo
        }));
    }

    // Public API
    getErrorHistory() {
        const storageKey = `toolkit_errors_${this.toolkitId}`;
        return JSON.parse(localStorage.getItem(storageKey) || '[]');
    }

    clearErrorHistory() {
        const storageKey = `toolkit_errors_${this.toolkitId}`;
        localStorage.removeItem(storageKey);
    }

    setErrorReporting(enabled) {
        this.errorReporting = enabled;
    }

    setUserNotifications(enabled) {
        this.userNotifications = enabled;
    }

    // Safe wrapper for async operations
    async safeAsync(operation, context = {}) {
        try {
            return await operation();
        } catch (error) {
            this.handleError(error, {
                ...context,
                type: 'async_operation_error'
            });
            return null;
        }
    }

    // Safe wrapper for synchronous operations
    safe(operation, context = {}) {
        try {
            return operation();
        } catch (error) {
            this.handleError(error, {
                ...context,
                type: 'sync_operation_error'
            });
            return null;
        }
    }
}

// Add CSS for error notifications
const errorStyles = document.createElement('style');
errorStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .error-notification-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }
    
    .error-icon {
        font-size: 20px;
        flex-shrink: 0;
    }
    
    .error-text {
        flex: 1;
    }
    
    .error-title {
        font-weight: 600;
        margin-bottom: 4px;
        color: var(--color-text, #2d3748);
    }
    
    .error-message {
        font-size: 14px;
        color: var(--color-text-secondary, #4a5568);
        line-height: 1.4;
    }
    
    .error-close {
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        color: var(--color-text-tertiary, #718096);
        flex-shrink: 0;
    }
    
    .error-close:hover {
        background: var(--color-bg-3, #edf2f7);
        color: var(--color-text, #2d3748);
    }
`;
document.head.appendChild(errorStyles);

// Global instance and export
window.StandardizedErrorHandler = StandardizedErrorHandler;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StandardizedErrorHandler;
}