/**
 * Global Search System for All Toolkits
 * Provides unified search across all knowledge resources with intelligent ranking and filtering
 */

class GlobalSearchSystem {
    constructor() {
        this.searchIndex = new Map();
        this.toolkitMetadata = new Map();
        this.searchHistory = [];
        this.popularSearches = new Map();
        this.knowledgeStore = null;
        this.initialized = false;
        
        this.initializeSearchSystem();
    }

    async initializeSearchSystem() {
        try {
            // Connect to unified knowledge store
            await this.connectToKnowledgeStore();
            
            // Build comprehensive search index
            await this.buildSearchIndex();
            
            // Initialize search analytics
            this.initializeAnalytics();
            
            this.initialized = true;
            console.log('Global Search System initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize Global Search System:', error);
        }
    }

    async connectToKnowledgeStore() {
        // Wait for knowledge store to be available
        let attempts = 0;
        while (!window.unifiedKnowledgeStore && attempts < 50) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
        
        if (window.unifiedKnowledgeStore) {
            this.knowledgeStore = window.unifiedKnowledgeStore;
        } else {
            console.warn('Unified Knowledge Store not available, using fallback indexing');
        }
    }

    async buildSearchIndex() {
        // Clear existing index
        this.searchIndex.clear();
        
        // Define all toolkit metadata with their searchable content
        const toolkitContent = {
            'algorithm-thinking-toolkit': {
                name: 'Algorithm Thinking Toolkit',
                category: 'Problem Solving',
                icon: '🧠',
                description: 'Systematic algorithmic problem-solving frameworks',
                concepts: [
                    {
                        id: 'four-step-method',
                        title: '4-Step Method',
                        description: 'Mountain exploration metaphor for problem solving',
                        keywords: ['algorithm', 'problem-solving', 'methodology', 'four-step', 'mountain', 'exploration'],
                        content: 'Analyze decompose plan strategy execute monitor evaluate optimize',
                        difficulty: 'beginner',
                        estimatedTime: 15
                    },
                    {
                        id: '5w1h-framework',
                        title: '5W1H Framework',
                        description: 'Comprehensive problem analysis technique',
                        keywords: ['analysis', '5w1h', 'what', 'why', 'who', 'when', 'where', 'how'],
                        content: 'What why who when where how comprehensive analysis framework',
                        difficulty: 'beginner',
                        estimatedTime: 10
                    },
                    {
                        id: 'polya-method',
                        title: 'Polya Method',
                        description: '4-step classical problem solving approach',
                        keywords: ['polya', 'classical', 'problem-solving', 'mathematical'],
                        content: 'Understand problem devise plan carry out plan look back reflect',
                        difficulty: 'intermediate',
                        estimatedTime: 20
                    },
                    {
                        id: 'computational-thinking',
                        title: 'Computational Thinking',
                        description: '4 pillars of computational problem solving',
                        keywords: ['computational', 'thinking', 'decomposition', 'pattern', 'abstraction', 'algorithm'],
                        content: 'Decomposition pattern recognition abstraction algorithmic thinking',
                        difficulty: 'intermediate',
                        estimatedTime: 25
                    }
                ]
            },
            'software-engineer-handbook': {
                name: 'Software Engineer Handbook',
                category: 'Software Development',
                icon: '💻',
                description: 'Comprehensive software engineering knowledge',
                concepts: [
                    {
                        id: 'clean-architecture',
                        title: 'Clean Architecture',
                        description: 'Maintainable software architecture principles',
                        keywords: ['clean', 'architecture', 'ddd', 'domain-driven', 'design'],
                        content: 'Clean architecture domain driven design dependency inversion entities use cases',
                        difficulty: 'advanced',
                        estimatedTime: 45
                    },
                    {
                        id: 'microservices',
                        title: 'Microservices Architecture',
                        description: 'Distributed systems and service design',
                        keywords: ['microservices', 'distributed', 'systems', 'scalability'],
                        content: 'Microservices distributed systems scalability service mesh api gateway',
                        difficulty: 'advanced',
                        estimatedTime: 60
                    },
                    {
                        id: 'javascript-typescript',
                        title: 'JavaScript/TypeScript',
                        description: 'Modern JavaScript and TypeScript development',
                        keywords: ['javascript', 'typescript', 'programming', 'web', 'development'],
                        content: 'JavaScript TypeScript nodejs react async programming closures prototypes',
                        difficulty: 'intermediate',
                        estimatedTime: 40
                    },
                    {
                        id: 'python-development',
                        title: 'Python Development',
                        description: 'Python programming and data science',
                        keywords: ['python', 'programming', 'data-science', 'machine-learning'],
                        content: 'Python programming data science machine learning pandas numpy django flask',
                        difficulty: 'intermediate',
                        estimatedTime: 35
                    }
                ]
            },
            'software-architecture-explorer': {
                name: 'Software Architecture Explorer',
                category: 'Architecture & Design',
                icon: '🏗️',
                description: 'Enterprise architecture patterns and practices',
                concepts: [
                    {
                        id: 'design-patterns',
                        title: 'Design Patterns',
                        description: 'Gang of Four and architectural patterns',
                        keywords: ['design', 'patterns', 'gof', 'singleton', 'factory', 'observer'],
                        content: 'Design patterns creational structural behavioral singleton factory observer strategy',
                        difficulty: 'intermediate',
                        estimatedTime: 30
                    },
                    {
                        id: 'enterprise-patterns',
                        title: 'Enterprise Patterns',
                        description: 'Enterprise application architecture patterns',
                        keywords: ['enterprise', 'patterns', 'architecture', 'domain', 'data'],
                        content: 'Enterprise patterns domain logic data source repository active record',
                        difficulty: 'advanced',
                        estimatedTime: 50
                    },
                    {
                        id: 'integration-patterns',
                        title: 'Integration Patterns',
                        description: 'System integration and messaging patterns',
                        keywords: ['integration', 'messaging', 'patterns', 'communication'],
                        content: 'Integration patterns messaging endpoints channels routers transformers',
                        difficulty: 'advanced',
                        estimatedTime: 40
                    }
                ]
            },
            'thinking-os-toolkit': {
                name: 'Thinking OS Toolkit',
                category: 'Cognitive Framework',
                icon: '🧩',
                description: 'Meta-cognitive thinking operating system',
                concepts: [
                    {
                        id: 'meta-principles',
                        title: 'Meta Principles',
                        description: 'Universal thinking principles',
                        keywords: ['meta', 'principles', 'thinking', 'universal', 'philosophy'],
                        content: 'Meta principles subject contradiction quantity quality dialectical thinking',
                        difficulty: 'advanced',
                        estimatedTime: 45
                    },
                    {
                        id: 'cognitive-architecture',
                        title: 'Cognitive Architecture',
                        description: 'Mental processing framework',
                        keywords: ['cognitive', 'architecture', 'perception', 'memory', 'reasoning'],
                        content: 'Cognitive architecture perception memory reasoning decision metacognition',
                        difficulty: 'advanced',
                        estimatedTime: 40
                    },
                    {
                        id: 'universal-process',
                        title: 'Universal 12-Step Process',
                        description: 'Universal problem-solving methodology',
                        keywords: ['universal', 'process', '12-step', 'methodology', 'systematic'],
                        content: 'Universal process define measure analyze first principles systems hypothesize',
                        difficulty: 'expert',
                        estimatedTime: 60
                    }
                ]
            },
            'workspace-knowledge-toolkit': {
                name: 'Workspace Knowledge Toolkit',
                category: 'Knowledge Management',
                icon: '🧠',
                description: 'Unified knowledge management and discovery',
                concepts: [
                    {
                        id: 'knowledge-indexing',
                        title: 'Knowledge Indexing',
                        description: 'Systematic knowledge organization',
                        keywords: ['knowledge', 'indexing', 'organization', 'taxonomy'],
                        content: 'Knowledge indexing organization taxonomy cross references search',
                        difficulty: 'intermediate',
                        estimatedTime: 25
                    },
                    {
                        id: 'search-discovery',
                        title: 'Search & Discovery',
                        description: 'Advanced search and content discovery',
                        keywords: ['search', 'discovery', 'content', 'navigation'],
                        content: 'Search discovery semantic indexing content navigation knowledge graph',
                        difficulty: 'intermediate',
                        estimatedTime: 30
                    }
                ]
            }
        };

        // Build searchable index
        for (const [toolkitId, toolkit] of Object.entries(toolkitContent)) {
            this.toolkitMetadata.set(toolkitId, {
                name: toolkit.name,
                category: toolkit.category,
                icon: toolkit.icon,
                description: toolkit.description
            });

            for (const concept of toolkit.concepts) {
                const searchableItem = {
                    id: concept.id,
                    toolkitId,
                    toolkitName: toolkit.name,
                    toolkitCategory: toolkit.category,
                    toolkitIcon: toolkit.icon,
                    title: concept.title,
                    description: concept.description,
                    keywords: concept.keywords,
                    content: concept.content,
                    difficulty: concept.difficulty,
                    estimatedTime: concept.estimatedTime,
                    searchableText: this.createSearchableText(concept),
                    popularity: 0, // Will be updated based on usage
                    lastAccessed: null
                };

                this.searchIndex.set(`${toolkitId}:${concept.id}`, searchableItem);
            }
        }

        console.log(`Search index built with ${this.searchIndex.size} searchable items`);
    }

    createSearchableText(concept) {
        return [
            concept.title,
            concept.description,
            ...concept.keywords,
            concept.content
        ].join(' ').toLowerCase();
    }

    // Main search functionality
    search(query, options = {}) {
        const {
            limit = 20,
            toolkits = null, // Filter by specific toolkits
            categories = null, // Filter by categories
            difficulty = null, // Filter by difficulty
            includeRelated = true,
            sortBy = 'relevance' // 'relevance', 'popularity', 'recency'
        } = options;

        if (!query || query.trim().length < 2) {
            return { results: [], totalResults: 0, suggestions: this.getPopularSearches() };
        }

        const normalizedQuery = query.toLowerCase().trim();
        const queryTerms = normalizedQuery.split(/\s+/);
        
        // Record search
        this.recordSearch(normalizedQuery);

        const results = [];

        // Search through all indexed items
        for (const [key, item] of this.searchIndex.entries()) {
            // Apply filters
            if (toolkits && !toolkits.includes(item.toolkitId)) continue;
            if (categories && !categories.includes(item.toolkitCategory)) continue;
            if (difficulty && item.difficulty !== difficulty) continue;

            // Calculate relevance score
            const relevanceScore = this.calculateRelevanceScore(item, normalizedQuery, queryTerms);
            
            if (relevanceScore > 0) {
                results.push({
                    ...item,
                    relevanceScore,
                    highlightedTitle: this.highlightText(item.title, queryTerms),
                    highlightedDescription: this.highlightText(item.description, queryTerms),
                    relatedConcepts: includeRelated ? this.findRelatedConcepts(item) : []
                });
            }
        }

        // Sort results
        this.sortResults(results, sortBy);

        // Update popularity
        results.slice(0, 5).forEach(result => {
            const item = this.searchIndex.get(`${result.toolkitId}:${result.id}`);
            if (item) {
                item.popularity += 1;
                item.lastAccessed = new Date();
            }
        });

        return {
            results: results.slice(0, limit),
            totalResults: results.length,
            query: normalizedQuery,
            suggestions: results.length === 0 ? this.generateSuggestions(normalizedQuery) : [],
            facets: this.generateFacets(results)
        };
    }

    calculateRelevanceScore(item, query, queryTerms) {
        let score = 0;

        // Exact title match (highest score)
        if (item.title.toLowerCase().includes(query)) {
            score += 10;
        }

        // Title word matches
        queryTerms.forEach(term => {
            if (item.title.toLowerCase().includes(term)) {
                score += 5;
            }
        });

        // Description matches
        queryTerms.forEach(term => {
            if (item.description.toLowerCase().includes(term)) {
                score += 3;
            }
        });

        // Keyword matches
        queryTerms.forEach(term => {
            item.keywords.forEach(keyword => {
                if (keyword.toLowerCase().includes(term)) {
                    score += 4;
                }
                if (keyword.toLowerCase() === term) {
                    score += 6; // Exact keyword match
                }
            });
        });

        // Content matches
        queryTerms.forEach(term => {
            if (item.content.toLowerCase().includes(term)) {
                score += 2;
            }
        });

        // Full text search
        queryTerms.forEach(term => {
            if (item.searchableText.includes(term)) {
                score += 1;
            }
        });

        // Boost by popularity and recency
        score += Math.log(item.popularity + 1) * 0.5;
        
        if (item.lastAccessed) {
            const daysSinceAccess = (Date.now() - item.lastAccessed.getTime()) / (1000 * 60 * 60 * 24);
            score += Math.max(0, 2 - daysSinceAccess * 0.1); // Boost recent items
        }

        return score;
    }

    sortResults(results, sortBy) {
        switch (sortBy) {
            case 'popularity':
                results.sort((a, b) => b.popularity - a.popularity);
                break;
            case 'recency':
                results.sort((a, b) => {
                    const aTime = a.lastAccessed ? a.lastAccessed.getTime() : 0;
                    const bTime = b.lastAccessed ? b.lastAccessed.getTime() : 0;
                    return bTime - aTime;
                });
                break;
            case 'difficulty':
                const difficultyOrder = { 'beginner': 1, 'intermediate': 2, 'advanced': 3, 'expert': 4 };
                results.sort((a, b) => difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]);
                break;
            default: // relevance
                results.sort((a, b) => b.relevanceScore - a.relevanceScore);
        }
    }

    highlightText(text, queryTerms) {
        let highlightedText = text;
        queryTerms.forEach(term => {
            const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        return highlightedText;
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    findRelatedConcepts(item) {
        const related = [];
        const itemKeywords = new Set(item.keywords);

        // Find items with overlapping keywords
        for (const [key, otherItem] of this.searchIndex.entries()) {
            if (key === `${item.toolkitId}:${item.id}`) continue;

            const overlap = otherItem.keywords.filter(keyword => itemKeywords.has(keyword));
            if (overlap.length > 0) {
                related.push({
                    id: otherItem.id,
                    toolkitId: otherItem.toolkitId,
                    title: otherItem.title,
                    similarity: overlap.length / Math.max(itemKeywords.size, otherItem.keywords.length)
                });
            }
        }

        return related
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, 5);
    }

    generateFacets(results) {
        const facets = {
            toolkits: new Map(),
            categories: new Map(),
            difficulties: new Map()
        };

        results.forEach(result => {
            // Toolkit facets
            const toolkitCount = facets.toolkits.get(result.toolkitId) || 0;
            facets.toolkits.set(result.toolkitId, toolkitCount + 1);

            // Category facets
            const categoryCount = facets.categories.get(result.toolkitCategory) || 0;
            facets.categories.set(result.toolkitCategory, categoryCount + 1);

            // Difficulty facets
            const difficultyCount = facets.difficulties.get(result.difficulty) || 0;
            facets.difficulties.set(result.difficulty, difficultyCount + 1);
        });

        return {
            toolkits: Array.from(facets.toolkits.entries()).map(([id, count]) => ({
                id,
                name: this.toolkitMetadata.get(id)?.name || id,
                count
            })),
            categories: Array.from(facets.categories.entries()).map(([category, count]) => ({
                category,
                count
            })),
            difficulties: Array.from(facets.difficulties.entries()).map(([difficulty, count]) => ({
                difficulty,
                count
            }))
        };
    }

    // Search Analytics
    initializeAnalytics() {
        this.loadSearchHistory();
    }

    recordSearch(query) {
        this.searchHistory.unshift({
            query,
            timestamp: new Date(),
            userId: this.getUserId()
        });

        // Keep only last 100 searches
        this.searchHistory = this.searchHistory.slice(0, 100);

        // Update popular searches
        const count = this.popularSearches.get(query) || 0;
        this.popularSearches.set(query, count + 1);

        this.saveSearchHistory();
    }

    getPopularSearches(limit = 10) {
        return Array.from(this.popularSearches.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit)
            .map(([query, count]) => ({ query, count }));
    }

    generateSuggestions(query) {
        const suggestions = [];
        
        // Fuzzy matching for typos
        for (const [key, item] of this.searchIndex.entries()) {
            if (this.fuzzyMatch(query, item.title.toLowerCase(), 0.7) ||
                item.keywords.some(keyword => this.fuzzyMatch(query, keyword.toLowerCase(), 0.7))) {
                suggestions.push(item.title);
            }
        }

        // Add popular searches that contain the query
        this.getPopularSearches(20).forEach(({ query: popularQuery }) => {
            if (popularQuery.includes(query) && !suggestions.includes(popularQuery)) {
                suggestions.push(popularQuery);
            }
        });

        return suggestions.slice(0, 5);
    }

    fuzzyMatch(query, target, threshold = 0.8) {
        const distance = this.levenshteinDistance(query, target);
        const similarity = 1 - distance / Math.max(query.length, target.length);
        return similarity >= threshold;
    }

    levenshteinDistance(str1, str2) {
        const matrix = [];
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        return matrix[str2.length][str1.length];
    }

    // Search Autocompletion
    getAutocompleteSuggestions(partialQuery, limit = 8) {
        if (partialQuery.length < 2) return [];

        const suggestions = new Set();
        const query = partialQuery.toLowerCase();

        // Search in titles and keywords
        for (const [key, item] of this.searchIndex.entries()) {
            // Title matches
            if (item.title.toLowerCase().startsWith(query)) {
                suggestions.add(item.title);
            }
            
            // Keyword matches
            item.keywords.forEach(keyword => {
                if (keyword.toLowerCase().startsWith(query)) {
                    suggestions.add(keyword);
                }
            });

            if (suggestions.size >= limit) break;
        }

        // Add popular searches that start with the query
        this.getPopularSearches().forEach(({ query: popularQuery }) => {
            if (popularQuery.startsWith(query) && suggestions.size < limit) {
                suggestions.add(popularQuery);
            }
        });

        return Array.from(suggestions).slice(0, limit);
    }

    // Storage methods
    loadSearchHistory() {
        try {
            const stored = localStorage.getItem('globalSearch.history');
            if (stored) {
                const data = JSON.parse(stored);
                this.searchHistory = data.searchHistory || [];
                this.popularSearches = new Map(data.popularSearches || []);
            }
        } catch (error) {
            console.error('Failed to load search history:', error);
        }
    }

    saveSearchHistory() {
        try {
            const data = {
                searchHistory: this.searchHistory,
                popularSearches: Array.from(this.popularSearches.entries()),
                lastSaved: new Date().toISOString()
            };
            localStorage.setItem('globalSearch.history', JSON.stringify(data));
        } catch (error) {
            console.error('Failed to save search history:', error);
        }
    }

    getUserId() {
        return localStorage.getItem('unifiedToolkits.userId') || 'anonymous';
    }

    // Public API
    isInitialized() {
        return this.initialized;
    }

    getSearchStatistics() {
        return {
            totalSearchableItems: this.searchIndex.size,
            totalSearches: this.searchHistory.length,
            uniqueQueries: this.popularSearches.size,
            topSearches: this.getPopularSearches(10)
        };
    }

    exportSearchData() {
        return {
            searchHistory: this.searchHistory,
            popularSearches: Array.from(this.popularSearches.entries()),
            searchIndex: Array.from(this.searchIndex.entries()),
            exportedAt: new Date().toISOString()
        };
    }
}

// Global instance
window.GlobalSearchSystem = GlobalSearchSystem;

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
    window.globalSearchSystem = new GlobalSearchSystem();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GlobalSearchSystem;
}