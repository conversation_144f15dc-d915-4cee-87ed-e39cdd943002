# 🏗️ NestJS CRUD: Complete Enterprise Documentation

> **The definitive guide to building enterprise-grade CRUD applications with NestJS, TypeORM, and modern architectural patterns**

---

## 📋 Table of Contents

### 🎯 [Getting Started](#getting-started)
- [Executive Summary](#executive-summary)
- [Quick Start Guide](#quick-start-guide)
- [Installation & Setup](#installation--setup)
- [Monorepo Structure](#monorepo-structure)

### 🏛️ [Architecture & Design](#architecture--design)
- [System Architecture](#system-architecture)
- [Design Patterns](#design-patterns)
- [SOLID Principles](#solid-principles)
- [Clean Architecture](#clean-architecture)

### 🚀 [Implementation Guide](#implementation-guide)
- [Project Structure](#project-structure)
- [Entity Implementation](#entity-implementation)
- [Service Layer](#service-layer)
- [Controller Layer](#controller-layer)
- [Security Implementation](#security-implementation)

### 🔍 [Core Concepts](#core-concepts)
- [NestJSX CRUD Overview](#nestjsx-crud-overview)
- [Request Processing Flow](#request-processing-flow)
- [Query Parsing & Building](#query-parsing--building)
- [TypeORM Integration](#typeorm-integration)
- [Validation Groups](#validation-groups)
- [Soft Delete Support](#soft-delete-support)
- [Advanced Join Options](#advanced-join-options)
- [Search API](#search-api)
- [Authentication Integration](#authentication-integration)

### ⚡ [Performance & Optimization](#performance--optimization)
- [Algorithm Analysis](#algorithm-analysis)
- [Caching Strategies](#caching-strategies)
- [Database Optimization](#database-optimization)
- [Memory Management](#memory-management)

### 🛡️ [Security & Best Practices](#security--best-practices)
- [Security Implementation](#security-implementation-1)
- [Input Validation](#input-validation)
- [Authentication & Authorization](#authentication--authorization)
- [Rate Limiting](#rate-limiting)

### 🧪 [Testing Strategy](#testing-strategy)
- [Unit Testing](#unit-testing)
- [Integration Testing](#integration-testing)
- [End-to-End Testing](#end-to-end-testing)
- [Performance Testing](#performance-testing)

### 🚀 [Deployment & Operations](#deployment--operations)
- [Docker Configuration](#docker-configuration)
- [Production Setup](#production-setup)
- [Monitoring & Observability](#monitoring--observability)
- [CI/CD Pipeline](#cicd-pipeline)

### 🔧 [Advanced Topics](#advanced-topics)
- [Framework Agnostic Implementation](#framework-agnostic-implementation)
- [Microservices Architecture](#microservices-architecture)
- [Event-Driven Patterns](#event-driven-patterns)
- [Custom Extensions](#custom-extensions)

### 📚 [Reference](#reference)
- [API Reference](#api-reference)
- [Configuration Options](#configuration-options)
- [Real-World Examples](#real-world-examples)
- [Troubleshooting](#troubleshooting)
- [Migration Guide](#migration-guide)
- [Contributing](#contributing)

---

## 🎯 Getting Started

### Executive Summary

The NestJS-CRUD package represents a sophisticated implementation of enterprise-grade CRUD operations that seamlessly integrates with modern software architecture principles. This comprehensive documentation combines architectural excellence, design patterns, algorithms, and production-ready practices to provide a complete foundation for building scalable, maintainable, and high-performance applications.

**Key Benefits:**
- ✅ **Rapid Development**: Auto-generated REST controllers with minimal configuration
- ✅ **Enterprise Architecture**: Clean Architecture with SOLID principles
- ✅ **Rich Query Semantics**: Advanced filtering, sorting, pagination, and relations
- ✅ **Type Safety**: Full TypeScript support with comprehensive validation
- ✅ **Production Ready**: Security, monitoring, caching, and performance optimization
- ✅ **Extensible Design**: Plugin architecture with customization points

**Target Audience:**
- **Enterprise Architects** seeking scalable CRUD solutions
- **Senior Developers** implementing production-grade APIs
- **DevOps Engineers** deploying and monitoring CRUD services
- **Technical Leaders** evaluating architectural decisions

### Quick Start Guide

#### 1. Installation

```bash
# Create new NestJS project
npm i -g @nestjs/cli
nest new enterprise-crud-app

# Install NestJS-CRUD packages
npm install @nestjsx/crud @nestjsx/crud-typeorm @nestjsx/crud-request
npm install class-transformer class-validator
npm install typeorm @nestjs/typeorm pg

# Install enterprise packages
npm install @nestjs/swagger @nestjs/jwt @nestjs/passport
npm install helmet compression rate-limiter-flexible
```

#### 2. Basic Entity

```typescript
// user.entity.ts
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { IsEmail, IsString, MinLength } from 'class-validator';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  @IsEmail()
  email: string;

  @Column()
  @IsString()
  @MinLength(2)
  firstName: string;

  @Column()
  @IsString()
  @MinLength(2)
  lastName: string;

  @Column({ default: true })
  isActive: boolean;
}
```

#### 3. Service Implementation

```typescript
// users.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';
import { User } from './user.entity';

@Injectable()
export class UsersService extends TypeOrmCrudService<User> {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {
    super(userRepository);
  }
}
```

#### 4. Controller Setup

```typescript
// users.controller.ts
import { Controller } from '@nestjs/common';
import { Crud, CrudController } from '@nestjsx/crud';
import { User } from './user.entity';
import { UsersService } from './users.service';

@Crud({
  model: { type: User },
  query: {
    maxLimit: 100,
    cache: 2000,
  },
})
@Controller('users')
export class UsersController implements CrudController<User> {
  constructor(public service: UsersService) {}
}
```

#### 5. Module Configuration

```typescript
// users.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './user.entity';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';

@Module({
  imports: [TypeOrmModule.forFeature([User])],
  providers: [UsersService],
  controllers: [UsersController],
})
export class UsersModule {}
```

**Result**: You now have a fully functional CRUD API with:
- `GET /users` - List users with filtering, sorting, pagination
- `GET /users/:id` - Get single user
- `POST /users` - Create user
- `PATCH /users/:id` - Update user
- `DELETE /users/:id` - Delete user

### Installation & Setup

#### Enterprise Project Structure

```
src/
├── 🏗️ core/                    # Core infrastructure
│   ├── database/               # Database configuration
│   ├── security/               # Security middleware
│   ├── monitoring/             # Metrics and logging
│   └── cache/                  # Caching strategies
├── 🧠 domain/                  # Domain layer (DDD)
│   ├── entities/               # Domain entities
│   ├── value-objects/          # Value objects
│   ├── events/                 # Domain events
│   └── services/               # Domain services
├── ⚡ application/              # Application layer
│   ├── services/               # Application services
│   ├── dto/                    # Data transfer objects
│   ├── interfaces/             # Service interfaces
│   └── use-cases/              # Use case implementations
├── 🌐 presentation/            # Presentation layer
│   ├── controllers/            # HTTP controllers
│   ├── guards/                 # Authentication guards
│   ├── interceptors/           # Request/response interceptors
│   └── filters/                # Exception filters
└── 🏗️ infrastructure/          # Infrastructure layer
    ├── repositories/           # Data repositories
    ├── external/               # External service clients
    └── config/                 # Configuration
```

#### Environment Configuration

```typescript
// config/database.config.ts
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export const databaseConfig: TypeOrmModuleOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'enterprise_crud',
  
  // Performance optimizations
  poolSize: 20,
  maxQueryExecutionTime: 1000,
  
  // Connection pooling
  extra: {
    connectionLimit: 20,
    acquireTimeout: 60000,
    timeout: 60000,
  },
  
  autoLoadEntities: true,
  synchronize: process.env.NODE_ENV === 'development',
  migrations: ['dist/migrations/*.js'],
  migrationsRun: true,
};
```

### Monorepo Structure

The NestJS CRUD project is organized as a **Lerna monorepo** with multiple packages:

#### Package Architecture

```
packages/
├── 🧠 crud/                    # Core CRUD functionality
│   ├── src/
│   │   ├── decorators/         # @Crud(), @Override(), @ParsedRequest()
│   │   ├── interceptors/       # CrudRequestInterceptor, CrudResponseInterceptor
│   │   ├── interfaces/         # CrudController, CrudOptions, CrudRequest
│   │   ├── services/           # CrudService abstract class
│   │   ├── crud/               # CrudRoutesFactory, SwaggerHelper
│   │   └── module/             # CrudConfigService
│   └── package.json            # @nestjsx/crud
│
├── 🔍 crud-request/            # Request parsing & building
│   ├── src/
│   │   ├── request-query.builder.ts    # Client-side query builder
│   │   ├── request-query.parser.ts     # Server-side query parser
│   │   ├── request-query.validator.ts  # Query validation
│   │   ├── interfaces/         # ParsedRequest, QueryFilter types
│   │   └── types/              # Request parameter types
│   └── package.json            # @nestjsx/crud-request
│
├── 🗄️ crud-typeorm/            # TypeORM integration
│   ├── src/
│   │   ├── typeorm-crud.service.ts     # TypeORM CRUD service
│   │   └── index.ts
│   └── package.json            # @nestjsx/crud-typeorm
│
└── 🔧 util/                    # Shared utilities
    ├── src/
    │   └── index.ts             # Common utility functions
    └── package.json             # @nestjsx/util
```

#### Package Dependencies

```typescript
// Dependency flow (no circular dependencies)
@nestjsx/util → (base utilities)
@nestjsx/crud-request → @nestjsx/util
@nestjsx/crud → @nestjsx/crud-request + @nestjsx/util
@nestjsx/crud-typeorm → @nestjsx/crud + @nestjsx/crud-request
```

#### Development Setup

```bash
# Clone the repository
git clone https://github.com/nestjsx/crud.git
cd crud

# Install dependencies and bootstrap packages
yarn install
yarn boot

# Build all packages
yarn build

# Run tests
yarn test

# Start development server with TypeORM integration
yarn start:typeorm
```

#### Lerna Configuration

```json
{
  "packages": ["packages/*"],
  "npmClient": "yarn",
  "useWorkspaces": true,
  "version": "5.0.0-alpha.3",
  "command": {
    "publish": {
      "message": "chore: release"
    }
  }
}
```

#### Package Versions

| Package | Version | Description |
|---------|---------|-------------|
| `@nestjsx/crud` | 5.0.0-alpha.3 | Core CRUD functionality |
| `@nestjsx/crud-request` | 5.0.0-alpha.3 | Request parsing & building |
| `@nestjsx/crud-typeorm` | 5.0.0-alpha.3 | TypeORM integration |
| `@nestjsx/util` | 5.0.0-alpha.3 | Shared utilities |

---

## 🏛️ Architecture & Design

### System Architecture

The NestJS-CRUD package follows Clean Architecture principles with clear separation of concerns:

```typescript
┌─────────────────────────────────────────────────────────┐
│                    🌐 PRESENTATION LAYER                │
│  @Crud() Decorator │ Controllers │ HTTP Endpoints       │
├─────────────────────────────────────────────────────────┤
│                   ⚡ APPLICATION LAYER                   │
│  CrudService │ Business Logic │ Validation │ DTOs       │
├─────────────────────────────────────────────────────────┤
│                    🧠 DOMAIN LAYER                      │
│  Entities │ Value Objects │ Domain Events │ Rules       │
├─────────────────────────────────────────────────────────┤
│                 🏗️ INFRASTRUCTURE LAYER                 │
│  TypeOrmCrudService │ Database │ External APIs          │
└─────────────────────────────────────────────────────────┘
```

### Design Patterns

#### 1. Repository Pattern ⭐⭐⭐
```

#### 3. Builder Pattern ⭐⭐⭐
```typescript
// RequestQueryBuilder for complex query construction
export class RequestQueryBuilder {
  public queryObject: { [key: string]: any } = {};

  select(fields: QueryFields): this {
    if (isArrayFull(fields)) {
      this.queryObject[this.paramNames.fields] = fields.join(this.options.delimStr);
    }
    return this;
  }

  setFilter(f: QueryFilter): this {
    this.setCondition(f, 'filter');
    return this;
  }

  sortBy(s: QuerySort): this {
    // Build sorting logic
    return this;
  }

  query(): string {
    return stringify(this.queryObject);
  }
}
```

#### 4. Strategy Pattern ⭐⭐⭐
```typescript
// Different strategies for query operations
interface QueryStrategy {
  execute(query: ParsedRequest): Promise<any>;
}

class PostgresQueryStrategy implements QueryStrategy {
  execute(query: ParsedRequest): Promise<any> {
    // PostgreSQL-specific implementation
  }
}

class MySQLQueryStrategy implements QueryStrategy {
  execute(query: ParsedRequest): Promise<any> {
    // MySQL-specific implementation
  }
}
```

#### 5. Factory Pattern ⭐⭐
```typescript
// CrudRoutesFactory generates endpoints dynamically
export class CrudRoutesFactory {
  static create(target: any, options: CrudOptions): void {
    // Factory method creates appropriate routes based on configuration
    this.createGetManyRoute(target, options);
    this.createGetOneRoute(target, options);
    this.createCreateOneRoute(target, options);
    // ... other routes
  }
}
```

### SOLID Principles

#### Single Responsibility Principle (SRP) ✅
- **CrudService**: Handles only CRUD operations
- **RequestQueryBuilder**: Responsible only for query building
- **TypeOrmCrudService**: Focuses on TypeORM-specific database operations
- **Validators**: Handle only input validation

#### Open/Closed Principle (OCP) ✅
```typescript
// Open for extension through inheritance
export class CustomUserService extends TypeOrmCrudService<User> {
  // Extend functionality without modifying base class
  async createUser(dto: CreateUserDto): Promise<User> {
    // Custom business logic
    const user = await this.createOne(req, dto);
    await this.sendWelcomeEmail(user);
    return user;
  }
}
```

#### Liskov Substitution Principle (LSP) ✅
```typescript
// Any CrudService implementation can be substituted
function processEntity<T>(service: CrudService<T>, req: CrudRequest): Promise<T[]> {
  return service.getMany(req); // Works with any CrudService implementation
}
```

#### Interface Segregation Principle (ISP) ✅
```typescript
// Separate interfaces for different concerns
interface CrudReader<T> {
  getMany(req: CrudRequest): Promise<T[]>;
  getOne(req: CrudRequest): Promise<T>;
}

interface CrudWriter<T> {
  createOne(req: CrudRequest, dto: T): Promise<T>;
  updateOne(req: CrudRequest, dto: T): Promise<T>;
  deleteOne(req: CrudRequest): Promise<void>;
}
```

#### Dependency Inversion Principle (DIP) ✅
```typescript
// Depends on abstractions, not concretions
export class TypeOrmCrudService<T> extends CrudService<T> {
  constructor(
    protected repo: Repository<T>, // Abstraction
    private cache: ICacheService,  // Abstraction
    private logger: ILogger        // Abstraction
  ) {
    super();
  }
}
```

### Clean Architecture

The implementation follows Clean Architecture with these key principles:

1. **Dependency Rule**: Dependencies point inward toward the domain
2. **Separation of Concerns**: Each layer has distinct responsibilities
3. **Independence**: Business logic is independent of frameworks and databases
4. **Testability**: Each layer can be tested in isolation

---

## 🚀 Implementation Guide

### Project Structure

#### Unified Enterprise Structure

```
nestjs-crud-enterprise/
├── 📦 package.json                  # Dependencies and scripts
├── 📋 tsconfig.json                 # TypeScript configuration
├── 🔧 jest.config.js                # Testing configuration
├── 🐳 Dockerfile                    # Container configuration
├── 🐳 docker-compose.yml            # Development environment
│
├── 🏗️ src/                          # Main source code
│   ├── 🧠 core/                     # Core CRUD functionality
│   │   ├── decorators/              # Metadata decorators
│   │   ├── factories/               # Route and metadata factories
│   │   ├── interceptors/            # Cross-cutting concerns
│   │   ├── services/                # Abstract service layer
│   │   ├── guards/                  # Security guards
│   │   └── pipes/                   # Validation pipes
│   │
│   ├── 🔍 request/                  # Request parsing & building
│   │   ├── parser/                  # Query string parsing
│   │   ├── builder/                 # Query building (client-side)
│   │   ├── validators/              # Input validation
│   │   ├── types/                   # Request types & interfaces
│   │   └── exceptions/              # Request-specific exceptions
│   │
│   ├── 🗄️ typeorm/                  # TypeORM integration
│   │   ├── service/                 # TypeORM service implementation
│   │   ├── repository/              # Repository patterns
│   │   ├── utils/                   # TypeORM utilities
│   │   └── decorators/              # TypeORM-specific decorators
│   │
│   ├── 🔧 common/                   # Shared utilities
│   │   ├── utils/                   # Helper functions
│   │   ├── constants/               # Application constants
│   │   ├── exceptions/              # Custom exceptions
│   │   ├── interfaces/              # Common interfaces
│   │   ├── dto/                     # Data Transfer Objects
│   │   └── enums/                   # Enumeration types
│   │
│   └── 🔌 modules/                  # Feature modules
│       ├── crud.module.ts           # Main CRUD module
│       └── typeorm-crud.module.ts   # TypeORM CRUD module
│
├── 📚 docs/                         # Documentation
├── 🧪 tests/                        # Test suites
├── 📋 examples/                     # Usage examples
├── 🔧 tools/                        # Build and development tools
├── 🚀 scripts/                      # Utility scripts
└── 📊 monitoring/                   # Monitoring configuration
```

### Entity Implementation

#### Domain Entity with Design Patterns

```typescript
// domain/entities/user.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from 'typeorm';
import { IsEmail, IsString, MinLength, MaxLength } from 'class-validator';
import { Exclude, Transform } from 'class-transformer';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  @IsEmail()
  email: string;

  @Column()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  firstName: string;

  @Column()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  lastName: string;

  @Column()
  @Exclude() // Security: Exclude from serialization
  @MinLength(8)
  password: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  lastLoginAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  // Domain method - Business logic encapsulation
  public activate(): void {
    this.isActive = true;
  }

  public deactivate(): void {
    this.isActive = false;
  }

  // Value object pattern for full name
  @Transform(({ obj }) => `${obj.firstName} ${obj.lastName}`)
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  // Factory method pattern
  public static create(email: string, firstName: string, lastName: string, password: string): User {
    const user = new User();
    user.email = email;
    user.firstName = firstName;
    user.lastName = lastName;
    user.password = password;
    user.isActive = true;
    return user;
  }
}
```

### Service Layer

#### Enterprise Service Implementation with SOLID Principles

```typescript
// application/services/users.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';
import { CrudRequest } from '@nestjsx/crud';
import { User } from '../../domain/entities/user.entity';
import { CreateUserDto, UpdateUserDto } from '../dto/user.dto';
import { IUserService } from '../interfaces/user-service.interface';
import { ICacheService } from '../../core/cache/cache-service.interface';
import { IEventBus } from '../../core/events/event-bus.interface';
import { UserCreatedEvent, UserUpdatedEvent } from '../../domain/events/user.events';

// Single Responsibility Principle: Handles only User CRUD operations
@Injectable()
export class UsersService extends TypeOrmCrudService<User> implements IUserService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,

    // Dependency Inversion Principle: Depend on abstractions
    private readonly cacheService: ICacheService,
    private readonly eventBus: IEventBus,
  ) {
    super(userRepository);
  }

  // Template Method Pattern: Override base behavior
  async createOne(req: CrudRequest, dto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating user with email: ${dto.email}`);

    try {
      // Business logic validation
      await this.validateUniqueEmail(dto.email);

      // Hash password (security best practice)
      const hashedPassword = await this.hashPassword(dto.password);

      // Factory method pattern
      const user = User.create(dto.email, dto.firstName, dto.lastName, hashedPassword);

      // Save to database
      const savedUser = await super.createOne(req, user);

      // Cache the new user (performance optimization)
      await this.cacheService.set(`user:${savedUser.id}`, savedUser, 3600);

      // Publish domain event (event-driven architecture)
      await this.eventBus.publish(new UserCreatedEvent(savedUser));

      this.logger.log(`User created successfully: ${savedUser.id}`);
      return savedUser;

    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Open/Closed Principle: Extend functionality without modifying base class
  async updateOne(req: CrudRequest, dto: UpdateUserDto): Promise<User> {
    const userId = req.parsed.paramsFilter[0].value;

    try {
      // Get existing user from cache first (performance optimization)
      let existingUser = await this.cacheService.get(`user:${userId}`);

      if (!existingUser) {
        existingUser = await this.getOneOrFail(req);
        await this.cacheService.set(`user:${userId}`, existingUser, 3600);
      }

      // Business logic: Update last login if password changed
      if (dto.password) {
        dto.password = await this.hashPassword(dto.password);
        dto.lastLoginAt = new Date();
      }

      const updatedUser = await super.updateOne(req, dto);

      // Update cache
      await this.cacheService.set(`user:${updatedUser.id}`, updatedUser, 3600);

      // Publish domain event
      await this.eventBus.publish(new UserUpdatedEvent(updatedUser, existingUser));

      return updatedUser;

    } catch (error) {
      this.logger.error(`Failed to update user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Interface Segregation Principle: Specific methods for specific needs
  async findByEmail(email: string): Promise<User | null> {
    const cacheKey = `user:email:${email}`;

    // Try cache first (O(1) lookup)
    let user = await this.cacheService.get(cacheKey);

    if (!user) {
      // Database lookup (O(log n) with index)
      user = await this.userRepository.findOne({ where: { email } });

      if (user) {
        await this.cacheService.set(cacheKey, user, 3600);
      }
    }

    return user;
  }

  async activateUser(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new Error('User not found');
    }

    // Domain method encapsulates business logic
    user.activate();

    const savedUser = await this.userRepository.save(user);

    // Update cache
    await this.cacheService.set(`user:${userId}`, savedUser, 3600);

    return savedUser;
  }

  // Private methods for internal logic
  private async validateUniqueEmail(email: string): Promise<void> {
    const existingUser = await this.findByEmail(email);

    if (existingUser) {
      throw new Error('Email already exists');
    }
  }

  private async hashPassword(password: string): Promise<string> {
    const bcrypt = await import('bcrypt');
    return bcrypt.hash(password, 12);
  }
}
```

#### Interface Definition (ISP)

```typescript
// application/interfaces/user-service.interface.ts
import { CrudRequest } from '@nestjsx/crud';
import { User } from '../../domain/entities/user.entity';
import { CreateUserDto, UpdateUserDto } from '../dto/user.dto';

// Interface Segregation Principle: Separate interfaces for different concerns
export interface IUserReader {
  getMany(req: CrudRequest): Promise<User[]>;
  getOne(req: CrudRequest): Promise<User>;
  findByEmail(email: string): Promise<User | null>;
}

export interface IUserWriter {
  createOne(req: CrudRequest, dto: CreateUserDto): Promise<User>;
  updateOne(req: CrudRequest, dto: UpdateUserDto): Promise<User>;
  deleteOne(req: CrudRequest): Promise<void>;
}

export interface IUserService extends IUserReader, IUserWriter {
  activateUser(userId: string): Promise<User>;
  deactivateUser(userId: string): Promise<User>;
}
```

### Controller Layer

#### Clean Architecture Controller

```typescript
// presentation/controllers/users.controller.ts
import {
  Controller,
  UseGuards,
  UseInterceptors,
  Logger,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query
} from '@nestjs/common';
import {
  Crud,
  CrudController,
  CrudRequest,
  ParsedRequest,
  CreateManyDto,
  Override
} from '@nestjsx/crud';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { User } from '../../domain/entities/user.entity';
import { UsersService } from '../../application/services/users.service';
import { CreateUserDto, UpdateUserDto, UserResponseDto } from '../../application/dto/user.dto';
import { JwtAuthGuard } from '../../core/security/jwt-auth.guard';
import { RolesGuard } from '../../core/security/roles.guard';
import { Roles } from '../../core/security/roles.decorator';
import { LoggingInterceptor } from '../../core/monitoring/logging.interceptor';
import { CacheInterceptor } from '../../core/cache/cache.interceptor';
import { RateLimitGuard } from '../../core/security/rate-limit.guard';

@ApiTags('Users')
@ApiBearerAuth()
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard, RateLimitGuard)
@UseInterceptors(LoggingInterceptor, CacheInterceptor)
@Crud({
  model: {
    type: User,
  },
  dto: {
    create: CreateUserDto,
    update: UpdateUserDto,
    replace: UpdateUserDto,
  },
  serialize: {
    get: UserResponseDto,
    getMany: UserResponseDto,
    create: UserResponseDto,
    update: UserResponseDto,
    replace: UserResponseDto,
  },
  query: {
    // Performance optimization: Limit max results
    maxLimit: 100,
    // Security: Exclude sensitive fields
    exclude: ['password'],
    // Caching strategy
    cache: 2000,
    // Soft delete support
    softDelete: true,
    // Join configuration for related entities
    join: {
      profile: { eager: true },
      roles: { eager: false, allow: ['name'] },
    },
    // Sorting configuration
    sort: [
      { field: 'createdAt', order: 'DESC' },
    ],
  },
  routes: {
    // Security: Restrict certain operations
    only: ['getManyBase', 'getOneBase', 'createOneBase', 'updateOneBase'],
    getManyBase: {
      decorators: [
        Roles('admin', 'user'),
      ],
    },
    getOneBase: {
      decorators: [
        Roles('admin', 'user'),
      ],
    },
    createOneBase: {
      decorators: [
        Roles('admin'),
      ],
    },
    updateOneBase: {
      decorators: [
        Roles('admin', 'user'),
      ],
    },
  },
  params: {
    id: {
      field: 'id',
      type: 'uuid',
      primary: true,
    },
  },
})
export class UsersController implements CrudController<User> {
  private readonly logger = new Logger(UsersController.name);

  constructor(public service: UsersService) {}

  // Override base method to add custom logic
  @Override()
  @ApiOperation({ summary: 'Get many users with advanced filtering' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully', type: [UserResponseDto] })
  async getMany(@ParsedRequest() req: CrudRequest) {
    this.logger.log(`Getting users with filters: ${JSON.stringify(req.parsed.filter)}`);

    // Custom business logic before calling service
    const result = await this.service.getMany(req);

    // Custom response transformation
    if (Array.isArray(result)) {
      return {
        data: result,
        count: result.length,
        total: result.length,
        page: 1,
        pageCount: 1,
      };
    }

    return result;
  }

  @Override()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully', type: UserResponseDto })
  async createOne(@ParsedRequest() req: CrudRequest, @Body() dto: CreateUserDto) {
    this.logger.log(`Creating user: ${dto.email}`);

    // Additional validation or business logic
    if (!dto.email || !dto.password) {
      throw new Error('Email and password are required');
    }

    return this.service.createOne(req, dto);
  }

  // Custom endpoint for user activation
  @Post(':id/activate')
  @Roles('admin')
  @ApiOperation({ summary: 'Activate a user' })
  @ApiResponse({ status: 200, description: 'User activated successfully', type: UserResponseDto })
  async activateUser(@Param('id') id: string): Promise<User> {
    this.logger.log(`Activating user: ${id}`);
    return this.service.activateUser(id);
  }

  // Custom endpoint for user deactivation
  @Post(':id/deactivate')
  @Roles('admin')
  @ApiOperation({ summary: 'Deactivate a user' })
  @ApiResponse({ status: 200, description: 'User deactivated successfully', type: UserResponseDto })
  async deactivateUser(@Param('id') id: string): Promise<User> {
    this.logger.log(`Deactivating user: ${id}`);
    return this.service.deactivateUser(id);
  }

  // Custom endpoint for finding user by email
  @Get('by-email/:email')
  @Roles('admin')
  @ApiOperation({ summary: 'Find user by email' })
  @ApiResponse({ status: 200, description: 'User found', type: UserResponseDto })
  async findByEmail(@Param('email') email: string): Promise<User | null> {
    this.logger.log(`Finding user by email: ${email}`);
    return this.service.findByEmail(email);
  }
}
```

---

## 🔍 Core Concepts

### NestJSX CRUD Overview

#### What it is
- Auto-generates REST controllers with rich query semantics using minimal config
- Monorepo with three key packages:
  - **@nestjsx/crud**: Crud() decorator, route factory, interceptors, global config
  - **@nestjsx/crud-request**: RequestQueryParser/RequestQueryBuilder; operators, joins, pagination
  - **@nestjsx/crud-typeorm**: TypeOrmCrudService that maps parsed requests into TypeORM QueryBuilder

#### Core Flow
1. **CrudConfigService.load()**: Set global defaults
2. **@Crud({...})**: Stores metadata and invokes CrudRoutesFactory to synthesize routes and swagger + interceptors
3. **CrudRequestInterceptor**: Parses query (?filter, ?sort, ?join, ?page/limit, ?s search, params, soft-delete) → ParsedRequestParams
4. **TypeOrmCrudService**: Converts parsed params to TypeORM QueryBuilder (joins, where, select, order, paginate, cache)
5. **CrudResponseInterceptor**: Serializes data to DTOs or pagination envelopes per serialize options

#### Key Subsystems
- **CrudRoutesFactory**: Builds route methods; respects routes.only/exclude, params, DTO groups; sets OpenAPI
- **RequestQueryParser**: Validates/normalizes query; operators $eq,$ne,$gt,$lt,$gte,$lte,$starts,$ends,$cont,$excl,$in,$notin,$isnull,$notnull,$between (+ case-insensitive variants)
- **TypeOrmCrudService**: Safe SQL mapping with params; joins (eager/nested), fields allow/exclude, soft delete, pagination, cache
- **Interceptors**: CrudRequestInterceptor, CrudResponseInterceptor
- **Swagger**: Auto query params, path params, response models; dynamic GetManyXResponseDto
- **Validation**: DTOs and bulk create DTOs via class-validator/transformer

### Request Processing Flow

```typescript
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   HTTP Request  │───▶│ CrudRequestInter │───▶│ ParsedRequest   │
│   ?filter=...   │    │ ceptor           │    │ Params          │
│   ?sort=...     │    │                  │    │                 │
│   ?join=...     │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ HTTP Response   │◀───│ CrudResponseInter│◀───│ TypeOrmCrud     │
│ (JSON/DTO)      │    │ ceptor           │    │ Service         │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Query Parsing & Building

#### Supported Query Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `$eq` | Equals | `?filter=name||$eq||John` |
| `$ne` | Not equals | `?filter=status||$ne||inactive` |
| `$gt` | Greater than | `?filter=age||$gt||18` |
| `$gte` | Greater than or equal | `?filter=age||$gte||21` |
| `$lt` | Less than | `?filter=price||$lt||100` |
| `$lte` | Less than or equal | `?filter=price||$lte||50` |
| `$starts` | Starts with | `?filter=name||$starts||Jo` |
| `$ends` | Ends with | `?filter=email||$ends||@gmail.com` |
| `$cont` | Contains | `?filter=description||$cont||important` |
| `$excl` | Excludes | `?filter=tags||$excl||deprecated` |
| `$in` | In array | `?filter=status||$in||active,pending` |
| `$notin` | Not in array | `?filter=role||$notin||admin,super` |
| `$isnull` | Is null | `?filter=deletedAt||$isnull` |
| `$notnull` | Is not null | `?filter=lastLoginAt||$notnull` |
| `$between` | Between values | `?filter=createdAt||$between||2023-01-01,2023-12-31` |

#### Advanced Query Examples

```typescript
// Complex filtering with multiple conditions
GET /users?filter=isActive||$eq||true&filter=age||$gte||18&filter=role||$in||user,admin

// Sorting by multiple fields
GET /users?sort=lastName,ASC&sort=firstName,ASC

// Pagination with limit and offset
GET /users?limit=20&offset=40

// Field selection
GET /users?fields=id,email,firstName,lastName

// Joining related entities
GET /users?join=profile&join=roles

// Search across multiple fields
GET /users?s={"$or":[{"firstName":{"$cont":"john"}},{"lastName":{"$cont":"john"}}]}

// Combining all features
GET /users?filter=isActive||$eq||true&sort=createdAt,DESC&limit=10&fields=id,email,firstName&join=profile
```

#### RequestQueryBuilder (Client-side)

```typescript
import { RequestQueryBuilder } from '@nestjsx/crud-request';

const queryBuilder = RequestQueryBuilder.create()
  .setFilter({
    field: 'isActive',
    operator: '$eq',
    value: true,
  })
  .setFilter({
    field: 'age',
    operator: '$gte',
    value: 18,
  })
  .sortBy({
    field: 'createdAt',
    order: 'DESC',
  })
  .setLimit(20)
  .setOffset(0)
  .select(['id', 'email', 'firstName', 'lastName'])
  .setJoin({
    field: 'profile',
    select: ['avatar', 'bio'],
  });

const queryString = queryBuilder.query();
// Result: ?filter=isActive||$eq||true&filter=age||$gte||18&sort=createdAt,DESC&limit=20&offset=0&fields=id,email,firstName,lastName&join=profile||avatar,bio
```

### TypeORM Integration

#### Service Implementation

```typescript
export class TypeOrmCrudService<T> extends CrudService<T> {
  constructor(protected repo: Repository<T>) {
    super();
  }

  // Convert parsed request to TypeORM QueryBuilder
  async createBuilder(
    parsed: ParsedRequestParams,
    options: CrudRequestOptions
  ): Promise<SelectQueryBuilder<T>> {
    const builder = this.repo.createQueryBuilder(this.alias);

    // Apply field selection
    this.setSelect(builder, parsed, options);

    // Apply joins
    this.setJoin(builder, parsed, options);

    // Apply filters
    this.setWhere(builder, parsed, options);

    // Apply sorting
    this.setOrder(builder, parsed, options);

    // Apply pagination
    this.setPagination(builder, parsed, options);

    // Apply caching
    this.setCache(builder, parsed, options);

    return builder;
  }

  // Safe parameter binding to prevent SQL injection
  private setWhere(
    builder: SelectQueryBuilder<T>,
    parsed: ParsedRequestParams,
    options: CrudRequestOptions
  ): void {
    const filters = parsed.filter || [];

    filters.forEach((filter, index) => {
      const paramName = `param_${index}`;
      const condition = this.buildCondition(filter, paramName);

      if (index === 0) {
        builder.where(condition, { [paramName]: filter.value });
      } else {
        builder.andWhere(condition, { [paramName]: filter.value });
      }
    });
  }

  // Build safe SQL conditions with parameter binding
  private buildCondition(filter: QueryFilter, paramName: string): string {
    const { field, operator } = filter;
    const column = `${this.alias}.${field}`;

    switch (operator) {
      case '$eq':
        return `${column} = :${paramName}`;
      case '$ne':
        return `${column} != :${paramName}`;
      case '$gt':
        return `${column} > :${paramName}`;
      case '$gte':
        return `${column} >= :${paramName}`;
      case '$lt':
        return `${column} < :${paramName}`;
      case '$lte':
        return `${column} <= :${paramName}`;
      case '$starts':
        return `${column} LIKE :${paramName}`;
      case '$ends':
        return `${column} LIKE :${paramName}`;
      case '$cont':
        return `${column} LIKE :${paramName}`;
      case '$excl':
        return `${column} NOT LIKE :${paramName}`;
      case '$in':
        return `${column} IN (:...${paramName})`;
      case '$notin':
        return `${column} NOT IN (:...${paramName})`;
      case '$isnull':
        return `${column} IS NULL`;
      case '$notnull':
        return `${column} IS NOT NULL`;
      case '$between':
        return `${column} BETWEEN :${paramName}_start AND :${paramName}_end`;
      default:
        throw new Error(`Unsupported operator: ${operator}`);
    }
  }
}
```

### Validation Groups

NestJS CRUD provides built-in validation groups for different CRUD operations:

#### CrudValidationGroups

```typescript
import { CrudValidationGroups } from '@nestjsx/crud';

const { CREATE, UPDATE } = CrudValidationGroups;

@Entity('users')
export class User {
  @IsOptional({ groups: [UPDATE] })
  @IsNotEmpty({ groups: [CREATE] })
  @IsString({ always: true })
  @MaxLength(255, { always: true })
  @IsEmail({ require_tld: false }, { always: true })
  @Column({ type: 'varchar', length: 255, nullable: false, unique: true })
  email: string;

  @IsOptional({ groups: [UPDATE] })
  @IsNotEmpty({ groups: [CREATE] })
  @IsBoolean({ always: true })
  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  // Nested validation with groups
  @IsOptional({ groups: [UPDATE] })
  @IsNotEmpty({ groups: [CREATE] })
  @ValidateNested({ always: true })
  @Type(() => UserProfile)
  @OneToOne(() => UserProfile, (p) => p.user, { cascade: true })
  @JoinColumn()
  profile?: UserProfile;
}
```

#### Validation Group Usage

| Group | Usage | Description |
|-------|-------|-------------|
| `CREATE` | POST requests | Validates required fields for creation |
| `UPDATE` | PATCH/PUT requests | Validates fields for updates (optional) |
| `always: true` | All operations | Always validate regardless of group |

#### Custom Validation Groups

```typescript
// Define custom validation groups
export const CustomValidationGroups = {
  ADMIN_CREATE: 'ADMIN_CREATE',
  USER_UPDATE: 'USER_UPDATE',
} as const;

@Entity('users')
export class User {
  @IsOptional({ groups: [UPDATE, CustomValidationGroups.USER_UPDATE] })
  @IsNotEmpty({ groups: [CREATE, CustomValidationGroups.ADMIN_CREATE] })
  @Column()
  adminOnlyField: string;
}
```

### Soft Delete Support

NestJS CRUD provides comprehensive soft delete functionality:

#### Entity Configuration

```typescript
import { DeleteDateColumn } from 'typeorm';

@Entity('users')
export class User {
  @DeleteDateColumn({ nullable: true })
  deletedAt?: Date;

  // Other fields...
}
```

#### Controller Configuration

```typescript
@Crud({
  model: { type: User },
  query: {
    softDelete: true, // Enable soft delete support
  },
  routes: {
    deleteOneBase: {
      returnDeleted: true, // Return deleted entity
    },
  },
})
@Controller('users')
export class UsersController implements CrudController<User> {
  constructor(public service: UsersService) {}
}
```

#### Query Parameters for Soft Delete

```typescript
// Include soft deleted records
GET /users?includeDeleted=1

// Only soft deleted records
GET /users?includeDeleted=2

// Exclude soft deleted records (default)
GET /users
```

#### Service Implementation

```typescript
export class UsersService extends TypeOrmCrudService<User> {
  // Soft delete is handled automatically by the base service
  // Custom soft delete logic can be added here

  async restoreOne(id: string): Promise<User> {
    const user = await this.repo.findOne({
      where: { id },
      withDeleted: true
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.deletedAt = null;
    return this.repo.save(user);
  }
}
```

### Advanced Join Options

NestJS CRUD supports sophisticated join configurations:

#### Basic Join Configuration

```typescript
@Crud({
  model: { type: User },
  query: {
    join: {
      // Simple join
      profile: {
        eager: true,
        exclude: ['updatedAt'],
      },

      // Join with alias
      company: {
        alias: 'comp',
        exclude: ['description'],
      },

      // Nested join
      'company.projects': {
        alias: 'proj',
        exclude: ['description'],
      },

      // Join without selection
      roles: {
        select: false, // Join but don't select
      },
    },
  },
})
```

#### Advanced Join Examples

```typescript
@Crud({
  model: { type: User },
  query: {
    join: {
      // Eager loading with specific fields
      profile: {
        eager: true,
        allow: ['avatar', 'bio'], // Only allow these fields
        exclude: ['sensitiveData'], // Exclude these fields
      },

      // Conditional joins
      'profile.settings': {
        required: false, // LEFT JOIN instead of INNER JOIN
        alias: 'settings',
      },

      // Multiple level nesting
      'company.projects.tasks': {
        alias: 'tasks',
        allow: ['title', 'status'],
      },
    },
  },
})
```

#### Runtime Join Queries

```typescript
// Client-side join building
const query = RequestQueryBuilder.create()
  .setJoin({
    field: 'profile',
    select: ['avatar', 'bio'],
  })
  .setJoin({
    field: 'company.projects',
    select: ['name', 'status'],
  })
  .query();

// Result: ?join=profile||avatar,bio&join=company.projects||name,status
```

### Search API

NestJS CRUD provides a powerful search API for complex queries:

#### Basic Search

```typescript
// Simple search across multiple fields
GET /users?s={"name":{"$cont":"john"}}

// Case insensitive search
GET /users?s={"email":{"$contL":"JOHN"}}
```

#### Advanced Search Queries

```typescript
// OR conditions
GET /users?s={"$or":[{"firstName":{"$cont":"john"}},{"lastName":{"$cont":"doe"}}]}

// AND conditions with nested OR
GET /users?s={"isActive":true,"$or":[{"email":{"$cont":"gmail"}},{"email":{"$cont":"yahoo"}}]}

// Complex nested search
GET /users?s={"$and":[{"isActive":true},{"$or":[{"age":{"$gte":18}},{"role":"admin"}]}]}
```

#### Search with Joins

```typescript
// Search in joined relations
GET /users?join=profile&s={"profile.bio":{"$cont":"developer"}}

// Multiple relation search
GET /users?join=company&join=profile&s={"$or":[{"company.name":{"$cont":"tech"}},{"profile.skills":{"$in":["javascript","typescript"]}}]}
```

#### Client-side Search Builder

```typescript
import { RequestQueryBuilder } from '@nestjsx/crud-request';

const query = RequestQueryBuilder.create()
  .search({
    $or: [
      { firstName: { $cont: 'john' } },
      { lastName: { $cont: 'doe' } },
    ],
  })
  .setJoin({ field: 'profile' })
  .query();
```

#### Search Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `$cont` | Contains | `{"name":{"$cont":"john"}}` |
| `$contL` | Contains (case insensitive) | `{"name":{"$contL":"JOHN"}}` |
| `$starts` | Starts with | `{"email":{"$starts":"admin"}}` |
| `$startsL` | Starts with (case insensitive) | `{"email":{"$startsL":"ADMIN"}}` |
| `$ends` | Ends with | `{"email":{"$ends":"@gmail.com"}}` |
| `$endsL` | Ends with (case insensitive) | `{"email":{"$endsL":"@GMAIL.COM"}}` |
| `$eq` | Equals | `{"isActive":{"$eq":true}}` |
| `$ne` | Not equals | `{"status":{"$ne":"deleted"}}` |
| `$gt` | Greater than | `{"age":{"$gt":18}}` |
| `$gte` | Greater than or equal | `{"age":{"$gte":21}}` |
| `$lt` | Less than | `{"price":{"$lt":100}}` |
| `$lte` | Less than or equal | `{"price":{"$lte":50}}` |
| `$in` | In array | `{"status":{"$in":["active","pending"]}}` |
| `$notin` | Not in array | `{"role":{"$notin":["admin","super"]}}` |
| `$isnull` | Is null | `{"deletedAt":{"$isnull":true}}` |
| `$notnull` | Is not null | `{"lastLoginAt":{"$notnull":true}}` |
| `$between` | Between values | `{"createdAt":{"$between":["2023-01-01","2023-12-31"]}}` |

### Authentication Integration

NestJS CRUD provides built-in authentication support:

#### @CrudAuth Decorator

```typescript
import { CrudAuth } from '@nestjsx/crud';

@CrudAuth({
  property: 'user',
  filter: (user) => ({
    companyId: user.companyId, // Filter by user's company
  }),
  persist: (user) => ({
    userId: user.id, // Auto-set userId on create/update
  }),
})
@Crud({
  model: { type: User },
})
@Controller('users')
export class UsersController implements CrudController<User> {
  constructor(public service: UsersService) {}
}
```

#### Auth Configuration Options

```typescript
interface CrudAuthOptions {
  property: string; // Request property containing user info
  filter?: (user: any) => any; // Auto-apply filters based on user
  persist?: (user: any) => any; // Auto-persist user data
  classTransformOptions?: ClassTransformOptions;
}
```

#### Advanced Auth Example

```typescript
@CrudAuth({
  property: 'user',
  filter: (user: AuthUser) => {
    // Multi-tenant filtering
    const baseFilter = { tenantId: user.tenantId };

    // Role-based filtering
    if (user.role === 'admin') {
      return baseFilter; // Admins see all in their tenant
    } else {
      return { ...baseFilter, userId: user.id }; // Users see only their data
    }
  },
  persist: (user: AuthUser) => ({
    tenantId: user.tenantId,
    createdBy: user.id,
    updatedBy: user.id,
  }),
})
@Crud({
  model: { type: Document },
  routes: {
    createOneBase: {
      decorators: [UseGuards(JwtAuthGuard)],
    },
    updateOneBase: {
      decorators: [UseGuards(JwtAuthGuard)],
    },
  },
})
@Controller('documents')
export class DocumentsController implements CrudController<Document> {
  constructor(public service: DocumentsService) {}
}
```

---

## ⚡ Performance & Optimization

### Algorithm Analysis

#### Query Performance Analysis

| Operation | Time Complexity | Space Complexity | Optimization Strategy |
|-----------|----------------|------------------|----------------------|
| Simple GET | O(log n) | O(1) | Database indexing |
| Filtered GET | O(n log n) | O(k) | Composite indexes |
| JOIN queries | O(n * m) | O(n + m) | Query optimization |
| Bulk CREATE | O(n) | O(n) | Batch processing |
| Pagination | O(log n) | O(1) | Cursor-based pagination |

#### Efficient Search Implementation

```typescript
// Binary search concepts applied to database queries
protected setSearchCondition(builder: SelectQueryBuilder<T>, search: SCondition) {
  // O(log n) complexity for indexed searches
  if (isObject(search)) {
    const keys = objKeys(search);
    // Optimized condition building
    this.buildOptimizedConditions(builder, keys, search);
  }
}
```

**Time Complexity Analysis:**
- **Simple queries**: O(log n) with proper indexing
- **Complex searches**: O(n log n) with multiple conditions
- **Join operations**: O(n * m) where n, m are table sizes

#### Pagination Algorithm

```typescript
// Efficient pagination with offset/limit
protected async doGetMany(
  builder: SelectQueryBuilder<T>,
  query: ParsedRequestParams,
  options: CrudRequestOptions
): Promise<GetManyDefaultResponse<T> | T[]> {
  if (this.decidePagination(query, options)) {
    const [data, total] = await builder.getManyAndCount();
    const limit = builder.expressionMap.take;
    const offset = builder.expressionMap.skip;

    return this.createPageInfo(data, total, limit || total, offset || 0);
  }

  return builder.getMany();
}
```

**Performance Characteristics:**
- **Space Complexity**: O(1) for pagination metadata
- **Time Complexity**: O(log n) for indexed pagination
- **Memory Usage**: Constant regardless of total dataset size

### Caching Strategies

#### Multi-level Caching Implementation

```typescript
// Multi-level caching aligned with enterprise caching strategies
public async createBuilder(
  parsed: ParsedRequestParams,
  options: CrudRequestOptions
): Promise<SelectQueryBuilder<T>> {
  const builder = this.repo.createQueryBuilder(this.alias);

  // L1 Cache: Query result caching
  if (options.query.cache && parsed.cache !== 0) {
    builder.cache(builder.getQueryAndParameters(), options.query.cache);
  }

  return builder;
}
```

#### Redis Cache Service

```typescript
// core/cache/redis-cache.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { Redis } from 'ioredis';
import { ICacheService } from './cache-service.interface';

@Injectable()
export class RedisCacheService implements ICacheService {
  private readonly logger = new Logger(RedisCacheService.name);
  private readonly redis: Redis;

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      this.logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl: number = 3600): Promise<void> {
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      this.logger.error(`Cache set error for key ${key}:`, error);
    }
  }

  // Advanced caching patterns
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    let value = await this.get<T>(key);

    if (value === null) {
      value = await factory();
      await this.set(key, value, ttl);
    }

    return value;
  }

  // Cache invalidation patterns
  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      this.logger.error(`Cache invalidation error for pattern ${pattern}:`, error);
    }
  }
}
```

### Database Optimization

#### Optimized Database Configuration

```typescript
// core/database/database.config.ts
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export const databaseConfig: TypeOrmModuleOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'enterprise_crud',

  // Performance optimizations
  poolSize: 20,
  maxQueryExecutionTime: 1000,

  // Connection pooling
  extra: {
    connectionLimit: 20,
    acquireTimeout: 60000,
    timeout: 60000,
  },

  // Logging for development
  logging: process.env.NODE_ENV === 'development' ? ['query', 'error'] : ['error'],

  // Auto-load entities
  autoLoadEntities: true,

  // Synchronization (disable in production)
  synchronize: process.env.NODE_ENV === 'development',

  // Migrations
  migrations: ['dist/migrations/*.js'],
  migrationsRun: true,
};
```

#### Data Structure Optimizations

```typescript
// Hash Map Usage for Efficient Lookups
protected entityColumnsHash: ObjectLiteral = {};
protected entityRelationsHash: Map<string, IAllowedRelation> = new Map();

// Efficient column validation - O(1) lookup time
protected getAllowedColumns(columns: string[], options: QueryOptions): string[] {
  return columns.filter(column =>
    this.entityColumnsHash[column] !== undefined // O(1) lookup
  );
}

// Set Operations for Deduplication - O(1) insertion and lookup
const select = new Set([
  ...allowedRelation.primaryColumns,
  ...(isArrayFull(options.persist) ? options.persist : []),
  ...columns
].map(col => `${alias}.${col}`));

return Array.from(select); // O(n) conversion
```

### Memory Management

#### Optimized Memory Usage

```typescript
// Efficient memory management
export class OptimizedCrudService<T> extends TypeOrmCrudService<T> {
  private queryCache = new Map<string, any>();
  private readonly MAX_CACHE_SIZE = 1000;

  async getMany(req: CrudRequest): Promise<GetManyDefaultResponse<T> | T[]> {
    const cacheKey = this.generateCacheKey(req);

    // LRU cache implementation
    if (this.queryCache.has(cacheKey)) {
      return this.queryCache.get(cacheKey);
    }

    const result = await super.getMany(req);

    // Prevent memory leaks
    if (this.queryCache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.queryCache.keys().next().value;
      this.queryCache.delete(firstKey);
    }

    this.queryCache.set(cacheKey, result);
    return result;
  }
}
```

---

## 🛡️ Security & Best Practices

### Security Implementation

#### SQL Injection Prevention

```typescript
// Multi-layer protection against SQL injection
protected sqlInjectionRegEx: RegExp[] = [
  /(%27)|(\')|(--)|(%23)|(#)/gi,
  /((%3D)|(=))[^\n]*((%27)|(\')|(--)|(%3B)|(;))/gi,
  /w*((%27)|(\'))((%6F)|o|(%4F))((%72)|r|(%52))/gi,
  /((%27)|(\'))union/gi,
];

private checkSqlInjection(field: string): string {
  if (this.sqlInjectionRegEx.length) {
    for (let i = 0; i < this.sqlInjectionRegEx.length; i++) {
      if (this.sqlInjectionRegEx[0].test(field)) {
        this.throwBadRequestException(`SQL injection detected: "${field}"`);
      }
    }
  }
  return field;
}
```

#### JWT Authentication Guard

```typescript
// core/security/jwt-auth.guard.ts
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('Access token is required');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
      });

      // Attach user to request for use in controllers
      request['user'] = payload;

      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid access token');
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

### Input Validation

#### Comprehensive Validation Strategy

```typescript
// Comprehensive validation using class-validator
protected prepareEntityBeforeSave(dto: T | Partial<T>, parsed: CrudRequest['parsed']): T {
  if (!isObject(dto)) {
    return undefined;
  }

  // Apply parameter filters for security
  if (hasLength(parsed.paramsFilter)) {
    for (const filter of parsed.paramsFilter) {
      dto[filter.field] = filter.value;
    }
  }

  return plainToClass(this.entityType, { ...dto, ...parsed.authPersist }, parsed.classTransformOptions);
}
```

#### DTO Validation Examples

```typescript
// CreateUserDto with comprehensive validation
export class CreateUserDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @IsString({ message: 'First name must be a string' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  @MaxLength(50, { message: 'First name cannot exceed 50 characters' })
  @Matches(/^[a-zA-Z\s]+$/, { message: 'First name can only contain letters and spaces' })
  firstName: string;

  @IsString({ message: 'Last name must be a string' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  @MaxLength(50, { message: 'Last name cannot exceed 50 characters' })
  @Matches(/^[a-zA-Z\s]+$/, { message: 'Last name can only contain letters and spaces' })
  lastName: string;

  @IsString({ message: 'Password must be a string' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
  })
  password: string;

  @IsOptional()
  @IsBoolean({ message: 'isActive must be a boolean value' })
  isActive?: boolean = true;
}
```

### Authentication & Authorization

#### Role-Based Access Control

```typescript
// core/security/roles.guard.ts
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from './roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();

    if (!user || !user.roles) {
      return false;
    }

    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}

// Roles decorator
import { SetMetadata } from '@nestjs/common';

export const ROLES_KEY = 'roles';
export const Roles = (...roles: string[]) => SetMetadata(ROLES_KEY, roles);
```

### Rate Limiting

#### Advanced Rate Limiting Implementation

```typescript
// core/security/rate-limit.guard.ts
import { Injectable, CanActivate, ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';

@Injectable()
export class RateLimitGuard implements CanActivate {
  private rateLimiter = new RateLimiterRedis({
    storeClient: redisClient,
    keyGenerator: (req: any) => req.ip || req.user?.id || 'anonymous',
    points: 100, // Number of requests
    duration: 60, // Per 60 seconds
    blockDuration: 60, // Block for 60 seconds if limit exceeded
  });

  // Different limits for different endpoints
  private endpointLimits = new Map([
    ['POST', { points: 10, duration: 60 }], // Stricter for write operations
    ['GET', { points: 100, duration: 60 }], // More lenient for read operations
    ['DELETE', { points: 5, duration: 60 }], // Very strict for delete operations
  ]);

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const method = request.method;

    // Get method-specific limits
    const limits = this.endpointLimits.get(method) || { points: 50, duration: 60 };

    try {
      await this.rateLimiter.consume(
        request.ip || request.user?.id || 'anonymous',
        1,
        { points: limits.points, duration: limits.duration }
      );
      return true;
    } catch (rejRes) {
      throw new HttpException(
        {
          message: 'Too many requests',
          retryAfter: rejRes.msBeforeNext,
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }
  }
}
```

---

## 🧪 Testing Strategy

### Unit Testing

#### SOLID Principles in Testing

```typescript
// application/services/__tests__/users.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UsersService } from '../users.service';
import { User } from '../../../domain/entities/user.entity';
import { ICacheService } from '../../../core/cache/cache-service.interface';
import { IEventBus } from '../../../core/events/event-bus.interface';
import { CreateUserDto } from '../../dto/user.dto';

describe('UsersService', () => {
  let service: UsersService;
  let repository: jest.Mocked<Repository<User>>;
  let cacheService: jest.Mocked<ICacheService>;
  let eventBus: jest.Mocked<IEventBus>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
              where: jest.fn().mockReturnThis(),
              getOne: jest.fn(),
              getMany: jest.fn(),
              getManyAndCount: jest.fn(),
            })),
          },
        },
        {
          provide: 'ICacheService',
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: 'IEventBus',
          useValue: {
            publish: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repository = module.get(getRepositoryToken(User));
    cacheService = module.get('ICacheService');
    eventBus = module.get('IEventBus');
  });

  describe('createOne', () => {
    it('should create a user following SOLID principles', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
      };

      const mockUser = User.create(
        createUserDto.email,
        createUserDto.firstName,
        createUserDto.lastName,
        'hashedPassword',
      );
      mockUser.id = 'uuid-123';

      repository.findOne.mockResolvedValue(null); // Email doesn't exist
      repository.save.mockResolvedValue(mockUser);
      cacheService.set.mockResolvedValue(undefined);
      eventBus.publish.mockResolvedValue(undefined);

      const mockRequest = {
        parsed: { paramsFilter: [], authPersist: {} },
        options: { routes: { createOneBase: { returnShallow: false } } },
      } as any;

      // Act
      const result = await service.createOne(mockRequest, createUserDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.email).toBe(createUserDto.email);
      expect(repository.save).toHaveBeenCalled();
      expect(cacheService.set).toHaveBeenCalledWith(`user:${result.id}`, result, 3600);
      expect(eventBus.publish).toHaveBeenCalled();
    });

    it('should throw error for duplicate email (business rule)', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
      };

      const existingUser = new User();
      existingUser.email = createUserDto.email;

      repository.findOne.mockResolvedValue(existingUser);

      const mockRequest = {
        parsed: { paramsFilter: [], authPersist: {} },
        options: { routes: { createOneBase: { returnShallow: false } } },
      } as any;

      // Act & Assert
      await expect(service.createOne(mockRequest, createUserDto)).rejects.toThrow('Email already exists');
    });
  });

  describe('findByEmail', () => {
    it('should use cache for performance optimization', async () => {
      // Arrange
      const email = '<EMAIL>';
      const cachedUser = new User();
      cachedUser.email = email;

      cacheService.get.mockResolvedValue(cachedUser);

      // Act
      const result = await service.findByEmail(email);

      // Assert
      expect(result).toBe(cachedUser);
      expect(cacheService.get).toHaveBeenCalledWith(`user:email:${email}`);
      expect(repository.findOne).not.toHaveBeenCalled(); // Should not hit database
    });

    it('should fallback to database when cache miss', async () => {
      // Arrange
      const email = '<EMAIL>';
      const dbUser = new User();
      dbUser.email = email;

      cacheService.get.mockResolvedValue(null); // Cache miss
      repository.findOne.mockResolvedValue(dbUser);
      cacheService.set.mockResolvedValue(undefined);

      // Act
      const result = await service.findByEmail(email);

      // Assert
      expect(result).toBe(dbUser);
      expect(repository.findOne).toHaveBeenCalledWith({ where: { email } });
      expect(cacheService.set).toHaveBeenCalledWith(`user:email:${email}`, dbUser, 3600);
    });
  });
});
```

### Integration Testing

#### Enterprise Integration Tests

```typescript
// presentation/controllers/__tests__/users.controller.integration.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../app.module';
import { User } from '../../../domain/entities/user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

describe('UsersController (Integration)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    userRepository = app.get<Repository<User>>(getRepositoryToken(User));
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean database before each test
    await userRepository.clear();
  });

  describe('GET /users', () => {
    it('should return paginated users with proper performance', async () => {
      // Arrange: Create test data
      const users = Array.from({ length: 50 }, (_, i) =>
        User.create(`user${i}@example.com`, `First${i}`, `Last${i}`, 'password')
      );
      await userRepository.save(users);

      const startTime = Date.now();

      // Act
      const response = await request(app.getHttpServer())
        .get('/users')
        .query({
          limit: 10,
          offset: 0,
          sort: 'createdAt,DESC',
        })
        .expect(200);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Assert
      expect(response.body.data).toHaveLength(10);
      expect(response.body.total).toBe(50);
      expect(response.body.count).toBe(10);
      expect(executionTime).toBeLessThan(100); // Performance assertion
    });

    it('should handle complex filtering with proper algorithm complexity', async () => {
      // Arrange
      const users = [
        User.create('<EMAIL>', 'John', 'Doe', 'password'),
        User.create('<EMAIL>', 'Jane', 'Smith', 'password'),
        User.create('<EMAIL>', 'Bob', 'Johnson', 'password'),
      ];
      await userRepository.save(users);

      // Act
      const response = await request(app.getHttpServer())
        .get('/users')
        .query({
          filter: 'firstName||$cont||Jo',
          sort: 'firstName,ASC',
        })
        .expect(200);

      // Assert
      expect(response.body.data).toHaveLength(2); // John and Johnson
      expect(response.body.data[0].firstName).toBe('John');
    });
  });

  describe('POST /users', () => {
    it('should create user with proper validation and business rules', async () => {
      // Arrange
      const createUserDto = {
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        password: 'securePassword123',
      };

      // Act
      const response = await request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(201);

      // Assert
      expect(response.body.email).toBe(createUserDto.email);
      expect(response.body.firstName).toBe(createUserDto.firstName);
      expect(response.body.password).toBeUndefined(); // Should be excluded
      expect(response.body.id).toBeDefined();
      expect(response.body.createdAt).toBeDefined();

      // Verify in database
      const savedUser = await userRepository.findOne({
        where: { email: createUserDto.email }
      });
      expect(savedUser).toBeDefined();
      expect(savedUser.isActive).toBe(true);
    });

    it('should reject duplicate email (business rule validation)', async () => {
      // Arrange
      const existingUser = User.create('<EMAIL>', 'Existing', 'User', 'password');
      await userRepository.save(existingUser);

      const duplicateUserDto = {
        email: '<EMAIL>',
        firstName: 'Duplicate',
        lastName: 'User',
        password: 'password123',
      };

      // Act & Assert
      await request(app.getHttpServer())
        .post('/users')
        .send(duplicateUserDto)
        .expect(400);
    });
  });
});
```

### End-to-End Testing

#### Complete User Journey Tests

```typescript
// tests/e2e/user-management.e2e-spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

describe('User Management (E2E)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Setup authentication
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'adminPassword123',
      })
      .expect(200);

    authToken = loginResponse.body.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Complete User Lifecycle', () => {
    let userId: string;

    it('should create a new user', async () => {
      const createUserDto = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        password: 'testPassword123',
      };

      const response = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createUserDto)
        .expect(201);

      expect(response.body.email).toBe(createUserDto.email);
      expect(response.body.id).toBeDefined();
      userId = response.body.id;
    });

    it('should retrieve the created user', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.id).toBe(userId);
      expect(response.body.email).toBe('<EMAIL>');
    });

    it('should update the user', async () => {
      const updateUserDto = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      const response = await request(app.getHttpServer())
        .patch(`/users/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateUserDto)
        .expect(200);

      expect(response.body.firstName).toBe('Updated');
      expect(response.body.lastName).toBe('Name');
    });

    it('should activate/deactivate user', async () => {
      // Deactivate user
      await request(app.getHttpServer())
        .post(`/users/${userId}/deactivate`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify user is deactivated
      const deactivatedResponse = await request(app.getHttpServer())
        .get(`/users/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(deactivatedResponse.body.isActive).toBe(false);

      // Activate user
      await request(app.getHttpServer())
        .post(`/users/${userId}/activate`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify user is activated
      const activatedResponse = await request(app.getHttpServer())
        .get(`/users/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(activatedResponse.body.isActive).toBe(true);
    });

    it('should delete the user', async () => {
      await request(app.getHttpServer())
        .delete(`/users/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify user is deleted (soft delete)
      await request(app.getHttpServer())
        .get(`/users/${userId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('Advanced Query Features', () => {
    beforeEach(async () => {
      // Create test data
      const users = [
        { email: '<EMAIL>', firstName: 'Alice', lastName: 'Johnson', age: 25 },
        { email: '<EMAIL>', firstName: 'Bob', lastName: 'Smith', age: 30 },
        { email: '<EMAIL>', firstName: 'Charlie', lastName: 'Brown', age: 35 },
      ];

      for (const user of users) {
        await request(app.getHttpServer())
          .post('/users')
          .set('Authorization', `Bearer ${authToken}`)
          .send({ ...user, password: 'password123' });
      }
    });

    it('should filter users by age range', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .query({
          filter: 'age||$gte||30',
          sort: 'age,ASC',
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.length).toBeGreaterThanOrEqual(2);
      expect(response.body.data[0].firstName).toBe('Bob');
    });

    it('should search users by name', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .query({
          s: JSON.stringify({
            $or: [
              { firstName: { $cont: 'Ali' } },
              { lastName: { $cont: 'Ali' } }
            ]
          }),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.length).toBeGreaterThanOrEqual(1);
      expect(response.body.data[0].firstName).toBe('Alice');
    });

    it('should paginate results correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .query({
          limit: 2,
          offset: 1,
          sort: 'firstName,ASC',
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveLength(2);
      expect(response.body.count).toBe(2);
      expect(response.body.total).toBeGreaterThanOrEqual(3);
    });
  });
});
```

### Performance Testing

#### Load Testing Implementation

```typescript
// tests/performance/load-test.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

describe('Performance Tests', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Load Testing', () => {
    it('should handle concurrent requests efficiently', async () => {
      const concurrentRequests = 100;
      const startTime = Date.now();

      // Create concurrent requests
      const promises = Array.from({ length: concurrentRequests }, (_, i) =>
        request(app.getHttpServer())
          .get('/users')
          .query({ limit: 10, offset: i * 10 })
      );

      const responses = await Promise.all(promises);
      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Assert performance requirements
      expect(totalTime).toBeLessThan(5000); // Should complete within 5 seconds
      expect(responses.every(res => res.status === 200)).toBe(true);

      // Calculate average response time
      const averageResponseTime = totalTime / concurrentRequests;
      expect(averageResponseTime).toBeLessThan(100); // Average response time < 100ms
    });

    it('should maintain performance with large datasets', async () => {
      // Create large dataset
      const batchSize = 1000;
      const users = Array.from({ length: batchSize }, (_, i) => ({
        email: `user${i}@example.com`,
        firstName: `User${i}`,
        lastName: `Test${i}`,
        password: 'password123',
      }));

      // Batch create users
      const createPromises = users.map(user =>
        request(app.getHttpServer())
          .post('/users')
          .send(user)
      );

      await Promise.all(createPromises);

      // Test query performance with large dataset
      const startTime = Date.now();

      const response = await request(app.getHttpServer())
        .get('/users')
        .query({
          filter: 'firstName||$cont||User',
          sort: 'createdAt,DESC',
          limit: 50,
        })
        .expect(200);

      const endTime = Date.now();
      const queryTime = endTime - startTime;

      // Assert performance with large dataset
      expect(queryTime).toBeLessThan(200); // Query should complete within 200ms
      expect(response.body.data).toHaveLength(50);
      expect(response.body.total).toBe(batchSize);
    });
  });

  describe('Memory Usage Tests', () => {
    it('should not have memory leaks during repeated operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform repeated operations
      for (let i = 0; i < 1000; i++) {
        await request(app.getHttpServer())
          .get('/users')
          .query({ limit: 10 });
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });
});
```

---

## 🚀 Deployment & Operations

### Docker Configuration

#### Production-Ready Dockerfile

```dockerfile
# Multi-stage build for optimized production image
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Copy built application
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./

# Switch to non-root user
USER nestjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/main"]
```

#### Docker Compose for Production

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${DB_PASSWORD}@postgres:5432/${DB_NAME}
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
```

### Production Setup

#### Environment Configuration

```typescript
// src/config/configuration.ts
import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  // Application
  port: parseInt(process.env.PORT, 10) || 3000,
  environment: process.env.NODE_ENV || 'development',

  // Database
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'enterprise_crud',
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    poolSize: parseInt(process.env.DB_POOL_SIZE, 10) || 20,
    maxQueryExecutionTime: parseInt(process.env.DB_MAX_QUERY_TIME, 10) || 1000,
  },

  // Redis
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB, 10) || 0,
    ttl: parseInt(process.env.REDIS_TTL, 10) || 3600,
  },

  // Security
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },

  // Rate Limiting
  rateLimit: {
    ttl: parseInt(process.env.RATE_LIMIT_TTL, 10) || 60,
    limit: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100,
  },

  // Monitoring
  monitoring: {
    enabled: process.env.MONITORING_ENABLED === 'true',
    metricsPath: process.env.METRICS_PATH || '/metrics',
    healthPath: process.env.HEALTH_PATH || '/health',
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    destination: process.env.LOG_DESTINATION || 'console',
  },
}));
```

#### Health Check Implementation

```typescript
// src/health/health.controller.ts
import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService, TypeOrmHealthIndicator, MemoryHealthIndicator } from '@nestjs/terminus';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private memory: MemoryHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'Health check successful' })
  @ApiResponse({ status: 503, description: 'Service unavailable' })
  check() {
    return this.health.check([
      // Database health check
      () => this.db.pingCheck('database'),

      // Memory health check (should not exceed 1GB)
      () => this.memory.checkHeap('memory_heap', 1024 * 1024 * 1024),

      // RSS memory check (should not exceed 1.5GB)
      () => this.memory.checkRSS('memory_rss', 1024 * 1024 * 1024 * 1.5),
    ]);
  }

  @Get('ready')
  @ApiOperation({ summary: 'Readiness probe' })
  @ApiResponse({ status: 200, description: 'Service is ready' })
  ready() {
    return { status: 'ready', timestamp: new Date().toISOString() };
  }

  @Get('live')
  @ApiOperation({ summary: 'Liveness probe' })
  @ApiResponse({ status: 200, description: 'Service is alive' })
  live() {
    return { status: 'alive', timestamp: new Date().toISOString() };
  }
}
```

### Monitoring & Observability

#### Prometheus Metrics

```typescript
// src/monitoring/metrics.service.ts
import { Injectable } from '@nestjs/common';
import { register, Counter, Histogram, Gauge } from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly httpRequestsTotal = new Counter({
    name: 'http_requests_total',
    help: 'Total number of HTTP requests',
    labelNames: ['method', 'route', 'status_code'],
  });

  private readonly httpRequestDuration = new Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route'],
    buckets: [0.1, 0.5, 1, 2, 5],
  });

  private readonly databaseConnectionsActive = new Gauge({
    name: 'database_connections_active',
    help: 'Number of active database connections',
  });

  private readonly cacheHitRate = new Counter({
    name: 'cache_hits_total',
    help: 'Total number of cache hits',
    labelNames: ['cache_type'],
  });

  private readonly cacheMissRate = new Counter({
    name: 'cache_misses_total',
    help: 'Total number of cache misses',
    labelNames: ['cache_type'],
  });

  recordHttpRequest(method: string, route: string, statusCode: number, duration: number) {
    this.httpRequestsTotal.inc({ method, route, status_code: statusCode });
    this.httpRequestDuration.observe({ method, route }, duration / 1000);
  }

  recordDatabaseConnections(count: number) {
    this.databaseConnectionsActive.set(count);
  }

  recordCacheHit(cacheType: string) {
    this.cacheHitRate.inc({ cache_type: cacheType });
  }

  recordCacheMiss(cacheType: string) {
    this.cacheMissRate.inc({ cache_type: cacheType });
  }

  getMetrics() {
    return register.metrics();
  }
}
```

#### Structured Logging

```typescript
// src/logging/logger.service.ts
import { Injectable, LoggerService } from '@nestjs/common';
import { createLogger, format, transports, Logger } from 'winston';

@Injectable()
export class CustomLoggerService implements LoggerService {
  private logger: Logger;

  constructor() {
    this.logger = createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: format.combine(
        format.timestamp(),
        format.errors({ stack: true }),
        format.json(),
        format.printf(({ timestamp, level, message, ...meta }) => {
          return JSON.stringify({
            timestamp,
            level,
            message,
            ...meta,
          });
        }),
      ),
      transports: [
        new transports.Console(),
        new transports.File({ filename: 'logs/error.log', level: 'error' }),
        new transports.File({ filename: 'logs/combined.log' }),
      ],
    });
  }

  log(message: string, context?: string) {
    this.logger.info(message, { context });
  }

  error(message: string, trace?: string, context?: string) {
    this.logger.error(message, { trace, context });
  }

  warn(message: string, context?: string) {
    this.logger.warn(message, { context });
  }

  debug(message: string, context?: string) {
    this.logger.debug(message, { context });
  }

  verbose(message: string, context?: string) {
    this.logger.verbose(message, { context });
  }
}
```

### CI/CD Pipeline

#### GitHub Actions Workflow

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run type checking
      run: npm run build

    - name: Run unit tests
      run: npm run test:cov
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379

    - name: Run integration tests
      run: npm run test:e2e
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Run security audit
      run: npm audit --audit-level high

    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

  build-and-push:
    needs: [test, security]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    permissions:
      contents: read
      packages: write

    steps:
    - uses: actions/checkout@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment"
        # Add your deployment commands here
        # e.g., kubectl apply, helm upgrade, etc.
```

---

## 🔧 Advanced Topics

### Framework Agnostic Implementation

#### Vanilla Node.js CRUD Blueprint

For teams wanting to implement similar functionality without NestJS, here's a framework-agnostic approach:

```typescript
// Express.js implementation example
import express from 'express';
import { registerCrudController } from './core/crud/factory';
import { User } from './modules/user/user.entity';
import { UserService } from './modules/user/user.service';
import { crudRequestMiddleware, crudResponseMiddleware } from './core/crud/middleware';

const app = express();
app.use(express.json());
app.use(crudRequestMiddleware());

registerCrudController(app, {
  path: '/users',
  model: User,
  service: new UserService(),
  options: {
    query: { softDelete: true, limit: 50 },
    routes: { deleteOneBase: { returnDeleted: true } }
  }
});

app.use(crudResponseMiddleware());
export default app;
```

#### Core Components for Vanilla Implementation

```typescript
// core/crud/services/typeorm-crud.service.ts
export class TypeOrmCrudService<T> extends CrudService<T> {
  constructor(protected repo: Repository<T>) { super(); }

  async getMany(req) {
    const qb = await this.createBuilder(req.parsed, req.options);
    return this.doGetMany(qb, req.parsed, req.options);
  }
  // Map operators to QB with param binding; joins; pagination; soft delete; cache
}

// core/crud/middleware/request.ts
export function crudRequestMiddleware() {
  return (req, _res, next) => {
    req.crud = RequestQueryParser.create().parseQuery(req.query).getParsed();
    next();
  };
}
```

**Key Differences from NestJS:**
- Replace decorators with configuration objects
- Use Express/Fastify middleware instead of interceptors
- Implement dependency injection with libraries like `tsyringe` or `inversify`
- Use `swagger-jsdoc` for API documentation
- Replace NestJS guards with middleware functions

### Microservices Architecture

#### Service Decomposition Strategy

```typescript
// Each entity can be a separate microservice
@Controller('users')
@Crud({
  model: { type: User },
  routes: {
    only: ['getManyBase', 'getOneBase', 'createOneBase', 'updateOneBase']
  }
})
export class UsersController implements CrudController<User> {
  constructor(public service: UsersService) {}
}

@Controller('orders')
@Crud({
  model: { type: Order },
  query: {
    join: {
      user: { eager: true },
      items: { eager: true }
    }
  }
})
export class OrdersController implements CrudController<Order> {
  constructor(public service: OrdersService) {}
}
```

#### Inter-Service Communication

```typescript
// Event-driven communication between services
export class UserService extends TypeOrmCrudService<User> {
  async createOne(req: CrudRequest, dto: CreateUserDto): Promise<User> {
    const user = await super.createOne(req, dto);

    // Emit domain event for other services
    await this.eventBus.publish(new UserCreatedEvent(user));

    return user;
  }
}

// API Gateway integration
@Controller('api/v1')
export class ApiGatewayController {
  constructor(
    private userService: UsersService,
    private orderService: OrdersService,
    private authService: AuthService
  ) {}

  @UseGuards(JwtAuthGuard)
  @Get('users')
  async getUsers(@Req() req: CrudRequest) {
    return this.userService.getMany(req);
  }
}
```

### Event-Driven Patterns

#### Domain Events Implementation

```typescript
// Domain events for enterprise architecture
export class UserCreatedEvent {
  constructor(
    public readonly user: User,
    public readonly timestamp: Date = new Date()
  ) {}
}

export class UserUpdatedEvent {
  constructor(
    public readonly user: User,
    public readonly previousUser: User,
    public readonly timestamp: Date = new Date()
  ) {}
}

// Event handlers for cross-cutting concerns
@EventsHandler(UserCreatedEvent)
export class UserCreatedHandler implements IEventHandler<UserCreatedEvent> {
  constructor(
    private emailService: EmailService,
    private auditService: AuditService,
    private analyticsService: AnalyticsService
  ) {}

  async handle(event: UserCreatedEvent) {
    // Send welcome email
    await this.emailService.sendWelcomeEmail(event.user);

    // Log audit event
    await this.auditService.log('USER_CREATED', {
      userId: event.user.id,
      timestamp: event.timestamp
    });

    // Track analytics
    await this.analyticsService.track('user_registered', {
      userId: event.user.id,
      email: event.user.email
    });
  }
}
```

#### CQRS Implementation

```typescript
// Command Query Responsibility Segregation
export class CreateUserCommand {
  constructor(
    public readonly email: string,
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly password: string
  ) {}
}

@CommandHandler(CreateUserCommand)
export class CreateUserHandler implements ICommandHandler<CreateUserCommand> {
  constructor(
    private userRepository: Repository<User>,
    private eventBus: EventBus
  ) {}

  async execute(command: CreateUserCommand): Promise<User> {
    const user = User.create(
      command.email,
      command.firstName,
      command.lastName,
      command.password
    );

    const savedUser = await this.userRepository.save(user);

    // Publish domain event
    await this.eventBus.publish(new UserCreatedEvent(savedUser));

    return savedUser;
  }
}

// Query side
export class GetUsersQuery {
  constructor(
    public readonly filters: QueryFilter[],
    public readonly pagination: PaginationOptions
  ) {}
}

@QueryHandler(GetUsersQuery)
export class GetUsersHandler implements IQueryHandler<GetUsersQuery> {
  constructor(private userReadModel: UserReadModelRepository) {}

  async execute(query: GetUsersQuery): Promise<User[]> {
    return this.userReadModel.findMany(query.filters, query.pagination);
  }
}
```

### Custom Extensions

#### Custom Query Operators

```typescript
// Extending the query parser with custom operators
export class CustomRequestQueryParser extends RequestQueryParser {
  protected operatorsMap = {
    ...super.operatorsMap,
    '$regex': '$regex',
    '$distance': '$distance',
    '$within': '$within',
  };

  protected parseCondition(condition: string): QueryFilter {
    const parsed = super.parseCondition(condition);

    // Handle custom operators
    switch (parsed.operator) {
      case '$regex':
        return this.handleRegexOperator(parsed);
      case '$distance':
        return this.handleDistanceOperator(parsed);
      case '$within':
        return this.handleWithinOperator(parsed);
      default:
        return parsed;
    }
  }

  private handleRegexOperator(filter: QueryFilter): QueryFilter {
    // Validate regex pattern
    try {
      new RegExp(filter.value);
      return filter;
    } catch (error) {
      throw new BadRequestException('Invalid regex pattern');
    }
  }

  private handleDistanceOperator(filter: QueryFilter): QueryFilter {
    // Handle geospatial distance queries
    const [lat, lng, distance] = filter.value.split(',');
    return {
      ...filter,
      value: { lat: parseFloat(lat), lng: parseFloat(lng), distance: parseFloat(distance) }
    };
  }
}
```

#### Custom Service Extensions

```typescript
// Extending TypeOrmCrudService with custom functionality
export class EnhancedCrudService<T> extends TypeOrmCrudService<T> {
  constructor(
    repo: Repository<T>,
    private auditService: AuditService,
    private cacheService: CacheService,
    private searchService: SearchService
  ) {
    super(repo);
  }

  // Override with audit logging
  async createOne(req: CrudRequest, dto: T): Promise<T> {
    const result = await super.createOne(req, dto);

    // Audit logging
    await this.auditService.log('CREATE', {
      entity: this.entityType.name,
      entityId: result['id'],
      data: dto,
      user: req.user
    });

    return result;
  }

  // Add full-text search capability
  async search(query: string, options: SearchOptions = {}): Promise<T[]> {
    const searchResults = await this.searchService.search(
      this.entityType.name,
      query,
      options
    );

    // Get full entities from search results
    const ids = searchResults.map(result => result.id);
    return this.repo.findByIds(ids);
  }

  // Add bulk operations
  async bulkCreate(dtos: T[]): Promise<T[]> {
    const results = await this.repo.save(dtos);

    // Audit bulk operation
    await this.auditService.log('BULK_CREATE', {
      entity: this.entityType.name,
      count: results.length,
      data: dtos
    });

    return results;
  }

  // Add soft delete with cascade
  async softDeleteWithCascade(id: string): Promise<void> {
    // Find related entities
    const entity = await this.repo.findOne({
      where: { id } as any,
      relations: this.getRelations()
    });

    if (!entity) {
      throw new NotFoundException('Entity not found');
    }

    // Soft delete related entities
    await this.cascadeSoftDelete(entity);

    // Soft delete main entity
    await this.repo.softDelete(id);

    // Clear cache
    await this.cacheService.invalidatePattern(`${this.entityType.name}:*`);
  }

  private async cascadeSoftDelete(entity: T): Promise<void> {
    // Implementation for cascading soft delete
    // This would depend on your specific entity relationships
  }

  private getRelations(): string[] {
    // Get entity relations from metadata
    const metadata = this.repo.metadata;
    return metadata.relations.map(relation => relation.propertyName);
  }
}
```

---

## 📚 Reference

### API Reference

#### Core Decorators

| Decorator | Purpose | Example |
|-----------|---------|---------|
| `@Crud()` | Main decorator for CRUD controllers | `@Crud({ model: { type: User } })` |
| `@Override()` | Override default CRUD methods | `@Override() async getMany() {}` |
| `@ParsedRequest()` | Inject parsed request parameters | `getMany(@ParsedRequest() req: CrudRequest)` |
| `@Feature()` | Enable/disable CRUD features | `@Feature('CREATE_MANY')` |
| `@Action()` | Define custom CRUD actions | `@Action('CUSTOM_ACTION')` |

#### Configuration Options

```typescript
interface CrudOptions {
  model: {
    type: Function;
  };
  dto?: {
    create?: Function;
    update?: Function;
    replace?: Function;
  };
  serialize?: {
    get?: Function;
    getMany?: Function;
    create?: Function;
    update?: Function;
    replace?: Function;
    delete?: Function;
  };
  query?: {
    maxLimit?: number;
    cache?: number | false;
    alwaysPaginate?: boolean;
    softDelete?: boolean;
    join?: {
      [key: string]: JoinOptions;
    };
    sort?: SortOption[];
    filter?: FilterOption[];
    exclude?: string[];
    allow?: string[];
  };
  routes?: {
    only?: CrudActions[];
    exclude?: CrudActions[];
    getManyBase?: RouteOptions;
    getOneBase?: RouteOptions;
    createOneBase?: RouteOptions;
    createManyBase?: RouteOptions;
    updateOneBase?: RouteOptions;
    replaceOneBase?: RouteOptions;
    deleteOneBase?: RouteOptions;
  };
  params?: {
    [key: string]: ParamOptions;
  };
}
```

### Configuration Options

#### Global Configuration

```typescript
// Set global defaults
CrudConfigService.load({
  query: {
    limit: 25,
    maxLimit: 100,
    cache: 2000,
  },
  routes: {
    getManyBase: { interceptors: [CacheInterceptor] },
    createOneBase: { interceptors: [AuditInterceptor] },
  },
  params: {
    id: {
      field: 'id',
      type: 'uuid',
      primary: true,
    },
  },
});
```

#### Query Parameters Reference

| Parameter | Description | Example |
|-----------|-------------|---------|
| `fields` | Select specific fields | `?fields=id,name,email` |
| `filter` | Filter results | `?filter=name||$cont||john` |
| `or` | OR conditions | `?or=name||$cont||john&or=email||$cont||john` |
| `sort` | Sort results | `?sort=name,ASC&sort=createdAt,DESC` |
| `join` | Join relations | `?join=profile&join=roles` |
| `limit` | Limit results | `?limit=10` |
| `offset` | Offset results | `?offset=20` |
| `page` | Page number | `?page=2` |
| `cache` | Cache duration | `?cache=3600` |
| `s` | Search | `?s={"name":{"$cont":"john"}}` |

### Real-World Examples

This section showcases actual implementations from the integration examples in the repository:

#### Multi-Tenant User Management

Based on the integration example in `/integration/crud-typeorm/users/`:

```typescript
// user.entity.ts - Real entity from the codebase
import {
  Entity,
  Column,
  JoinColumn,
  OneToOne,
  OneToMany,
  ManyToOne,
  ManyToMany,
  DeleteDateColumn,
} from 'typeorm';
import {
  IsOptional,
  IsString,
  MaxLength,
  IsNotEmpty,
  IsEmail,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CrudValidationGroups } from '@nestjsx/crud';

import { BaseEntity } from '../base-entity';
import { UserProfile } from '../users-profiles/user-profile.entity';
import { Company } from '../companies/company.entity';
import { Project } from '../projects/project.entity';

const { CREATE, UPDATE } = CrudValidationGroups;

// Embedded entity for complex data types
export class Name {
  @IsString({ always: true })
  @Column({ nullable: true })
  first: string;

  @IsString({ always: true })
  @Column({ nullable: true })
  last: string;
}

@Entity('users')
export class User extends BaseEntity {
  @IsOptional({ groups: [UPDATE] })
  @IsNotEmpty({ groups: [CREATE] })
  @IsString({ always: true })
  @MaxLength(255, { always: true })
  @IsEmail({ require_tld: false }, { always: true })
  @Column({ type: 'varchar', length: 255, nullable: false, unique: true })
  email: string;

  @IsOptional({ groups: [UPDATE] })
  @IsNotEmpty({ groups: [CREATE] })
  @IsBoolean({ always: true })
  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  // Embedded entity
  @Type(() => Name)
  @Column(() => Name)
  name: Name;

  @Column({ nullable: true })
  profileId?: number;

  @Column({ nullable: false })
  companyId?: number;

  // Soft delete support
  @DeleteDateColumn({ nullable: true })
  deletedAt?: Date;

  // Relations with validation
  @IsOptional({ groups: [UPDATE] })
  @IsNotEmpty({ groups: [CREATE] })
  @ValidateNested({ always: true })
  @Type(() => UserProfile)
  @OneToOne(() => UserProfile, (p) => p.user, { cascade: true })
  @JoinColumn()
  profile?: UserProfile;

  @ManyToOne(() => Company, (c) => c.users)
  company?: Company;

  @ManyToMany(() => Project, (c) => c.users)
  projects?: Project[];
}
```

```typescript
// users.controller.ts - Real controller from the codebase
import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  Crud,
  CrudController,
  CrudRequest,
  ParsedRequest,
  Override,
} from '@nestjsx/crud';

import { User } from './user.entity';
import { UsersService } from './users.service';

@Crud({
  model: {
    type: User,
  },
  params: {
    companyId: {
      field: 'companyId',
      type: 'number',
    },
    id: {
      field: 'id',
      type: 'number',
      primary: true,
    },
  },
  query: {
    softDelete: true, // Enable soft delete
    join: {
      company: {
        exclude: ['description'], // Exclude sensitive fields
      },
      'company.projects': {
        alias: 'pr', // Use alias for nested joins
        exclude: ['description'],
      },
      profile: {
        eager: true, // Always load profile
        exclude: ['updatedAt'],
      },
    },
  },
})
@ApiTags('users')
@Controller('/companies/:companyId/users') // Nested resource
export class UsersController implements CrudController<User> {
  constructor(public service: UsersService) {}

  get base(): CrudController<User> {
    return this;
  }

  @Override('getManyBase')
  getAll(@ParsedRequest() req: CrudRequest) {
    return this.base.getManyBase(req);
  }
}
```

#### Development Environment Setup

Based on the actual project configuration:

```yaml
# docker-compose.yml - Real configuration from the codebase
version: '3'

networks:
  nestjsx_crud:

services:
  postgres:
    # TypeORM fails with Postgres v.12
    image: postgres:11.5
    ports:
      - 5455:5432
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: root
      POSTGRES_DB: nestjsx_crud
    networks:
      - nestjsx_crud

  mysql:
    platform: linux/x86_64
    image: mysql:5.7
    ports:
      - 3316:3306
    environment:
      MYSQL_DATABASE: nestjsx_crud
      MYSQL_USER: nestjsx_crud
      MYSQL_PASSWORD: nestjsx_crud
      MYSQL_ROOT_PASSWORD: nestjsx_crud

  redis:
    image: redis:alpine
    ports:
      - 6399:6379
    command: redis-server
    networks:
      - nestjsx_crud
```

```javascript
// jest.config.js - Real test configuration
const tsconfig = require('tsconfig-extends');
const { pathsToModuleNameMapper } = require('ts-jest/utils');
const compilerOptions = tsconfig.load_file_sync('./tsconfig.jest.json', __dirname);

module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['jest-extended/all'],
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, {
    prefix: '<rootDir>/packages/',
  }),
  moduleFileExtensions: ['ts', 'js'],
  testRegex: '\\.spec.ts$',
  rootDir: '.',
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.jest.json',
    },
  },
  coverageReporters: ['json', 'lcov', 'text-summary'],
  coverageDirectory: 'coverage',
  collectCoverageFrom: [
    'packages/**/*.ts',
    '!packages/**/*.d.ts',
    '!packages/**/index.ts',
    '!packages/**/*.interface.ts',
    '!**/node_modules/**',
    '!**/__stubs__/**',
    '!**/__fixture__/**',
    '!integration/*',
  ],
};
```

#### Package Scripts

Based on the actual `package.json`:

```json
{
  "scripts": {
    "boot": "npx lerna bootstrap",
    "rebuild": "yarn clean && yarn build",
    "build": "npx mrepo build",
    "clean": "npx mrepo clean && npx rimraf ./.mrepo",
    "test": "npx mrepo test",
    "test:coverage": "yarn test:all --coverage",
    "test:all": "yarn test:mysql && yarn test:postgres",
    "test:postgres": "yarn db:prepare:typeorm:postgres && yarn test",
    "test:mysql": "yarn db:prepare:typeorm:mysql && TYPEORM_CONNECTION=mysql yarn test",
    "start:typeorm": "npx nodemon -w ./integration/crud-typeorm -e ts node_modules/ts-node/dist/bin.js integration/crud-typeorm/main.ts",
    "db:cli:typeorm": "cd ./integration/crud-typeorm && npx ts-node -r tsconfig-paths/register ../../node_modules/typeorm/cli.js",
    "db:sync:typeorm": "yarn db:cli:typeorm schema:sync",
    "db:drop:typeorm": "yarn db:cli:typeorm schema:drop",
    "db:seeds:typeorm": "yarn db:cli:typeorm migration:run",
    "db:prepare:typeorm:postgres": "yarn db:drop:typeorm -d=orm.postgres.ts && yarn db:sync:typeorm -d=orm.postgres.ts && yarn db:seeds:typeorm -d=orm.postgres.ts",
    "db:prepare:typeorm:mysql": "yarn db:drop:typeorm -d=orm.mysql.ts && yarn db:sync:typeorm -d=orm.mysql.ts && yarn db:seeds:typeorm -d=orm.mysql.ts"
  }
}
```

### Troubleshooting

#### Common Issues and Solutions

**1. Query Performance Issues**
```typescript
// Problem: Slow queries with large datasets
// Solution: Add proper indexing and optimize queries

// Add database indexes
@Entity('users')
@Index(['email']) // Single column index
@Index(['firstName', 'lastName']) // Composite index
@Index(['createdAt']) // For sorting
export class User {
  // ... entity definition
}

// Optimize query with specific field selection
@Crud({
  query: {
    exclude: ['password', 'sensitiveData'],
    join: {
      profile: { eager: false, allow: ['avatar'] }, // Lazy load with specific fields
    },
  },
})
```

**2. Memory Leaks**
```typescript
// Problem: Memory usage keeps growing
// Solution: Implement proper caching with TTL and size limits

export class OptimizedCrudService<T> extends TypeOrmCrudService<T> {
  private cache = new LRUCache<string, any>({
    max: 1000, // Maximum number of items
    ttl: 1000 * 60 * 5, // 5 minutes TTL
  });

  async getMany(req: CrudRequest): Promise<any> {
    const cacheKey = this.generateCacheKey(req);

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const result = await super.getMany(req);
    this.cache.set(cacheKey, result);

    return result;
  }
}
```

**3. Validation Errors**
```typescript
// Problem: DTO validation not working
// Solution: Ensure proper validation pipe configuration

// In main.ts
app.useGlobalPipes(
  new ValidationPipe({
    whitelist: true, // Strip unknown properties
    forbidNonWhitelisted: true, // Throw error for unknown properties
    transform: true, // Transform payloads to DTO instances
    transformOptions: {
      enableImplicitConversion: true, // Convert string to number automatically
    },
  }),
);

// In DTO
export class CreateUserDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @Transform(({ value }) => value?.toLowerCase()) // Transform to lowercase
  email: string;

  @IsString()
  @MinLength(2)
  @MaxLength(50)
  @Transform(({ value }) => value?.trim()) // Trim whitespace
  firstName: string;
}
```

**4. Authentication Issues**
```typescript
// Problem: JWT authentication not working
// Solution: Proper JWT configuration and error handling

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('Access token is required');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
        ignoreExpiration: false, // Don't ignore expiration
      });

      // Validate payload structure
      if (!payload.sub || !payload.email) {
        throw new UnauthorizedException('Invalid token payload');
      }

      request['user'] = payload;
      return true;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid token');
      }
      throw new UnauthorizedException('Authentication failed');
    }
  }
}
```

### Migration Guide

#### Migrating from v4 to v5

**1. Update Dependencies**
```bash
npm uninstall @nestjsx/crud @nestjsx/crud-request @nestjsx/crud-typeorm
npm install @nestjsx/crud@^5.0.0 @nestjsx/crud-request@^5.0.0 @nestjsx/crud-typeorm@^5.0.0
```

**2. Update Import Statements**
```typescript
// Old (v4)
import { Crud, CrudController } from '@nestjsx/crud';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

// New (v5) - No changes needed for basic imports
import { Crud, CrudController } from '@nestjsx/crud';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';
```

**3. Update Configuration**
```typescript
// Old (v4)
@Crud({
  model: { type: User },
  query: {
    limit: 10,
    maxLimit: 100,
  },
})

// New (v5) - Enhanced configuration options
@Crud({
  model: { type: User },
  query: {
    limit: 10,
    maxLimit: 100,
    cache: 2000, // New caching option
    softDelete: true, // Enhanced soft delete support
  },
  serialize: {
    get: UserResponseDto, // Enhanced serialization
    getMany: UserResponseDto,
  },
})
```

**4. Update Custom Services**
```typescript
// Old (v4)
export class UsersService extends TypeOrmCrudService<User> {
  constructor(@InjectRepository(User) repo) {
    super(repo);
  }
}

// New (v5) - Enhanced with dependency injection
export class UsersService extends TypeOrmCrudService<User> {
  constructor(
    @InjectRepository(User) repo: Repository<User>,
    private cacheService: CacheService, // New dependencies
    private eventBus: EventBus,
  ) {
    super(repo);
  }
}
```

---

## 🎯 Conclusion

This comprehensive documentation provides a complete guide to building enterprise-grade CRUD applications with NestJS. By following the architectural patterns, design principles, and best practices outlined in this guide, you can create scalable, maintainable, and high-performance applications that meet enterprise requirements.

### Key Takeaways

1. **Architecture Excellence**: Clean Architecture with SOLID principles provides a solid foundation
2. **Design Pattern Mastery**: Proper implementation of GoF patterns enhances code quality
3. **Performance Optimization**: Algorithm analysis and caching strategies ensure scalability
4. **Security First**: Comprehensive security measures protect against common vulnerabilities
5. **Testing Strategy**: Multi-layered testing approach ensures reliability
6. **Production Readiness**: Complete deployment and monitoring setup for enterprise environments

### Next Steps

1. **Start with the Quick Start Guide** to get familiar with basic concepts
2. **Implement the Enterprise Project Structure** for your specific use case
3. **Add Security and Monitoring** components for production readiness
4. **Customize and Extend** the framework to meet your specific requirements
5. **Contribute Back** to the community with improvements and extensions

### Resources

- **GitHub Repository**: [nestjsx/crud](https://github.com/nestjsx/crud)
- **Official Documentation**: [NestJS CRUD Wiki](https://github.com/nestjsx/crud/wiki)
- **Community Support**: [GitHub Issues](https://github.com/nestjsx/crud/issues)
- **Examples**: See the `examples/` directory for complete implementations

### Contributing

We welcome contributions to the NestJS CRUD project! Here's how you can get involved:

#### Development Setup

```bash
# Fork and clone the repository
git clone https://github.com/your-username/crud.git
cd crud

# Install dependencies
yarn install

# Bootstrap packages
yarn boot

# Build all packages
yarn build

# Run tests
yarn test

# Start development server
yarn start:typeorm
```

#### Project Structure for Contributors

```
nestjs-crud/
├── 📦 packages/                 # Monorepo packages
│   ├── crud/                   # Core CRUD functionality
│   ├── crud-request/           # Request parsing & building
│   ├── crud-typeorm/           # TypeORM integration
│   └── util/                   # Shared utilities
│
├── 🧪 integration/             # Integration tests & examples
│   ├── crud-typeorm/           # TypeORM integration example
│   └── shared/                 # Shared test utilities
│
├── 📚 docs/                    # Documentation
│   └── MASTER_DOCUMENTATION.md # This comprehensive guide
│
├── 🐳 docker-compose.yml       # Development databases
├── 📋 lerna.json              # Monorepo configuration
├── 🧪 jest.config.js          # Test configuration
└── 📦 package.json            # Root package configuration
```

#### Contribution Guidelines

1. **Code Quality Standards**
   - Follow TypeScript strict mode
   - Maintain 100% test coverage for new features
   - Use ESLint and Prettier for code formatting
   - Follow SOLID principles and design patterns

2. **Testing Requirements**
   ```bash
   # Run all tests
   yarn test

   # Test with PostgreSQL
   yarn test:postgres

   # Test with MySQL
   yarn test:mysql

   # Coverage report
   yarn test:coverage
   ```

3. **Documentation Standards**
   - Update this master documentation for new features
   - Include JSDoc comments for all public APIs
   - Provide code examples for new functionality
   - Update README files in affected packages

4. **Pull Request Process**
   - Create feature branch from `main`
   - Write comprehensive tests
   - Update documentation
   - Ensure all CI checks pass
   - Request review from maintainers

#### Areas for Contribution

- **New Database Adapters**: MongoDB, Prisma, Sequelize
- **Advanced Features**: GraphQL support, real-time subscriptions
- **Performance Optimizations**: Query caching, connection pooling
- **Security Enhancements**: Rate limiting, input sanitization
- **Documentation**: Tutorials, examples, translations
- **Testing**: E2E tests, performance benchmarks

#### Community

- **GitHub Issues**: [Report bugs and request features](https://github.com/nestjsx/crud/issues)
- **Discussions**: [Community discussions and Q&A](https://github.com/nestjsx/crud/discussions)
- **Discord**: Join our community chat
- **Stack Overflow**: Tag questions with `nestjsx-crud`

---

*This comprehensive documentation is maintained by the NestJS CRUD community. For updates and contributions, please visit our [GitHub repository](https://github.com/nestjsx/crud).*
