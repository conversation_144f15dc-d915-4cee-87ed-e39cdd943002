---
name: "\U0001F41B Bug Report"
about: "If something isn't working as expected \U0001F914."
title: ''
labels: 'type: potential issue'
assignees: ''
---

## Bug Report

## Current behavior

<!-- Describe how the issue manifests. -->

## Input Code

<!-- REPL or Repo link if applicable: -->

```ts
const your = (code) => here;
```

## Expected behavior

<!-- A clear and concise description of what you expected to happen (or code). -->

## Possible Solution

<!--- Only if you have suggestions on a fix for the bug -->

## Environment

<pre><code>
Package version: X.Y.Z
<!-- Check whether this is still an issue in the most recent package(s) version -->
 
For Tooling issues:
- Node version: XX  <!-- run `node --version` -->
- Platform:  <!-- Mac, Linux, Windows -->
- Database <!-- MySQL, Postgres, etc. (with version) -->

Others:
<!-- Anything else relevant?  Operating system version, IDE, ... -->
</code></pre>

## Repository with minimal reproduction
