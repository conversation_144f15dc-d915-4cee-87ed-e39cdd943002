version: '3'

networks:
  nestjsx_crud:

services:
  postgres:
    # <PERSON>OR<PERSON> fails with Postgres v.12
    image: postgres:11.5
    ports:
      - 5455:5432
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: root
      POSTGRES_DB: nestjsx_crud
    networks:
      - nestjsx_crud

  mysql:
    platform: linux/x86_64
    image: mysql:5.7
    ports:
      - 3316:3306
    environment:
      MYSQL_DATABASE: nestjsx_crud
      MYSQL_USER: nestjsx_crud
      MYSQL_PASSWORD: nestjsx_crud
      MYSQL_ROOT_PASSWORD: nestjsx_crud

  redis:
    image: redis:alpine
    ports:
      - 6399:6379
    command: redis-server
    networks:
      - nestjsx_crud
