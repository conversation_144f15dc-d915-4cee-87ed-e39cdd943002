{"name": "@nestjsx/crud", "version": "4.0.0", "description": "Nest CRUD for RESTful APIs", "license": "MIT", "workspaces": ["packages/*"], "private": true, "scripts": {"boot": "npx lerna bootstrap", "rebuild": "yarn clean && yarn build", "build": "npx mrepo build", "clean": "npx mrepo clean && npx rimraf ./.mrepo", "test": "npx mrepo test", "test:coverage": "yarn test:all --coverage", "test:coveralls": "yarn test:coverage --coverageReporters=text-lcov | coveralls", "test:all": "yarn test:mysql && yarn test:postgres", "test:postgres": "yarn db:prepare:typeorm:postgres && yarn test", "test:mysql": "yarn db:prepare:typeorm:mysql && TYPEORM_CONNECTION=mysql yarn test", "start:typeorm": "npx nodemon -w ./integration/crud-typeorm -e ts node_modules/ts-node/dist/bin.js integration/crud-typeorm/main.ts", "db:cli:typeorm": "cd ./integration/crud-typeorm && npx ts-node -r tsconfig-paths/register ../../node_modules/typeorm/cli.js", "db:sync:typeorm": "yarn db:cli:typeorm schema:sync", "db:drop:typeorm": "yarn db:cli:typeorm schema:drop", "db:seeds:typeorm": "yarn db:cli:typeorm migration:run", "db:prepare:typeorm:postgres": "yarn db:drop:typeorm -d=orm.postgres.ts && yarn db:sync:typeorm -d=orm.postgres.ts && yarn db:seeds:typeorm -d=orm.postgres.ts", "db:prepare:typeorm:mysql": "yarn db:drop:typeorm -d=orm.mysql.ts && yarn db:sync:typeorm -d=orm.mysql.ts && yarn db:seeds:typeorm -d=orm.mysql.ts", "format": "npx pretty-quick --pattern \"packages/**/!(*.d).ts\"", "lint": "npx eslint \"packages/**/!(*.d).ts\" --fix", "commit": "npx git-cz", "postinstall": "npx opencollective", "prepare": "npx husky install", "release": "mrepo release", "release:alpha": "yarn release prerelease --preid alpha --dist-tag alpha", "release:beta": "yarn release prerelease --preid beta --dist-tag beta"}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "collective": {"type": "opencollective", "url": "https://opencollective.com/nestjsx", "donation": {"text": "Become a partner:"}}, "dependencies": {"@nestjs/common": "^9.0.0", "@nestjs/core": "^9.0.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/swagger": "5.2.1", "@nestjs/testing": "^9.0.0", "@nestjs/typeorm": "^9.0.0", "@zmotivat0r/mrepo": "0.8.1", "class-transformer": "0.3.2", "class-validator": "0.13.2", "mysql": "2.18.1", "pg": "8.7.3", "pluralize": "8.0.0", "qs": "6.10.3", "redis": "4.0.4", "reflect-metadata": "0.1.13", "rxjs": "7.5.5", "swagger-ui-express": "4.3.0", "typeorm": "^0.3.0"}, "devDependencies": {"@nuxtjs/opencollective": "0.2.2", "@types/jest": "27.4.1", "@types/node": "17.0.23", "@types/qs": "6.9.7", "@types/supertest": "2.0.12", "@typescript-eslint/eslint-plugin": "4.19.0", "@typescript-eslint/parser": "4.19.0", "commitizen": "4.2.3", "conventional-changelog-cli": "2.1.1", "coveralls": "3.1.1", "eslint": "7.22.0", "eslint-config-airbnb-base": "14.2.1", "eslint-config-prettier": "8.1.0", "eslint-plugin-import": "2.22.1", "husky": "7.0.4", "jest": "27.5.1", "jest-extended": "2.0.0", "lerna": "4.0.0", "nodemon": "2.0.15", "prettier": "2.6.1", "pretty-quick": "3.1.3", "rimraf": "3.0.2", "supertest": "6.2.2", "ts-jest": "27.1.4", "ts-node": "10.7.0", "tsconfig-extends": "1.0.1", "tsconfig-paths": "3.14.1", "typescript": "4.6.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}}