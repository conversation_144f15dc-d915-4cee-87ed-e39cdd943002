{"name": "@nestjsx/crud", "description": "NestJs CRUD for RESTful APIs", "version": "5.0.0-alpha.3", "license": "MIT", "main": "lib/index.js", "typings": "lib/index.d.ts", "publishConfig": {"access": "public"}, "files": ["lib"], "repository": {"type": "git", "url": "https://github.com/nestjsx/crud.git"}, "bugs": {"url": "https://github.com/nestjsx/crud/issues"}, "keywords": ["typescript", "typeorm", "nest", "<PERSON><PERSON><PERSON>", "rest", "restful", "api", "crud", "crud-generator", "backend", "frameworks"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "npx tsc -b"}, "dependencies": {"@nestjsx/crud-request": "^5.0.0-alpha.3", "@nestjsx/util": "^5.0.0-alpha.3", "deepmerge": "^3.2.0", "pluralize": "^8.0.0"}, "peerDependencies": {"class-transformer": "*", "class-validator": "*"}}