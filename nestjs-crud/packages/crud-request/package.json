{"name": "@nestjsx/crud-request", "description": "NestJs CRUD for RESTful APIs - request query builder", "version": "5.0.0-alpha.3", "license": "MIT", "main": "lib/index.js", "typings": "lib/index.d.ts", "publishConfig": {"access": "public"}, "files": ["lib"], "repository": {"type": "git", "url": "https://github.com/nestjsx/crud.git"}, "bugs": {"url": "https://github.com/nestjsx/crud/issues"}, "keywords": ["typescript", "typeorm", "nest", "<PERSON><PERSON><PERSON>", "rest", "restful", "api", "crud", "crud-generator", "http", "request", "request-query", "requestquery", "get", "query", "query-string", "querystring", "query-builder", "querybuilder"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "npx tsc -b"}, "dependencies": {"@nestjsx/util": "^5.0.0-alpha.3", "qs": "^6.10.3"}}