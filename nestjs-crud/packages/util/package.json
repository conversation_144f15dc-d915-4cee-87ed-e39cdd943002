{"name": "@nestjsx/util", "description": "NestJs CRUD for RESTful APIs - util", "version": "5.0.0-alpha.3", "license": "MIT", "main": "lib/index.js", "typings": "lib/index.d.ts", "publishConfig": {"access": "public"}, "files": ["lib"], "repository": {"type": "git", "url": "https://github.com/nestjsx/crud.git"}, "bugs": {"url": "https://github.com/nestjsx/crud/issues"}, "keywords": ["typescript", "typeorm", "nest", "<PERSON><PERSON><PERSON>", "rest", "restful", "api", "crud", "crud-generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "npx tsc -b"}}