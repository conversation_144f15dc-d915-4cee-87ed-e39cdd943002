# Development overrides
# This file is automatically loaded by docker-compose for development

services:
  # Override development service
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
      - deno-cache:/deno-dir
    environment:
      - DENO_ENV=development
      - DENO_NO_UPDATE_CHECK=1
      - DENO_NO_PROMPT=1
      - DENO_DIR=/deno-dir
    command:
      [
        "deno",
        "run",
        "--watch",
        "--allow-net=0.0.0.0:8000",
        "--allow-read=/app",
        "--allow-env",
        "--allow-write=/app",
        "src/app.ts",
      ]
    profiles:
      - dev

  # Development database
  postgres-dev:
    image: postgres:15-alpine
    container_name: deno-postgres-dev
    environment:
      POSTGRES_DB: deno_app_dev
      POSTGRES_USER: deno_user
      POSTGRES_PASSWORD: deno_password_dev
    ports:
      - "5433:5432"
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
    networks:
      - deno-network
    restart: unless-stopped
    profiles:
      - dev

  # Development Redis
  redis-dev:
    image: redis:alpine
    container_name: deno-redis-dev
    ports:
      - "6380:6379"
    volumes:
      - redis-dev-data:/data
    networks:
      - deno-network
    restart: unless-stopped
    profiles:
      - dev

  # Development monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: deno-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=200h"
      - "--web.enable-lifecycle"
    networks:
      - deno-network
    restart: unless-stopped
    profiles:
      - dev

  grafana:
    image: grafana/grafana:latest
    container_name: deno-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    networks:
      - deno-network
    restart: unless-stopped
    profiles:
      - dev

volumes:
  deno-cache:
    driver: local
  postgres-dev-data:
    driver: local
  redis-dev-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
