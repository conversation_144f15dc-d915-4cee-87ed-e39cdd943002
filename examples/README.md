# 💡 **Implementation Examples - Real-World Code Samples**

> **Comprehensive examples showcasing enterprise patterns, integrations, and best practices**

## 📋 **Overview**

The `examples/` directory contains **real-world implementation examples** that demonstrate enterprise patterns, design principles, and best practices. These examples serve as learning resources and reference implementations for building production-ready applications.

## 🏗️ **Architecture Overview**

```
examples/
├── complete-features/   # 🎯 End-to-End Feature Implementations
├── patterns/           # 🏛️ Design Pattern Examples
├── integrations/       # 🔗 Third-Party Integration Examples
└── best-practices/     # ✅ Best Practice Implementations
```

## 📁 **Examples Directory & Guides**

| Category | Technology Stack | Purpose | README Guide | Status |
|----------|------------------|---------|--------------|--------|
| [🎯 **complete-features/**](complete-features/README.md) | Full-stack implementations | Complete feature examples | [📖 Guide](complete-features/README.md) | 🔄 |
| [🏛️ **patterns/**](patterns/README.md) | Design pattern implementations | Pattern examples and usage | [📖 Guide](patterns/README.md) | 🔄 |
| [🔗 **integrations/**](integrations/README.md) | Third-party integrations | Integration examples | [📖 Guide](integrations/README.md) | 🔄 |
| [✅ **best-practices/**](best-practices/README.md) | Best practice implementations | Code quality examples | [📖 Guide](best-practices/README.md) | 🔄 |

## 🎯 **Complete Features**

**End-to-end feature implementations** with full stack examples:

### **🔧 Key Features**
- ✅ **User Authentication** - Complete auth flow with JWT
- ✅ **Task Management** - CRUD operations with real-time updates
- ✅ **File Upload** - Secure file handling with cloud storage
- ✅ **Real-time Chat** - WebSocket-based messaging
- ✅ **Payment Processing** - Stripe integration example
- ✅ **Email Notifications** - Automated email workflows

### **📁 Structure**
```
complete-features/
├── user-authentication/     # Complete auth system
│   ├── backend/
│   │   ├── auth-service/
│   │   ├── user-service/
│   │   └── api-gateway/
│   ├── frontend/
│   │   ├── login-component/
│   │   ├── register-component/
│   │   └── profile-component/
│   ├── tests/
│   └── README.md
├── task-management/         # Task CRUD with real-time
│   ├── backend/
│   ├── frontend/
│   ├── websocket/
│   ├── tests/
│   └── README.md
├── file-upload/            # Secure file handling
│   ├── backend/
│   ├── frontend/
│   ├── storage/
│   ├── tests/
│   └── README.md
├── real-time-chat/         # WebSocket messaging
│   ├── backend/
│   ├── frontend/
│   ├── websocket/
│   ├── tests/
│   └── README.md
├── payment-processing/     # Stripe integration
│   ├── backend/
│   ├── frontend/
│   ├── webhooks/
│   ├── tests/
│   └── README.md
└── email-notifications/    # Email workflows
    ├── backend/
    ├── templates/
    ├── queue/
    ├── tests/
    └── README.md
```

### **🔐 User Authentication Example**
```typescript
// complete-features/user-authentication/backend/auth-service/src/auth.controller.ts
import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto, RegisterDto } from './dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    const user = await this.authService.register(registerDto);
    const tokens = await this.authService.generateTokens(user);
    
    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      ...tokens,
    };
  }

  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    const user = await this.authService.validateUser(
      loginDto.email,
      loginDto.password
    );
    
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const tokens = await this.authService.generateTokens(user);
    
    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      ...tokens,
    };
  }

  @Post('refresh')
  async refresh(@Body('refreshToken') refreshToken: string) {
    return this.authService.refreshTokens(refreshToken);
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  async logout(@Request() req) {
    await this.authService.logout(req.user.id);
    return { message: 'Logged out successfully' };
  }
}
```

### **📱 Frontend Auth Component**
```typescript
// complete-features/user-authentication/frontend/src/components/LoginForm.tsx
import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { LoginCredentials } from '../types/auth';

export const LoginForm: React.FC = () => {
  const [credentials, setCredentials] = useState<LoginCredentials>({
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await login(credentials);
      // Redirect handled by auth context
    } catch (error) {
      console.error('Login failed:', error);
      // Handle error (show toast, etc.)
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="email" className="block text-sm font-medium">
          Email
        </label>
        <input
          id="email"
          type="email"
          required
          value={credentials.email}
          onChange={(e) =>
            setCredentials({ ...credentials, email: e.target.value })
          }
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
        />
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium">
          Password
        </label>
        <input
          id="password"
          type="password"
          required
          value={credentials.password}
          onChange={(e) =>
            setCredentials({ ...credentials, password: e.target.value })
          }
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
        />
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50"
      >
        {isLoading ? 'Signing in...' : 'Sign in'}
      </button>
    </form>
  );
};
```

## 🏛️ **Design Patterns**

**Enterprise design pattern implementations**:

### **🔧 Key Patterns**
- ✅ **Repository Pattern** - Data access abstraction
- ✅ **CQRS Pattern** - Command Query Responsibility Segregation
- ✅ **Observer Pattern** - Event-driven notifications
- ✅ **Factory Pattern** - Object creation abstraction
- ✅ **Strategy Pattern** - Algorithm selection
- ✅ **Decorator Pattern** - Behavior extension

### **📁 Structure**
```
patterns/
├── repository/             # Repository pattern
│   ├── interfaces/
│   ├── implementations/
│   ├── examples/
│   └── README.md
├── cqrs/                   # CQRS pattern
│   ├── commands/
│   ├── queries/
│   ├── handlers/
│   ├── examples/
│   └── README.md
├── observer/               # Observer pattern
│   ├── subjects/
│   ├── observers/
│   ├── examples/
│   └── README.md
├── factory/                # Factory pattern
│   ├── factories/
│   ├── products/
│   ├── examples/
│   └── README.md
├── strategy/               # Strategy pattern
│   ├── strategies/
│   ├── context/
│   ├── examples/
│   └── README.md
└── decorator/              # Decorator pattern
    ├── decorators/
    ├── components/
    ├── examples/
    └── README.md
```

### **🏛️ Repository Pattern Example**
```typescript
// patterns/repository/interfaces/IUserRepository.ts
export interface IUserRepository {
  findById(id: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findAll(page: number, limit: number): Promise<User[]>;
  save(user: User): Promise<void>;
  delete(id: string): Promise<void>;
  exists(email: string): Promise<boolean>;
}

// patterns/repository/implementations/PostgresUserRepository.ts
import { IUserRepository } from '../interfaces/IUserRepository';
import { User } from '../entities/User';

export class PostgresUserRepository implements IUserRepository {
  constructor(
    private userModel: UserModel,
    private mapper: UserMapper,
    private cache: ICacheService
  ) {}

  async findById(id: string): Promise<User | null> {
    // Check cache first
    const cached = await this.cache.get(`user:${id}`);
    if (cached) {
      return this.mapper.toDomain(JSON.parse(cached));
    }

    // Query database
    const userData = await this.userModel.findById(id);
    if (!userData) {
      return null;
    }

    const user = this.mapper.toDomain(userData);
    
    // Cache result
    await this.cache.set(`user:${id}`, JSON.stringify(userData), 300);
    
    return user;
  }

  async save(user: User): Promise<void> {
    const userData = this.mapper.toPersistence(user);
    
    if (await this.userModel.findById(user.id.value)) {
      await this.userModel.update(user.id.value, userData);
    } else {
      await this.userModel.create(userData);
    }

    // Invalidate cache
    await this.cache.delete(`user:${user.id.value}`);
  }
}
```

### **⚡ CQRS Pattern Example**
```typescript
// patterns/cqrs/commands/CreateUserCommand.ts
export class CreateUserCommand {
  constructor(
    public readonly email: string,
    public readonly password: string,
    public readonly firstName: string,
    public readonly lastName: string
  ) {}
}

// patterns/cqrs/handlers/CreateUserCommandHandler.ts
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { CreateUserCommand } from '../commands/CreateUserCommand';

@CommandHandler(CreateUserCommand)
export class CreateUserCommandHandler implements ICommandHandler<CreateUserCommand> {
  constructor(
    private userRepository: IUserRepository,
    private passwordHasher: IPasswordHasher,
    private eventBus: IEventBus
  ) {}

  async execute(command: CreateUserCommand): Promise<string> {
    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(command.email);
    if (existingUser) {
      throw new ConflictException('User already exists');
    }

    // Hash password
    const hashedPassword = await this.passwordHasher.hash(command.password);

    // Create user entity
    const user = User.create({
      email: Email.create(command.email),
      password: Password.create(hashedPassword),
      firstName: command.firstName,
      lastName: command.lastName,
    });

    // Save user
    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }

    return user.id.value;
  }
}
```

## 🔗 **Third-Party Integrations**

**Real-world integration examples**:

### **🔧 Key Integrations**
- ✅ **Stripe Payment** - Payment processing
- ✅ **SendGrid Email** - Email delivery
- ✅ **AWS S3** - File storage
- ✅ **OpenAI API** - AI/ML services
- ✅ **Slack API** - Team notifications
- ✅ **Google OAuth** - Social authentication

### **📁 Structure**
```
integrations/
├── stripe/                 # Stripe payment integration
│   ├── payment-service/
│   ├── webhook-handler/
│   ├── examples/
│   └── README.md
├── sendgrid/              # SendGrid email integration
│   ├── email-service/
│   ├── templates/
│   ├── examples/
│   └── README.md
├── aws-s3/                # AWS S3 integration
│   ├── storage-service/
│   ├── upload-handler/
│   ├── examples/
│   └── README.md
├── openai/                # OpenAI API integration
│   ├── ai-service/
│   ├── prompt-templates/
│   ├── examples/
│   └── README.md
├── slack/                 # Slack API integration
│   ├── notification-service/
│   ├── bot-commands/
│   ├── examples/
│   └── README.md
└── google-oauth/          # Google OAuth integration
    ├── auth-service/
    ├── oauth-handler/
    ├── examples/
    └── README.md
```

## ✅ **Best Practices**

**Code quality and best practice examples**:

### **🔧 Key Areas**
- ✅ **Error Handling** - Comprehensive error management
- ✅ **Logging** - Structured logging practices
- ✅ **Testing** - Testing strategies and examples
- ✅ **Security** - Security implementation examples
- ✅ **Performance** - Performance optimization techniques
- ✅ **Documentation** - Code documentation standards

### **📁 Structure**
```
best-practices/
├── error-handling/        # Error handling patterns
│   ├── global-exception-filter/
│   ├── custom-exceptions/
│   ├── error-boundaries/
│   └── README.md
├── logging/               # Logging best practices
│   ├── structured-logging/
│   ├── correlation-ids/
│   ├── log-aggregation/
│   └── README.md
├── testing/               # Testing strategies
│   ├── unit-testing/
│   ├── integration-testing/
│   ├── e2e-testing/
│   └── README.md
├── security/              # Security practices
│   ├── input-validation/
│   ├── authentication/
│   ├── authorization/
│   └── README.md
├── performance/           # Performance optimization
│   ├── caching/
│   ├── database-optimization/
│   ├── api-optimization/
│   └── README.md
└── documentation/         # Documentation standards
    ├── api-documentation/
    ├── code-comments/
    ├── readme-templates/
    └── README.md
```

## 🚀 **Usage Guidelines**

### **📋 How to Use Examples**
1. **Browse Categories** - Explore different example categories
2. **Read Documentation** - Each example includes detailed README
3. **Copy and Adapt** - Use examples as starting points
4. **Follow Patterns** - Implement consistent patterns
5. **Test Thoroughly** - Adapt tests for your use case

### **🔧 Running Examples**
```bash
# Navigate to specific example
cd examples/complete-features/user-authentication

# Install dependencies
npm install

# Set up environment
cp .env.example .env

# Run the example
npm run dev

# Run tests
npm test
```

### **📊 Example Structure**
Each example follows a consistent structure:
```
example-name/
├── README.md              # Detailed documentation
├── package.json           # Dependencies and scripts
├── .env.example          # Environment template
├── src/                  # Source code
├── tests/                # Test files
├── docs/                 # Additional documentation
└── docker-compose.yml    # Local development setup
```

## 🧪 **Testing Examples**

### **🔬 Test Coverage**
- ✅ **Unit Tests** - Individual component testing
- ✅ **Integration Tests** - Component interaction testing
- ✅ **E2E Tests** - Full workflow testing
- ✅ **Performance Tests** - Load and stress testing
- ✅ **Security Tests** - Vulnerability testing

### **📊 Quality Metrics**
- ✅ **Code Coverage** - Minimum 80% coverage
- ✅ **Performance Benchmarks** - Response time targets
- ✅ **Security Scans** - Vulnerability assessments
- ✅ **Code Quality** - SonarQube analysis
- ✅ **Documentation** - Comprehensive documentation

## 🔗 **Related Documentation**

- [🏗️ Project Structure](../docs/PROJECT_STRUCTURE.md)
- [📝 Templates](../templates/README.md)
- [⚡ Services Layer](../services/README.md)
- [🎯 Applications Layer](../apps/README.md)
- [🧠 Knowledge Base](../docs/07-knowledge-base/README.md)

## 🤝 **Contributing**

1. **Add Examples** - Contribute new implementation examples
2. **Improve Documentation** - Enhance example documentation
3. **Update Patterns** - Add new design pattern examples
4. **Test Examples** - Ensure examples work correctly
5. **Share Knowledge** - Document lessons learned

---

> **Next Steps**: Explore individual example category READMEs for detailed implementation guides and start building with proven patterns.
