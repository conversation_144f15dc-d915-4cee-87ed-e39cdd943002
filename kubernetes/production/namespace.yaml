# 🚀 Kubernetes Production Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: enterprise-platform
  labels:
    name: enterprise-platform
    environment: production
    team: platform
    project: enterprise-api
  annotations:
    description: "Enterprise Platform Production Environment"
---
# 🔒 Network Policy for Security Isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: enterprise-platform-network-policy
  namespace: enterprise-platform
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector: {}
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
---
# 💾 Resource Quota for the Namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: enterprise-platform-quota
  namespace: enterprise-platform
spec:
  hard:
    requests.cpu: "8"
    requests.memory: 16Gi
    limits.cpu: "16" 
    limits.memory: 32Gi
    persistentvolumeclaims: "20"
    pods: "50"
    secrets: "20"
    services: "10"
    services.loadbalancers: "2"
    services.nodeports: "5"
---
# ⚡ Limit Range for Pods
apiVersion: v1
kind: LimitRange
metadata:
  name: enterprise-platform-limits
  namespace: enterprise-platform
spec:
  limits:
  - type: Pod
    max:
      cpu: "4"
      memory: 8Gi
    min:
      cpu: 100m
      memory: 128Mi
  - type: Container
    default:
      cpu: 500m
      memory: 512Mi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    max:
      cpu: "2"
      memory: 4Gi
    min:
      cpu: 50m
      memory: 64Mi