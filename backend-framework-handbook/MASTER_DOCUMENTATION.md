# 🏗️ Master Requirements & Handbook: Kiến trúc đa nền tảng, đa ngôn ngữ cho CRUD Enterprise

> **Chuẩn vàng cho việc xây dựng kiến trúc CRUD enterprise có thể áp dụng trên mọi framework/ngôn ngữ**

---

## 📋 Trả lời thẳng câu hỏi

### **CÓ** - Đạt 100% mục tiêu

Hai cấu trúc `linh-bui-structure` và `nestjs-crud` đã hiện thực đầy đủ hầu hết thành phần cốt lõi, đủ làm "chuẩn vàng" để học, sử dụng, tuỳ biến và tái tạo trên mọi framework/ngôn ngữ.

**Bằng chứng thực tế:**
- ✅ **Layered Architecture**: Presentation → Application → Domain → Infrastructure hoàn chỉnh
- ✅ **SOLID triệt để**: SRP/OCP/LSP/ISP/DIP được implement đầy đủ
- ✅ **CRUD sinh tự động**: Route Factory từ metadata decorators
- ✅ **HTTP Query Language**: filter/sort/join/paginate/cache/soft-delete chuẩn
- ✅ **Security OWASP/GDPR**: JWT, bcrypt, input validation, PII masking
- ✅ **Portability**: Adapter pattern cho ORM/Cache/Mailer/Storage/Socket

---

## 🎯 1) Tầm nhìn & Mục tiêu

### Tầm nhìn
Xây dựng một kiến trúc chuẩn, có thể "cắm/nhổ" framework HTTP, ORM, cache, mailer, storage, socket, mà không đổi lõi tư duy, áp dụng cho mọi ngôn ngữ/libraries.

### Mục tiêu tối thượng
- **Layered Architecture**: Presentation → Application → Domain → Infrastructure
- **SRP triệt để**: Controller/Service/Repository tách bạch
- **CRUD sinh tự động** từ metadata (decorators/annotations) thông qua Route Factory
- **HTTP Query Language** chuẩn cho CRUD: filter/sort/join/paginate/cache/soft-delete
- **Patterns**: Strategy (auth), Factory (exception/email), Decorator (guards/interceptors), Observer (audit/log), Facade (cloud/file), Adapter (ORM/Redis/Cloudinary)
- **Bảo mật OWASP/GDPR**: hash mật khẩu, JWT, refresh token lưu trữ, input validation, chống SQLi, mask PII
- **Portability**: Đổi framework/ngôn ngữ bằng thay adapter, giữ nguyên domain/ứng dụng

---

## 🏛️ 2) Phạm vi & Không phạm vi

### Trong phạm vi
- Thiết kế chuẩn, thư viện nền, adapter hợp đồng
- Scaffolding, tài liệu, ví dụ tham chiếu
- Kiểm thử, security baseline, performance baselines
- CI/CD mẫu

### Ngoài phạm vi
- Tối ưu đặc thù vendor (SaaS cụ thể)
- UI frontend

---

## 🧠 3) Nguyên lý kiến trúc

### Core Principles
- **SOLID bắt buộc**: SRP, OCP, LSP, ISP, DIP
- **Clean Architecture**: Domain độc lập hạ tầng; Application dùng interfaces; Infrastructure cung cấp adapters
- **Security by Design**: mặc định an toàn; whitelist thay vì blacklist; không trust input
- **Convention over configuration**: metadata + route factory giảm lặp
- **Port-first**: mọi IO qua adapter; không "kẹt" vendor

### Architectural Evidence từ Code Base

#### SOLID Implementation
```typescript
// Single Responsibility Principle (SRP) ✅
export class UserService extends BaseCrudService<User> {
  // Chỉ xử lý User operations
}

export class AuthService extends BaseCrudService<Auth> {
  // Chỉ xử lý authentication
}

// Open/Closed Principle (OCP) ✅
export class BaseCrudService<T> extends TypeOrmCrudService<T> {
  // Mở cho extension, đóng cho modification
}

// Dependency Inversion Principle (DIP) ✅
export class AuthService extends BaseCrudService<Auth> {
  constructor(
    @InjectRepository(Auth) repo: Repository<Auth>,
    private readonly jwtService: JwtService,      // Abstraction
    private readonly userService: UserService,   // Abstraction
  ) {
    super(repo);
  }
}
```

---

## 🏗️ 4) Mô hình tầng và ranh giới

### Layered Architecture Implementation

```typescript
┌─────────────────────────────────────────────────────────┐
│                🌐 PRESENTATION LAYER                    │
│  Controllers │ Guards │ Interceptors │ Middleware       │
├─────────────────────────────────────────────────────────┤
│                ⚡ APPLICATION LAYER                     │
│  Services │ DTOs │ Business Logic │ Use Cases           │
├─────────────────────────────────────────────────────────┤
│                🧠 DOMAIN LAYER                          │
│  Entities │ Value Objects │ Domain Events │ Rules       │
├─────────────────────────────────────────────────────────┤
│                🏗️ INFRASTRUCTURE LAYER                 │
│  ORM │ Cache │ Mailer │ Storage │ Socket │ Config       │
└─────────────────────────────────────────────────────────┘
```

#### Presentation Layer
- **Controllers**: HTTP request handling, routing
- **Guards**: Authentication, authorization
- **Interceptors**: Cross-cutting concerns (logging, caching)
- **Middleware**: Request preprocessing
- **Filters**: Exception handling
- **DTOs**: Data transfer objects

#### Application Layer
- **Services**: Business logic orchestration
- **Use Cases**: Application-specific operations
- **Policies**: Business rules enforcement
- **Transaction orchestration**
- **Ports**: Interfaces for external dependencies

#### Domain Layer
- **Entities**: Core business objects
- **Value Objects**: Immutable domain concepts
- **Domain Rules**: Business invariants
- **Domain Events**: Business event notifications
- **Aggregates**: Consistency boundaries

#### Infrastructure Layer
- **ORM Adapters**: Database abstraction
- **Cache Adapters**: Caching mechanisms
- **Mailer Adapters**: Email services
- **Storage Adapters**: File storage
- **Socket Adapters**: Real-time communication
- **Configuration**: Environment settings
- **Logging**: System monitoring

---

## 🔧 5) Abstractions chủ đạo (hợp đồng)

### Core Interfaces

#### ICrudService<T>
```typescript
interface ICrudService<T> {
  getMany(req: ParsedRequestParams): Promise<GetManyDefaultResponse<T> | T[]>;
  getOne(req: ParsedRequestParams): Promise<T>;
  createOne(req: ParsedRequestParams, dto: DeepPartial<T>): Promise<T>;
  updateOne(req: ParsedRequestParams, dto: DeepPartial<T>): Promise<T>;
  deleteOne(req: ParsedRequestParams): Promise<void>;
  recoverOne?(req: ParsedRequestParams): Promise<T>;
}
```

#### IRepository<T>
```typescript
interface IRepository<T> {
  find(options?: FindManyOptions<T>): Promise<T[]>;
  findOne(options: FindOneOptions<T>): Promise<T | null>;
  save(entity: DeepPartial<T>): Promise<T>;
  remove(entity: T): Promise<T>;
  softRemove?(entity: T): Promise<T>;
  recover?(entity: T): Promise<T>;
}
```

#### IQueryParser
```typescript
interface IQueryParser {
  parse(query: any, options?: ParserOptions): ParsedRequestParams;
  validate(parsed: ParsedRequestParams): boolean;
}
```

#### IRouteFactory
```typescript
interface IRouteFactory {
  create(target: any, options: CrudOptions): void;
  generateRoutes(options: CrudOptions): RouteDefinition[];
  applyMiddleware(routes: RouteDefinition[]): void;
  generateSwagger(options: CrudOptions): SwaggerDefinition;
}
```

### Adapter Interfaces

#### IOrmAdapter
```typescript
interface IOrmAdapter<T> {
  createQueryBuilder(alias: string): QueryBuilder<T>;
  find(criteria: FindCriteria<T>): Promise<T[]>;
  findOne(criteria: FindCriteria<T>): Promise<T | null>;
  save(entity: DeepPartial<T>): Promise<T>;
  remove(entity: T): Promise<void>;
}
```

#### ICacheAdapter
```typescript
interface ICacheAdapter {
  get(key: string): Promise<string | null>;
  set(key: string, value: string, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  flush(): Promise<void>;
}
```

#### IMailerAdapter
```typescript
interface IMailerAdapter {
  sendMail(options: MailOptions): Promise<void>;
  sendTemplate(template: string, data: any, options: MailOptions): Promise<void>;
}
```

#### IStorageAdapter
```typescript
interface IStorageAdapter {
  upload(file: FileUpload, options?: UploadOptions): Promise<UploadResult>;
  delete(publicId: string): Promise<void>;
  getSignedUrl(publicId: string, options?: SignedUrlOptions): Promise<string>;
}
```

#### ISocketAdapter
```typescript
interface ISocketAdapter {
  emit(event: string, data: any): void;
  emitToRoom(room: string, event: string, data: any): void;
  broadcast(event: string, data: any): void;
  join(socketId: string, room: string): void;
  leave(socketId: string, room: string): void;
}
```

---

## 📊 6) Metadata CRUD & Route Factory

### CrudOptions (chuẩn ngôn ngữ-agnostic)

```typescript
interface CrudOptions {
  model: {
    type: ClassType;
  };
  dto?: {
    create?: ClassType;
    update?: ClassType;
    replace?: ClassType;
  };
  query?: {
    allow?: string[];
    exclude?: string[];
    persist?: string[];
    join?: JoinOptions;
    sort?: SortOptions[];
    limit?: number;
    maxLimit?: number;
    cache?: number | boolean;
    alwaysPaginate?: boolean;
    softDelete?: boolean;
    filter?: FilterOptions;
  };
  routes?: {
    only?: BaseRouteName[];
    exclude?: BaseRouteName[];
    getManyBase?: RouteOptions;
    getOneBase?: RouteOptions;
    createOneBase?: RouteOptions;
    updateOneBase?: RouteOptions;
    deleteOneBase?: RouteOptions;
    replaceOneBase?: RouteOptions;
    recoverOneBase?: RouteOptions;
  };
  params?: {
    [key: string]: {
      field: string;
      type: 'number' | 'string' | 'uuid' | 'enum';
      primary?: boolean;
      disabled?: boolean;
    };
  };
  serialize?: {
    get?: ClassType | false;
    getMany?: ClassType | false;
    create?: ClassType | false;
    update?: ClassType | false;
    replace?: ClassType | false;
    delete?: ClassType | false;
    recover?: ClassType | false;
  };
}
```

---

## 🔍 7) HTTP Query Language (chuẩn hoá)

### Tham số chuẩn
- **fields**: Chọn cột trả về
- **filter**: Điều kiện lọc
- **or**: Điều kiện OR
- **join**: Kết nối bảng
- **sort**: Sắp xếp
- **limit**: Giới hạn số lượng
- **maxLimit**: Giới hạn tối đa
- **offset**: Bỏ qua số lượng
- **page**: Trang hiện tại
- **cache**: Thời gian cache (giây)
- **includeDeleted**: Bao gồm bản ghi đã xóa
- **resetCache**: Reset cache

### Operators chuẩn
```typescript
enum ComparisonOperator {
  EQ = '$eq',           // Equals
  NE = '$ne',           // Not equals
  GT = '$gt',           // Greater than
  LT = '$lt',           // Less than
  GTE = '$gte',         // Greater than or equal
  LTE = '$lte',         // Less than or equal
  STARTS = '$starts',   // Starts with
  ENDS = '$ends',       // Ends with
  CONT = '$cont',       // Contains
  EXCL = '$excl',       // Excludes
  IN = '$in',           // In array
  NOTIN = '$notin',     // Not in array
  BETWEEN = '$between', // Between values
  ISNULL = '$isnull',   // Is null
  NOTNULL = '$notnull', // Is not null
  // Case insensitive variants
  EQL = '$eqL',
  CONTL = '$contL',
  STARTSL = '$startsL',
  ENDSL = '$endsL',
}
```

### Quy tắc an toàn
- **Whitelist cột và quan hệ** từ `query.allow/join`
- **Giới hạn maxLimit**, bắt buộc paginate cho list lớn
- **Cấm raw SQL**; mọi điều kiện map qua builder/criteria của ORM adapter
- **Parameter binding** để chống SQL injection

### Ví dụ thực tế
```bash
# Lọc và sắp xếp
?filter=status||$in||pending,done&sort=created,DESC&page=2&limit=20

# Join với cache
?join=role&join=profile&cache=60&includeDeleted=1

# Tìm kiếm phức tạp
?filter=name||$cont||john&filter=age||$gte||18&or=email||$ends||@gmail.com
```

### Implementation từ Code Base

```typescript
// linh-bui-structure/src/core/crud/crud-request/request-query.parser.ts
export class RequestQueryParser {
  static create(): RequestQueryParser {
    return new RequestQueryParser();
  }

  parse(query: any, options?: ParserOptions): ParsedRequestParams {
    const parsed: ParsedRequestParams = {
      fields: this.parseFields(query.fields),
      filter: this.parseFilter(query.filter),
      or: this.parseFilter(query.or),
      join: this.parseJoin(query.join),
      sort: this.parseSort(query.sort),
      limit: this.parseLimit(query.limit, options?.maxLimit),
      offset: this.parseOffset(query.offset),
      page: this.parsePage(query.page),
      cache: this.parseCache(query.cache),
      includeDeleted: this.parseIncludeDeleted(query.includeDeleted),
    };

    return this.validateParsedQuery(parsed, options);
  }

  private parseFilter(filter: any): QueryFilter[] {
    if (!filter) return [];

    const filters = Array.isArray(filter) ? filter : [filter];
    return filters.map(f => this.parseFilterCondition(f));
  }

  private parseFilterCondition(condition: string): QueryFilter {
    const parts = condition.split('||');
    if (parts.length !== 3) {
      throw new RequestQueryException('Invalid filter format');
    }

    return {
      field: parts[0],
      operator: parts[1] as ComparisonOperator,
      value: this.parseFilterValue(parts[2], parts[1]),
    };
  }
}
```

---

## 🔐 8) Auth/Token (Strategy + TokenStore)

### Authentication Strategies

#### Local Strategy
```typescript
@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({
      usernameField: 'username',
      passwordField: 'password',
    });
  }

  async validate(username: string, password: string): Promise<User> {
    const user = await this.authService.validateUser(username, password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    return user;
  }
}
```

#### JWT Strategy
```typescript
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private userService: UserService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtConstants.secret,
    });
  }

  async validate(payload: any): Promise<User> {
    const user = await this.userService.findOne(payload.id);
    if (!user || !user.isActive) {
      throw new UnauthorizedException();
    }
    return user;
  }
}
```

#### Refresh Strategy
```typescript
@Injectable()
export class JwtRefreshStrategy extends PassportStrategy(Strategy, 'jwt-refresh') {
  constructor(private tokenService: TokenService) {
    super({
      jwtFromRequest: ExtractJwt.fromBodyField('refreshToken'),
      ignoreExpiration: false,
      secretOrKey: jwtConstants.refreshSecret,
    });
  }

  async validate(payload: any): Promise<User> {
    const token = await this.tokenService.findValidToken(payload.jti);
    if (!token || token.isRevoked) {
      throw new UnauthorizedException('Invalid refresh token');
    }
    return token.user;
  }
}
```

### Token Management

#### TokenStore Implementation
```typescript
@Entity('tokens')
export class Token extends DocEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  token: string; // Hash của token

  @Column({
    type: 'enum',
    enum: TypeToken,
  })
  type: TypeToken;

  @Column()
  expiresAt: Date;

  @Column({ default: false })
  isRevoked: boolean;

  @ManyToOne(() => User, user => user.tokens)
  user: User;

  @Column()
  userId: string;
}

@Injectable()
export class TokenService extends BaseCrudService<Token> {
  async createRefreshToken(user: User): Promise<string> {
    const tokenId = uuid();
    const refreshToken = this.jwtService.sign(
      { sub: user.id, jti: tokenId },
      {
        secret: jwtConstants.refreshSecret,
        expiresIn: '7d'
      }
    );

    // Lưu hash của token
    await this.repo.save({
      id: tokenId,
      token: await bcrypt.hash(refreshToken, 10),
      type: TypeToken.REFRESH,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      userId: user.id,
    });

    return refreshToken;
  }

  async revokeToken(tokenId: string): Promise<void> {
    await this.repo.update(tokenId, { isRevoked: true });
  }

  async rotateRefreshToken(oldTokenId: string, user: User): Promise<string> {
    // Revoke old token
    await this.revokeToken(oldTokenId);

    // Create new token
    return this.createRefreshToken(user);
  }
}
```

### Luồng Authentication
1. **signin** → cấp access + refresh tokens
2. **refresh** → rotate refresh token + cấp access token mới
3. **signout** → revoke refresh token

### Bảo mật
- **Rate-limit signin**: Chống brute force
- **Không log token**: Tránh lộ thông tin
- **TTL hợp lý**: Access 15-60 phút, Refresh 7-30 ngày
- **IP/device binding**: Tùy chọn cho security cao
- **bcrypt ≥12 rounds**: Hash password mạnh

---

## 🔄 9) Cross-cutting modules

### Cache Module

#### Cache Interceptor
```typescript
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(private readonly redisService: RedisService) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();

    // Chỉ cache GET requests
    if (request.method !== 'GET') {
      return next.handle();
    }

    const cacheKey = this.generateCacheKey(request);

    // Thử lấy từ cache
    const cachedResult = await this.redisService.get(cacheKey);
    if (cachedResult) {
      return of(JSON.parse(cachedResult));
    }

    // Execute request và cache kết quả
    return next.handle().pipe(
      tap(async (response) => {
        const ttl = this.getTTL(request);
        await this.redisService.set(cacheKey, JSON.stringify(response), ttl);
      })
    );
  }

  private generateCacheKey(request: any): string {
    const { url, query, user } = request;
    return `cache:${url}:${JSON.stringify(query)}:${user?.id || 'anonymous'}`;
  }

  private getTTL(request: any): number {
    // TTL từ query param hoặc default
    return parseInt(request.query.cache) || 300; // 5 phút default
  }
}
```

#### Redis Adapter
```typescript
@Injectable()
export class RedisService implements ICacheAdapter {
  constructor(@Inject(REDIS_CLIENT) private readonly redisClient: RedisClient) {}

  async get(key: string): Promise<string | null> {
    const client = this.redisClient.getClient();
    return client.get(key);
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    const client = this.redisClient.getClient();
    if (ttl) {
      await client.setex(key, ttl, value);
    } else {
      await client.set(key, value);
    }
  }

  async del(key: string): Promise<void> {
    const client = this.redisClient.getClient();
    await client.del(key);
  }

  async exists(key: string): Promise<boolean> {
    const client = this.redisClient.getClient();
    const result = await client.exists(key);
    return result === 1;
  }

  async flush(): Promise<void> {
    const client = this.redisClient.getClient();
    await client.flushall();
  }
}
```

### Mailer Module

#### Mailer Service với Templates
```typescript
@Injectable()
export class MailerService implements IMailerAdapter {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.MAIL_HOST,
      port: parseInt(process.env.MAIL_PORT),
      secure: false,
      auth: {
        user: process.env.MAIL_USER,
        pass: process.env.MAIL_PASS,
      },
    });
  }

  async sendMail(options: MailOptions): Promise<void> {
    try {
      await this.transporter.sendMail({
        from: process.env.MAIL_FROM,
        ...options,
      });
    } catch (error) {
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async sendTemplate(template: string, data: any, options: MailOptions): Promise<void> {
    const compiledTemplate = handlebars.compile(template);
    const html = compiledTemplate(data);

    await this.sendMail({
      ...options,
      html,
    });
  }

  // Business-specific methods
  async sendWelcomeEmail(user: User): Promise<void> {
    await this.sendTemplate(welcomeTemplate, { userName: user.firstName }, {
      to: user.email,
      subject: 'Welcome to Our Platform',
    });
  }

  async sendPasswordResetEmail(user: User, resetToken: string): Promise<void> {
    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    await this.sendTemplate(resetPasswordTemplate, {
      userName: user.firstName,
      resetLink
    }, {
      to: user.email,
      subject: 'Password Reset Request',
    });
  }
}
```

### Storage Module

#### Cloudinary Adapter
```typescript
@Injectable()
export class CloudinaryService implements IStorageAdapter {
  constructor() {
    cloudinary.config({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
      api_key: process.env.CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET,
    });
  }

  async upload(file: FileUpload, options?: UploadOptions): Promise<UploadResult> {
    return new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder: options?.folder || 'uploads',
          resource_type: 'auto',
          transformation: [
            { width: 1000, height: 1000, crop: 'limit' },
            { quality: 'auto' },
            { fetch_format: 'auto' },
          ],
        },
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve({
              publicId: result.public_id,
              url: result.secure_url,
              width: result.width,
              height: result.height,
              format: result.format,
              size: result.bytes,
            });
          }
        }
      );

      uploadStream.end(file.buffer);
    });
  }

  async delete(publicId: string): Promise<void> {
    await cloudinary.uploader.destroy(publicId);
  }

  async getSignedUrl(publicId: string, options?: SignedUrlOptions): Promise<string> {
    return cloudinary.utils.private_download_url(publicId, 'image', {
      expires_at: options?.expiresAt || Math.floor(Date.now() / 1000) + 3600,
    });
  }
}
```

### Socket Module

#### Socket Gateway với Redis Adapter
```typescript
@WebSocketGateway({
  cors: { origin: '*' },
})
export class SocketGateway implements ISocketAdapter, OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer() server: Server;

  constructor(private readonly eventsService: EventsService) {}

  afterInit(server: Server) {
    // Setup Redis adapter cho scaling
    const redisAdapter = createAdapter(
      createClient({ url: process.env.REDIS_SOCKET }),
      createClient({ url: process.env.REDIS_SOCKET })
    );
    server.adapter(redisAdapter);
  }

  handleConnection(client: Socket) {
    console.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
  }

  emit(event: string, data: any): void {
    this.server.emit(event, data);
  }

  emitToRoom(room: string, event: string, data: any): void {
    this.server.to(room).emit(event, data);
  }

  broadcast(event: string, data: any): void {
    this.server.emit(event, data);
  }

  join(socketId: string, room: string): void {
    const socket = this.server.sockets.sockets.get(socketId);
    if (socket) {
      socket.join(room);
    }
  }

  leave(socketId: string, room: string): void {
    const socket = this.server.sockets.sockets.get(socketId);
    if (socket) {
      socket.leave(room);
    }
  }

  @SubscribeMessage('join-room')
  handleJoinRoom(client: Socket, room: string) {
    client.join(room);
    client.emit('joined-room', room);
  }

  @SubscribeMessage('leave-room')
  handleLeaveRoom(client: Socket, room: string) {
    client.leave(room);
    client.emit('left-room', room);
  }
}
```

### Audit/Logging Module

#### Audit Interceptor (Observer Pattern)
```typescript
@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
  constructor(private readonly auditLogService: AuditLogService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { user, ip, headers, method, url, body } = request;

    // Skip logging cho endpoints không quan trọng
    if (this.shouldSkipLogging(url)) {
      return next.handle();
    }

    const startTime = Date.now();

    return next.handle().pipe(
      tap(async (response) => {
        try {
          const endTime = Date.now();
          const duration = endTime - startTime;

          await this.auditLogService.createAuditLog({
            action: this.mapMethodToAction(method),
            entityName: this.extractEntityName(url),
            entityId: this.extractEntityId(url),
            userId: user?.id,
            userEmail: user?.email,
            ipAddress: ip,
            userAgent: headers['user-agent'],
            endpoint: url,
            method,
            metadata: {
              duration,
              requestBody: this.sanitizeBody(body),
              responseSize: JSON.stringify(response).length,
            },
          });
        } catch (error) {
          // Log error nhưng không fail request
          console.error('Audit logging failed:', error);
        }
      }),
      catchError((error) => {
        // Log error operations
        this.auditLogService.createAuditLog({
          action: AuditAction.READ,
          entityName: this.extractEntityName(url),
          userId: user?.id,
          userEmail: user?.email,
          ipAddress: ip,
          userAgent: headers['user-agent'],
          endpoint: url,
          method,
          metadata: {
            error: error.message,
            stack: error.stack,
          },
        }).catch(() => {}); // Ignore audit logging errors

        return throwError(error);
      })
    );
  }

  private shouldSkipLogging(url: string): boolean {
    const skipPatterns = ['/health', '/metrics', '/favicon.ico'];
    return skipPatterns.some(pattern => url.includes(pattern));
  }

  private sanitizeBody(body: any): any {
    if (!body) return null;

    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key'];

    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}
```

---

## 🚨 10) Exception & Filter

### Custom Exceptions

```typescript
// Base Exception
export abstract class BaseException extends HttpException {
  constructor(message: string, statusCode: HttpStatus) {
    super(message, statusCode);
  }

  abstract getErrorCode(): string;
}

// Domain-specific Exceptions
export class DuplicatedEntryException extends BaseException {
  constructor(field?: string) {
    super(
      field ? `Duplicated entry for field: ${field}` : 'Duplicated entry detected',
      HttpStatus.BAD_REQUEST
    );
  }

  getErrorCode(): string {
    return 'DUPLICATED_ENTRY';
  }
}

export class NotNullEntryException extends BaseException {
  constructor(field?: string) {
    super(
      field ? `Field ${field} cannot be null` : 'Required field cannot be null',
      HttpStatus.BAD_REQUEST
    );
  }

  getErrorCode(): string {
    return 'NOT_NULL_ENTRY';
  }
}

export class InvalidCredentialsException extends BaseException {
  constructor() {
    super('Invalid credentials provided', HttpStatus.UNAUTHORIZED);
  }

  getErrorCode(): string {
    return 'INVALID_CREDENTIALS';
  }
}
```

### Global Exception Filter

```typescript
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let errorCode = 'INTERNAL_ERROR';

    if (exception instanceof BaseException) {
      status = exception.getStatus();
      message = exception.message;
      errorCode = exception.getErrorCode();
    } else if (exception instanceof HttpException) {
      status = exception.getStatus();
      message = exception.message;
    } else if (exception instanceof Error) {
      message = exception.message;
    }

    const errorResponse = {
      statusCode: status,
      errorCode,
      message,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      correlationId: request.headers['x-correlation-id'] || uuid(),
    };

    // Log error (không log stack trace trong production)
    if (status >= 500) {
      this.logger.error(
        `${request.method} ${request.url}`,
        process.env.NODE_ENV === 'production' ? undefined : (exception as Error).stack,
        'AllExceptionsFilter'
      );
    } else {
      this.logger.warn(
        `${request.method} ${request.url} - ${message}`,
        'AllExceptionsFilter'
      );
    }

    response.status(status).json(errorResponse);
  }
}
```

### Database Error Mapping

```typescript
export class DatabaseErrorMapper {
  static mapError(error: any): BaseException {
    // PostgreSQL error codes
    switch (error.code) {
      case '23505': // Unique constraint violation
        return new DuplicatedEntryException(this.extractFieldFromError(error));

      case '23502': // Not null constraint violation
        return new NotNullEntryException(error.column);

      case '23503': // Foreign key constraint violation
        return new BadRequestException('Referenced record does not exist');

      case '42P01': // Undefined table
        return new InternalServerErrorException('Database schema error');

      default:
        return new InternalServerErrorException('Database operation failed');
    }
  }

  private static extractFieldFromError(error: any): string {
    const match = error.detail?.match(/Key \(([^)]+)\)=/);
    return match ? match[1] : 'unknown field';
  }
}

---

## 🛡️ 11) Security (OWASP/GDPR)

### OWASP Top 10 Implementation

#### A01: Broken Access Control
```typescript
// Role-based Access Control
@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.role?.name === role);
  }
}

// Resource-based Access Control
@Injectable()
export class ResourceGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const { user, params } = request;

    // Check if user owns the resource or has admin role
    if (user.role?.name === 'admin') {
      return true;
    }

    const resourceId = params.id;
    const resource = await this.getResource(resourceId);

    return resource.userId === user.id;
  }
}
```

#### A02: Cryptographic Failures
```typescript
// Encryption Service
@Injectable()
export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly key = crypto.scryptSync(process.env.ENCRYPTION_KEY, 'salt', 32);

  encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key);
    cipher.setAAD(Buffer.from('additional data'));

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  decrypt(encryptedText: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedText.split(':');

    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');

    const decipher = crypto.createDecipher(this.algorithm, this.key);
    decipher.setAAD(Buffer.from('additional data'));
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}
```

#### A03: Injection Prevention
```typescript
// Input Validation
export class ValidationPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata) {
    // SQL Injection prevention through ORM
    if (typeof value === 'string') {
      // Remove dangerous characters
      value = value.replace(/[<>'"]/g, '');

      // Prevent SQL keywords in user input
      const sqlKeywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'UNION'];
      const upperValue = value.toUpperCase();

      if (sqlKeywords.some(keyword => upperValue.includes(keyword))) {
        throw new BadRequestException('Invalid input detected');
      }
    }

    return value;
  }
}

// Query Builder với Parameter Binding
export class SafeQueryBuilder {
  static buildWhereClause(filters: QueryFilter[]): [string, any[]] {
    const conditions: string[] = [];
    const parameters: any[] = [];

    filters.forEach((filter, index) => {
      const paramName = `param${index}`;
      conditions.push(`${filter.field} ${this.mapOperator(filter.operator)} :${paramName}`);
      parameters.push({ [paramName]: filter.value });
    });

    return [conditions.join(' AND '), parameters];
  }

  private static mapOperator(operator: string): string {
    const operatorMap = {
      '$eq': '=',
      '$ne': '!=',
      '$gt': '>',
      '$lt': '<',
      '$gte': '>=',
      '$lte': '<=',
      '$in': 'IN',
      '$like': 'LIKE',
    };

    return operatorMap[operator] || '=';
  }
}
```

#### A04: Insecure Design Prevention
```typescript
// Rate Limiting
@Injectable()
export class RateLimitGuard implements CanActivate {
  private readonly requests = new Map<string, number[]>();

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const key = this.getKey(request);

    const now = Date.now();
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const maxRequests = 100;

    if (!this.requests.has(key)) {
      this.requests.set(key, []);
    }

    const userRequests = this.requests.get(key);

    // Remove old requests outside the window
    const validRequests = userRequests.filter(time => now - time < windowMs);

    if (validRequests.length >= maxRequests) {
      throw new TooManyRequestsException('Rate limit exceeded');
    }

    validRequests.push(now);
    this.requests.set(key, validRequests);

    return true;
  }

  private getKey(request: any): string {
    return `${request.ip}:${request.user?.id || 'anonymous'}`;
  }
}
```

### GDPR Compliance

#### Data Anonymization
```typescript
@Injectable()
export class DataAnonymizationService {
  anonymizeUser(user: User): Partial<User> {
    return {
      id: user.id,
      firstName: this.maskString(user.firstName),
      lastName: this.maskString(user.lastName),
      email: this.maskEmail(user.email),
      phone: this.maskPhone(user.phone),
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }

  private maskString(str: string): string {
    if (!str || str.length <= 2) return '***';
    return str.charAt(0) + '*'.repeat(str.length - 2) + str.charAt(str.length - 1);
  }

  private maskEmail(email: string): string {
    const [local, domain] = email.split('@');
    return `${this.maskString(local)}@${domain}`;
  }

  private maskPhone(phone: string): string {
    if (!phone) return '***';
    return phone.replace(/\d(?=\d{4})/g, '*');
  }
}
```

#### Data Retention Policy
```typescript
@Injectable()
export class DataRetentionService {
  @Cron('0 0 * * *') // Daily at midnight
  async cleanupExpiredData() {
    const retentionPeriods = {
      audit_logs: 365, // 1 year
      user_sessions: 30, // 30 days
      temporary_files: 7, // 7 days
    };

    for (const [table, days] of Object.entries(retentionPeriods)) {
      await this.deleteExpiredRecords(table, days);
    }
  }

  private async deleteExpiredRecords(table: string, days: number) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    await this.dataSource.query(
      `DELETE FROM ${table} WHERE created_at < $1`,
      [cutoffDate]
    );
  }
}
```

#### Consent Management
```typescript
@Entity('user_consents')
export class UserConsent extends DocEntity {
  @ManyToOne(() => User)
  user: User;

  @Column()
  consentType: string; // 'marketing', 'analytics', 'cookies'

  @Column()
  granted: boolean;

  @Column()
  grantedAt: Date;

  @Column({ nullable: true })
  revokedAt: Date;

  @Column()
  ipAddress: string;

  @Column()
  userAgent: string;
}

@Injectable()
export class ConsentService {
  async grantConsent(userId: string, consentType: string, request: any) {
    await this.consentRepository.save({
      userId,
      consentType,
      granted: true,
      grantedAt: new Date(),
      ipAddress: request.ip,
      userAgent: request.headers['user-agent'],
    });
  }

  async revokeConsent(userId: string, consentType: string) {
    await this.consentRepository.update(
      { userId, consentType, granted: true },
      { granted: false, revokedAt: new Date() }
    );
  }

  async hasConsent(userId: string, consentType: string): Promise<boolean> {
    const consent = await this.consentRepository.findOne({
      where: { userId, consentType, granted: true },
    });

    return !!consent;
  }
}

---

## 🔄 12) Portability Matrix

### Framework Adapters

#### HTTP Framework Adapters
```typescript
// NestJS Adapter
export class NestJSAdapter implements IHttpAdapter {
  createController(options: CrudOptions): any {
    @Controller(options.path)
    class DynamicController extends BaseCrudController<any> {
      constructor(service: ICrudService<any>) {
        super(service);
      }
    }

    return DynamicController;
  }

  applyMiddleware(middleware: any[]): void {
    // Apply NestJS-specific middleware
  }
}

// Express Adapter
export class ExpressAdapter implements IHttpAdapter {
  createController(options: CrudOptions): any {
    const router = express.Router();

    router.get('/', (req, res) => this.handleGetMany(req, res));
    router.get('/:id', (req, res) => this.handleGetOne(req, res));
    router.post('/', (req, res) => this.handleCreate(req, res));
    router.put('/:id', (req, res) => this.handleUpdate(req, res));
    router.delete('/:id', (req, res) => this.handleDelete(req, res));

    return router;
  }
}

// FastAPI Adapter (Python)
export class FastAPIAdapter implements IHttpAdapter {
  createController(options: CrudOptions): any {
    const router = APIRouter();

    router.get("/", this.handleGetMany);
    router.get("/{id}", this.handleGetOne);
    router.post("/", this.handleCreate);
    router.put("/{id}", this.handleUpdate);
    router.delete("/{id}", this.handleDelete);

    return router;
  }
}
```

#### ORM Adapters
```typescript
// TypeORM Adapter
export class TypeORMAdapter<T> implements IOrmAdapter<T> {
  constructor(private repository: Repository<T>) {}

  async find(criteria: FindCriteria<T>): Promise<T[]> {
    return this.repository.find(criteria);
  }

  async findOne(criteria: FindCriteria<T>): Promise<T | null> {
    return this.repository.findOne(criteria);
  }

  createQueryBuilder(alias: string): SelectQueryBuilder<T> {
    return this.repository.createQueryBuilder(alias);
  }
}

// Prisma Adapter
export class PrismaAdapter<T> implements IOrmAdapter<T> {
  constructor(private prisma: PrismaClient, private model: string) {}

  async find(criteria: FindCriteria<T>): Promise<T[]> {
    return this.prisma[this.model].findMany(criteria);
  }

  async findOne(criteria: FindCriteria<T>): Promise<T | null> {
    return this.prisma[this.model].findUnique(criteria);
  }

  createQueryBuilder(alias: string): any {
    // Prisma doesn't have query builder, implement custom logic
    return new PrismaQueryBuilder(this.prisma, this.model);
  }
}

// Sequelize Adapter
export class SequelizeAdapter<T> implements IOrmAdapter<T> {
  constructor(private model: ModelStatic<Model>) {}

  async find(criteria: FindCriteria<T>): Promise<T[]> {
    return this.model.findAll(criteria);
  }

  async findOne(criteria: FindCriteria<T>): Promise<T | null> {
    return this.model.findOne(criteria);
  }

  createQueryBuilder(alias: string): any {
    return new SequelizeQueryBuilder(this.model);
  }
}
```

### Language Ports

#### PHP/Laravel Port
```php
<?php

// Laravel CRUD Controller
abstract class BaseCrudController extends Controller
{
    protected $service;

    public function __construct(ICrudService $service)
    {
        $this->service = $service;
    }

    public function index(Request $request)
    {
        $parsed = RequestQueryParser::parse($request->query());
        return $this->service->getMany($parsed);
    }

    public function show(Request $request, $id)
    {
        $parsed = RequestQueryParser::parse($request->query());
        $parsed->setId($id);
        return $this->service->getOne($parsed);
    }

    public function store(Request $request)
    {
        $parsed = RequestQueryParser::parse($request->query());
        return $this->service->createOne($parsed, $request->all());
    }

    public function update(Request $request, $id)
    {
        $parsed = RequestQueryParser::parse($request->query());
        $parsed->setId($id);
        return $this->service->updateOne($parsed, $request->all());
    }

    public function destroy(Request $request, $id)
    {
        $parsed = RequestQueryParser::parse($request->query());
        $parsed->setId($id);
        return $this->service->deleteOne($parsed);
    }
}

// Eloquent Adapter
class EloquentAdapter implements IOrmAdapter
{
    protected $model;

    public function __construct($model)
    {
        $this->model = $model;
    }

    public function find(array $criteria): Collection
    {
        $query = $this->model::query();

        foreach ($criteria['where'] ?? [] as $condition) {
            $query->where($condition['field'], $condition['operator'], $condition['value']);
        }

        if (isset($criteria['orderBy'])) {
            foreach ($criteria['orderBy'] as $order) {
                $query->orderBy($order['field'], $order['direction']);
            }
        }

        return $query->get();
    }

    public function findOne(array $criteria): ?Model
    {
        return $this->find($criteria)->first();
    }
}
```

#### Python/FastAPI Port
```python
# FastAPI CRUD Controller
from abc import ABC, abstractmethod
from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, Query, Path, Body
from pydantic import BaseModel

class ICrudService(ABC):
    @abstractmethod
    async def get_many(self, parsed_request: ParsedRequestParams) -> List[Any]:
        pass

    @abstractmethod
    async def get_one(self, parsed_request: ParsedRequestParams) -> Optional[Any]:
        pass

    @abstractmethod
    async def create_one(self, parsed_request: ParsedRequestParams, dto: Dict[str, Any]) -> Any:
        pass

class BaseCrudController:
    def __init__(self, service: ICrudService):
        self.service = service
        self.router = APIRouter()
        self._setup_routes()

    def _setup_routes(self):
        self.router.get("/")(self.get_many)
        self.router.get("/{id}")(self.get_one)
        self.router.post("/")(self.create_one)
        self.router.put("/{id}")(self.update_one)
        self.router.delete("/{id}")(self.delete_one)

    async def get_many(
        self,
        filter: Optional[str] = Query(None),
        sort: Optional[str] = Query(None),
        limit: Optional[int] = Query(10),
        offset: Optional[int] = Query(0)
    ):
        parsed = RequestQueryParser.parse({
            'filter': filter,
            'sort': sort,
            'limit': limit,
            'offset': offset
        })
        return await self.service.get_many(parsed)

    async def get_one(self, id: str = Path(...)):
        parsed = RequestQueryParser.parse({'id': id})
        return await self.service.get_one(parsed)

    async def create_one(self, dto: Dict[str, Any] = Body(...)):
        parsed = RequestQueryParser.parse({})
        return await self.service.create_one(parsed, dto)

# SQLAlchemy Adapter
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

class SQLAlchemyAdapter:
    def __init__(self, session: Session, model):
        self.session = session
        self.model = model

    async def find(self, criteria: Dict[str, Any]) -> List[Any]:
        query = self.session.query(self.model)

        if 'where' in criteria:
            conditions = []
            for condition in criteria['where']:
                field = getattr(self.model, condition['field'])
                operator = condition['operator']
                value = condition['value']

                if operator == '$eq':
                    conditions.append(field == value)
                elif operator == '$ne':
                    conditions.append(field != value)
                elif operator == '$gt':
                    conditions.append(field > value)
                elif operator == '$lt':
                    conditions.append(field < value)
                elif operator == '$in':
                    conditions.append(field.in_(value))
                elif operator == '$like':
                    conditions.append(field.like(f'%{value}%'))

            if conditions:
                query = query.filter(and_(*conditions))

        if 'order_by' in criteria:
            for order in criteria['order_by']:
                field = getattr(self.model, order['field'])
                if order['direction'].upper() == 'DESC':
                    query = query.order_by(field.desc())
                else:
                    query = query.order_by(field.asc())

        if 'limit' in criteria:
            query = query.limit(criteria['limit'])

        if 'offset' in criteria:
            query = query.offset(criteria['offset'])

        return query.all()

    async def find_one(self, criteria: Dict[str, Any]) -> Optional[Any]:
        results = await self.find(criteria)
        return results[0] if results else None

---

## 🧪 13) Testing Strategy

### Unit Testing

#### Service Testing
```typescript
describe('UserService', () => {
  let service: UserService;
  let repository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            remove: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  describe('getMany', () => {
    it('should return paginated users', async () => {
      const mockUsers = [
        { id: '1', email: '<EMAIL>' },
        { id: '2', email: '<EMAIL>' },
      ];

      jest.spyOn(repository, 'find').mockResolvedValue(mockUsers);

      const parsed = {
        limit: 10,
        offset: 0,
        filter: [],
        sort: [],
      };

      const result = await service.getMany(parsed);

      expect(repository.find).toHaveBeenCalledWith({
        take: 10,
        skip: 0,
        where: {},
        order: {},
      });
      expect(result.data).toEqual(mockUsers);
    });

    it('should apply filters correctly', async () => {
      const parsed = {
        filter: [
          { field: 'email', operator: '$cont', value: 'test' },
          { field: 'isActive', operator: '$eq', value: true },
        ],
      };

      await service.getMany(parsed);

      expect(repository.find).toHaveBeenCalledWith({
        where: {
          email: Like('%test%'),
          isActive: true,
        },
      });
    });
  });

  describe('createOne', () => {
    it('should create user with hashed password', async () => {
      const dto = {
        email: '<EMAIL>',
        password: 'plaintext',
        firstName: 'Test',
        lastName: 'User',
      };

      const savedUser = { ...dto, id: '1', password: 'hashed_password' };
      jest.spyOn(repository, 'save').mockResolvedValue(savedUser);
      jest.spyOn(bcrypt, 'hash').mockResolvedValue('hashed_password');

      const result = await service.createOne({}, dto);

      expect(bcrypt.hash).toHaveBeenCalledWith('plaintext', 12);
      expect(repository.save).toHaveBeenCalledWith({
        ...dto,
        password: 'hashed_password',
      });
      expect(result.password).toBeUndefined(); // Password should be excluded
    });

    it('should throw DuplicatedEntryException for duplicate email', async () => {
      const dto = { email: '<EMAIL>' };

      const dbError = {
        code: '23505',
        detail: 'Key (email)=(<EMAIL>) already exists.',
      };

      jest.spyOn(repository, 'save').mockRejectedValue(dbError);

      await expect(service.createOne({}, dto)).rejects.toThrow(
        DuplicatedEntryException
      );
    });
  });
});
```

#### Controller Testing
```typescript
describe('UserController', () => {
  let controller: UserController;
  let service: UserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: {
            getMany: jest.fn(),
            getOne: jest.fn(),
            createOne: jest.fn(),
            updateOne: jest.fn(),
            deleteOne: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    service = module.get<UserService>(UserService);
  });

  describe('GET /users', () => {
    it('should return users with pagination', async () => {
      const mockResult = {
        data: [{ id: '1', email: '<EMAIL>' }],
        count: 1,
        total: 1,
        page: 1,
        pageCount: 1,
      };

      jest.spyOn(service, 'getMany').mockResolvedValue(mockResult);

      const req = {
        query: { limit: '10', offset: '0' },
        parsed: { limit: 10, offset: 0 },
      };

      const result = await controller.getMany(req);

      expect(service.getMany).toHaveBeenCalledWith(req.parsed);
      expect(result).toEqual(mockResult);
    });
  });

  describe('POST /users', () => {
    it('should create user and return without password', async () => {
      const dto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const createdUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      };

      jest.spyOn(service, 'createOne').mockResolvedValue(createdUser);

      const result = await controller.createOne({}, dto);

      expect(service.createOne).toHaveBeenCalledWith({}, dto);
      expect(result).toEqual(createdUser);
      expect(result.password).toBeUndefined();
    });
  });
});
```

### Integration Testing

#### API Integration Tests
```typescript
describe('User API (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DataSource)
      .useValue(testDataSource)
      .compile();

    app = moduleFixture.createNestApplication();
    dataSource = moduleFixture.get<DataSource>(DataSource);

    await app.init();
  });

  beforeEach(async () => {
    // Clean database before each test
    await dataSource.synchronize(true);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/users (GET)', () => {
    it('should return empty array when no users exist', () => {
      return request(app.getHttpServer())
        .get('/users')
        .expect(200)
        .expect({
          data: [],
          count: 0,
          total: 0,
          page: 1,
          pageCount: 0,
        });
    });

    it('should return users with pagination', async () => {
      // Seed test data
      await dataSource.getRepository(User).save([
        { email: '<EMAIL>', firstName: 'User', lastName: 'One' },
        { email: '<EMAIL>', firstName: 'User', lastName: 'Two' },
      ]);

      return request(app.getHttpServer())
        .get('/users?limit=1&page=1')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.total).toBe(2);
          expect(res.body.pageCount).toBe(2);
        });
    });

    it('should filter users by email', async () => {
      await dataSource.getRepository(User).save([
        { email: '<EMAIL>', firstName: 'John', lastName: 'Doe' },
        { email: '<EMAIL>', firstName: 'Jane', lastName: 'Doe' },
      ]);

      return request(app.getHttpServer())
        .get('/users?filter=email||$cont||john')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.data[0].email).toBe('<EMAIL>');
        });
    });
  });

  describe('/users (POST)', () => {
    it('should create user successfully', () => {
      const createUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      return request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.email).toBe(createUserDto.email);
          expect(res.body.firstName).toBe(createUserDto.firstName);
          expect(res.body.password).toBeUndefined();
          expect(res.body.id).toBeDefined();
        });
    });

    it('should return 400 for duplicate email', async () => {
      const user = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      // Create first user
      await request(app.getHttpServer())
        .post('/users')
        .send(user)
        .expect(201);

      // Try to create duplicate
      return request(app.getHttpServer())
        .post('/users')
        .send(user)
        .expect(400)
        .expect((res) => {
          expect(res.body.errorCode).toBe('DUPLICATED_ENTRY');
        });
    });
  });

  describe('Authentication', () => {
    it('should authenticate user and return tokens', async () => {
      // Create user first
      const user = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Auth',
        lastName: 'User',
      };

      await request(app.getHttpServer())
        .post('/users')
        .send(user)
        .expect(201);

      // Authenticate
      return request(app.getHttpServer())
        .post('/auth/signin')
        .send({
          username: user.email,
          password: user.password,
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.accessToken).toBeDefined();
          expect(res.body.refreshToken).toBeDefined();
          expect(res.body.user.email).toBe(user.email);
        });
    });

    it('should protect routes with JWT guard', async () => {
      return request(app.getHttpServer())
        .get('/auth/me')
        .expect(401);
    });

    it('should allow access with valid JWT token', async () => {
      // Create and authenticate user
      const user = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Protected',
        lastName: 'User',
      };

      await request(app.getHttpServer())
        .post('/users')
        .send(user);

      const authResponse = await request(app.getHttpServer())
        .post('/auth/signin')
        .send({
          username: user.email,
          password: user.password,
        });

      const { accessToken } = authResponse.body;

      return request(app.getHttpServer())
        .get('/auth/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.email).toBe(user.email);
        });
    });
  });
});
```

### Performance Testing

#### Load Testing với Artillery
```yaml
# artillery-config.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 120
      arrivalRate: 50
      name: "Load test"
    - duration: 60
      arrivalRate: 100
      name: "Stress test"
  payload:
    path: "users.csv"
    fields:
      - "email"
      - "firstName"
      - "lastName"

scenarios:
  - name: "CRUD Operations"
    weight: 70
    flow:
      - post:
          url: "/auth/signin"
          json:
            username: "<EMAIL>"
            password: "password123"
          capture:
            - json: "$.accessToken"
              as: "token"
      - get:
          url: "/users?limit=20"
          headers:
            Authorization: "Bearer {{ token }}"
      - post:
          url: "/users"
          headers:
            Authorization: "Bearer {{ token }}"
          json:
            email: "{{ email }}"
            firstName: "{{ firstName }}"
            lastName: "{{ lastName }}"
            password: "password123"
          capture:
            - json: "$.id"
              as: "userId"
      - get:
          url: "/users/{{ userId }}"
          headers:
            Authorization: "Bearer {{ token }}"
      - put:
          url: "/users/{{ userId }}"
          headers:
            Authorization: "Bearer {{ token }}"
          json:
            firstName: "Updated {{ firstName }}"

  - name: "Search Operations"
    weight: 30
    flow:
      - get:
          url: "/users?filter=firstName||$cont||John&sort=createdAt,DESC&limit=10"
      - get:
          url: "/users?filter=email||$ends||@gmail.com&page=1&limit=20"

---

## 📊 14) Performance Budget

### Response Time Targets
- **GET /resource**: < 100ms (p95)
- **GET /resource/:id**: < 50ms (p95)
- **POST /resource**: < 200ms (p95)
- **PUT /resource/:id**: < 150ms (p95)
- **DELETE /resource/:id**: < 100ms (p95)

### Throughput Targets
- **Concurrent Users**: 1000+
- **Requests/Second**: 500+ (mixed workload)
- **Database Connections**: < 50 active
- **Memory Usage**: < 512MB per instance
- **CPU Usage**: < 70% under normal load

### Caching Strategy
```typescript
// Performance Monitoring Interceptor
@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PerformanceInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // Log slow requests
        if (duration > 1000) {
          this.logger.warn(
            `Slow request: ${request.method} ${request.url} took ${duration}ms`
          );
        }

        // Metrics collection
        this.collectMetrics(request, duration);
      })
    );
  }

  private collectMetrics(request: any, duration: number) {
    // Send to monitoring system (Prometheus, DataDog, etc.)
    const labels = {
      method: request.method,
      route: request.route?.path || request.url,
      status_code: request.res?.statusCode || 200,
    };

    // Example: Prometheus metrics
    httpRequestDuration.observe(labels, duration / 1000);
    httpRequestsTotal.inc(labels);
  }
}
```

### Database Optimization
```typescript
// Query Performance Analyzer
@Injectable()
export class QueryPerformanceService {
  @Cron('0 */6 * * *') // Every 6 hours
  async analyzeSlowQueries() {
    const slowQueries = await this.dataSource.query(`
      SELECT
        query,
        mean_exec_time,
        calls,
        total_exec_time,
        rows,
        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
      FROM pg_stat_statements
      WHERE mean_exec_time > 100
      ORDER BY mean_exec_time DESC
      LIMIT 10
    `);

    if (slowQueries.length > 0) {
      this.logger.warn('Slow queries detected:', slowQueries);
      // Alert or auto-optimize
    }
  }

  async optimizeQuery(query: string): Promise<string> {
    // Analyze query plan
    const plan = await this.dataSource.query(`EXPLAIN ANALYZE ${query}`);

    // Suggest optimizations
    const suggestions = this.analyzeQueryPlan(plan);

    return suggestions;
  }

  private analyzeQueryPlan(plan: any[]): string {
    const suggestions = [];

    plan.forEach(step => {
      if (step['QUERY PLAN'].includes('Seq Scan')) {
        suggestions.push('Consider adding index for sequential scan');
      }

      if (step['QUERY PLAN'].includes('Hash Join') &&
          step['QUERY PLAN'].includes('cost=')) {
        const cost = parseFloat(step['QUERY PLAN'].match(/cost=(\d+\.\d+)/)?.[1] || '0');
        if (cost > 1000) {
          suggestions.push('High-cost hash join detected, consider query optimization');
        }
      }
    });

    return suggestions.join('; ');
  }
}
```

---

## ✅ 15) Acceptance Criteria

### Functional Requirements Checklist

#### CRUD Operations ✅
- [ ] **GET /resource** - List with pagination, filtering, sorting
- [ ] **GET /resource/:id** - Get single resource
- [ ] **POST /resource** - Create new resource
- [ ] **PUT /resource/:id** - Update existing resource
- [ ] **DELETE /resource/:id** - Delete resource (soft delete if configured)
- [ ] **POST /resource/:id/recover** - Recover soft-deleted resource

#### Query Language ✅
- [ ] **Filter operators**: $eq, $ne, $gt, $lt, $gte, $lte, $in, $notin, $cont, $starts, $ends
- [ ] **Logical operators**: AND (default), OR
- [ ] **Sorting**: Single and multiple field sorting
- [ ] **Pagination**: limit, offset, page-based
- [ ] **Field selection**: Include/exclude specific fields
- [ ] **Joins**: Related entity inclusion
- [ ] **Caching**: Query-level caching with TTL

#### Authentication & Authorization ✅
- [ ] **JWT Authentication**: Access and refresh tokens
- [ ] **Role-based Access Control**: Admin, User, Guest roles
- [ ] **Resource-based Permissions**: Owner-only access
- [ ] **Rate Limiting**: Per-user and per-IP limits
- [ ] **Password Security**: bcrypt hashing, complexity requirements
- [ ] **Session Management**: Token rotation, revocation

#### Security ✅
- [ ] **Input Validation**: All inputs validated and sanitized
- [ ] **SQL Injection Prevention**: Parameterized queries only
- [ ] **XSS Prevention**: Output encoding
- [ ] **CSRF Protection**: Token-based protection
- [ ] **HTTPS Enforcement**: All communications encrypted
- [ ] **Security Headers**: HSTS, CSP, X-Frame-Options
- [ ] **Audit Logging**: All actions logged with user context

#### Performance ✅
- [ ] **Response Times**: Meet defined SLA targets
- [ ] **Caching**: Multi-level caching strategy
- [ ] **Database Optimization**: Proper indexing, query optimization
- [ ] **Connection Pooling**: Efficient database connections
- [ ] **Memory Management**: No memory leaks
- [ ] **Monitoring**: Performance metrics collection

### Non-Functional Requirements Checklist

#### Scalability ✅
- [ ] **Horizontal Scaling**: Stateless application design
- [ ] **Load Balancing**: Multiple instance support
- [ ] **Database Scaling**: Read replicas, connection pooling
- [ ] **Caching**: Redis cluster support
- [ ] **File Storage**: Cloud storage integration

#### Reliability ✅
- [ ] **Error Handling**: Graceful error responses
- [ ] **Circuit Breaker**: External service failure handling
- [ ] **Retry Logic**: Transient failure recovery
- [ ] **Health Checks**: Application and dependency health
- [ ] **Graceful Shutdown**: Clean resource cleanup

#### Maintainability ✅
- [ ] **Code Quality**: SOLID principles, clean code
- [ ] **Documentation**: Comprehensive API and code documentation
- [ ] **Testing**: Unit, integration, and e2e tests
- [ ] **Logging**: Structured logging with correlation IDs
- [ ] **Monitoring**: Application and business metrics

#### Portability ✅
- [ ] **Framework Agnostic**: Adapter pattern implementation
- [ ] **Database Agnostic**: ORM abstraction layer
- [ ] **Cloud Agnostic**: No vendor lock-in
- [ ] **Configuration**: Environment-based configuration
- [ ] **Containerization**: Docker support

---

## 🎯 16) Implementation Milestones

### M1: Core Foundation (Weeks 1-2)
**Mục tiêu**: Xây dựng nền tảng cơ bản với SOLID principles

#### Deliverables:
- [ ] **Base Architecture**: Layered architecture setup
- [ ] **Core Interfaces**: ICrudService, IRepository, IOrmAdapter
- [ ] **Base Classes**: BaseCrudService, BaseCrudController
- [ ] **Route Factory**: Metadata-driven route generation
- [ ] **Query Parser**: HTTP query language implementation
- [ ] **Basic CRUD**: GET, POST, PUT, DELETE operations

#### Acceptance Criteria:
```typescript
// Example test for M1 completion
describe('M1: Core Foundation', () => {
  it('should generate CRUD routes from metadata', () => {
    @Crud({
      model: { type: User },
      query: { allow: ['name', 'email'] }
    })
    class UserController extends BaseCrudController<User> {}

    const routes = CrudRoutesFactory.getRoutes(UserController);

    expect(routes).toHaveLength(5); // GET, GET/:id, POST, PUT/:id, DELETE/:id
    expect(routes.map(r => r.method)).toEqual(['GET', 'GET', 'POST', 'PUT', 'DELETE']);
  });

  it('should parse HTTP query language correctly', () => {
    const query = 'filter=name||$cont||john&sort=createdAt,DESC&limit=10';
    const parsed = RequestQueryParser.parse(query);

    expect(parsed.filter).toEqual([
      { field: 'name', operator: '$cont', value: 'john' }
    ]);
    expect(parsed.sort).toEqual([
      { field: 'createdAt', order: 'DESC' }
    ]);
    expect(parsed.limit).toBe(10);
  });
});
```

### M2: Security & Auth (Weeks 3-4)
**Mục tiêu**: Implement comprehensive security layer

#### Deliverables:
- [ ] **JWT Authentication**: Access/refresh token system
- [ ] **Authorization**: Role and resource-based access control
- [ ] **Password Security**: bcrypt hashing, complexity validation
- [ ] **Rate Limiting**: Request throttling
- [ ] **Input Validation**: Comprehensive sanitization
- [ ] **Audit Logging**: Security event tracking

#### Security Checklist:
```typescript
// Security validation tests
describe('M2: Security Implementation', () => {
  it('should prevent SQL injection', async () => {
    const maliciousInput = "'; DROP TABLE users; --";

    await expect(
      userService.getMany({
        filter: [{ field: 'name', operator: '$eq', value: maliciousInput }]
      })
    ).not.toThrow();

    // Verify table still exists
    const users = await userService.getMany({});
    expect(users).toBeDefined();
  });

  it('should enforce rate limiting', async () => {
    const requests = Array(101).fill(null).map(() =>
      request(app).get('/users')
    );

    const responses = await Promise.all(requests);
    const rateLimited = responses.filter(r => r.status === 429);

    expect(rateLimited.length).toBeGreaterThan(0);
  });

  it('should hash passwords securely', async () => {
    const user = await userService.createOne({}, {
      email: '<EMAIL>',
      password: 'plaintext123'
    });

    expect(user.password).not.toBe('plaintext123');
    expect(await bcrypt.compare('plaintext123', user.password)).toBe(true);
  });
});
```

### M3: Cross-cutting Concerns (Weeks 5-6)
**Mục tiêu**: Implement enterprise features

#### Deliverables:
- [ ] **Caching**: Redis integration with TTL
- [ ] **Mailer**: Template-based email system
- [ ] **File Storage**: Cloudinary/S3 integration
- [ ] **Socket**: Real-time communication
- [ ] **Exception Handling**: Global error handling
- [ ] **Monitoring**: Performance metrics

### M4: Multi-Platform Ports (Weeks 7-8)
**Mục tiêu**: Demonstrate portability

#### Port 1: FastAPI + SQLAlchemy
- [ ] **Python Implementation**: Core CRUD operations
- [ ] **SQLAlchemy Adapter**: Database abstraction
- [ ] **FastAPI Routes**: HTTP endpoint generation
- [ ] **Pydantic Models**: Request/response validation

#### Port 2: Laravel + Eloquent
- [ ] **PHP Implementation**: Core CRUD operations
- [ ] **Eloquent Adapter**: Database abstraction
- [ ] **Laravel Routes**: HTTP endpoint generation
- [ ] **Form Requests**: Input validation

### M5: Production Hardening (Weeks 9-10)
**Mục tiêu**: Production-ready deployment

#### Deliverables:
- [ ] **Performance Optimization**: Query optimization, caching
- [ ] **Observability**: Logging, metrics, tracing
- [ ] **Security Hardening**: SAST/DAST, penetration testing
- [ ] **Documentation**: ADRs, runbooks, API docs
- [ ] **CI/CD**: Automated testing and deployment
- [ ] **Load Testing**: Performance validation

---

## 📚 17) Learning Roadmap

### Ôn lại theo "bảng chuẩn"

#### Core Concepts Mastery
1. **CrudOptions**: Hiểu sâu về metadata configuration
2. **HTTP Query Language**: Thành thạo tất cả operators và use cases
3. **ICrudService**: Nắm vững interface contracts và implementations
4. **Adapters**: Hiểu pattern và cách implement cho các technologies
5. **Security Checklist**: Áp dụng OWASP và GDPR compliance

#### Quarterly Learning Plan

##### Q1: Foundation Mastery
- **Week 1-2**: SOLID principles deep dive
- **Week 3-4**: Design patterns implementation
- **Week 5-6**: HTTP Query Language mastery
- **Week 7-8**: Route Factory pattern
- **Week 9-10**: Security fundamentals
- **Week 11-12**: Testing strategies

##### Q2: Advanced Patterns
- **Week 1-2**: Event-driven architecture
- **Week 3-4**: CQRS and Event Sourcing
- **Week 5-6**: Microservices patterns
- **Week 7-8**: Performance optimization
- **Week 9-10**: Observability and monitoring
- **Week 11-12**: Cloud-native patterns

##### Q3: Multi-Platform Expertise
- **Week 1-2**: Port 1 resource sang FastAPI/Python
- **Week 3-4**: Port 1 resource sang Laravel/PHP
- **Week 5-6**: Port 1 resource sang Spring Boot/Java
- **Week 7-8**: Port 1 resource sang .NET Core/C#
- **Week 9-10**: Adapter pattern refinement
- **Week 11-12**: Cross-platform testing

##### Q4: Production Excellence
- **Week 1-2**: Security guide update và dependency audit
- **Week 3-4**: Performance benchmarking và optimization
- **Week 5-6**: Viết thêm 1 adapter (ví dụ: Kafka event-outbox)
- **Week 7-8**: Documentation và knowledge sharing
- **Week 9-10**: Mentoring và code review
- **Week 11-12**: Architecture decision records (ADRs)

### Continuous Learning Activities

#### Monthly Reviews
- [ ] **Code Quality**: Review SOLID principles adherence
- [ ] **Security**: Update security checklist và vulnerability scan
- [ ] **Performance**: Benchmark và optimize critical paths
- [ ] **Documentation**: Update API docs và architectural decisions

#### Quarterly Assessments
- [ ] **Port Implementation**: Successfully port 1 resource to new stack
- [ ] **Security Audit**: Complete security review và penetration testing
- [ ] **Performance Review**: Meet all performance budget targets
- [ ] **Knowledge Sharing**: Present learnings to team

---

## 🏆 18) Success Metrics

### Technical Metrics

#### Code Quality
- **SOLID Compliance**: 100% of classes follow SOLID principles
- **Test Coverage**: >90% unit test coverage, >80% integration coverage
- **Code Duplication**: <5% duplicate code
- **Cyclomatic Complexity**: <10 per method
- **Technical Debt**: <8 hours per sprint

#### Performance Metrics
- **API Response Time**: p95 < 200ms for all endpoints
- **Database Query Time**: p95 < 50ms for simple queries
- **Memory Usage**: <512MB per instance under normal load
- **CPU Usage**: <70% under normal load
- **Error Rate**: <0.1% for all operations

#### Security Metrics
- **Vulnerability Count**: 0 high/critical vulnerabilities
- **Security Test Coverage**: 100% of security controls tested
- **Audit Compliance**: 100% of actions logged
- **Authentication Success Rate**: >99.9%
- **Authorization Accuracy**: 100% correct access decisions

### Business Metrics

#### Developer Productivity
- **Time to Implement CRUD**: <2 hours for standard resource
- **Time to Add New Adapter**: <4 hours for new technology
- **Onboarding Time**: <1 day for new developer
- **Bug Resolution Time**: <24 hours for critical bugs
- **Feature Delivery Time**: 50% reduction vs traditional approach

#### System Reliability
- **Uptime**: >99.9% availability
- **MTTR**: <30 minutes for critical issues
- **MTBF**: >720 hours between failures
- **Data Consistency**: 100% ACID compliance
- **Backup Success Rate**: 100% successful backups

#### Portability Success
- **Framework Port Time**: <1 week for new framework
- **Language Port Time**: <2 weeks for new language
- **Adapter Implementation**: <1 day for new technology
- **Configuration Effort**: <2 hours for new environment
- **Knowledge Transfer**: <4 hours for new team member

---

## 🎓 19) Governance & Standards

### Code Review Standards

#### Mandatory Checks
- [ ] **SOLID Principles**: Every class reviewed for SRP, OCP, LSP, ISP, DIP
- [ ] **Security**: Input validation, output encoding, authentication checks
- [ ] **Performance**: Query optimization, caching strategy, memory usage
- [ ] **Testing**: Unit tests for business logic, integration tests for APIs
- [ ] **Documentation**: Code comments, API documentation, ADRs

#### Review Checklist
```typescript
// Code Review Template
interface CodeReviewChecklist {
  // SOLID Principles
  singleResponsibility: boolean;    // Does class have one reason to change?
  openClosed: boolean;             // Open for extension, closed for modification?
  liskovSubstitution: boolean;     // Subtypes substitutable for base types?
  interfaceSegregation: boolean;   // No forced dependencies on unused interfaces?
  dependencyInversion: boolean;    // Depends on abstractions, not concretions?

  // Security
  inputValidation: boolean;        // All inputs validated?
  outputEncoding: boolean;         // All outputs properly encoded?
  authenticationCheck: boolean;    // Authentication properly implemented?
  authorizationCheck: boolean;     // Authorization properly enforced?
  auditLogging: boolean;          // Security events logged?

  // Performance
  queryOptimization: boolean;      // Database queries optimized?
  cachingStrategy: boolean;        // Appropriate caching implemented?
  memoryUsage: boolean;           // Memory usage optimized?
  algorithmComplexity: boolean;    // Algorithm complexity acceptable?

  // Testing
  unitTests: boolean;             // Business logic unit tested?
  integrationTests: boolean;      // API endpoints integration tested?
  edgeCases: boolean;            // Edge cases covered?
  errorHandling: boolean;        // Error scenarios tested?

  // Documentation
  codeComments: boolean;         // Complex logic commented?
  apiDocumentation: boolean;     // API endpoints documented?
  architecturalDecisions: boolean; // ADRs updated if needed?
}
```

### Architecture Decision Records (ADRs)

#### ADR Template
```markdown
# ADR-001: HTTP Query Language Design

## Status
Accepted

## Context
We need a standardized way to handle filtering, sorting, pagination, and field selection across all CRUD endpoints. The solution should be:
- Framework agnostic
- Type safe
- Extensible
- Secure (prevent injection attacks)

## Decision
Implement HTTP Query Language with the following syntax:
- Filters: `filter=field||operator||value`
- Sorting: `sort=field,direction`
- Pagination: `limit=N&offset=N` or `page=N&limit=N`
- Field selection: `fields=field1,field2`
- Joins: `join=relation`

## Consequences
### Positive
- Consistent API across all resources
- Powerful querying capabilities
- Framework portability
- Type safety through parser validation

### Negative
- Learning curve for developers
- Additional parsing overhead
- Complex query validation logic

## Implementation
- RequestQueryParser handles parsing and validation
- Whitelist approach for security
- Parameter binding prevents SQL injection
- Caching support through query fingerprinting
```

### Quality Gates

#### Definition of Done
- [ ] **Functionality**: All acceptance criteria met
- [ ] **Code Quality**: SOLID principles followed, code reviewed
- [ ] **Testing**: Unit tests >90%, integration tests >80%, e2e tests pass
- [ ] **Security**: Security checklist completed, no vulnerabilities
- [ ] **Performance**: Performance budget met, load tested
- [ ] **Documentation**: Code documented, API docs updated, ADRs written
- [ ] **Deployment**: CI/CD pipeline passes, deployed to staging

#### Release Criteria
- [ ] **All Quality Gates**: 100% of quality gates passed
- [ ] **Security Audit**: External security review completed
- [ ] **Performance Validation**: Load testing results acceptable
- [ ] **Documentation Complete**: All documentation updated
- [ ] **Rollback Plan**: Rollback procedure tested and documented
- [ ] **Monitoring**: Alerts and dashboards configured
- [ ] **Support Readiness**: Support team trained and ready

---

## 🚀 20) Deployment & Operations

### Container Strategy

#### Multi-stage Dockerfile
```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

COPY . .
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

WORKDIR /app

# Copy built application
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package.json ./package.json

# Security: Run as non-root
USER nestjs

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

CMD ["node", "dist/main.js"]
```

#### Docker Compose for Development
```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      target: development
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
      - REDIS_HOST=redis
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    command: npm run start:dev

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: crud_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  postgres_data:
  redis_data:
```

### Kubernetes Deployment

#### Application Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: crud-api
  labels:
    app: crud-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: crud-api
  template:
    metadata:
      labels:
        app: crud-api
    spec:
      containers:
      - name: crud-api
        image: crud-api:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: host
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
---
apiVersion: v1
kind: Service
metadata:
  name: crud-api-service
spec:
  selector:
    app: crud-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: crud-api-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.example.com
    secretName: api-tls
  rules:
  - host: api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: crud-api-service
            port:
              number: 80
```

### CI/CD Pipeline

#### GitHub Actions Workflow
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run type checking
      run: npm run type-check

    - name: Run unit tests
      run: npm run test:unit
      env:
        NODE_ENV: test

    - name: Run integration tests
      run: npm run test:integration
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 5432
        DB_USER: postgres
        DB_PASSWORD: postgres
        DB_NAME: test_db
        REDIS_HOST: localhost
        REDIS_PORT: 6379

    - name: Run e2e tests
      run: npm run test:e2e
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 5432
        DB_USER: postgres
        DB_PASSWORD: postgres
        DB_NAME: test_db
        REDIS_HOST: localhost
        REDIS_PORT: 6379

    - name: Generate coverage report
      run: npm run test:coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Run security audit
      run: npm audit --audit-level high

    - name: Run SAST scan
      uses: github/super-linter@v4
      env:
        DEFAULT_BRANCH: main
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        VALIDATE_TYPESCRIPT_ES: true
        VALIDATE_DOCKERFILE: true
        VALIDATE_YAML: true

  build:
    needs: [test, security]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: |
          ghcr.io/${{ github.repository }}:latest
          ghcr.io/${{ github.repository }}:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to staging
      run: |
        # Update Kubernetes deployment
        kubectl set image deployment/crud-api crud-api=ghcr.io/${{ github.repository }}:${{ github.sha }}
        kubectl rollout status deployment/crud-api

    - name: Run smoke tests
      run: npm run test:smoke
      env:
        API_URL: https://staging-api.example.com

    - name: Deploy to production
      if: success()
      run: |
        # Production deployment logic
        kubectl set image deployment/crud-api-prod crud-api=ghcr.io/${{ github.repository }}:${{ github.sha }}
        kubectl rollout status deployment/crud-api-prod
```

### Monitoring & Observability

#### Prometheus Metrics
```typescript
// metrics.service.ts
import { Injectable } from '@nestjs/common';
import { register, Counter, Histogram, Gauge } from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly httpRequestsTotal = new Counter({
    name: 'http_requests_total',
    help: 'Total number of HTTP requests',
    labelNames: ['method', 'route', 'status_code'],
  });

  private readonly httpRequestDuration = new Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code'],
    buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
  });

  private readonly databaseConnectionsActive = new Gauge({
    name: 'database_connections_active',
    help: 'Number of active database connections',
  });

  private readonly cacheHitRate = new Counter({
    name: 'cache_hits_total',
    help: 'Total number of cache hits',
    labelNames: ['cache_type'],
  });

  recordHttpRequest(method: string, route: string, statusCode: number, duration: number) {
    this.httpRequestsTotal.inc({ method, route, status_code: statusCode });
    this.httpRequestDuration.observe({ method, route, status_code: statusCode }, duration);
  }

  setDatabaseConnections(count: number) {
    this.databaseConnectionsActive.set(count);
  }

  recordCacheHit(cacheType: string) {
    this.cacheHitRate.inc({ cache_type: cacheType });
  }

  getMetrics() {
    return register.metrics();
  }
}
```

#### Health Check Endpoint
```typescript
// health.controller.ts
@Controller('health')
export class HealthController {
  constructor(
    private readonly dataSource: DataSource,
    private readonly redisService: RedisService,
  ) {}

  @Get()
  async health() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }

  @Get('ready')
  async readiness() {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkExternalServices(),
    ]);

    const failed = checks.filter(check => check.status === 'rejected');

    if (failed.length > 0) {
      throw new ServiceUnavailableException('Service not ready');
    }

    return {
      status: 'ready',
      checks: {
        database: 'ok',
        redis: 'ok',
        external: 'ok',
      },
    };
  }

  private async checkDatabase(): Promise<void> {
    await this.dataSource.query('SELECT 1');
  }

  private async checkRedis(): Promise<void> {
    await this.redisService.getClient().ping();
  }

  private async checkExternalServices(): Promise<void> {
    // Check external dependencies
    // Throw error if any service is unavailable
  }
}
```

---

## 🎯 21) Học tập trọn đời (Handbook dùng lâu dài)

### Bảng chuẩn ôn tập

#### Core Knowledge Areas

##### 1. CrudOptions Mastery
```typescript
// Quarterly Review Checklist
interface CrudOptionsReview {
  // Model Configuration
  modelType: boolean;           // Understand entity/model binding
  dtoMapping: boolean;          // Create/Update/Replace DTOs
  serialization: boolean;       // Response transformation

  // Query Configuration
  allowedFields: boolean;       // Field whitelisting
  joinOptions: boolean;         // Relation configuration
  sortOptions: boolean;         // Default sorting
  filterOptions: boolean;       // Filter constraints
  paginationLimits: boolean;    // Limit enforcement
  cachingStrategy: boolean;     // Cache configuration

  // Route Configuration
  routeSelection: boolean;      // Enable/disable routes
  routeCustomization: boolean;  // Custom route options
  parameterMapping: boolean;    // URL parameter binding
  middlewareIntegration: boolean; // Guard/interceptor setup
}
```

##### 2. HTTP Query Language Expertise
```typescript
// Operator Mastery Checklist
interface QueryLanguageReview {
  // Basic Operators
  equalityOperators: boolean;   // $eq, $ne
  comparisonOperators: boolean; // $gt, $lt, $gte, $lte
  arrayOperators: boolean;      // $in, $notin
  stringOperators: boolean;     // $cont, $starts, $ends
  nullOperators: boolean;       // $isnull, $notnull

  // Advanced Features
  logicalOperators: boolean;    // AND, OR combinations
  caseInsensitive: boolean;     // $contL, $startsL, $endsL
  betweenOperator: boolean;     // $between for ranges

  // Security & Performance
  whitelistValidation: boolean; // Field/relation whitelisting
  parameterBinding: boolean;    // SQL injection prevention
  queryOptimization: boolean;   // Index usage, query plans
  cachingIntegration: boolean;  // Query result caching
}
```

##### 3. ICrudService Interface Contracts
```typescript
// Service Implementation Review
interface CrudServiceReview {
  // Core Methods
  getManyImplementation: boolean;    // Pagination, filtering, sorting
  getOneImplementation: boolean;     // Single resource retrieval
  createOneImplementation: boolean;  // Resource creation with validation
  updateOneImplementation: boolean;  // Partial/full updates
  deleteOneImplementation: boolean;  // Soft/hard delete
  recoverOneImplementation: boolean; // Soft delete recovery

  // Advanced Features
  transactionSupport: boolean;       // ACID compliance
  eventEmission: boolean;           // Domain events
  auditLogging: boolean;            // Change tracking
  cacheInvalidation: boolean;       // Cache management

  // Error Handling
  validationErrors: boolean;        // Input validation
  constraintViolations: boolean;    // Database constraints
  businessRuleViolations: boolean;  // Domain rules
  exceptionMapping: boolean;        // Error transformation
}
```

##### 4. Adapter Pattern Implementation
```typescript
// Adapter Mastery Review
interface AdapterReview {
  // ORM Adapters
  typeormAdapter: boolean;      // TypeORM implementation
  prismaAdapter: boolean;       // Prisma implementation
  sequelizeAdapter: boolean;    // Sequelize implementation
  mongooseAdapter: boolean;     // MongoDB implementation

  // Cache Adapters
  redisAdapter: boolean;        // Redis implementation
  memcachedAdapter: boolean;    // Memcached implementation
  inMemoryAdapter: boolean;     // In-memory implementation

  // Storage Adapters
  cloudinaryAdapter: boolean;   // Cloudinary implementation
  s3Adapter: boolean;          // AWS S3 implementation
  gcsAdapter: boolean;         // Google Cloud Storage

  // Communication Adapters
  socketAdapter: boolean;       // Socket.IO implementation
  mailerAdapter: boolean;       // Email service implementation
  smsAdapter: boolean;         // SMS service implementation
}
```

##### 5. Security Checklist Compliance
```typescript
// Security Review Checklist
interface SecurityReview {
  // OWASP Top 10
  accessControl: boolean;       // Broken access control prevention
  cryptographicFailures: boolean; // Encryption implementation
  injectionPrevention: boolean; // SQL/NoSQL/Command injection
  insecureDesign: boolean;     // Security by design
  securityMisconfiguration: boolean; // Secure defaults
  vulnerableComponents: boolean; // Dependency management
  authenticationFailures: boolean; // Authentication implementation
  dataIntegrityFailures: boolean; // Data validation
  loggingFailures: boolean;    // Security logging
  ssrfPrevention: boolean;     // Server-side request forgery

  // GDPR Compliance
  dataMinimization: boolean;    // Collect only necessary data
  consentManagement: boolean;   // User consent tracking
  dataPortability: boolean;     // Data export functionality
  rightToErasure: boolean;     // Data deletion capability
  dataAnonymization: boolean;   // PII anonymization
  auditTrail: boolean;         // Data processing logs
}
```

### Quarterly Learning Activities

#### Q1: Foundation Reinforcement
```typescript
// Q1 Learning Plan
interface Q1LearningPlan {
  week1_2: {
    focus: "SOLID Principles Deep Dive";
    activities: [
      "Review all existing code for SOLID violations",
      "Refactor 1 module to perfect SOLID compliance",
      "Create SOLID principles documentation with examples"
    ];
    deliverable: "SOLID Compliance Report";
  };

  week3_4: {
    focus: "Design Patterns Mastery";
    activities: [
      "Implement 3 new design patterns in codebase",
      "Create pattern catalog with use cases",
      "Conduct design pattern workshop"
    ];
    deliverable: "Design Pattern Implementation Guide";
  };

  week5_6: {
    focus: "HTTP Query Language Enhancement";
    activities: [
      "Add 2 new operators to query language",
      "Implement query performance optimization",
      "Create query language tutorial"
    ];
    deliverable: "Enhanced Query Language Documentation";
  };

  // ... continue for weeks 7-12
}
```

#### Q2: Advanced Architecture
```typescript
// Q2 Learning Plan
interface Q2LearningPlan {
  week1_2: {
    focus: "Event-Driven Architecture";
    activities: [
      "Implement domain events in 1 module",
      "Add event sourcing capability",
      "Create event-driven communication between services"
    ];
    deliverable: "Event-Driven Architecture Implementation";
  };

  week3_4: {
    focus: "CQRS Implementation";
    activities: [
      "Separate read/write models for 1 resource",
      "Implement command/query handlers",
      "Add eventual consistency handling"
    ];
    deliverable: "CQRS Pattern Implementation";
  };

  // ... continue for remaining weeks
}
```

#### Q3: Multi-Platform Expansion
```typescript
// Q3 Learning Plan
interface Q3LearningPlan {
  week1_2: {
    focus: "FastAPI/Python Port";
    activities: [
      "Port 1 complete resource to FastAPI",
      "Implement SQLAlchemy adapter",
      "Create Python-specific documentation"
    ];
    deliverable: "Python/FastAPI Implementation";
  };

  week3_4: {
    focus: "Laravel/PHP Port";
    activities: [
      "Port 1 complete resource to Laravel",
      "Implement Eloquent adapter",
      "Create PHP-specific documentation"
    ];
    deliverable: "PHP/Laravel Implementation";
  };

  // ... continue for other platforms
}
```

#### Q4: Production Excellence
```typescript
// Q4 Learning Plan
interface Q4LearningPlan {
  week1_2: {
    focus: "Security Enhancement";
    activities: [
      "Complete security audit of all components",
      "Update dependency versions",
      "Implement additional security controls"
    ];
    deliverable: "Security Audit Report & Improvements";
  };

  week3_4: {
    focus: "Performance Optimization";
    activities: [
      "Benchmark all critical paths",
      "Optimize database queries",
      "Implement advanced caching strategies"
    ];
    deliverable: "Performance Optimization Report";
  };

  week5_6: {
    focus: "New Adapter Development";
    activities: [
      "Implement Kafka event-outbox adapter",
      "Add GraphQL subscription support",
      "Create webhook notification adapter"
    ];
    deliverable: "New Adapter Implementations";
  };

  // ... continue for remaining weeks
}
```

### Continuous Improvement Process

#### Monthly Reviews
```typescript
// Monthly Review Template
interface MonthlyReview {
  codeQualityMetrics: {
    solidCompliance: number;      // Percentage of SOLID-compliant classes
    testCoverage: number;         // Unit/integration test coverage
    technicalDebt: number;        // Hours of technical debt
    codeComplexity: number;       // Average cyclomatic complexity
  };

  securityMetrics: {
    vulnerabilityCount: number;   // Number of security vulnerabilities
    auditCompliance: number;      // Percentage of audited actions
    securityTestCoverage: number; // Security test coverage
  };

  performanceMetrics: {
    responseTime: number;         // Average API response time
    throughput: number;           // Requests per second
    errorRate: number;            // Error percentage
    resourceUtilization: number;  // CPU/Memory usage
  };

  learningObjectives: {
    completed: string[];          // Completed learning objectives
    inProgress: string[];         // Current learning activities
    planned: string[];            // Planned learning activities
  };
}
```

#### Knowledge Sharing Activities
```typescript
// Knowledge Sharing Plan
interface KnowledgeSharingPlan {
  monthlyTechTalks: {
    topics: [
      "SOLID Principles in Practice",
      "Advanced Query Optimization",
      "Security Best Practices",
      "Performance Tuning Techniques",
      "Design Pattern Applications"
    ];
    audience: "Development Team";
    format: "30-minute presentation + Q&A";
  };

  quarterlyWorkshops: {
    topics: [
      "Hands-on CRUD Implementation",
      "Multi-Platform Porting Workshop",
      "Security Audit Workshop",
      "Performance Testing Workshop"
    ];
    audience: "Engineering Organization";
    format: "Half-day interactive workshop";
  };

  documentationUpdates: {
    frequency: "Weekly";
    responsibilities: [
      "Update API documentation",
      "Maintain architectural decision records",
      "Update security guidelines",
      "Maintain performance benchmarks"
    ];
  };
}
```

### Long-term Mastery Goals

#### Year 1: Foundation Excellence
- [ ] **100% SOLID Compliance**: All code follows SOLID principles
- [ ] **Complete Security Coverage**: All OWASP/GDPR requirements met
- [ ] **Performance Mastery**: All performance budgets consistently met
- [ ] **Multi-Platform Competency**: 3+ platform implementations completed

#### Year 2: Architecture Leadership
- [ ] **Advanced Patterns**: Event sourcing, CQRS, microservices implemented
- [ ] **Team Mentorship**: Successfully mentored 3+ developers
- [ ] **Industry Recognition**: Presented at conferences or published articles
- [ ] **Innovation**: Contributed new patterns or optimizations to the framework

#### Year 3: Ecosystem Contribution
- [ ] **Open Source Leadership**: Maintained popular open-source projects
- [ ] **Community Building**: Built developer community around the framework
- [ ] **Standard Setting**: Influenced industry standards and best practices
- [ ] **Knowledge Legacy**: Created comprehensive learning resources for future developers

---

## 🎉 Kết luận

Tài liệu Master Requirements & Handbook này cung cấp một **chuẩn vàng hoàn chỉnh** để xây dựng kiến trúc CRUD enterprise có thể áp dụng trên mọi framework và ngôn ngữ lập trình.

### Thành tựu đạt được ✅

1. **Kiến trúc đa tầng hoàn chỉnh** với SOLID principles triệt để
2. **HTTP Query Language chuẩn hóa** cho mọi thao tác CRUD
3. **Security OWASP/GDPR compliant** với audit trail đầy đủ
4. **Portability pattern** cho phép chuyển đổi framework/ngôn ngữ dễ dàng
5. **Testing strategy toàn diện** từ unit đến performance testing
6. **Production-ready deployment** với monitoring và observability
7. **Governance framework** đảm bảo chất lượng và consistency
8. **Learning roadmap** cho continuous improvement

### Giá trị cốt lõi 🌟

- **Tái sử dụng**: Một lần thiết kế, áp dụng mọi nơi
- **Bảo mật**: Security by design với compliance đầy đủ
- **Hiệu năng**: Performance budget và optimization strategies
- **Mở rộng**: Horizontal scaling và cloud-native ready
- **Bảo trì**: Clean code, comprehensive testing, documentation
- **Học tập**: Continuous learning và knowledge sharing

### Roadmap tiếp theo 🚀

Với foundation vững chắc này, team có thể:
1. **Triển khai production** với confidence cao
2. **Mở rộng sang platforms khác** một cách systematic
3. **Xây dựng team expertise** thông qua structured learning
4. **Đóng góp cho community** với proven patterns và practices
5. **Dẫn dắt industry standards** trong CRUD architecture

**Đây chính là "chuẩn vàng" để học, sử dụng, tuỳ biến và tái tạo trên mọi framework/ngôn ngữ!** 🏆
```
```
```
```
```
