# 🏗️ Enterprise Platform Makefile
# Comprehensive automation for the ultimate enterprise architecture

.PHONY: help setup clean build test lint format docker-build docker-up docker-down health-check dev prod deploy

# Default target
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
RESET := \033[0m

# Project configuration
PROJECT_NAME := enterprise-platform
DOCKER_COMPOSE := docker-compose
NODE_VERSION := 18
PYTHON_VERSION := 3.9

## 🎯 Main Commands

help: ## 📖 Show this help message
	@echo "$(BLUE)🏗️ Enterprise Platform - Available Commands$(RESET)"
	@echo ""
	@awk 'BEGIN {FS = ":.*##"; printf "Usage: make $(GREEN)<target>$(RESET)\n\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2 } /^##@/ { printf "\n$(BLUE)%s$(RESET)\n", substr($$0, 5) }' $(MAKEFILE_LIST)

setup: ## 🚀 Complete one-command setup
	@echo "$(BLUE)🚀 Starting complete enterprise platform setup...$(RESET)"
	@chmod +x scripts/setup.sh
	@./scripts/setup.sh

quick-setup: ## ⚡ Quick setup (skip some checks)
	@echo "$(BLUE)⚡ Starting quick setup...$(RESET)"
	@chmod +x scripts/setup.sh
	@./scripts/setup.sh --skip-requirements

##@ 🔧 Development

dev: ## 💻 Start development environment
	@echo "$(BLUE)💻 Starting development environment...$(RESET)"
	@$(DOCKER_COMPOSE) up -d postgres redis
	@npm run dev

dev-full: ## 🌟 Start full development environment with all services
	@echo "$(BLUE)🌟 Starting full development environment...$(RESET)"
	@$(DOCKER_COMPOSE) up -d

stop: ## 🛑 Stop all services
	@echo "$(YELLOW)🛑 Stopping all services...$(RESET)"
	@$(DOCKER_COMPOSE) down

restart: ## 🔄 Restart all services
	@echo "$(BLUE)🔄 Restarting all services...$(RESET)"
	@$(DOCKER_COMPOSE) restart

logs: ## 📋 Show logs from all services
	@$(DOCKER_COMPOSE) logs -f

##@ 🧪 Testing

test: ## 🧪 Run all tests
	@echo "$(BLUE)🧪 Running all tests...$(RESET)"
	@npm run test

test-unit: ## 🔬 Run unit tests only
	@echo "$(BLUE)🔬 Running unit tests...$(RESET)"
	@npm run test:unit

test-integration: ## 🔗 Run integration tests
	@echo "$(BLUE)🔗 Running integration tests...$(RESET)"
	@npm run test:integration

test-e2e: ## 🎭 Run end-to-end tests
	@echo "$(BLUE)🎭 Running E2E tests...$(RESET)"
	@npm run test:e2e

test-performance: ## ⚡ Run performance tests
	@echo "$(BLUE)⚡ Running performance tests...$(RESET)"
	@npm run test:performance

coverage: ## 📊 Generate test coverage report
	@echo "$(BLUE)📊 Generating coverage report...$(RESET)"
	@npm run test:coverage

##@ 🔍 Code Quality

lint: ## 🔍 Run linting
	@echo "$(BLUE)🔍 Running linters...$(RESET)"
	@npm run lint

lint-fix: ## 🔧 Fix linting issues
	@echo "$(BLUE)🔧 Fixing linting issues...$(RESET)"
	@npm run lint:fix

format: ## 🎨 Format code
	@echo "$(BLUE)🎨 Formatting code...$(RESET)"
	@npm run format

type-check: ## 📝 Run TypeScript type checking
	@echo "$(BLUE)📝 Running type checking...$(RESET)"
	@npm run type-check

security-scan: ## 🔒 Run security vulnerability scan
	@echo "$(BLUE)🔒 Running security scan...$(RESET)"
	@npm audit
	@npm run security:scan

quality-check: lint type-check security-scan ## ✅ Run all quality checks
	@echo "$(GREEN)✅ All quality checks completed$(RESET)"

##@ 🐳 Docker Operations

docker-build: ## 🏗️ Build all Docker images
	@echo "$(BLUE)🏗️ Building Docker images...$(RESET)"
	@$(DOCKER_COMPOSE) build

docker-up: ## 🚀 Start all Docker services
	@echo "$(BLUE)🚀 Starting Docker services...$(RESET)"
	@$(DOCKER_COMPOSE) up -d

docker-down: ## 🛑 Stop and remove Docker containers
	@echo "$(YELLOW)🛑 Stopping Docker services...$(RESET)"
	@$(DOCKER_COMPOSE) down

docker-clean: ## 🧹 Clean Docker resources
	@echo "$(YELLOW)🧹 Cleaning Docker resources...$(RESET)"
	@docker system prune -f
	@docker volume prune -f

docker-rebuild: docker-down docker-clean docker-build docker-up ## 🔄 Rebuild and restart all Docker services

##@ 💾 Database Operations

db-migrate: ## 📊 Run database migrations
	@echo "$(BLUE)📊 Running database migrations...$(RESET)"
	@npm run migration:run

db-rollback: ## ⏪ Rollback last migration
	@echo "$(YELLOW)⏪ Rolling back last migration...$(RESET)"
	@npm run migration:revert

db-seed: ## 🌱 Seed database with test data
	@echo "$(BLUE)🌱 Seeding database...$(RESET)"
	@npm run db:seed

db-reset: ## 🔄 Reset database (drop, create, migrate, seed)
	@echo "$(YELLOW)🔄 Resetting database...$(RESET)"
	@npm run db:reset

db-backup: ## 💾 Create database backup
	@echo "$(BLUE)💾 Creating database backup...$(RESET)"
	@./scripts/backup-database.sh

##@ 🚀 Deployment

build: ## 🏗️ Build production assets
	@echo "$(BLUE)🏗️ Building production assets...$(RESET)"
	@npm run build

deploy-staging: ## 🎭 Deploy to staging environment
	@echo "$(BLUE)🎭 Deploying to staging...$(RESET)"
	@./scripts/deploy-staging.sh

deploy-prod: ## 🌟 Deploy to production environment
	@echo "$(RED)🌟 Deploying to production...$(RESET)"
	@./scripts/deploy-production.sh

##@ 📊 Monitoring & Health

health-check: ## 🏥 Run health checks on all services
	@echo "$(BLUE)🏥 Running health checks...$(RESET)"
	@./scripts/health-check.sh

monitoring-up: ## 📈 Start monitoring stack
	@echo "$(BLUE)📈 Starting monitoring stack...$(RESET)"
	@$(DOCKER_COMPOSE) up -d grafana prometheus jaeger

monitoring-down: ## 📉 Stop monitoring stack
	@echo "$(YELLOW)📉 Stopping monitoring stack...$(RESET)"
	@$(DOCKER_COMPOSE) stop grafana prometheus jaeger

status: ## 📊 Show status of all services
	@echo "$(BLUE)📊 Service Status:$(RESET)"
	@$(DOCKER_COMPOSE) ps

##@ 🛠️ Utilities

clean: ## 🧹 Clean all build artifacts and dependencies
	@echo "$(YELLOW)🧹 Cleaning build artifacts...$(RESET)"
	@rm -rf node_modules
	@rm -rf dist
	@rm -rf build
	@rm -rf coverage
	@rm -rf .nyc_output
	@find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true

install: ## 📦 Install all dependencies
	@echo "$(BLUE)📦 Installing dependencies...$(RESET)"
	@npm install
	@npm run install:all

update: ## 🔄 Update all dependencies
	@echo "$(BLUE)🔄 Updating dependencies...$(RESET)"
	@npm update
	@npm run update:all

##@ 🎯 Code Generation

generate-service: ## 🔧 Generate new service (usage: make generate-service name=my-service type=nestjs)
	@echo "$(BLUE)🔧 Generating new service...$(RESET)"
	@./tools/generators/generate-service.sh --type=$(type) --name=$(name)

generate-component: ## 🧩 Generate new component (usage: make generate-component name=MyComponent type=react)
	@echo "$(BLUE)🧩 Generating new component...$(RESET)"
	@./tools/generators/generate-component.sh --type=$(type) --name=$(name)

generate-api: ## 🌐 Generate API endpoints (usage: make generate-api name=users)
	@echo "$(BLUE)🌐 Generating API endpoints...$(RESET)"
	@./tools/generators/generate-api.sh --name=$(name)

##@ 📚 Documentation

docs-build: ## 📖 Build documentation
	@echo "$(BLUE)📖 Building documentation...$(RESET)"
	@npm run docs:build

docs-serve: ## 🌐 Serve documentation locally
	@echo "$(BLUE)🌐 Serving documentation...$(RESET)"
	@npm run docs:serve

docs-deploy: ## 🚀 Deploy documentation
	@echo "$(BLUE)🚀 Deploying documentation...$(RESET)"
	@npm run docs:deploy

##@ 🔐 Security

security-audit: ## 🔍 Run security audit
	@echo "$(BLUE)🔍 Running security audit...$(RESET)"
	@npm audit
	@npm run security:audit

security-fix: ## 🔧 Fix security vulnerabilities
	@echo "$(BLUE)🔧 Fixing security vulnerabilities...$(RESET)"
	@npm audit fix

##@ 🎓 Learning & Examples

examples: ## 💡 Run example implementations
	@echo "$(BLUE)💡 Running examples...$(RESET)"
	@./scripts/run-examples.sh

tutorial: ## 📚 Start interactive tutorial
	@echo "$(BLUE)📚 Starting tutorial...$(RESET)"
	@./scripts/tutorial.sh

benchmark: ## ⚡ Run performance benchmarks
	@echo "$(BLUE)⚡ Running benchmarks...$(RESET)"
	@npm run benchmark

##@ 🌍 Environment Management

env-setup: ## ⚙️ Setup environment files
	@echo "$(BLUE)⚙️ Setting up environment files...$(RESET)"
	@./scripts/setup-env.sh

env-validate: ## ✅ Validate environment configuration
	@echo "$(BLUE)✅ Validating environment...$(RESET)"
	@./scripts/validate-env.sh

##@ 🚀 Quick Actions

quick-start: setup dev ## ⚡ Complete setup and start development (one command)
	@echo "$(GREEN)⚡ Quick start completed! Development environment is ready.$(RESET)"

full-reset: docker-down clean install docker-build docker-up db-migrate ## 🔄 Complete reset and rebuild
	@echo "$(GREEN)🔄 Full reset completed!$(RESET)"

production-ready: quality-check test build ## 🌟 Ensure production readiness
	@echo "$(GREEN)🌟 Production readiness check completed!$(RESET)"

##@ ℹ️ Information

info: ## ℹ️ Show project information
	@echo "$(BLUE)📋 Project Information:$(RESET)"
	@echo "  Name: $(PROJECT_NAME)"
	@echo "  Node Version: $(NODE_VERSION)"
	@echo "  Python Version: $(PYTHON_VERSION)"
	@echo "  Docker Compose: $(DOCKER_COMPOSE)"
	@echo ""
	@echo "$(BLUE)📊 Quick Stats:$(RESET)"
	@find . -name "*.ts" -not -path "./node_modules/*" | wc -l | xargs echo "  TypeScript files:"
	@find . -name "*.py" -not -path "./node_modules/*" | wc -l | xargs echo "  Python files:"
	@find . -name "*.md" -not -path "./node_modules/*" | wc -l | xargs echo "  Documentation files:"

urls: ## 🌐 Show all service URLs
	@echo "$(BLUE)🌐 Service URLs:$(RESET)"
	@echo "  API Gateway:     http://localhost:3000"
	@echo "  API Docs:        http://localhost:3000/docs"
	@echo "  AI Service:      http://localhost:8000"
	@echo "  Grafana:         http://localhost:3001 (admin/admin123)"
	@echo "  Prometheus:      http://localhost:9090"
	@echo "  Jaeger:          http://localhost:16686"
	@echo "  Kibana:          http://localhost:5601"

version: ## 📋 Show version information
	@echo "$(BLUE)📋 Version Information:$(RESET)"
	@node --version 2>/dev/null || echo "  Node.js: Not installed"
	@npm --version 2>/dev/null || echo "  npm: Not installed"
	@python3 --version 2>/dev/null || echo "  Python: Not installed"
	@docker --version 2>/dev/null || echo "  Docker: Not installed"
	@$(DOCKER_COMPOSE) --version 2>/dev/null || echo "  Docker Compose: Not installed"

##@ 🎯 Aliases (shortcuts)

s: setup ## Alias for setup
d: dev ## Alias for dev
t: test ## Alias for test
l: lint ## Alias for lint
b: build ## Alias for build
h: health-check ## Alias for health-check

# Make scripts executable
scripts-executable:
	@chmod +x scripts/*.sh
	@chmod +x tools/generators/*.sh
	@chmod +x tools/scripts/*.sh

# Initialize project (run once after cloning)
init: scripts-executable setup
	@echo "$(GREEN)🎉 Project initialization completed!$(RESET)"