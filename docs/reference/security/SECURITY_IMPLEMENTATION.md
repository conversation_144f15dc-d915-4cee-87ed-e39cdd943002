# Security Implementation (Canonical)

Purpose
- Central reference for authentication and authorization implementation patterns
- Consolidates duplicated code snippets and guidance from architecture and implementation docs

Contents
1) Authentication: JWT Guard (NestJS)
2) Authorization: Roles-based and attribute-based access control
3) Layering model (RBAC, ABAC, ReBAC)
4) Best practices and checklists
5) References

1) Authentication: JWT Guard (NestJS)
```typescript
// JWT Authentication Guard (NestJS)
@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private userService: UserService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException();
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
      });

      const user = await this.userService.findById(payload.sub);
      if (!user || !user.isActive) {
        throw new UnauthorizedException();
      }

      request['user'] = user;
      return true;
    } catch {
      throw new UnauthorizedException();
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

2) Authorization: Roles Guard (RBAC)
```typescript
// RBAC Roles Guard (NestJS)
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}
```

3) Layering model
- RBAC: role-based access for coarse-grained permissions
- ABAC: attribute-based rules (user, resource, context) for fine-grained decisions
- ReBAC: relationship-based (graph) for complex org/resource hierarchies
- Policy evaluation order (suggestion): deny overrides > explicit allow > implicit deny

4) Best practices and checklists
- Secrets: no secrets in code; use secret store; rotate frequently
- Tokens: short-lived JWTs; refresh tokens; revoke on compromise
- Least privilege: scope tokens and service accounts narrowly
- Input validation: server-side validation and sanitization for all inputs
- Audit: record auth events; enable tamper-evident logs; monitor anomalies
- Compliance: adopt OWASP ASVS controls; review regularly

5) References
- Standards hub: ../../standards/README.md
- Architecture security overview: ../../core/architecture/README.md#-security-architecture
- Implementation security guide: ../../core/implementation/README.md

