# 🏗️ **CẤU TRÚC THƯ MỤC ENTERPRISE-GRADE HOÀN CHỈNH**

> **Tổ chức theo Clean Architecture + DDD + Microservices + AI-Native patterns**

## 📋 **TỔNG QUAN CẤU TRÚC**

Dựa trên phân tích tài liệu kiến trúc phần mềm từ README.md, INSTRUCTIONS.md, TAI_LIEU_TONG_HOP_HUONG_DAN_HE_THONG.md và KNOWLEDGE_BASE.md, đây là cấu trúc thư mục được đề xuất theo 100% kiến thức từ các tài liệu:

```
enterprise-platform/
├── 📚 docs/                          # 📖 COMPLETE DOCUMENTATION HUB
│   ├── 01-getting-started/           # Quick start & installation guides
│   ├── 02-architecture/              # System design & architectural patterns
│   ├── 03-development/               # Development workflows & standards
│   ├── 04-api/                       # API documentation & specifications
│   ├── 05-deployment/                # Deployment guides & procedures
│   ├── 06-operations/                # Operations & monitoring guides
│   ├── 07-knowledge-base/            # Complete IT knowledge repository
│   ├── 08-tutorials/                 # Step-by-step learning tutorials
│   ├── 09-reference/                 # Quick references & cheat sheets
│   ├── 10-contributing/              # Contribution guidelines
│   └── README.md                     # Documentation navigation index
│
├── 🎯 apps/                          # 🖥️ APPLICATION LAYER (Presentation)
│   ├── api-gateway/                  # Main API Gateway (NestJS + GraphQL)
│   │   ├── src/
│   │   │   ├── auth/                 # Authentication modules
│   │   │   ├── middleware/           # Request/response middleware
│   │   │   ├── routes/               # Route definitions
│   │   │   ├── graphql/              # GraphQL schemas & resolvers
│   │   │   └── main.ts               # Application entry point
│   │   ├── Dockerfile
│   │   ├── package.json
│   │   └── README.md                 # Gateway setup & configuration
│   ├── web-app/                      # Frontend Web Application (Next.js)
│   │   ├── src/
│   │   │   ├── components/           # Reusable UI components
│   │   │   ├── pages/                # Next.js pages
│   │   │   ├── hooks/                # Custom React hooks
│   │   │   ├── services/             # API service clients
│   │   │   └── utils/                # Frontend utilities
│   │   ├── public/                   # Static assets
│   │   ├── package.json
│   │   └── README.md                 # Frontend setup guide
│   ├── admin-panel/                  # Admin Dashboard (React + TypeScript)
│   │   ├── src/
│   │   │   ├── components/           # Admin UI components
│   │   │   ├── pages/                # Admin pages
│   │   │   ├── services/             # Admin API services
│   │   │   └── utils/                # Admin utilities
│   │   └── README.md                 # Admin panel guide
│   ├── mobile-app/                   # Mobile Application (React Native)
│   │   ├── src/
│   │   │   ├── components/           # Mobile components
│   │   │   ├── screens/              # App screens
│   │   │   ├── navigation/           # Navigation setup
│   │   │   └── services/             # Mobile API services
│   │   └── README.md                 # Mobile app setup
│   └── README.md                     # Applications overview & setup
│
├── ⚡ services/                      # 🔧 MICROSERVICES LAYER (Business Logic)
│   ├── user-service/                 # User Management Service (NestJS)
│   │   ├── src/
│   │   │   ├── domain/               # Domain layer (entities, value objects)
│   │   │   │   ├── entities/         # User, Profile entities
│   │   │   │   ├── value-objects/    # Email, UserId value objects
│   │   │   │   ├── events/           # Domain events
│   │   │   │   └── repositories/     # Repository interfaces
│   │   │   ├── application/          # Application layer (use cases)
│   │   │   │   ├── commands/         # Command handlers (CQRS)
│   │   │   │   ├── queries/          # Query handlers (CQRS)
│   │   │   │   ├── services/         # Application services
│   │   │   │   └── dtos/             # Data transfer objects
│   │   │   ├── infrastructure/       # Infrastructure layer
│   │   │   │   ├── database/         # Database implementations
│   │   │   │   ├── external/         # External service clients
│   │   │   │   └── messaging/        # Message broker implementations
│   │   │   ├── interface/            # Interface layer
│   │   │   │   ├── controllers/      # REST API controllers
│   │   │   │   ├── middleware/       # Service middleware
│   │   │   │   └── validators/       # Input validators
│   │   │   └── main.ts               # Service bootstrap
│   │   ├── tests/                    # Service-specific tests
│   │   ├── Dockerfile
│   │   ├── package.json
│   │   └── README.md                 # User service documentation
│   ├── ai-service/                   # AI/ML Processing Service (FastAPI + Python)
│   │   ├── app/
│   │   │   ├── domain/               # AI domain models
│   │   │   ├── services/             # ML services
│   │   │   ├── models/               # ML model definitions
│   │   │   ├── api/                  # FastAPI routes
│   │   │   └── main.py               # FastAPI application
│   │   ├── models/                   # Trained model files
│   │   ├── requirements.txt
│   │   ├── Dockerfile
│   │   └── README.md                 # AI service setup & usage
│   ├── analytics-service/            # Data Analytics Service (FastAPI + Python)
│   ├── performance-service/          # High Performance APIs (Go)
│   ├── notification-service/         # Real-time Notifications (WebSocket)
│   ├── file-service/                 # File Management Service (Go)
│   ├── _template/                    # Service template for new services
│   └── README.md                     # Services overview & patterns
│
├── 📚 libs/                          # 🧩 SHARED LIBRARIES & UTILITIES
│   ├── shared-types/                 # Common TypeScript interfaces & types
│   │   ├── api/                      # API response/request types
│   │   ├── domain/                   # Domain model types
│   │   ├── events/                   # Event types
│   │   └── README.md                 # Types documentation
│   ├── domain-models/                # DDD Entities & Value Objects
│   │   ├── base/                     # Base classes (Entity, ValueObject)
│   │   ├── user/                     # User domain models
│   │   ├── task/                     # Task domain models
│   │   └── README.md                 # Domain modeling guide
│   ├── algorithms/                   # Data Structures & Algorithms
│   │   ├── sorting/                  # Sorting algorithms
│   │   ├── searching/                # Search algorithms
│   │   ├── data-structures/          # Common data structures
│   │   └── README.md                 # Algorithms documentation
│   ├── security/                     # Security utilities & middleware
│   │   ├── auth/                     # Authentication utilities
│   │   ├── encryption/               # Encryption/decryption
│   │   ├── validation/               # Input validation
│   │   └── README.md                 # Security implementation guide
│   ├── database/                     # Database abstractions & utilities
│   │   ├── repositories/             # Base repository patterns
│   │   ├── migrations/               # Database migration utilities
│   │   ├── seeders/                  # Data seeding utilities
│   │   └── README.md                 # Database patterns guide
│   ├── testing/                      # Testing utilities & frameworks
│   │   ├── fixtures/                 # Test data fixtures
│   │   ├── mocks/                    # Mock implementations
│   │   ├── helpers/                  # Test helper functions
│   │   └── README.md                 # Testing strategy guide
│   └── README.md                     # Shared libraries overview
│
├── 📝 templates/                     # 🎨 CODE TEMPLATES & GENERATORS
│   ├── service/                      # Service templates
│   │   ├── nestjs-service/           # NestJS service template
│   │   ├── fastapi-service/          # FastAPI service template
│   │   ├── go-service/               # Go service template
│   │   └── README.md                 # Service template usage
│   ├── component/                    # UI component templates
│   │   ├── react-component/          # React component template
│   │   ├── vue-component/            # Vue component template
│   │   └── README.md                 # Component template guide
│   ├── infrastructure/               # Infrastructure templates
│   │   ├── kubernetes/               # K8s deployment templates
│   │   ├── docker/                   # Docker configuration templates
│   │   └── README.md                 # Infrastructure template guide
│   ├── testing/                      # Testing templates
│   │   ├── unit-test/                # Unit test templates
│   │   ├── integration-test/         # Integration test templates
│   │   └── README.md                 # Testing template guide
│   ├── documentation/                # Documentation templates
│   │   ├── api-docs/                 # API documentation templates
│   │   ├── service-docs/             # Service documentation templates
│   │   └── README.md                 # Documentation template guide
│   └── README.md                     # Templates overview & usage
│
├── 💡 examples/                      # 📋 IMPLEMENTATION EXAMPLES
│   ├── complete-features/            # End-to-end feature implementations
│   │   ├── user-registration/        # Complete user registration flow
│   │   ├── task-management/          # Task CRUD with AI recommendations
│   │   ├── real-time-chat/           # WebSocket chat implementation
│   │   └── README.md                 # Feature examples guide
│   ├── patterns/                     # Design pattern implementations
│   │   ├── repository-pattern/       # Repository pattern examples
│   │   ├── observer-pattern/         # Observer pattern examples
│   │   ├── factory-pattern/          # Factory pattern examples
│   │   └── README.md                 # Pattern examples guide
│   ├── integrations/                 # Third-party integration examples
│   │   ├── payment-gateways/         # Payment integration examples
│   │   ├── social-auth/              # Social authentication examples
│   │   ├── cloud-services/           # Cloud service integrations
│   │   └── README.md                 # Integration examples guide
│   ├── best-practices/               # Best practice implementations
│   │   ├── error-handling/           # Error handling examples
│   │   ├── logging/                  # Logging implementation examples
│   │   ├── caching/                  # Caching strategy examples
│   │   └── README.md                 # Best practices guide
│   └── README.md                     # Examples overview & navigation
│
├── 🏗️ infrastructure/               # ☁️ INFRASTRUCTURE AS CODE
│   ├── kubernetes/                   # K8s manifests & Helm charts
│   │   ├── namespaces/               # Namespace definitions
│   │   ├── deployments/              # Service deployments
│   │   ├── services/                 # Service definitions
│   │   ├── ingress/                  # Ingress configurations
│   │   ├── configmaps/               # Configuration maps
│   │   ├── secrets/                  # Secret management
│   │   └── README.md                 # K8s deployment guide
│   ├── terraform/                    # Cloud infrastructure (AWS/GCP/Azure)
│   │   ├── modules/                  # Reusable Terraform modules
│   │   ├── environments/             # Environment-specific configs
│   │   ├── providers/                # Cloud provider configurations
│   │   └── README.md                 # Infrastructure setup guide
│   ├── docker/                       # Docker configurations
│   │   ├── base-images/              # Custom base images
│   │   ├── compose-files/            # Docker Compose configurations
│   │   └── README.md                 # Docker setup guide
│   ├── monitoring/                   # Observability stack
│   │   ├── prometheus/               # Prometheus configuration
│   │   ├── grafana/                  # Grafana dashboards
│   │   ├── jaeger/                   # Distributed tracing
│   │   ├── elk/                      # Elasticsearch, Logstash, Kibana
│   │   └── README.md                 # Monitoring setup guide
│   └── README.md                     # Infrastructure overview
│
├── 🗄️ data/                         # 💾 DATA MANAGEMENT
│   ├── databases/                    # Database schemas & migrations
│   │   ├── postgresql/               # PostgreSQL schemas
│   │   ├── mongodb/                  # MongoDB collections
│   │   ├── redis/                    # Redis configurations
│   │   └── README.md                 # Database setup guide
│   ├── seeds/                        # Test data & fixtures
│   │   ├── development/              # Development seed data
│   │   ├── testing/                  # Test fixtures
│   │   ├── production/               # Production initial data
│   │   └── README.md                 # Data seeding guide
│   ├── backups/                      # Backup scripts & procedures
│   │   ├── automated/                # Automated backup scripts
│   │   ├── manual/                   # Manual backup procedures
│   │   └── README.md                 # Backup strategy guide
│   └── README.md                     # Data management overview
│
├── 🧪 tests/                        # 🔬 COMPREHENSIVE TESTING
│   ├── unit/                         # Unit tests (90%+ coverage)
│   │   ├── services/                 # Service unit tests
│   │   ├── libs/                     # Library unit tests
│   │   └── README.md                 # Unit testing guide
│   ├── integration/                  # Integration tests (80%+ coverage)
│   │   ├── api/                      # API integration tests
│   │   ├── database/                 # Database integration tests
│   │   └── README.md                 # Integration testing guide
│   ├── e2e/                          # End-to-end tests (70%+ coverage)
│   │   ├── user-flows/               # Complete user journey tests
│   │   ├── api-workflows/            # API workflow tests
│   │   └── README.md                 # E2E testing guide
│   ├── performance/                  # Load & stress testing
│   │   ├── load-tests/               # Load testing scripts
│   │   ├── stress-tests/             # Stress testing scenarios
│   │   └── README.md                 # Performance testing guide
│   └── README.md                     # Testing strategy overview
│
├── 🔧 tools/                        # 🛠️ DEVELOPMENT TOOLS
│   ├── scripts/                      # Automation scripts
│   │   ├── build/                    # Build automation
│   │   ├── deploy/                   # Deployment scripts
│   │   ├── maintenance/              # Maintenance utilities
│   │   └── README.md                 # Scripts documentation
│   ├── generators/                   # Code generators
│   │   ├── service-generator/        # Service scaffolding
│   │   ├── component-generator/      # Component scaffolding
│   │   └── README.md                 # Generator usage guide
│   ├── linters/                      # Custom linting rules
│   │   ├── eslint-config/            # ESLint configurations
│   │   ├── prettier-config/          # Prettier configurations
│   │   └── README.md                 # Linting setup guide
│   └── README.md                     # Development tools overview
│
├── 🤖 scripts/                      # ⚙️ AUTOMATION SCRIPTS
│   ├── setup/                        # Environment setup
│   │   ├── install-dependencies.sh   # Dependency installation
│   │   ├── configure-environment.sh  # Environment configuration
│   │   └── README.md                 # Setup instructions
│   ├── deployment/                   # Deployment automation
│   │   ├── deploy-staging.sh         # Staging deployment
│   │   ├── deploy-production.sh      # Production deployment
│   │   └── README.md                 # Deployment procedures
│   ├── maintenance/                  # Maintenance scripts
│   │   ├── backup-databases.sh       # Database backup automation
│   │   ├── cleanup-logs.sh           # Log cleanup utilities
│   │   └── README.md                 # Maintenance procedures
│   ├── monitoring/                   # Monitoring scripts
│   │   ├── health-check.sh           # System health checks
│   │   ├── performance-monitor.sh    # Performance monitoring
│   │   └── README.md                 # Monitoring procedures
│   └── README.md                     # Automation scripts overview
│
├── 📊 monitoring/                    # 📈 OBSERVABILITY STACK
│   ├── grafana/                      # Grafana dashboards
│   │   ├── dashboards/               # Pre-configured dashboards
│   │   ├── datasources/              # Data source configurations
│   │   └── README.md                 # Grafana setup guide
│   ├── prometheus/                   # Prometheus configuration
│   │   ├── rules/                    # Alerting rules
│   │   ├── targets/                  # Scrape targets
│   │   └── README.md                 # Prometheus setup guide
│   ├── jaeger/                       # Distributed tracing
│   │   ├── config/                   # Jaeger configurations
│   │   └── README.md                 # Tracing setup guide
│   ├── elk/                          # Elasticsearch, Logstash, Kibana
│   │   ├── elasticsearch/            # Elasticsearch configurations
│   │   ├── logstash/                 # Logstash pipelines
│   │   ├── kibana/                   # Kibana dashboards
│   │   └── README.md                 # ELK stack setup guide
│   └── README.md                     # Monitoring stack overview
│
├── 🚀 deployment/                   # 🌐 DEPLOYMENT CONFIGURATIONS
│   ├── environments/                 # Environment-specific configs
│   │   ├── development/              # Development environment
│   │   ├── staging/                  # Staging environment
│   │   ├── production/               # Production environment
│   │   └── README.md                 # Environment configuration guide
│   ├── ci-cd/                        # CI/CD pipelines
│   │   ├── github-actions/           # GitHub Actions workflows
│   │   ├── gitlab-ci/                # GitLab CI configurations
│   │   ├── jenkins/                  # Jenkins pipelines
│   │   └── README.md                 # CI/CD setup guide
│   ├── secrets/                      # Secret management
│   │   ├── vault/                    # HashiCorp Vault configurations
│   │   ├── k8s-secrets/              # Kubernetes secrets
│   │   └── README.md                 # Secret management guide
│   └── README.md                     # Deployment overview
│
├── 📄 Root Configuration Files       # 🔧 PROJECT CONFIGURATION
├── package.json                      # Node.js project configuration
├── deno.json                         # Deno project configuration
├── docker-compose.yml               # Local development environment
├── docker-compose.override.yml      # Development overrides
├── Dockerfile                        # Production container image
├── Dockerfile.dev                   # Development container image
├── Makefile                          # Build automation commands
├── nginx.conf                        # NGINX configuration
├── .env.example                      # Environment variables template
├── .gitignore                        # Git ignore patterns
├── .dockerignore                     # Docker ignore patterns
├── tsconfig.json                     # TypeScript configuration
├── eslint.config.js                 # ESLint configuration
├── prettier.config.js               # Prettier configuration
├── jest.config.js                   # Jest testing configuration
├── README.md                         # Project overview & quick start
├── CONTRIBUTING.md                   # Contribution guidelines
├── LICENSE                           # Project license
└── CHANGELOG.md                      # Version history & changes
```

## 📖 **CHỈ MỤC HƯỚNG DẪN CHI TIẾT**

### **🎯 1. DOCS/ - HUB TÀI LIỆU HOÀN CHỈNH**

**Nhiệm vụ**: Trung tâm tài liệu với cấu trúc có tổ chức theo từng chủ đề cụ thể

**Chỉ mục thành phần**:
- `01-getting-started/`: Hướng dẫn cài đặt và khởi chạy nhanh
- `02-architecture/`: Thiết kế hệ thống và patterns kiến trúc
- `03-development/`: Quy trình phát triển và coding standards
- `04-api/`: Tài liệu API với OpenAPI/Swagger specs
- `05-deployment/`: Hướng dẫn triển khai production
- `06-operations/`: Vận hành, monitoring và troubleshooting
- `07-knowledge-base/`: Kho kiến thức IT đầy đủ
- `08-tutorials/`: Tutorials từng bước cho developers
- `09-reference/`: Cheat sheets và quick references
- `10-contributing/`: Guidelines cho contributors

### **🖥️ 2. APPS/ - TẦNG Ứng DỤNG (PRESENTATION LAYER)**

**Nhiệm vụ**: Các ứng dụng frontend và API Gateway theo Clean Architecture

**Chỉ mục thành phần**:
- `api-gateway/`: Gateway chính (NestJS + GraphQL + Rate Limiting + Auth)
- `web-app/`: Ứng dụng web (Next.js + React + TypeScript)
- `admin-panel/`: Dashboard quản trị (React + TypeScript)
- `mobile-app/`: Ứng dụng di động (React Native)

**Cấu trúc chuẩn mỗi app**:
```
src/
├── components/     # UI components tái sử dụng
├── pages/         # Pages/screens
├── hooks/         # Custom hooks
├── services/      # API service clients
├── utils/         # Utilities
└── types/         # TypeScript type definitions
```

### *[object Object]SERVICES/ - TẦNG MICROSERVICES (BUSINESS LOGIC)**

**Nhiệm vụ**: Các microservices theo Clean Architecture + DDD + CQRS

**Chỉ mục thành phần**:
- `user-service/`: Quản lý user (NestJS + TypeScript)
- `ai-service/`: AI/ML processing (FastAPI + Python)
- `analytics-service/`: Phân tích dữ liệu (FastAPI + Python)
- `performance-service/`: APIs hiệu năng cao (Go)
- `notification-service/`: Thông báo real-time (WebSocket)
- `file-service/`: Quản lý file (Go)
- `_template/`: Template cho service mới

**Cấu trúc Clean Architecture cho mỗi service**:
```
src/
├── domain/           # Domain Layer (Core Business Logic)
│   ├── entities/     # Business entities với behavior
│   ├── value-objects/# Immutable value objects
│   ├── events/       # Domain events
│   └── repositories/ # Repository interfaces
├── application/      # Application Layer (Use Cases)
│   ├── commands/     # Command handlers (CQRS Write)
│   ├── queries/      # Query handlers (CQRS Read)
│   ├── services/     # Application services
│   └── dtos/         # Data transfer objects
├── infrastructure/   # Infrastructure Layer (External)
│   ├── database/     # Database implementations
│   ├── external/     # External service clients
│   └── messaging/    # Message broker implementations
└── interface/        # Interface Layer (API/Controllers)
    ├── controllers/  # REST API controllers
    ├── middleware/   # Request/response middleware
    └── validators/   # Input validation
```

### [object Object]LIBS/ - THƯ VIỆN CHIA SẺ**

**Nhiệm vụ**: Shared libraries và utilities được sử dụng chung

**Chỉ mục thành phần**:
- `shared-types/`: TypeScript interfaces và types chung
- `domain-models/`: DDD Entities & Value Objects
- `algorithms/`: Data Structures & Algorithms implementations
- `security/`: Security utilities & middleware
- `database/`: Database abstractions & utilities
- `testing/`: Testing utilities & frameworks

### [object Object]LATES/ - CODE TEMPLATES & GENERATORS**

**Nhiệm vụ**: Templates để tạo code nhanh và đồng nhất

**Chỉ mục thành phần**:
- `service/`: Templates cho các loại service (NestJS, FastAPI, Go)
- `component/`: Templates cho UI components
- `infrastructure/`: Templates cho K8s, Docker configs
- `testing/`: Templates cho các loại test
- `documentation/`: Templates cho tài liệu

### [object Object]. EXAMPLES/ - VÍ DỤ IMPLEMENTATION**

**Nhiệm vụ**: Ví dụ thực tế cho patterns và best practices

**Chỉ mục thành phần**:
- `complete-features/`: Features hoàn chỉnh end-to-end
- `patterns/`: Design patterns implementations
- `integrations/`: Third-party integrations
- `best-practices/`: Best practices implementations

### **☁️ 7. INFRASTRUCTURE/ - INFRASTRUCTURE AS CODE**

**Nhiệm vụ**: Quản lý infrastructure bằng code

**Chỉ mục thành phần**:
- `kubernetes/`: K8s manifests & Helm charts
- `terraform/`: Multi-cloud infrastructure
- `docker/`: Container configurations
- `monitoring/`: Observability stack setup

### **💾 8. DATA/ - QUẢN LÝ DỮ LIỆU**

**Nhiệm vụ**: Quản lý schemas, migrations, seeds và backups

**Chỉ mục thành phần**:
- `databases/`: Database schemas cho PostgreSQL, MongoDB, Redis
- `seeds/`: Test data và production initial data
- `backups/`: Backup strategies và automation

### **🔬 9. TESTS/ - COMPREHENSIVE TESTING**

**Nhiệm vụ**: Testing strategy hoàn chỉnh theo Testing Pyramid

**Chỉ mục thành phần**:
- `unit/`: Unit tests (90%+ coverage)
- `integration/`: Integration tests (80%+ coverage)
- `e2e/`: End-to-end tests (70%+ coverage)
- `performance/`: Load & stress testing

### *[object Object]0. TOOLS/ - DEVELOPMENT TOOLS**

**Nhiệm vụ**: Tools hỗ trợ development workflow

**Chỉ mục thành phần**:
- `scripts/`: Build, deploy, maintenance scripts
- `generators/`: Code generators và scaffolding
- `linters/`: Linting configurations

### **⚙️ 11. SCRIPTS/ - AUTOMATION SCRIPTS**

**Nhiệm vụ**: Automation cho setup, deployment, maintenance

**Chỉ mục thành phần**:
- `setup/`: Environment setup automation
- `deployment/`: Deployment automation
- `maintenance/`: Maintenance và cleanup
- `monitoring/`: Health checks và monitoring

###[object Object]12. MONITORING/ - OBSERVABILITY STACK**

**Nhiệm vụ**: Complete observability với metrics, logs, traces

**Chỉ mục thành phần**:
- `grafana/`: Dashboards và visualizations
- `prometheus/`: Metrics collection và alerting
- `jaeger/`: Distributed tracing
- `elk/`: Centralized logging stack

### **🌐 13. DEPLOYMENT/ - DEPLOYMENT CONFIGURATIONS**

**Nhiệm vụ**: Deployment configs cho các environments

**Chỉ mục thành phần**:
- `environments/`: Dev, staging, production configs
- `ci-cd/`: CI/CD pipelines cho các platforms
- `secrets/`: Secret management strategies

## [object Object]ẮC TỔ CHỨC THEO TÀI LIỆU**

### **Clean Architecture Layers**
1. **Domain Layer**: Core business logic, entities, value objects
2. **Application Layer**: Use cases, commands, queries (CQRS)
3. **Infrastructure Layer**: Database, external services, messaging
4. **Interface Layer**: Controllers, middleware, validators

### **DDD Patterns**
- **Bounded Contexts**: Mỗi service là một bounded context
- **Aggregates**: Consistency boundaries trong domain
- **Domain Events**: Loose coupling giữa các services
- **Repository Pattern**: Abstraction cho data access

### **Microservices Principles**
- **Single Responsibility**: Mỗi service có một nhiệm vụ rõ ràng
- **Independent Deployment**: Services deploy độc lập
- **Database per Service**: Mỗi service có database riêng
- **Event-Driven Communication**: Async messaging

### **AI-Native Integration**
- **Vector Embeddings**: Semantic search capabilities
- **LLM Integration**: AI-powered features
- **MLOps Pipeline**: Model serving và monitoring
- **Context Engineering**: AI context management

## 🚀 **HƯỚNG DẪN SỬ DỤNG CẤU TRÚC**

1. **Bắt đầu với docs/**: Đọc tài liệu architecture và setup
2. **Sử dụng templates/**: Tạo services/components mới
3. **Tham khảo examples/**: Học từ implementations có sẵn
4. **Follow testing strategy**: Implement tests theo pyramid
5. **Deploy theo environments**: Dev → Staging → Production
