# 📋 Coding Standards & Best Practices

> Canonical standards for code quality, style, testing, security, and performance across the enterprise platform

## Scope

- Language-specific style guides (TypeScript, Python, Go)
- Commit conventions (Conventional Commits)
- Branching strategy (Git Flow)
- Documentation standards (JSDoc, OpenAPI)
- Testing requirements and coverage targets
- Security guidelines (OWASP, secrets management)
- Performance targets and profiling practices

## Core Principles

- Readability over cleverness
- Small, composable units (functions, modules)
- SOLID principles and Clean Architecture boundaries
- Program to interfaces; depend on abstractions
- Tests first for critical paths; automate in CI
- Security by default; least privilege everywhere
- Measure, then optimize; regressions guarded by tests

## Minimal Checklists

- Code
  - [ ] Follows linter and formatter
  - [ ] Names are descriptive; no dead code
  - [ ] Public APIs documented
- Tests
  - [ ] Unit tests for business logic
  - [ ] Integration tests for boundaries
  - [ ] Coverage meets targets
- Security
  - [ ] Inputs validated/sanitized
  - [ ] No secrets in code; use env/secret store
  - [ ] AuthN/Z enforced where applicable

Use this page as the stable link target for “Standards” across the docs.

