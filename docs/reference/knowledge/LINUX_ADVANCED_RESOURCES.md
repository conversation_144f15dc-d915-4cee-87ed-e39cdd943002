# Linux Advanced Resources & Documentation Guide

## Tài liệu Ch<PERSON>h thức về Kernel & Systemd

### Linux Kernel Documentation
- **Linux Kernel Documentation (docs.kernel.org)** - Tài liệu chính thức về kernel Linux với API, tracepoints, subsystem guides
- **Understanding and administering systemd** - Hướng dẫn quản trị systemd từ Fedora
- **systemd.io** - Trang chính thức systemd với architecture documentation
- **ArchWiki systemd** - Tài liệu systemd chi tiết từ cộng đồng Arch Linux
- **Red Hat systemd guide** - Hướng dẫn systemd enterprise từ Red Hat

### System Administration Guides
- **Red Hat Enterprise Linux System Administrator's Guide** - Tài liệu quản trị hệ thống chính thức
- **RHEL Performance & Tuning guides** - Hướng dẫn tối ưu hiệu suất
- **SELinux, kdump, tuned-profiles documentation** - Tà<PERSON> liệu bảo mật và troubleshooting

### TLDP (The Linux Documentation Project)
- **Linux System Administrator Guide** - Hướng dẫn quản trị hệ thống từ TLDP
- **Linux Network Administrator Guide** - Tà<PERSON> liệu quản trị mạng Linux chi tiết
- **TLDP HOWTOs collection** - Bộ sưu tập hướng dẫn từng bước

## Sách Chuyên môn Cao cấp

### O'Reilly & Technical Books
- **Practical Linux System Administration (O'Reilly)** - Hướng dẫn thực hành quản trị Linux toàn diện
- **BPF Performance Tools - Brendan Gregg** - Sách về BPF và công cụ phân tích hiệu suất
- **Mastering Linux System Administration - Bresnahan & Blum** - Sách nâng cao về quản trị Linux

## Performance & eBPF Documentation

### Brendan Gregg's Materials
- **Linux Performance Tools slides** - Bộ slide về công cụ phân tích hiệu suất Linux
- **Linux Systems Performance presentations** - Video tutorials và methodology

### LWN.net Articles
- **LWN Kernel Index** - Bài viết kernel hàng tuần từ LWN.net
- **Linux kernel development coverage** - Phân tích chi tiết về phát triển kernel

### eBPF Learning Resources
- **eBPF Developer Tutorial** - Tutorial eBPF từ cơ bản đến nâng cao
- **Learning eBPF book (Cilium/Isovalent)** - Sách học eBPF CO-RE
- **BCC and bpftrace examples** - Ví dụ thực tế về eBPF programming

## Network Performance Tuning

### FasterData & Network Optimization
- **ESnet Network Performance Tuning** - Hướng dẫn tối ưu mạng từ ESnet
- **Linux TCP tuning guides** - Tối ưu TCP stack
- **High-speed networking documentation** - Mạng tốc độ cao

### Advanced Networking
- **Linux Advanced Routing & Traffic Control HOWTO** - Định tuyến và kiểm soát traffic nâng cao
- **Netfilter documentation** - Tài liệu về netfilter framework
- **iptables tutorials and best practices** - Hướng dẫn iptables chi tiết

## Security & Hardening

### Security Guides
- **CIS Benchmarks for Linux** - Chuẩn bảo mật từ Center for Internet Security
- **NIST Cybersecurity Framework** - Framework bảo mật từ NIST
- **OWASP Linux Security** - Hướng dẫn bảo mật Linux từ OWASP

### SELinux & AppArmor
- **SELinux User's and Administrator's Guide** - Hướng dẫn SELinux chi tiết
- **AppArmor documentation** - Tài liệu AppArmor từ Ubuntu
- **Security-Enhanced Linux tutorials** - Tutorial SELinux thực hành

## Container & Orchestration

### Docker Documentation
- **Docker Official Documentation** - Tài liệu chính thức Docker
- **Docker Best Practices** - Best practices cho Docker
- **Dockerfile reference** - Tham khảo Dockerfile chi tiết

### Kubernetes Resources
- **Kubernetes Official Documentation** - Tài liệu chính thức Kubernetes
- **Kubernetes the Hard Way** - Hướng dẫn setup Kubernetes từ đầu
- **Kubernetes Patterns book** - Patterns và best practices

## Monitoring & Observability

### Prometheus & Grafana
- **Prometheus Documentation** - Tài liệu chính thức Prometheus
- **Grafana Documentation** - Hướng dẫn Grafana chi tiết
- **Monitoring with Prometheus book** - Sách về monitoring với Prometheus

### Logging & Tracing
- **ELK Stack Documentation** - Elasticsearch, Logstash, Kibana
- **Jaeger Tracing Documentation** - Distributed tracing với Jaeger
- **OpenTelemetry Documentation** - Observability framework

## Development Tools & IDEs

### Text Editors & IDEs
- **Vim/Neovim Documentation** - Tài liệu Vim chi tiết
- **Emacs Manual** - Hướng dẫn Emacs toàn diện
- **VS Code Linux Development** - Phát triển trên Linux với VS Code

### Build Systems & Package Management
- **Make Manual** - Hướng dẫn GNU Make
- **CMake Documentation** - Build system CMake
- **Package management guides** - APT, YUM, DNF, Pacman

## Cloud & Infrastructure

### Infrastructure as Code
- **Terraform Documentation** - Infrastructure as Code với Terraform
- **Ansible Documentation** - Configuration management với Ansible
- **Puppet Documentation** - Configuration management với Puppet

### Cloud Platforms
- **AWS Linux Documentation** - Linux trên AWS
- **Google Cloud Linux Guides** - Linux trên Google Cloud
- **Azure Linux Documentation** - Linux trên Microsoft Azure

## Specialized Topics

### Real-Time Systems
- **Real-Time Linux Documentation** - RT-PREEMPT và real-time capabilities
- **RTOS alternatives** - Real-time operating systems

### Embedded Linux
- **Embedded Linux Development** - Phát triển Linux embedded
- **Yocto Project Documentation** - Build custom Linux distributions
- **Buildroot Documentation** - Simple embedded Linux build system

### High-Performance Computing
- **Linux HPC Documentation** - High-performance computing trên Linux
- **SLURM Documentation** - Workload manager cho HPC
- **OpenMPI Documentation** - Message passing interface

## Community Resources

### Forums & Communities
- **Linux Questions Forum** - Cộng đồng hỏi đáp Linux
- **Stack Overflow Linux Tags** - Q&A về Linux programming
- **Reddit r/linux** - Cộng đồng Linux trên Reddit
- **Linux Foundation Training** - Khóa học chính thức

### Blogs & News
- **LWN.net** - Linux Weekly News
- **Phoronix** - Linux hardware và performance news
- **DistroWatch** - Tin tức về các Linux distributions
- **Planet Linux** - Tập hợp blog từ Linux developers

### Certification Paths
- **Linux Professional Institute (LPI)** - LPIC-1, LPIC-2, LPIC-3
- **Red Hat Certifications** - RHCSA, RHCE, RHCA
- **CompTIA Linux+** - Vendor-neutral Linux certification
- **SUSE Certifications** - SUSE Linux Enterprise certifications

## Learning Methodology

### Structured Learning Path
1. **Foundation**: Basic commands, file system, permissions
2. **Intermediate**: Shell scripting, process management, networking
3. **Advanced**: Kernel internals, performance tuning, security
4. **Expert**: Custom kernel modules, embedded systems, HPC

### Hands-on Practice
- **Virtual Labs**: VirtualBox, VMware, QEMU
- **Cloud Labs**: AWS EC2, Google Compute Engine, Azure VMs
- **Container Labs**: Docker, Podman, LXC
- **Raspberry Pi**: Physical hardware for embedded learning

### Project-Based Learning
- **Home Lab Setup**: Build personal Linux infrastructure
- **Open Source Contribution**: Contribute to Linux projects
- **Certification Projects**: Prepare for Linux certifications
- **Real-World Scenarios**: Simulate production environments

## New Advanced Topics from freeCodeCamp

### Advanced Text Editing Mastery

#### Vim Advanced Features
- **Multiple Windows**: Split, vsplit, window navigation
- **Macros**: Record and replay complex editing sequences
- **Registers**: Named registers for advanced copy/paste
- **Plugins**: Vim-plug, pathogen, vundle for extensibility
- **Customization**: .vimrc configuration, syntax highlighting

#### Nano Advanced Usage
- **Line Numbers**: `nano -l filename` for line numbers
- **Search and Replace**: Advanced search patterns
- **Multiple Files**: Edit multiple files simultaneously
- **Custom Key Bindings**: Personalize keyboard shortcuts

### Advanced File System Operations

#### Find Command Mastery
```bash
# Advanced find patterns
find /path -regex ".*\.(log|txt)$"     # Regex pattern matching
find /path -executable -type f          # Find executable files
find /path -user username               # Find files by owner
find /path -group groupname             # Find files by group
find /path -perm 644                    # Find files by permissions
find /path -newer reference_file        # Find files newer than reference
find /path -older reference_file        # Find files older than reference

# Complex find operations
find /path -type f -size +100M -exec ls -lh {} \;  # List large files
find /path -name "*.tmp" -mtime +7 -delete         # Delete old temp files
find /path -type f -exec grep -l "pattern" {} \;   # Find files containing pattern
```

#### Advanced File Manipulation
```bash
# Advanced diff options
diff -r dir1 dir2                      # Compare directories recursively
diff -Naur old_file new_file           # Create patch file
diff -w file1 file2                    # Ignore whitespace differences

# Advanced file processing
split -l 1000 large_file prefix        # Split large file into chunks
csplit input_file '/pattern/' '{*}'    # Split file on pattern
join file1 file2                        # Join files on common field
paste file1 file2                       # Merge files side by side
```

### Advanced Process Management

#### Process Monitoring Tools
```bash
# Advanced top usage
top -p PID1,PID2,PID3                 # Monitor specific processes
top -u username                        # Monitor user processes
top -d 1                              # Update every second

# Process tree analysis
pstree -a                             # Show command arguments
pstree -h                             # Highlight current process
pstree -T                             # Show thread names

# Advanced ps options
ps -eo pid,ppid,cmd,etime             # Custom output format
ps -p PID -o pid,ppid,state,time      # Process state and time
ps aux --sort=-%cpu                   # Sort by CPU usage
ps aux --sort=-%mem                   # Sort by memory usage
```

#### Process Control Advanced
```bash
# Signal handling
trap 'cleanup_function' EXIT           # Trap exit signal
trap 'echo "Interrupted"' INT          # Trap interrupt signal

# Process groups
set -m                                 # Enable job control
fg %1                                  # Bring job 1 to foreground
bg %1                                  # Send job 1 to background

# Advanced job control
disown %1                              # Remove job from shell's job list
wait %1                                # Wait for job completion
jobs -l                                # List jobs with PIDs
```

### Advanced Networking

#### Network Troubleshooting
```bash
# Advanced ping options
ping -i 0.2 -s 1500 host              # Custom interval and packet size
ping -c 100 -W 1 host                 # 100 packets with 1s timeout
ping -D host                           # Timestamp each packet

# Advanced traceroute
traceroute -n -w 1 host                # No DNS resolution, 1s timeout
traceroute -I host                     # Use ICMP instead of UDP
traceroute -T -p 80 host              # TCP traceroute on port 80

# Network interface bonding
# /etc/network/interfaces
auto bond0
iface bond0 inet static
    address *************
    netmask *************
    bond-slaves eth0 eth1
    bond-mode 802.3ad
    bond-miimon 100
```

#### Network Performance Tuning
```bash
# TCP tuning parameters
echo 'net.ipv4.tcp_congestion_control = bbr' >> /etc/sysctl.conf
echo 'net.core.default_qdisc = fq' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_fastopen = 3' >> /etc/sysctl.conf

# Network buffer tuning
echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 16777216' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 16777216' >> /etc/sysctl.conf

# Apply changes
sysctl -p
```

### Advanced Log Analysis

#### Log Processing Tools
```bash
# Advanced grep patterns
grep -P 'pattern'                      # Perl-compatible regex
grep -A 5 -B 5 'pattern'              # Show 5 lines before and after
grep -v 'pattern'                      # Exclude lines matching pattern
grep -f patterns_file                  # Read patterns from file

# Advanced awk usage
awk 'NR==1,NR==10' file               # Print lines 1-10
awk '$1 > 100 && $2 < 50' file        # Complex conditions
awk '{sum+=$1} END {print sum}' file   # Sum first column
awk 'BEGIN {FS=":"} {print $1}' file   # Custom field separator

# Advanced sed usage
sed '1,5d' file                        # Delete lines 1-5
sed 's/old/new/g' file                 # Global substitution
sed '/pattern/d' file                   # Delete lines matching pattern
sed '1i\new line' file                 # Insert before line 1
```

#### Log Analysis Scripts
```bash
#!/bin/bash
# Advanced log analyzer
LOG_FILE="/var/log/application.log"
ERROR_PATTERN="ERROR|CRITICAL|FATAL"

echo "=== Log Analysis Report ==="
echo "Date: $(date)"
echo "Log file: $LOG_FILE"
echo

# Count error types
echo "Error Summary:"
grep -E "$ERROR_PATTERN" "$LOG_FILE" | \
    awk '{print $4}' | sort | uniq -c | sort -nr

# Top error sources
echo -e "\nTop Error Sources:"
grep -E "$ERROR_PATTERN" "$LOG_FILE" | \
    awk '{print $1, $2}' | sort | uniq -c | sort -nr | head -10

# Error timeline
echo -e "\nError Timeline (last 24 hours):"
grep -E "$ERROR_PATTERN" "$LOG_FILE" | \
    grep "$(date -d '1 day ago' '+%Y-%m-%d')" | \
    awk '{print $1, $2}' | sort | uniq -c
```

### Advanced Automation

#### Systemd Timers vs Cron
```bash
# Systemd timer example
# /etc/systemd/system/backup.timer
[Unit]
Description=Daily backup at 2 AM
Requires=backup.service

[Timer]
OnCalendar=*-*-* 02:00:00
RandomizedDelaySec=300
Persistent=true

[Install]
WantedBy=timers.target

# Enable and manage
systemctl enable backup.timer
systemctl start backup.timer
systemctl list-timers --all
```

#### Advanced Cron Patterns
```bash
# Complex cron schedules
0 2 * * 1-5 /usr/bin/daily_backup.sh    # Weekdays at 2 AM
0 9,17 * * 1-5 /usr/bin/check.sh       # 9 AM and 5 PM weekdays
0 0 1,15 * * /usr/bin/monthly.sh        # 1st and 15th of month
0 12 * * 0 /usr/bin/weekly.sh           # Noon on Sundays
0 0 1 1 * /usr/bin/yearly.sh            # New Year at midnight

# Special cron strings
@reboot    /usr/bin/startup.sh
@yearly    /usr/bin/yearly.sh
@annually  /usr/bin/yearly.sh
@monthly   /usr/bin/monthly.sh
@weekly    /usr/bin/weekly.sh
@daily     /usr/bin/daily.sh
@hourly    /usr/bin/hourly.sh
```

### Advanced Security Hardening

#### SELinux Advanced Configuration
```bash
# SELinux context management
semanage fcontext -a -t httpd_exec_t "/var/www/cgi-bin(/.*)?"
restorecon -R -v /var/www/cgi-bin

# SELinux boolean settings
setsebool -P httpd_can_network_connect on
setsebool -P httpd_can_network_relay on
setsebool -P httpd_exec_mem on

# SELinux policy customization
audit2allow -a -M my_policy
semodule -i my_policy.pp
```

#### Advanced Firewall Configuration
```bash
# iptables advanced rules
# Rate limiting
iptables -A INPUT -p tcp --dport 22 -m limit --limit 3/minute --limit-burst 5 -j ACCEPT

# Connection tracking
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# Custom chains
iptables -N LOGGING
iptables -A INPUT -j LOGGING
iptables -A LOGGING -j LOG --log-prefix "IPTables-Log: "
iptables -A LOGGING -j DROP

# Save and restore
iptables-save > /etc/iptables/rules.v4
iptables-restore < /etc/iptables/rules.v4
```

## Performance Tuning Advanced

### Kernel Parameter Optimization
```bash
# Memory management tuning
echo 'vm.swappiness = 1' >> /etc/sysctl.conf
echo 'vm.dirty_ratio = 15' >> /etc/sysctl.conf
echo 'vm.dirty_background_ratio = 5' >> /etc/sysctl.conf
echo 'vm.overcommit_memory = 1' >> /etc/sysctl.conf

# File system tuning
echo 'fs.file-max = 2097152' >> /etc/sysctl.conf
echo 'fs.inotify.max_user_watches = 524288' >> /etc/sysctl.conf

# Network tuning
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_congestion_control = bbr' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_fastopen = 3' >> /etc/sysctl.conf

# Apply changes
sysctl -p
```

### Tuned Profiles Advanced
```bash
# Create custom tuned profile
mkdir -p /etc/tuned/custom-profile
cat > /etc/tuned/custom-profile/tuned.conf << EOF
[main]
summary=Custom performance profile

[cpu]
governor=performance
energy_perf_bias=performance

[vm]
transparent_hugepages=always
swappiness=1

[disk]
readahead=4096

[sysctl]
vm.swappiness=1
vm.dirty_ratio=15
vm.dirty_background_ratio=5
net.core.somaxconn=65535
EOF

# Apply custom profile
tuned-adm profile custom-profile
```

## Container & Virtualization Advanced

### Docker Advanced Usage
```dockerfile
# Multi-stage build with security
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm audit fix

FROM node:18-alpine AS production
WORKDIR /app

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy application
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .

# Security: Run as non-root
USER nextjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Security: Read-only root filesystem
VOLUME ["/tmp", "/var/tmp"]
EXPOSE 3000
CMD ["npm", "start"]
```

### Advanced Container Management
```bash
# Container resource limits
docker run -d \
  --name myapp \
  --memory=512m \
  --cpus=1.0 \
  --pids-limit=100 \
  --security-opt=no-new-privileges \
  myapp:latest

# Container networking
docker network create --driver bridge --subnet=**********/16 mynetwork
docker run --network mynetwork --ip *********** myapp

# Container security scanning
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image myapp:latest
```

## Monitoring & Observability Advanced

### Advanced Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'linux-nodes'
    static_configs:
      - targets: ['localhost:9100']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        regex: '([^:]+)(?::\d+)?'
        replacement: '${1}'
```

### Advanced Log Aggregation
```bash
# rsyslog advanced configuration
# /etc/rsyslog.conf
module(load="imfile")
module(load="omelasticsearch")

# File monitoring
input(type="imfile"
      File="/var/log/application.log"
      Tag="app"
      Severity="info"
      Facility="local0")

# Elasticsearch output
action(type="omelasticsearch"
       server="elasticsearch"
       serverport="9200"
       template="plain"
       searchIndex="logs")
```

## Kết luận

Mastering Linux requires combining theoretical knowledge với hands-on practice, continuous learning, và active participation trong Linux community. The resources listed provide comprehensive coverage từ beginner đến expert level.

### Advanced Skills Development Path:
1. **Text Editor Mastery**: Vim/Nano advanced features
2. **File System Expertise**: Advanced find, diff, and manipulation
3. **Process Management**: Advanced monitoring and control
4. **Network Mastery**: Advanced diagnostics and tuning
5. **Security Hardening**: SELinux, AppArmor, advanced firewall
6. **Performance Tuning**: Kernel parameters, tuned profiles
7. **Automation**: Advanced cron, systemd timers
8. **Container Management**: Docker advanced features
9. **Monitoring**: Prometheus, advanced log analysis
10. **Troubleshooting**: Advanced debugging and diagnostics

---

**Tài liệu liên quan:**
- [Linux Complete Guide](LINUX_COMPLETE_GUIDE.md) - Hướng dẫn toàn diện từ cơ bản đến nâng cao
- [Linux Mastery Philosophy](LINUX_MASTERY_PHILOSOPHY.md) - Triết lý và nguyên lý cốt lõi
- [Command Line Mastery](advanced/system-admin/linux-cli.md) - Thành thạo command line
