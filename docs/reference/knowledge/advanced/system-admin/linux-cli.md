# Command Line Mastery - Complete Guide

## 📋 Mục lục

1. [Tổng quan](#tổng-quan)
2. [<PERSON><PERSON> bản về Command Line](#cơ-bản-về-command-line)
3. [Navigation & File Management](#navigation--file-management)
4. [Text Processing & Manipulation](#text-processing--manipulation)
5. [Process Management](#process-management)
6. [Network Operations](#network-operations)
7. [System Information & Monitoring](#system-information--monitoring)
8. [Package Management](#package-management)
9. [User & Permission Management](#user--permission-management)
10. [Shell Scripting](#shell-scripting)
11. [Advanced Techniques](#advanced-techniques)
12. [Troubleshooting & Debugging](#troubleshooting--debugging)
13. [Best Practices & Security](#best-practices--security)
14. [New Advanced Topics](#new-advanced-topics)

---

## Tổng quan

### Command Line là gì?
Command Line Interface (CLI) là giao diện dòng lệnh cho phép người dùng tương tác với hệ thống máy tính thông qua các lệnh văn bản. CLI cung cấp sức mạnh và tính linh hoạt cao hơn so với giao diện đồ họa (GUI).

### Tại sao học Command Line?
- **Hiệu quả**: Thực hiện tác vụ nhanh hơn GUI
- **Automation**: Tự động hóa các tác vụ lặp lại
- **Remote access**: Quản lý hệ thống từ xa
- **Resource efficiency**: Sử dụng ít tài nguyên hơn
- **Power**: Truy cập vào tất cả tính năng hệ thống

### Shell là gì?
Shell là chương trình thông dịch lệnh, cung cấp giao diện giữa người dùng và kernel. Các shell phổ biến:
- **Bash**: Bourne Again Shell (mặc định trên Linux)
- **Zsh**: Z Shell (cải tiến từ Bash)
- **Fish**: Friendly Interactive Shell
- **PowerShell**: Shell của Microsoft

---

## Cơ bản về Command Line

### Mở Terminal
```bash
# Trên Linux
Ctrl + Alt + T
# hoặc
gnome-terminal
konsole
xterm

# Trên macOS
Cmd + Space, gõ "Terminal"
# hoặc
Applications > Utilities > Terminal

# Trên Windows
Win + R, gõ "cmd"
# hoặc PowerShell
Win + X > Windows PowerShell
```

### Cấu trúc lệnh cơ bản
```bash
command [options] [arguments]
```

**Ví dụ:**
```bash
ls -la /home/<USER>
# ls: command
# -la: options (flags)
# /home/<USER>
```

### Các ký tự đặc biệt
```bash
# ~ : Home directory
cd ~

# . : Current directory
ls .

# .. : Parent directory
cd ..

# * : Wildcard (match any characters)
ls *.txt

# ? : Single character wildcard
ls file?.txt

# | : Pipe (output của lệnh trước làm input cho lệnh sau)
ls | grep "pattern"

# > : Redirect output to file
ls > file_list.txt

# >> : Append output to file
echo "new line" >> file_list.txt

# < : Redirect input from file
cat < input.txt

# & : Run command in background
long_running_command &

# ; : Separate commands
command1; command2

# && : Run second command only if first succeeds
command1 && command2

# || : Run second command only if first fails
command1 || command2
```

### Help và Documentation
```bash
# Built-in help
command --help
command -h

# Manual pages
man command
man -k keyword

# Info pages
info command

# Whatis (brief description)
whatis command

# Type (show command type)
type command
```

---

## Navigation & File Management

### Navigation Commands
```bash
# Change directory
cd /path/to/directory
cd ~/Documents
cd -

# Print working directory
pwd

# List directory contents
ls
ls -la          # All files with details
ls -lh          # Human-readable sizes
ls -lt          # Sort by time
ls -ltr         # Sort by time (reverse)
ls -R           # Recursive listing
ls -d */        # Only directories
ls *.txt        # Only .txt files
```

### File Operations
```bash
# Copy files/directories
cp source destination
cp -r source_dir dest_dir    # Recursive copy
cp -v file1 file2            # Verbose output
cp -i file1 file2            # Interactive (ask before overwrite)

# Move/rename files
mv old_name new_name
mv file1 file2 directory/
mv -i file1 file2            # Interactive

# Remove files/directories
rm filename
rm -r directory              # Recursive remove
rm -f filename               # Force (no confirmation)
rm -i filename               # Interactive
rm -rf directory             # Dangerous! Remove everything

# Create files/directories
touch filename               # Create empty file
mkdir directory_name
mkdir -p parent/child        # Create parent directories
```

### File Viewing
```bash
# View file contents
cat filename                 # Display entire file
less filename               # Page through file
more filename               # Page through file (older)
head filename               # First 10 lines
head -n 20 filename         # First 20 lines
tail filename               # Last 10 lines
tail -n 20 filename         # Last 20 lines
tail -f filename            # Follow file changes (real-time)

# View file type
file filename
file -i filename            # MIME type

# View file size
wc filename                 # Word, line, character count
wc -l filename             # Line count only
wc -w filename             # Word count only
wc -c filename             # Character count only
du -h filename             # Disk usage
```

### File Search
```bash
# Find files by name
find /path -name "filename"
find /path -name "*.txt"
find /path -iname "filename"    # Case insensitive

# Find files by type
find /path -type f              # Files only
find /path -type d              # Directories only
find /path -type l              # Symbolic links only

# Find files by size
find /path -size +100M          # Larger than 100MB
find /path -size -1G            # Smaller than 1GB

# Find files by modification time
find /path -mtime -7            # Modified in last 7 days
find /path -mtime +30           # Modified more than 30 days ago

# Find files by permissions
find /path -perm 644
find /path -perm -u+w           # User writable

# Find and execute command
find /path -name "*.tmp" -exec rm {} \;
find /path -name "*.log" -exec cp {} /backup/ \;

# Locate (faster than find, uses database)
locate filename
updatedb                        # Update locate database
```

---

## Text Processing & Manipulation

### Text Viewing & Searching
```bash
# Search for patterns in files
grep "pattern" filename
grep -i "pattern" filename      # Case insensitive
grep -v "pattern" filename      # Invert match
grep -n "pattern" filename      # Show line numbers
grep -r "pattern" directory     # Recursive search
grep -l "pattern" *.txt         # Show only filenames
grep -c "pattern" filename      # Count matches

# Extended regular expressions
grep -E "pattern1|pattern2" filename
egrep "pattern1|pattern2" filename

# Search in multiple files
grep "pattern" file1 file2 file3
grep "pattern" *.txt

# Search excluding certain files
grep -r "pattern" . --exclude="*.log"
grep -r "pattern" . --exclude-dir="node_modules"
```

### Text Editing & Manipulation
```bash
# Stream editor
sed 's/old/new/g' filename     # Replace text
sed 's/old/new/' filename      # Replace first occurrence
sed '5d' filename              # Delete line 5
sed '5,10d' filename           # Delete lines 5-10
sed '5i\new line' filename     # Insert before line 5
sed '5a\new line' filename     # Append after line 5

# Text processing with awk
awk '{print $1}' filename      # Print first field
awk '{print $1, $3}' filename  # Print fields 1 and 3
awk '$1 > 100' filename        # Print lines where field 1 > 100
awk 'length > 80' filename     # Print lines longer than 80 chars
awk 'NR==1,NR==10' filename    # Print lines 1-10

# Sort and unique
sort filename                  # Sort alphabetically
sort -n filename              # Sort numerically
sort -r filename              # Sort in reverse order
sort -k2 filename             # Sort by field 2
sort filename | uniq          # Remove duplicates
sort filename | uniq -c       # Count duplicates
sort filename | uniq -d       # Show only duplicates

# Cut and paste
cut -d: -f1 filename          # Cut field 1 (colon-separated)
cut -c1-10 filename           # Cut characters 1-10
paste file1 file2             # Merge files side by side
```

### Text Comparison
```bash
# Compare files
diff file1 file2               # Show differences
diff -u file1 file2            # Unified format
diff -r dir1 dir2              # Compare directories

# Compare sorted files
comm file1 file2               # Common lines, unique lines
comm -12 file1 file2           # Show only common lines
comm -23 file1 file2           # Show lines only in file1

# Patch files
patch original_file < patch_file
```

---

## Process Management

### Process Information
```bash
# List processes
ps                              # Current shell processes
ps aux                          # All processes with details
ps auxf                         # Process tree
ps -ef                          # All processes (BSD style)
ps -p PID                       # Specific process

# Process tree
pstree
pstree -p                       # Show PIDs
pstree -u                       # Show users

# Real-time process monitoring
top
htop                           # Interactive top (if installed)
iotop                          # I/O monitoring
```

### Process Control
```bash
# Kill processes
kill PID                        # Send TERM signal
kill -9 PID                    # Send KILL signal
kill -HUP PID                  # Send HUP signal
killall process_name           # Kill all processes by name
pkill process_name             # Kill processes by name
kill %job_number               # Kill background job

# Background and foreground
command &                      # Run in background
bg                             # Resume background job
fg                             # Bring job to foreground
jobs                           # List background jobs
nohup command &                # Run command immune to hangups

# Process priority
nice -n 10 command             # Run with lower priority
renice 10 PID                  # Change priority of running process
```

### System Services
```bash
# Systemd service management
systemctl start service_name
systemctl stop service_name
systemctl restart service_name
systemctl status service_name
systemctl enable service_name
systemctl disable service_name
systemctl list-units --type=service

# Service information
systemctl show service_name
systemctl list-dependencies service_name
```

---

## Network Operations

### Network Information
```bash
# Network interfaces
ip addr show                   # Show all interfaces
ip link show                   # Show interface status
ifconfig                       # Legacy interface info
iwconfig                       # Wireless interface info

# Network connections
ss -tuln                       # Show listening ports
netstat -tuln                  # Legacy way to show ports
lsof -i                        # Show processes using network
lsof -i :80                    # Show processes using port 80

# Network routing
ip route show                  # Show routing table
route -n                       # Legacy routing table
traceroute destination         # Trace route to destination
mtr destination                # Continuous traceroute
```

### Network Testing
```bash
# Connectivity testing
ping destination               # Test connectivity
ping -c 4 destination          # Send 4 packets
ping -i 0.2 destination        # Send packet every 0.2 seconds

# Port testing
telnet host port               # Test TCP connection
nc -zv host port               # Test TCP connection
nc -zu host port               # Test UDP connection

# HTTP testing
curl -I url                    # HTTP headers only
curl -v url                    # Verbose output
wget url                       # Download file
wget -c url                    # Continue interrupted download
```

### Network File Transfer
```bash
# Secure copy
scp source user@host:destination
scp user@host:source destination
scp -r directory user@host:destination

# Secure file transfer
sftp user@host
# sftp commands:
# get filename
# put filename
# ls
# cd directory
# quit

# Rsync (efficient file transfer)
rsync -av source/ destination/
rsync -avz source/ user@host:destination/
rsync -av --delete source/ destination/
```

---

## System Information & Monitoring

### System Information
```bash
# Hardware information
lscpu                          # CPU information
lshw                           # Hardware information
lsblk                          # Block devices
lsusb                          # USB devices
lspci                          # PCI devices

# System status
uptime                         # System uptime and load
w                              # Who is logged in
who                            # Current users
last                           # Login history
free -h                        # Memory usage
df -h                          # Disk usage
du -sh directory               # Directory size
```

### System Monitoring
```bash# Performance monitoring
vmstat 1                       # Virtual memory stats every second
iostat -x 1                    # I/O stats every second
sar -u 1 10                    # CPU stats every second for 10 times
dstat -tcm                     # Combined system stats

# Log monitoring
tail -f /var/log/syslog        # Follow system log
journalctl -f                  # Follow systemd journal
journalctl -u service_name     # Service-specific logs
logwatch                       # Log summary (if installed)

# System health
systemctl status               # Overall system status
systemctl --failed             # Failed services
dmesg                          # Kernel messages
dmesg | tail -20               # Last 20 kernel messages
```

---

## Package Management

### Debian/Ubuntu (apt)
```bash
# Update package lists
sudo apt update

# Upgrade packages
sudo apt upgrade
sudo apt dist-upgrade          # Distribution upgrade

# Install packages
sudo apt install package_name
sudo apt install package1 package2

# Remove packages
sudo apt remove package_name
sudo apt purge package_name    # Remove configuration files
sudo apt autoremove            # Remove unused packages

# Search packages
apt search keyword
apt show package_name

# List installed packages
apt list --installed
dpkg -l | grep package_name
```

### Red Hat/CentOS (dnf/yum)
```bash
# Update system
sudo dnf update
sudo yum update                # Older systems

# Install packages
sudo dnf install package_name
sudo yum install package_name

# Remove packages
sudo dnf remove package_name
sudo yum remove package_name

# Search packages
dnf search keyword
yum search keyword

# List installed packages
dnf list installed
rpm -qa | grep package_name
```

### Arch Linux (pacman)
```bash
# Update system
sudo pacman -Syu

# Install packages
sudo pacman -S package_name

# Remove packages
sudo pacman -R package_name
sudo pacman -Rs package_name   # Remove dependencies

# Search packages
pacman -Ss keyword

# List installed packages
pacman -Q
pacman -Q | grep package_name
```

---

## User & Permission Management

### User Management
```bash
# User information
whoami                         # Current user
id                            # User and group IDs
groups                        # User's groups
passwd                        # Change password

# Create/modify users
sudo useradd username
sudo usermod -aG group_name username
sudo userdel username
sudo usermod -s /bin/bash username

# Group management
sudo groupadd group_name
sudo groupdel group_name
sudo gpasswd -a username group_name
sudo gpasswd -d username group_name
```

### Permission Management
```bash
# File permissions
ls -la                        # Show permissions
chmod 644 filename            # Set permissions (octal)
chmod u+rw filename           # Add read/write for user
chmod g-w filename            # Remove write for group
chmod o-r filename            # Remove read for others

# Permission examples
chmod 755 directory           # rwxr-xr-x (directory)
chmod 644 file                # rw-r--r-- (file)
chmod 600 file                # rw------- (private file)
chmod 700 directory           # rwx------ (private directory)

# Ownership
chown user:group filename
chown user filename
chgrp group filename

# Default permissions
umask                          # Show current umask
umask 022                     # Set umask
umask 077                     # Restrictive umask
```

### Sudo Configuration
```bash
# Edit sudoers file
sudo visudo                    # Safe way to edit sudoers
sudo EDITOR=nano visudo       # Use nano editor

# Sudoers examples
username ALL=(ALL:ALL) ALL    # Full sudo access
username ALL=(ALL:ALL) NOPASSWD: ALL  # No password required
%sudo ALL=(ALL:ALL) ALL       # Group sudo access
username ALL=(ALL:ALL) /usr/bin/apt   # Limited sudo access
```

---

## Shell Scripting

### Basic Script Structure
```bash
#!/bin/bash
# Script description
# Author: Your Name
# Date: YYYY-MM-DD

# Strict mode
set -euo pipefail

# Variables
SCRIPT_NAME=$(basename "$0")
VERSION="1.0.0"

# Functions
usage() {
    echo "Usage: $SCRIPT_NAME [OPTIONS]"
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -v, --version  Show version information"
}

# Main logic
main() {
    local arg="$1"
    
    case "$arg" in
        -h|--help)
            usage
            ;;
        -v|--version)
            echo "Version: $VERSION"
            ;;
        *)
            echo "Processing: $arg"
            ;;
    esac
}

# Check if script is sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

### Variables and Parameters
```bash
#!/bin/bash

# Variable assignment
NAME="John"
AGE=25
FILES=("file1.txt" "file2.txt")

# Command substitution
CURRENT_DIR=$(pwd)
DATE=$(date +%Y-%m-%d)
USER_COUNT=$(who | wc -l)

# Parameter expansion
echo "Script name: $0"
echo "First argument: $1"
echo "All arguments: $@"
echo "Argument count: $#"

# Default values
FILE="${1:-default.txt}"
PORT="${2:-8080}"

# String operations
STRING="Hello World"
echo "${STRING:0:5}"           # Hello
echo "${STRING#Hello }"        # World
echo "${STRING% World}"        # Hello
```

### Control Structures
```bash
#!/bin/bash

# If statements
if [[ -f "$1" ]]; then
    echo "File exists"
elif [[ -d "$1" ]]; then
    echo "Directory exists"
else
    echo "Neither file nor directory"
fi

# Case statements
case "$1" in
    start)
        echo "Starting service"
        ;;
    stop)
        echo "Stopping service"
        ;;
    restart)
        echo "Restarting service"
        ;;
    *)
        echo "Usage: $0 {start|stop|restart}"
        exit 1
        ;;
esac

# Loops
for file in *.txt; do
    echo "Processing: $file"
done

while read -r line; do
    echo "Line: $line"
done < input.txt

until [[ $COUNTER -gt 5 ]]; do
    echo "Counter: $COUNTER"
    ((COUNTER++))
done
```

### Functions
```bash
#!/bin/bash

# Function definition
process_file() {
    local file="$1"
    local output="$2"
    
    if [[ ! -f "$file" ]]; then
        echo "Error: File $file not found" >&2
        return 1
    fi
    
    # Process file
    awk '{print $1}' "$file" > "$output"
    echo "Processed $file -> $output"
}

# Function with return value
get_file_size() {
    local file="$1"
    if [[ -f "$file" ]]; then
        stat -c%s "$file"
    else
        echo "0"
    fi
}

# Usage
main() {
    local input_file="${1:-/dev/stdin}"
    local output_file="${2:-/dev/stdout}"
    
    process_file "$input_file" "$output_file"
    
    local size=$(get_file_size "$output_file")
    echo "Output file size: $size bytes"
}

# Call main function
main "$@"
```

---

## Advanced Techniques

### Process Substitution
```bash
# Compare sorted files
diff <(sort file1) <(sort file2)

# Process multiple files
while IFS= read -r line; do
    echo "Processing: $line"
done < <(find . -name "*.txt")

# Combine commands
paste <(cut -d: -f1 file1) <(cut -d: -f1 file2)
```

### Here Documents
```bash
# Create configuration file
cat > config.txt << 'EOF'
Host: localhost
Port: 3306
Database: myapp
User: admin
Password: secret
EOF

# Multi-line command
mysql -u root -p << 'EOF'
USE database_name;
SELECT * FROM table_name;
EXIT;
EOF
```

### Advanced Redirection
```bash
# Redirect both stdout and stderr
command > output.log 2>&1
command &> output.log

# Append both stdout and stderr
command >> output.log 2>&1
command &>> output.log

# Discard output
command > /dev/null 2>&1
command &> /dev/null

# Tee (output to both file and terminal)
command | tee output.log
command | tee -a output.log    # Append mode
```

### Job Control
```bash
# Background jobs
long_command &
sleep 100 &

# Job management
jobs                           # List background jobs
fg %1                          # Bring job 1 to foreground
bg %1                          # Resume job 1 in background
kill %1                        # Kill job 1

# Suspend and resume
Ctrl + Z                       # Suspend current job
bg                             # Resume in background
fg                             # Resume in foreground
```

---

## Troubleshooting & Debugging

### Common Issues
```bash
# Permission denied
ls -la filename                # Check permissions
sudo chmod +x filename         # Make executable
sudo chown user:group filename # Change ownership

# Command not found
which command                  # Find command location
type command                   # Show command type
echo $PATH                     # Check PATH variable

# File not found
find / -name filename 2>/dev/null
locate filename
whereis command

# Disk space issues
df -h                          # Check disk usage
du -sh directory               # Check directory size
sudo apt autoremove            # Remove unused packages
```

### Debugging Techniques
```bash
# Verbose output
command -v                     # Verbose mode
command --verbose

# Debug mode
bash -x script.sh              # Execute with debug output
set -x                         # Enable debug mode in script
set +x                         # Disable debug mode

# Error handling
set -e                         # Exit on error
set -u                         # Exit on undefined variable
set -o pipefail                # Exit on pipe failure

# Logging
exec 1> >(tee -a "$logfile")
exec 2> >(tee -a "$logfile" >&2)
```

### Performance Analysis
```bash
# Command timing
time command                   # Measure execution time
/usr/bin/time command          # More detailed timing

# Profiling
strace -p PID                  # Trace system calls
ltrace -p PID                  # Trace library calls

# Memory analysis
valgrind --tool=memcheck program
```

---

## Best Practices & Security

### Security Best Practices
```bash
# File permissions
chmod 600 ~/.ssh/id_rsa        # Private keys
chmod 700 ~/.ssh               # SSH directory
chmod 644 ~/.ssh/id_rsa.pub    # Public keys

# Sudo usage
sudo -l                        # List allowed commands
sudo -i                        # Interactive root shell
sudo -u user command           # Run as specific user

# Password security
passwd                         # Change password regularly
chage -l username              # Check password aging
```

### Scripting Best Practices
```bash
#!/bin/bash
# Always use shebang
# Use strict mode
set -euo pipefail

# Use meaningful variable names
USER_NAME="john_doe"
CONFIG_FILE="/etc/app.conf"

# Quote variables
echo "Hello, $USER_NAME"
echo "Config: $CONFIG_FILE"

# Use local variables in functions
my_function() {
    local var="$1"
    echo "$var"
}

# Error handling
if [[ ! -f "$file" ]]; then
    echo "Error: File $file not found" >&2
    exit 1
fi

# Cleanup
cleanup() {
    echo "Cleaning up..."
    rm -f "$temp_file"
}
trap cleanup EXIT
```

### System Maintenance
```bash
# Regular updates
sudo apt update && sudo apt upgrade
sudo dnf update
sudo pacman -Syu

# Log rotation
sudo logrotate -f /etc/logrotate.conf

# Disk cleanup
sudo apt autoremove
sudo apt autoclean
sudo journalctl --vacuum-time=30d

# Security updates
sudo unattended-upgrades
sudo yum update --security
```

---

## New Advanced Topics

### Advanced Text Editing

#### Vim Mastery
```bash
# Three Vim modes
# 1. Command Mode (default) - navigation
# 2. Insert Mode - editing
# 3. Visual Mode - text selection

# Navigation with hjkl
# h: left, j: down, k: up, l: right

# Mode switching
i               # Enter insert mode
ESC             # Return to command mode
v               # Visual mode
Shift+V         # Visual line mode
Ctrl+V          # Visual block mode

# Advanced navigation
gg              # Beginning of file
G               # End of file
0               # Beginning of line
$               # End of line

# Editing commands
dd              # Delete line
yy              # Copy line
p               # Paste
x               # Delete character

# Search and replace
/pattern        # Search forward
?pattern        # Search backward
n               # Next match
N               # Previous match
:%s/old/new/g   # Replace all occurrences

# Multiple windows
:split          # Horizontal split
:vsplit         # Vertical split
Ctrl+w + h/j/k/l # Navigate between splits
```

#### Nano Advanced Usage
```bash
# Key bindings
Ctrl+O          # Save file
Ctrl+X          # Exit
Ctrl+K          # Cut line
Ctrl+U          # Paste
Ctrl+W          # Search
Alt+A           # Select text
Alt+\           # Beginning of file
Alt+/           # End of file
Alt+G           # Go to line number

# Line numbers
nano -l filename               # Show line numbers

# Multiple files
nano file1 file2               # Edit multiple files
Ctrl+X                         # Exit current file
Ctrl+R                         # Read another file
```

### Advanced File Operations

#### Find Command Mastery
```bash
# Advanced find patterns
find /path -regex ".*\.(log|txt)$"     # Regex pattern matching
find /path -executable -type f          # Find executable files
find /path -user username               # Find files by owner
find /path -group groupname             # Find files by group
find /path -perm 644                    # Find files by permissions
find /path -newer reference_file        # Find files newer than reference
find /path -older reference_file        # Find files older than reference

# Complex find operations
find /path -type f -size +100M -exec ls -lh {} \;  # List large files
find /path -name "*.tmp" -mtime +7 -delete         # Delete old temp files
find /path -type f -exec grep -l "pattern" {} \;   # Find files containing pattern

# Find with multiple conditions
find /path -type f -name "*.log" -size +10M -mtime -7
```

#### Advanced File Manipulation
```bash
# Advanced diff options
diff -r dir1 dir2                      # Compare directories recursively
diff -Naur old_file new_file           # Create patch file
diff -w file1 file2                    # Ignore whitespace differences
diff -y file1 file2                    # Side by side comparison

# Advanced file processing
split -l 1000 large_file prefix        # Split large file into chunks
csplit input_file '/pattern/' '{*}'    # Split file on pattern
join file1 file2                        # Join files on common field
paste file1 file2                       # Merge files side by side

# File statistics
wc -L filename                          # Longest line length
wc -m filename                          # Character count
wc -w filename                          # Word count
```

### Advanced Process Management

#### Process Monitoring Tools
```bash
# Advanced top usage
top -p PID1,PID2,PID3                 # Monitor specific processes
top -u username                        # Monitor user processes
top -d 1                              # Update every second
top -H                                # Show threads

# Process tree analysis
pstree -a                             # Show command arguments
pstree -h                             # Highlight current process
pstree -T                             # Show thread names
pstree -p                             # Show PIDs

# Advanced ps options
ps -eo pid,ppid,cmd,etime             # Custom output format
ps -p PID -o pid,ppid,state,time      # Process state and time
ps aux --sort=-%cpu                   # Sort by CPU usage
ps aux --sort=-%mem                   # Sort by memory usage
ps -ef --forest                       # Process tree view
```

#### Process Control Advanced
```bash
# Signal handling
trap 'cleanup_function' EXIT           # Trap exit signal
trap 'echo "Interrupted"' INT          # Trap interrupt signal
trap -l                                # List all signals

# Process groups
set -m                                 # Enable job control
fg %1                                  # Bring job 1 to foreground
bg %1                                  # Send job 1 to background

# Advanced job control
disown %1                              # Remove job from shell's job list
wait %1                                # Wait for job completion
jobs -l                                # List jobs with PIDs
jobs -r                                # List running jobs only
```

### Advanced Networking

#### Network Troubleshooting
```bash
# Advanced ping options
ping -i 0.2 -s 1500 host              # Custom interval and packet size
ping -c 100 -W 1 host                 # 100 packets with 1s timeout
ping -D host                           # Timestamp each packet
ping -f host                           # Flood ping

# Advanced traceroute
traceroute -n -w 1 host                # No DNS resolution, 1s timeout
traceroute -I host                     # Use ICMP instead of UDP
traceroute -T -p 80 host              # TCP traceroute on port 80
traceroute -m 30 host                  # Max 30 hops

# Network interface bonding
# /etc/network/interfaces
auto bond0
iface bond0 inet static
    address *************
    netmask *************
    bond-slaves eth0 eth1
    bond-mode 802.3ad
    bond-miimon 100
```

#### Network Performance Tuning
```bash
# TCP tuning parameters
echo 'net.ipv4.tcp_congestion_control = bbr' >> /etc/sysctl.conf
echo 'net.core.default_qdisc = fq' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_fastopen = 3' >> /etc/sysctl.conf

# Network buffer tuning
echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 16777216' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 16777216' >> /etc/sysctl.conf

# Apply changes
sysctl -p
```

### Advanced Log Analysis

#### Log Processing Tools
```bash
# Advanced grep patterns
grep -P 'pattern'                      # Perl-compatible regex
grep -A 5 -B 5 'pattern'              # Show 5 lines before and after
grep -v 'pattern'                      # Exclude lines matching pattern
grep -f patterns_file                  # Read patterns from file
grep -o 'pattern'                      # Show only matching part

# Advanced awk usage
awk 'NR==1,NR==10' file               # Print lines 1-10
awk '$1 > 100 && $2 < 50' file        # Complex conditions
awk '{sum+=$1} END {print sum}' file   # Sum first column
awk 'BEGIN {FS=":"} {print $1}' file   # Custom field separator
awk 'length > 80' file                 # Lines longer than 80 chars

# Advanced sed usage
sed '1,5d' file                        # Delete lines 1-5
sed 's/old/new/g' file                 # Global substitution
sed '/pattern/d' file                   # Delete lines matching pattern
sed '1i\new line' file                 # Insert before line 1
sed 's/^/prefix /' file                # Add prefix to each line
```

#### Log Analysis Scripts
```bash
#!/bin/bash
# Advanced log analyzer
LOG_FILE="/var/log/application.log"
ERROR_PATTERN="ERROR|CRITICAL|FATAL"

echo "=== Log Analysis Report ==="
echo "Date: $(date)"
echo "Log file: $LOG_FILE"
echo

# Count error types
echo "Error Summary:"
grep -E "$ERROR_PATTERN" "$LOG_FILE" | \
    awk '{print $4}' | sort | uniq -c | sort -nr

# Top error sources
echo -e "\nTop Error Sources:"
grep -E "$ERROR_PATTERN" "$LOG_FILE" | \
    awk '{print $1, $2}' | sort | uniq -c | sort -nr | head -10

# Error timeline
echo -e "\nError Timeline (last 24 hours):"
grep -E "$ERROR_PATTERN" "$LOG_FILE" | \
    grep "$(date -d '1 day ago' '+%Y-%m-%d')" | \
    awk '{print $1, $2}' | sort | uniq -c

# Performance analysis
echo -e "\nResponse Time Analysis:"
grep "response_time" "$LOG_FILE" | \
    awk '{print $NF}' | \
    awk '{sum+=$1; count++} END {print "Avg:", sum/count, "Max:", max}'
```

### Advanced Automation

#### Systemd Timers vs Cron
```bash
# Systemd timer example
# /etc/systemd/system/backup.timer
[Unit]
Description=Daily backup at 2 AM
Requires=backup.service

[Timer]
OnCalendar=*-*-* 02:00:00
RandomizedDelaySec=300
Persistent=true

[Install]
WantedBy=timers.target

# Enable and manage
systemctl enable backup.timer
systemctl start backup.timer
systemctl list-timers --all
```

#### Advanced Cron Patterns
```bash
# Complex cron schedules
0 2 * * 1-5 /usr/bin/daily_backup.sh    # Weekdays at 2 AM
0 9,17 * * 1-5 /usr/bin/check.sh       # 9 AM and 5 PM weekdays
0 0 1,15 * * /usr/bin/monthly.sh        # 1st and 15th of month
0 12 * * 0 /usr/bin/weekly.sh           # Noon on Sundays
0 0 1 1 * /usr/bin/yearly.sh            # New Year at midnight

# Special cron strings
@reboot    /usr/bin/startup.sh
@yearly    /usr/bin/yearly.sh
@annually  /usr/bin/yearly.sh
@monthly   /usr/bin/monthly.sh
@weekly    /usr/bin/weekly.sh
@daily     /usr/bin/daily.sh
@hourly    /usr/bin/hourly.sh

# Crontab management
crontab -e                          # Edit crontab
crontab -l                          # List crontab
crontab -r                          # Remove crontab
crontab -u username -e              # Edit user's crontab
```

---

## 🎯 Tóm tắt

### Key Takeaways
1. **Command Line** cung cấp sức mạnh và hiệu quả cao
2. **Shell scripting** cho phép tự động hóa tác vụ
3. **Text processing** với grep, sed, awk mạnh mẽ
4. **Process management** giúp kiểm soát hệ thống
5. **Network tools** cho quản trị mạng
6. **Security** thông qua quản lý quyền và sudo
7. **Advanced tools** cho text editing, file operations, và automation

### Learning Path
1. **Beginner**: Basic commands, navigation, file operations
2. **Intermediate**: Text processing, process management, scripting
3. **Advanced**: Advanced scripting, system administration, automation
4. **Expert**: Advanced text editors, complex automation, performance tuning

### Resources
- **Online**: man pages, --help options, online documentation
- **Books**: "The Linux Command Line" by William Shotts
- **Practice**: Set up a Linux VM, practice daily commands
- **Projects**: Automate repetitive tasks, create system scripts
- **Advanced**: Master Vim/Nano, advanced find commands, systemd timers

---

**Tiếp theo:** [File System Management](filesystem.md) - Quản lý hệ thống tệp và phân quyền

**Liên quan:** 
- [Linux Mastery Philosophy](../LINUX_MASTERY_PHILOSOPHY.md) - Triết lý và nguyên lý cốt lõi của Linux
- [Linux Complete Guide](../LINUX_COMPLETE_GUIDE.md) - Hướng dẫn toàn diện từ cơ bản đến nâng cao
- [Linux Advanced Resources](../LINUX_ADVANCED_RESOURCES.md) - Tài nguyên nâng cao