# Data Analysis & Storytelling Mastery Guide

## Vai trò của Data Analyst và SQL trong Doanh nghiệp

### 1.1 Data Analyst - Ngư<PERSON>i tìm ra Insights
Data Analyst là người tìm ra insights thông qua những con số giúp doanh nghiệp đưa ra các quyết định có cơ sở, cải thiện tình hình kinh doanh và chất lượng sản phẩm.

### 1.2 6 Giai đoạn Phân tích Dữ liệu
1. **Data Specifications**: Xác định dữ liệu cần thu thập trong quá trình xây dựng và vận hành tính năng sản phẩm
2. **Data Collection**: Dữ liệu được xử lý và lưu trữ trong các hệ thống cơ sở dữ liệu
3. **Data Preparation**: <PERSON><PERSON><PERSON>, làm sạch và xử lý dữ liệu thừa/thiếu
4. **Data Exploratory**: <PERSON><PERSON> phá dữ liệu để tìm ra insights
5. **Modelling & Evaluation**: Áp dụng thuật toán học máy để giải quyết các bài toán dự báo
6. **Communication of Insights**: Trình bày kết quả phân tích

### 1.3 SQL trong công việc DA/BI
- **Làm dashboard**: Truy xuất và làm sạch dữ liệu trước khi tạo dashboard trên các công cụ BI (Power BI, Tableau)
- **Reporting và ad-hoc analysis**: Dùng SQL để truy xuất, tính toán ra con số hay bảng thống kê kết quả để báo cáo khẩn cấp
- **SQL sẽ phổ biến như Excel**: Trở thành yêu cầu cơ bản như thành thạo Microsoft Office

## Nguyên lý Cốt lõi Database cho Data Analysis

### 2.1 ACID Properties cho Data Integrity
- **Atomicity**: "All or Nothing" - Giao dịch không thể "hoàn thành một nửa"
- **Consistency**: Cơ sở dữ liệu luôn ở trạng thái hợp lệ
- **Isolation**: Các giao dịch đồng thời không ảnh hưởng nhau
- **Durability**: Dữ liệu đã commit sẽ tồn tại vĩnh viễn

### 2.2 12 Nguyên tắc Thiết kế Database cho Analytics
1. **Normalization First, Denormalization by Need**: Bắt đầu với 3NF, denormalize có lý do
2. **Index Strategy**: Composite index > Multiple single indexes
3. **Query Optimization**: WHERE > JOIN > Subquery > Scalar functions
4. **Data Integrity**: Constraints + Triggers + Stored Procedures
5. **Security by Design**: Principle of least privilege + encryption
6. **Backup Everything**: Full + Incremental + Differential + Point-in-time
7. **Monitor Continuously**: Proactive alerts > Reactive fixes
8. **Document Relentlessly**: Schema, queries, procedures, troubleshooting
9. **Test Thoroughly**: Unit tests cho stored procedures, performance tests
10. **Scale Horizontally**: Sharding + Replication + Caching
11. **Automate Repetitive**: ETL, backups, monitoring, deployments
12. **Evolve Gradually**: Migration scripts + Rollback plans + Blue-green deployment

## Advanced SQL cho Data Analysis

### 3.1 Window Functions - Analytical Powerhouse
```sql
-- Running totals và moving averages
SELECT
    date,
    sales,
    SUM(sales) OVER (ORDER BY date) AS running_total,
    AVG(sales) OVER (ORDER BY date ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) AS moving_avg_7day,
    LAG(sales, 1) OVER (ORDER BY date) AS previous_day_sales,
    (sales - LAG(sales, 1) OVER (ORDER BY date)) / LAG(sales, 1) OVER (ORDER BY date) * 100 AS growth_rate
FROM sales_data;

-- Ranking và percentiles
SELECT
    employee_id,
    salary,
    RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as salary_rank,
    NTILE(4) OVER (ORDER BY salary) as salary_quartile,
    PERCENT_RANK() OVER (ORDER BY salary) as salary_percentile
FROM employees;
```

### 3.2 Common Table Expressions (CTEs) cho Complex Analysis
```sql
-- Recursive CTE for hierarchical data analysis
WITH RECURSIVE sales_hierarchy AS (
    -- Anchor: Top-level categories
    SELECT category_id, category_name, parent_id, 0 as level,
           CAST(category_name AS VARCHAR(1000)) as path
    FROM categories
    WHERE parent_id IS NULL
    
    UNION ALL
    
    -- Recursive: Child categories
    SELECT c.category_id, c.category_name, c.parent_id, sh.level + 1,
           sh.path + ' -> ' + c.category_name
    FROM categories c
    JOIN sales_hierarchy sh ON c.parent_id = sh.category_id
),
category_sales AS (
    SELECT sh.category_id, sh.category_name, sh.level, sh.path,
           SUM(s.amount) as total_sales,
           COUNT(s.sale_id) as transaction_count
    FROM sales_hierarchy sh
    LEFT JOIN sales s ON sh.category_id = s.category_id
    GROUP BY sh.category_id, sh.category_name, sh.level, sh.path
)
SELECT * FROM category_sales ORDER BY level, total_sales DESC;
```

### 3.3 Advanced Aggregation Techniques
```sql
-- CUBE và ROLLUP for multi-dimensional analysis
SELECT 
    COALESCE(region, 'ALL REGIONS') as region,
    COALESCE(product_category, 'ALL CATEGORIES') as category,
    COALESCE(CAST(YEAR(sale_date) AS VARCHAR), 'ALL YEARS') as year,
    SUM(amount) as total_sales,
    COUNT(*) as transaction_count,
    AVG(amount) as avg_transaction_value
FROM sales
GROUP BY CUBE(region, product_category, YEAR(sale_date))
ORDER BY region, category, year;

-- Conditional aggregation
SELECT 
    product_id,
    SUM(CASE WHEN YEAR(sale_date) = 2023 THEN amount ELSE 0 END) as sales_2023,
    SUM(CASE WHEN YEAR(sale_date) = 2024 THEN amount ELSE 0 END) as sales_2024,
    SUM(CASE WHEN YEAR(sale_date) = 2024 THEN amount ELSE 0 END) - 
    SUM(CASE WHEN YEAR(sale_date) = 2023 THEN amount ELSE 0 END) as growth_amount,
    CASE 
        WHEN SUM(CASE WHEN YEAR(sale_date) = 2023 THEN amount ELSE 0 END) > 0 THEN
            (SUM(CASE WHEN YEAR(sale_date) = 2024 THEN amount ELSE 0 END) - 
             SUM(CASE WHEN YEAR(sale_date) = 2023 THEN amount ELSE 0 END)) /
            SUM(CASE WHEN YEAR(sale_date) = 2023 THEN amount ELSE 0 END) * 100
        ELSE NULL
    END as growth_percentage
FROM sales
WHERE YEAR(sale_date) IN (2023, 2024)
GROUP BY product_id;
```

## Data Warehousing & ETL cho Analytics

### 4.1 Star Schema Design cho Analytics
```sql
-- Fact table (center of star)
CREATE TABLE sales_fact (
    sale_id BIGINT PRIMARY KEY,
    product_key INTEGER,
    customer_key INTEGER,
    date_key INTEGER,
    store_key INTEGER,
    quantity INTEGER,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(15,2),
    discount_amount DECIMAL(15,2),
    profit_amount DECIMAL(15,2),
    FOREIGN KEY (product_key) REFERENCES dim_product(product_key),
    FOREIGN KEY (customer_key) REFERENCES dim_customer(customer_key),
    FOREIGN KEY (date_key) REFERENCES dim_date(date_key),
    FOREIGN KEY (store_key) REFERENCES dim_store(store_key)
);

-- Dimension tables
CREATE TABLE dim_date (
    date_key INTEGER PRIMARY KEY,
    date_value DATE UNIQUE,
    year_number INTEGER,
    quarter_number INTEGER,
    month_number INTEGER,
    month_name VARCHAR(20),
    day_of_week INTEGER,
    day_name VARCHAR(20),
    is_weekend BOOLEAN,
    is_holiday BOOLEAN,
    fiscal_year INTEGER,
    fiscal_quarter INTEGER
);
```

### 4.2 ETL Pipeline Implementation
```python
# Modern ETL Pipeline
import pandas as pd
import numpy as np
from sqlalchemy import create_engine
import logging

class ETLPipeline:
    def __init__(self, source_conn, target_conn):
        self.source_engine = create_engine(source_conn)
        self.target_engine = create_engine(target_conn)
        self.logger = logging.getLogger(__name__)
    
    def extract_from_sources(self, last_extract_date):
        """Extract phase"""
        # Database extraction
        db_data = pd.read_sql("""
            SELECT order_id, customer_id, order_date, total_amount, status
            FROM orders
            WHERE modified_date >= %s
        """, self.source_engine, params=[last_extract_date])
        
        # API extraction (example)
        # api_data = requests.get(f"{API_BASE}/customers?since={last_extract_date}")
        
        return db_data
    
    def transform_data(self, raw_data):
        """Transform phase"""
        # Data cleaning
        clean_data = raw_data.dropna().drop_duplicates()
        
        # Data enrichment
        enriched_data = clean_data.copy()
        enriched_data['order_month'] = pd.to_datetime(enriched_data['order_date']).dt.to_period('M')
        enriched_data['order_year'] = pd.to_datetime(enriched_data['order_date']).dt.year
        
        # Business logic transformation
        enriched_data['customer_segment'] = pd.cut(
            enriched_data['total_amount'],
            bins=[0, 100, 500, float('inf')],
            labels=['Low Value', 'Medium Value', 'High Value']
        )
        
        # Calculate derived metrics
        customer_stats = enriched_data.groupby('customer_id').agg({
            'total_amount': ['sum', 'mean', 'count'],
            'order_date': ['min', 'max']
        }).round(2)
        
        customer_stats.columns = ['total_spent', 'avg_order_value', 'order_count', 'first_order', 'last_order']
        customer_stats['customer_lifetime_days'] = (customer_stats['last_order'] - customer_stats['first_order']).dt.days
        
        return enriched_data, customer_stats
    
    def load_to_warehouse(self, data, customer_stats):
        """Load phase with error handling"""
        try:
            # Staging table load
            data.to_sql('staging_orders', self.target_engine, 
                       if_exists='replace', index=False, method='multi')
            
            customer_stats.to_sql('staging_customer_stats', self.target_engine,
                                 if_exists='replace', index=True, method='multi')
            
            # Data validation
            validation_query = """
                SELECT COUNT(*) as record_count,
                       SUM(CASE WHEN total_amount < 0 THEN 1 ELSE 0 END) as negative_amounts,
                       SUM(CASE WHEN customer_id IS NULL THEN 1 ELSE 0 END) as null_customers
                FROM staging_orders
            """
            validation_result = pd.read_sql(validation_query, self.target_engine)
            
            if validation_result['negative_amounts'].iloc[0] > 0:
                raise ValueError("Negative amounts detected in staging data")
            
            if validation_result['null_customers'].iloc[0] > 0:
                raise ValueError("Null customer IDs detected in staging data")
            
            # Production load
            with self.target_engine.begin() as conn:
                conn.execute("""
                    INSERT INTO fact_orders (order_id, customer_key, date_key, amount, status)
                    SELECT s.order_id, c.customer_key, d.date_key, s.total_amount, s.status
                    FROM staging_orders s
                    JOIN dim_customer c ON s.customer_id = c.customer_id
                    JOIN dim_date d ON DATE(s.order_date) = d.date_value
                """)
            
            self.logger.info(f"Successfully loaded {len(data)} records")
            
        except Exception as e:
            self.logger.error(f"ETL load failed: {str(e)}")
            raise
```

## Data Visualization & Storytelling

### 5.1 Principles of Effective Data Visualization
**Visual Hierarchy**:
- **Primary Message**: Most important insight (largest, boldest)
- **Supporting Details**: Context and explanation (medium size)
- **Reference Information**: Legends, axes, footnotes (smallest)

**Color Strategy**:
- **Categorical Data**: Distinct colors for different categories
- **Sequential Data**: Color gradients for ordered data
- **Diverging Data**: Two-color gradient with neutral midpoint
- **Accessibility**: Color-blind friendly palettes

### 5.2 Dashboard Design Best Practices
```python
# Dashboard layout principles
dashboard_layout = {
    "header": {
        "title": "Sales Performance Dashboard",
        "filters": ["Date Range", "Region", "Product Category"],
        "kpis": ["Total Revenue", "Growth Rate", "Customer Count"]
    },
    "main_content": {
        "primary_chart": "Revenue Trend Over Time",
        "secondary_charts": [
            "Revenue by Region",
            "Top Products",
            "Customer Segments"
        ]
    },
    "footer": {
        "data_source": "Sales Database",
        "last_updated": "Real-time",
        "contact": "<EMAIL>"
    }
}
```

### 5.3 Storytelling with Data Framework
**STAR Method**:
- **S**ituation: Set the context
- **T**ask: Define the challenge or question
- **A**ction: Describe the analysis performed
- **R**esult: Present findings and recommendations

**Narrative Structure**:
1. **Hook**: Compelling opening that grabs attention
2. **Context**: Background information and problem statement
3. **Conflict**: Challenges or obstacles discovered
4. **Resolution**: Solutions and recommendations
5. **Call to Action**: Next steps and implementation plan

## Advanced Analytics Techniques

### 6.1 Statistical Analysis in SQL
```sql
-- Descriptive statistics
SELECT 
    product_category,
    COUNT(*) as sample_size,
    AVG(price) as mean_price,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY price) as median_price,
    STDDEV(price) as std_deviation,
    MIN(price) as min_price,
    MAX(price) as max_price,
    PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY price) as q1,
    PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY price) as q3
FROM products
GROUP BY product_category;

-- Correlation analysis
WITH price_sales AS (
    SELECT 
        product_id,
        AVG(unit_price) as avg_price,
        SUM(quantity) as total_sales
    FROM order_items
    GROUP BY product_id
),
stats AS (
    SELECT 
        AVG(avg_price) as mean_price,
        AVG(total_sales) as mean_sales,
        STDDEV(avg_price) as std_price,
        STDDEV(total_sales) as std_sales
    FROM price_sales
)
SELECT 
    SUM((ps.avg_price - s.mean_price) * (ps.total_sales - s.mean_sales)) / 
    (COUNT(*) * s.std_price * s.std_sales) as correlation_coefficient
FROM price_sales ps, stats s;
```

### 6.2 Time Series Analysis
```sql
-- Seasonal decomposition
WITH monthly_sales AS (
    SELECT 
        DATE_TRUNC('month', order_date) as month,
        SUM(total_amount) as monthly_total
    FROM orders
    GROUP BY DATE_TRUNC('month', order_date)
),
trend_analysis AS (
    SELECT 
        month,
        monthly_total,
        AVG(monthly_total) OVER (
            ORDER BY month 
            ROWS BETWEEN 5 PRECEDING AND 5 FOLLOWING
        ) as trend,
        monthly_total - AVG(monthly_total) OVER (
            ORDER BY month 
            ROWS BETWEEN 5 PRECEDING AND 5 FOLLOWING
        ) as detrended
    FROM monthly_sales
)
SELECT 
    month,
    monthly_total,
    trend,
    detrended,
    EXTRACT(MONTH FROM month) as month_number,
    AVG(detrended) OVER (
        PARTITION BY EXTRACT(MONTH FROM month)
    ) as seasonal_component
FROM trend_analysis
ORDER BY month;
```

## Performance Optimization cho Analytics

### 7.1 Query Optimization Strategies
```sql
-- Efficient aggregation with proper indexing
CREATE INDEX idx_sales_analysis ON sales (date_column, category_id, amount);

-- Optimized query structure
SELECT 
    category_id,
    DATE_TRUNC('month', date_column) as month,
    SUM(amount) as total_sales,
    COUNT(*) as transaction_count,
    AVG(amount) as avg_transaction
FROM sales
WHERE date_column >= '2024-01-01'
    AND date_column < '2025-01-01'
    AND category_id IN (1, 2, 3, 4, 5)
GROUP BY category_id, DATE_TRUNC('month', date_column)
ORDER BY category_id, month;
```

### 7.2 Materialized Views cho Performance
```sql
-- Pre-computed aggregates for fast dashboard queries
CREATE MATERIALIZED VIEW monthly_sales_summary AS
SELECT
    DATE_TRUNC('month', order_date) as month,
    product_category,
    region,
    COUNT(*) as order_count,
    SUM(total_amount) as total_sales,
    AVG(total_amount) as avg_order_value,
    COUNT(DISTINCT customer_id) as unique_customers
FROM orders o
JOIN products p ON o.product_id = p.product_id
JOIN customers c ON o.customer_id = c.customer_id
GROUP BY DATE_TRUNC('month', order_date), product_category, region;

-- Refresh strategy
REFRESH MATERIALIZED VIEW CONCURRENTLY monthly_sales_summary;
```

Data Analysis và Storytelling yêu cầu sự kết hợp giữa kỹ năng kỹ thuật (SQL, Python, visualization tools) và kỹ năng soft skills (communication, business understanding, critical thinking) để biến dữ liệu thành insights có giá trị và actionable recommendations.
