# 🚀 **EXPRESS.JS COMPREHENSIVE GUIDE**

> **Complete guide to Express.js framework for Node.js applications**

## 📚 **OVERVIEW**

Express.js là một web framework tối thiểu, linh hoạt cho Node.js, cung cấp một bộ tính năng mạnh mẽ để phát triển web và mobile applications. N<PERSON> được thiết kế để xây dựng APIs và ứng dụng web một cách nhanh chóng và dễ dàng.

## 🏗️ **GETTING STARTED**

### **Installation & Basic Setup**

```bash
# Initialize project
npm init -y

# Install Express
npm install express

# Install development dependencies
npm install --save-dev nodemon
```

**Basic Express Application:**
```javascript
const express = require('express');
const app = express();
const PORT = process.env.PORT || 3000;

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Basic route
app.get('/', (req, res) => {
    res.json({ message: 'Hello Express!' });
});

// Start server
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
```

**Package.json Scripts:**
```json
{
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js",
    "test": "jest"
  }
}
```

## 🌐 **HTTP HEADERS MANAGEMENT**

### **Reading Request Headers**

```javascript
app.get('/headers', (req, res) => {
    // Get all headers
    const allHeaders = req.headers;
    
    // Get specific headers
    const userAgent = req.header('User-Agent');
    const authorization = req.header('Authorization');
    const contentType = req.header('Content-Type');
    
    // Alternative syntax
    const host = req.get('Host');
    const accept = req.get('Accept');
    
    res.json({
        userAgent,
        authorization,
        contentType,
        host,
        accept,
        allHeaders
    });
});
```

### **Setting Response Headers**

```javascript
app.get('/response-headers', (req, res) => {
    // Set individual headers
    res.set('X-Custom-Header', 'MyValue');
    res.set('Content-Type', 'application/json');
    
    // Set multiple headers at once
    res.set({
        'X-API-Version': '1.0',
        'X-Rate-Limit': '1000',
        'Cache-Control': 'no-cache'
    });
    
    // Content-Type shortcuts
    res.type('json');     // Sets Content-Type to application/json
    res.type('html');     // Sets Content-Type to text/html
    res.type('xml');      // Sets Content-Type to text/xml
    res.type('png');      // Sets Content-Type to image/png
    
    res.json({ message: 'Headers set successfully' });
});
```

**Security Headers:**
```javascript
app.use((req, res, next) => {
    // Security headers
    res.set({
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'"
    });
    next();
});
```

## 🔄 **REDIRECTS & URL HANDLING**

### **Basic Redirects**

```javascript
// Temporary redirect (302)
app.get('/temp-redirect', (req, res) => {
    res.redirect('/new-location');
});

// Permanent redirect (301)
app.get('/permanent-redirect', (req, res) => {
    res.redirect(301, '/new-permanent-location');
});

// Redirect with status codes
app.get('/custom-redirect', (req, res) => {
    res.redirect(302, '/temporary');
    // res.redirect(301, '/permanent');
    // res.redirect(303, '/see-other');
    // res.redirect(307, '/temporary-redirect');
    // res.redirect(308, '/permanent-redirect');
});
```

### **Advanced Redirect Patterns**

```javascript
// Absolute URL redirect
app.get('/external', (req, res) => {
    res.redirect('https://google.com');
});

// Relative path redirect
app.get('/relative', (req, res) => {
    res.redirect('../dashboard');
});

// Back to previous page
app.get('/go-back', (req, res) => {
    res.redirect('back');
});

// Conditional redirects
app.get('/mobile-detect', (req, res) => {
    const userAgent = req.get('User-Agent');
    
    if (/mobile/i.test(userAgent)) {
        res.redirect('/mobile-app');
    } else {
        res.redirect('/desktop-app');
    }
});

// Redirect with query parameters
app.get('/redirect-with-params', (req, res) => {
    const { userId, action } = req.query;
    res.redirect(`/dashboard?user=${userId}&action=${action}`);
});
```

## 🛤️ **ADVANCED ROUTING**

### **Route Parameters**

```javascript
// Single parameter
app.get('/user/:id', (req, res) => {
    const { id } = req.params;
    res.json({ message: `User ID: ${id}` });
});

// Multiple parameters
app.get('/user/:userId/post/:postId', (req, res) => {
    const { userId, postId } = req.params;
    res.json({ userId, postId });
});

// Optional parameters
app.get('/product/:id?', (req, res) => {
    const { id } = req.params;
    
    if (id) {
        res.json({ message: `Product ID: ${id}` });
    } else {
        res.json({ message: 'All products' });
    }
});

// Wildcard parameters
app.get('/files/*', (req, res) => {
    const filePath = req.params[0];
    res.json({ filePath });
});
```

### **Route Patterns & Regular Expressions**

```javascript
// String patterns
app.get('/ab?cd', handler);      // Matches acd, abcd
app.get('/ab+cd', handler);      // Matches abcd, abbcd, abbbcd, etc.
app.get('/ab*cd', handler);      // Matches abcd, abxcd, abRANDOMcd, etc.
app.get('/ab(cd)?e', handler);   // Matches abe, abcde

// Regular expressions
app.get(/.*fly$/, (req, res) => {
    res.send('Route ending with "fly"');
});

app.get(/post/, (req, res) => {
    res.send('Route containing "post"');
});

// Named regex groups
app.get(/^\/user\/(\d+)$/, (req, res) => {
    const userId = req.params[0];
    res.json({ userId });
});
```

### **Route Handlers & Middleware**

```javascript
// Multiple route handlers
app.get('/multi-handler', 
    (req, res, next) => {
        console.log('First handler');
        next();
    },
    (req, res, next) => {
        console.log('Second handler');
        next();
    },
    (req, res) => {
        res.send('Final handler');
    }
);

// Array of handlers
const validators = [
    (req, res, next) => {
        // Validation logic
        next();
    },
    (req, res, next) => {
        // Authentication logic
        next();
    }
];

app.post('/protected', validators, (req, res) => {
    res.json({ message: 'Protected route accessed' });
});

// Route-specific middleware
const logRequest = (req, res, next) => {
    console.log(`${req.method} ${req.path} - ${new Date()}`);
    next();
};

app.get('/logged', logRequest, (req, res) => {
    res.send('This request was logged');
});
```

### **Router Module**

```javascript
// routes/users.js
const express = require('express');
const router = express.Router();

// Middleware specific to this router
router.use((req, res, next) => {
    console.log('User router middleware');
    next();
});

// Define routes
router.get('/', (req, res) => {
    res.json({ message: 'Users list' });
});

router.get('/:id', (req, res) => {
    res.json({ message: `User ${req.params.id}` });
});

router.post('/', (req, res) => {
    res.json({ message: 'User created' });
});

module.exports = router;

// app.js
const userRoutes = require('./routes/users');
app.use('/users', userRoutes);
```

## 🎨 **TEMPLATE ENGINES**

### **Pug Template Engine**

**Setup:**
```javascript
const express = require('express');
const app = express();

// Set Pug as template engine
app.set('view engine', 'pug');
app.set('views', './views');

// Enable template caching in production
if (app.get('env') === 'production') {
    app.set('view cache', true);
}
```

**Basic Pug Template (views/index.pug):**
```pug
doctype html
html(lang='en')
    head
        meta(charset='UTF-8')
        meta(name='viewport', content='width=device-width, initial-scale=1.0')
        title= title
        link(rel='stylesheet', href='/css/style.css')
    body
        header
            h1= heading
            nav
                ul
                    each item in navigation
                        li
                            a(href=item.url)= item.text
        
        main
            if user
                p Welcome back, #{user.name}!
                .user-info
                    p Email: #{user.email}
                    p Role: #{user.role}
            else
                p Please log in to continue
            
            .content
                != content
        
        footer
            p &copy; 2024 My App
        
        script(src='/js/app.js')
```

**Rendering Pug Templates:**
```javascript
app.get('/', (req, res) => {
    res.render('index', {
        title: 'Home Page',
        heading: 'Welcome to My App',
        navigation: [
            { text: 'Home', url: '/' },
            { text: 'About', url: '/about' },
            { text: 'Contact', url: '/contact' }
        ],
        user: {
            name: 'John Doe',
            email: '<EMAIL>',
            role: 'admin'
        },
        content: '<p>This is <strong>HTML content</strong></p>'
    });
});
```

### **EJS Template Engine**

**Setup:**
```javascript
app.set('view engine', 'ejs');
app.set('views', './views');
```

**EJS Template (views/profile.ejs):**
```html
<!DOCTYPE html>
<html>
<head>
    <title><%= title %></title>
</head>
<body>
    <h1>User Profile</h1>
    
    <% if (user) { %>
        <div class="profile">
            <h2><%= user.name %></h2>
            <p>Email: <%= user.email %></p>
            <p>Joined: <%= user.joinDate.toDateString() %></p>
            
            <h3>Posts</h3>
            <ul>
                <% user.posts.forEach(post => { %>
                    <li>
                        <strong><%= post.title %></strong>
                        <p><%- post.content %></p>
                        <small>Posted on <%= post.date.toDateString() %></small>
                    </li>
                <% }); %>
            </ul>
        </div>
    <% } else { %>
        <p>User not found</p>
    <% } %>
</body>
</html>
```

**Rendering EJS:**
```javascript
app.get('/profile/:id', (req, res) => {
    // Get user data
    const user = {
        name: 'Jane Smith',
        email: '<EMAIL>',
        joinDate: new Date('2023-01-15'),
        posts: [
            {
                title: 'My First Post',
                content: 'This is my <em>first</em> blog post!',
                date: new Date('2023-02-01')
            },
            {
                title: 'Another Post',
                content: 'Here is another post with <strong>bold</strong> text.',
                date: new Date('2023-02-15')
            }
        ]
    };
    
    res.render('profile', {
        title: 'User Profile',
        user: user
    });
});
```

### **Handlebars Template Engine**

**Setup:**
```javascript
const exphbs = require('express-handlebars');

app.engine('handlebars', exphbs.engine());
app.set('view engine', 'handlebars');
```

**Handlebars Template:**
```handlebars
<!DOCTYPE html>
<html>
<head>
    <title>{{title}}</title>
</head>
<body>
    <h1>{{heading}}</h1>
    
    {{#if users}}
        <ul>
            {{#each users}}
                <li>
                    {{name}} - {{email}}
                    {{#if isAdmin}}
                        <span class="admin-badge">Admin</span>
                    {{/if}}
                </li>
            {{/each}}
        </ul>
    {{else}}
        <p>No users found</p>
    {{/if}}
</body>
</html>
```

## 🔧 **MIDDLEWARE DEEP DIVE**

### **Built-in Middleware**

```javascript
// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb' 
}));

// Static file serving
app.use(express.static('public'));
app.use('/assets', express.static('assets'));

// Cookie parsing
const cookieParser = require('cookie-parser');
app.use(cookieParser('secret-key'));
```

### **Custom Middleware**

```javascript
// Request logging middleware
const requestLogger = (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
        const duration = Date.now() - start;
        console.log(`${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
    });
    
    next();
};

// Request ID middleware
const requestId = (req, res, next) => {
    req.id = Math.random().toString(36).substr(2, 9);
    res.set('X-Request-ID', req.id);
    next();
};

// Response time middleware
const responseTime = (req, res, next) => {
    const start = process.hrtime();
    
    res.on('finish', () => {
        const [seconds, nanoseconds] = process.hrtime(start);
        const milliseconds = seconds * 1000 + nanoseconds / 1000000;
        res.set('X-Response-Time', `${milliseconds.toFixed(2)}ms`);
    });
    
    next();
};

// Authentication middleware
const authenticate = (req, res, next) => {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
        return res.status(401).json({ error: 'No token provided' });
    }
    
    try {
        // Verify JWT token here
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        res.status(401).json({ error: 'Invalid token' });
    }
};

// Authorization middleware
const authorize = (roles = []) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: 'Not authenticated' });
        }
        
        if (roles.length && !roles.includes(req.user.role)) {
            return res.status(403).json({ error: 'Insufficient permissions' });
        }
        
        next();
    };
};

// Apply middleware
app.use(requestLogger);
app.use(requestId);
app.use(responseTime);

// Protected routes
app.use('/api/admin', authenticate, authorize(['admin']));
app.use('/api/user', authenticate);
```

### **Error Handling Middleware**

```javascript
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl,
        method: req.method
    });
});

// Global error handler
app.use((err, req, res, next) => {
    console.error(err.stack);
    
    // Mongoose validation error
    if (err.name === 'ValidationError') {
        return res.status(400).json({
            error: 'Validation Error',
            details: Object.values(err.errors).map(e => e.message)
        });
    }
    
    // Mongoose cast error
    if (err.name === 'CastError') {
        return res.status(400).json({
            error: 'Invalid ID format'
        });
    }
    
    // JWT error
    if (err.name === 'JsonWebTokenError') {
        return res.status(401).json({
            error: 'Invalid token'
        });
    }
    
    // Multer error
    if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
            error: 'File too large'
        });
    }
    
    // Default error response
    res.status(err.status || 500).json({
        error: process.env.NODE_ENV === 'production' 
            ? 'Internal Server Error' 
            : err.message,
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
});
```

## 📁 **STATIC FILE SERVING**

### **Basic Static Serving**

```javascript
// Serve files from public directory
app.use(express.static('public'));

// Multiple static directories
app.use(express.static('public'));
app.use(express.static('assets'));
app.use(express.static('uploads'));

// Virtual path prefix
app.use('/static', express.static('public'));
app.use('/media', express.static('uploads'));
```

### **Advanced Static Configuration**

```javascript
// Static files with options
app.use('/static', express.static('public', {
    // Cache settings
    maxAge: '1d',              // Cache for 1 day
    etag: true,                // Enable ETag
    lastModified: true,        // Enable Last-Modified header
    
    // Index files
    index: ['index.html', 'index.htm', 'default.html'],
    
    // Dotfiles handling
    dotfiles: 'ignore',        // 'allow', 'deny', 'ignore'
    
    // Fallback
    fallthrough: true,         // Continue to next middleware if file not found
    
    // Custom headers
    setHeaders: (res, path, stat) => {
        if (path.endsWith('.css')) {
            res.set('Content-Type', 'text/css');
        }
        if (path.endsWith('.js')) {
            res.set('Content-Type', 'application/javascript');
        }
    }
}));

// Conditional static serving
app.use('/uploads', (req, res, next) => {
    // Check authentication for uploads
    if (req.header('Authorization')) {
        next();
    } else {
        res.status(401).send('Unauthorized');
    }
}, express.static('uploads'));
```

### **File Download Handling**

```javascript
// Simple file download
app.get('/download/:filename', (req, res) => {
    const filename = req.params.filename;
    const path = `./files/${filename}`;
    
    res.download(path);
});

// Download with custom filename
app.get('/report/:id', (req, res) => {
    const reportId = req.params.id;
    const filePath = `./reports/report_${reportId}.pdf`;
    const downloadName = `Report_${reportId}_${new Date().toISOString().split('T')[0]}.pdf`;
    
    res.download(filePath, downloadName);
});

// Download with error handling
app.get('/secure-download/:id', authenticate, (req, res) => {
    const fileId = req.params.id;
    const filePath = `./secure-files/${fileId}`;
    
    res.download(filePath, (err) => {
        if (err) {
            console.error('Download error:', err);
            if (!res.headersSent) {
                res.status(404).json({ error: 'File not found' });
            }
        } else {
            console.log(`File ${fileId} downloaded by user ${req.user.id}`);
        }
    });
});

// Streaming large files
app.get('/stream/:filename', (req, res) => {
    const filename = req.params.filename;
    const path = `./large-files/${filename}`;
    
    const stat = fs.statSync(path);
    const fileSize = stat.size;
    const range = req.headers.range;
    
    if (range) {
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
        const chunksize = (end - start) + 1;
        const file = fs.createReadStream(path, { start, end });
        const head = {
            'Content-Range': `bytes ${start}-${end}/${fileSize}`,
            'Accept-Ranges': 'bytes',
            'Content-Length': chunksize,
            'Content-Type': 'video/mp4',
        };
        res.writeHead(206, head);
        file.pipe(res);
    } else {
        const head = {
            'Content-Length': fileSize,
            'Content-Type': 'video/mp4',
        };
        res.writeHead(200, head);
        fs.createReadStream(path).pipe(res);
    }
});
```

💡 **Ghi nhớ**: Đây là tài liệu toàn diện về Express.js được cập nhật từ FreeCodeCamp Express Handbook, bao gồm tất cả các tính năng và best practices để xây dựng ứng dụng web hiệu quả với Express framework.