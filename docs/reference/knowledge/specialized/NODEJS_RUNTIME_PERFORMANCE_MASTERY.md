# 🚀 **NODE.JS RUNTIME PERFORMANCE MASTERY**

> **Comprehensive guide to Node.js runtime optimization, memory management, and performance tuning**

## 📚 **OVERVIEW**

Node.js là một runtime JavaScript mạnh mẽ được xây dựng trên V8 engine của Google, thiết kế cho khả năng mở rộng cao và hiệu suất tối ưu. Việc quản lý hiệu suất Node.js đòi hỏi hiểu biết sâu về kiến trúc đơn luồng, event loop, garbage collection, và các kỹ thuật tối ưu hóa.

## 🏗️ **RUNTIME ARCHITECTURE**

### **V8 Engine & Just-In-Time Compilation**

Node.js sử dụng V8 engine của Google để thực thi JavaScript. V8 thực hiện Just-In-Time (JIT) compilation, chuyển đổi JavaScript thành mã máy tối ưu thay vì thông dịch trực tiếp.

**V8 Engine Components:**
- **Parsing**: Chuyển đổi mã JavaScript thành Abstract Syntax Tree (AST)
- **Ignition**: Trình thông dịch tạo bytecode từ AST
- **TurboFan**: JIT compiler tối ưu hóa các đoạn mã "hot" thường xuyên thực thi

V8 engine có gần 800 tùy chọn cấu hình trong Node.js version 22, cho phép điều chỉnh chi tiết hiệu suất.

```javascript
// Kiểm tra V8 version và memory
console.log(`V8 version: ${process.versions.v8}`);

const v8 = require('v8');
const heapStats = v8.getHeapStatistics();
console.log('Heap size limit:', (heapStats.heap_size_limit / 1024 / 1024).toFixed(2), 'MB');
console.log('Used heap size:', (heapStats.used_heap_size / 1024 / 1024).toFixed(2), 'MB');
```

### **Event Loop & Asynchronous Architecture**

Node.js sử dụng kiến trúc đơn luồng với event loop cho phép xử lý đồng thời nhiều hoạt động. Event loop hoạt động theo nguyên tắc non-blocking, khởi tạo tác vụ mới trước khi tác vụ trước hoàn thành.

**Event Loop Lag Monitoring:**
```javascript
const toobusy = require('toobusy-js');
toobusy.maxLag(100); // Độ trễ tối đa 100ms
toobusy.interval(500); // Kiểm tra mỗi 500ms

setInterval(() => {
    const lag = toobusy.lag();
    console.log(`Event loop lag: ${lag}ms`);
    
    // Alert if lag is too high
    if (lag > 100) {
        console.warn(`⚠️ High event loop lag: ${lag}ms`);
    }
}, 1000);
```

## 💾 **MEMORY MANAGEMENT & GARBAGE COLLECTION**

### **V8 Heap Architecture**

V8 sử dụng generational garbage collection với hai vùng nhớ chính:

- **New Space (Young Generation)**: Chứa các object tạm thời, GC thường xuyên
- **Old Space**: Chứa các object tồn tại lâu, GC ít thường xuyên hơn

### **Memory Tuning Parameters**

```bash
# Tăng Old Space size
node --max-old-space-size=4096 app.js

# Tăng New Space size
node --max-semi-space-size=64 app.js

# Tối ưu GC
node --optimize-for-size app.js
node --gc-interval=100 app.js
```

Kết quả benchmark cho thấy việc điều chỉnh V8 heap có thể cải thiện hiệu suất **11-45%** và giảm CPU usage **22-68%**.

### **Optimal Memory Configuration**

```javascript
// Tính toán memory phù hợp cho production
const totalMemory = 32; // GB
const systemOverhead = 4; // GB
const applications = 3;
const memoryPerApp = (totalMemory - systemOverhead) / applications; // 9.33GB
const heapSize = memoryPerApp * 0.7; // 6.53GB per app

// Memory monitoring
const memUsage = process.memoryUsage();
const memoryInfo = {
    rss: (memUsage.rss / 1024 / 1024).toFixed(2) + ' MB',
    heapTotal: (memUsage.heapTotal / 1024 / 1024).toFixed(2) + ' MB',
    heapUsed: (memUsage.heapUsed / 1024 / 1024).toFixed(2) + ' MB',
    external: (memUsage.external / 1024 / 1024).toFixed(2) + ' MB'
};
```

## 📊 **PERFORMANCE MONITORING & PROFILING**

### **Key Performance Metrics**

Các chỉ số quan trọng cần theo dõi:

- **Event Loop Utilization (ELU)**: Độ sử dụng event loop
- **Memory Usage**: Heap used/total, RSS memory
- **CPU Usage**: Thời gian xử lý CPU
- **Garbage Collection**: Tần suất và thời gian GC
- **Active Handles/Requests**: Số lượng operations đang chờ xử lý

### **Clinic.js Professional Profiling Suite**

Clinic.js cung cấp bộ công cụ profiling chuyên dụng:

- **Clinic Doctor**: Chẩn đoán tổng quát về CPU, memory, event loop
- **Clinic Bubbleprof**: Phân tích async operations
- **Clinic Flame**: Visualize CPU usage bằng flame graphs
- **Clinic Heap Profiler**: Phát hiện memory leaks

```bash
npm install -g clinic

# CPU Profiling
clinic doctor -- node app.js
clinic flame -- node app.js

# Memory Profiling
clinic heap -- node app.js

# Async Operations Profiling
clinic bubbleprof -- node app.js
```

### **APM Tools Comparison**

| Tool | Type | Features | Best For |
|------|------|----------|----------|
| SigNoz | Open Source | Full observability, tracing, alerts | Self-hosted environments |
| Datadog | Commercial | APM, logs, ML insights | Enterprise applications |
| New Relic | Commercial | AI analytics, broad integrations | Complex applications |
| PM2 | Open Source | Process management, basic monitoring | Simple deployments |

## 🔄 **CLUSTERING & SCALABILITY**

### **Node.js Cluster Module**

Node.js clustering cho phép tận dụng multiple CPU cores:

```javascript
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
    console.log(`Master ${process.pid} is running`);
    
    // Fork workers
    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }
    
    cluster.on('exit', (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} died`);
        cluster.fork(); // Restart worker
    });
} else {
    // Worker process
    require('./app.js');
    console.log(`Worker ${process.pid} started`);
}
```

### **Performance Gains from Clustering**

Benchmark kết quả cho thấy clustering có thể cải thiện:
- **Throughput**: Tăng từ 88 req/s lên 352 req/s (4x improvement)
- **Latency**: Giảm từ 2085ms xuống 514ms (4x faster)
- **Total time**: Giảm từ 13s xuống 3s

### **PM2 for Production Clustering**

```bash
npm install -g pm2

# Start app with clustering
pm2 start app.js -i max  # Use all CPU cores
pm2 start app.js -i 4    # Use 4 instances

# Monitoring
pm2 monit
pm2 logs
pm2 status
```

## 🧵 **WORKER THREADS FOR CPU-INTENSIVE TASKS**

### **When to Use Worker Threads**

Worker threads phù hợp cho:
- **CPU-intensive operations**: Image processing, encryption, complex calculations
- **Parallel processing**: Tận dụng multi-core systems
- **Background tasks**: Long-running operations không block main thread

### **Worker Thread Pool Pattern**

```javascript
const { Worker } = require('worker_threads');

class WorkerPool {
    constructor(poolSize, workerScript) {
        this.poolSize = poolSize;
        this.workerScript = workerScript;
        this.workers = [];
        this.queue = [];
        
        this.initializeWorkers();
    }
    
    initializeWorkers() {
        for (let i = 0; i < this.poolSize; i++) {
            const worker = new Worker(this.workerScript);
            worker.busy = false;
            this.workers.push(worker);
        }
    }
    
    execute(data) {
        return new Promise((resolve, reject) => {
            const availableWorker = this.workers.find(w => !w.busy);
            
            if (availableWorker) {
                this.runTask(availableWorker, data, resolve, reject);
            } else {
                this.queue.push({ data, resolve, reject });
            }
        });
    }
    
    runTask(worker, data, resolve, reject) {
        worker.busy = true;
        
        worker.once('message', (result) => {
            worker.busy = false;
            resolve(result);
            this.processQueue();
        });
        
        worker.once('error', (error) => {
            worker.busy = false;
            reject(error);
            this.processQueue();
        });
        
        worker.postMessage(data);
    }
    
    processQueue() {
        if (this.queue.length > 0) {
            const { data, resolve, reject } = this.queue.shift();
            const availableWorker = this.workers.find(w => !w.busy);
            if (availableWorker) {
                this.runTask(availableWorker, data, resolve, reject);
            }
        }
    }
}
```

## 🌊 **STREAMS & BUFFER OPTIMIZATION**

### **Efficient Data Handling**

Streams cho phép xử lý dữ liệu lớn một cách hiệu quả:

```javascript
const fs = require('fs');

// Readable Stream cho file lớn
const readStream = fs.createReadStream('largefile.txt', {
    highWaterMark: 64 * 1024 // 64KB buffer
});

const writeStream = fs.createWriteStream('output.txt');

readStream.pipe(writeStream);

// Monitor performance
readStream.on('data', (chunk) => {
    console.log(`Processed ${chunk.length} bytes`);
});

// Error handling
readStream.on('error', (error) => {
    console.error('Read stream error:', error);
});

writeStream.on('error', (error) => {
    console.error('Write stream error:', error);
});
```

### **Buffer Best Practices**

```javascript
// Tối ưu buffer size dựa trên file size
const stats = fs.statSync('largefile.txt');
const buffer = Buffer.alloc(stats.size);

// Sử dụng encoding phù hợp
const textBuffer = Buffer.from('Hello World', 'utf-8');
console.log(textBuffer.toString('utf-8'));

// Streaming cho data lớn
const stream = fs.createReadStream('largefile.txt');
stream.pipe(process.stdout);

// Buffer pooling for high-performance scenarios
class BufferPool {
    constructor(poolSize = 10, bufferSize = 64 * 1024) {
        this.pool = [];
        this.poolSize = poolSize;
        this.bufferSize = bufferSize;
        this.initializePool();
    }
    
    initializePool() {
        for (let i = 0; i < this.poolSize; i++) {
            this.pool.push(Buffer.alloc(this.bufferSize));
        }
    }
    
    getBuffer() {
        return this.pool.pop() || Buffer.alloc(this.bufferSize);
    }
    
    returnBuffer(buffer) {
        if (this.pool.length < this.poolSize) {
            buffer.fill(0); // Clear buffer
            this.pool.push(buffer);
        }
    }
}
```

## 🌐 **HTTP SERVER OPTIMIZATION**

### **Keep-Alive Configuration**

HTTP Keep-Alive có thể cải thiện hiệu suất đáng kể:

```javascript
const express = require('express');
const app = express();

// Server-side Keep-Alive
const server = app.listen(3000);
server.keepAliveTimeout = 30000; // 30 seconds
server.headersTimeout = 31000;   // 31 seconds

// Client-side Keep-Alive
const https = require('https');
const agent = new https.Agent({
    keepAlive: true,
    keepAliveMsecs: 30000,
    maxSockets: 50
});

// HTTP/2 support
const http2 = require('http2');
const http2Server = http2.createSecureServer({
    key: fs.readFileSync('key.pem'),
    cert: fs.readFileSync('cert.pem')
});
```

Kết quả cho thấy Keep-Alive có thể làm request thứ hai **nhanh hơn 70%** so với request đầu tiên.

### **Compression Middleware**

```javascript
const compression = require('compression');

// Advanced compression configuration
app.use(compression({
    threshold: 1024,        // Only compress responses > 1KB
    level: 6,               // Compression level (0-9)
    memLevel: 8,            // Memory usage for compression
    filter: (req, res) => {
        // Skip compression for certain content types
        if (req.headers['x-no-compression']) {
            return false;
        }
        return compression.filter(req, res);
    }
}));
```

**Compression Performance Comparison:**

| Algorithm | Compression Ratio | Processing Time | File Size Reduction |
|-----------|-------------------|-----------------|-------------------|
| Brotli | 64.26% | 95.31ms | Best (1.89MB) |
| Gzip | 61.66% | 252.01ms | Good (2.03MB) |
| Deflate | 61.66% | 241.10ms | Good (2.03MB) |

## 🗄️ **DATABASE CONNECTION POOLING**

### **PostgreSQL Connection Pool Implementation**

```javascript
const { Pool } = require('pg');

const pool = new Pool({
    user: 'username',
    host: 'localhost',
    database: 'dbname',
    password: 'password',
    port: 5432,
    max: 20,                    // Maximum connections
    idleTimeoutMillis: 30000,   // Close idle connections
    connectionTimeoutMillis: 2000,
    allowExitOnIdle: true,      // Allow process to exit when idle
    maxUses: 7500               // Close connections after 7500 queries
});

// Connection pool monitoring
pool.on('connect', (client) => {
    console.log('New client connected to pool');
});

pool.on('error', (err, client) => {
    console.error('Unexpected error on idle client', err);
});

pool.on('remove', (client) => {
    console.log('Client removed from pool');
});

// Sử dụng pool với transaction support
async function executeTransaction(callback) {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');
        const result = await callback(client);
        await client.query('COMMIT');
        return result;
    } catch (error) {
        await client.query('ROLLBACK');
        throw error;
    } finally {
        client.release();
    }
}
```

### **Connection Pool Benefits**

Connection pooling có thể cải thiện:
- Application responsiveness: **lên đến 40%** improvement
- Request handling: **35%** increase
- Database connection efficiency: **50%** reduction in connection overhead

## 🔒 **SECURITY & RATE LIMITING**

### **Express Rate Limiting**

```javascript
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,                 // 100 requests per window
    message: 'Too many requests',
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
        // Skip rate limiting for specific conditions
        return req.ip === '127.0.0.1' || req.path === '/health';
    }
});

app.use('/api', limiter);
```

### **Advanced Rate Limiting Strategies**

```javascript
// Sliding window với Redis
const RedisStore = require('redis-store');
const redis = require('redis');

const client = redis.createClient();

const advancedLimiter = rateLimit({
    store: new RedisStore({
        client: client,
        prefix: 'rl:'
    }),
    windowMs: 60 * 1000,     // 1 minute
    max: 50,                 // 50 requests per minute
    skip: (req) => {
        // Skip rate limiting for specific conditions
        return req.ip === '127.0.0.1';
    },
    keyGenerator: (req) => {
        // Custom key generation
        return req.ip + ':' + req.path;
    }
});

// Tiered rate limiting
const tieredLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: (req) => {
        // Different limits for different user types
        if (req.user?.isPremium) return 1000;
        if (req.user?.isVerified) return 500;
        return 100;
    }
});
```

## ⚡ **ASYNC/AWAIT OPTIMIZATION**

### **Best Practices cho Concurrency**

```javascript
// Sequential execution - CHẬM
async function sequential() {
    const result1 = await api1();
    const result2 = await api2();
    const result3 = await api3();
    return [result1, result2, result3];
}

// Parallel execution - NHANH
async function parallel() {
    const [result1, result2, result3] = await Promise.all([
        api1(),
        api2(),
        api3()
    ]);
    return [result1, result2, result3];
}

// Controlled concurrency
const pLimit = require('p-limit');
const limit = pLimit(3); // Giới hạn 3 concurrent requests

const results = await Promise.all(
    urls.map(url => limit(() => fetch(url)))
);

// Batch processing
async function processBatch(items, batchSize = 10) {
    const results = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        const batchResults = await Promise.all(
            batch.map(item => processItem(item))
        );
        results.push(...batchResults);
    }
    
    return results;
}
```

### **Error Handling Patterns**

```javascript
// Promise.allSettled cho error resilience
async function robustParallel() {
    const results = await Promise.allSettled([
        api1(),
        api2(),
        api3()
    ]);
    
    const successful = results
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value);
        
    const failed = results
        .filter(result => result.status === 'rejected')
        .map(result => result.reason);
        
    return { successful, failed };
}

// Retry mechanism with exponential backoff
async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await fn();
        } catch (error) {
            if (attempt === maxRetries) throw error;
            
            const delay = baseDelay * Math.pow(2, attempt - 1);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}
```

## 🚀 **PRODUCTION DEPLOYMENT BEST PRACTICES**

### **Environment Configuration**

```javascript
// Production optimizations
if (process.env.NODE_ENV === 'production') {
    // Enable production mode
    app.set('trust proxy', 1);
    
    // Security headers
    app.use(helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'"],
                imgSrc: ["'self'", "data:", "https:"],
            },
        },
        hsts: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true
        }
    }));
    
    // Compression
    app.use(compression({
        threshold: 1024,
        level: 6
    }));
    
    // Static file caching
    app.use(express.static('public', {
        maxAge: '1y',
        etag: true,
        lastModified: true
    }));
    
    // Request logging
    app.use(morgan('combined'));
}
```

### **Health Check & Monitoring Setup**

```javascript
// Health check endpoint
app.get('/health', (req, res) => {
    const health = {
        uptime: process.uptime(),
        message: 'OK',
        timestamp: Date.now(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        eventLoop: {
            lag: toobusy.lag(),
            maxLag: toobusy.maxLag()
        },
        v8: v8.getHeapStatistics(),
        environment: process.env.NODE_ENV,
        version: process.version
    };
    
    res.json(health);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    
    // Close server
    server.close(() => {
        console.log('HTTP server closed');
        
        // Close database connections
        pool.end(() => {
            console.log('Database connections closed');
            process.exit(0);
        });
    });
    
    // Force exit after timeout
    setTimeout(() => {
        console.error('Could not close connections in time, forcefully shutting down');
        process.exit(1);
    }, 10000);
});

// Uncaught exception handling
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
```

## 🧪 **PERFORMANCE TESTING & BENCHMARKING**

### **Load Testing với AutoCannon**

```bash
# Basic load testing
npx autocannon -d 30 -c 100 http://localhost:3000/api

# Advanced configuration
npx autocannon \
  --connections 100 \
  --duration 60 \
  --pipelining 10 \
  --renderStatusCodes \
  --method POST \
  --body '{"test": "data"}' \
  --headers '{"Content-Type": "application/json"}' \
  http://localhost:3000/api

# Custom scenarios
npx autocannon \
  --setup "curl -X POST http://localhost:3000/api/setup" \
  --teardown "curl -X DELETE http://localhost:3000/api/cleanup" \
  --connections 50 \
  --duration 30 \
  http://localhost:3000/api/test
```

### **Memory Leak Detection**

```javascript
// Heap snapshot analysis
const v8 = require('v8');
const fs = require('fs');

function takeHeapSnapshot() {
    const heapSnapshot = v8.writeHeapSnapshot();
    console.log('Heap snapshot written to', heapSnapshot);
    
    // Analyze snapshot size
    const stats = fs.statSync(heapSnapshot);
    console.log(`Snapshot size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
}

// Schedule periodic snapshots
setInterval(takeHeapSnapshot, 5 * 60 * 1000); // Every 5 minutes

// Memory leak detection
class MemoryLeakDetector {
    constructor() {
        this.snapshots = [];
        this.maxSnapshots = 10;
    }
    
    takeSnapshot() {
        const snapshot = {
            timestamp: Date.now(),
            memory: process.memoryUsage(),
            heapStats: v8.getHeapStatistics()
        };
        
        this.snapshots.push(snapshot);
        
        if (this.snapshots.length > this.maxSnapshots) {
            this.snapshots.shift();
        }
        
        this.analyzeTrend();
    }
    
    analyzeTrend() {
        if (this.snapshots.length < 2) return;
        
        const recent = this.snapshots[this.snapshots.length - 1];
        const previous = this.snapshots[this.snapshots.length - 2];
        
        const memoryGrowth = recent.memory.heapUsed - previous.memory.heapUsed;
        const timeDiff = recent.timestamp - previous.timestamp;
        
        if (memoryGrowth > 0 && timeDiff > 0) {
            const growthRate = memoryGrowth / timeDiff;
            console.log(`Memory growth rate: ${(growthRate * 1000).toFixed(2)} bytes/second`);
            
            if (growthRate > 1000) { // 1KB per second
                console.warn('⚠️ Potential memory leak detected!');
            }
        }
    }
}

const leakDetector = new MemoryLeakDetector();
setInterval(() => leakDetector.takeSnapshot(), 60000); // Every minute
```

## 🚀 **NODE.JS PERFORMANCE ROADMAP 2025**

### **Recent Improvements trong Node.js v22**

Node.js v22 mang lại những cải tiến đáng kể:
- **Test Runner**: Tăng 10% performance trong test creation
- **URL Parser**: Tối ưu hóa URL.resolve
- **WebStreams**: Cải thiện hơn 100% performance
- **Fetch API**: Tăng từ 2,246 lên 2,689 requests/second
- **ESM Performance**: Cải thiện module loading performance

### **Express.js Performance Optimizations**

**HTTP Headers Optimization:**
```javascript
// Efficient header handling
app.use((req, res, next) => {
    // Set common headers once
    res.set({
        'X-Powered-By': 'Node.js',
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
    });
    next();
});

// Content-Type shortcuts
res.type('json');    // => 'application/json'
res.type('html');    // => 'text/html'
res.type('xml');     // => 'text/xml'
```

**Redirect Performance:**
```javascript
// Efficient redirects
app.get('/old-url', (req, res) => {
    res.redirect(301, '/new-url'); // Permanent redirect for caching
});

// Conditional redirects
app.get('/mobile', (req, res) => {
    const userAgent = req.get('User-Agent');
    if (/mobile/i.test(userAgent)) {
        res.redirect('/mobile-app');
    } else {
        res.redirect('/desktop');
    }
});
```

**Template Engine Performance:**
```javascript
// Template caching
app.set('view cache', true);

// Pre-compile templates
const compiledTemplate = pug.compileFile('template.pug');
app.get('/', (req, res) => {
    const html = compiledTemplate({ data: req.data });
    res.send(html);
});
```

### **V8 Engine Updates**

V8 engine tiếp tục được cải tiến với:
- **JIT Compiler**: Cải thiện 30% startup time
- **Memory Management**: Tối ưu garbage collection
- **WebAssembly**: Native support tích hợp
- **Turbofan Optimizations**: Better code optimization

### **Future Performance Features**

- **ShadowRealm**: Secure JavaScript execution contexts
- **Import Maps**: Advanced module resolution
- **Web Crypto API**: Native cryptographic operations
- **Performance Hooks**: Enhanced performance monitoring

## 📊 **PERFORMANCE MONITORING DASHBOARD**

### **Key Metrics Dashboard**

```javascript
// Prometheus metrics for Node.js
const { Counter, Histogram, Gauge } = require('prom-client');

// Event Loop Metrics
export const eventLoopLag = new Histogram({
  name: 'nodejs_eventloop_lag_seconds',
  help: 'Event loop lag in seconds',
  buckets: [0.001, 0.01, 0.1, 0.5, 1, 2, 5],
});

export const activeHandles = new Gauge({
  name: 'nodejs_active_handles',
  help: 'Number of active handles',
});

export const activeRequests = new Gauge({
  name: 'nodejs_active_requests',
  help: 'Number of active requests',
});

// Garbage Collection Metrics
export const gcDuration = new Histogram({
  name: 'nodejs_gc_duration_seconds',
  help: 'Garbage collection duration in seconds',
  labelNames: ['type'],
  buckets: [0.001, 0.01, 0.1, 0.5, 1, 2, 5],
});

// Memory Usage Metrics
export const heapUsed = new Gauge({
  name: 'nodejs_heap_size_used_bytes',
  help: 'Used heap size in bytes',
});

export const heapTotal = new Gauge({
  name: 'nodejs_heap_size_total_bytes',
  help: 'Total heap size in bytes',
});

// Update metrics periodically
setInterval(() => {
  const memUsage = process.memoryUsage();
  heapUsed.set(memUsage.heapUsed);
  heapTotal.set(memUsage.heapTotal);
  
  activeHandles.set(process._getActiveHandles().length);
  activeRequests.set(process._getActiveRequests().length);
}, 5000);
```

## 🎯 **CONCLUSION**

Quản lý hiệu suất Node.js đòi hỏi hiểu biết toàn diện về kiến trúc runtime, memory management, và các kỹ thuật tối ưu hóa. Việc áp dụng đúng các chiến lược về clustering, worker threads, connection pooling, và monitoring sẽ giúp xây dựng ứng dụng Node.js có khả năng mở rộng và hiệu suất cao.

**Key Performance Takeaways:**
- Sử dụng clustering để tận dụng multi-core systems
- Implement connection pooling cho database operations
- Monitor event loop lag và memory usage liên tục
- Áp dụng compression và HTTP optimization
- Sử dụng worker threads cho CPU-intensive tasks
- Thực hiện rate limiting để bảo vệ ứng dụng
- Optimize async patterns với Promise.all và proper error handling
- Implement comprehensive monitoring và alerting
- Use memory-aware caching strategies
- Regular performance testing và profiling

Với sự phát triển liên tục của Node.js ecosystem, việc cập nhật và áp dụng các best practices mới sẽ đảm bảo ứng dụng của bạn luôn đạt hiệu suất tối ưu.

## 📚 **REFERENCES**

- [1] Node.js Architecture: Guide to Scalability & Performance
- [2] Node.js V8 Engine - W3Schools
- [3] Understanding the V8 Engine: Optimizing JavaScript for Peak Performance
- [4] Tuning Node.js and V8 settings to unlock 2x performance
- [5] Node.js Memory Management and Garbage Collection
- [6] Boost Node.js with V8 GC Optimization
- [7] Node.js Performance Monitoring
- [8] All You Need To Know About Node.js Profiling With Clinic.js
- [9] Scaling Node.js Applications with Clustering
- [10] Node.js Multithreading: A Beginner's Guide to Worker Threads
- [11] Best Practices for Buffer Management in Node.js
- [12] HTTP Compression in Node.js: Gzip, Deflate, and Brotli
- [13] Mastering Database Connection Pooling
- [14] State of Node.js Performance 2024
- [15] Node.js Performance Optimization Techniques
