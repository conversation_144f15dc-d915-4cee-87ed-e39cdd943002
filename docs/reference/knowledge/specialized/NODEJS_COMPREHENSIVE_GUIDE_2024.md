# 🚀 **NODE.JS COMPREHENSIVE GUIDE 2024**

> **Complete guide to Node.js development, performance optimization, and best practices**

## 📚 **OVERVIEW**

Node.js là một runtime JavaScript mạnh mẽ được xây dựng trên V8 engine của Google, thiết kế cho khả năng mở rộng cao và hiệu suất tối ưu. Node.js cho phép chạy JavaScript code "bên ngoài web browser", mở ra khả năng phát triển backend, desktop applications, và nhiều ứng dụng khác.

**Key Features:**
- **Server-side JavaScript**: Chạy JavaScript trên server thay vì chỉ trong browser
- **Event-driven & Non-blocking I/O**: Xử lý nhiều requests đồng thời một cách hiệu quả
- **Rich Ecosystem**: Hơn 1.5 triệu packages trên npm
- **Cross-platform**: Chạy trên Windows, macOS, Linux
- **Fast Development**: Sử dụng cùng ngôn ngữ cho frontend và backend

## 🏗️ **GETTING STARTED**

### **Installation**

1. Truy cập [https://nodejs.org/](https://nodejs.org/)
2. Tải phiên bản LTS (Long Term Support) cho hệ điều hành của bạn
3. Chạy installer và làm theo hướng dẫn
4. Kiểm tra cài đặt: `node -v` và `npm -v`

### **First Node.js Application**

```javascript
// app.js
console.log("Hello from Node.js!");

// Chạy với lệnh: node app.js
```

## 🌍 **GLOBAL VARIABLES**

Node.js cung cấp một số global variables quan trọng:

```javascript
// __dirname: Đường dẫn đến thư mục hiện tại
console.log(__dirname);

// __filename: Đường dẫn đến file hiện tại
console.log(__filename);

// global: Object chứa tất cả global variables
global.myVariable = 'Hello World';
console.log(myVariable); // Output: Hello World
```

## 📦 **MODULES SYSTEM**

### **Module Basics**

Modules giúp tổ chức code thành các phần nhỏ, có thể tái sử dụng:

```javascript
// hello.js - Module file
function sayHello(name) {
    console.log(`Hello ${name}`);
}

module.exports = sayHello;

// app.js - Main file
const sayHello = require('./hello.js');
sayHello('John'); // Output: Hello John
```

### **Module.exports Deep Dive**

```javascript
// myModule.js
function myFunction1() {
    console.log('Hello from myFunction1!');
}

function myFunction2() {
    console.log('Hello from myFunction2!');
}

// Export multiple functions
module.exports = {
    foo: 'bar',
    myFunction1: myFunction1,
    myFunction2: myFunction2
};

// app.js
const myModule = require('./myModule');
console.log(myModule.foo); // logs 'bar'
myModule.myFunction1(); // logs 'Hello from myFunction1!'
myModule.myFunction2(); // logs 'Hello from myFunction2!'
```

### **Types of Modules**

#### **1. Built-in Modules**

**OS Module:**
```javascript
const os = require('os');

const systemInfo = {
    uptime: os.uptime(),
    userInfo: os.userInfo(),
    platform: os.type(),
    release: os.release(),
    totalMem: os.totalmem(),
    freeMem: os.freemem()
};

console.log(systemInfo);
```

**Path Module:**
```javascript
const path = require('path');

const myPath = '/mnt/c/Desktop/NodeJSTut/app.js';

const pathInfo = {
    fileName: path.basename(myPath),        // 'app.js'
    folderName: path.dirname(myPath),       // '/mnt/c/Desktop/NodeJSTut'
    fileExtension: path.extname(myPath),    // '.js'
    absoluteOrNot: path.isAbsolute(myPath), // true
    detailInfo: path.parse(myPath)          // Detailed path breakdown
};

// Cross-platform path joining
console.log(path.join('folder1', 'folder2', 'file.txt'));
// Windows: folder1\folder2\file.txt
// Unix: folder1/folder2/file.txt
```

**FS Module:**
```javascript
const fs = require('fs');

// Create directory
fs.mkdir('./myFolder', (err) => {
    if (err) {
        console.log(err);
    } else {
        console.log('Folder Created Successfully');
    }
});

// Write file asynchronously
const data = "Hi, this is newFile.txt";
fs.writeFile('./myFolder/myFile.txt', data, (err) => {
    if (err) {
        console.log(err);
        return;
    }
    console.log('Written to file successfully!');
});

// Write file with append flag
fs.writeFile('./myFolder/myFile.txt', data, {flag: 'a'}, (err) => {
    if (err) {
        console.log(err);
        return;
    }
    console.log('Appended to file successfully!');
});

// Read file asynchronously
fs.readFile('./myFolder/myFile.txt', {encoding: 'utf-8'}, (err, data) => {
    if (err) {
        console.log(err);
        return;
    }
    console.log('File read successfully! Here is the data:');
    console.log(data);
});

// Synchronous operations
try {
    // Write to file synchronously
    fs.writeFileSync('./myFolder/myFileSync.txt', 'myFileSync says Hi');
    console.log('Write operation successful');

    // Read file synchronously
    const fileData = fs.readFileSync('./myFolder/myFileSync.txt', 'utf-8');
    console.log('Read operation successful. Here is the data:');
    console.log(fileData);
} catch (err) {
    console.log('Error occurred!');
    console.log(err);
}

// Read directory contents
fs.readdir('./myFolder', (err, files) => {
    if (err) {
        console.log(err);
        return;
    }
    console.log('Directory read successfully! Here are the files:');
    console.log(files);
});

// Rename file
fs.rename('./myFolder/newFile.txt', './myFolder/newFileAsync.txt', (err) => {
    if (err) {
        console.log(err);
        return;
    }
    console.log('File renamed successfully!');
});

// Delete file
fs.unlink('./myFolder/myFileSync.txt', (err) => {
    if (err) {
        console.log(err);
        return;
    }
    console.log('File Deleted Successfully!');
});
```

## 🔄 **EVENT-DRIVEN PROGRAMMING**

Event-driven programming là paradigm nơi luồng chương trình được xác định bởi events thay vì logic chương trình.

### **Event Emitter**

```javascript
const EventEmitter = require('events');
const myEmitter = new EventEmitter();

// Listener function
const welcomeUser = () => {
    console.log('Hi There, Welcome to the server!');
};

// Listen for userJoined event
myEmitter.on('userJoined', welcomeUser);

// Emit the userJoined event
myEmitter.emit('userJoined');
```

### **Multiple Listeners**

```javascript
const EventEmitter = require('events');
const myEmitter = new EventEmitter();

// Multiple listener functions
const sayHello = () => console.log('Hello User');
const sayHi = () => console.log('Hi User');
const greetNewYear = () => console.log('Happy New Year!');

// Subscribe to userJoined event
myEmitter.on('userJoined', sayHello);
myEmitter.on('userJoined', sayHi);
myEmitter.on('userJoined', greetNewYear);

// Emit the event
myEmitter.emit('userJoined');
// Output:
// Hello User
// Hi User
// Happy New Year!
```

### **Event with Parameters**

```javascript
const EventEmitter = require('events');
const myEmitter = new EventEmitter();

const greetBirthday = (name, newAge) => {
    console.log(`Happy Birthday ${name}. You are now ${newAge}!`);
};

myEmitter.on('birthdayEvent', greetBirthday);
myEmitter.emit('birthdayEvent', 'John', '24');
// Output: Happy Birthday John. You are now 24!
```

**Important Notes:**
1. Có thể có nhiều `on()` cho một `emit()`
2. `emit()` có thể chứa arguments được truyền cho listener functions
3. `emit()` phải được định nghĩa sau tất cả `on()` functions

## 🌐 **HTTP MODULE & WEB SERVERS**

### **HTTP Basics**

HTTP (Hypertext Transfer Protocol) được sử dụng để truyền dữ liệu qua internet, cho phép giao tiếp giữa clients và servers.

**Request-Response Components:**
1. **Status Line**: Chứa method, URL, protocol version
2. **Headers**: Key-value pairs với thông tin bổ sung
3. **Body**: Dữ liệu thực tế được gửi/nhận

### **HTTP Methods**

- **GET**: Lấy resource từ server
- **POST**: Thêm resource vào server
- **PUT**: Cập nhật resource hiện có
- **DELETE**: Xóa resource từ server

### **HTTP Status Codes**

- **200**: OK - Request thành công
- **201**: Created - Resource được tạo
- **400**: Bad Request - Request không hợp lệ
- **401**: Unauthorized - Không có quyền truy cập
- **404**: Not Found - Resource không tồn tại
- **500**: Internal Server Error - Lỗi server

### **Creating a Basic Server**

```javascript
const http = require('http');

const server = http.createServer((req, res) => {
    res.end('Hello World');
});

server.listen(5000, () => {
    console.log('Server listening at port 5000');
});
```

### **Advanced Server with Routing**

```javascript
const http = require('http');

const server = http.createServer((req, res) => {
    if (req.url === '/') {
        res.writeHead(200, {'content-type': 'text/html'});
        res.write('<h1>Home Page</h1>');
        res.end();
    } else if (req.url === '/about') {
        res.writeHead(200, {'content-type': 'text/html'});
        res.write('<h1>About Page</h1>');
        res.end();
    } else if (req.url === '/contact') {
        res.writeHead(200, {'content-type': 'text/html'});
        res.write('<h1>Contact Page</h1>');
        res.end();
    } else {
        res.writeHead(404, {'content-type': 'text/html'});
        res.write('<h1>404, Resource Not Found <a href="/">Go Back Home</a></h1>');
        res.end();
    }
});

server.listen(5000, () => {
    console.log('Server listening at port 5000');
});
```

### **Response Methods**

1. **`res.writeHead()`**: Gửi response headers (status code, content-type)
2. **`res.write()`**: Gửi response body
3. **`res.end()`**: Kết thúc response process

**Content Types:**
- `text/html`: HTML content
- `application/json`: JSON data
- `text/css`: CSS code
- `text/javascript`: JavaScript code
- `text/plain`: Plain text

## 🚀 **PERFORMANCE OPTIMIZATION**

### **V8 Engine & JIT Compilation**

Node.js sử dụng V8 engine với Just-In-Time compilation:

```javascript
// Kiểm tra V8 version và memory
console.log(`V8 version: ${process.versions.v8}`);

const v8 = require('v8');
const heapStats = v8.getHeapStatistics();
console.log('Heap size limit:', (heapStats.heap_size_limit / 1024 / 1024).toFixed(2), 'MB');
console.log('Used heap size:', (heapStats.used_heap_size / 1024 / 1024).toFixed(2), 'MB');
```

### **Memory Management**

```bash
# Tăng Old Space size
node --max-old-space-size=4096 app.js

# Tối ưu GC
node --optimize-for-size app.js
node --gc-interval=100 app.js
```

### **Clustering for Multi-Core**

```javascript
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
    console.log(`Master ${process.pid} is running`);
    
    // Fork workers
    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }
    
    cluster.on('exit', (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} died`);
        cluster.fork(); // Restart worker
    });
} else {
    // Worker process
    require('./app.js');
    console.log(`Worker ${process.pid} started`);
}
```

### **Worker Threads for CPU-Intensive Tasks**

```javascript
const { Worker } = require('worker_threads');

class WorkerPool {
    constructor(poolSize, workerScript) {
        this.poolSize = poolSize;
        this.workerScript = workerScript;
        this.workers = [];
        this.queue = [];
        
        this.initializeWorkers();
    }
    
    initializeWorkers() {
        for (let i = 0; i < this.poolSize; i++) {
            const worker = new Worker(this.workerScript);
            worker.busy = false;
            this.workers.push(worker);
        }
    }
    
    execute(data) {
        return new Promise((resolve, reject) => {
            const availableWorker = this.workers.find(w => !w.busy);
            
            if (availableWorker) {
                this.runTask(availableWorker, data, resolve, reject);
            } else {
                this.queue.push({ data, resolve, reject });
            }
        });
    }
    
    runTask(worker, data, resolve, reject) {
        worker.busy = true;
        
        worker.once('message', (result) => {
            worker.busy = false;
            resolve(result);
            this.processQueue();
        });
        
        worker.once('error', (error) => {
            worker.busy = false;
            reject(error);
            this.processQueue();
        });
        
        worker.postMessage(data);
    }
    
    processQueue() {
        if (this.queue.length > 0) {
            const { data, resolve, reject } = this.queue.shift();
            const availableWorker = this.workers.find(w => !w.busy);
            if (availableWorker) {
                this.runTask(availableWorker, data, resolve, reject);
            }
        }
    }
}
```

### **Streams & Buffer Optimization**

```javascript
const fs = require('fs');

// Efficient file handling
const readStream = fs.createReadStream('largefile.txt', {
    highWaterMark: 64 * 1024 // 64KB buffer
});

const writeStream = fs.createWriteStream('output.txt');
readStream.pipe(writeStream);

// Buffer pooling
class BufferPool {
    constructor(poolSize = 10, bufferSize = 64 * 1024) {
        this.pool = [];
        this.poolSize = poolSize;
        this.bufferSize = bufferSize;
        this.initializePool();
    }
    
    initializePool() {
        for (let i = 0; i < this.poolSize; i++) {
            this.pool.push(Buffer.alloc(this.bufferSize));
        }
    }
    
    getBuffer() {
        return this.pool.pop() || Buffer.alloc(this.bufferSize);
    }
    
    returnBuffer(buffer) {
        if (this.pool.length < this.poolSize) {
            buffer.fill(0);
            this.pool.push(buffer);
        }
    }
}
```

### **HTTP Server Optimization**

```javascript
const express = require('express');
const app = express();

// Keep-Alive configuration
const server = app.listen(3000);
server.keepAliveTimeout = 30000; // 30 seconds
server.headersTimeout = 31000;   // 31 seconds

// Compression middleware
const compression = require('compression');
app.use(compression({
    threshold: 1024,        // Only compress responses > 1KB
    level: 6,               // Compression level (0-9)
    memLevel: 8             // Memory usage for compression
}));
```

### **Database Connection Pooling**

```javascript
const { Pool } = require('pg');

const pool = new Pool({
    user: 'username',
    host: 'localhost',
    database: 'dbname',
    password: 'password',
    port: 5432,
    max: 20,                    // Maximum connections
    idleTimeoutMillis: 30000,   // Close idle connections
    connectionTimeoutMillis: 2000,
    allowExitOnIdle: true,      // Allow process to exit when idle
    maxUses: 7500               // Close connections after 7500 queries
});

// Connection pool monitoring
pool.on('connect', (client) => {
    console.log('New client connected to pool');
});

pool.on('error', (err, client) => {
    console.error('Unexpected error on idle client', err);
});
```

### **Async/Await Optimization**

```javascript
// Sequential execution - CHẬM
async function sequential() {
    const result1 = await api1();
    const result2 = await api2();
    const result3 = await api3();
    return [result1, result2, result3];
}

// Parallel execution - NHANH
async function parallel() {
    const [result1, result2, result3] = await Promise.all([
        api1(),
        api2(),
        api3()
    ]);
    return [result1, result2, result3];
}

// Controlled concurrency
const pLimit = require('p-limit');
const limit = pLimit(3); // Giới hạn 3 concurrent requests

const results = await Promise.all(
    urls.map(url => limit(() => fetch(url)))
);

// Error handling with Promise.allSettled
async function robustParallel() {
    const results = await Promise.allSettled([
        api1(),
        api2(),
        api3()
    ]);
    
    const successful = results
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value);
        
    const failed = results
        .filter(result => result.status === 'rejected')
        .map(result => result.reason);
        
    return { successful, failed };
}
```

## 🔒 **SECURITY & BEST PRACTICES**

### **Rate Limiting**

```javascript
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,                 // 100 requests per window
    message: 'Too many requests',
    standardHeaders: true,
    legacyHeaders: false
});

app.use('/api', limiter);
```

### **Security Headers**

```javascript
const helmet = require('helmet');

app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    }
}));
```

## 📊 **MONITORING & PROFILING**

### **Performance Monitoring**

```javascript
// Health check endpoint
app.get('/health', (req, res) => {
    const health = {
        uptime: process.uptime(),
        message: 'OK',
        timestamp: Date.now(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        environment: process.env.NODE_ENV,
        version: process.version
    };
    
    res.json(health);
});
```

### **Clinic.js Profiling**

```bash
npm install -g clinic

# CPU Profiling
clinic doctor -- node app.js
clinic flame -- node app.js

# Memory Profiling
clinic heap -- node app.js

# Async Operations Profiling
clinic bubbleprof -- node app.js
```

### **Load Testing**

```bash
# Basic load testing
npx autocannon -d 30 -c 100 http://localhost:3000/api

# Advanced configuration
npx autocannon \
  --connections 100 \
  --duration 60 \
  --pipelining 10 \
  --renderStatusCodes \
  http://localhost:3000/api
```

## 🚀 **PRODUCTION DEPLOYMENT**

### **Environment Configuration**

```javascript
if (process.env.NODE_ENV === 'production') {
    app.set('trust proxy', 1);
    
    // Security headers
    app.use(helmet());
    
    // Compression
    app.use(compression({
        threshold: 1024,
        level: 6
    }));
    
    // Static file caching
    app.use(express.static('public', {
        maxAge: '1y',
        etag: true,
        lastModified: true
    }));
}
```

### **Graceful Shutdown**

```javascript
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    
    server.close(() => {
        console.log('HTTP server closed');
        
        pool.end(() => {
            console.log('Database connections closed');
            process.exit(0);
        });
    });
    
    setTimeout(() => {
        console.error('Could not close connections in time, forcefully shutting down');
        process.exit(1);
    }, 10000);
});
```

## 🎯 **CONCLUSION**

Node.js cung cấp một nền tảng mạnh mẽ để xây dựng các ứng dụng server-side với JavaScript. Với kiến trúc event-driven, non-blocking I/O, và ecosystem phong phú, Node.js là lựa chọn tuyệt vời cho:

- **Web Applications**: REST APIs, real-time applications
- **Microservices**: Lightweight, scalable services
- **CLI Tools**: Command-line utilities
- **Desktop Applications**: Electron-based apps
- **IoT Applications**: Device communication

**Key Takeaways:**
- Sử dụng modules để tổ chức code
- Hiểu event-driven programming
- Tối ưu hóa performance với clustering và worker threads
- Implement security best practices
- Monitor và profile ứng dụng
- Sử dụng connection pooling cho database
- Implement graceful shutdown

## 📚 **REFERENCES**

- [1] Node.js Official Documentation
- [2] FreeCodeCamp Node.js Handbook
- [3] Node.js Performance Best Practices
- [4] V8 Engine Documentation
- [5] Express.js Framework Guide
- [6] Node.js Security Best Practices
- [7] Performance Monitoring Tools
- [8] Production Deployment Guide