# Database Engineering Mastery Guide

## <PERSON><PERSON><PERSON>n lý Cốt lõi Bất biến của Database Engineering

### Triết lý Database Engineer
**"Data is the new oil, but refined data is the new gold"**

Ba kết luận then chốt:
- **Database là trái tim của mọi hệ thống**: Từ ACID properties đến distributed systems
- **SQL là ngôn ngữ vạn năng**: 80% logic business có thể được triển khai thuần SQL
- **Linux + Database = Sức mạnh tối thượng**: Kết hợp OS level đến application level

### 12 Nguyên tắc Thiết kế Bất biến

1. **Normalization First, Denormalization by Need**: Bắt đầu với 3NF, denormalize có lý do
2. **Index Strategy**: Composite index > Multiple single indexes
3. **Query Optimization**: WHERE > JOIN > Subquery > Scalar functions
4. **Data Integrity**: Constraints + Triggers + Stored Procedures
5. **Security by Design**: Principle of least privilege + encryption
6. **Backup Everything**: Full + Incremental + Differential + Point-in-time
7. **Monitor Continuously**: Proactive alerts > Reactive fixes
8. **Document Relentlessly**: Schema, queries, procedures, troubleshooting
9. **Test Thoroughly**: Unit tests cho stored procedures, performance tests
10. **Scale Horizontally**: Sharding + Replication + Caching
11. **Automate Repetitive**: ETL, backups, monitoring, deployments
12. **Evolve Gradually**: Migration scripts + Rollback plans + Blue-green deployment

## ACID Properties - Chân lý bất biến

### Atomicity: "All or Nothing"
```sql
BEGIN TRANSACTION;
UPDATE accounts SET balance = balance - 1000 WHERE account_id = 1;
UPDATE accounts SET balance = balance + 1000 WHERE account_id = 2;
-- Nếu bất kỳ statement nào fail → toàn bộ transaction rollback
COMMIT;
```

### Consistency: Database luôn ở trạng thái valid
```sql
-- Constraint đảm bảo consistency
ALTER TABLE orders ADD CONSTRAINT chk_total
CHECK (total_amount >= 0);
```

### Isolation: Concurrent transactions không ảnh hưởng nhau
```sql
-- Isolation levels: READ UNCOMMITTED < READ COMMITTED < REPEATABLE READ < SERIALIZABLE
SET TRANSACTION ISOLATION LEVEL REPEATABLE READ;
```

### Durability: Committed data tồn tại vĩnh viễn
- WAL (Write-Ahead Logging) + fsync đảm bảo durability

## Advanced SQL Mastery

### Window Functions - Analytical Powerhouse
```sql
-- Running totals
SELECT
    date,
    sales,
    SUM(sales) OVER (ORDER BY date) AS running_total,
    AVG(sales) OVER (ORDER BY date ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) AS moving_avg_7day
FROM sales_data;

-- Ranking and percentiles
SELECT
    employee_id,
    salary,
    RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as salary_rank,
    NTILE(4) OVER (ORDER BY salary) as salary_quartile,
    LAG(salary, 1) OVER (ORDER BY hire_date) as prev_hire_salary
FROM employees;
```

### Common Table Expressions (CTEs) - Recursive Power
```sql
-- Recursive CTE for hierarchical data
WITH RECURSIVE org_chart AS (
    -- Anchor member
    SELECT employee_id, name, manager_id, 0 as level,
           CAST(name AS VARCHAR(1000)) as path
    FROM employees
    WHERE manager_id IS NULL
    
    UNION ALL
    
    -- Recursive member
    SELECT e.employee_id, e.name, e.manager_id, oc.level + 1,
           oc.path + ' -> ' + e.name
    FROM employees e
    JOIN org_chart oc ON e.manager_id = oc.employee_id
)
SELECT * FROM org_chart ORDER BY level, path;
```

### Complex Stored Procedure Pattern
```sql
DELIMITER //
CREATE PROCEDURE ProcessMonthlyReports(
    IN p_year INT,
    IN p_month INT,
    OUT p_total_sales DECIMAL(15,2),
    OUT p_status VARCHAR(50)
)
BEGIN
    DECLARE v_error_count INT DEFAULT 0;
    DECLARE v_temp_total DECIMAL(15,2) DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_status = 'ERROR: Transaction rolled back';
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Create temporary aggregation table
    CREATE TEMPORARY TABLE temp_monthly_sales AS
    SELECT
        product_id,
        SUM(quantity) as total_qty,
        SUM(unit_price * quantity) as total_sales
    FROM orders o
    JOIN order_items oi ON o.order_id = oi.order_id
    WHERE YEAR(o.order_date) = p_year
      AND MONTH(o.order_date) = p_month
    GROUP BY product_id;
    
    -- Update product statistics
    UPDATE products p
    JOIN temp_monthly_sales tms ON p.product_id = tms.product_id
    SET p.monthly_sales = tms.total_sales,
        p.monthly_units = tms.total_qty,
        p.last_updated = NOW();
    
    -- Calculate total sales
    SELECT SUM(total_sales) INTO v_temp_total FROM temp_monthly_sales;
    SET p_total_sales = v_temp_total;
    
    -- Error checking
    SELECT COUNT(*) INTO v_error_count
    FROM products
    WHERE monthly_sales < 0;
    
    IF v_error_count > 0 THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Negative sales detected';
    END IF;
    
    COMMIT;
    SET p_status = 'SUCCESS';
    DROP TEMPORARY TABLE temp_monthly_sales;
END //
DELIMITER ;
```

## Database Design Patterns

### 1. Normalization Pattern - Consistency First
```sql
-- 3NF normalized design
CREATE TABLE customers (
    customer_id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE addresses (
    address_id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES customers(customer_id),
    street VARCHAR(255),
    city VARCHAR(100),
    is_primary BOOLEAN DEFAULT FALSE
);
```

### 2. Star Schema Pattern - Analytics Powerhouse
```sql
-- Fact table (center of star)
CREATE TABLE sales_fact (
    sale_id BIGINT PRIMARY KEY,
    product_key INTEGER,
    customer_key INTEGER,
    date_key INTEGER,
    quantity INTEGER,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(15,2)
);

-- Dimension tables (points of star)
CREATE TABLE product_dim (
    product_key INTEGER PRIMARY KEY,
    product_id VARCHAR(50),
    product_name VARCHAR(255),
    category VARCHAR(100),
    brand VARCHAR(100)
);
```

### 3. EAV Pattern - Ultimate Flexibility
```sql
-- Entity-Attribute-Value for dynamic attributes
CREATE TABLE entities (
    entity_id SERIAL PRIMARY KEY,
    entity_type VARCHAR(50)
);

CREATE TABLE attributes (
    attribute_id SERIAL PRIMARY KEY,
    attribute_name VARCHAR(100),
    data_type VARCHAR(20)
);

CREATE TABLE entity_attributes (
    entity_id INTEGER REFERENCES entities(entity_id),
    attribute_id INTEGER REFERENCES attributes(attribute_id),
    value TEXT,
    PRIMARY KEY (entity_id, attribute_id)
);
```

## Performance Tuning Mastery

### Index Strategy Master Formula
```sql
-- Composite index order: Equality → Range → Order By
CREATE INDEX idx_orders_optimized ON orders (
    status,        -- Equality filter (WHERE status = 'completed')
    customer_id,   -- Equality filter (WHERE customer_id = 123)
    order_date,    -- Range filter (WHERE order_date >= '2024-01-01')
    total_amount   -- Order by clause (ORDER BY total_amount DESC)
);

-- Covering index - include all columns needed
CREATE INDEX idx_orders_covering ON orders (customer_id, status)
INCLUDE (order_date, total_amount, shipping_address);
```

### Query Rewriting Patterns
```sql
-- BAD: Scalar subquery in SELECT
SELECT c.customer_id, c.name,
       (SELECT COUNT(*) FROM orders o WHERE o.customer_id = c.customer_id) as order_count
FROM customers c;

-- GOOD: JOIN with aggregation
SELECT c.customer_id, c.name, COALESCE(o.order_count, 0) as order_count
FROM customers c
LEFT JOIN (
    SELECT customer_id, COUNT(*) as order_count
    FROM orders
    GROUP BY customer_id
) o ON c.customer_id = o.customer_id;
```

## Linux Integration for Database Administration

### Database Management với Linux
```bash
# MySQL administration
mysql -u root -p -e "SHOW PROCESSLIST;"
mysqldump --single-transaction --routines --triggers mydb > backup.sql
mysql mydb < backup.sql

# PostgreSQL administration
psql -U postgres -c "SELECT * FROM pg_stat_activity;"
pg_dump -U postgres -Fc mydb > mydb.dump
pg_restore -U postgres -d mydb mydb.dump

# Performance monitoring
iostat -x 1 | grep -E "(dm-|sd)"
sar -u 1 10  # CPU utilization
sar -r 1 10  # Memory usage
```

### Database-specific Linux Tuning
```bash
# Memory optimization
echo 'vm.swappiness = 1' >> /etc/sysctl.conf
echo 'kernel.shmmax = 68719476736' >> /etc/sysctl.conf

# I/O optimization
echo 'deadline' > /sys/block/sda/queue/scheduler
tune2fs -o journal_data_writeback /dev/sda1

# Network optimization for database connections
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
```

## Database Security Framework

### 10-Point Security Checklist
1. **Physical Security**: Data center access, hardware protection
2. **Network Security**: Firewall, VPN, encryption in transit
3. **Authentication**: Strong passwords, MFA, certificate-based auth
4. **Authorization**: Role-based access, principle of least privilege
5. **Data Encryption**: At rest + in transit + key management
6. **Audit Logging**: All access attempts, changes, admin actions
7. **Backup Security**: Encrypted backups, secure storage
8. **Patch Management**: Regular updates, vulnerability assessment
9. **Database Firewall**: SQL injection protection, query filtering
10. **Incident Response**: Detection, containment, recovery, forensics

### SQL Injection Prevention
```sql
-- BAD: String concatenation (vulnerable)
SET @sql = CONCAT('SELECT * FROM users WHERE username = "', @username, '"');

-- GOOD: Parameterized queries
PREPARE stmt FROM 'SELECT * FROM users WHERE username = ?';
SET @username = 'john_doe';
EXECUTE stmt USING @username;
DEALLOCATE PREPARE stmt;
```

## Backup & Recovery Strategies

### 3-2-1 Rule Implementation
- **3 copies** of important data
- **2 different storage media**
- **1 offsite location**

### Backup Types & Schedule
```bash
# Full backup (weekly - Sunday 2 AM)
BACKUP_DIR="/backup/mysql/full"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump --single-transaction --routines --triggers \
    --all-databases --flush-logs --lock-all-tables \
    > ${BACKUP_DIR}/full_backup_${DATE}.sql

# Compress and encrypt
gzip ${BACKUP_DIR}/full_backup_${DATE}.sql
gpg --cipher-algo AES256 --compress-algo 1 --symmetric \
    ${BACKUP_DIR}/full_backup_${DATE}.sql.gz
```

Để trở thành Database Expert, cần thành thạo **6 trụ cột**:
1. **Database Fundamentals**: ACID properties, CAP theorem, architectural patterns
2. **SQL Mastery**: Advanced queries, window functions, stored procedures, optimization
3. **System Design**: Normalization/denormalization, indexing strategies, partitioning
4. **Operations Excellence**: Monitoring, security, backup/recovery, troubleshooting
5. **Linux Integration**: Command-line tools, performance tuning, automation scripts
6. **Modern Technologies**: Cloud databases, AI integration, real-time analytics, containers
