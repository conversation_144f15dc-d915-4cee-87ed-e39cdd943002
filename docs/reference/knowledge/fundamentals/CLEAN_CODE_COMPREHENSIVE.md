# 🧹 **CLEAN CODE - TÀI LIỆU TOÀN DIỆN**

> **Hướng dẫn chi tiết về việc viết code sạch, d<PERSON> đọc, d<PERSON> bảo trì và có thể mở rộng**

[![Clean Code](https://img.shields.io/badge/Clean_Code-Best_Practices-brightgreen)](README.md)
[![SOLID](https://img.shields.io/badge/SOLID-Principles-blue)](../principles/solid-principles.md)
[![Testing](https://img.shields.io/badge/Testing-TDD-yellow)](../../advanced/quality-assurance/README.md)

## 📖 **MỤC LỤC**

1. [Giới thiệu về Clean Code](#-giới-thiệu-về-clean-code)
2. [Nguyên tắc cơ bản](#-nguyên-tắc-cơ-bản)
3. [Đặt tên có ý nghĩa](#-đặt-tên-có-ý-nghĩa)
4. [Viết hàm tốt](#-viết-hàm-tốt)
5. [Comment hiệu quả](#-comment-hiệu-quả)
6. [Định dạng code](#-định-dạng-code)
7. [Xử lý lỗi](#-xử-lý-lỗi)
8. [Kiểm thử](#-kiểm-thử)
9. [Refactoring](#-refactoring)
10. [Nguyên tắc SOLID](#-nguyên-tắc-solid)
11. [Hiệu suất và bảo mật](#-hiệu-suất-và-bảo-mật)
12. [Ví dụ thực tế](#-ví-dụ-thực-tế)

---

## 🎯 **Giới thiệu về Clean Code**

### **🔍 Clean Code là gì?**

**Clean Code** là code được viết theo cách dễ đọc, dễ hiểu, dễ bảo trì và có thể mở rộng. Nó không chỉ hoạt động đúng mà còn truyền đạt ý định của lập trình viên một cách rõ ràng.

### **📊 Đặc điểm của Clean Code**

| Đặc điểm | Mô tả | Lợi ích |
|----------|-------|---------|
| **Dễ đọc** | Code như văn xuôi, dễ hiểu | Tiết kiệm thời gian đọc hiểu |
| **Đơn giản** | Không phức tạp không cần thiết | Giảm bugs, dễ debug |
| **Có mục đích** | Mỗi dòng code có lý do tồn tại | Tránh code thừa |
| **Nhất quán** | Cùng một phong cách xuyên suốt | Dễ làm việc nhóm |
| **Có thể kiểm thử** | Dễ viết unit test | Đảm bảo chất lượng |

### **🚫 Tác hại của Bad Code**

- **Technical Debt**: Chi phí bảo trì tăng theo thời gian
- **Productivity Loss**: Team làm việc chậm hơn
- **Bug Prone**: Dễ sinh ra lỗi mới
- **Hard to Scale**: Khó mở rộng tính năng
- **Developer Frustration**: Team stress, burnout

---

## 💡 **Nguyên tắc cơ bản**

### **🎯 DRY - Don't Repeat Yourself**

**Nguyên tắc**: Không lặp lại logic, dữ liệu hoặc cấu trúc

```typescript
// ❌ BAD: Lặp lại logic
function calculateDiscountForVIP(price: number): number {
  if (price > 1000) {
    return price * 0.8; // 20% discount
  }
  return price * 0.9; // 10% discount
}

function calculateDiscountForRegular(price: number): number {
  if (price > 1000) {
    return price * 0.9; // 10% discount
  }
  return price * 0.95; // 5% discount
}

// ✅ GOOD: Tái sử dụng logic
interface DiscountTier {
  threshold: number;
  highDiscount: number;
  lowDiscount: number;
}

const DISCOUNT_TIERS: Record<string, DiscountTier> = {
  VIP: { threshold: 1000, highDiscount: 0.8, lowDiscount: 0.9 },
  REGULAR: { threshold: 1000, highDiscount: 0.9, lowDiscount: 0.95 }
};

function calculateDiscount(price: number, customerType: string): number {
  const tier = DISCOUNT_TIERS[customerType];
  if (!tier) throw new Error(`Unknown customer type: ${customerType}`);
  
  const discount = price > tier.threshold ? tier.highDiscount : tier.lowDiscount;
  return price * discount;
}
```

### **🎯 KISS - Keep It Simple, Stupid**

**Nguyên tắc**: Giữ cho mọi thứ đơn giản nhất có thể

```typescript
// ❌ BAD: Phức tạp không cần thiết
function isValidEmail(email: string): boolean {
  const parts = email.split('@');
  if (parts.length !== 2) return false;
  
  const localPart = parts[0];
  const domainPart = parts[1];
  
  if (localPart.length === 0 || domainPart.length === 0) return false;
  if (localPart.length > 64 || domainPart.length > 255) return false;
  
  const localRegex = /^[a-zA-Z0-9._%+-]+$/;
  const domainRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  
  return localRegex.test(localPart) && domainRegex.test(domainPart);
}

// ✅ GOOD: Đơn giản và hiệu quả
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```

### **🎯 YAGNI - You Aren't Gonna Need It**

**Nguyên tắc**: Chỉ implement những gì cần thiết ngay bây giờ

```typescript
// ❌ BAD: Over-engineering
interface User {
  id: string;
  name: string;
  email: string;
  // Các field có thể sẽ cần trong tương lai
  phone?: string;
  address?: Address;
  preferences?: UserPreferences;
  socialMedia?: SocialMediaLinks;
  subscription?: Subscription;
}

class UserService {
  // Các method có thể sẽ cần
  async updatePhone(userId: string, phone: string): Promise<void> { /* ... */ }
  async updateAddress(userId: string, address: Address): Promise<void> { /* ... */ }
  async updatePreferences(userId: string, prefs: UserPreferences): Promise<void> { /* ... */ }
  // ... 20+ methods khác
}

// ✅ GOOD: Chỉ implement những gì cần
interface User {
  id: string;
  name: string;
  email: string;
}

class UserService {
  async createUser(userData: Omit<User, 'id'>): Promise<User> { /* ... */ }
  async getUserById(id: string): Promise<User | null> { /* ... */ }
  async updateUser(id: string, updates: Partial<User>): Promise<User> { /* ... */ }
  async deleteUser(id: string): Promise<void> { /* ... */ }
}
```

---

## 🏷️ **Đặt tên có ý nghĩa**

### **📝 Nguyên tắc đặt tên**

#### **1. Sử dụng tên có ý nghĩa và mục đích rõ ràng**

```typescript
// ❌ BAD: Tên không có ý nghĩa
let d: number; // ngày?
let u: User[];
function calc(x: number, y: number): number { return x * y * 0.1; }

// ✅ GOOD: Tên có ý nghĩa rõ ràng
let daysSinceLastModification: number;
let activeUsers: User[];
function calculateDiscountAmount(price: number, discountRate: number): number {
  return price * discountRate * 0.1;
}
```

#### **2. Tránh thông tin sai lệch**

```typescript
// ❌ BAD: Tên gây hiểu lầm
let userList: Set<User>; // Không phải list mà là Set
let isValid = false; // Boolean nhưng có thể chứa string

// ✅ GOOD: Tên chính xác
let userSet: Set<User>;
let users: User[];
let validationResult: boolean;
```

#### **3. Sử dụng tên có thể phát âm được**

```typescript
// ❌ BAD: Tên khó đọc
let usrMgmtSys: UserManagementSystem;
let calcPrcWithDscnt: (price: number, discount: number) => number;

// ✅ GOOD: Tên dễ đọc
let userManagementSystem: UserManagementSystem;
let calculatePriceWithDiscount: (price: number, discount: number) => number;
```

#### **4. Sử dụng tên có thể tìm kiếm**

```typescript
// ❌ BAD: Magic numbers và tên ngắn
for (let i = 0; i < 34; i++) {
  s += (t[i] * 4) / 5;
}

// ✅ GOOD: Constants và tên có ý nghĩa
const WORK_DAYS_PER_WEEK = 5;
const DAYS_PER_MONTH = 34;

let sum = 0;
for (let dayIndex = 0; dayIndex < DAYS_PER_MONTH; dayIndex++) {
  sum += (taskHours[dayIndex] * 4) / WORK_DAYS_PER_WEEK;
}
```

### **🎯 Convention đặt tên theo ngôn ngữ**

#### **TypeScript/JavaScript**

```typescript
// Variables & Functions: camelCase
const userName = 'john_doe';
const isUserActive = true;
function calculateTotalPrice() { }

// Classes: PascalCase
class UserService { }
class PaymentProcessor { }

// Constants: SCREAMING_SNAKE_CASE
const MAX_RETRY_ATTEMPTS = 3;
const API_BASE_URL = 'https://api.example.com';

// Interfaces: PascalCase với prefix I (optional)
interface User { }
interface IUserRepository { } // Optional I prefix

// Enums: PascalCase
enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  MODERATOR = 'moderator'
}
```

#### **Python**

```python
# Variables & Functions: snake_case
user_name = 'john_doe'
is_user_active = True

def calculate_total_price():
    pass

# Classes: PascalCase
class UserService:
    pass

class PaymentProcessor:
    pass

# Constants: SCREAMING_SNAKE_CASE
MAX_RETRY_ATTEMPTS = 3
API_BASE_URL = 'https://api.example.com'

# Private: prefix with underscore
def _private_method():
    pass

_private_variable = 'secret'
```

#### **Go**

```go
// Public: PascalCase (exported)
type UserService struct {}
func CalculateTotalPrice() {}

// Private: camelCase (unexported)
type userRepository struct {}
func calculateDiscount() {}

// Constants: camelCase or PascalCase
const maxRetryAttempts = 3
const APIBaseURL = "https://api.example.com"
```

---

## ⚙️ **Viết hàm tốt**

### **📏 Kích thước hàm**

**Nguyên tắc**: Hàm nên nhỏ, càng nhỏ càng tốt

```typescript
// ❌ BAD: Hàm quá lớn
function processOrder(order: Order): OrderResult {
  // Validate order (20 lines)
  if (!order.customerId) throw new Error('Missing customer ID');
  if (!order.items || order.items.length === 0) throw new Error('No items');
  for (let item of order.items) {
    if (!item.productId) throw new Error('Missing product ID');
    if (item.quantity <= 0) throw new Error('Invalid quantity');
    // ... more validation
  }
  
  // Calculate total (15 lines)
  let subtotal = 0;
  for (let item of order.items) {
    const product = getProduct(item.productId);
    subtotal += product.price * item.quantity;
  }
  const tax = subtotal * 0.1;
  const shipping = subtotal > 100 ? 0 : 10;
  const total = subtotal + tax + shipping;
  
  // Process payment (25 lines)
  // ... payment logic
  
  // Update inventory (20 lines)
  // ... inventory logic
  
  // Send notifications (15 lines)
  // ... notification logic
  
  return { success: true, orderId: generateId() };
}

// ✅ GOOD: Chia nhỏ thành các hàm
function processOrder(order: Order): OrderResult {
  validateOrder(order);
  const pricing = calculateOrderPricing(order);
  const payment = processPayment(order, pricing.total);
  updateInventory(order.items);
  sendOrderNotifications(order, payment);
  
  return {
    success: true,
    orderId: generateId(),
    total: pricing.total
  };
}

function validateOrder(order: Order): void {
  if (!order.customerId) throw new Error('Missing customer ID');
  if (!order.items?.length) throw new Error('No items in order');
  
  for (const item of order.items) {
    validateOrderItem(item);
  }
}

function validateOrderItem(item: OrderItem): void {
  if (!item.productId) throw new Error('Missing product ID');
  if (item.quantity <= 0) throw new Error('Invalid quantity');
}

function calculateOrderPricing(order: Order): OrderPricing {
  const subtotal = calculateSubtotal(order.items);
  const tax = calculateTax(subtotal);
  const shipping = calculateShipping(subtotal);
  
  return {
    subtotal,
    tax,
    shipping,
    total: subtotal + tax + shipping
  };
}
```

### **🎯 Single Responsibility cho hàm**

**Nguyên tắc**: Một hàm chỉ nên làm một việc

```typescript
// ❌ BAD: Hàm làm nhiều việc
function saveUserAndSendEmail(userData: UserData): void {
  // Validate data
  if (!userData.email) throw new Error('Email required');
  
  // Save to database
  const user = database.users.create(userData);
  
  // Send welcome email
  emailService.send({
    to: user.email,
    subject: 'Welcome!',
    body: generateWelcomeEmail(user.name)
  });
  
  // Log activity
  logger.info(`User created: ${user.id}`);
}

// ✅ GOOD: Mỗi hàm một trách nhiệm
function createUser(userData: UserData): User {
  validateUserData(userData);
  return saveUserToDatabase(userData);
}

function sendWelcomeEmail(user: User): void {
  const emailContent = generateWelcomeEmail(user.name);
  emailService.send({
    to: user.email,
    subject: 'Welcome!',
    body: emailContent
  });
}

function logUserCreation(user: User): void {
  logger.info(`User created: ${user.id}`);
}

// Orchestrator function
async function registerNewUser(userData: UserData): Promise<User> {
  const user = createUser(userData);
  
  // Async operations không block nhau
  await Promise.all([
    sendWelcomeEmail(user),
    logUserCreation(user)
  ]);
  
  return user;
}
```

### **📝 Tham số hàm**

**Nguyên tắc**: Hạn chế số lượng tham số, tối đa 3-4 tham số

```typescript
// ❌ BAD: Quá nhiều tham số
function createUser(
  name: string,
  email: string,
  phone: string,
  address: string,
  city: string,
  country: string,
  zipCode: string,
  isActive: boolean,
  role: string,
  department: string
): User {
  // ...
}

// ✅ GOOD: Sử dụng object parameter
interface CreateUserParams {
  personalInfo: {
    name: string;
    email: string;
    phone: string;
  };
  address: {
    street: string;
    city: string;
    country: string;
    zipCode: string;
  };
  accountInfo: {
    isActive: boolean;
    role: UserRole;
    department: string;
  };
}

function createUser(params: CreateUserParams): User {
  const { personalInfo, address, accountInfo } = params;
  // ...
}

// Hoặc sử dụng builder pattern
class UserBuilder {
  private user: Partial<User> = {};
  
  withPersonalInfo(name: string, email: string, phone: string): UserBuilder {
    this.user.name = name;
    this.user.email = email;
    this.user.phone = phone;
    return this;
  }
  
  withAddress(street: string, city: string, country: string, zipCode: string): UserBuilder {
    this.user.address = { street, city, country, zipCode };
    return this;
  }
  
  withAccountInfo(isActive: boolean, role: UserRole, department: string): UserBuilder {
    this.user.isActive = isActive;
    this.user.role = role;
    this.user.department = department;
    return this;
  }
  
  build(): User {
    if (!this.isValid()) {
      throw new Error('Invalid user data');
    }
    return this.user as User;
  }
  
  private isValid(): boolean {
    return !!(this.user.name && this.user.email);
  }
}

// Usage
const user = new UserBuilder()
  .withPersonalInfo('John Doe', '<EMAIL>', '************')
  .withAddress('123 Main St', 'Anytown', 'USA', '12345')
  .withAccountInfo(true, UserRole.USER, 'Engineering')
  .build();
```

### **🔄 Tránh side effects**

```typescript
// ❌ BAD: Side effects không mong muốn
let globalCounter = 0;

function calculateTotal(items: Item[]): number {
  globalCounter++; // Side effect!
  
  let total = 0;
  for (const item of items) {
    total += item.price;
    item.processed = true; // Mutating input!
  }
  
  return total;
}

// ✅ GOOD: Pure function
function calculateTotal(items: readonly Item[]): number {
  return items.reduce((total, item) => total + item.price, 0);
}

function markItemsAsProcessed(items: Item[]): Item[] {
  return items.map(item => ({ ...item, processed: true }));
}

// Separate concerns
function incrementCounter(): number {
  return ++globalCounter;
}
```

---

## 💬 **Comment hiệu quả**

### **🎯 Khi nào nên comment**

#### **✅ Comment tốt**

```typescript
// Giải thích thuật toán phức tạp
function quickSort(arr: number[]): number[] {
  if (arr.length <= 1) return arr;
  
  // Chọn pivot là phần tử giữa để tránh worst case với mảng đã sắp xếp
  const pivotIndex = Math.floor(arr.length / 2);
  const pivot = arr[pivotIndex];
  
  const less = arr.filter((x, i) => i !== pivotIndex && x < pivot);
  const equal = arr.filter((x, i) => i !== pivotIndex && x === pivot);
  const greater = arr.filter((x, i) => i !== pivotIndex && x > pivot);
  
  return [...quickSort(less), pivot, ...equal, ...quickSort(greater)];
}

// Giải thích business rules
function calculateInsurancePremium(age: number, riskFactor: number): number {
  // Theo quy định của bộ tài chính, phí bảo hiểm tối thiểu là 100,000 VND
  const MIN_PREMIUM = 100000;
  
  // Formula được phê duyệt bởi hội đồng quản trị ngày 2024-01-15
  // Premium = (age * 1000) + (riskFactor * 50000) + base
  const calculatedPremium = (age * 1000) + (riskFactor * 50000) + MIN_PREMIUM;
  
  return Math.max(calculatedPremium, MIN_PREMIUM);
}

// Warning về side effects
/**
 * CẢNH BÁO: Function này sẽ thay đổi global state
 * Chỉ sử dụng trong error handling hoặc shutdown sequence
 */
function resetApplicationState(): void {
  clearCache();
  closeConnections();
  resetCounters();
}

// TODO comments
// TODO: Implement caching mechanism for frequently accessed data
// FIXME: Memory leak when processing large files
// HACK: Temporary workaround for API rate limiting
```

#### **❌ Comment xấu**

```typescript
// Comment giải thích code rõ ràng
let i = 0; // set i to 0
i++; // increment i
if (user.age >= 18) { // check if user is adult
  // user is adult
}

// Comment lỗi thời
// This function returns user name (actually returns full user object)
function getUser(id: string): User {
  return userRepository.findById(id);
}

// Comment thay thế cho code tốt
// Check if user is admin or moderator
if (user.role === 'admin' || user.role === 'moderator') {
  // Should be: if (user.hasAdminPrivileges())
}

// Commented out code
function calculateDiscount(price: number): number {
  // const oldWay = price * 0.1;
  // return Math.min(oldWay, 100);
  
  return Math.min(price * 0.15, 150);
}
```

### **📚 Documentation Comments**

```typescript
/**
 * Calculates the compound interest for an investment
 * 
 * @param principal - The initial amount invested
 * @param rate - The annual interest rate (as decimal, e.g., 0.05 for 5%)
 * @param time - The number of years
 * @param compoundFrequency - How many times per year interest is compounded
 * @returns The final amount after compound interest
 * 
 * @example
 * ```typescript
 * const result = calculateCompoundInterest(1000, 0.05, 10, 12);
 * console.log(result); // 1643.62
 * ```
 * 
 * @throws {Error} When any parameter is negative or zero
 * @since 1.0.0
 */
function calculateCompoundInterest(
  principal: number,
  rate: number,
  time: number,
  compoundFrequency: number
): number {
  if (principal <= 0 || rate < 0 || time <= 0 || compoundFrequency <= 0) {
    throw new Error('All parameters must be positive numbers');
  }
  
  return principal * Math.pow(1 + rate / compoundFrequency, compoundFrequency * time);
}
```

---

## 🎨 **Định dạng code**

### **📏 Vertical Formatting**

```typescript
// ✅ GOOD: Nhóm các khái niệm liên quan
class UserService {
  private userRepository: UserRepository;
  private emailService: EmailService;
  private logger: Logger;

  constructor(
    userRepository: UserRepository,
    emailService: EmailService,
    logger: Logger
  ) {
    this.userRepository = userRepository;
    this.emailService = emailService;
    this.logger = logger;
  }

  // Public methods
  async createUser(userData: CreateUserData): Promise<User> {
    this.validateUserData(userData);
    const user = await this.saveUser(userData);
    await this.sendWelcomeEmail(user);
    this.logUserCreation(user);
    return user;
  }

  async getUserById(id: string): Promise<User | null> {
    return this.userRepository.findById(id);
  }

  // Private methods
  private validateUserData(userData: CreateUserData): void {
    if (!userData.email) {
      throw new Error('Email is required');
    }
    if (!userData.name) {
      throw new Error('Name is required');
    }
  }

  private async saveUser(userData: CreateUserData): Promise<User> {
    return this.userRepository.create(userData);
  }

  private async sendWelcomeEmail(user: User): Promise<void> {
    try {
      await this.emailService.sendWelcomeEmail(user.email, user.name);
    } catch (error) {
      this.logger.error('Failed to send welcome email', { userId: user.id, error });
    }
  }

  private logUserCreation(user: User): void {
    this.logger.info('User created successfully', { userId: user.id });
  }
}
```

### **📐 Horizontal Formatting**

```typescript
// ✅ GOOD: Alignment và spacing
const userConfig = {
  database: {
    host    : process.env.DB_HOST || 'localhost',
    port    : parseInt(process.env.DB_PORT || '5432'),
    name    : process.env.DB_NAME || 'userdb',
    username: process.env.DB_USER || 'user',
    password: process.env.DB_PASS || 'password'
  },
  
  email: {
    service  : process.env.EMAIL_SERVICE || 'smtp',
    host     : process.env.EMAIL_HOST || 'localhost',
    port     : parseInt(process.env.EMAIL_PORT || '587'),
    secure   : process.env.EMAIL_SECURE === 'true',
    username : process.env.EMAIL_USER,
    password : process.env.EMAIL_PASS
  }
};

// Method chaining
const processedData = inputData
  .filter(item => item.isActive)
  .map(item => transformItem(item))
  .sort((a, b) => a.priority - b.priority)
  .slice(0, 10);

// Function parameters
function createPaymentProcessor(
  paymentGateway: PaymentGateway,
  fraudDetection: FraudDetectionService,
  auditLogger: AuditLogger,
  notificationService: NotificationService
): PaymentProcessor {
  // ...
}
```

### **🔧 Team Conventions**

```typescript
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}

// .eslintrc.json
{
  "extends": ["@typescript-eslint/recommended"],
  "rules": {
    "max-len": ["error", { "code": 100 }],
    "no-multiple-empty-lines": ["error", { "max": 2 }],
    "no-trailing-spaces": "error",
    "eol-last": "error",
    "comma-dangle": ["error", "always-multiline"],
    "quotes": ["error", "single"],
    "semi": ["error", "always"]
  }
}
```

---

## ⚠️ **Xử lý lỗi**

### **🛡️ Fail Fast Principle**

```typescript
// ✅ GOOD: Validate early, fail fast
function processPayment(payment: PaymentRequest): PaymentResult {
  // Validate input immediately
  if (!payment) {
    throw new Error('Payment request is required');
  }
  
  if (!payment.amount || payment.amount <= 0) {
    throw new Error('Payment amount must be positive');
  }
  
  if (!payment.currency || !SUPPORTED_CURRENCIES.includes(payment.currency)) {
    throw new Error(`Unsupported currency: ${payment.currency}`);
  }
  
  if (!payment.paymentMethod) {
    throw new Error('Payment method is required');
  }
  
  // Process only when all validations pass
  return processValidPayment(payment);
}
```

### **🎯 Exception Types**

```typescript
// Custom error classes
abstract class AppError extends Error {
  abstract readonly statusCode: number;
  abstract readonly isOperational: boolean;
  
  constructor(message: string, public readonly context?: Record<string, any>) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  readonly statusCode = 400;
  readonly isOperational = true;
}

class NotFoundError extends AppError {
  readonly statusCode = 404;
  readonly isOperational = true;
}

class InternalError extends AppError {
  readonly statusCode = 500;
  readonly isOperational = false;
}

// Usage
function getUserById(id: string): User {
  if (!id) {
    throw new ValidationError('User ID is required');
  }
  
  const user = userRepository.findById(id);
  if (!user) {
    throw new NotFoundError('User not found', { userId: id });
  }
  
  return user;
}
```

### **🔄 Error Handling Patterns**

```typescript
// Result pattern
type Result<T, E = Error> = {
  success: true;
  data: T;
} | {
  success: false;
  error: E;
};

function safeParseJson<T>(json: string): Result<T> {
  try {
    const data = JSON.parse(json) as T;
    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error('Unknown error') 
    };
  }
}

// Usage
const result = safeParseJson<User>(userJson);
if (result.success) {
  console.log('User:', result.data);
} else {
  console.error('Parse error:', result.error.message);
}

// Maybe pattern
class Maybe<T> {
  constructor(private value: T | null | undefined) {}
  
  static of<T>(value: T | null | undefined): Maybe<T> {
    return new Maybe(value);
  }
  
  static none<T>(): Maybe<T> {
    return new Maybe<T>(null);
  }
  
  isNone(): boolean {
    return this.value == null;
  }
  
  isSome(): boolean {
    return this.value != null;
  }
  
  map<U>(fn: (value: T) => U): Maybe<U> {
    if (this.isNone()) {
      return Maybe.none<U>();
    }
    return Maybe.of(fn(this.value!));
  }
  
  flatMap<U>(fn: (value: T) => Maybe<U>): Maybe<U> {
    if (this.isNone()) {
      return Maybe.none<U>();
    }
    return fn(this.value!);
  }
  
  getOrElse(defaultValue: T): T {
    return this.isNone() ? defaultValue : this.value!;
  }
}

// Usage
function findUserEmail(userId: string): Maybe<string> {
  const user = userRepository.findById(userId);
  return Maybe.of(user?.email);
}

const email = findUserEmail('123')
  .map(email => email.toLowerCase())
  .getOrElse('<EMAIL>');
```

---

## ✅ **Kiểm thử**

### **🔺 Test Pyramid**

```typescript
// Unit Tests (70-80%)
describe('UserValidator', () => {
  let validator: UserValidator;
  
  beforeEach(() => {
    validator = new UserValidator();
  });
  
  describe('validateEmail', () => {
    it('should return valid for correct email format', () => {
      const result = validator.validateEmail('<EMAIL>');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    
    it('should return invalid for missing @', () => {
      const result = validator.validateEmail('userexample.com');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid email format');
    });
    
    it('should return invalid for empty email', () => {
      const result = validator.validateEmail('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Email is required');
    });
  });
});

// Integration Tests (15-25%)
describe('UserService Integration', () => {
  let userService: UserService;
  let testDb: TestDatabase;
  
  beforeAll(async () => {
    testDb = await TestDatabase.create();
    userService = new UserService(testDb.getUserRepository());
  });
  
  afterAll(async () => {
    await testDb.cleanup();
  });
  
  beforeEach(async () => {
    await testDb.clearTables();
  });
  
  it('should create user and save to database', async () => {
    const userData = {
      name: 'John Doe',
      email: '<EMAIL>'
    };
    
    const user = await userService.createUser(userData);
    
    expect(user.id).toBeDefined();
    expect(user.name).toBe(userData.name);
    expect(user.email).toBe(userData.email);
    
    // Verify saved in database
    const savedUser = await userService.getUserById(user.id);
    expect(savedUser).toEqual(user);
  });
});

// E2E Tests (5-10%)
describe('User Management E2E', () => {
  it('should complete full user registration flow', async () => {
    // Start browser
    const browser = await puppeteer.launch();
    const page = await browser.newPage();
    
    try {
      // Navigate to registration page
      await page.goto('http://localhost:3000/register');
      
      // Fill form
      await page.type('#name', 'John Doe');
      await page.type('#email', '<EMAIL>');
      await page.type('#password', 'StrongPassword123!');
      
      // Submit form
      await page.click('#submit-button');
      
      // Wait for success message
      await page.waitForSelector('.success-message');
      const message = await page.$eval('.success-message', el => el.textContent);
      expect(message).toContain('Registration successful');
      
      // Verify redirect to dashboard
      await page.waitForNavigation();
      expect(page.url()).toContain('/dashboard');
      
    } finally {
      await browser.close();
    }
  });
});
```

### **🧪 Test Best Practices**

```typescript
// AAA Pattern: Arrange, Act, Assert
describe('calculateDiscount', () => {
  it('should apply 10% discount for regular customers', () => {
    // Arrange
    const price = 100;
    const customerType = 'regular';
    const expected = 90;
    
    // Act
    const result = calculateDiscount(price, customerType);
    
    // Assert
    expect(result).toBe(expected);
  });
});

// Test naming convention
describe('OrderService', () => {
  describe('when processing valid order', () => {
    it('should return success result with order ID', () => {
      // Test implementation
    });
    
    it('should update inventory for all items', () => {
      // Test implementation
    });
    
    it('should send confirmation email to customer', () => {
      // Test implementation
    });
  });
  
  describe('when order validation fails', () => {
    it('should throw ValidationError for missing customer ID', () => {
      // Test implementation
    });
    
    it('should throw ValidationError for empty items list', () => {
      // Test implementation
    });
  });
});

// Mocking external dependencies
describe('EmailService', () => {
  let emailService: EmailService;
  let mockSmtpTransporter: jest.Mocked<SmtpTransporter>;
  
  beforeEach(() => {
    mockSmtpTransporter = {
      sendMail: jest.fn(),
      verify: jest.fn()
    } as any;
    
    emailService = new EmailService(mockSmtpTransporter);
  });
  
  it('should send email with correct parameters', async () => {
    const emailData = {
      to: '<EMAIL>',
      subject: 'Test',
      body: 'Test body'
    };
    
    mockSmtpTransporter.sendMail.mockResolvedValue({ messageId: '123' });
    
    await emailService.sendEmail(emailData);
    
    expect(mockSmtpTransporter.sendMail).toHaveBeenCalledWith({
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.body
    });
  });
});
```

### **📊 Test Coverage**

```typescript
// jest.config.js
module.exports = {
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/core/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },
  collectCoverageFrom: [
    'src/**/*.{ts,js}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{ts,js}',
    '!src/test-utils/**'
  ]
};
```

---

## 🔄 **Refactoring**

### **👃 Code Smells**

#### **1. Duplicate Code**

```typescript
// ❌ SMELL: Code duplication
class OrderEmailService {
  sendOrderConfirmation(order: Order): void {
    const emailTemplate = `
      <h1>Order Confirmation</h1>
      <p>Dear ${order.customerName},</p>
      <p>Your order #${order.id} has been confirmed.</p>
      <p>Total: $${order.total}</p>
      <p>Best regards,<br>Sales Team</p>
    `;
    
    this.emailService.send({
      to: order.customerEmail,
      subject: `Order Confirmation #${order.id}`,
      html: emailTemplate
    });
  }
  
  sendOrderShipped(order: Order): void {
    const emailTemplate = `
      <h1>Order Shipped</h1>
      <p>Dear ${order.customerName},</p>
      <p>Your order #${order.id} has been shipped.</p>
      <p>Total: $${order.total}</p>
      <p>Best regards,<br>Sales Team</p>
    `;
    
    this.emailService.send({
      to: order.customerEmail,
      subject: `Order Shipped #${order.id}`,
      html: emailTemplate
    });
  }
}

// ✅ REFACTORED: Extract common template
class OrderEmailService {
  private generateEmailTemplate(
    title: string,
    message: string,
    order: Order
  ): string {
    return `
      <h1>${title}</h1>
      <p>Dear ${order.customerName},</p>
      <p>${message}</p>
      <p>Order #${order.id} - Total: $${order.total}</p>
      <p>Best regards,<br>Sales Team</p>
    `;
  }
  
  private sendOrderEmail(
    order: Order,
    subject: string,
    title: string,
    message: string
  ): void {
    const emailTemplate = this.generateEmailTemplate(title, message, order);
    
    this.emailService.send({
      to: order.customerEmail,
      subject: `${subject} #${order.id}`,
      html: emailTemplate
    });
  }
  
  sendOrderConfirmation(order: Order): void {
    this.sendOrderEmail(
      order,
      'Order Confirmation',
      'Order Confirmation',
      'Your order has been confirmed.'
    );
  }
  
  sendOrderShipped(order: Order): void {
    this.sendOrderEmail(
      order,
      'Order Shipped',
      'Order Shipped',
      'Your order has been shipped.'
    );
  }
}
```

#### **2. Long Methods**

```typescript
// ❌ SMELL: Method quá dài
class ReportGenerator {
  generateSalesReport(startDate: Date, endDate: Date): SalesReport {
    // Validate dates (5 lines)
    if (startDate > endDate) throw new Error('Invalid date range');
    if (startDate > new Date()) throw new Error('Start date cannot be in future');
    
    // Get sales data (10 lines)
    const salesData = this.salesRepository.findByDateRange(startDate, endDate);
    let totalRevenue = 0;
    let totalOrders = salesData.length;
    let uniqueCustomers = new Set();
    
    for (const sale of salesData) {
      totalRevenue += sale.amount;
      uniqueCustomers.add(sale.customerId);
    }
    
    // Calculate averages (8 lines)
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const averageRevenuePerCustomer = uniqueCustomers.size > 0 
      ? totalRevenue / uniqueCustomers.size : 0;
    
    // Group by product (15 lines)
    const productSales = new Map();
    for (const sale of salesData) {
      for (const item of sale.items) {
        if (productSales.has(item.productId)) {
          productSales.set(item.productId, {
            quantity: productSales.get(item.productId).quantity + item.quantity,
            revenue: productSales.get(item.productId).revenue + item.total
          });
        } else {
          productSales.set(item.productId, {
            quantity: item.quantity,
            revenue: item.total
          });
        }
      }
    }
    
    // Sort top products (10 lines)
    const topProducts = Array.from(productSales.entries())
      .sort((a, b) => b[1].revenue - a[1].revenue)
      .slice(0, 10)
      .map(([productId, data]) => ({
        productId,
        quantity: data.quantity,
        revenue: data.revenue
      }));
    
    return {
      period: { startDate, endDate },
      totalRevenue,
      totalOrders,
      uniqueCustomers: uniqueCustomers.size,
      averageOrderValue,
      averageRevenuePerCustomer,
      topProducts
    };
  }
}

// ✅ REFACTORED: Chia nhỏ thành các methods
class ReportGenerator {
  generateSalesReport(startDate: Date, endDate: Date): SalesReport {
    this.validateDateRange(startDate, endDate);
    
    const salesData = this.getSalesData(startDate, endDate);
    const metrics = this.calculateMetrics(salesData);
    const topProducts = this.getTopProducts(salesData);
    
    return {
      period: { startDate, endDate },
      ...metrics,
      topProducts
    };
  }
  
  private validateDateRange(startDate: Date, endDate: Date): void {
    if (startDate > endDate) {
      throw new Error('Start date must be before end date');
    }
    if (startDate > new Date()) {
      throw new Error('Start date cannot be in future');
    }
  }
  
  private getSalesData(startDate: Date, endDate: Date): Sale[] {
    return this.salesRepository.findByDateRange(startDate, endDate);
  }
  
  private calculateMetrics(salesData: Sale[]): SalesMetrics {
    const totalRevenue = salesData.reduce((sum, sale) => sum + sale.amount, 0);
    const totalOrders = salesData.length;
    const uniqueCustomers = new Set(salesData.map(sale => sale.customerId)).size;
    
    return {
      totalRevenue,
      totalOrders,
      uniqueCustomers,
      averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0,
      averageRevenuePerCustomer: uniqueCustomers > 0 ? totalRevenue / uniqueCustomers : 0
    };
  }
  
  private getTopProducts(salesData: Sale[]): ProductSales[] {
    const productSales = this.groupSalesByProduct(salesData);
    return this.sortAndLimitProducts(productSales, 10);
  }
  
  private groupSalesByProduct(salesData: Sale[]): Map<string, ProductSalesData> {
    const productSales = new Map<string, ProductSalesData>();
    
    for (const sale of salesData) {
      for (const item of sale.items) {
        const existing = productSales.get(item.productId);
        productSales.set(item.productId, {
          quantity: (existing?.quantity || 0) + item.quantity,
          revenue: (existing?.revenue || 0) + item.total
        });
      }
    }
    
    return productSales;
  }
  
  private sortAndLimitProducts(
    productSales: Map<string, ProductSalesData>,
    limit: number
  ): ProductSales[] {
    return Array.from(productSales.entries())
      .sort((a, b) => b[1].revenue - a[1].revenue)
      .slice(0, limit)
      .map(([productId, data]) => ({
        productId,
        quantity: data.quantity,
        revenue: data.revenue
      }));
  }
}
```

#### **3. Large Classes**

```typescript
// ❌ SMELL: God class
class UserManager {
  // User CRUD operations
  createUser(userData: UserData): User { /* ... */ }
  updateUser(id: string, updates: Partial<User>): User { /* ... */ }
  deleteUser(id: string): void { /* ... */ }
  getUserById(id: string): User | null { /* ... */ }
  
  // Authentication
  login(email: string, password: string): AuthResult { /* ... */ }
  logout(token: string): void { /* ... */ }
  validateToken(token: string): boolean { /* ... */ }
  refreshToken(token: string): string { /* ... */ }
  
  // Password management
  changePassword(userId: string, oldPassword: string, newPassword: string): void { /* ... */ }
  resetPassword(email: string): void { /* ... */ }
  validatePassword(password: string): ValidationResult { /* ... */ }
  
  // Email operations
  sendWelcomeEmail(user: User): void { /* ... */ }
  sendPasswordResetEmail(email: string): void { /* ... */ }
  sendAccountActivationEmail(user: User): void { /* ... */ }
  
  // User preferences
  updatePreferences(userId: string, preferences: UserPreferences): void { /* ... */ }
  getPreferences(userId: string): UserPreferences { /* ... */ }
  
  // Analytics and reporting
  getUserStatistics(): UserStats { /* ... */ }
  generateUserReport(startDate: Date, endDate: Date): UserReport { /* ... */ }
  trackUserActivity(userId: string, activity: Activity): void { /* ... */ }
}

// ✅ REFACTORED: Chia thành nhiều classes chuyên biệt
class UserService {
  constructor(
    private userRepository: UserRepository,
    private userValidator: UserValidator
  ) {}
  
  async createUser(userData: UserData): Promise<User> {
    this.userValidator.validate(userData);
    return this.userRepository.create(userData);
  }
  
  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    this.userValidator.validateUpdates(updates);
    return this.userRepository.update(id, updates);
  }
  
  async deleteUser(id: string): Promise<void> {
    await this.userRepository.delete(id);
  }
  
  async getUserById(id: string): Promise<User | null> {
    return this.userRepository.findById(id);
  }
}

class AuthenticationService {
  constructor(
    private userRepository: UserRepository,
    private tokenService: TokenService,
    private passwordService: PasswordService
  ) {}
  
  async login(email: string, password: string): Promise<AuthResult> {
    const user = await this.userRepository.findByEmail(email);
    if (!user || !await this.passwordService.verify(password, user.passwordHash)) {
      throw new Error('Invalid credentials');
    }
    
    const token = this.tokenService.generate(user.id);
    return { user, token };
  }
  
  async logout(token: string): Promise<void> {
    await this.tokenService.revoke(token);
  }
  
  validateToken(token: string): boolean {
    return this.tokenService.validate(token);
  }
  
  async refreshToken(token: string): Promise<string> {
    return this.tokenService.refresh(token);
  }
}

class PasswordService {
  async changePassword(
    userId: string,
    oldPassword: string,
    newPassword: string
  ): Promise<void> {
    const user = await this.userRepository.findById(userId);
    if (!user) throw new Error('User not found');
    
    if (!await this.verify(oldPassword, user.passwordHash)) {
      throw new Error('Invalid current password');
    }
    
    this.validatePasswordStrength(newPassword);
    const newPasswordHash = await this.hash(newPassword);
    await this.userRepository.updatePassword(userId, newPasswordHash);
  }
  
  async resetPassword(email: string): Promise<void> {
    const user = await this.userRepository.findByEmail(email);
    if (!user) return; // Don't reveal if email exists
    
    const resetToken = this.generateResetToken();
    await this.userRepository.savePasswordResetToken(user.id, resetToken);
    await this.emailService.sendPasswordResetEmail(user.email, resetToken);
  }
  
  validatePasswordStrength(password: string): ValidationResult {
    // Password validation logic
  }
  
  async hash(password: string): Promise<string> {
    // Password hashing logic
  }
  
  async verify(password: string, hash: string): Promise<boolean> {
    // Password verification logic
  }
}

class UserEmailService {
  constructor(private emailService: EmailService) {}
  
  async sendWelcomeEmail(user: User): Promise<void> {
    const template = this.getWelcomeTemplate(user);
    await this.emailService.send({
      to: user.email,
      subject: 'Welcome to our platform!',
      html: template
    });
  }
  
  async sendPasswordResetEmail(email: string, resetToken: string): Promise<void> {
    const template = this.getPasswordResetTemplate(resetToken);
    await this.emailService.send({
      to: email,
      subject: 'Reset your password',
      html: template
    });
  }
  
  async sendAccountActivationEmail(user: User): Promise<void> {
    const template = this.getActivationTemplate(user);
    await this.emailService.send({
      to: user.email,
      subject: 'Activate your account',
      html: template
    });
  }
}

class UserPreferencesService {
  constructor(private userRepository: UserRepository) {}
  
  async updatePreferences(
    userId: string,
    preferences: UserPreferences
  ): Promise<void> {
    await this.userRepository.updatePreferences(userId, preferences);
  }
  
  async getPreferences(userId: string): Promise<UserPreferences> {
    const user = await this.userRepository.findById(userId);
    if (!user) throw new Error('User not found');
    return user.preferences || this.getDefaultPreferences();
  }
}

class UserAnalyticsService {
  constructor(
    private userRepository: UserRepository,
    private activityRepository: ActivityRepository
  ) {}
  
  async getUserStatistics(): Promise<UserStats> {
    // Analytics logic
  }
  
  async generateUserReport(startDate: Date, endDate: Date): Promise<UserReport> {
    // Report generation logic
  }
  
  async trackUserActivity(userId: string, activity: Activity): Promise<void> {
    await this.activityRepository.create({
      userId,
      ...activity,
      timestamp: new Date()
    });
  }
}
```

### **🔧 Refactoring Techniques**

#### **Extract Method**

```typescript
// Before: Long method with complex logic
function processOrder(order: Order): void {
  // Validation
  if (!order.customerId) throw new Error('Customer ID required');
  if (!order.items || order.items.length === 0) throw new Error('No items');
  
  // Calculate pricing
  let subtotal = 0;
  for (const item of order.items) {
    subtotal += item.price * item.quantity;
  }
  const tax = subtotal * 0.1;
  const shipping = subtotal > 100 ? 0 : 10;
  const total = subtotal + tax + shipping;
  
  // Update inventory
  for (const item of order.items) {
    const product = inventory.getProduct(item.productId);
    if (product.stock < item.quantity) {
      throw new Error(`Insufficient stock for ${product.name}`);
    }
    inventory.reduceStock(item.productId, item.quantity);
  }
  
  // Save order
  order.total = total;
  orderRepository.save(order);
}

// After: Extracted methods
function processOrder(order: Order): void {
  validateOrder(order);
  const total = calculateOrderTotal(order);
  updateInventory(order.items);
  saveOrder(order, total);
}

function validateOrder(order: Order): void {
  if (!order.customerId) throw new Error('Customer ID required');
  if (!order.items?.length) throw new Error('No items');
}

function calculateOrderTotal(order: Order): number {
  const subtotal = order.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const tax = subtotal * 0.1;
  const shipping = subtotal > 100 ? 0 : 10;
  return subtotal + tax + shipping;
}

function updateInventory(items: OrderItem[]): void {
  for (const item of items) {
    const product = inventory.getProduct(item.productId);
    if (product.stock < item.quantity) {
      throw new Error(`Insufficient stock for ${product.name}`);
    }
    inventory.reduceStock(item.productId, item.quantity);
  }
}

function saveOrder(order: Order, total: number): void {
  order.total = total;
  orderRepository.save(order);
}
```

#### **Replace Magic Numbers với Constants**

```typescript
// Before: Magic numbers
function calculateShipping(weight: number, distance: number): number {
  if (weight <= 1) {
    return distance * 0.5;
  } else if (weight <= 5) {
    return distance * 0.75;
  } else {
    return distance * 1.0;
  }
  
  if (distance > 1000) {
    return total * 1.2; // 20% surcharge
  }
  
  return total;
}

// After: Named constants
const SHIPPING_RATES = {
  LIGHT: 0.5,    // <= 1kg
  MEDIUM: 0.75,  // <= 5kg
  HEAVY: 1.0     // > 5kg
} as const;

const SHIPPING_THRESHOLDS = {
  LIGHT_WEIGHT: 1,
  MEDIUM_WEIGHT: 5,
  LONG_DISTANCE: 1000
} as const;

const LONG_DISTANCE_SURCHARGE = 0.2;

function calculateShipping(weight: number, distance: number): number {
  let rate: number;
  
  if (weight <= SHIPPING_THRESHOLDS.LIGHT_WEIGHT) {
    rate = SHIPPING_RATES.LIGHT;
  } else if (weight <= SHIPPING_THRESHOLDS.MEDIUM_WEIGHT) {
    rate = SHIPPING_RATES.MEDIUM;
  } else {
    rate = SHIPPING_RATES.HEAVY;
  }
  
  let total = distance * rate;
  
  if (distance > SHIPPING_THRESHOLDS.LONG_DISTANCE) {
    total *= (1 + LONG_DISTANCE_SURCHARGE);
  }
  
  return total;
}
```

---

## 🏗️ **Nguyên tắc SOLID**

[Xem thêm tài liệu chi tiết về SOLID principles](../principles/solid-principles.md)

### **📋 Tóm tắt SOLID**

| Nguyên tắc | Mô tả | Lợi ích |
|------------|-------|---------|
| **S**ingle Responsibility | Một class chỉ nên có một lý do để thay đổi | Dễ bảo trì, test |
| **O**pen/Closed | Mở để mở rộng, đóng để sửa đổi | Tính mở rộng |
| **L**iskov Substitution | Subclass có thể thay thế base class | Đáng tin cậy |
| **I**nterface Segregation | Nhiều interface cụ thể thay vì một interface lớn | Linh hoạt |
| **D**ependency Inversion | Phụ thuộc vào abstraction, không phải concrete | Có thể test được |

### **🎯 Ví dụ áp dụng SOLID**

```typescript
// Áp dụng tất cả nguyên tắc SOLID
interface PaymentProcessor {
  process(amount: number, paymentData: any): Promise<PaymentResult>;
}

interface NotificationService {
  send(message: string, recipient: string): Promise<void>;
}

interface OrderRepository {
  save(order: Order): Promise<Order>;
  findById(id: string): Promise<Order | null>;
}

// Single Responsibility: Chỉ xử lý business logic của order
class OrderService {
  constructor(
    private paymentProcessor: PaymentProcessor,    // Dependency Inversion
    private notificationService: NotificationService,
    private orderRepository: OrderRepository
  ) {}
  
  async processOrder(orderData: OrderData): Promise<Order> {
    const order = this.createOrder(orderData);
    
    const paymentResult = await this.paymentProcessor.process(
      order.total,
      orderData.paymentData
    );
    
    if (!paymentResult.success) {
      throw new Error('Payment failed');
    }
    
    order.paymentId = paymentResult.transactionId;
    const savedOrder = await this.orderRepository.save(order);
    
    await this.notificationService.send(
      `Order ${savedOrder.id} confirmed`,
      orderData.customerEmail
    );
    
    return savedOrder;
  }
  
  private createOrder(orderData: OrderData): Order {
    return {
      id: generateId(),
      customerId: orderData.customerId,
      items: orderData.items,
      total: this.calculateTotal(orderData.items),
      status: 'pending',
      createdAt: new Date()
    };
  }
  
  private calculateTotal(items: OrderItem[]): number {
    return items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  }
}

// Open/Closed: Có thể mở rộng payment methods mà không sửa code
class CreditCardProcessor implements PaymentProcessor {
  async process(amount: number, paymentData: CreditCardData): Promise<PaymentResult> {
    // Credit card processing logic
    return { success: true, transactionId: 'cc_' + Date.now() };
  }
}

class PayPalProcessor implements PaymentProcessor {
  async process(amount: number, paymentData: PayPalData): Promise<PaymentResult> {
    // PayPal processing logic
    return { success: true, transactionId: 'pp_' + Date.now() };
  }
}

// Interface Segregation: Chia nhỏ thành các interface cụ thể
interface EmailNotificationService extends NotificationService {
  sendHTML(htmlContent: string, recipient: string): Promise<void>;
}

interface SMSNotificationService extends NotificationService {
  sendBulk(message: string, recipients: string[]): Promise<void>;
}

// Liskov Substitution: EmailService có thể thay thế NotificationService
class EmailService implements EmailNotificationService {
  async send(message: string, recipient: string): Promise<void> {
    // Send plain text email
  }
  
  async sendHTML(htmlContent: string, recipient: string): Promise<void> {
    // Send HTML email
  }
}
```

---

## ⚡ **Hiệu suất và bảo mật**

### **🚀 Performance Best Practices**

#### **1. Lazy Loading**

```typescript
// Lazy loading for heavy dependencies
class ReportService {
  private _pdfGenerator?: PDFGenerator;
  private _excelGenerator?: ExcelGenerator;
  
  private get pdfGenerator(): PDFGenerator {
    if (!this._pdfGenerator) {
      this._pdfGenerator = new PDFGenerator();
    }
    return this._pdfGenerator;
  }
  
  private get excelGenerator(): ExcelGenerator {
    if (!this._excelGenerator) {
      this._excelGenerator = new ExcelGenerator();
    }
    return this._excelGenerator;
  }
  
  async generatePDFReport(data: ReportData): Promise<Buffer> {
    return this.pdfGenerator.generate(data);
  }
  
  async generateExcelReport(data: ReportData): Promise<Buffer> {
    return this.excelGenerator.generate(data);
  }
}
```

#### **2. Caching**

```typescript
// Memory cache with TTL
class CacheService<T> {
  private cache = new Map<string, { data: T; expires: number }>();
  
  set(key: string, data: T, ttlSeconds: number = 300): void {
    const expires = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { data, expires });
  }
  
  get(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  clear(): void {
    this.cache.clear();
  }
}

// Usage with service
class UserService {
  private cache = new CacheService<User>();
  
  async getUserById(id: string): Promise<User | null> {
    // Check cache first
    const cached = this.cache.get(`user:${id}`);
    if (cached) return cached;
    
    // Fetch from database
    const user = await this.userRepository.findById(id);
    if (user) {
      this.cache.set(`user:${id}`, user, 600); // Cache for 10 minutes
    }
    
    return user;
  }
}
```

#### **3. Database Optimization**

```typescript
// Efficient database queries
class OrderRepository {
  // Use indexes and limit results
  async findRecentOrders(limit: number = 50): Promise<Order[]> {
    return this.db.query(`
      SELECT * FROM orders 
      WHERE created_at >= NOW() - INTERVAL '30 days'
      ORDER BY created_at DESC 
      LIMIT $1
    `, [limit]);
  }
  
  // Batch operations
  async createOrdersInBatch(orders: Order[]): Promise<void> {
    const values = orders.map(order => 
      `('${order.id}', '${order.customerId}', ${order.total}, NOW())`
    ).join(',');
    
    await this.db.query(`
      INSERT INTO orders (id, customer_id, total, created_at)
      VALUES ${values}
    `);
  }
  
  // Pagination with offset
  async findOrdersPaginated(page: number, pageSize: number): Promise<{
    orders: Order[];
    total: number;
    hasNext: boolean;
  }> {
    const offset = page * pageSize;
    
    const [orders, totalResult] = await Promise.all([
      this.db.query(`
        SELECT * FROM orders 
        ORDER BY created_at DESC 
        LIMIT $1 OFFSET $2
      `, [pageSize, offset]),
      
      this.db.query('SELECT COUNT(*) as total FROM orders')
    ]);
    
    const total = parseInt(totalResult[0].total);
    const hasNext = offset + pageSize < total;
    
    return { orders, total, hasNext };
  }
}
```

### **🔒 Security Best Practices**

#### **1. Input Validation**

```typescript
// Comprehensive input validation
class UserValidator {
  validateCreateUser(data: any): asserts data is CreateUserData {
    if (!this.isObject(data)) {
      throw new ValidationError('Invalid input: must be an object');
    }
    
    // Required fields
    if (!data.email || typeof data.email !== 'string') {
      throw new ValidationError('Email is required and must be a string');
    }
    
    if (!data.name || typeof data.name !== 'string') {
      throw new ValidationError('Name is required and must be a string');
    }
    
    // Format validation
    if (!this.isValidEmail(data.email)) {
      throw new ValidationError('Invalid email format');
    }
    
    if (data.name.length < 2 || data.name.length > 100) {
      throw new ValidationError('Name must be between 2 and 100 characters');
    }
    
    // Sanitization
    data.email = data.email.toLowerCase().trim();
    data.name = this.sanitizeString(data.name);
    
    // Optional fields validation
    if (data.phone && !this.isValidPhone(data.phone)) {
      throw new ValidationError('Invalid phone number format');
    }
  }
  
  private isObject(value: any): value is object {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }
  
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }
  
  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }
  
  private sanitizeString(input: string): string {
    return input.trim().replace(/[<>]/g, '');
  }
}
```

#### **2. Authentication & Authorization**

```typescript
// JWT with proper security
class AuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET!;
  private readonly JWT_EXPIRES_IN = '1h';
  private readonly REFRESH_TOKEN_EXPIRES_IN = '7d';
  
  async login(email: string, password: string): Promise<AuthResult> {
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      throw new AuthenticationError('Invalid credentials');
    }
    
    // Rate limiting check
    await this.checkRateLimit(email);
    
    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);
    if (!isValidPassword) {
      await this.recordFailedLogin(email);
      throw new AuthenticationError('Invalid credentials');
    }
    
    // Generate tokens
    const accessToken = this.generateAccessToken(user);
    const refreshToken = this.generateRefreshToken(user);
    
    // Store refresh token
    await this.storeRefreshToken(user.id, refreshToken);
    
    return {
      accessToken,
      refreshToken,
      user: this.sanitizeUser(user)
    };
  }
  
  private generateAccessToken(user: User): string {
    return jwt.sign(
      { 
        userId: user.id, 
        email: user.email,
        role: user.role 
      },
      this.JWT_SECRET,
      { 
        expiresIn: this.JWT_EXPIRES_IN,
        issuer: 'your-app',
        audience: 'your-app-users'
      }
    );
  }
  
  private sanitizeUser(user: User): SafeUser {
    const { passwordHash, ...safeUser } = user;
    return safeUser;
  }
  
  async verifyToken(token: string): Promise<TokenPayload> {
    try {
      const payload = jwt.verify(token, this.JWT_SECRET) as TokenPayload;
      
      // Check if user still exists and is active
      const user = await this.userRepository.findById(payload.userId);
      if (!user || !user.isActive) {
        throw new AuthenticationError('User not found or inactive');
      }
      
      return payload;
    } catch (error) {
      throw new AuthenticationError('Invalid token');
    }
  }
}

// Authorization middleware
function requireRole(requiredRole: UserRole) {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = req.user; // Set by authentication middleware
    
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }
    
    if (!hasPermission(user.role, requiredRole)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    
    next();
  };
}

function hasPermission(userRole: UserRole, requiredRole: UserRole): boolean {
  const roleHierarchy: Record<UserRole, number> = {
    'user': 1,
    'moderator': 2,
    'admin': 3,
    'super_admin': 4
  };
  
  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}
```

#### **3. SQL Injection Prevention**

```typescript
// Safe database queries
class UserRepository {
  // ✅ GOOD: Parameterized queries
  async findByEmail(email: string): Promise<User | null> {
    const result = await this.db.query(
      'SELECT * FROM users WHERE email = $1',
      [email]
    );
    return result.rows[0] || null;
  }
  
  async searchUsers(searchTerm: string, limit: number): Promise<User[]> {
    const result = await this.db.query(`
      SELECT id, name, email, created_at 
      FROM users 
      WHERE name ILIKE $1 OR email ILIKE $1
      ORDER BY created_at DESC
      LIMIT $2
    `, [`%${searchTerm}%`, limit]);
    
    return result.rows;
  }
  
  // ❌ BAD: String concatenation (vulnerable to SQL injection)
  // async findByEmailUnsafe(email: string): Promise<User | null> {
  //   const result = await this.db.query(
  //     `SELECT * FROM users WHERE email = '${email}'`
  //   );
  //   return result.rows[0] || null;
  // }
}

// Query builder for complex queries
class QueryBuilder {
  private query: string = '';
  private params: any[] = [];
  
  select(columns: string[]): this {
    this.query += `SELECT ${columns.join(', ')} `;
    return this;
  }
  
  from(table: string): this {
    this.query += `FROM ${table} `;
    return this;
  }
  
  where(condition: string, value: any): this {
    const paramIndex = this.params.length + 1;
    this.query += `WHERE ${condition} $${paramIndex} `;
    this.params.push(value);
    return this;
  }
  
  orderBy(column: string, direction: 'ASC' | 'DESC' = 'ASC'): this {
    this.query += `ORDER BY ${column} ${direction} `;
    return this;
  }
  
  limit(count: number): this {
    const paramIndex = this.params.length + 1;
    this.query += `LIMIT $${paramIndex} `;
    this.params.push(count);
    return this;
  }
  
  build(): { query: string; params: any[] } {
    return { query: this.query.trim(), params: this.params };
  }
}

// Usage
const builder = new QueryBuilder()
  .select(['id', 'name', 'email'])
  .from('users')
  .where('email ILIKE', `%${searchTerm}%`)
  .orderBy('created_at', 'DESC')
  .limit(10);

const { query, params } = builder.build();
const result = await db.query(query, params);
```

---

## 🛠️ **Ví dụ thực tế**

### **📚 E-commerce Order System**

```typescript
// Domain models
interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  category: string;
}

interface OrderItem {
  productId: string;
  quantity: number;
  price: number;
}

interface Order {
  id: string;
  customerId: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  status: OrderStatus;
  createdAt: Date;
  updatedAt: Date;
}

type OrderStatus = 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';

// Value objects
class Money {
  constructor(private readonly amount: number, private readonly currency: string = 'USD') {
    if (amount < 0) throw new Error('Amount cannot be negative');
  }
  
  add(other: Money): Money {
    if (this.currency !== other.currency) {
      throw new Error('Cannot add different currencies');
    }
    return new Money(this.amount + other.amount, this.currency);
  }
  
  multiply(factor: number): Money {
    return new Money(this.amount * factor, this.currency);
  }
  
  toNumber(): number {
    return this.amount;
  }
  
  toString(): string {
    return `${this.amount} ${this.currency}`;
  }
}

// Services
interface ProductService {
  findById(id: string): Promise<Product | null>;
  reserveStock(productId: string, quantity: number): Promise<void>;
  releaseStock(productId: string, quantity: number): Promise<void>;
}

interface PaymentService {
  processPayment(amount: Money, paymentMethod: PaymentMethod): Promise<PaymentResult>;
}

interface EmailService {
  sendOrderConfirmation(order: Order, customerEmail: string): Promise<void>;
}

// Order service with clean architecture
class OrderService {
  constructor(
    private productService: ProductService,
    private paymentService: PaymentService,
    private emailService: EmailService,
    private orderRepository: OrderRepository,
    private logger: Logger
  ) {}
  
  async createOrder(request: CreateOrderRequest): Promise<Order> {
    // Validate request
    this.validateOrderRequest(request);
    
    try {
      // Check product availability
      await this.validateProductAvailability(request.items);
      
      // Calculate pricing
      const pricing = await this.calculateOrderPricing(request.items);
      
      // Reserve stock
      await this.reserveOrderStock(request.items);
      
      try {
        // Process payment
        const paymentResult = await this.paymentService.processPayment(
          pricing.total,
          request.paymentMethod
        );
        
        if (!paymentResult.success) {
          throw new PaymentError('Payment processing failed');
        }
        
        // Create order
        const order = this.buildOrder(request, pricing, paymentResult);
        const savedOrder = await this.orderRepository.save(order);
        
        // Send confirmation email (async)
        this.emailService.sendOrderConfirmation(savedOrder, request.customerEmail)
          .catch(error => this.logger.error('Failed to send order confirmation', error));
        
        this.logger.info('Order created successfully', { orderId: savedOrder.id });
        return savedOrder;
        
      } catch (paymentError) {
        // Release stock if payment fails
        await this.releaseOrderStock(request.items);
        throw paymentError;
      }
      
    } catch (error) {
      this.logger.error('Order creation failed', { error, request });
      throw error;
    }
  }
  
  private validateOrderRequest(request: CreateOrderRequest): void {
    if (!request.customerId) {
      throw new ValidationError('Customer ID is required');
    }
    
    if (!request.items || request.items.length === 0) {
      throw new ValidationError('Order must contain at least one item');
    }
    
    for (const item of request.items) {
      if (!item.productId) {
        throw new ValidationError('Product ID is required for all items');
      }
      
      if (item.quantity <= 0) {
        throw new ValidationError('Item quantity must be positive');
      }
    }
  }
  
  private async validateProductAvailability(items: OrderItemRequest[]): Promise<void> {
    for (const item of items) {
      const product = await this.productService.findById(item.productId);
      
      if (!product) {
        throw new ValidationError(`Product not found: ${item.productId}`);
      }
      
      if (product.stock < item.quantity) {
        throw new ValidationError(
          `Insufficient stock for ${product.name}. Available: ${product.stock}, Requested: ${item.quantity}`
        );
      }
    }
  }
  
  private async calculateOrderPricing(items: OrderItemRequest[]): Promise<OrderPricing> {
    let subtotal = new Money(0);
    const orderItems: OrderItem[] = [];
    
    for (const item of items) {
      const product = await this.productService.findById(item.productId);
      if (!product) throw new ValidationError(`Product not found: ${item.productId}`);
      
      const itemPrice = new Money(product.price);
      const itemTotal = itemPrice.multiply(item.quantity);
      subtotal = subtotal.add(itemTotal);
      
      orderItems.push({
        productId: item.productId,
        quantity: item.quantity,
        price: product.price
      });
    }
    
    const tax = subtotal.multiply(0.1); // 10% tax
    const shipping = this.calculateShipping(subtotal);
    const total = subtotal.add(tax).add(shipping);
    
    return {
      items: orderItems,
      subtotal,
      tax,
      shipping,
      total
    };
  }
  
  private calculateShipping(subtotal: Money): Money {
    const FREE_SHIPPING_THRESHOLD = 100;
    const STANDARD_SHIPPING = 10;
    
    return subtotal.toNumber() >= FREE_SHIPPING_THRESHOLD 
      ? new Money(0) 
      : new Money(STANDARD_SHIPPING);
  }
  
  private async reserveOrderStock(items: OrderItemRequest[]): Promise<void> {
    for (const item of items) {
      await this.productService.reserveStock(item.productId, item.quantity);
    }
  }
  
  private async releaseOrderStock(items: OrderItemRequest[]): Promise<void> {
    for (const item of items) {
      try {
        await this.productService.releaseStock(item.productId, item.quantity);
      } catch (error) {
        this.logger.error('Failed to release stock', { 
          productId: item.productId, 
          quantity: item.quantity, 
          error 
        });
      }
    }
  }
  
  private buildOrder(
    request: CreateOrderRequest,
    pricing: OrderPricing,
    paymentResult: PaymentResult
  ): Order {
    return {
      id: generateOrderId(),
      customerId: request.customerId,
      items: pricing.items,
      subtotal: pricing.subtotal.toNumber(),
      tax: pricing.tax.toNumber(),
      shipping: pricing.shipping.toNumber(),
      total: pricing.total.toNumber(),
      status: 'confirmed',
      paymentId: paymentResult.transactionId,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
}

// Error classes
class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

class PaymentError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'PaymentError';
  }
}

// Usage
async function handleCreateOrder(req: Request, res: Response) {
  try {
    const orderRequest: CreateOrderRequest = req.body;
    const order = await orderService.createOrder(orderRequest);
    
    res.status(201).json({
      success: true,
      data: order
    });
  } catch (error) {
    if (error instanceof ValidationError) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    } else if (error instanceof PaymentError) {
      res.status(402).json({
        success: false,
        error: 'Payment processing failed'
      });
    } else {
      logger.error('Unexpected error in order creation', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}
```

---

## 🎯 **Kết luận**

### **📋 Checklist Clean Code**

#### **✅ Code Quality**
- [ ] Code dễ đọc như văn xuôi
- [ ] Tên biến/hàm/class có ý nghĩa
- [ ] Hàm nhỏ, làm một việc duy nhất
- [ ] Không có code duplication
- [ ] Comment chỉ khi cần thiết
- [ ] Consistent formatting

#### **✅ Architecture**
- [ ] Áp dụng SOLID principles
- [ ] Separation of concerns
- [ ] Dependency injection
- [ ] Error handling comprehensive
- [ ] Proper abstraction layers

#### **✅ Testing**
- [ ] Unit test coverage >= 80%
- [ ] Integration tests cho critical paths
- [ ] E2E tests cho user journeys
- [ ] Tests đọc được như documentation

#### **✅ Security**
- [ ] Input validation ở mọi boundaries
- [ ] Parameterized queries
- [ ] Authentication & authorization
- [ ] Sensitive data encryption
- [ ] Rate limiting

#### **✅ Performance**
- [ ] Efficient algorithms
- [ ] Appropriate caching
- [ ] Database query optimization
- [ ] Lazy loading khi cần
- [ ] Memory management

### **🚀 Continuous Improvement**

```typescript
// Code review checklist
const CODE_REVIEW_CHECKLIST = {
  functionality: [
    'Code hoạt động đúng như mong đợi',
    'Edge cases được xử lý',
    'Error handling appropriate'
  ],
  
  design: [
    'Tuân thủ SOLID principles',
    'Appropriate design patterns',
    'Good separation of concerns',
    'Proper abstraction levels'
  ],
  
  readability: [
    'Code self-documenting',
    'Meaningful names',
    'Consistent formatting',
    'Appropriate comments'
  ],
  
  maintainability: [
    'DRY principle applied',
    'YAGNI principle followed',
    'Easy to modify/extend',
    'Minimal technical debt'
  ],
  
  testing: [
    'Adequate test coverage',
    'Tests are readable',
    'Tests cover edge cases',
    'Tests are fast and reliable'
  ],
  
  security: [
    'Input validation present',
    'No security vulnerabilities',
    'Proper error handling',
    'Authentication/authorization correct'
  ],
  
  performance: [
    'No obvious performance issues',
    'Efficient algorithms used',
    'Appropriate caching',
    'Database queries optimized'
  ]
};
```

### **📚 Tài liệu tham khảo**

- **Clean Code** - Robert C. Martin
- **Refactoring** - Martin Fowler  
- **Design Patterns** - Gang of Four
- **SOLID Principles** - Robert C. Martin
- **Clean Architecture** - Robert C. Martin
- **Test Driven Development** - Kent Beck

### **🔗 Liên kết hữu ích**

- [SOLID Principles chi tiết](../principles/solid-principles.md)
- [Design Patterns](../../../design-patterns/README.md)
- [Testing Guidelines](../../advanced/quality-assurance/README.md)
- [Security Best Practices](../../advanced/security/README.md)

---

**"Clean code is written by humans for humans"** - Robert C. Martin

*Hãy luôn nhớ rằng code sạch không chỉ hoạt động tốt mà còn dễ đọc, dễ hiểu và dễ bảo trì. Đầu tư thời gian viết clean code ngay từ đầu sẽ tiết kiệm rất nhiều thời gian và công sức trong tương lai.*