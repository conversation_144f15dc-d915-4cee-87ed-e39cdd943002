# 🏗️ **SOLID PRINCIPLES**

> **The five fundamental principles of object-oriented design that create maintainable, flexible, and robust software**

## 🎯 **Overview**

SOLID is an acronym for five design principles that help developers create more maintainable, understandable, and flexible software. These principles were introduced by <PERSON> (Uncle <PERSON>) and form the foundation of clean object-oriented design.

### **📊 The SOLID Acronym**

| Letter | Principle                                                                 | Focus                                       | Benefit         |
| ------ | ------------------------------------------------------------------------- | ------------------------------------------- | --------------- |
| **S**  | [Single Responsibility Principle](#s-single-responsibility-principle-srp) | One reason to change                        | Maintainability |
| **O**  | [Open/Closed Principle](#o-openclosed-principle-ocp)                      | Open for extension, closed for modification | Extensibility   |
| **L**  | [Liskov Substitution Principle](#l-liskov-substitution-principle-lsp)     | Substitutability of derived classes         | Reliability     |
| **I**  | [Interface Segregation Principle](#i-interface-segregation-principle-isp) | Many specific interfaces                    | Flexibility     |
| **D**  | [Dependency Inversion Principle](#d-dependency-inversion-principle-dip)   | Depend on abstractions                      | Testability     |

---

## **S** - Single Responsibility Principle (SRP)

> **"A class should have only one reason to change"**

### **🎯 Core Concept**

Each class should have only one responsibility and therefore only one reason to change. This principle helps create focused, cohesive classes that are easier to understand, test, and maintain.

### **❌ Violation Example**

```typescript
// BAD: Multiple responsibilities in one class
class UserService {
  // Responsibility 1: User management
  createUser(userData: UserData): User {
    // Validation logic
    if (!userData.email || !userData.name) {
      throw new Error("Invalid user data");
    }

    // Database logic
    const user = this.database.save(userData);

    // Email logic
    this.emailService.sendWelcomeEmail(user.email);

    // Logging logic
    console.log(`User created: ${user.id}`);

    return user;
  }

  // Responsibility 2: Email sending
  sendWelcomeEmail(email: string): void {
    // Email implementation
  }

  // Responsibility 3: Logging
  logUserActivity(activity: string): void {
    // Logging implementation
  }

  // Responsibility 4: Data validation
  validateUserData(data: UserData): boolean {
    // Validation implementation
  }
}
```

### **✅ Correct Implementation**

```typescript
// GOOD: Each class has a single responsibility

// Responsibility 1: User data validation
class UserValidator {
  validate(userData: UserData): ValidationResult {
    const errors: string[] = [];

    if (!userData.email) errors.push("Email is required");
    if (!userData.name) errors.push("Name is required");
    if (userData.email && !this.isValidEmail(userData.email)) {
      errors.push("Invalid email format");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private isValidEmail(email: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }
}

// Responsibility 2: Email operations
class EmailService {
  async sendWelcomeEmail(email: string): Promise<void> {
    const template = this.getWelcomeTemplate();
    await this.sendEmail(email, "Welcome!", template);
  }

  private async sendEmail(
    to: string,
    subject: string,
    body: string
  ): Promise<void> {
    // Email sending implementation
  }

  private getWelcomeTemplate(): string {
    return "Welcome to our platform!";
  }
}

// Responsibility 3: Activity logging
class ActivityLogger {
  logUserCreation(userId: string): void {
    this.log("USER_CREATED", { userId, timestamp: new Date() });
  }

  logUserLogin(userId: string): void {
    this.log("USER_LOGIN", { userId, timestamp: new Date() });
  }

  private log(event: string, data: any): void {
    console.log(`[${event}]`, JSON.stringify(data));
  }
}

// Responsibility 4: User business logic
class UserService {
  constructor(
    private userRepository: UserRepository,
    private userValidator: UserValidator,
    private emailService: EmailService,
    private activityLogger: ActivityLogger
  ) {}

  async createUser(userData: UserData): Promise<Result<User>> {
    // Validate input
    const validation = this.userValidator.validate(userData);
    if (!validation.isValid) {
      return Result.fail(validation.errors.join(", "));
    }

    try {
      // Save user
      const user = await this.userRepository.save(userData);

      // Send welcome email (async, don't wait)
      this.emailService.sendWelcomeEmail(user.email).catch((error) => {
        console.error("Failed to send welcome email:", error);
      });

      // Log activity
      this.activityLogger.logUserCreation(user.id);

      return Result.success(user);
    } catch (error) {
      return Result.fail(`Failed to create user: ${error.message}`);
    }
  }
}
```

### **🎯 Benefits of SRP**

- **Easier Testing** - Each class has focused functionality to test
- **Better Maintainability** - Changes to one responsibility don't affect others
- **Improved Readability** - Classes have clear, single purposes
- **Reduced Coupling** - Classes depend on fewer things
- **Enhanced Reusability** - Focused classes can be reused in different contexts

### **🚨 Common Violations**

1. **God Classes** - Classes that do everything
2. **Mixed Concerns** - UI logic mixed with business logic
3. **Utility Classes** - Classes with unrelated static methods
4. **Data + Behavior** - Classes that both store and manipulate data inappropriately

---

## **O** - Open/Closed Principle (OCP)

> **"Software entities should be open for extension but closed for modification"**

### **🎯 Core Concept**

You should be able to extend a class's behavior without modifying its existing code. This is typically achieved through inheritance, composition, and interfaces.

### **❌ Violation Example**

```typescript
// BAD: Must modify existing code to add new functionality
class PaymentProcessor {
  processPayment(amount: number, type: string): void {
    if (type === "credit_card") {
      this.processCreditCard(amount);
    } else if (type === "paypal") {
      this.processPayPal(amount);
    } else if (type === "bank_transfer") {
      this.processBankTransfer(amount);
    }
    // Adding new payment method requires modifying this method
  }

  private processCreditCard(amount: number): void {
    console.log(`Processing credit card payment: $${amount}`);
  }

  private processPayPal(amount: number): void {
    console.log(`Processing PayPal payment: $${amount}`);
  }

  private processBankTransfer(amount: number): void {
    console.log(`Processing bank transfer: $${amount}`);
  }
}
```

### **✅ Correct Implementation**

```typescript
// GOOD: Open for extension, closed for modification

// Abstract payment method interface
interface PaymentMethod {
  process(amount: number): Promise<PaymentResult>;
  validate(paymentData: any): ValidationResult;
  getType(): string;
}

// Concrete payment implementations
class CreditCardPayment implements PaymentMethod {
  constructor(private cardData: CreditCardData) {}

  async process(amount: number): Promise<PaymentResult> {
    // Credit card processing logic
    console.log(`Processing credit card payment: $${amount}`);
    return { success: true, transactionId: "cc_" + Date.now() };
  }

  validate(paymentData: CreditCardData): ValidationResult {
    // Credit card validation logic
    return { isValid: true, errors: [] };
  }

  getType(): string {
    return "credit_card";
  }
}

class PayPalPayment implements PaymentMethod {
  constructor(private paypalData: PayPalData) {}

  async process(amount: number): Promise<PaymentResult> {
    // PayPal processing logic
    console.log(`Processing PayPal payment: $${amount}`);
    return { success: true, transactionId: "pp_" + Date.now() };
  }

  validate(paymentData: PayPalData): ValidationResult {
    // PayPal validation logic
    return { isValid: true, errors: [] };
  }

  getType(): string {
    return "paypal";
  }
}

// New payment method can be added without modifying existing code
class CryptocurrencyPayment implements PaymentMethod {
  constructor(private cryptoData: CryptoData) {}

  async process(amount: number): Promise<PaymentResult> {
    // Cryptocurrency processing logic
    console.log(`Processing crypto payment: $${amount}`);
    return { success: true, transactionId: "crypto_" + Date.now() };
  }

  validate(paymentData: CryptoData): ValidationResult {
    // Crypto validation logic
    return { isValid: true, errors: [] };
  }

  getType(): string {
    return "cryptocurrency";
  }
}

// Payment processor is closed for modification but open for extension
class PaymentProcessor {
  private paymentMethods: Map<string, PaymentMethod> = new Map();

  registerPaymentMethod(method: PaymentMethod): void {
    this.paymentMethods.set(method.getType(), method);
  }

  async processPayment(
    amount: number,
    type: string,
    paymentData: any
  ): Promise<PaymentResult> {
    const paymentMethod = this.paymentMethods.get(type);

    if (!paymentMethod) {
      throw new Error(`Unsupported payment method: ${type}`);
    }

    const validation = paymentMethod.validate(paymentData);
    if (!validation.isValid) {
      throw new Error(`Invalid payment data: ${validation.errors.join(", ")}`);
    }

    return await paymentMethod.process(amount);
  }
}

// Usage
const processor = new PaymentProcessor();
processor.registerPaymentMethod(new CreditCardPayment(cardData));
processor.registerPaymentMethod(new PayPalPayment(paypalData));
processor.registerPaymentMethod(new CryptocurrencyPayment(cryptoData)); // New method added without modification
```
