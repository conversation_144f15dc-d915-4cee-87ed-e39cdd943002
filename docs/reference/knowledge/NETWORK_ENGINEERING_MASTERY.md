# Network Engineering Mastery Guide

## Nguyên lý Cốt lõi Bất biến của Internet

### Triết lý Internet
Internet kế thừa triết lý UNIX nhưng mở rộng cho mạng toàn cầu:
- **Decentralized**: Không có điểm điều khiển trung tâm
- **End-to-end principle**: Trí tuệ ở các đầu cuối, mạng chỉ là đường ống
- **Best effort delivery**: Không đảm bảo, nhưng tối ưu
- **Layered architecture**: Mỗi lớp độc lập, tách biệt chức năng

### Kiến trúc 3 lớp Internet
- **Physical Layer**: <PERSON><PERSON><PERSON> quang biển (95% lưu lượng xuyên lục địa), Internet Exchange Points (IXPs), data centers
- **Logical Layer**: Protocols (TCP/IP, BGP, OSPF), routing tables, DNS hierarchy
- **Application Layer**: HTTP/HTTPS, email, VoIP, cloud services

### ISP Tier Model - C<PERSON> chế hoạt động Internet

**Tier 1 ISPs**: Backbone toàn cầu
- AT&T, Verizon, NTT, Deutsche Telekom
- Peering miễn phí với nhau, sở hữu cáp quang xuyên lục địa
- Không trả phí transit, chỉ peering

**Tier 2 ISPs**: Regional/national
- Kết hợp peering + mua transit từ Tier 1
- Ví dụ: Các ISP quốc gia như Viettel, VNPT

**Tier 3 ISPs**: Local access
- Chỉ mua transit, cung cấp "last mile" cho end users

## Mô hình OSI và TCP/IP - Troubleshooting Framework

### So sánh OSI vs TCP/IP

| OSI (7 layers) | TCP/IP (4 layers) | Chức năng chính | Tools chính |
|---|---|---|---|
| Physical | Network Access | Tín hiệu điện, cáp | `ethtool`, cable tester |
| Data Link | Network Access | MAC, switching | `ip link`, `brctl` |
| Network | Internet | IP routing | `ip route`, `ping`, `traceroute` |
| Transport | Transport | TCP/UDP, ports | `ss`, `netstat`, `lsof` |
| Session | Application | Kết nối, authentication | `ssh`, `openssl` |
| Presentation | Application | Encryption, compression | `gpg`, `gzip` |
| Application | Application | User interface | `curl`, `dig`, `nslookup` |

### Phương pháp Troubleshooting OSI

**Bottom-up approach**: Bắt đầu từ Layer 1
1. **Physical**: Kiểm tra cable, port lights, `ethtool eth0`
2. **Data Link**: Kiểm tra ARP table, `ip neighbor show`
3. **Network**: Test connectivity, `ping`, `traceroute`
4. **Transport**: Kiểm tra ports, `ss -tulpn`
5. **Application**: Test service, `curl`, `telnet`

**Top-down approach**: Bắt đầu từ Application xuống
**Divide and conquer**: Bắt đầu từ Layer 3 (Network)

## Routing Protocols - Trái tim của Internet

### Interior Gateway Protocols (IGP)

**OSPF (Open Shortest Path First)**:
- Link-state protocol, sử dụng Dijkstra algorithm
- Fast convergence, hierarchical design với Areas
- Tốt cho enterprise network, phức tạp cấu hình
- Dấu hiệu: LSA flooding khi topology thay đổi

**RIP (Routing Information Protocol)**:
- Distance-vector, hop count metric (max 15)
- Đơn giản nhưng chậm convergence
- Chỉ dùng cho mạng nhỏ

### Exterior Gateway Protocol (EGP)

**BGP (Border Gateway Protocol)**:
- Path-vector protocol cho inter-AS routing
- Policy-based routing, không chỉ shortest path
- Backbone của Internet, phức tạp và chậm convergence
- Dấu hiệu: BGP hijacking → traffic đi sai đường

**Công thức ghi nhớ**:
- OSPF = Fast + Complex = Enterprise
- BGP = Policy + Scalable = Internet
- RIP = Simple + Limited = Lab/Small office

## Network Administration - Linux Command Arsenal

### Interface Management
```bash
# Interface info và configuration
ip addr show                    # Thay thế ifconfig
ip link set eth0 up/down       # Enable/disable interface
ethtool eth0                   # Physical layer info
nmcli device status            # NetworkManager status
```

### Connectivity Testing
```bash
# Basic connectivity
ping -c4 *******              # ICMP test
traceroute google.com          # Path tracing
mtr google.com                 # Continuous traceroute
nc -zv host 80                 # Port testing
```

### Socket và Port Analysis
```bash
# Modern tools (faster than netstat)
ss -tulpn                      # Socket statistics
ss -4 state established        # IPv4 established connections
lsof -i :80                    # Process using port 80
fuser -n tcp 80                # Which process on port 80
```

### Packet Capture & Analysis
```bash
# Capture traffic
tcpdump -i any -w dump.pcap
tcpdump -i eth0 host *********** and port 80

# Advanced filtering
tcpdump -i eth0 'tcp[tcpflags] & (tcp-syn|tcp-fin) != 0'

# Analyze with tshark
tshark -r dump.pcap -Y "http.request.method == GET"
```

### Network Performance Monitoring
```bash
# Bandwidth monitoring
iftop -i eth0                  # Real-time interface traffic
nethogs                       # Per-process network usage
vnstat -i eth0                 # Network statistics

# Network configuration
ip route show                  # Routing table
ip rule show                   # Routing rules
arp -a                         # ARP table
```

## Advanced Network Troubleshooting

### Network Diagnostics Workflow
```bash
# 1. Check physical connectivity
ethtool eth0 | grep "Link detected"
ip link show eth0

# 2. Check IP configuration
ip addr show eth0
ip route show

# 3. Test local network
ping $(ip route | grep default | awk '{print $3}')

# 4. Test DNS resolution
nslookup google.com
dig @******* google.com

# 5. Test application connectivity
telnet google.com 80
curl -I http://google.com
```

### Performance Analysis Tools
```bash
# Network latency testing
ping -c 100 -i 0.1 target_host | tail -1

# Bandwidth testing
iperf3 -c server_ip -t 30      # Client mode
iperf3 -s                      # Server mode

# Network path analysis
traceroute -n target_host
mtr --report --report-cycles 100 target_host
```

## Network Security Implementation

### Firewall Configuration
```bash
# iptables rules
iptables -A INPUT -p tcp --dport 22 -j ACCEPT
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
iptables -A INPUT -j DROP

# nftables (modern replacement)
nft add table inet filter
nft add chain inet filter input { type filter hook input priority 0 \; }
nft add rule inet filter input tcp dport 22 accept
nft add rule inet filter input tcp dport { 80, 443 } accept
nft add rule inet filter input drop
```

### Network Monitoring & Intrusion Detection
```bash
# Monitor network connections
watch -n 1 'ss -tuln'

# Log suspicious activities
tcpdump -i any -w /var/log/network.pcap 'not port 22'

# Analyze network traffic patterns
netstat -i                     # Interface statistics
cat /proc/net/dev              # Network device statistics
```

## Modern Network Technologies

### Software-Defined Networking (SDN)
- **Control Plane**: Centralized network intelligence
- **Data Plane**: Packet forwarding based on flow tables
- **OpenFlow Protocol**: Communication between controller and switches

### Network Function Virtualization (NFV)
- **Virtual Network Functions**: Firewalls, load balancers, routers as VMs
- **NFV Infrastructure**: Compute, storage, network resources
- **Management and Orchestration**: Automated deployment and scaling

### Container Networking
```bash
# Docker networking
docker network ls
docker network create --driver bridge my-network
docker run --network my-network nginx

# Kubernetes networking
kubectl get pods -o wide
kubectl describe service my-service
kubectl get endpoints
```

## Network Automation & Scripting

### Network Configuration Management
```python
# Paramiko for SSH automation
import paramiko

def configure_switch(hostname, username, password, commands):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        ssh.connect(hostname, username=username, password=password)
        
        for command in commands:
            stdin, stdout, stderr = ssh.exec_command(command)
            print(f"Command: {command}")
            print(f"Output: {stdout.read().decode()}")
            
    finally:
        ssh.close()

# Usage
commands = [
    "configure terminal",
    "interface GigabitEthernet0/1",
    "description 'Configured by automation'",
    "no shutdown",
    "exit"
]

configure_switch("***********", "admin", "password", commands)
```

### Network Monitoring Automation
```bash
#!/bin/bash
# Network health check script

HOSTS=("*******" "*******" "google.com")
LOG_FILE="/var/log/network_health.log"

for host in "${HOSTS[@]}"; do
    if ping -c 1 -W 5 "$host" > /dev/null 2>&1; then
        echo "$(date): $host is reachable" >> "$LOG_FILE"
    else
        echo "$(date): $host is unreachable" >> "$LOG_FILE"
        # Send alert
        echo "Network issue: $host unreachable" | mail -s "Network Alert" <EMAIL>
    fi
done

# Check interface status
for interface in $(ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' '); do
    if ip link show "$interface" | grep -q "state UP"; then
        echo "$(date): Interface $interface is UP" >> "$LOG_FILE"
    else
        echo "$(date): Interface $interface is DOWN" >> "$LOG_FILE"
    fi
done
```

## Network Performance Optimization

### TCP Tuning
```bash
# TCP buffer sizes
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' >> /etc/sysctl.conf

# TCP congestion control
echo 'net.ipv4.tcp_congestion_control = bbr' >> /etc/sysctl.conf

# Apply changes
sysctl -p
```

### Network Interface Optimization
```bash
# Increase network interface queue length
echo 'net.core.netdev_max_backlog = 5000' >> /etc/sysctl.conf

# Optimize network interface settings
ethtool -G eth0 rx 4096 tx 4096    # Ring buffer sizes
ethtool -K eth0 gro on             # Generic Receive Offload
ethtool -K eth0 tso on             # TCP Segmentation Offload
```

Để trở thành Internet Expert, cần nắm vững:
1. **Internet Architecture**: ISP tiers, protocols, infrastructure
2. **Troubleshooting Methodology**: Systematic approach using OSI model
3. **Linux Network Tools**: Command-line mastery for diagnostics
4. **Routing & Switching**: OSPF, BGP, network design
5. **Security Implementation**: Firewalls, monitoring, intrusion detection
6. **Modern Technologies**: SDN, NFV, container networking
7. **Automation**: Scripting, configuration management, monitoring
