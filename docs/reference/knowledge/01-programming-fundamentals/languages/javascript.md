# 🟨 **JAVASCRIPT PROGRAMMING LANGUAGE**

> **Complete JavaScript guide for developers** - From basic syntax to advanced features and modern ES6+ capabilities

## 🎯 **Overview**

JavaScript is a high-level, interpreted programming language that is the backbone of modern web development. Originally created for browsers, it has evolved into a powerful, multi-paradigm language that runs on servers (Node.js), desktops, and mobile devices. JavaScript's flexibility and ubiquity make it essential for every developer.

### **🌟 Key Features**

- **Multi-paradigm**: Supports object-oriented, functional, and procedural programming
- **Dynamic Typing**: Variables can hold different types of data
- **First-class Functions**: Functions are treated as values
- **Event-driven**: Built-in support for asynchronous programming
- **Universal**: Runs everywhere - browsers, servers, desktops, mobile

### **🔥 Why Learn JavaScript?**

JavaScript is one of the most versatile and widely-used programming languages today:

1. **Web Development Foundation**: Essential for frontend development and increasingly popular for backend
2. **Rapid Development**: Quick prototyping and immediate feedback
3. **Large Ecosystem**: Vast library ecosystem with npm
4. **Career Opportunities**: High demand in the job market
5. **Community Support**: Large, active community with abundant resources
6. **Cross-platform**: Write once, run anywhere (web, mobile, desktop, server)

### **⚡ JavaScript vs Java**

Despite similar names, JavaScript and Java are completely different languages:

| Aspect | JavaScript | Java |
|--------|------------|------|
| **Type System** | Dynamic, loosely typed | Static, strongly typed |
| **Compilation** | Interpreted (JIT compiled) | Compiled to bytecode |
| **Platform** | Web browsers, Node.js | JVM (Java Virtual Machine) |
| **Syntax** | Flexible, C-like | Verbose, object-oriented |
| **Memory Management** | Automatic garbage collection | Manual + garbage collection |
| **Use Cases** | Web development, scripting | Enterprise applications, Android |

## 🚀 **Getting Started**

### **Development Environment Setup**

#### **1. Install Visual Studio Code**
```bash
# Download from https://code.visualstudio.com/
# or using package managers:

# Windows (Chocolatey)
choco install vscode

# macOS (Homebrew)
brew install --cask visual-studio-code

# Linux (Ubuntu/Debian)
sudo snap install code --classic
```

**Recommended VSCode Extensions:**
- JavaScript (ES6) code snippets
- Prettier - Code formatter
- ESLint
- Live Server
- Auto Rename Tag
- Bracket Pair Colorizer

#### **2. Install Node.js**
```bash
# Download from https://nodejs.org/
# Choose LTS (Long Term Support) version

# Verify installation
node --version
npm --version

# Alternative: Using NVM (Node Version Manager)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install --lts
nvm use --lts
```

### **Running JavaScript**

#### **In the Browser**
```html
<!DOCTYPE html>
<html>
<head>
    <title>JavaScript Example</title>
</head>
<body>
    <h1>My First JavaScript Program</h1>
    <script>
        console.log("Hello from JavaScript!");
        alert("Welcome to JavaScript!");
        document.write("JavaScript is running!");
    </script>
</body>
</html>
```

#### **In Node.js**
```bash
# Install Node.js from nodejs.org
node --version

# Run JavaScript file
node script.js

# Interactive mode (REPL)
node
> console.log("Hello from Node.js!")
> .exit
```

#### **First JavaScript Program**
```javascript
// hello.js
console.log("Hello, World!");
console.log("Welcome to JavaScript!");
console.log("Today is:", new Date());

// Run with: node hello.js
```

### **🔧 Console Introduction**

The console is your debugging companion in JavaScript:

```javascript
// Different console methods
console.log("Regular message");           // Standard output
console.error("Error message");          // Error output (red)
console.warn("Warning message");         // Warning output (yellow)
console.info("Info message");            // Info output
console.table([{name: "John", age: 25}]); // Table format
console.group("Grouped messages");       // Group related logs
console.log("Inside group");
console.groupEnd();

// Clear console
console.clear();
```

## 📚 **JavaScript Basics**

### **🔧 Code Structure**

#### **Statements**
JavaScript programs are made up of statements that tell the computer what to do:

```javascript
// Simple statements
let name = "JavaScript";     // Variable declaration
console.log(name);          // Function call
name = "JS";               // Assignment

// Complex statements
if (name === "JS") {       // Conditional statement
    console.log("Short name!");
}
```

#### **Comments**
Comments explain code and are ignored by the interpreter:

```javascript
// This is a single-line comment

/*
This is a
multi-line comment
*/

/**
 * JSDoc comment for documentation
 * @param {string} name - The name to greet
 * @returns {string} A greeting message
 */
function greet(name) {
    return `Hello, ${name}!`;
}
```

#### **Execution Flow**
JavaScript code executes from top to bottom, line by line:

```javascript
console.log("First");     // Executes first
console.log("Second");    // Executes second
console.log("Third");     // Executes third

// Exception: Function declarations are hoisted
greetUser("Alice");       // This works even though function is defined below

function greetUser(name) {
    console.log(`Hello, ${name}!`);
}
```

### **Variables and Data Types**

#### **Variable Declaration**
```javascript
// Variable declaration (ES6+)
let name = "JavaScript";        // Block-scoped, can be reassigned
const version = "ES2022";       // Block-scoped, cannot be reassigned
var oldWay = "legacy";          // Function-scoped, avoid using

// Multiple declarations
let firstName = "John", lastName = "Doe", age = 25;

// Uninitialized variables
let uninitializedVar;           // undefined
console.log(uninitializedVar);  // undefined
```

#### **Variable Naming Rules**
```javascript
// Valid variable names
let userName = "valid";
let _private = "valid";
let $element = "valid";
let user123 = "valid";
let 用户名 = "valid (Unicode)";

// Invalid variable names (will cause errors)
// let 123user = "invalid";    // Cannot start with number
// let user-name = "invalid";  // Cannot contain hyphens
// let class = "invalid";      // Cannot use reserved keywords
```

#### **Constants**
```javascript
// Constants must be initialized
const PI = 3.14159;
const COMPANY_NAME = "TechCorp";

// Constants are immutable, but objects/arrays can be modified
const user = { name: "John" };
user.name = "Jane";             // This is allowed
user.age = 25;                  // This is allowed
// user = {};                   // This would cause an error

const numbers = [1, 2, 3];
numbers.push(4);                // This is allowed
// numbers = [];                // This would cause an error
```

#### **The var Keyword (Legacy)**
```javascript
// var has function scope and can cause issues
function example() {
    if (true) {
        var x = 1;              // Function-scoped
        let y = 2;              // Block-scoped
    }
    console.log(x);             // 1 (accessible)
    // console.log(y);          // ReferenceError: y is not defined
}

// var hoisting behavior
console.log(hoistedVar);        // undefined (not error)
var hoistedVar = "I'm hoisted";

// let/const don't have this behavior
// console.log(notHoisted);     // ReferenceError
let notHoisted = "Better approach";
```

### **Basic Data Types**

JavaScript has eight basic data types:

```javascript
// Primitive types
let string = "Hello";                    // String
let number = 42;                         // Number
let bigInt = 9007199254740991n;         // BigInt (ES2020+)
let boolean = true;                      // Boolean
let nullValue = null;                    // Null
let undefinedValue;                      // Undefined
let symbol = Symbol("unique");           // Symbol (ES6+)

// Non-primitive type
let object = {key: "value"};            // Object

// Type checking
console.log(typeof string);             // "string"
console.log(typeof number);             // "number"
console.log(typeof bigInt);             // "bigint"
console.log(typeof boolean);            // "boolean"
console.log(typeof nullValue);          // "object" (this is a known bug!)
console.log(typeof undefinedValue);     // "undefined"
console.log(typeof symbol);             // "symbol"
console.log(typeof object);             // "object"

// More specific type checking
console.log(Array.isArray([]));         // true
console.log(Number.isInteger(42));      // true
console.log(Number.isNaN(NaN));         // true
```

### **Strings**

#### **String Creation**
```javascript
// Different ways to create strings
let firstName = "John";                  // Double quotes
let lastName = 'Doe';                    // Single quotes
let fullName = `${firstName} ${lastName}`; // Template literal (ES6+)

// String properties and methods
let text = "  Hello, World!  ";
console.log(text.length);                // 15
console.log(text.trim());                // "Hello, World!" (removes whitespace)
console.log(text.toUpperCase());         // "  HELLO, WORLD!  "
console.log(text.toLowerCase());         // "  hello, world!  "
console.log(text.replace("World", "JavaScript")); // "  Hello, JavaScript!  "
console.log(text.includes("Hello"));     // true
console.log(text.startsWith("  H"));     // true
console.log(text.endsWith("!  "));       // true
console.log(text.indexOf("World"));      // 9
console.log(text.slice(2, 7));          // "Hello"
```

#### **String Manipulation**
```javascript
// String concatenation
let greeting = "Hello" + " " + "World";  // "Hello World"
let repeated = "Ha".repeat(3);           // "HaHaHa" (ES6+)

// Template literals (ES6+)
let name = "Alice";
let age = 25;
let message = `My name is ${name} and I am ${age} years old`;

// Multi-line strings
let multiline = `
    This is a
    multiline string
    using template literals
`;

// Escape characters
let escaped = "She said, \"Hello!\"";   // "She said, "Hello!""
let path = "C:\\Users\\<USER>\Users\Documents"
let newline = "Line 1\nLine 2";         // Line break
```

### **Numbers and Math**

#### **Number Operations**
```javascript
// Basic arithmetic
let a = 10;
let b = 3;

console.log(a + b);   // 13 (addition)
console.log(a - b);   // 7 (subtraction)
console.log(a * b);   // 30 (multiplication)
console.log(a / b);   // 3.3333... (division)
console.log(a % b);   // 1 (modulo/remainder)
console.log(a ** b);  // 1000 (exponentiation - ES2016+)

// Increment and decrement
let count = 5;
count++;              // 6 (post-increment)
++count;              // 7 (pre-increment)
count--;              // 6 (post-decrement)
--count;              // 5 (pre-decrement)
```

#### **Math Object**
```javascript
// Math constants
console.log(Math.PI);            // 3.141592653589793
console.log(Math.E);             // 2.718281828459045

// Math methods
console.log(Math.abs(-5));       // 5 (absolute value)
console.log(Math.sqrt(16));      // 4 (square root)
console.log(Math.pow(2, 3));     // 8 (power)
console.log(Math.ceil(3.2));     // 4 (round up)
console.log(Math.floor(3.8));    // 3 (round down)
console.log(Math.round(3.6));    // 4 (round to nearest)
console.log(Math.random());      // Random number between 0 and 1
console.log(Math.max(1, 2, 3));  // 3 (maximum)
console.log(Math.min(1, 2, 3));  // 1 (minimum)

// Random number in range
function randomInRange(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
console.log(randomInRange(1, 10)); // Random number between 1-10
```

#### **Number Methods**
```javascript
let num = 42.123456;
console.log(num.toFixed(2));        // "42.12" (2 decimal places)
console.log(num.toPrecision(4));    // "42.12" (4 significant digits)
console.log(num.toString());        // "42.123456" (convert to string)

// Number validation
console.log(Number.isInteger(42));   // true
console.log(Number.isInteger(42.1)); // false
console.log(Number.isNaN(NaN));      // true
console.log(Number.isNaN("hello")); // false (important!)
console.log(isNaN("hello"));        // true (global isNaN coerces)

// Special number values
console.log(Infinity);              // Infinity
console.log(-Infinity);             // -Infinity
console.log(NaN);                   // NaN (Not a Number)
console.log(1 / 0);                 // Infinity
console.log(0 / 0);                 // NaN
```

### **Type Conversion and Coercion**

#### **Explicit Type Conversion**
```javascript
// String conversion
let num = 42;
let str = String(num);              // "42"
let str2 = num.toString();          // "42"
let str3 = `${num}`;               // "42" (template literal)

// Number conversion
let str = "42";
let num1 = Number(str);             // 42
let num2 = parseInt(str);           // 42 (integer only)
let num3 = parseFloat("3.14");      // 3.14
let num4 = +str;                    // 42 (unary plus operator)

// Boolean conversion
let bool1 = Boolean(1);             // true
let bool2 = Boolean(0);             // false
let bool3 = Boolean("hello");       // true
let bool4 = Boolean("");            // false
let bool5 = !!1;                    // true (double negation)
```

#### **Implicit Type Coercion**
```javascript
// Automatic type conversion
console.log("5" + 3);               // "53" (number to string)
console.log("5" - 3);               // 2 (string to number)
console.log("5" * 3);               // 15 (string to number)
console.log("5" / 3);               // 1.666... (string to number)

// Truthy and falsy values
// Falsy: false, 0, "", null, undefined, NaN
// Truthy: everything else

if ("hello") {                      // true (non-empty string)
    console.log("Truthy");
}

if (0) {                           // false
    console.log("This won't run");
}

// Comparison coercion
console.log("5" == 5);             // true (loose equality)
console.log("5" === 5);            // false (strict equality)
console.log(null == undefined);    // true
console.log(null === undefined);   // false
```

### **Operators in JavaScript**

#### **Arithmetic Operators**
```javascript
let a = 10, b = 3;

console.log(a + b);    // 13 (addition)
console.log(a - b);    // 7 (subtraction)
console.log(a * b);    // 30 (multiplication)
console.log(a / b);    // 3.333... (division)
console.log(a % b);    // 1 (remainder)
console.log(a ** b);   // 1000 (exponentiation)

// Assignment operators
a += 5;                // a = a + 5
a -= 2;                // a = a - 2
a *= 3;                // a = a * 3
a /= 2;                // a = a / 2
a %= 4;                // a = a % 4
a **= 2;               // a = a ** 2
```

#### **Comparison Operators**
```javascript
let x = 5, y = 10, z = "5";

// Equality
console.log(x == z);    // true (loose equality, type coercion)
console.log(x === z);   // false (strict equality, no coercion)
console.log(x != y);    // true (loose inequality)
console.log(x !== z);   // true (strict inequality)

// Relational
console.log(x < y);     // true
console.log(x <= y);    // true
console.log(x > y);     // false
console.log(x >= y);    // false
```

#### **Logical Operators**
```javascript
let a = true, b = false;

// Basic logical operations
console.log(a && b);    // false (AND)
console.log(a || b);    // true (OR)
console.log(!a);        // false (NOT)

// Short-circuit evaluation
let result = a && "This runs";      // "This runs"
let defaultValue = b || "Default"; // "Default"

// Nullish coalescing operator (ES2020)
let value = null ?? "default";     // "default"
let value2 = 0 ?? "default";       // 0 (0 is not nullish)
```

#### **Ternary Operator**
```javascript
// condition ? value_if_true : value_if_false
let age = 18;
let status = age >= 18 ? "adult" : "minor";
console.log(status); // "adult"

// Nested ternary (use sparingly)
let score = 85;
let grade = score >= 90 ? "A" : score >= 80 ? "B" : score >= 70 ? "C" : "F";
console.log(grade); // "B"
```

### **Control Structures**

#### **Conditionals**
```javascript
// if-else statements
let age = 18;

if (age < 13) {
    console.log("Child");
} else if (age < 20) {
    console.log("Teenager");
} else if (age < 65) {
    console.log("Adult");
} else {
    console.log("Senior");
}

// Switch statement
let day = "Monday";
switch (day) {
    case "Monday":
        console.log("Start of work week");
        break;
    case "Tuesday":
    case "Wednesday":
    case "Thursday":
        console.log("Midweek");
        break;
    case "Friday":
        console.log("TGIF!");
        break;
    case "Saturday":
    case "Sunday":
        console.log("Weekend!");
        break;
    default:
        console.log("Invalid day");
}

// Truthiness in conditions
let name = "";
if (!name) { // equivalent to if (name === "")
    console.log("Name is empty");
}
```

#### **Loops**
```javascript
// for loop
for (let i = 0; i < 5; i++) {
    console.log(i); // 0, 1, 2, 3, 4
}

// for...of loop (ES6+) - for arrays and iterables
let fruits = ["apple", "banana", "cherry"];
for (let fruit of fruits) {
    console.log(fruit);
}

// for...in loop - for object properties
let person = {name: "John", age: 30, city: "NYC"};
for (let key in person) {
    console.log(`${key}: ${person[key]}`);
}

// while loop
let count = 0;
while (count < 5) {
    console.log(count);
    count++;
}

// do...while loop
let num = 0;
do {
    console.log(num);
    num++;
} while (num < 3);

// Loop control
for (let i = 0; i < 10; i++) {
    if (i === 3) {
        continue; // skip this iteration
    }
    if (i === 7) {
        break;    // exit loop
    }
    console.log(i); // prints: 0, 1, 2, 4, 5, 6
}
```

## 📊 **Data Structures**

### **🔢 JavaScript Arrays**

Arrays are ordered collections that can store multiple values in a single variable:

#### **Array Creation and Basic Operations**
```javascript
// Array creation
let numbers = [1, 2, 3, 4, 5];
let mixed = [1, "hello", true, 3.14, null];
let empty = [];
let arrayFromConstructor = new Array(1, 2, 3);
let arrayWithLength = new Array(5); // Creates array with 5 empty slots

// Accessing elements (zero-indexed)
console.log(numbers[0]);                    // 1 (first element)
console.log(numbers[numbers.length - 1]);  // 5 (last element)
console.log(numbers[10]);                   // undefined (out of bounds)

// Array length
console.log(numbers.length);                // 5
numbers.length = 3;                         // Truncate array
console.log(numbers);                       // [1, 2, 3]
```

#### **Adding and Removing Elements**
```javascript
let fruits = ["apple", "banana"];

// Adding elements
fruits.push("cherry");                      // Add to end: ["apple", "banana", "cherry"]
fruits.unshift("orange");                   // Add to beginning: ["orange", "apple", "banana", "cherry"]

// Removing elements
let lastFruit = fruits.pop();               // Remove from end: "cherry"
let firstFruit = fruits.shift();            // Remove from beginning: "orange"
console.log(fruits);                        // ["apple", "banana"]

// Adding/removing at specific position
fruits.splice(1, 0, "grape", "kiwi");      // Insert at index 1: ["apple", "grape", "kiwi", "banana"]
fruits.splice(2, 1);                        // Remove 1 element at index 2: ["apple", "grape", "banana"]
```

#### **Array Methods and Iteration**
```javascript
let numbers = [1, 2, 3, 4, 5];

// Traditional for loop
for (let i = 0; i < numbers.length; i++) {
    console.log(numbers[i]);
}

// Array iteration methods
numbers.forEach((num, index) => {
    console.log(`Index ${index}: ${num}`);
});

// Array transformation methods
let doubled = numbers.map(x => x * 2);              // [2, 4, 6, 8, 10]
let evens = numbers.filter(x => x % 2 === 0);       // [2, 4]
let sum = numbers.reduce((acc, x) => acc + x, 0);   // 15
let found = numbers.find(x => x > 3);               // 4
let foundIndex = numbers.findIndex(x => x > 3);     // 3
let hasLarge = numbers.some(x => x > 10);           // false
let allPositive = numbers.every(x => x > 0);        // true

// Array searching and checking
let fruits = ["apple", "banana", "cherry"];
console.log(fruits.includes("banana"));             // true
console.log(fruits.indexOf("cherry"));              // 2
console.log(fruits.lastIndexOf("apple"));           // 0

// Array manipulation
let moreFruits = ["grape", "kiwi"];
let allFruits = fruits.concat(moreFruits);          // Join arrays
let fruitString = fruits.join(", ");                // "apple, banana, cherry"
let reversed = fruits.slice().reverse();            // ["cherry", "banana", "apple"]
let sorted = fruits.slice().sort();                 // ["apple", "banana", "cherry"]

// Array copying
let shallowCopy = [...fruits];                      // Spread operator (ES6+)
let anotherCopy = Array.from(fruits);               // Array.from method
let sliceCopy = fruits.slice();                     // slice method
```

#### **Advanced Array Operations**
```javascript
// Multi-dimensional arrays
let matrix = [
    [1, 2, 3],
    [4, 5, 6],
    [7, 8, 9]
];
console.log(matrix[1][2]);                          // 6

// Array destructuring (ES6+)
let [first, second, ...rest] = [1, 2, 3, 4, 5];
console.log(first);                                 // 1
console.log(second);                                // 2
console.log(rest);                                  // [3, 4, 5]

// Swapping variables with destructuring
let a = 1, b = 2;
[a, b] = [b, a];                                   // Now a = 2, b = 1

// Array methods chaining
let result = [1, 2, 3, 4, 5, 6]
    .filter(x => x % 2 === 0)                     // [2, 4, 6]
    .map(x => x ** 2)                              // [4, 16, 36]
    .reduce((sum, x) => sum + x, 0);               // 56

// Flattening arrays
let nested = [[1, 2], [3, 4], [5, 6]];
let flattened = nested.flat();                     // [1, 2, 3, 4, 5, 6]
let deepNested = [1, [2, [3, [4]]]];
let deepFlattened = deepNested.flat(Infinity);     // [1, 2, 3, 4]
```

### **🏗️ JavaScript Objects**

Objects are collections of key-value pairs and the foundation of JavaScript programming:

#### **Object Creation and Basic Operations**
```javascript
// Object creation methods
let person = {
    name: "John",
    age: 30,
    city: "New York",
    isEmployed: true
};

// Alternative creation methods
let emptyObject = {};
let objectFromConstructor = new Object();
let personClone = Object.create(person);

// Accessing properties
console.log(person.name);           // "John" (dot notation)
console.log(person["age"]);         // 30 (bracket notation)
console.log(person.country || "Unknown"); // "Unknown" (default value)

// Dynamic property access
let propertyName = "city";
console.log(person[propertyName]); // "New York"

// Adding and modifying properties
person.email = "<EMAIL>";  // Add new property
person["phone"] = "************";   // Add using bracket notation
person.age = 31;                    // Modify existing property
person["city"] = "San Francisco";   // Modify using bracket notation

// Deleting properties
delete person.isEmployed;
console.log(person.isEmployed);     // undefined
```

#### **Object Methods and Functions**
```javascript
let calculator = {
    // Method shorthand (ES6+)
    add(a, b) {
        return a + b;
    },
    
    // Traditional method definition
    subtract: function(a, b) {
        return a - b;
    },
    
    // Arrow function (note: 'this' behaves differently)
    multiply: (a, b) => a * b,
    
    // Method with 'this' reference
    describe() {
        return `This is a calculator object`;
    }
};

console.log(calculator.add(5, 3));      // 8
console.log(calculator.subtract(10, 4)); // 6
console.log(calculator.multiply(3, 7));  // 21
console.log(calculator.describe());     // "This is a calculator object"
```

#### **Object Property Management**
```javascript
let user = {
    firstName: "Alice",
    lastName: "Johnson",
    age: 28,
    email: "<EMAIL>"
};

// Object.keys() - get property names
console.log(Object.keys(user));        
// ["firstName", "lastName", "age", "email"]

// Object.values() - get property values
console.log(Object.values(user));      
// ["Alice", "Johnson", 28, "<EMAIL>"]

// Object.entries() - get key-value pairs
console.log(Object.entries(user));     
// [["firstName", "Alice"], ["lastName", "Johnson"], ["age", 28], ["email", "<EMAIL>"]]

// Check if property exists
console.log("age" in user);            // true
console.log(user.hasOwnProperty("age")); // true
console.log("toString" in user);       // true (inherited)
console.log(user.hasOwnProperty("toString")); // false

// Property descriptors
Object.defineProperty(user, "id", {
    value: 12345,
    writable: false,    // Cannot be changed
    enumerable: false,  // Won't appear in for...in loops
    configurable: false // Cannot be deleted or reconfigured
});

console.log(user.id);                  // 12345
user.id = 67890;                       // Silently fails (or throws in strict mode)
console.log(user.id);                  // Still 12345
```

#### **Object Iteration and Destructuring**
```javascript
let product = {
    name: "Laptop",
    brand: "TechCorp",
    price: 999,
    inStock: true,
    specs: {
        cpu: "Intel i7",
        ram: "16GB",
        storage: "512GB SSD"
    }
};

// for...in loop
for (let key in product) {
    if (product.hasOwnProperty(key)) {
        console.log(`${key}: ${product[key]}`);
    }
}

// Object.entries() with forEach
Object.entries(product).forEach(([key, value]) => {
    console.log(`${key}: ${value}`);
});

// Object destructuring (ES6+)
let {name, price, inStock} = product;
console.log(name);     // "Laptop"
console.log(price);    // 999
console.log(inStock);  // true

// Nested destructuring
let {specs: {cpu, ram}} = product;
console.log(cpu);      // "Intel i7"
console.log(ram);      // "16GB"

// Destructuring with renaming and defaults
let {name: productName, color = "Unknown"} = product;
console.log(productName); // "Laptop"
console.log(color);       // "Unknown"

// Rest operator in destructuring
let {name: itemName, ...otherProps} = product;
console.log(itemName);    // "Laptop"
console.log(otherProps);  // {brand: "TechCorp", price: 999, inStock: true, specs: {...}}
```

#### **Object Manipulation and Cloning**
```javascript
let original = {
    name: "Original",
    nested: {
        value: 42
    }
};

// Shallow copying
let shallowCopy1 = {...original};              // Spread operator (ES6+)
let shallowCopy2 = Object.assign({}, original); // Object.assign

// Modifying nested object affects both
shallowCopy1.nested.value = 100;
console.log(original.nested.value);            // 100 (affected!)

// Deep copying (simple approach, limitations with functions, dates, etc.)
let deepCopy = JSON.parse(JSON.stringify(original));

// Object merging
let defaults = {theme: "light", language: "en"};
let userPrefs = {theme: "dark", fontSize: 14};
let merged = {...defaults, ...userPrefs};      // {theme: "dark", language: "en", fontSize: 14}

// Object.assign with multiple sources
let config = Object.assign({}, defaults, userPrefs, {debug: true});
```

#### **Advanced Object Patterns**
```javascript
// Computed property names (ES6+)
let propertyName = "dynamic";
let dynamicObj = {
    [propertyName]: "value",
    [`computed_${propertyName}`]: "computed value",
    [1 + 2]: "calculated key"  // "3": "calculated key"
};

// Object factory function
function createPerson(name, age) {
    return {
        name,
        age,
        greet() {
            return `Hi, I'm ${this.name}`;
        },
        isAdult() {
            return this.age >= 18;
        }
    };
}

let alice = createPerson("Alice", 25);
console.log(alice.greet());        // "Hi, I'm Alice"
console.log(alice.isAdult());      // true

// Object validation
function validateUser(user) {
    const requiredFields = ["name", "email", "age"];
    const missingFields = requiredFields.filter(field => !(field in user));
    
    if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
    }
    
    return true;
}

// Object freezing and sealing
let config = {api: "https://api.example.com", timeout: 5000};

Object.freeze(config);              // Completely immutable
// config.api = "new url";          // Silently fails or throws

Object.seal(config);                // Can modify existing, can't add/delete
// config.newProp = "value";        // Silently fails or throws
config.timeout = 10000;             // This works
```

### **Objects**

```javascript
// Object creation
let person = {
    name: "John",
    age: 30,
    city: "New York"
};

// Accessing properties
console.log(person.name);           // John
console.log(person["age"]);         // 30 (bracket notation)
console.log(person.country || "Unknown"); // Default value

// Adding/modifying properties
person.email = "<EMAIL>";
person["age"] = 31;

// Object methods
console.log(Object.keys(person));    // ["name", "age", "city", "email"]
console.log(Object.values(person));  // ["John", 31, "New York", "<EMAIL>"]
console.log(Object.entries(person)); // [["name", "John"], ["age", 31], ...]

// Object destructuring (ES6+)
let {name, age, ...otherProps} = person;
console.log(name);        // "John"
console.log(age);         // 31
console.log(otherProps);  // {city: "New York", email: "<EMAIL>"}

// Object spread operator (ES6+)
let updatedPerson = {...person, age: 32, country: "USA"};

// Object methods
let obj = {
    method() {
        return "This is a method";
    },
    arrowMethod: () => "This is an arrow method"
};

// Computed property names (ES6+)
let propertyName = "dynamic";
let dynamicObj = {
    [propertyName]: "value",
    [`computed_${propertyName}`]: "computed value"
};
```

### **Sets (ES6+)**

```javascript
// Set creation
let fruits = new Set(["apple", "banana", "cherry"]);
let numbers = new Set([1, 2, 3, 4, 5]);

// Set methods
fruits.add("orange");
fruits.delete("banana");
console.log(fruits.has("apple")); // true
console.log(fruits.size);         // 3

// Set operations
let set1 = new Set([1, 2, 3, 4]);
let set2 = new Set([3, 4, 5, 6]);

let union = new Set([...set1, ...set2]);           // {1, 2, 3, 4, 5, 6}
let intersection = new Set([...set1].filter(x => set2.has(x))); // {3, 4}
let difference = new Set([...set1].filter(x => !set2.has(x))); // {1, 2}

// Converting between arrays and sets
let arrayFromSet = [...fruits];
let setFromArray = new Set([1, 2, 2, 3, 3, 4]); // {1, 2, 3, 4}
```

### **Maps (ES6+)**

```javascript
// Map creation
let userMap = new Map();
userMap.set("name", "John");
userMap.set("age", 30);

// Or create from array of key-value pairs
let userMap2 = new Map([
    ["name", "John"],
    ["age", 30]
]);

// Map methods
console.log(userMap.get("name"));     // "John"
console.log(userMap.has("age"));      // true
userMap.delete("age");
console.log(userMap.size);            // 1

// Map iteration
for (let [key, value] of userMap) {
    console.log(`${key}: ${value}`);
}

userMap.forEach((value, key) => {
    console.log(`${key}: ${value}`);
});

// Maps vs Objects
// Maps: any value as key, size property, iteration order guaranteed
// Objects: only strings/symbols as keys, no size property, iteration order not guaranteed
```

## 🔧 **Functions**

### **📋 JavaScript Functions - Complete Guide**

Functions are reusable blocks of code that perform specific tasks. They are one of the fundamental building blocks of JavaScript:

#### **Function Declaration and Expression**
```javascript
// Function declaration (hoisted)
function greet(name) {
    return `Hello, ${name}!`;
}

// Function expression (not hoisted)
let greetExpression = function(name) {
    return `Hello, ${name}!`;
};

// Named function expression
let greetNamed = function greetUser(name) {
    return `Hello, ${name}!`;
};

// Arrow function (ES6+) - concise syntax
let greetArrow = (name) => `Hello, ${name}!`;
let greetArrowBlock = (name) => {
    return `Hello, ${name}!`;
};

// Function call examples
console.log(greet("Alice"));           // "Hello, Alice!"
console.log(greetExpression("Bob"));   // "Hello, Bob!"
console.log(greetArrow("Charlie"));    // "Hello, Charlie!"
```

#### **Function Parameters and Arguments**
```javascript
// Basic parameters
function add(a, b) {
    return a + b;
}

// Default parameters (ES6+)
function greetWithTitle(name, title = "Mr.") {
    return `Hello, ${title} ${name}!`;
}

console.log(greetWithTitle("Smith"));           // "Hello, Mr. Smith!"
console.log(greetWithTitle("Johnson", "Dr."));  // "Hello, Dr. Johnson!"

// Rest parameters (ES6+) - collect remaining arguments
function sumAll(...numbers) {
    return numbers.reduce((sum, num) => sum + num, 0);
}

console.log(sumAll(1, 2, 3, 4, 5));  // 15
console.log(sumAll(10, 20));         // 30

// Spread operator - expand arguments
function multiply(a, b, c) {
    return a * b * c;
}

let numbers = [2, 3, 4];
console.log(multiply(...numbers));   // 24

// Arguments object (legacy, use rest parameters instead)
function oldStyleVariadic() {
    let total = 0;
    for (let i = 0; i < arguments.length; i++) {
        total += arguments[i];
    }
    return total;
}

// Parameter destructuring
function processUser({name, age, email = "No email"}) {
    return `User: ${name}, Age: ${age}, Email: ${email}`;
}

let user = {name: "Alice", age: 25};
console.log(processUser(user)); // "User: Alice, Age: 25, Email: No email"

// Array parameter destructuring
function getCoordinates([x, y, z = 0]) {
    return {x, y, z};
}

console.log(getCoordinates([10, 20]));     // {x: 10, y: 20, z: 0}
console.log(getCoordinates([5, 15, 25]));  // {x: 5, y: 15, z: 25}
```

#### **Arrow Functions Deep Dive (ES6+)**
```javascript
// Single parameter (parentheses optional)
let square = x => x * x;
let cube = (x) => x * x * x;

// Multiple parameters
let add = (a, b) => a + b;
let greet = (name, age) => `Hello ${name}, you are ${age} years old`;

// No parameters
let random = () => Math.random();
let getCurrentTime = () => new Date();

// Multi-line arrow functions
let processData = (data) => {
    let result = data.map(x => x * 2);
    let filtered = result.filter(x => x > 10);
    return filtered.reduce((sum, x) => sum + x, 0);
};

// Arrow functions and 'this' context
let obj = {
    name: "Object",
    
    // Traditional method - 'this' refers to obj
    traditionalMethod: function() {
        console.log(`Traditional: ${this.name}`);
        
        setTimeout(function() {
            console.log(`Nested traditional: ${this.name}`); // 'this' is undefined/global
        }, 100);
    },
    
    // Arrow method - inherits 'this' from surrounding scope
    arrowMethod: function() {
        console.log(`Arrow wrapper: ${this.name}`);
        
        setTimeout(() => {
            console.log(`Nested arrow: ${this.name}`); // 'this' refers to obj
        }, 100);
    }
};

// Returning objects from arrow functions (wrap in parentheses)
let createUser = (name, age) => ({name, age, isActive: true});
console.log(createUser("Alice", 25)); // {name: "Alice", age: 25, isActive: true}

// Arrow functions in array methods
let numbers = [1, 2, 3, 4, 5];
let doubled = numbers.map(x => x * 2);
let evens = numbers.filter(x => x % 2 === 0);
let sum = numbers.reduce((acc, x) => acc + x, 0);
```

#### **Function Scope and Closures**
```javascript
// Function scope
function outerFunction(x) {
    let outerVariable = x;
    
    function innerFunction(y) {
        let innerVariable = y;
        // Can access both inner and outer variables
        return outerVariable + innerVariable;
    }
    
    return innerFunction;
}

let addToTen = outerFunction(10);
console.log(addToTen(5)); // 15

// Closures - functions remember their lexical environment
function createCounter() {
    let count = 0;
    
    return {
        increment: () => ++count,
        decrement: () => --count,
        getValue: () => count,
        reset: () => { count = 0; }
    };
}

let counter = createCounter();
console.log(counter.getValue()); // 0
console.log(counter.increment()); // 1
console.log(counter.increment()); // 2
console.log(counter.decrement()); // 1

// Module pattern using closures
let calculator = (function() {
    let history = [];
    
    return {
        add: function(a, b) {
            let result = a + b;
            history.push(`${a} + ${b} = ${result}`);
            return result;
        },
        
        subtract: function(a, b) {
            let result = a - b;
            history.push(`${a} - ${b} = ${result}`);
            return result;
        },
        
        getHistory: function() {
            return [...history]; // Return copy to preserve encapsulation
        },
        
        clearHistory: function() {
            history = [];
        }
    };
})();

console.log(calculator.add(5, 3));        // 8
console.log(calculator.subtract(10, 4));  // 6
console.log(calculator.getHistory());     // ["5 + 3 = 8", "10 - 4 = 6"]
```

#### **Higher-Order Functions**
```javascript
// Functions that take other functions as parameters
function applyOperation(func, ...args) {
    return func(...args);
}

function multiply(x, y) {
    return x * y;
}

let result = applyOperation(multiply, 4, 5); // 20

// Functions that return other functions
function createMultiplier(factor) {
    return function(number) {
        return number * factor;
    };
}

let double = createMultiplier(2);
let triple = createMultiplier(3);

console.log(double(5));  // 10
console.log(triple(4));  // 12

// Function composition
function compose(...functions) {
    return function(arg) {
        return functions.reduceRight((result, func) => func(result), arg);
    };
}

function addOne(x) { return x + 1; }
function multiplyByTwo(x) { return x * 2; }
function square(x) { return x ** 2; }

let composed = compose(square, multiplyByTwo, addOne);
let result = composed(3); // ((3 + 1) * 2)^2 = 64

// Partial application
function partial(func, ...args) {
    return function(...moreArgs) {
        return func(...args, ...moreArgs);
    };
}

function add(a, b, c) {
    return a + b + c;
}

let addFive = partial(add, 5);
console.log(addFive(3, 2)); // 10

// Currying
function curry(func) {
    return function curried(...args) {
        if (args.length >= func.length) {
            return func.apply(this, args);
        } else {
            return function(...nextArgs) {
                return curried(...args, ...nextArgs);
            };
        }
    };
}

let curriedAdd = curry((a, b, c) => a + b + c);
console.log(curriedAdd(1)(2)(3)); // 6
console.log(curriedAdd(1, 2)(3)); // 6
console.log(curriedAdd(1)(2, 3)); // 6
```

#### **Function Patterns and Best Practices**
```javascript
// Immediately Invoked Function Expression (IIFE)
(function() {
    let privateVariable = "I'm private";
    console.log("IIFE executed immediately!");
})();

// IIFE with parameters
let result = (function(a, b) {
    return a + b;
})(5, 3); // 8

// Recursive functions
function factorial(n) {
    if (n <= 1) return 1;
    return n * factorial(n - 1);
}

console.log(factorial(5)); // 120

// Memoization for optimization
function memoize(func) {
    let cache = {};
    return function(...args) {
        let key = JSON.stringify(args);
        if (key in cache) {
            return cache[key];
        }
        let result = func.apply(this, args);
        cache[key] = result;
        return result;
    };
}

let memoizedFactorial = memoize(function(n) {
    console.log(`Computing factorial of ${n}`);
    if (n <= 1) return 1;
    return n * memoizedFactorial(n - 1);
});

console.log(memoizedFactorial(5)); // Computes and caches
console.log(memoizedFactorial(5)); // Returns cached result

// Function throttling
function throttle(func, delay) {
    let lastCall = 0;
    return function(...args) {
        let now = Date.now();
        if (now - lastCall >= delay) {
            lastCall = now;
            return func.apply(this, args);
        }
    };
}

// Function debouncing
function debounce(func, delay) {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// Usage examples
let throttledLog = throttle(() => console.log("Throttled!"), 1000);
let debouncedLog = debounce(() => console.log("Debounced!"), 1000);
```

### **Basic Functions**

```javascript
// Function declaration
function greet(name) {
    return `Hello, ${name}!`;
}

// Function expression
let greet2 = function(name) {
    return `Hello, ${name}!`;
};

// Arrow function (ES6+)
let greet3 = (name) => `Hello, ${name}!`;

// Function call
let message = greet("Alice");
console.log(message); // "Hello, Alice!"

// Multiple parameters
function add(a, b) {
    return a + b;
}

let result = add(5, 3); // 8

// Default parameters (ES6+)
function greetWithTitle(name, title = "Mr.") {
    return `Hello, ${title} ${name}!`;
}

console.log(greetWithTitle("Smith"));           // "Hello, Mr. Smith!"
console.log(greetWithTitle("Johnson", "Dr."));  // "Hello, Dr. Johnson!"

// Rest parameters (ES6+)
function sumAll(...args) {
    return args.reduce((sum, num) => sum + num, 0);
}

console.log(sumAll(1, 2, 3, 4, 5)); // 15
```

## 🏗️ **Object-Oriented Programming**

### **Classes (ES6+)**

```javascript
// Class definition
class Person {
    // Class fields (ES2022+)
    species = "Homo sapiens";
    
    // Constructor
    constructor(name, age) {
        this.name = name;
        this.age = age;
    }
    
    // Instance method
    greet() {
        return `Hello, my name is ${this.name}`;
    }
    
    // Getter
    get isAdult() {
        return this.age >= 18;
    }
    
    // Setter
    set age(value) {
        if (value < 0) {
            throw new Error("Age cannot be negative");
        }
        this._age = value;
    }
    
    get age() {
        return this._age;
    }
    
    // Static method
    static createAnonymous() {
        return new Person("Anonymous", 0);
    }
}

// Creating objects
let person1 = new Person("Alice", 25);
let person2 = new Person("Bob", 30);

// Accessing methods and properties
console.log(person1.name);           // Alice
console.log(person1.greet());        // "Hello, my name is Alice"
console.log(Person.species);         // Homo sapiens
console.log(person1.isAdult);        // true

// Anonymous person
let anon = Person.createAnonymous();
```

### **Inheritance**

```javascript
class Animal {
    constructor(name) {
        this.name = name;
    }
    
    speak() {
        return "Some sound";
    }
}

class Dog extends Animal {
    constructor(name, breed) {
        super(name); // Call parent constructor
        this.breed = breed;
    }
    
    speak() {
        return `${this.name} says Woof!`;
    }
    
    fetch() {
        return `${this.name} fetches the ball`;
    }
}

class Cat extends Animal {
    speak() {
        return `${this.name} says Meow!`;
    }
}

// Using inheritance
let dog = new Dog("Buddy", "Golden Retriever");
let cat = new Cat("Whiskers");

console.log(dog.speak());  // "Buddy says Woof!"
console.log(cat.speak());  // "Whiskers says Meow!"
console.log(dog.fetch());  // "Buddy fetches the ball"

// instanceof operator
console.log(dog instanceof Dog);    // true
console.log(dog instanceof Animal); // true
console.log(dog instanceof Cat);    // false
```

### **Private Fields and Methods (ES2022+)**

```javascript
class BankAccount {
    #balance = 0; // Private field
    
    constructor(initialBalance) {
        this.#balance = initialBalance;
    }
    
    deposit(amount) {
        if (amount > 0) {
            this.#balance += amount;
            return true;
        }
        return false;
    }
    
    withdraw(amount) {
        if (0 < amount <= this.#balance) {
            this.#balance -= amount;
            return true;
        }
        return false;
    }
    
    getBalance() {
        return this.#balance;
    }
    
    #validateAmount(amount) { // Private method
        return typeof amount === 'number' && amount > 0;
    }
}

let account = new BankAccount(1000);
console.log(account.getBalance()); // 1000

account.deposit(500);
console.log(account.getBalance()); // 1500

// account.#balance = 2000; // SyntaxError: Private field
```

## 🔄 **Functional Programming**

### **Pure Functions and Immutability**

```javascript
// Pure function (no side effects)
function addNumbers(a, b) {
    return a + b;
}

// Impure function (has side effects)
let total = 0;
function addToTotal(value) {
    total += value;
    return total;
}

// Immutable data operations
let originalArray = [1, 2, 3, 4, 5];

// Instead of modifying the original array
let newArray = [...originalArray, 6];           // Add element
let filteredArray = originalArray.filter(x => x > 2); // Filter elements
let mappedArray = originalArray.map(x => x * 2);      // Transform elements

// Immutable object operations
let originalPerson = {name: "John", age: 30};

let updatedPerson = {...originalPerson, age: 31}; // Update property
let personWithEmail = {...originalPerson, email: "<EMAIL>"}; // Add property
```

### **Higher-Order Functions**

```javascript
// Map function
let numbers = [1, 2, 3, 4, 5];
let squared = numbers.map(x => x ** 2); // [1, 4, 9, 16, 25]

// Filter function
let evenNumbers = numbers.filter(x => x % 2 === 0); // [2, 4]

// Reduce function
let sum = numbers.reduce((acc, x) => acc + x, 0); // 15

// Chaining
let result = numbers
    .filter(x => x % 2 === 0)
    .map(x => x ** 2)
    .reduce((acc, x) => acc + x, 0);

console.log(result); // 20 (2^2 + 4^2 = 4 + 16 = 20)

// Custom higher-order function
function withLogging(func) {
    return function(...args) {
        console.log(`Calling ${func.name} with args:`, args);
        let result = func(...args);
        console.log(`${func.name} returned:`, result);
        return result;
    };
}

let loggedAdd = withLogging(add);
loggedAdd(3, 4);
```

## 🐛 **Error Handling**

### **Try-Catch Blocks**

```javascript
// Basic error handling
try {
    let number = parseInt(prompt("Enter a number: "));
    let result = 10 / number;
    console.log(`Result: ${result}`);
} catch (error) {
    if (error instanceof TypeError) {
        console.log("Invalid input. Please enter a valid number.");
    } else if (error.message.includes("divide by zero")) {
        console.log("Cannot divide by zero.");
    } else {
        console.log(`An unexpected error occurred: ${error.message}`);
    }
} finally {
    console.log("This always runs.");
}

// Custom errors
class CustomError extends Error {
    constructor(message, code = null) {
        super(message);
        this.name = "CustomError";
        this.code = code;
    }
}

// Throwing errors
function validateAge(age) {
    if (age < 0) {
        throw new CustomError("Age cannot be negative", "INVALID_AGE");
    }
    if (age > 150) {
        throw new CustomError("Age seems unrealistic", "UNREALISTIC_AGE");
    }
    return true;
}

try {
    validateAge(-5);
} catch (error) {
    if (error instanceof CustomError) {
        console.log(`Error: ${error.message} (Code: ${error.code})`);
    }
}
```

## 📦 **Modules (ES6+)**

### **Import/Export**

```javascript
// math-utils.js
export function add(a, b) {
    return a + b;
}

export function multiply(a, b) {
    return a * b;
}

export const PI = 3.14159;

// Default export
export default class Calculator {
    add(a, b) { return a + b; }
    subtract(a, b) { return a - b; }
}

// main.js
import { add, multiply, PI } from './math-utils.js';
import Calculator from './math-utils.js';

console.log(add(5, 3));        // 8
console.log(multiply(4, 5));   // 20
console.log(PI);               // 3.14159

let calc = new Calculator();
console.log(calc.subtract(10, 4)); // 6

// Namespace import
import * as MathUtils from './math-utils.js';
console.log(MathUtils.add(2, 3)); // 5

// Dynamic import
async function loadModule() {
    let module = await import('./math-utils.js');
    console.log(module.add(1, 2)); // 3
}
```

### **Module Structure**

```
my-module/
├── package.json
├── index.js
└── src/
    ├── utils.js
    └── helpers.js
```

```javascript
// package.json
{
  "name": "my-module",
  "type": "module",
  "main": "index.js"
}

// index.js
export { add, multiply } from './src/utils.js';
export { helper } from './src/helpers.js';

// src/utils.js
export function add(a, b) { return a + b; }
export function multiply(a, b) { return a * b; }

// src/helpers.js
export function helper() { return "I'm a helper"; }
```

## 🧪 **Testing**

### **Unit Testing with Jest**

```javascript
// math.js
function add(a, b) {
    return a + b;
}

function multiply(a, b) {
    return a * b;
}

module.exports = { add, multiply };

// math.test.js
const { add, multiply } = require('./math');

describe('Math Functions', () => {
    test('adds two positive numbers', () => {
        expect(add(2, 3)).toBe(5);
    });
    
    test('adds negative numbers', () => {
        expect(add(-1, -1)).toBe(-2);
    });
    
    test('multiplies two numbers', () => {
        expect(multiply(4, 5)).toBe(20);
    });
    
    test('handles zero', () => {
        expect(add(5, 0)).toBe(5);
    });
});
```

### **Testing with Mocha and Chai**

```javascript
// math.js
function add(a, b) {
    return a + b;
}

// math.test.js
const { expect } = require('chai');
const { add } = require('./math');

describe('Math Functions', () => {
    it('should add two positive numbers', () => {
        expect(add(2, 3)).to.equal(5);
    });
    
    it('should add negative numbers', () => {
        expect(add(-1, -1)).to.equal(-2);
    });
    
    it('should handle zero', () => {
        expect(add(5, 0)).to.equal(5);
    });
});
```

## 🚀 **Advanced Features**

### **Promises and Async/Await**

```javascript
// Promise basics
let promise = new Promise((resolve, reject) => {
    setTimeout(() => {
        let random = Math.random();
        if (random > 0.5) {
            resolve(`Success: ${random}`);
        } else {
            reject(`Error: ${random}`);
        }
    }, 1000);
});

promise
    .then(result => console.log(result))
    .catch(error => console.error(error));

// Async/await (ES2017+)
async function fetchData() {
    try {
        let response = await fetch('https://api.example.com/data');
        let data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching data:', error);
        throw error;
    }
}

// Multiple async operations
async function fetchMultiple(urls) {
    let promises = urls.map(url => fetch(url));
    let responses = await Promise.all(promises);
    let data = await Promise.all(responses.map(r => r.json()));
    return data;
}

// Promise utilities
Promise.all([promise1, promise2, promise3])     // Wait for all
Promise.race([promise1, promise2, promise3])    // Wait for first
Promise.allSettled([promise1, promise2, promise3]) // Wait for all to settle
```

### **Generators and Iterators**

```javascript
// Generator function
function* fibonacci(n) {
    let a = 0, b = 1;
    for (let i = 0; i < n; i++) {
        yield a;
        [a, b] = [b, a + b];
    }
}

// Using generator
for (let num of fibonacci(10)) {
    console.log(num); // 0, 1, 1, 2, 3, 5, 8, 13, 21, 34
}

// Iterator protocol
class CountDown {
    constructor(start) {
        this.start = start;
    }
    
    [Symbol.iterator]() {
        let current = this.start;
        return {
            next() {
                if (current <= 0) {
                    return { done: true };
                }
                return { value: current--, done: false };
            }
        };
    }
}

for (let num of new CountDown(5)) {
    console.log(num); // 5, 4, 3, 2, 1
}
```

### **Proxy and Reflection (ES6+)**

```javascript
// Proxy for validation
let handler = {
    get(target, prop) {
        if (prop in target) {
            return target[prop];
        }
        throw new Error(`Property ${prop} does not exist`);
    },
    
    set(target, prop, value) {
        if (prop === 'age' && (value < 0 || value > 150)) {
            throw new Error('Invalid age value');
        }
        target[prop] = value;
        return true;
    }
};

let person = new Proxy({}, handler);
person.name = "John";
person.age = 25;
// person.age = -5; // Error: Invalid age value
// person.nonexistent; // Error: Property nonexistent does not exist

// Reflection API
let obj = { x: 1, y: 2 };
console.log(Reflect.has(obj, 'x'));           // true
console.log(Reflect.get(obj, 'x'));           // 1
Reflect.set(obj, 'z', 3);                    // true
console.log(Reflect.deleteProperty(obj, 'y')); // true
console.log(Reflect.ownKeys(obj));            // ['x', 'z']
```

## 🔗 **JavaScript vs Python Comparison**

### **Syntax Differences**

| Feature | JavaScript | Python |
|---------|------------|--------|
| Variable declaration | `let name = "value"` | `name = "value"` |
| Function definition | `function func() {}` | `def func():` |
| Conditional | `if (x > 0) {` | `if x > 0:` |
| Loop | `for (let item of items)` | `for item in items:` |
| String formatting | `` `Hello ${name}` `` | `f"Hello {name}"` |
| Array/List | `[1, 2, 3]` | `[1, 2, 3]` |
| Object/Dictionary | `{"key": "value"}` | `{"key": "value"}` |

### **Conceptual Differences**

| Aspect | JavaScript | Python |
|--------|------------|--------|
| Typing | Dynamic, weak | Dynamic, strong |
| Functions | First-class objects | First-class objects |
| Classes | ES6+ classes | Built-in support |
| Async | Promises, async/await | async/await |
| Package management | npm, yarn | pip, conda |
| Web development | Native, React, Vue | Flask, Django |

## 📚 **Learning Resources**

### **Official Documentation**
- [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
- [ECMAScript Specification](https://tc39.es/ecma262/)

### **Online Courses**
- [JavaScript.info](https://javascript.info/)
- [Eloquent JavaScript](https://eloquentjavascript.net/)
- [You Don't Know JS](https://github.com/getify/You-Dont-Know-JS)

### **Books**
- "Eloquent JavaScript" by Marijn Haverbeke
- "You Don't Know JavaScript" by Kyle Simpson
- "JavaScript: The Good Parts" by Douglas Crockford

### **Practice Platforms**
- [LeetCode](https://leetcode.com/)
- [HackerRank](https://www.hackerrank.com/)
- [Codewars](https://www.codewars.com/)

## 🎯 **Next Steps**

1. **Master the basics** - Variables, control structures, functions
2. **Learn ES6+ features** - Arrow functions, destructuring, modules
3. **Understand OOP** - Classes, inheritance, encapsulation
4. **Practice functional programming** - Pure functions, higher-order functions
5. **Build projects** - Web apps, Node.js applications, APIs
6. **Explore the ecosystem** - Popular frameworks and libraries

## 🚀 **JavaScript Best Practices**

### **📏 Code Quality and Style**

#### **1. Variable and Function Naming**
```javascript
// Good: Descriptive and clear
const userName = "john_doe";
const userAge = 25;
const isLoggedIn = true;

function calculateTotalPrice(items, taxRate) {
    return items.reduce((total, item) => total + item.price, 0) * (1 + taxRate);
}

// Bad: Unclear and abbreviated
const u = "john_doe";
const a = 25;
const flag = true;

function calc(arr, tr) {
    return arr.reduce((t, i) => t + i.p, 0) * (1 + tr);
}
```

#### **2. Error Handling Best Practices**
```javascript
// Good: Comprehensive error handling
async function fetchUserData(userId) {
    try {
        if (!userId) {
            throw new Error("User ID is required");
        }
        
        const response = await fetch(`/api/users/${userId}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const userData = await response.json();
        return userData;
        
    } catch (error) {
        console.error("Failed to fetch user data:", error.message);
        
        // Handle different error types
        if (error.name === "TypeError") {
            throw new Error("Network error - please check your connection");
        }
        
        throw error; // Re-throw for caller to handle
    }
}

// Usage with proper error handling
async function displayUser(userId) {
    try {
        const user = await fetchUserData(userId);
        document.getElementById('user-name').textContent = user.name;
    } catch (error) {
        document.getElementById('error-message').textContent = error.message;
    }
}
```

#### **3. Performance Optimization**
```javascript
// Use const for better performance and clarity
const CONFIG = {
    API_URL: "https://api.example.com",
    TIMEOUT: 5000,
    MAX_RETRIES: 3
};

// Avoid global variables
(function() {
    "use strict";
    
    let cache = new Map();
    
    function expensiveOperation(input) {
        if (cache.has(input)) {
            return cache.get(input);
        }
        
        // Simulate expensive computation
        let result = input * Math.random() * 1000;
        cache.set(input, result);
        return result;
    }
    
    window.myApp = { expensiveOperation };
})();

// Use array methods efficiently
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

// Good: Single pass with chaining
const result = numbers
    .filter(n => n % 2 === 0)
    .map(n => n ** 2)
    .reduce((sum, n) => sum + n, 0);

// Bad: Multiple passes
const evens = numbers.filter(n => n % 2 === 0);
const squares = evens.map(n => n ** 2);
const sum = squares.reduce((sum, n) => sum + n, 0);
```

### **🔒 Security Best Practices**

```javascript
// Input validation and sanitization
function sanitizeInput(input) {
    if (typeof input !== 'string') {
        throw new Error('Input must be a string');
    }
    
    // Remove HTML tags and potential XSS
    return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/[<>'"&]/g, char => {
            const entities = {
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#x27;',
                '&': '&amp;'
            };
            return entities[char];
        });
}

// Safe JSON parsing
function safeJsonParse(str, defaultValue = null) {
    try {
        return JSON.parse(str);
    } catch (error) {
        console.warn('Invalid JSON string:', error.message);
        return defaultValue;
    }
}

// Avoid eval() and use safer alternatives
// Bad: Using eval
const userInput = "alert('XSS attack!')";
// eval(userInput); // Never do this!

// Good: Use Function constructor or JSON.parse for data
const safeData = '{"name": "John", "age": 30}';
const userData = JSON.parse(safeData);
```

### **🎨 Modern JavaScript Patterns**

#### **1. Module Pattern (ES6 Modules)**
```javascript
// userService.js
export class UserService {
    constructor(apiUrl) {
        this.apiUrl = apiUrl;
    }
    
    async getUser(id) {
        const response = await fetch(`${this.apiUrl}/users/${id}`);
        return response.json();
    }
    
    async createUser(userData) {
        const response = await fetch(`${this.apiUrl}/users`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        return response.json();
    }
}

// utils.js
export const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

export const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
};

// main.js
import { UserService } from './userService.js';
import { debounce, formatCurrency } from './utils.js';

const userService = new UserService('https://api.example.com');
const debouncedSearch = debounce(searchUsers, 300);

async function searchUsers(query) {
    // Implementation
}
```

#### **2. Factory Pattern for Object Creation**
```javascript
// Factory for creating different types of users
function createUser(type, data) {
    const baseUser = {
        id: Date.now(),
        createdAt: new Date(),
        isActive: true,
        
        activate() {
            this.isActive = true;
        },
        
        deactivate() {
            this.isActive = false;
        }
    };
    
    switch (type) {
        case 'admin':
            return {
                ...baseUser,
                ...data,
                role: 'admin',
                permissions: ['read', 'write', 'delete'],
                canManageUsers: true
            };
            
        case 'moderator':
            return {
                ...baseUser,
                ...data,
                role: 'moderator',
                permissions: ['read', 'write'],
                canModerate: true
            };
            
        case 'user':
        default:
            return {
                ...baseUser,
                ...data,
                role: 'user',
                permissions: ['read']
            };
    }
}

// Usage
const admin = createUser('admin', { name: 'Alice', email: '<EMAIL>' });
const user = createUser('user', { name: 'Bob', email: '<EMAIL>' });
```

### **📱 Practical JavaScript Exercises**

#### **Exercise 1: Build a Todo List**
```javascript
class TodoList {
    constructor() {
        this.todos = [];
        this.nextId = 1;
    }
    
    addTodo(text) {
        const todo = {
            id: this.nextId++,
            text: text.trim(),
            completed: false,
            createdAt: new Date()
        };
        
        this.todos.push(todo);
        return todo;
    }
    
    removeTodo(id) {
        const index = this.todos.findIndex(todo => todo.id === id);
        if (index !== -1) {
            return this.todos.splice(index, 1)[0];
        }
        return null;
    }
    
    toggleTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (todo) {
            todo.completed = !todo.completed;
        }
        return todo;
    }
    
    getActiveTodos() {
        return this.todos.filter(todo => !todo.completed);
    }
    
    getCompletedTodos() {
        return this.todos.filter(todo => todo.completed);
    }
    
    clearCompleted() {
        this.todos = this.todos.filter(todo => !todo.completed);
    }
}

// Usage
const todoList = new TodoList();
todoList.addTodo("Learn JavaScript");
todoList.addTodo("Build a project");
todoList.addTodo("Practice coding");

console.log(todoList.getActiveTodos());
todoList.toggleTodo(1);
console.log(todoList.getCompletedTodos());
```

#### **Exercise 2: API Data Fetcher with Caching**
```javascript
class DataFetcher {
    constructor() {
        this.cache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
    }
    
    async fetchData(url) {
        // Check cache first
        if (this.cache.has(url)) {
            const cached = this.cache.get(url);
            if (Date.now() - cached.timestamp < this.cacheExpiry) {
                console.log('Returning cached data');
                return cached.data;
            }
        }
        
        try {
            console.log('Fetching fresh data');
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Cache the result
            this.cache.set(url, {
                data: data,
                timestamp: Date.now()
            });
            
            return data;
        } catch (error) {
            console.error('Fetch error:', error);
            throw error;
        }
    }
    
    clearCache() {
        this.cache.clear();
    }
    
    getCacheSize() {
        return this.cache.size;
    }
}

// Usage
const fetcher = new DataFetcher();

async function loadUserData() {
    try {
        const users = await fetcher.fetchData('https://jsonplaceholder.typicode.com/users');
        console.log('Users loaded:', users.length);
        return users;
    } catch (error) {
        console.error('Failed to load users:', error);
    }
}
```

### **🌐 Browser APIs and Modern Features**

#### **Local Storage and Session Storage**
```javascript
// Local Storage helper
class StorageHelper {
    static set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
        }
    }
    
    static get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Failed to read from localStorage:', error);
            return defaultValue;
        }
    }
    
    static remove(key) {
        localStorage.removeItem(key);
    }
    
    static clear() {
        localStorage.clear();
    }
}

// Usage
StorageHelper.set('user', { name: 'John', preferences: { theme: 'dark' } });
const user = StorageHelper.get('user');
console.log(user);
```

#### **Fetch API with Better Error Handling**
```javascript
class ApiClient {
    constructor(baseUrl, options = {}) {
        this.baseUrl = baseUrl;
        this.defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
            ...options
        };
    }
    
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            ...this.defaultOptions,
            ...options,
            headers: {
                ...this.defaultOptions.headers,
                ...options.headers
            }
        };
        
        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            
            return await response.text();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
    
    get(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'GET' });
    }
    
    post(endpoint, data, options = {}) {
        return this.request(endpoint, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    put(endpoint, data, options = {}) {
        return this.request(endpoint, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    delete(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'DELETE' });
    }
}

// Usage
const api = new ApiClient('https://jsonplaceholder.typicode.com');

async function manageUsers() {
    try {
        // Get all users
        const users = await api.get('/users');
        console.log('Users:', users);
        
        // Create new user
        const newUser = await api.post('/users', {
            name: 'John Doe',
            email: '<EMAIL>'
        });
        console.log('Created user:', newUser);
        
    } catch (error) {
        console.error('User management failed:', error);
    }
}
```

## 📚 **Learning Resources and Next Steps**

### **📖 Essential Resources**

#### **Official Documentation**
- [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Web/JavaScript) - The most comprehensive JavaScript reference
- [ECMAScript Specification](https://tc39.es/ecma262/) - Official language specification
- [JavaScript.info](https://javascript.info/) - Modern JavaScript tutorial with excellent explanations

#### **Online Learning Platforms**
- [freeCodeCamp](https://www.freecodecamp.org/) - Free comprehensive curriculum
- [Eloquent JavaScript](https://eloquentjavascript.net/) - Free online book
- [You Don't Know JS](https://github.com/getify/You-Dont-Know-JS) - Deep dive into JavaScript

#### **Practice and Coding Challenges**
- [LeetCode](https://leetcode.com/) - Algorithm and data structure problems
- [HackerRank](https://www.hackerrank.com/) - Programming challenges
- [Codewars](https://www.codewars.com/) - Coding kata and community
- [JavaScript30](https://javascript30.com/) - 30 Day Vanilla JS Challenge

### **🛠️ Development Tools and Setup**

```bash
# Essential Node.js tools
npm install -g nodemon          # Auto-restart on file changes
npm install -g http-server      # Simple static file server
npm install -g eslint           # Code linting
npm install -g prettier         # Code formatting

# Project setup example
mkdir my-js-project
cd my-js-project
npm init -y
npm install --save-dev eslint prettier
```

### **🎯 Learning Path Recommendations**

#### **Beginner (0-3 months)**
1. **Basics**: Variables, data types, operators, control structures
2. **Functions**: Declaration, parameters, return values, scope
3. **Objects and Arrays**: Creation, manipulation, iteration
4. **DOM Manipulation**: Selecting elements, events, styling
5. **Basic Projects**: Calculator, todo list, simple games

#### **Intermediate (3-6 months)**
1. **ES6+ Features**: Arrow functions, destructuring, modules, classes
2. **Asynchronous JavaScript**: Promises, async/await, fetch API
3. **Error Handling**: Try-catch, custom errors, debugging
4. **Local Storage**: Data persistence, JSON handling
5. **Advanced Projects**: Weather app, expense tracker, API integration

#### **Advanced (6+ months)**
1. **Design Patterns**: Module, Factory, Observer, Singleton
2. **Performance**: Optimization, memory management, profiling
3. **Testing**: Unit tests, integration tests, TDD
4. **Build Tools**: Webpack, Babel, npm scripts
5. **Frameworks**: React, Vue, Angular, or Node.js for backend

### **💡 Tips for Success**

1. **Practice Daily**: Consistency is key - code every day, even if just for 30 minutes
2. **Build Projects**: Apply what you learn in real projects
3. **Read Others' Code**: Study well-written JavaScript on GitHub
4. **Join Communities**: Participate in JavaScript forums and Discord servers
5. **Stay Updated**: Follow JavaScript news and updates
6. **Debug Actively**: Use browser dev tools and learn debugging techniques
7. **Understand the 'Why'**: Don't just memorize syntax, understand concepts

---

**🟨 JavaScript's versatility and ubiquity make it an essential language for modern development. From simple scripts to complex applications, JavaScript powers the web and beyond! Start with the basics, practice consistently, and gradually work your way up to advanced concepts. Happy coding! 🚀**