# 🐍 **PYTH<PERSON> PROGRAMMING LANGUAGE**

> **Complete Python guide for developers** - From JavaScript developer perspective to Python mastery

## 🎯 **Overview**

Python is a high-level, interpreted programming language known for its simplicity, readability, and versatility. It's particularly popular for web development, data science, machine learning, and automation. For JavaScript developers, Python offers familiar concepts with some key differences in syntax and philosophy.

### **🌟 Key Features**

- **Readable Syntax**: Clean, English-like syntax that emphasizes code readability
- **Dynamic Typing**: Variables can hold different types of data
- **Rich Ecosystem**: Extensive libraries for almost any task
- **Cross-platform**: Runs on Windows, macOS, and Linux
- **Multiple Paradigms**: Supports OOP, functional, and procedural programming

## 🚀 **Getting Started**

### **Installation**

```bash
# Download from python.org or use package manager
# macOS
brew install python

# Ubuntu/Debian
sudo apt-get install python3

# Windows
# Download installer from python.org
```

### **First Python Program**

```python
# hello.py
print("Hello, <PERSON>!")
print("Welcome to Python!")

# Run with: python hello.py
```

### **Interactive Mode (REPL)**

```bash
python3
>>> print("Hello from Python!")
>>> 2 + 2
4
>>> exit()
```

## 📚 **Python Basics**

### **Variables and Data Types**

```python
# Variable assignment (no let/const/var needed)
name = "Python"
age = 30
is_awesome = True
pi = 3.14159

# Type checking
print(type(name))        # <class 'str'>
print(type(age))         # <class 'int'>
print(type(is_awesome))  # <class 'bool'>
print(type(pi))          # <class 'float'>

# Multiple assignment
x, y, z = 1, 2, 3
a = b = c = 0

# Type conversion
number = "42"
actual_number = int(number)  # 42
float_number = float(number) # 42.0
```

### **Strings**

```python
# String creation
first_name = "John"
last_name = 'Doe'
full_name = f"{first_name} {last_name}"  # f-string (Python 3.6+)

# String methods
text = "  Hello, World!  "
print(text.strip())           # "Hello, World!"
print(text.upper())           # "  HELLO, WORLD!  "
print(text.lower())           # "  hello, world!  "
print(text.replace("World", "Python"))  # "  Hello, Python!  "

# String concatenation
greeting = "Hello" + " " + "World"  # "Hello World"
repeated = "Ha" * 3                 # "HaHaHa"

# String formatting
name = "Alice"
age = 25
message = "My name is {} and I am {} years old".format(name, age)
message2 = f"My name is {name} and I am {age} years old"
```

### **Numbers and Math**

```python
# Basic arithmetic
a = 10
b = 3

print(a + b)   # 13
print(a - b)   # 7
print(a * b)   # 30
print(a / b)   # 3.3333... (float division)
print(a // b)  # 3 (integer division)
print(a % b)   # 1 (modulo)
print(a ** b)  # 1000 (exponentiation)

# Math functions
import math

print(math.sqrt(16))      # 4.0
print(math.ceil(3.2))     # 4
print(math.floor(3.8))    # 3
print(math.pi)            # 3.141592653589793
print(round(3.6))         # 4

# Complex numbers
c = 3 + 4j
print(abs(c))  # 5.0 (magnitude)
```

### **Control Structures**

#### **Conditionals**

```python
# if-elif-else (no parentheses needed, colon required)
age = 18

if age < 13:
    print("Child")
elif age < 20:
    print("Teenager")
elif age < 65:
    print("Adult")
else:
    print("Senior")

# Ternary operator
status = "adult" if age >= 18 else "minor"

# Truthiness (similar to JavaScript)
# Falsy values: False, 0, "", None, [], (), {}
# Truthy values: everything else

name = ""
if not name:  # equivalent to if name == ""
    print("Name is empty")

# Comparison operators
x = 5
y = 10

print(x == y)   # False
print(x != y)   # True
print(x < y)    # True
print(x <= y)   # True
print(x > y)    # False
print(x >= y)   # False

# Logical operators
a = True
b = False

print(a and b)  # False (not &&)
print(a or b)   # True  (not ||)
print(not a)    # False (not !)
```

#### **Loops**

```python
# for loop (similar to JavaScript for...of)
fruits = ["apple", "banana", "cherry"]

for fruit in fruits:
    print(fruit)

# Range function
for i in range(5):        # 0, 1, 2, 3, 4
    print(i)

for i in range(1, 6):     # 1, 2, 3, 4, 5
    print(i)

for i in range(0, 10, 2): # 0, 2, 4, 6, 8
    print(i)

# while loop
count = 0
while count < 5:
    print(count)
    count += 1

# Loop control
for i in range(10):
    if i == 3:
        continue  # skip this iteration
    if i == 7:
        break     # exit loop
    print(i)

# List comprehension (Pythonic way)
squares = [x**2 for x in range(5)]  # [0, 1, 4, 9, 16]
even_squares = [x**2 for x in range(10) if x % 2 == 0]  # [0, 4, 16, 36, 64]
```

## 📊 **Data Structures**

### **Lists (Dynamic Arrays)**

```python
# List creation
numbers = [1, 2, 3, 4, 5]
mixed = [1, "hello", True, 3.14]
empty = []

# Accessing elements
print(numbers[0])      # 1 (zero-indexed)
print(numbers[-1])     # 5 (last element)
print(numbers[1:3])    # [2, 3] (slicing)
print(numbers[::2])    # [1, 3, 5] (step by 2)

# List methods
numbers.append(6)      # [1, 2, 3, 4, 5, 6]
numbers.insert(0, 0)   # [0, 1, 2, 3, 4, 5, 6]
numbers.remove(3)      # [0, 1, 2, 4, 5, 6]
popped = numbers.pop() # 6, list becomes [0, 1, 2, 4, 5]
numbers.sort()         # [0, 1, 2, 4, 5]
numbers.reverse()      # [5, 4, 2, 1, 0]

# List operations
list1 = [1, 2, 3]
list2 = [4, 5, 6]
combined = list1 + list2  # [1, 2, 3, 4, 5, 6]
repeated = list1 * 3      # [1, 2, 3, 1, 2, 3, 1, 2, 3]

# List comprehension
squares = [x**2 for x in range(5)]
filtered = [x for x in range(10) if x % 2 == 0]
```

### **Tuples (Immutable Lists)**

```python
# Tuple creation
coordinates = (10, 20)
person = ("John", 30, "Developer")
single_item = (42,)  # Note the comma

# Tuple unpacking
x, y = coordinates
name, age, job = person

# Tuples are immutable
# coordinates[0] = 15  # TypeError!

# Tuple methods
print(coordinates.count(10))  # 1
print(coordinates.index(20))  # 1
```

### **Dictionaries (Objects/Maps)**

```python
# Dictionary creation
person = {
    "name": "John",
    "age": 30,
    "city": "New York"
}

# Accessing values
print(person["name"])           # John
print(person.get("age"))        # 30
print(person.get("country", "Unknown"))  # Default value

# Adding/modifying
person["email"] = "<EMAIL>"
person["age"] = 31

# Dictionary methods
print(person.keys())    # dict_keys(['name', 'age', 'city', 'email'])
print(person.values())  # dict_values(['John', 31, 'New York', '<EMAIL>'])
print(person.items())   # dict_items([('name', 'John'), ('age', 31), ...])

# Iterating
for key in person:
    print(f"{key}: {person[key]}")

for key, value in person.items():
    print(f"{key}: {value}")

# Dictionary comprehension
squares = {x: x**2 for x in range(5)}  # {0: 0, 1: 1, 2: 4, 3: 9, 4: 16}
```

### **Sets (Unique Collections)**

```python
# Set creation
fruits = {"apple", "banana", "cherry"}
numbers = set([1, 2, 3, 4, 5])

# Set operations
fruits.add("orange")
fruits.remove("banana")  # Raises KeyError if not found
fruits.discard("grape")  # No error if not found

# Set operations
set1 = {1, 2, 3, 4}
set2 = {3, 4, 5, 6}

print(set1 | set2)   # Union: {1, 2, 3, 4, 5, 6}
print(set1 & set2)   # Intersection: {3, 4}
print(set1 - set2)   # Difference: {1, 2}
print(set1 ^ set2)   # Symmetric difference: {1, 2, 5, 6}
```

## 🔧 **Functions**

### **Basic Functions**

```python
# Function definition
def greet(name):
    return f"Hello, {name}!"

# Function call
message = greet("Alice")
print(message)  # "Hello, Alice!"

# Multiple parameters
def add(a, b):
    return a + b

result = add(5, 3)  # 8

# Default parameters
def greet_with_title(name, title="Mr."):
    return f"Hello, {title} {name}!"

print(greet_with_title("Smith"))           # "Hello, Mr. Smith!"
print(greet_with_title("Johnson", "Dr."))  # "Hello, Dr. Johnson!"

# Keyword arguments
def create_profile(name, age, city, email):
    return {"name": name, "age": age, "city": city, "email": email}

profile = create_profile(
    name="John",
    age=30,
    city="NYC",
    email="<EMAIL>"
)
```

### **Advanced Functions**

```python
# Variable number of arguments
def sum_all(*args):
    return sum(args)

print(sum_all(1, 2, 3, 4, 5))  # 15

# Keyword arguments
def create_user(**kwargs):
    return kwargs

user = create_user(name="Alice", age=25, role="admin")
# {'name': 'Alice', 'age': 25, 'role': 'admin'}

# Lambda functions (arrow functions)
square = lambda x: x**2
add = lambda x, y: x + y

print(square(5))    # 25
print(add(3, 4))    # 7

# Higher-order functions
def apply_operation(func, *args):
    return func(*args)

def multiply(x, y):
    return x * y

result = apply_operation(multiply, 4, 5)  # 20

# Decorators
def timer(func):
    import time
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.4f} seconds")
        return result
    return wrapper

@timer
def slow_function():
    import time
    time.sleep(1)
    return "Done!"

slow_function()  # slow_function took 1.0000 seconds
```

## 🏗️ **Object-Oriented Programming**

### **Classes and Objects**

```python
# Class definition
class Person:
    # Class variable
    species = "Homo sapiens"
    
    # Constructor
    def __init__(self, name, age):
        self.name = name  # Instance variable
        self.age = age
    
    # Instance method
    def greet(self):
        return f"Hello, my name is {self.name}"
    
    # Class method
    @classmethod
    def create_anonymous(cls):
        return cls("Anonymous", 0)
    
    # Static method
    @staticmethod
    def is_adult(age):
        return age >= 18

# Creating objects
person1 = Person("Alice", 25)
person2 = Person("Bob", 30)

# Accessing attributes and methods
print(person1.name)           # Alice
print(person1.greet())        # "Hello, my name is Alice"
print(Person.species)         # Homo sapiens
print(Person.is_adult(20))    # True

# Anonymous person
anon = Person.create_anonymous()
```

### **Inheritance**

```python
class Animal:
    def __init__(self, name):
        self.name = name
    
    def speak(self):
        pass

class Dog(Animal):
    def speak(self):
        return f"{self.name} says Woof!"

class Cat(Animal):
    def speak(self):
        return f"{self.name} says Meow!"

# Using inheritance
dog = Dog("Buddy")
cat = Cat("Whiskers")

print(dog.speak())  # "Buddy says Woof!"
print(cat.speak())  # "Whiskers says Meow!"

# Multiple inheritance
class Pet(Animal):
    def __init__(self, name, owner):
        super().__init__(name)
        self.owner = owner

class PetDog(Dog, Pet):
    def __init__(self, name, owner):
        Pet.__init__(self, name, owner)

pet_dog = PetDog("Max", "John")
print(pet_dog.speak())  # "Max says Woof!"
print(pet_dog.owner)    # "John"
```

### **Encapsulation and Properties**

```python
class BankAccount:
    def __init__(self, balance):
        self._balance = balance  # Protected attribute
    
    @property
    def balance(self):
        return self._balance
    
    @balance.setter
    def balance(self, value):
        if value < 0:
            raise ValueError("Balance cannot be negative")
        self._balance = value
    
    def deposit(self, amount):
        if amount > 0:
            self._balance += amount
            return True
        return False
    
    def withdraw(self, amount):
        if 0 < amount <= self._balance:
            self._balance -= amount
            return True
        return False

# Using the class
account = BankAccount(1000)
print(account.balance)  # 1000

account.deposit(500)
print(account.balance)  # 1500

account.withdraw(200)
print(account.balance)  # 1300

# account.balance = -100  # ValueError!
```

## 🔄 **Functional Programming**

### **Pure Functions and Immutability**

```python
# Pure function (no side effects)
def add_numbers(a, b):
    return a + b

# Impure function (has side effects)
total = 0
def add_to_total(value):
    global total
    total += value
    return total

# Immutable data structures
from collections import namedtuple

Point = namedtuple('Point', ['x', 'y'])
p1 = Point(1, 2)
# p1.x = 3  # AttributeError: can't set attribute

# Creating new instances
p2 = Point(p1.x + 1, p1.y + 1)
```

### **Higher-Order Functions**

```python
# Map function
numbers = [1, 2, 3, 4, 5]
squared = list(map(lambda x: x**2, numbers))  # [1, 4, 9, 16, 25]

# Filter function
even_numbers = list(filter(lambda x: x % 2 == 0, numbers))  # [2, 4]

# Reduce function
from functools import reduce
sum_all = reduce(lambda acc, x: acc + x, numbers, 0)  # 15

# List comprehensions (more Pythonic)
squared = [x**2 for x in numbers]
even_numbers = [x for x in numbers if x % 2 == 0]
```

### **Decorators and Function Composition**

```python
def log_function(func):
    def wrapper(*args, **kwargs):
        print(f"Calling {func.__name__} with args: {args}")
        result = func(*args, **kwargs)
        print(f"{func.__name__} returned: {result}")
        return result
    return wrapper

@log_function
def add(a, b):
    return a + b

result = add(3, 4)
# Output:
# Calling add with args: (3, 4)
# add returned: 7

# Function composition
def compose(*functions):
    def inner(arg):
        for f in reversed(functions):
            arg = f(arg)
        return arg
    return inner

def add_one(x): return x + 1
def multiply_by_two(x): return x * 2
def square(x): return x ** 2

composed = compose(square, multiply_by_two, add_one)
result = composed(3)  # ((3 + 1) * 2)^2 = 64
```

## 🐛 **Error Handling**

### **Try-Except Blocks**

```python
# Basic error handling
try:
    number = int(input("Enter a number: "))
    result = 10 / number
    print(f"Result: {result}")
except ValueError:
    print("Invalid input. Please enter a valid number.")
except ZeroDivisionError:
    print("Cannot divide by zero.")
except Exception as e:
    print(f"An unexpected error occurred: {e}")
else:
    print("No errors occurred.")
finally:
    print("This always runs.")

# Custom exceptions
class CustomError(Exception):
    def __init__(self, message, code=None):
        self.message = message
        self.code = code
        super().__init__(self.message)

# Raising exceptions
def validate_age(age):
    if age < 0:
        raise CustomError("Age cannot be negative", "INVALID_AGE")
    if age > 150:
        raise CustomError("Age seems unrealistic", "UNREALISTIC_AGE")
    return True

try:
    validate_age(-5)
except CustomError as e:
    print(f"Error: {e.message} (Code: {e.code})")
```

## 📦 **Modules and Packages**

### **Importing Modules**

```python
# Standard library imports
import math
import random
import datetime

# Using imported modules
print(math.pi)
print(random.randint(1, 10))
print(datetime.datetime.now())

# Specific imports
from math import sqrt, pi
from random import choice

print(sqrt(16))  # 4.0
print(pi)        # 3.141592653589793
print(choice(['a', 'b', 'c']))

# Aliasing
import datetime as dt
from math import sqrt as square_root

print(dt.datetime.now())
print(square_root(25))

# Creating your own modules
# math_utils.py
def add(a, b):
    return a + b

def multiply(a, b):
    return a * b

# main.py
import math_utils

result = math_utils.add(5, 3)
print(result)  # 8
```

### **Package Structure**

```
my_package/
├── __init__.py
├── module1.py
├── module2.py
└── subpackage/
    ├── __init__.py
    └── module3.py
```

```python
# __init__.py
from .module1 import function1
from .module2 import function2

# module1.py
def function1():
    return "Hello from module1"

# main.py
from my_package import function1, function2
```

## 🧪 **Testing**

### **Unit Testing with unittest**

```python
import unittest

def add(a, b):
    return a + b

class TestMathFunctions(unittest.TestCase):
    def test_add_positive_numbers(self):
        self.assertEqual(add(2, 3), 5)
    
    def test_add_negative_numbers(self):
        self.assertEqual(add(-1, -1), -2)
    
    def test_add_zero(self):
        self.assertEqual(add(5, 0), 5)
    
    def test_add_floats(self):
        self.assertAlmostEqual(add(1.1, 2.2), 3.3, places=1)

if __name__ == '__main__':
    unittest.main()
```

### **Testing with pytest**

```python
# test_math.py
import pytest

def add(a, b):
    return a + b

def test_add_positive_numbers():
    assert add(2, 3) == 5

def test_add_negative_numbers():
    assert add(-1, -1) == -2

@pytest.mark.parametrize("a, b, expected", [
    (1, 2, 3),
    (0, 0, 0),
    (-1, 1, 0),
])
def test_add_various_numbers(a, b, expected):
    assert add(a, b) == expected
```

## 🚀 **Advanced Features**

### **Context Managers**

```python
# Using with statement
with open('file.txt', 'r') as file:
    content = file.read()

# Custom context manager
class Timer:
    def __init__(self, name):
        self.name = name
    
    def __enter__(self):
        import time
        self.start = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        self.end = time.time()
        print(f"{self.name} took {self.end - self.start:.4f} seconds")

# Using custom context manager
with Timer("calculation"):
    result = sum(range(1000000))
```

### **Generators and Iterators**

```python
# Generator function
def fibonacci(n):
    a, b = 0, 1
    for _ in range(n):
        yield a
        a, b = b, a + b

# Using generator
for num in fibonacci(10):
    print(num, end=" ")  # 0 1 1 2 3 5 8 13 21 34

# Generator expression
squares = (x**2 for x in range(5))
print(list(squares))  # [0, 1, 4, 9, 16]

# Iterator protocol
class CountDown:
    def __init__(self, start):
        self.start = start
    
    def __iter__(self):
        return self
    
    def __next__(self):
        if self.start <= 0:
            raise StopIteration
        self.start -= 1
        return self.start + 1

for num in CountDown(5):
    print(num, end=" ")  # 5 4 3 2 1
```

### **Async Programming**

```python
import asyncio
import aiohttp

async def fetch_url(session, url):
    async with session.get(url) as response:
        return await response.text()

async def main():
    urls = [
        'https://api.github.com/users/1',
        'https://api.github.com/users/2',
        'https://api.github.com/users/3'
    ]
    
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_url(session, url) for url in urls]
        results = await asyncio.gather(*tasks)
        
        for url, result in zip(urls, results):
            print(f"{url}: {len(result)} characters")

# Run async function
asyncio.run(main())
```

## 🔗 **Python vs JavaScript Comparison**

### **Syntax Differences**

| Feature | Python | JavaScript |
|---------|--------|------------|
| Variable declaration | `name = "value"` | `let name = "value"` |
| Function definition | `def func():` | `function func() {}` |
| Conditional | `if x > 0:` | `if (x > 0) {` |
| Loop | `for item in items:` | `for (let item of items)` |
| String formatting | `f"Hello {name}"` | `` `Hello ${name}` `` |
| List/Array | `[1, 2, 3]` | `[1, 2, 3]` |
| Dictionary/Object | `{"key": "value"}` | `{"key": "value"}` |

### **Conceptual Differences**

| Aspect | Python | JavaScript |
|--------|--------|------------|
| Typing | Dynamic, strong | Dynamic, weak |
| Functions | First-class objects | First-class objects |
| Classes | Built-in support | Prototype-based |
| Async | async/await | async/await |
| Package management | pip, conda | npm, yarn |
| Web development | Flask, Django | Express, React |

## 📚 **Learning Resources**

### **Official Documentation**
- [Python.org](https://www.python.org/doc/)
- [Python Tutorial](https://docs.python.org/3/tutorial/)

### **Online Courses**
- [Python for Everybody](https://www.py4e.com/)
- [Real Python](https://realpython.com/)
- [Python Crash Course](https://ehmatthes.github.io/pcc/)

### **Books**
- "Python Crash Course" by Eric Matthes
- "Fluent Python" by Luciano Ramalho
- "Effective Python" by Brett Slatkin

### **Practice Platforms**
- [LeetCode](https://leetcode.com/)
- [HackerRank](https://www.hackerrank.com/)
- [Codewars](https://www.codewars.com/)

## 🎯 **Next Steps**

1. **Master the basics** - Variables, control structures, functions
2. **Learn data structures** - Lists, dictionaries, sets, tuples
3. **Understand OOP** - Classes, inheritance, polymorphism
4. **Practice functional programming** - Pure functions, decorators
5. **Build projects** - Web apps, data analysis, automation
6. **Explore the ecosystem** - Popular libraries and frameworks

---

**🐍 Python's simplicity and power make it an excellent choice for both beginners and experienced developers. Its extensive ecosystem and community support ensure you'll find solutions for almost any programming challenge!**