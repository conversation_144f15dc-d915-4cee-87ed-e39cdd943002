# 🏗️ **OBJECT-ORIENTED PROGRAMMING (OOP)**

> **Master the art of organizing code with objects** - From basic classes to advanced design patterns

## 🎯 **Overview**

Object-Oriented Programming (OOP) is a programming paradigm based on the concept of "objects" that contain data and code. OOP organizes software design around data, or objects, rather than functions and logic. It provides a clear structure for programs and makes code more maintainable, reusable, and scalable.

### **🌟 Core Principles**

- **Encapsulation**: Bundling data and methods that operate on that data
- **Inheritance**: Creating new classes based on existing ones
- **Polymorphism**: Using a single interface for different types
- **Abstraction**: Hiding complex implementation details

## 🔧 **Basic Concepts**

### **Classes and Objects**

A class is a blueprint for creating objects, while an object is an instance of a class.

#### **Python Implementation**

```python
class Car:
    # Class variable (shared by all instances)
    total_cars = 0
    
    def __init__(self, brand, model, year):
        # Instance variables (unique to each instance)
        self.brand = brand
        self.model = model
        self.year = year
        self.mileage = 0
        Car.total_cars += 1
    
    # Instance method
    def start_engine(self):
        return f"{self.brand} {self.model} engine started"
    
    def drive(self, distance):
        self.mileage += distance
        return f"Drove {distance} miles. Total mileage: {self.mileage}"
    
    # Class method
    @classmethod
    def get_total_cars(cls):
        return cls.total_cars
    
    # Static method
    @staticmethod
    def is_vintage(year):
        return year < 1990
    
    # Property (getter)
    @property
    def age(self):
        from datetime import datetime
        return datetime.now().year - self.year
    
    # Property with setter
    @property
    def fuel_level(self):
        return self._fuel_level
    
    @fuel_level.setter
    def fuel_level(self, value):
        if 0 <= value <= 100:
            self._fuel_level = value
        else:
            raise ValueError("Fuel level must be between 0 and 100")
    
    def __str__(self):
        return f"{self.year} {self.brand} {self.model}"
    
    def __repr__(self):
        return f"Car('{self.brand}', '{self.model}', {self.year})"

# Creating objects
car1 = Car("Toyota", "Camry", 2020)
car2 = Car("Honda", "Civic", 2019)

print(car1.start_engine())  # Toyota Camry engine started
print(car1.drive(50))       # Drove 50 miles. Total mileage: 50
print(car1.age)             # 4 (assuming current year is 2024)
print(f"Total cars: {Car.get_total_cars()}")  # Total cars: 2
print(Car.is_vintage(1985)) # True
```

#### **JavaScript Implementation**

```javascript
class Car {
    // Static property (shared by all instances)
    static totalCars = 0;
    
    constructor(brand, model, year) {
        // Instance properties
        this.brand = brand;
        this.model = model;
        this.year = year;
        this.mileage = 0;
        this._fuelLevel = 100;
        Car.totalCars++;
    }
    
    // Instance method
    startEngine() {
        return `${this.brand} ${this.model} engine started`;
    }
    
    drive(distance) {
        this.mileage += distance;
        return `Drove ${distance} miles. Total mileage: ${this.mileage}`;
    }
    
    // Static method
    static getTotalCars() {
        return Car.totalCars;
    }
    
    // Static method
    static isVintage(year) {
        return year < 1990;
    }
    
    // Getter
    get age() {
        return new Date().getFullYear() - this.year;
    }
    
    // Getter and setter for fuel level
    get fuelLevel() {
        return this._fuelLevel;
    }
    
    set fuelLevel(value) {
        if (value >= 0 && value <= 100) {
            this._fuelLevel = value;
        } else {
            throw new Error("Fuel level must be between 0 and 100");
        }
    }
    
    toString() {
        return `${this.year} ${this.brand} ${this.model}`;
    }
}

// Creating objects
const car1 = new Car("Toyota", "Camry", 2020);
const car2 = new Car("Honda", "Civic", 2019);

console.log(car1.startEngine());  // Toyota Camry engine started
console.log(car1.drive(50));      // Drove 50 miles. Total mileage: 50
console.log(car1.age);            // 4 (assuming current year is 2024)
console.log(`Total cars: ${Car.getTotalCars()}`);  // Total cars: 2
console.log(Car.isVintage(1985)); // true
```

### **Constructor and Initialization**

The constructor is a special method that gets called when creating a new object.

```python
class Person:
    def __init__(self, name, age, email=None):
        self.name = name
        self.age = age
        self.email = email
        self._id = self._generate_id()
    
    def _generate_id(self):
        import uuid
        return str(uuid.uuid4())[:8]
    
    def update_email(self, new_email):
        self.email = new_email
        return f"Email updated to: {new_email}"
    
    def get_info(self):
        return {
            'name': self.name,
            'age': self.age,
            'email': self.email,
            'id': self._id
        }

# Creating objects with different parameters
person1 = Person("Alice", 25, "<EMAIL>")
person2 = Person("Bob", 30)  # email will be None

print(person1.get_info())
print(person2.update_email("<EMAIL>"))
```

```javascript
class Person {
    constructor(name, age, email = null) {
        this.name = name;
        this.age = age;
        this.email = email;
        this._id = this._generateId();
    }
    
    _generateId() {
        return Math.random().toString(36).substr(2, 8);
    }
    
    updateEmail(newEmail) {
        this.email = newEmail;
        return `Email updated to: ${newEmail}`;
    }
    
    getInfo() {
        return {
            name: this.name,
            age: this.age,
            email: this.email,
            id: this._id
        };
    }
}

// Creating objects with different parameters
const person1 = new Person("Alice", 25, "<EMAIL>");
const person2 = new Person("Bob", 30); // email will be null

console.log(person1.getInfo());
console.log(person2.updateEmail("<EMAIL>"));
```

## 🧬 **Inheritance**

Inheritance allows a class to inherit properties and methods from another class.

### **Single Inheritance**

```python
class Animal:
    def __init__(self, name, species):
        self.name = name
        self.species = species
    
    def make_sound(self):
        return "Some sound"
    
    def get_info(self):
        return f"{self.name} is a {self.species}"

class Dog(Animal):
    def __init__(self, name, breed):
        # Call parent constructor
        super().__init__(name, "Dog")
        self.breed = breed
    
    def make_sound(self):
        return "Woof!"
    
    def fetch(self):
        return f"{self.name} fetches the ball"
    
    def get_info(self):
        return f"{super().get_info()}, breed: {self.breed}"

class Cat(Animal):
    def __init__(self, name, color):
        super().__init__(name, "Cat")
        self.color = color
    
    def make_sound(self):
        return "Meow!"
    
    def climb(self):
        return f"{self.name} climbs the tree"

# Using inheritance
dog = Dog("Buddy", "Golden Retriever")
cat = Cat("Whiskers", "Orange")

print(dog.make_sound())    # Woof!
print(cat.make_sound())    # Meow!
print(dog.fetch())         # Buddy fetches the ball
print(cat.climb())         # Whiskers climbs the tree
print(dog.get_info())      # Buddy is a Dog, breed: Golden Retriever
```

```javascript
class Animal {
    constructor(name, species) {
        this.name = name;
        this.species = species;
    }
    
    makeSound() {
        return "Some sound";
    }
    
    getInfo() {
        return `${this.name} is a ${this.species}`;
    }
}

class Dog extends Animal {
    constructor(name, breed) {
        // Call parent constructor
        super(name, "Dog");
        this.breed = breed;
    }
    
    makeSound() {
        return "Woof!";
    }
    
    fetch() {
        return `${this.name} fetches the ball`;
    }
    
    getInfo() {
        return `${super.getInfo()}, breed: ${this.breed}`;
    }
}

class Cat extends Animal {
    constructor(name, color) {
        super(name, "Cat");
        this.color = color;
    }
    
    makeSound() {
        return "Meow!";
    }
    
    climb() {
        return `${this.name} climbs the tree`;
    }
}

// Using inheritance
const dog = new Dog("Buddy", "Golden Retriever");
const cat = new Cat("Whiskers", "Orange");

console.log(dog.makeSound());    // Woof!
console.log(cat.makeSound());    // Meow!
console.log(dog.fetch());        // Buddy fetches the ball
console.log(cat.climb());        // Whiskers climbs the tree
console.log(dog.getInfo());      // Buddy is a Dog, breed: Golden Retriever
```

### **Multiple Inheritance (Python)**

Python supports multiple inheritance, allowing a class to inherit from multiple parent classes.

```python
class Flyable:
    def fly(self):
        return f"{self.name} is flying"
    
    def land(self):
        return f"{self.name} has landed"

class Swimmable:
    def swim(self):
        return f"{self.name} is swimming"
    
    def dive(self):
        return f"{self.name} is diving"

class Duck(Animal, Flyable, Swimmable):
    def __init__(self, name):
        Animal.__init__(self, name, "Duck")
    
    def make_sound(self):
        return "Quack!"
    
    def get_info(self):
        return f"{self.name} is a {self.species} that can fly and swim"

# Using multiple inheritance
duck = Duck("Donald")
print(duck.make_sound())  # Quack!
print(duck.fly())         # Donald is flying
print(duck.swim())        # Donald is swimming
print(duck.get_info())    # Donald is a Duck that can fly and swim

# Method Resolution Order (MRO)
print(Duck.__mro__)  # Shows the order in which methods are searched
```

### **Method Overriding and Overloading**

```python
class Calculator:
    def add(self, a, b):
        return a + b
    
    def add(self, a, b, c=0):  # Python doesn't support true overloading
        return a + b + c

class AdvancedCalculator(Calculator):
    def add(self, a, b, c=0):  # Override parent method
        result = super().add(a, b, c)
        print(f"Adding {a}, {b}, {c} = {result}")
        return result
    
    def multiply(self, *args):  # Variable arguments
        result = 1
        for num in args:
            result *= num
        return result

calc = AdvancedCalculator()
print(calc.add(1, 2))      # Adding 1, 2, 0 = 3
print(calc.add(1, 2, 3))   # Adding 1, 2, 3 = 6
print(calc.multiply(2, 3, 4))  # 24
```

```javascript
class Calculator {
    add(a, b, c = 0) {
        return a + b + c;
    }
}

class AdvancedCalculator extends Calculator {
    add(a, b, c = 0) {
        // Override parent method
        const result = super.add(a, b, c);
        console.log(`Adding ${a}, ${b}, ${c} = ${result}`);
        return result;
    }
    
    multiply(...args) {
        // Rest parameters
        return args.reduce((result, num) => result * num, 1);
    }
}

const calc = new AdvancedCalculator();
console.log(calc.add(1, 2));      // Adding 1, 2, 0 = 3
console.log(calc.add(1, 2, 3));   // Adding 1, 2, 3 = 6
console.log(calc.multiply(2, 3, 4));  // 24
```

## 🔒 **Encapsulation**

Encapsulation is the bundling of data and methods that operate on that data within a single unit.

### **Access Modifiers**

#### **Python (Convention-based)**

```python
class BankAccount:
    def __init__(self, account_number, balance):
        self.account_number = account_number  # Public
        self._balance = balance               # Protected (convention)
        self.__pin = "1234"                  # Private (name mangling)
    
    def deposit(self, amount):
        if amount > 0:
            self._balance += amount
            return f"Deposited ${amount}. New balance: ${self._balance}"
        return "Invalid amount"
    
    def withdraw(self, amount, pin):
        if pin != self.__pin:
            return "Invalid PIN"
        if 0 < amount <= self._balance:
            self._balance -= amount
            return f"Withdrew ${amount}. New balance: ${self._balance}"
        return "Insufficient funds"
    
    def get_balance(self):
        return self._balance
    
    def _validate_pin(self, pin):  # Protected method
        return pin == self.__pin

# Usage
account = BankAccount("12345", 1000)
print(account.deposit(500))        # Deposited $500. New balance: $1500
print(account.withdraw(200, "1234"))  # Withdrew $200. New balance: $1300
print(account.get_balance())       # 1300

# Accessing protected and private members (not recommended)
print(account._balance)            # 1300 (works but not recommended)
# print(account.__pin)            # AttributeError
print(account._BankAccount__pin)   # 1234 (name mangling)
```

#### **JavaScript (True Encapsulation with ES2022+)**

```javascript
class BankAccount {
    constructor(accountNumber, balance) {
        this.accountNumber = accountNumber;  // Public
        this._balance = balance;             // Protected (convention)
        this.#pin = "1234";                 // Private (ES2022+)
    }
    
    deposit(amount) {
        if (amount > 0) {
            this._balance += amount;
            return `Deposited $${amount}. New balance: $${this._balance}`;
        }
        return "Invalid amount";
    }
    
    withdraw(amount, pin) {
        if (pin !== this.#pin) {
            return "Invalid PIN";
        }
        if (amount > 0 && amount <= this._balance) {
            this._balance -= amount;
            return `Withdrew $${amount}. New balance: $${this._balance}`;
        }
        return "Insufficient funds";
    }
    
    getBalance() {
        return this._balance;
    }
    
    _validatePin(pin) {  // Protected method (convention)
        return pin === this.#pin;
    }
}

// Usage
const account = new BankAccount("12345", 1000);
console.log(account.deposit(500));        // Deposited $500. New balance: $1500
console.log(account.withdraw(200, "1234"));  // Withdrew $200. New balance: $1300
console.log(account.getBalance());        // 1300

// Accessing protected and private members
console.log(account._balance);            // 1300 (works but not recommended)
// console.log(account.#pin);            // SyntaxError: Private field
```

### **Properties and Getters/Setters**

```python
class Temperature:
    def __init__(self, celsius):
        self._celsius = celsius
    
    @property
    def celsius(self):
        return self._celsius
    
    @celsius.setter
    def celsius(self, value):
        if value < -273.15:
            raise ValueError("Temperature below absolute zero is not possible")
        self._celsius = value
    
    @property
    def fahrenheit(self):
        return (self._celsius * 9/5) + 32
    
    @fahrenheit.setter
    def fahrenheit(self, value):
        self.celsius = (value - 32) * 5/9
    
    @property
    def kelvin(self):
        return self._celsius + 273.15
    
    @kelvin.setter
    def kelvin(self, value):
        self.celsius = value - 273.15

# Usage
temp = Temperature(25)
print(f"Celsius: {temp.celsius}°C")      # Celsius: 25°C
print(f"Fahrenheit: {temp.fahrenheit}°F") # Fahrenheit: 77.0°F
print(f"Kelvin: {temp.kelvin}K")         # Kelvin: 298.15K

temp.fahrenheit = 100
print(f"Celsius: {temp.celsius}°C")      # Celsius: 37.77777777777778°C
```

```javascript
class Temperature {
    constructor(celsius) {
        this._celsius = celsius;
    }
    
    get celsius() {
        return this._celsius;
    }
    
    set celsius(value) {
        if (value < -273.15) {
            throw new Error("Temperature below absolute zero is not possible");
        }
        this._celsius = value;
    }
    
    get fahrenheit() {
        return (this._celsius * 9/5) + 32;
    }
    
    set fahrenheit(value) {
        this.celsius = (value - 32) * 5/9;
    }
    
    get kelvin() {
        return this._celsius + 273.15;
    }
    
    set kelvin(value) {
        this.celsius = value - 273.15;
    }
}

// Usage
const temp = new Temperature(25);
console.log(`Celsius: ${temp.celsius}°C`);      // Celsius: 25°C
console.log(`Fahrenheit: ${temp.fahrenheit}°F`); // Fahrenheit: 77°F
console.log(`Kelvin: ${temp.kelvin}K`);         // Kelvin: 298.15K

temp.fahrenheit = 100;
console.log(`Celsius: ${temp.celsius}°C`);      // Celsius: 37.77777777777778°C
```

## 🔄 **Polymorphism**

Polymorphism allows objects of different classes to be treated as objects of a common superclass.

### **Method Overriding**

```python
class Shape:
    def area(self):
        pass
    
    def perimeter(self):
        pass

class Rectangle(Shape):
    def __init__(self, width, height):
        self.width = width
        self.height = height
    
    def area(self):
        return self.width * self.height
    
    def perimeter(self):
        return 2 * (self.width + self.height)

class Circle(Shape):
    def __init__(self, radius):
        self.radius = radius
    
    def area(self):
        import math
        return math.pi * self.radius ** 2
    
    def perimeter(self):
        import math
        return 2 * math.pi * self.radius

class Triangle(Shape):
    def __init__(self, a, b, c):
        self.a = a
        self.b = b
        self.c = c
    
    def area(self):
        # Heron's formula
        s = (self.a + self.b + self.c) / 2
        return (s * (s - self.a) * (s - self.b) * (s - self.c)) ** 0.5
    
    def perimeter(self):
        return self.a + self.b + self.c

# Polymorphic behavior
shapes = [
    Rectangle(5, 3),
    Circle(4),
    Triangle(3, 4, 5)
]

for shape in shapes:
    print(f"{shape.__class__.__name__}:")
    print(f"  Area: {shape.area():.2f}")
    print(f"  Perimeter: {shape.perimeter():.2f}")
```

```javascript
class Shape {
    area() {
        // Abstract method
    }
    
    perimeter() {
        // Abstract method
    }
}

class Rectangle extends Shape {
    constructor(width, height) {
        super();
        this.width = width;
        this.height = height;
    }
    
    area() {
        return this.width * this.height;
    }
    
    perimeter() {
        return 2 * (this.width + this.height);
    }
}

class Circle extends Shape {
    constructor(radius) {
        super();
        this.radius = radius;
    }
    
    area() {
        return Math.PI * this.radius ** 2;
    }
    
    perimeter() {
        return 2 * Math.PI * this.radius;
    }
}

class Triangle extends Shape {
    constructor(a, b, c) {
        super();
        this.a = a;
        this.b = b;
        this.c = c;
    }
    
    area() {
        // Heron's formula
        const s = (this.a + this.b + this.c) / 2;
        return Math.sqrt(s * (s - this.a) * (s - this.b) * (s - this.c));
    }
    
    perimeter() {
        return this.a + this.b + this.c;
    }
}

// Polymorphic behavior
const shapes = [
    new Rectangle(5, 3),
    new Circle(4),
    new Triangle(3, 4, 5)
];

shapes.forEach(shape => {
    console.log(`${shape.constructor.name}:`);
    console.log(`  Area: ${shape.area().toFixed(2)}`);
    console.log(`  Perimeter: ${shape.perimeter().toFixed(2)}`);
});
```

### **Duck Typing (Python)**

Python uses duck typing: "If it walks like a duck and quacks like a duck, it's a duck."

```python
class Duck:
    def swim(self):
        return "Duck swimming"
    
    def quack(self):
        return "Quack quack!"

class RubberDuck:
    def swim(self):
        return "Rubber duck floating"
    
    def quack(self):
        return "Squeak squeak!"

def make_it_swim_and_quack(duck_like_object):
    print(duck_like_object.swim())
    print(duck_like_object.quack())

# Both objects work with the same function
make_it_swim_and_quack(Duck())
make_it_swim_and_quack(RubberDuck())
```

## 🎨 **Design Patterns**

### **Singleton Pattern**

Ensures a class has only one instance and provides a global point of access to it.

```python
class Singleton:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.data = []
            self._initialized = True
    
    def add_data(self, item):
        self.data.append(item)
    
    def get_data(self):
        return self.data.copy()

# Usage
singleton1 = Singleton()
singleton2 = Singleton()

print(singleton1 is singleton2)  # True (same instance)

singleton1.add_data("Hello")
singleton2.add_data("World")

print(singleton1.get_data())  # ['Hello', 'World']
print(singleton2.get_data())  # ['Hello', 'World']
```

```javascript
class Singleton {
    constructor() {
        if (Singleton.instance) {
            return Singleton.instance;
        }
        
        this.data = [];
        Singleton.instance = this;
    }
    
    addData(item) {
        this.data.push(item);
    }
    
    getData() {
        return [...this.data];
    }
}

// Usage
const singleton1 = new Singleton();
const singleton2 = new Singleton();

console.log(singleton1 === singleton2); // true (same instance)

singleton1.addData("Hello");
singleton2.addData("World");

console.log(singleton1.getData()); // ['Hello', 'World']
console.log(singleton2.getData()); // ['Hello', 'World']
```

### **Factory Pattern**

Creates objects without specifying their exact class.

```python
from abc import ABC, abstractmethod

class Animal(ABC):
    @abstractmethod
    def make_sound(self):
        pass

class Dog(Animal):
    def make_sound(self):
        return "Woof!"

class Cat(Animal):
    def make_sound(self):
        return "Meow!"

class Bird(Animal):
    def make_sound(self):
        return "Tweet!"

class AnimalFactory:
    @staticmethod
    def create_animal(animal_type):
        animals = {
            'dog': Dog,
            'cat': Cat,
            'bird': Bird
        }
        
        animal_class = animals.get(animal_type.lower())
        if animal_class:
            return animal_class()
        else:
            raise ValueError(f"Unknown animal type: {animal_type}")

# Usage
factory = AnimalFactory()

try:
    dog = factory.create_animal('dog')
    cat = factory.create_animal('cat')
    bird = factory.create_animal('bird')
    
    print(dog.make_sound())   # Woof!
    print(cat.make_sound())   # Meow!
    print(bird.make_sound())  # Tweet!
    
    # This will raise an error
    unknown = factory.create_animal('unknown')
except ValueError as e:
    print(f"Error: {e}")
```

```javascript
class Animal {
    makeSound() {
        // Abstract method
    }
}

class Dog extends Animal {
    makeSound() {
        return "Woof!";
    }
}

class Cat extends Animal {
    makeSound() {
        return "Meow!";
    }
}

class Bird extends Animal {
    makeSound() {
        return "Tweet!";
    }
}

class AnimalFactory {
    static createAnimal(animalType) {
        const animals = {
            'dog': Dog,
            'cat': Cat,
            'bird': Bird
        };
        
        const AnimalClass = animals[animalType.toLowerCase()];
        if (AnimalClass) {
            return new AnimalClass();
        } else {
            throw new Error(`Unknown animal type: ${animalType}`);
        }
    }
}

// Usage
try {
    const dog = AnimalFactory.createAnimal('dog');
    const cat = AnimalFactory.createAnimal('cat');
    const bird = AnimalFactory.createAnimal('bird');
    
    console.log(dog.makeSound());   // Woof!
    console.log(cat.makeSound());   // Meow!
    console.log(bird.makeSound());  // Tweet!
    
    // This will throw an error
    const unknown = AnimalFactory.createAnimal('unknown');
} catch (error) {
    console.log(`Error: ${error.message}`);
}
```

### **Observer Pattern**

Defines a one-to-many dependency between objects so that when one object changes state, all its dependents are notified.

```python
from abc import ABC, abstractmethod

class Observer(ABC):
    @abstractmethod
    def update(self, subject):
        pass

class Subject(ABC):
    def __init__(self):
        self._observers = []
        self._state = None
    
    def attach(self, observer):
        if observer not in self._observers:
            self._observers.append(observer)
    
    def detach(self, observer):
        self._observers.remove(observer)
    
    def notify(self):
        for observer in self._observers:
            observer.update(self)
    
    @property
    def state(self):
        return self._state
    
    @state.setter
    def state(self, value):
        self._state = value
        self.notify()

class ConcreteObserver(Observer):
    def __init__(self, name):
        self.name = name
    
    def update(self, subject):
        print(f"Observer {self.name} received update: {subject.state}")

# Usage
subject = Subject()

observer1 = ConcreteObserver("Alice")
observer2 = ConcreteObserver("Bob")
observer3 = ConcreteObserver("Charlie")

subject.attach(observer1)
subject.attach(observer2)
subject.attach(observer3)

subject.state = "Hello World!"  # All observers will be notified
```

```javascript
class Subject {
    constructor() {
        this._observers = [];
        this._state = null;
    }
    
    attach(observer) {
        if (!this._observers.includes(observer)) {
            this._observers.push(observer);
        }
    }
    
    detach(observer) {
        const index = this._observers.indexOf(observer);
        if (index > -1) {
            this._observers.splice(index, 1);
        }
    }
    
    notify() {
        this._observers.forEach(observer => observer.update(this));
    }
    
    get state() {
        return this._state;
    }
    
    set state(value) {
        this._state = value;
        this.notify();
    }
}

class ConcreteObserver {
    constructor(name) {
        this.name = name;
    }
    
    update(subject) {
        console.log(`Observer ${this.name} received update: ${subject.state}`);
    }
}

// Usage
const subject = new Subject();

const observer1 = new ConcreteObserver("Alice");
const observer2 = new ConcreteObserver("Bob");
const observer3 = new ConcreteObserver("Charlie");

subject.attach(observer1);
subject.attach(observer2);
subject.attach(observer3);

subject.state = "Hello World!"; // All observers will be notified
```

## 🏗️ **SOLID Principles**

### **Single Responsibility Principle (SRP)**

A class should have only one reason to change.

```python
# Bad: Multiple responsibilities
class UserManager:
    def create_user(self, user_data):
        # Create user logic
        pass
    
    def send_email(self, user, message):
        # Email logic
        pass
    
    def save_to_database(self, user):
        # Database logic
        pass

# Good: Single responsibility
class User:
    def __init__(self, name, email):
        self.name = name
        self.email = email

class UserRepository:
    def save(self, user):
        # Database logic only
        pass
    
    def find_by_email(self, email):
        # Database query logic only
        pass

class EmailService:
    def send_email(self, user, message):
        # Email logic only
        pass

class UserService:
    def __init__(self, user_repo, email_service):
        self.user_repo = user_repo
        self.email_service = email_service
    
    def create_user(self, name, email):
        user = User(name, email)
        self.user_repo.save(user)
        self.email_service.send_email(user, "Welcome!")
        return user
```

### **Open/Closed Principle (OCP)**

Software entities should be open for extension but closed for modification.

```python
from abc import ABC, abstractmethod

class PaymentMethod(ABC):
    @abstractmethod
    def process_payment(self, amount):
        pass

class CreditCardPayment(PaymentMethod):
    def process_payment(self, amount):
        return f"Processing ${amount} via Credit Card"

class PayPalPayment(PaymentMethod):
    def process_payment(self, amount):
        return f"Processing ${amount} via PayPal"

class BitcoinPayment(PaymentMethod):
    def process_payment(self, amount):
        return f"Processing ${amount} via Bitcoin"

class PaymentProcessor:
    def __init__(self):
        self.payment_methods = {}
    
    def register_payment_method(self, name, payment_method):
        self.payment_methods[name] = payment_method
    
    def process_payment(self, method_name, amount):
        if method_name in self.payment_methods:
            return self.payment_methods[method_name].process_payment(amount)
        else:
            raise ValueError(f"Unknown payment method: {method_name}")

# Usage
processor = PaymentProcessor()
processor.register_payment_method("credit_card", CreditCardPayment())
processor.register_payment_method("paypal", PayPalPayment())
processor.register_payment_method("bitcoin", BitcoinPayment())

print(processor.process_payment("credit_card", 100))
print(processor.process_payment("paypal", 50))
print(processor.process_payment("bitcoin", 25))
```

## 🎯 **Best Practices**

### **Composition over Inheritance**

```python
class Engine:
    def start(self):
        return "Engine started"
    
    def stop(self):
        return "Engine stopped"

class Wheels:
    def rotate(self):
        return "Wheels rotating"

class Car:
    def __init__(self):
        self.engine = Engine()
        self.wheels = Wheels()
    
    def start(self):
        return self.engine.start()
    
    def drive(self):
        return f"{self.engine.start()}, {self.wheels.rotate()}"

# Usage
car = Car()
print(car.start())   # Engine started
print(car.drive())   # Engine started, Wheels rotating
```

### **Interface Segregation**

```python
from abc import ABC, abstractmethod

class Workable(ABC):
    @abstractmethod
    def work(self):
        pass

class Eatable(ABC):
    @abstractmethod
    def eat(self):
        pass

class Sleepable(ABC):
    @abstractmethod
    def sleep(self):
        pass

class Human(Workable, Eatable, Sleepable):
    def work(self):
        return "Human working"
    
    def eat(self):
        return "Human eating"
    
    def sleep(self):
        return "Human sleeping"

class Robot(Workable):
    def work(self):
        return "Robot working"
    # Robot doesn't need eat() or sleep()
```

## 🏋️ **Practice Problems**

1. **Implement a simple banking system with Account, SavingsAccount, and CheckingAccount classes**
2. **Create a shape hierarchy with area and perimeter calculations**
3. **Build a simple event system using the Observer pattern**
4. **Implement a factory for creating different types of vehicles**
5. **Design a library management system with proper encapsulation**

## 📚 **Further Reading**

- **Books**: "Design Patterns" by Gang of Four, "Clean Code" by Robert Martin
- **Online**: Refactoring Guru, SourceMaking
- **Practice**: LeetCode OOP problems, HackerRank OOP challenges

---

**🏗️ Object-Oriented Programming provides a powerful way to organize and structure code. Mastering OOP principles will make you a better programmer and help you write more maintainable, scalable software!**