# 📊 **DATA STRUCTURES**

> **Master the fundamental building blocks of programming** - From basic arrays to complex trees and graphs

## 🎯 **Overview**

Data structures are the foundation of efficient algorithms and software design. They provide ways to organize, store, and access data efficiently. Understanding data structures is crucial for writing performant code and solving complex problems.

### **🌟 What You'll Learn**

- **Basic Structures**: Arrays, linked lists, stacks, queues
- **Tree Structures**: Binary trees, heaps, AVL trees
- **Graph Structures**: Adjacency lists, adjacency matrices
- **Hash Tables**: Collision resolution, load factor
- **Advanced Structures**: Tries, segment trees, disjoint sets

## 🔤 **Basic Data Structures**

### **Arrays**

Arrays are the most fundamental data structure - a collection of elements stored in contiguous memory.

#### **Array Operations**

```python
# Python implementation
class DynamicArray:
    def __init__(self, initial_capacity=10):
        self.capacity = initial_capacity
        self.size = 0
        self.data = [None] * initial_capacity
    
    def __getitem__(self, index):
        if index < 0 or index >= self.size:
            raise IndexError("Index out of bounds")
        return self.data[index]
    
    def __setitem__(self, index, value):
        if index < 0 or index >= self.size:
            raise IndexError("Index out of bounds")
        self.data[index] = value
    
    def append(self, value):
        if self.size >= self.capacity:
            self._resize(2 * self.capacity)
        self.data[self.size] = value
        self.size += 1
    
    def _resize(self, new_capacity):
        new_data = [None] * new_capacity
        for i in range(self.size):
            new_data[i] = self.data[i]
        self.data = new_data
        self.capacity = new_capacity
    
    def __len__(self):
        return self.size
    
    def __str__(self):
        return str(self.data[:self.size])

# Usage
arr = DynamicArray()
arr.append(1)
arr.append(2)
arr.append(3)
print(arr)  # [1, 2, 3]
print(arr[1])  # 2
```

```javascript
// JavaScript implementation
class DynamicArray {
    constructor(initialCapacity = 10) {
        this.capacity = initialCapacity;
        this.size = 0;
        this.data = new Array(initialCapacity);
    }
    
    get(index) {
        if (index < 0 || index >= this.size) {
            throw new Error("Index out of bounds");
        }
        return this.data[index];
    }
    
    set(index, value) {
        if (index < 0 || index >= this.size) {
            throw new Error("Index out of bounds");
        }
        this.data[index] = value;
    }
    
    push(value) {
        if (this.size >= this.capacity) {
            this._resize(2 * this.capacity);
        }
        this.data[this.size] = value;
        this.size++;
    }
    
    _resize(newCapacity) {
        const newData = new Array(newCapacity);
        for (let i = 0; i < this.size; i++) {
            newData[i] = this.data[i];
        }
        this.data = newData;
        this.capacity = newCapacity;
    }
    
    get length() {
        return this.size;
    }
    
    toString() {
        return this.data.slice(0, this.size).toString();
    }
}

// Usage
const arr = new DynamicArray();
arr.push(1);
arr.push(2);
arr.push(3);
console.log(arr.toString()); // "1,2,3"
console.log(arr.get(1));     // 2
```

#### **Array Complexity Analysis**

| Operation | Time Complexity | Space Complexity |
|-----------|----------------|------------------|
| Access    | O(1)           | O(1)             |
| Search    | O(n)           | O(1)             |
| Insertion | O(n)           | O(1)             |
| Deletion  | O(n)           | O(1)             |

### **Linked Lists**

Linked lists are collections of nodes where each node contains data and a reference to the next node.

#### **Singly Linked List**

```python
class Node:
    def __init__(self, data):
        self.data = data
        self.next = None

class SinglyLinkedList:
    def __init__(self):
        self.head = None
        self.size = 0
    
    def append(self, data):
        new_node = Node(data)
        if not self.head:
            self.head = new_node
        else:
            current = self.head
            while current.next:
                current = current.next
            current.next = new_node
        self.size += 1
    
    def prepend(self, data):
        new_node = Node(data)
        new_node.next = self.head
        self.head = new_node
        self.size += 1
    
    def delete(self, data):
        if not self.head:
            return False
        
        if self.head.data == data:
            self.head = self.head.next
            self.size -= 1
            return True
        
        current = self.head
        while current.next:
            if current.next.data == data:
                current.next = current.next.next
                self.size -= 1
                return True
            current = current.next
        return False
    
    def search(self, data):
        current = self.head
        while current:
            if current.data == data:
                return True
            current = current.next
        return False
    
    def __str__(self):
        elements = []
        current = self.head
        while current:
            elements.append(str(current.data))
            current = current.next
        return " -> ".join(elements) + " -> None"

# Usage
ll = SinglyLinkedList()
ll.append(1)
ll.append(2)
ll.append(3)
ll.prepend(0)
print(ll)  # 0 -> 1 -> 2 -> 3 -> None
print(ll.search(2))  # True
ll.delete(2)
print(ll)  # 0 -> 1 -> 3 -> None
```

```javascript
class Node {
    constructor(data) {
        this.data = data;
        this.next = null;
    }
}

class SinglyLinkedList {
    constructor() {
        this.head = null;
        this.size = 0;
    }
    
    append(data) {
        const newNode = new Node(data);
        if (!this.head) {
            this.head = newNode;
        } else {
            let current = this.head;
            while (current.next) {
                current = current.next;
            }
            current.next = newNode;
        }
        this.size++;
    }
    
    prepend(data) {
        const newNode = new Node(data);
        newNode.next = this.head;
        this.head = newNode;
        this.size++;
    }
    
    delete(data) {
        if (!this.head) return false;
        
        if (this.head.data === data) {
            this.head = this.head.next;
            this.size--;
            return true;
        }
        
        let current = this.head;
        while (current.next) {
            if (current.next.data === data) {
                current.next = current.next.next;
                this.size--;
                return true;
            }
            current = current.next;
        }
        return false;
    }
    
    search(data) {
        let current = this.head;
        while (current) {
            if (current.data === data) return true;
            current = current.next;
        }
        return false;
    }
    
    toString() {
        const elements = [];
        let current = this.head;
        while (current) {
            elements.push(current.data);
            current = current.next;
        }
        return elements.join(" -> ") + " -> null";
    }
}

// Usage
const ll = new SinglyLinkedList();
ll.append(1);
ll.append(2);
ll.append(3);
ll.prepend(0);
console.log(ll.toString()); // "0 -> 1 -> 2 -> 3 -> null"
console.log(ll.search(2));  // true
ll.delete(2);
console.log(ll.toString()); // "0 -> 1 -> 3 -> null"
```

#### **Doubly Linked List**

```python
class DoublyNode:
    def __init__(self, data):
        self.data = data
        self.prev = None
        self.next = None

class DoublyLinkedList:
    def __init__(self):
        self.head = None
        self.tail = None
        self.size = 0
    
    def append(self, data):
        new_node = DoublyNode(data)
        if not self.head:
            self.head = new_node
            self.tail = new_node
        else:
            new_node.prev = self.tail
            self.tail.next = new_node
            self.tail = new_node
        self.size += 1
    
    def delete(self, data):
        if not self.head:
            return False
        
        current = self.head
        while current:
            if current.data == data:
                if current.prev:
                    current.prev.next = current.next
                else:
                    self.head = current.next
                
                if current.next:
                    current.next.prev = current.prev
                else:
                    self.tail = current.prev
                
                self.size -= 1
                return True
            current = current.next
        return False
```

#### **Linked List Complexity Analysis**

| Operation | Time Complexity | Space Complexity |
|-----------|----------------|------------------|
| Access    | O(n)           | O(1)             |
| Search    | O(n)           | O(1)             |
| Insertion | O(1)           | O(1)             |
| Deletion  | O(n)           | O(1)             |

### **Stacks**

Stacks are LIFO (Last In, First Out) data structures.

```python
class Stack:
    def __init__(self):
        self.items = []
    
    def push(self, item):
        self.items.append(item)
    
    def pop(self):
        if not self.is_empty():
            return self.items.pop()
        raise IndexError("Stack is empty")
    
    def peek(self):
        if not self.is_empty():
            return self.items[-1]
        raise IndexError("Stack is empty")
    
    def is_empty(self):
        return len(self.items) == 0
    
    def size(self):
        return len(self.items)
    
    def __str__(self):
        return str(self.items)

# Usage
stack = Stack()
stack.push(1)
stack.push(2)
stack.push(3)
print(stack)      # [1, 2, 3]
print(stack.pop()) # 3
print(stack.peek()) # 2
```

```javascript
class Stack {
    constructor() {
        this.items = [];
    }
    
    push(item) {
        this.items.push(item);
    }
    
    pop() {
        if (this.isEmpty()) {
            throw new Error("Stack is empty");
        }
        return this.items.pop();
    }
    
    peek() {
        if (this.isEmpty()) {
            throw new Error("Stack is empty");
        }
        return this.items[this.items.length - 1];
    }
    
    isEmpty() {
        return this.items.length === 0;
    }
    
    size() {
        return this.items.length;
    }
    
    toString() {
        return this.items.toString();
    }
}

// Usage
const stack = new Stack();
stack.push(1);
stack.push(2);
stack.push(3);
console.log(stack.toString()); // "1,2,3"
console.log(stack.pop());      // 3
console.log(stack.peek());     // 2
```

#### **Stack Applications**

- **Function call stack**: Managing function calls and returns
- **Undo/Redo**: Storing previous states
- **Expression evaluation**: Converting infix to postfix notation
- **Backtracking**: Storing decision points

### **Queues**

Queues are FIFO (First In, First Out) data structures.

```python
from collections import deque

class Queue:
    def __init__(self):
        self.items = deque()
    
    def enqueue(self, item):
        self.items.append(item)
    
    def dequeue(self):
        if not self.is_empty():
            return self.items.popleft()
        raise IndexError("Queue is empty")
    
    def front(self):
        if not self.is_empty():
            return self.items[0]
        raise IndexError("Queue is empty")
    
    def is_empty(self):
        return len(self.items) == 0
    
    def size(self):
        return len(self.items)
    
    def __str__(self):
        return str(list(self.items))

# Usage
queue = Queue()
queue.enqueue(1)
queue.enqueue(2)
queue.enqueue(3)
print(queue)        # [1, 2, 3]
print(queue.dequeue()) # 1
print(queue.front())   # 2
```

```javascript
class Queue {
    constructor() {
        this.items = [];
    }
    
    enqueue(item) {
        this.items.push(item);
    }
    
    dequeue() {
        if (this.isEmpty()) {
            throw new Error("Queue is empty");
        }
        return this.items.shift();
    }
    
    front() {
        if (this.isEmpty()) {
            throw new Error("Queue is empty");
        }
        return this.items[0];
    }
    
    isEmpty() {
        return this.items.length === 0;
    }
    
    size() {
        return this.items.length;
    }
    
    toString() {
        return this.items.toString();
    }
}

// Usage
const queue = new Queue();
queue.enqueue(1);
queue.enqueue(2);
queue.enqueue(3);
console.log(queue.toString()); // "1,2,3"
console.log(queue.dequeue());  // 1
console.log(queue.front());    // 2
```

#### **Priority Queue**

```python
import heapq

class PriorityQueue:
    def __init__(self):
        self.heap = []
        self.index = 0
    
    def push(self, item, priority):
        heapq.heappush(self.heap, (priority, self.index, item))
        self.index += 1
    
    def pop(self):
        if self.heap:
            return heapq.heappop(self.heap)[2]
        raise IndexError("Priority queue is empty")
    
    def is_empty(self):
        return len(self.heap) == 0
    
    def size(self):
        return len(self.heap)

# Usage
pq = PriorityQueue()
pq.push("task1", 3)
pq.push("task2", 1)
pq.push("task3", 2)
print(pq.pop())  # "task2" (highest priority)
print(pq.pop())  # "task3"
print(pq.pop())  # "task1"
```

## 🌳 **Tree Data Structures**

### **Binary Tree**

A tree where each node has at most two children.

```python
class TreeNode:
    def __init__(self, data):
        self.data = data
        self.left = None
        self.right = None

class BinaryTree:
    def __init__(self):
        self.root = None
    
    def insert(self, data):
        if not self.root:
            self.root = TreeNode(data)
            return
        
        queue = [self.root]
        while queue:
            node = queue.pop(0)
            if not node.left:
                node.left = TreeNode(data)
                return
            if not node.right:
                node.right = TreeNode(data)
                return
            queue.append(node.left)
            queue.append(node.right)
    
    def inorder_traversal(self, node=None):
        if node is None:
            node = self.root
        if node:
            self.inorder_traversal(node.left)
            print(node.data, end=" ")
            self.inorder_traversal(node.right)
    
    def preorder_traversal(self, node=None):
        if node is None:
            node = self.root
        if node:
            print(node.data, end=" ")
            self.preorder_traversal(node.left)
            self.preorder_traversal(node.right)
    
    def postorder_traversal(self, node=None):
        if node is None:
            node = self.root
        if node:
            self.postorder_traversal(node.left)
            self.postorder_traversal(node.right)
            print(node.data, end=" ")
    
    def level_order_traversal(self):
        if not self.root:
            return
        
        queue = [self.root]
        while queue:
            node = queue.pop(0)
            print(node.data, end=" ")
            if node.left:
                queue.append(node.left)
            if node.right:
                queue.append(node.right)

# Usage
tree = BinaryTree()
tree.insert(1)
tree.insert(2)
tree.insert(3)
tree.insert(4)
tree.insert(5)

print("Inorder:", end=" ")
tree.inorder_traversal()
print()

print("Preorder:", end=" ")
tree.preorder_traversal()
print()

print("Postorder:", end=" ")
tree.postorder_traversal()
print()

print("Level order:", end=" ")
tree.level_order_traversal()
print()
```

```javascript
class TreeNode {
    constructor(data) {
        this.data = data;
        this.left = null;
        this.right = null;
    }
}

class BinaryTree {
    constructor() {
        this.root = null;
    }
    
    insert(data) {
        if (!this.root) {
            this.root = new TreeNode(data);
            return;
        }
        
        const queue = [this.root];
        while (queue.length > 0) {
            const node = queue.shift();
            if (!node.left) {
                node.left = new TreeNode(data);
                return;
            }
            if (!node.right) {
                node.right = new TreeNode(data);
                return;
            }
            queue.push(node.left);
            queue.push(node.right);
        }
    }
    
    inorderTraversal(node = this.root) {
        if (node) {
            this.inorderTraversal(node.left);
            process.stdout.write(node.data + " ");
            this.inorderTraversal(node.right);
        }
    }
    
    preorderTraversal(node = this.root) {
        if (node) {
            process.stdout.write(node.data + " ");
            this.preorderTraversal(node.left);
            this.preorderTraversal(node.right);
        }
    }
    
    postorderTraversal(node = this.root) {
        if (node) {
            this.postorderTraversal(node.left);
            this.postorderTraversal(node.right);
            process.stdout.write(node.data + " ");
        }
    }
    
    levelOrderTraversal() {
        if (!this.root) return;
        
        const queue = [this.root];
        while (queue.length > 0) {
            const node = queue.shift();
            process.stdout.write(node.data + " ");
            if (node.left) queue.push(node.left);
            if (node.right) queue.push(node.right);
        }
    }
}

// Usage
const tree = new BinaryTree();
tree.insert(1);
tree.insert(2);
tree.insert(3);
tree.insert(4);
tree.insert(5);

process.stdout.write("Inorder: ");
tree.inorderTraversal();
console.log();

process.stdout.write("Preorder: ");
tree.preorderTraversal();
console.log();

process.stdout.write("Postorder: ");
tree.postorderTraversal();
console.log();

process.stdout.write("Level order: ");
tree.levelOrderTraversal();
console.log();
```

### **Binary Search Tree (BST)**

A binary tree where the left subtree contains only nodes with values less than the parent, and the right subtree contains only nodes with values greater than the parent.

```python
class BSTNode:
    def __init__(self, data):
        self.data = data
        self.left = None
        self.right = None

class BinarySearchTree:
    def __init__(self):
        self.root = None
    
    def insert(self, data):
        if not self.root:
            self.root = BSTNode(data)
            return
        
        self._insert_recursive(self.root, data)
    
    def _insert_recursive(self, node, data):
        if data < node.data:
            if node.left is None:
                node.left = BSTNode(data)
            else:
                self._insert_recursive(node.left, data)
        else:
            if node.right is None:
                node.right = BSTNode(data)
            else:
                self._insert_recursive(node.right, data)
    
    def search(self, data):
        return self._search_recursive(self.root, data)
    
    def _search_recursive(self, node, data):
        if node is None or node.data == data:
            return node
        
        if data < node.data:
            return self._search_recursive(node.left, data)
        return self._search_recursive(node.right, data)
    
    def inorder_traversal(self, node=None):
        if node is None:
            node = self.root
        if node:
            self.inorder_traversal(node.left)
            print(node.data, end=" ")
            self.inorder_traversal(node.right)
    
    def find_min(self, node=None):
        if node is None:
            node = self.root
        while node.left:
            node = node.left
        return node.data
    
    def find_max(self, node=None):
        if node is None:
            node = self.root
        while node.right:
            node = node.right
        return node.data

# Usage
bst = BinarySearchTree()
bst.insert(50)
bst.insert(30)
bst.insert(70)
bst.insert(20)
bst.insert(40)
bst.insert(60)
bst.insert(80)

print("Inorder traversal (sorted):", end=" ")
bst.inorder_traversal()
print()

print(f"Minimum value: {bst.find_min()}")
print(f"Maximum value: {bst.find_max()}")

result = bst.search(40)
if result:
    print("Found 40 in BST")
else:
    print("40 not found in BST")
```

### **Heap**

A specialized tree-based data structure that satisfies the heap property.

```python
import heapq

class MinHeap:
    def __init__(self):
        self.heap = []
    
    def push(self, value):
        heapq.heappush(self.heap, value)
    
    def pop(self):
        if self.heap:
            return heapq.heappop(self.heap)
        raise IndexError("Heap is empty")
    
    def peek(self):
        if self.heap:
            return self.heap[0]
        raise IndexError("Heap is empty")
    
    def size(self):
        return len(self.heap)
    
    def is_empty(self):
        return len(self.heap) == 0

class MaxHeap:
    def __init__(self):
        self.heap = []
    
    def push(self, value):
        heapq.heappush(self.heap, -value)
    
    def pop(self):
        if self.heap:
            return -heapq.heappop(self.heap)
        raise IndexError("Heap is empty")
    
    def peek(self):
        if self.heap:
            return -self.heap[0]
        raise IndexError("Heap is empty")
    
    def size(self):
        return len(self.heap)
    
    def is_empty(self):
        return len(self.heap) == 0

# Usage
min_heap = MinHeap()
min_heap.push(3)
min_heap.push(1)
min_heap.push(4)
min_heap.push(2)

print("Min heap elements:")
while not min_heap.is_empty():
    print(min_heap.pop(), end=" ")
print()

max_heap = MaxHeap()
max_heap.push(3)
max_heap.push(1)
max_heap.push(4)
max_heap.push(2)

print("Max heap elements:")
while not max_heap.is_empty():
    print(max_heap.pop(), end=" ")
print()
```

## 🕸️ **Graph Data Structures**

### **Adjacency List**

A graph representation where each vertex maintains a list of adjacent vertices.

```python
from collections import defaultdict

class Graph:
    def __init__(self):
        self.graph = defaultdict(list)
    
    def add_edge(self, u, v):
        self.graph[u].append(v)
        # For undirected graph, uncomment the next line
        # self.graph[v].append(u)
    
    def print_graph(self):
        for vertex in self.graph:
            print(f"{vertex} -> {self.graph[vertex]}")
    
    def bfs(self, start_vertex):
        visited = set()
        queue = [start_vertex]
        visited.add(start_vertex)
        
        while queue:
            vertex = queue.pop(0)
            print(vertex, end=" ")
            
            for neighbor in self.graph[vertex]:
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append(neighbor)
        print()
    
    def dfs(self, start_vertex, visited=None):
        if visited is None:
            visited = set()
        
        visited.add(start_vertex)
        print(start_vertex, end=" ")
        
        for neighbor in self.graph[start_vertex]:
            if neighbor not in visited:
                self.dfs(neighbor, visited)

# Usage
g = Graph()
g.add_edge(0, 1)
g.add_edge(0, 2)
g.add_edge(1, 2)
g.add_edge(2, 0)
g.add_edge(2, 3)
g.add_edge(3, 3)

print("Graph representation:")
g.print_graph()

print("BFS starting from vertex 2:")
g.bfs(2)

print("DFS starting from vertex 2:")
g.dfs(2)
print()
```

```javascript
class Graph {
    constructor() {
        this.graph = new Map();
    }
    
    addEdge(u, v) {
        if (!this.graph.has(u)) {
            this.graph.set(u, []);
        }
        this.graph.get(u).push(v);
        // For undirected graph, uncomment the next lines
        // if (!this.graph.has(v)) {
        //     this.graph.set(v, []);
        // }
        // this.graph.get(v).push(u);
    }
    
    printGraph() {
        for (let [vertex, neighbors] of this.graph) {
            console.log(`${vertex} -> ${neighbors.join(', ')}`);
        }
    }
    
    bfs(startVertex) {
        const visited = new Set();
        const queue = [startVertex];
        visited.add(startVertex);
        
        while (queue.length > 0) {
            const vertex = queue.shift();
            process.stdout.write(vertex + " ");
            
            const neighbors = this.graph.get(vertex) || [];
            for (const neighbor of neighbors) {
                if (!visited.has(neighbor)) {
                    visited.add(neighbor);
                    queue.push(neighbor);
                }
            }
        }
        console.log();
    }
    
    dfs(startVertex, visited = new Set()) {
        visited.add(startVertex);
        process.stdout.write(startVertex + " ");
        
        const neighbors = this.graph.get(startVertex) || [];
        for (const neighbor of neighbors) {
            if (!visited.has(neighbor)) {
                this.dfs(neighbor, visited);
            }
        }
    }
}

// Usage
const g = new Graph();
g.addEdge(0, 1);
g.addEdge(0, 2);
g.addEdge(1, 2);
g.addEdge(2, 0);
g.addEdge(2, 3);
g.addEdge(3, 3);

console.log("Graph representation:");
g.printGraph();

process.stdout.write("BFS starting from vertex 2: ");
g.bfs(2);

process.stdout.write("DFS starting from vertex 2: ");
g.dfs(2);
console.log();
```

### **Adjacency Matrix**

A 2D matrix where `matrix[i][j]` indicates whether there's an edge between vertices `i` and `j`.

```python
class AdjacencyMatrixGraph:
    def __init__(self, num_vertices):
        self.num_vertices = num_vertices
        self.matrix = [[0] * num_vertices for _ in range(num_vertices)]
    
    def add_edge(self, u, v):
        self.matrix[u][v] = 1
        # For undirected graph, uncomment the next line
        # self.matrix[v][u] = 1
    
    def remove_edge(self, u, v):
        self.matrix[u][v] = 0
        # For undirected graph, uncomment the next line
        # self.matrix[v][u] = 0
    
    def has_edge(self, u, v):
        return self.matrix[u][v] == 1
    
    def print_matrix(self):
        for row in self.matrix:
            print(row)
    
    def get_neighbors(self, vertex):
        neighbors = []
        for i in range(self.num_vertices):
            if self.matrix[vertex][i] == 1:
                neighbors.append(i)
        return neighbors

# Usage
g = AdjacencyMatrixGraph(4)
g.add_edge(0, 1)
g.add_edge(0, 2)
g.add_edge(1, 2)
g.add_edge(2, 3)

print("Adjacency Matrix:")
g.print_matrix()

print(f"Neighbors of vertex 0: {g.get_neighbors(0)}")
print(f"Edge (0, 1) exists: {g.has_edge(0, 1)}")
print(f"Edge (1, 0) exists: {g.has_edge(1, 0)}")
```

## 🔑 **Hash Tables**

Hash tables provide average O(1) time complexity for insertions, deletions, and lookups.

```python
class HashTable:
    def __init__(self, size=10):
        self.size = size
        self.table = [[] for _ in range(size)]
    
    def _hash_function(self, key):
        return hash(key) % self.size
    
    def insert(self, key, value):
        hash_value = self._hash_function(key)
        bucket = self.table[hash_value]
        
        # Check if key already exists
        for i, (existing_key, existing_value) in enumerate(bucket):
            if existing_key == key:
                bucket[i] = (key, value)
                return
        
        # Add new key-value pair
        bucket.append((key, value))
    
    def get(self, key):
        hash_value = self._hash_function(key)
        bucket = self.table[hash_value]
        
        for existing_key, existing_value in bucket:
            if existing_key == key:
                return existing_value
        
        raise KeyError(f"Key '{key}' not found")
    
    def delete(self, key):
        hash_value = self._hash_function(key)
        bucket = self.table[hash_value]
        
        for i, (existing_key, existing_value) in enumerate(bucket):
            if existing_key == key:
                del bucket[i]
                return
        
        raise KeyError(f"Key '{key}' not found")
    
    def __str__(self):
        result = []
        for i, bucket in enumerate(self.table):
            if bucket:
                result.append(f"Bucket {i}: {bucket}")
        return "\n".join(result)

# Usage
ht = HashTable()
ht.insert("name", "John")
ht.insert("age", 30)
ht.insert("city", "New York")

print("Hash Table:")
print(ht)

print(f"Name: {ht.get('name')}")
print(f"Age: {ht.get('age')}")

ht.delete("age")
print("\nAfter deleting 'age':")
print(ht)
```

```javascript
class HashTable {
    constructor(size = 10) {
        this.size = size;
        this.table = new Array(size).fill(null).map(() => []);
    }
    
    _hashFunction(key) {
        let hash = 0;
        for (let i = 0; i < key.length; i++) {
            hash = (hash << 5) - hash + key.charCodeAt(i);
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash) % this.size;
    }
    
    insert(key, value) {
        const hashValue = this._hashFunction(key);
        const bucket = this.table[hashValue];
        
        // Check if key already exists
        for (let i = 0; i < bucket.length; i++) {
            if (bucket[i][0] === key) {
                bucket[i][1] = value;
                return;
            }
        }
        
        // Add new key-value pair
        bucket.push([key, value]);
    }
    
    get(key) {
        const hashValue = this._hashFunction(key);
        const bucket = this.table[hashValue];
        
        for (const [existingKey, existingValue] of bucket) {
            if (existingKey === key) {
                return existingValue;
            }
        }
        
        throw new Error(`Key '${key}' not found`);
    }
    
    delete(key) {
        const hashValue = this._hashFunction(key);
        const bucket = this.table[hashValue];
        
        for (let i = 0; i < bucket.length; i++) {
            if (bucket[i][0] === key) {
                bucket.splice(i, 1);
                return;
            }
        }
        
        throw new Error(`Key '${key}' not found`);
    }
    
    toString() {
        const result = [];
        for (let i = 0; i < this.table.length; i++) {
            const bucket = this.table[i];
            if (bucket.length > 0) {
                result.push(`Bucket ${i}: ${JSON.stringify(bucket)}`);
            }
        }
        return result.join('\n');
    }
}

// Usage
const ht = new HashTable();
ht.insert("name", "John");
ht.insert("age", 30);
ht.insert("city", "New York");

console.log("Hash Table:");
console.log(ht.toString());

console.log(`Name: ${ht.get('name')}`);
console.log(`Age: ${ht.get('age')}`);

ht.delete("age");
console.log("\nAfter deleting 'age':");
console.log(ht.toString());
```

## 📊 **Complexity Comparison**

| Data Structure | Access | Search | Insertion | Deletion | Space |
|----------------|--------|--------|-----------|----------|-------|
| Array          | O(1)   | O(n)   | O(n)      | O(n)     | O(n)  |
| Linked List    | O(n)   | O(n)   | O(1)      | O(n)     | O(n)  |
| Stack          | O(1)   | O(n)   | O(1)      | O(1)     | O(n)  |
| Queue          | O(1)   | O(n)   | O(1)      | O(1)     | O(n)  |
| BST            | O(log n)| O(log n)| O(log n) | O(log n) | O(n)  |
| Hash Table     | O(1)   | O(1)   | O(1)      | O(1)     | O(n)  |
| Graph (Adj List)| O(1)  | O(V+E) | O(1)      | O(V+E)   | O(V+E)|

## 🎯 **When to Use Each Data Structure**

### **Arrays**
- **Use when**: You need random access to elements
- **Avoid when**: Frequent insertions/deletions in the middle
- **Examples**: Lookup tables, matrices, image processing

### **Linked Lists**
- **Use when**: Frequent insertions/deletions at the beginning/end
- **Avoid when**: Random access is needed
- **Examples**: Undo/redo systems, browser history

### **Stacks**
- **Use when**: LIFO behavior is required
- **Examples**: Function call stack, expression evaluation

### **Queues**
- **Use when**: FIFO behavior is required
- **Examples**: Task scheduling, breadth-first search

### **Binary Search Trees**
- **Use when**: You need ordered data with efficient search
- **Avoid when**: Data is frequently modified
- **Examples**: Symbol tables, database indexing

### **Hash Tables**
- **Use when**: Fast lookups are needed
- **Avoid when**: Order matters or memory is limited
- **Examples**: Caches, database indexes, symbol tables

### **Graphs**
- **Use when**: Modeling relationships between entities
- **Examples**: Social networks, road networks, dependency graphs

## 🏋️ **Practice Problems**

1. **Implement a stack using two queues**
2. **Check if a binary tree is balanced**
3. **Find the shortest path in a graph**
4. **Implement LRU cache using hash table and doubly linked list**
5. **Serialize and deserialize a binary tree**

## 📚 **Further Reading**

- **Books**: "Data Structures and Algorithms" by Robert Lafore
- **Online**: LeetCode, HackerRank, GeeksforGeeks
- **Visualizations**: VisuAlgo, Data Structure Visualizations

---

**📊 Master data structures to build efficient algorithms and solve complex problems. The right data structure can make the difference between a solution that works and one that scales!**