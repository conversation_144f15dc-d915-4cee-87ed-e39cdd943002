# Mathematics & Machine Learning Mastery Guide

## N<PERSON><PERSON> tảng <PERSON><PERSON> học B<PERSON><PERSON> <PERSON> cho AI Engineer

Nền tảng toán học vững chắc là xương sống của AI. Phần này tập trung vào các khái niệm cốt lõi từ Đại số tuyế<PERSON> t<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ố<PERSON>, <PERSON><PERSON>, v<PERSON>ố<PERSON>ồ<PERSON>.

### Checklist <PERSON><PERSON><PERSON> thức Toán học

#### Đại số <PERSON>ến tính
- **Vectors và Vector spaces**: <PERSON>iểu diễn dữ liệu đa chiều
- **Matrix operations**: Phép nhân ma trận (cơ sở của neural networks), phép nhân <PERSON> (element-wise product)
- **Ma trận đơn vị và ma trận nghịch đảo**
- **<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> đ<PERSON> l<PERSON>, c<PERSON> sở của không gian vector**
- **<PERSON><PERSON><PERSON> (Eigenvalue) và Vector riêng (Eigenvector)**: PCA sử dụng eigendecomposition để giảm chiều dữ liệu
- **Chéo hoá ma trận (Diagonalization/Eigendecomposition)**
- **Chuẩn (Norm) của vector và ma trận** (L1, L2 norm, Frobenius norm)
- **Vết (Trace) của ma trận**

#### Giải tích
- **Đạo hàm riêng (Partial derivatives)**: Tối ưu hàm nhiều biến
- **Gradient descent**: Thuật toán tối ưu trọng tâm của ML training
- **Chain rule (Quy tắc chuỗi)**: Cơ sở của backpropagation
- **Đạo hàm bậc nhất (Gradient) và đạo hàm bậc hai (Hessian)**
- **Kiểm tra đạo hàm bằng cách xấp xỉ**

#### Xác suất Thống kê
- **Biến ngẫu nhiên**: rời rạc và liên tục
- **Hàm mật độ xác suất (PDF)**
- **Xác suất đồng thời, xác suất có điều kiện**
- **Quy tắc Bayes**
- **Biến ngẫu nhiên độc lập**
- **Kỳ vọng (Expectation), Phương sai (Variance), Ma trận hiệp phương sai (Covariance matrix)**
- **Phân phối chuẩn (Normal distribution)**: một chiều và nhiều chiều
- **Ước lượng tham số**: Maximum Likelihood Estimation (MLE) và Maximum A Posteriori Estimation (MAP)

#### Toán Rời rạc
- **Lý thuyết đồ thị (Graph theory)**: Nền tảng cho Graph Neural Networks
- **Lý thuyết tập hợp (Set theory)**: Các phép toán logic và cấu trúc dữ liệu
- **Lý thuyết thông tin (Information theory)**: Entropy (đo lường độ bất định)

#### Tối ưu Lồi (Convex Optimization)
- **Tập lồi (Convex set) và hàm lồi (Convex function)**
- **Điều kiện xác định hàm lồi** (first-order condition, second-order condition)
- **Các dạng bài toán tối ưu lồi**: Linear Programming (LP), Quadratic Programming (QP), Geometric Programming (GP)
- **Bài toán đối ngẫu (Dual problem) và điều kiện KKT (Karush-Kuhn-Tucker)**

## Practical Implementation với Python

### Linear Algebra với NumPy
```python
import numpy as np
from scipy.linalg import eig, svd
import matplotlib.pyplot as plt

class LinearAlgebraToolkit:
    def __init__(self):
        pass
    
    def matrix_operations_demo(self):
        """Demonstrate core matrix operations"""
        # Create matrices
        A = np.array([[1, 2], [3, 4]])
        B = np.array([[5, 6], [7, 8]])
        
        # Basic operations
        matrix_sum = A + B
        matrix_product = A @ B  # Matrix multiplication
        hadamard_product = A * B  # Element-wise multiplication
        
        # Matrix properties
        determinant = np.linalg.det(A)
        trace = np.trace(A)
        rank = np.linalg.matrix_rank(A)
        
        return {
            'sum': matrix_sum,
            'product': matrix_product,
            'hadamard': hadamard_product,
            'determinant': determinant,
            'trace': trace,
            'rank': rank
        }
    
    def eigenvalue_decomposition(self, matrix):
        """Perform eigenvalue decomposition"""
        eigenvalues, eigenvectors = eig(matrix)
        
        # Reconstruct original matrix
        reconstructed = eigenvectors @ np.diag(eigenvalues) @ np.linalg.inv(eigenvectors)
        
        return eigenvalues, eigenvectors, reconstructed
    
    def pca_implementation(self, data, n_components=2):
        """Principal Component Analysis implementation"""
        # Center the data
        data_centered = data - np.mean(data, axis=0)
        
        # Compute covariance matrix
        cov_matrix = np.cov(data_centered.T)
        
        # Eigenvalue decomposition
        eigenvalues, eigenvectors = eig(cov_matrix)
        
        # Sort by eigenvalues (descending)
        idx = np.argsort(eigenvalues)[::-1]
        eigenvalues = eigenvalues[idx]
        eigenvectors = eigenvectors[:, idx]
        
        # Select top n_components
        principal_components = eigenvectors[:, :n_components]
        
        # Transform data
        transformed_data = data_centered @ principal_components
        
        # Explained variance ratio
        explained_variance_ratio = eigenvalues[:n_components] / np.sum(eigenvalues)
        
        return {
            'transformed_data': transformed_data,
            'principal_components': principal_components,
            'explained_variance_ratio': explained_variance_ratio,
            'eigenvalues': eigenvalues
        }
```

### Calculus & Optimization
```python
import numpy as np
from scipy.optimize import minimize
import autograd.numpy as anp
from autograd import grad, hessian

class CalculusOptimization:
    def __init__(self):
        pass
    
    def gradient_descent_implementation(self, f, grad_f, x0, learning_rate=0.01, max_iter=1000, tol=1e-6):
        """Manual gradient descent implementation"""
        x = x0.copy()
        history = [x.copy()]
        
        for i in range(max_iter):
            gradient = grad_f(x)
            x_new = x - learning_rate * gradient
            
            # Check convergence
            if np.linalg.norm(x_new - x) < tol:
                break
                
            x = x_new
            history.append(x.copy())
        
        return x, history
    
    def automatic_differentiation_demo(self):
        """Demonstrate automatic differentiation"""
        # Define function
        def f(x):
            return anp.sum(x**2) + anp.sin(anp.sum(x))
        
        # Compute gradient and hessian automatically
        grad_f = grad(f)
        hess_f = hessian(f)
        
        # Test point
        x = anp.array([1.0, 2.0, 3.0])
        
        return {
            'function_value': f(x),
            'gradient': grad_f(x),
            'hessian': hess_f(x)
        }
    
    def newton_method(self, f, grad_f, hess_f, x0, max_iter=100, tol=1e-6):
        """Newton's method for optimization"""
        x = x0.copy()
        history = [x.copy()]
        
        for i in range(max_iter):
            gradient = grad_f(x)
            hessian_matrix = hess_f(x)
            
            # Newton step
            try:
                step = np.linalg.solve(hessian_matrix, gradient)
                x_new = x - step
            except np.linalg.LinAlgError:
                # Fallback to gradient descent if Hessian is singular
                x_new = x - 0.01 * gradient
            
            # Check convergence
            if np.linalg.norm(x_new - x) < tol:
                break
                
            x = x_new
            history.append(x.copy())
        
        return x, history
```

### Probability & Statistics
```python
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt

class ProbabilityStatistics:
    def __init__(self):
        pass
    
    def bayes_theorem_demo(self, prior, likelihood, evidence):
        """Demonstrate Bayes' theorem: P(H|E) = P(E|H) * P(H) / P(E)"""
        posterior = (likelihood * prior) / evidence
        return posterior
    
    def maximum_likelihood_estimation(self, data, distribution='normal'):
        """Maximum Likelihood Estimation for common distributions"""
        if distribution == 'normal':
            # For normal distribution: MLE of mean is sample mean, MLE of variance is sample variance
            mu_mle = np.mean(data)
            sigma2_mle = np.var(data, ddof=0)  # Population variance
            return {'mu': mu_mle, 'sigma2': sigma2_mle}
        
        elif distribution == 'exponential':
            # For exponential distribution: MLE of lambda is 1/sample_mean
            lambda_mle = 1 / np.mean(data)
            return {'lambda': lambda_mle}
    
    def central_limit_theorem_demo(self, population_dist, sample_size, num_samples=1000):
        """Demonstrate Central Limit Theorem"""
        # Generate samples
        sample_means = []
        for _ in range(num_samples):
            sample = np.random.choice(population_dist, size=sample_size)
            sample_means.append(np.mean(sample))
        
        sample_means = np.array(sample_means)
        
        # Theoretical values
        population_mean = np.mean(population_dist)
        population_std = np.std(population_dist)
        theoretical_std = population_std / np.sqrt(sample_size)
        
        return {
            'sample_means': sample_means,
            'sample_mean_of_means': np.mean(sample_means),
            'sample_std_of_means': np.std(sample_means),
            'theoretical_mean': population_mean,
            'theoretical_std': theoretical_std
        }
    
    def hypothesis_testing(self, sample1, sample2, alpha=0.05):
        """Perform t-test for difference in means"""
        # Two-sample t-test
        t_stat, p_value = stats.ttest_ind(sample1, sample2)
        
        # Effect size (Cohen's d)
        pooled_std = np.sqrt(((len(sample1) - 1) * np.var(sample1, ddof=1) + 
                             (len(sample2) - 1) * np.var(sample2, ddof=1)) / 
                            (len(sample1) + len(sample2) - 2))
        cohens_d = (np.mean(sample1) - np.mean(sample2)) / pooled_std
        
        return {
            't_statistic': t_stat,
            'p_value': p_value,
            'significant': p_value < alpha,
            'cohens_d': cohens_d,
            'effect_size': 'small' if abs(cohens_d) < 0.5 else 'medium' if abs(cohens_d) < 0.8 else 'large'
        }
```

### Machine Learning Mathematics
```python
import numpy as np
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split

class MLMathematics:
    def __init__(self):
        pass
    
    def logistic_regression_from_scratch(self, X, y, learning_rate=0.01, max_iter=1000):
        """Implement logistic regression using mathematical foundations"""
        # Add bias term
        X = np.column_stack([np.ones(X.shape[0]), X])
        
        # Initialize weights
        w = np.zeros(X.shape[1])
        
        # Gradient descent
        for i in range(max_iter):
            # Forward pass
            z = X @ w
            predictions = 1 / (1 + np.exp(-z))  # Sigmoid function
            
            # Compute cost (cross-entropy loss)
            cost = -np.mean(y * np.log(predictions + 1e-8) + (1 - y) * np.log(1 - predictions + 1e-8))
            
            # Compute gradient
            gradient = X.T @ (predictions - y) / len(y)
            
            # Update weights
            w -= learning_rate * gradient
            
            # Check convergence
            if i > 0 and abs(prev_cost - cost) < 1e-6:
                break
            prev_cost = cost
        
        return w, cost
    
    def neural_network_backpropagation(self, X, y, hidden_size=10, learning_rate=0.01, epochs=1000):
        """Implement neural network with backpropagation"""
        input_size = X.shape[1]
        output_size = 1
        
        # Initialize weights
        W1 = np.random.randn(input_size, hidden_size) * 0.1
        b1 = np.zeros((1, hidden_size))
        W2 = np.random.randn(hidden_size, output_size) * 0.1
        b2 = np.zeros((1, output_size))
        
        for epoch in range(epochs):
            # Forward pass
            z1 = X @ W1 + b1
            a1 = np.tanh(z1)  # Activation function
            z2 = a1 @ W2 + b2
            a2 = 1 / (1 + np.exp(-z2))  # Sigmoid output
            
            # Compute loss
            loss = np.mean((a2 - y.reshape(-1, 1))**2)
            
            # Backward pass
            # Output layer gradients
            dz2 = 2 * (a2 - y.reshape(-1, 1)) * a2 * (1 - a2)  # Chain rule
            dW2 = a1.T @ dz2 / len(y)
            db2 = np.mean(dz2, axis=0, keepdims=True)
            
            # Hidden layer gradients
            dz1 = (dz2 @ W2.T) * (1 - a1**2)  # Derivative of tanh
            dW1 = X.T @ dz1 / len(y)
            db1 = np.mean(dz1, axis=0, keepdims=True)
            
            # Update weights
            W1 -= learning_rate * dW1
            b1 -= learning_rate * db1
            W2 -= learning_rate * dW2
            b2 -= learning_rate * db2
        
        return {
            'W1': W1, 'b1': b1, 'W2': W2, 'b2': b2,
            'final_loss': loss
        }
    
    def information_theory_metrics(self, y_true, y_pred):
        """Calculate information theory metrics"""
        # Entropy
        def entropy(p):
            p = p[p > 0]  # Remove zeros to avoid log(0)
            return -np.sum(p * np.log2(p))
        
        # Calculate probabilities
        unique_true, counts_true = np.unique(y_true, return_counts=True)
        p_true = counts_true / len(y_true)
        
        unique_pred, counts_pred = np.unique(y_pred, return_counts=True)
        p_pred = counts_pred / len(y_pred)
        
        # Entropy calculations
        h_true = entropy(p_true)
        h_pred = entropy(p_pred)
        
        # Mutual information (simplified)
        # This is a basic implementation - more sophisticated methods exist
        mutual_info = 0
        for i, val_true in enumerate(unique_true):
            for j, val_pred in enumerate(unique_pred):
                joint_prob = np.sum((y_true == val_true) & (y_pred == val_pred)) / len(y_true)
                if joint_prob > 0:
                    mutual_info += joint_prob * np.log2(joint_prob / (p_true[i] * p_pred[j]))
        
        return {
            'entropy_true': h_true,
            'entropy_pred': h_pred,
            'mutual_information': mutual_info
        }
```

## Advanced Mathematical Concepts for AI

### Convex Optimization
```python
from scipy.optimize import minimize
import cvxpy as cp

class ConvexOptimization:
    def __init__(self):
        pass
    
    def support_vector_machine_dual(self, X, y, C=1.0):
        """SVM dual formulation using convex optimization"""
        n_samples = X.shape[0]
        
        # Compute kernel matrix (linear kernel)
        K = X @ X.T
        
        # Define optimization variables
        alpha = cp.Variable(n_samples)
        
        # Objective function: maximize dual objective
        objective = cp.Maximize(cp.sum(alpha) - 0.5 * cp.quad_form(alpha, cp.multiply(np.outer(y, y), K)))
        
        # Constraints
        constraints = [
            alpha >= 0,
            alpha <= C,
            cp.sum(cp.multiply(alpha, y)) == 0
        ]
        
        # Solve optimization problem
        problem = cp.Problem(objective, constraints)
        problem.solve()
        
        return alpha.value
    
    def portfolio_optimization(self, expected_returns, cov_matrix, risk_aversion=1.0):
        """Markowitz portfolio optimization"""
        n_assets = len(expected_returns)
        
        # Define optimization variables
        weights = cp.Variable(n_assets)
        
        # Objective: maximize return - risk penalty
        portfolio_return = expected_returns.T @ weights
        portfolio_risk = cp.quad_form(weights, cov_matrix)
        objective = cp.Maximize(portfolio_return - risk_aversion * portfolio_risk)
        
        # Constraints
        constraints = [
            cp.sum(weights) == 1,  # Weights sum to 1
            weights >= 0  # Long-only portfolio
        ]
        
        # Solve
        problem = cp.Problem(objective, constraints)
        problem.solve()
        
        return {
            'optimal_weights': weights.value,
            'expected_return': portfolio_return.value,
            'portfolio_risk': portfolio_risk.value
        }
```

Mathematics và Machine Learning yêu cầu sự kết hợp giữa theoretical understanding và practical implementation. Key success factors bao gồm:

1. **Solid Mathematical Foundation**: Linear algebra, calculus, probability
2. **Practical Implementation Skills**: NumPy, SciPy, optimization libraries
3. **Understanding of ML Algorithms**: From mathematical perspective
4. **Ability to Debug Mathematical Issues**: Numerical stability, convergence
5. **Continuous Learning**: Stay updated with new mathematical techniques in AI
