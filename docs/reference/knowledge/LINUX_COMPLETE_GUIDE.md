# Linux Complete Guide - From Basics to Advanced

## Tổng quan

Linux là một hệ điều hành mã nguồn mở dựa trên Unix, đư<PERSON><PERSON> tạo ra bởi Linus Torvalds vào năm 1991. Linux kế thừa triết lý UNIX: "Mọi thứ là file; chương trình nhỏ, làm một việc duy nhất và ghép nối qua dòng chảy văn bản".

### Tại sao học Linux?

- **Phổ biến**: Linux chiếm phần lớn trong data center và cloud computing
- **Hiệu quả**: Command line cung cấp sức mạnh và tốc độ cao
- **Bảo mật**: Hệ thống bảo mật mạnh mẽ với SELinux/AppArmor
- **Linh hoạt**: C<PERSON> thể tùy chỉnh theo nhu cầu cụ thể
- **Miễn phí**: Không có chi phí license

### Linux Kernel

Kernel là thành phần trung tâm của hệ điều hành, quản lý:
- **Process scheduler (CFS)**: Completely Fair Scheduler
- **Memory management**: MMU, cgroups
- **VFS**: Virtual File System
- **Device drivers**: Quản lý thiết bị
- **Netfilter**: Lọc gói tin mạng
- **LSM**: Linux Security Modules (SELinux/AppArmor)

## Phần 1: Giới thiệu về Linux

### Linux Distribution (Distro)

Linux distribution là phiên bản hoàn chỉnh của hệ điều hành Linux bao gồm:
- Linux kernel
- System utilities
- Application software
- Package management system

#### Các Distribution phổ biến:

1. **Ubuntu**: Thân thiện với người dùng, dành cho người mới bắt đầu
2. **Linux Mint**: Dựa trên Ubuntu, tập trung vào multimedia
3. **Arch Linux**: Nhẹ, linh hoạt, dành cho người dùng có kinh nghiệm
4. **Manjaro**: Dựa trên Arch, thân thiện với người dùng
5. **Kali Linux**: Tập trung vào cybersecurity và penetration testing

### Cài đặt Linux

#### Phương pháp 1: Cài đặt chính
```bash
# Tải Ubuntu ISO từ ubuntu.com/download/desktop
# Tạo bootable USB với Rufus
# Boot từ USB và làm theo hướng dẫn
```

#### Phương pháp 2: WSL2 trên Windows
```bash
# Bật Windows Subsystem for Linux
# Chạy lệnh cài đặt
wsl --install
# Khởi động lại và thiết lập user
```

#### Phương pháp 3: Virtual Machine
- Oracle VirtualBox
- VMware Workstation Player
- Multipass

#### Phương pháp 4: Browser-based
- Replit (online code editor với Linux terminal)
- JSLinux (web-based Linux terminal)

## Phần 2: Bash Shell và System Commands

### Bash Shell

Bash (Bourne Again Shell) là shell mặc định trên hầu hết Linux distributions:

```bash
# Kiểm tra shell hiện tại
echo $SHELL
# Output: /bin/bash

# Prompt format
[username@hostname ~]$
# Root user: [root@hostname ~]#
```

### Cấu trúc lệnh

```bash
command [options] [arguments]
```

**Ví dụ:**
```bash
ls -la /home/<USER>
# ls: command
# -la: options (flags)
# /home/<USER>
```

### Các ký tự đặc biệt

```bash
# ~ : Home directory
cd ~

# . : Current directory
ls .

# .. : Parent directory
cd ..

# * : Wildcard (match any characters)
ls *.txt

# | : Pipe (output của lệnh trước làm input cho lệnh sau)
ls | grep "pattern"

# > : Redirect output to file
ls > file_list.txt

# >> : Append output to file
echo "new line" >> file_list.txt
```

### Help và Documentation

```bash
# Built-in help
command --help
command -h

# Manual pages
man command
man -k keyword

# Info pages
info command
```

### Keyboard Shortcuts

| Operation | Shortcut |
|-----------|----------|
| Previous command | Up Arrow |
| Beginning of line | Ctrl+A |
| End of line | Ctrl+E |
| Clear to end | Ctrl+K |
| Command completion | Tab |
| Command history | history |

### Lệnh cơ bản

```bash
# Xác định user hiện tại
whoami

# Thông tin hệ thống
uname -a

# Thông tin CPU
lscpu

# Thời gian hoạt động
uptime
```

## Phần 3: Hiểu hệ thống Linux

### File System Hierarchy

```
/
├── bin/          # Essential command binaries
├── boot/         # Boot loader files
├── etc/          # System configuration
├── home/         # User home directories
├── lib/          # Shared libraries
├── mnt/          # Mount points
├── opt/          # Optional software
├── proc/         # Process information
├── root/         # Root user home
├── sbin/         # System binaries
├── tmp/          # Temporary files
├── usr/          # User programs
└── var/          # Variable data
```

### Kiểm tra file system

```bash
# Xem cấu trúc thư mục
tree -d -L 1

# Thông tin chi tiết
ls -la

# Phân biệt file và thư mục
# '-' = file, 'd' = directory
```

## Phần 4: Quản lý file từ Command Line

### Navigation

```bash
# Hiển thị thư mục hiện tại
pwd

# Thay đổi thư mục
cd /path/to/directory
cd ~              # Về home
cd ..             # Lên thư mục cha
cd -              # Về thư mục trước

# Liệt kê nội dung
ls
ls -la            # Tất cả file với chi tiết
ls -lh            # Kích thước dễ đọc
ls -lt            # Sắp xếp theo thời gian
ls -R             # Đệ quy
```

### Quản lý File và Thư mục

```bash
# Tạo thư mục
mkdir directory_name
mkdir -p parent/child    # Tạo thư mục cha nếu chưa có

# Tạo file
touch filename
touch file1.txt file2.txt file3.txt

# Copy file
cp source destination
cp -r source_dir dest_dir    # Copy thư mục
cp -v file1 file2            # Verbose output

# Di chuyển/đổi tên
mv old_name new_name
mv file1.txt backup/

# Xóa file/thư mục
rm filename
rm -r directory              # Xóa thư mục và nội dung
rm -f filename               # Force (không hỏi)
rmdir directory              # Chỉ xóa thư mục rỗng
```

### Tìm kiếm File

```bash
# Tìm theo tên
find /path -name "filename"
find /path -iname "filename"    # Không phân biệt hoa thường

# Tìm theo loại
find /path -type f              # Chỉ file
find /path -type d              # Chỉ thư mục
find /path -type l              # Symbolic links

# Tìm theo kích thước
find /path -size +100M          # Lớn hơn 100MB
find /path -size -1G            # Nhỏ hơn 1GB

# Tìm theo thời gian
find /path -mtime -7            # Sửa đổi trong 7 ngày qua

# Tìm và thực thi lệnh
find /path -name "*.tmp" -exec rm {} \;
```

### Xem nội dung File

```bash
# Hiển thị toàn bộ file
cat filename

# Xem từng trang
less filename
more filename

# Đầu file
head filename
head -n 20 filename

# Cuối file
tail filename
tail -n 50 filename
tail -f filename            # Theo dõi real-time

# Đếm
wc filename                 # Lines, words, characters
wc -l filename             # Chỉ số dòng
```

### So sánh File

```bash
# So sánh 2 file
diff file1 file2
diff -u file1 file2            # Unified format
diff -y file1 file2            # Side by side

# Kiểm tra nhanh
diff -q file1 file2
```

## Phần 5: Text Editing trong Linux

### Vim Editor

Vim là text editor mạnh mẽ với 3 chế độ:

#### Chế độ hoạt động:
1. **Command Mode**: Chế độ mặc định, để điều hướng
2. **Insert Mode**: Chế độ chỉnh sửa
3. **Visual Mode**: Chế độ chọn text

#### Điều hướng với hjkl:
- **h**: Trái
- **j**: Xuống
- **k**: Lên
- **l**: Phải

#### Các lệnh cơ bản:

```bash
# Chuyển chế độ
i               # Insert mode
ESC             # Về command mode
v               # Visual mode
Shift+V         # Visual line mode
Ctrl+V          # Visual block mode

# Điều hướng
gg              # Đầu file
G               # Cuối file
0               # Đầu dòng
$               # Cuối dòng

# Chỉnh sửa
dd              # Xóa dòng
yy              # Copy dòng
p               # Paste
x               # Xóa ký tự

# Tìm kiếm
/pattern        # Tìm về phía trước
?pattern        # Tìm về phía sau
n               # Tìm tiếp
N               # Tìm ngược

# Thay thế
:%s/old/new/g   # Thay thế toàn bộ

# Thoát
:w              # Lưu
:q              # Thoát
:wq             # Lưu và thoát
:q!             # Thoát không lưu
```

### Nano Editor

Nano là text editor thân thiện với người dùng:

```bash
# Mở file
nano filename

# Các phím tắt chính:
Ctrl+O          # Lưu file
Ctrl+X          # Thoát
Ctrl+K          # Cut line
Ctrl+U          # Paste
Ctrl+W          # Tìm kiếm
Alt+A           # Chọn text
```

## Phần 6: Bash Scripting

### Định nghĩa

Bash scripting là việc viết các lệnh shell để tự động hóa tác vụ.

### Ưu điểm

- **Tự động hóa**: Thực hiện tác vụ lặp lại
- **Hiệu quả**: Tiết kiệm thời gian
- **Linh hoạt**: Có thể tùy chỉnh theo nhu cầu
- **Portable**: Chạy trên nhiều hệ thống Linux

### Cấu trúc Script cơ bản

```bash
#!/bin/bash
# Script description
# Author: Your Name
# Date: YYYY-MM-DD

# Strict mode
set -euo pipefail

# Variables
SCRIPT_NAME=$(basename "$0")
VERSION="1.0.0"

# Functions
usage() {
    echo "Usage: $SCRIPT_NAME [OPTIONS]"
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -v, --version  Show version information"
}

# Main logic
main() {
    local arg="$1"
    
    case "$arg" in
        -h|--help)
            usage
            ;;
        -v|--version)
            echo "Version: $VERSION"
            ;;
        *)
            echo "Processing: $arg"
            ;;
    esac
}

# Check if script is sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

### Variables và Parameters

```bash
#!/bin/bash

# Variable assignment
NAME="John"
AGE=25
FILES=("file1.txt" "file2.txt")

# Command substitution
CURRENT_DIR=$(pwd)
DATE=$(date +%Y-%m-%d)
USER_COUNT=$(who | wc -l)

# Parameter expansion
echo "Script name: $0"
echo "First argument: $1"
echo "All arguments: $@"
echo "Argument count: $#"

# Default values
FILE="${1:-default.txt}"
PORT="${2:-8080}"

# String operations
STRING="Hello World"
echo "${STRING:0:5}"           # Hello
echo "${STRING#Hello }"        # World
```

### Control Structures

```bash
#!/bin/bash

# If statements
if [[ -f "$1" ]]; then
    echo "File exists"
elif [[ -d "$1" ]]; then
    echo "Directory exists"
else
    echo "Neither file nor directory"
fi

# Case statements
case "$1" in
    start)
        echo "Starting service"
        ;;
    stop)
        echo "Stopping service"
        ;;
    restart)
        echo "Restarting service"
        ;;
    *)
        echo "Usage: $0 {start|stop|restart}"
        exit 1
        ;;
esac

# Loops
for file in *.txt; do
    echo "Processing: $file"
done

while read -r line; do
    echo "Line: $line"
done < input.txt

until [[ $COUNTER -gt 5 ]]; do
    echo "Counter: $COUNTER"
    ((COUNTER++))
done
```

### Functions

```bash
#!/bin/bash

# Function definition
process_file() {
    local file="$1"
    local output="$2"
    
    if [[ ! -f "$file" ]]; then
        echo "Error: File $file not found" >&2
        return 1
    fi
    
    # Process file
    awk '{print $1}' "$file" > "$output"
    echo "Processed $file -> $output"
}

# Function with return value
get_file_size() {
    local file="$1"
    if [[ -f "$file" ]]; then
        stat -c%s "$file"
    else
        echo "0"
    fi
}

# Usage
main() {
    local input_file="${1:-/dev/stdin}"
    local output_file="${2:-/dev/stdout}"
    
    process_file "$input_file" "$output_file"
    
    local size=$(get_file_size "$output_file")
    echo "Output file size: $size bytes"
}

# Call main function
main "$@"
```

## Phần 7: Quản lý Software Packages

### Package Management

#### Debian/Ubuntu (apt)
```bash
# Cập nhật danh sách package
sudo apt update

# Nâng cấp packages
sudo apt upgrade
sudo apt dist-upgrade          # Distribution upgrade

# Cài đặt packages
sudo apt install package_name
sudo apt install package1 package2

# Gỡ bỏ packages
sudo apt remove package_name
sudo apt purge package_name    # Xóa cả file cấu hình
sudo apt autoremove            # Xóa packages không dùng

# Tìm kiếm packages
apt search keyword
apt show package_name

# Liệt kê packages đã cài
apt list --installed
dpkg -l | grep package_name
```

#### Red Hat/CentOS (dnf/yum)
```bash
# Cập nhật hệ thống
sudo dnf update
sudo yum update                # Hệ thống cũ

# Cài đặt packages
sudo dnf install package_name
sudo yum install package_name

# Gỡ bỏ packages
sudo dnf remove package_name
sudo yum remove package_name

# Tìm kiếm packages
dnf search keyword
yum search keyword

# Liệt kê packages đã cài
dnf list installed
rpm -qa | grep package_name
```

#### Arch Linux (pacman)
```bash
# Cập nhật hệ thống
sudo pacman -Syu

# Cài đặt packages
sudo pacman -S package_name

# Gỡ bỏ packages
sudo pacman -R package_name
sudo pacman -Rs package_name   # Xóa cả dependencies

# Tìm kiếm packages
pacman -Ss keyword

# Liệt kê packages đã cài
pacman -Q
pacman -Q | grep package_name
```

### Cài đặt từ source

```bash
# Tải source code
wget https://example.com/package.tar.gz
tar -xzf package.tar.gz
cd package/

# Compile và cài đặt
./configure
make
sudo make install
```

## Phần 8: Advanced Linux Topics

### User Management

```bash
# Thông tin user
whoami                         # User hiện tại
id                            # User và group IDs
groups                        # Groups của user
passwd                        # Đổi mật khẩu

# Tạo/sửa user
sudo useradd username
sudo usermod -aG group_name username
sudo userdel username
sudo usermod -s /bin/bash username

# Quản lý group
sudo groupadd group_name
sudo groupdel group_name
sudo gpasswd -a username group_name
sudo gpasswd -d username group_name
```

### Permission Management

```bash
# File permissions
ls -la                        # Hiển thị permissions
chmod 644 filename            # Set permissions (octal)
chmod u+rw filename           # Thêm read/write cho user
chmod g-w filename            # Bỏ write cho group
chmod o-r filename            # Bỏ read cho others

# Permission examples
chmod 755 directory           # rwxr-xr-x (thư mục)
chmod 644 file                # rw-r--r-- (file)
chmod 600 file                # rw------- (file riêng tư)
chmod 700 directory           # rwx------ (thư mục riêng tư)

# Ownership
chown user:group filename
chown user filename
chgrp group filename

# Default permissions
umask                          # Hiển thị umask hiện tại
umask 022                     # Set umask
umask 077                     # Umask hạn chế
```

### SSH và Remote Access

```bash
# Kết nối SSH
ssh user@hostname
ssh -p 2222 user@hostname     # Port khác
ssh -i key.pem user@hostname  # Sử dụng private key

# Copy file qua SSH
scp source user@host:destination
scp -r directory user@host:destination

# SFTP
sftp user@host
# sftp commands:
# get filename
# put filename
# ls
# cd directory
# quit
```

### Process Management

```bash
# Liệt kê processes
ps                              # Processes của shell hiện tại
ps aux                          # Tất cả processes với chi tiết
ps auxf                         # Process tree
ps -ef                          # Tất cả processes (BSD style)

# Process tree
pstree
pstree -p                       # Hiển thị PIDs
pstree -u                       # Hiển thị users

# Real-time monitoring
top
htop                           # Interactive top
iotop                          # I/O monitoring

# Kill processes
kill PID                        # Gửi TERM signal
kill -9 PID                    # Gửi KILL signal
killall process_name           # Kill tất cả processes theo tên
pkill process_name             # Kill processes theo tên
```

### System Services

```bash
# Systemd service management
systemctl start service_name
systemctl stop service_name
systemctl restart service_name
systemctl status service_name
systemctl enable service_name
systemctl disable service_name
systemctl list-units --type=service

# Service information
systemctl show service_name
systemctl list-dependencies service_name
```

### Networking

```bash
# Network interfaces
ip addr show                   # Hiển thị tất cả interfaces
ip link show                   # Hiển thị trạng thái interface
ifconfig                       # Legacy interface info
iwconfig                       # Wireless interface info

# Network connections
ss -tuln                       # Hiển thị ports đang listen
netstat -tuln                  # Legacy way
lsof -i                        # Processes sử dụng network
lsof -i :80                    # Processes sử dụng port 80

# Network routing
ip route show                  # Hiển thị routing table
route -n                       # Legacy routing table
traceroute destination         # Trace route
mtr destination                # Continuous traceroute

# Network testing
ping destination               # Test connectivity
ping -c 4 destination          # Gửi 4 packets
telnet host port               # Test TCP connection
nc -zv host port               # Test TCP connection
curl -I url                    # HTTP headers
```

### Log Analysis

```bash
# System logs
tail -f /var/log/syslog        # Follow system log
journalctl -f                  # Follow systemd journal
journalctl -u service_name     # Service-specific logs

# Log monitoring
tail -f /var/log/application.log
grep "ERROR" /var/log/application.log
grep "ERROR" /var/log/application.log | wc -l

# Advanced log parsing
awk '/ERROR/ {print $1, $2, $NF}' /var/log/application.log
grep "ERROR" /var/log/application.log | awk '{print $1, $2}' | sort | uniq -c
```

### Automation với Cron Jobs

```bash
# Edit crontab
crontab -e

# Crontab format
# minute hour day month weekday command
# 0-59   0-23  1-31 1-12  0-6

# Examples
0 2 * * * /usr/bin/backup.sh          # 2:00 AM hàng ngày
0 */6 * * * /usr/bin/check_status.sh  # Mỗi 6 giờ
0 0 * * 0 /usr/bin/weekly_report.sh   # Chủ nhật 00:00
30 1 1 * * /usr/bin/monthly_cleanup.sh # 1:30 AM ngày 1 hàng tháng

# List crontab
crontab -l

# Remove crontab
crontab -r
```

### Troubleshooting

#### Methodology 7 bước:
1. **Xác định vấn đề**: Thu thập symptoms, check logs
2. **Đưa ra giả thuyết**: So sánh baseline
3. **Kiểm tra giả thuyết**: Tái hiện, sử dụng tools
4. **Lập kế hoạch khắc phục**: Backup, maintenance window
5. **Triển khai giải pháp**: Ansible/script automation
6. **Xác nhận & monitoring**: Verify fix works
7. **Ghi nhật ký & học kinh nghiệm**: Document for future

#### Common Issues:

```bash
# Permission denied
ls -la filename                # Check permissions
sudo chmod +x filename         # Make executable
sudo chown user:group filename # Change ownership

# Command not found
which command                  # Find command location
type command                   # Show command type
echo $PATH                     # Check PATH variable

# File not found
find / -name filename 2>/dev/null
locate filename
whereis command

# Disk space issues
df -h                          # Check disk usage
du -sh directory               # Check directory size
sudo apt autoremove            # Remove unused packages
```

#### Debugging Techniques:

```bash
# Verbose output
command -v                     # Verbose mode
command --verbose

# Debug mode
bash -x script.sh              # Execute with debug output
set -x                         # Enable debug mode in script
set +x                         # Disable debug mode

# Error handling
set -e                         # Exit on error
set -u                         # Exit on undefined variable
set -o pipefail                # Exit on pipe failure

# Logging
exec 1> >(tee -a "$logfile")
exec 2> >(tee -a "$logfile" >&2)
```

### Performance Tuning

#### System Performance Tools:

```bash
# CPU monitoring
top -p $(pgrep process_name)
htop
sar -u 1 10

# Memory analysis
free -h
cat /proc/meminfo
vmstat 1

# I/O performance
iostat -x 1
iotop
lsof | wc -l

# Network performance
iftop -i eth0
nethogs
ss -i
```

#### Kernel Parameters Tuning:

```bash
# /etc/sysctl.conf
vm.swappiness = 10
fs.file-max = 2097152
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_congestion_control = bbr

# Apply changes
sysctl -p
```

#### Tuned Profiles:

```bash
# Install tuned
yum install tuned

# List available profiles
tuned-adm list

# Set performance profile
tuned-adm profile latency-performance

# Create custom profile
mkdir /etc/tuned/custom-profile
```

### Security Hardening

#### 20-Step Security Checklist:

1. **System Updates**
```bash
# Enable automatic security updates
apt install unattended-upgrades
dpkg-reconfigure unattended-upgrades
```

2. **Service Management**
```bash
# Disable unnecessary services
systemctl disable --now telnet
systemctl disable --now ftp
systemctl list-unit-files --state=enabled
```

3. **SSH Hardening**
```bash
# /etc/ssh/sshd_config
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
Protocol 2
AllowUsers specific_user
```

4. **Firewall Configuration**
```bash
# UFW (Uncomplicated Firewall)
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

5. **Sudo Configuration**
```bash
# /etc/sudoers
%sudo ALL=(ALL:ALL) ALL
Defaults timestamp_timeout=15
Defaults logfile=/var/log/sudo.log
```

6. **Filesystem Security**
```bash
# /etc/fstab - Add security options
/tmp /tmp tmpfs defaults,noexec,nodev,nosuid 0 0
/var/tmp /var/tmp tmpfs defaults,noexec,nodev,nosuid 0 0
```

7. **SELinux/AppArmor**
```bash
# Enable SELinux
setenforce 1
echo "SELINUX=enforcing" > /etc/selinux/config

# Or AppArmor
systemctl enable apparmor
systemctl start apparmor
```

8. **Audit & Logging**
```bash
# Install and configure auditd
apt install auditd
systemctl enable auditd

# Configure fail2ban
apt install fail2ban
systemctl enable fail2ban
```

## Kết luận

### Core Principles:
- **Simplicity**: Giữ vững triết lý đơn giản - ghép nối
- **Automation**: Tự động hóa mọi thứ lặp lại
- **Security**: Bảo mật và hiệu năng luôn đồng hành
- **Continuous Learning**: Thói quen cập nhật, giám sát và kiểm định
- **Documentation**: Ghi chép và chia sẻ kiến thức

### Daily Practices:
- Luyện tập shell hằng ngày
- Đọc log khi chưa có sự cố
- Monitor system health proactively
- Keep learning new tools and techniques
- Contribute to open source community

### Learning Path:
1. **Beginner**: Basic commands, navigation, file operations
2. **Intermediate**: Text processing, process management, scripting
3. **Advanced**: Advanced scripting, system administration, automation
4. **Expert**: Custom kernel modules, embedded systems, HPC

Linux mastery = **Philosophy + Practice + Automation + Security**

---

**Tài liệu liên quan:**
- [Linux Mastery Philosophy](LINUX_MASTERY_PHILOSOPHY.md)
- [Linux Advanced Resources](LINUX_ADVANCED_RESOURCES.md)
- [Command Line Mastery](advanced/system-admin/linux-cli.md)
- [Database Engineering Mastery](DATABASE_ENGINEERING_MASTERY.md)
- [Network Engineering Mastery](NETWORK_ENGINEERING_MASTERY.md)