# Linux Mastery Philosophy & Practice Guide

## Triết lý & <PERSON>uyên lý Cốt lõi

### UNIX Philosophy - Nền tảng bất biến
- **Mỗi chương trình làm một việc và làm tốt**
- **Kết quả của một chương trình trở thành đầu vào của chương trình khác qua pipe**
- **Tr<PERSON><PERSON> giao diện "cầm tay chỉ việc"; ưu tiên CLI**
- **Mọi cấu hình lưu ở file văn bản để dễ diff, version control**

### 17 Quy tắc Thiết kế Phần mềm kiểu UNIX
Modularity, Clarity, Composition, Separation, Simplicity, Parsimony...

**Checklist khi tự viết script/tool**:
1. <PERSON><PERSON> tách chính sách khỏi cơ chế?
2. <PERSON><PERSON> log đủ nhưng "im lặng" khi thành công?
3. <PERSON><PERSON> dễ ghép pipe?

### Nguyên tắc Bảo mật Bất biến
- **Defense in Depth**: <PERSON><PERSON><PERSON><PERSON> lớp bảo vệ
- **Least Privilege**: Quyền tối thiểu cần thiết
- **Know Your System**: Hiểu rõ hệ thống của bạn
- **Detect & Respond**: Phát hiện và phản ứng nhanh
- **Hardening liên tầng**: Bảo mật từng lớp

## Kiến trúc Hệ thống Linux

### Phần cứng & Firmware
- **BIOS/UEFI**: Secure Boot, IOMMU
- **Security**: Khóa cổng unused, đặt mật khẩu firmware

### Kernel
**Subsystems**:
- **Process scheduler (CFS)**: Completely Fair Scheduler
- **Memory (MMU, cgroups)**: Memory management
- **VFS**: Virtual File System
- **Drivers**: Device drivers
- **Netfilter**: Network packet filtering
- **LSM (SELinux/AppArmor)**: Linux Security Modules

**Dấu hiệu cần rebuild/upgrade kernel**:
- Thiết bị mới không nhận
- CVE nghiêm trọng
- Tính năng eBPF mới

### Thư viện Hệ thống
- **glibc, libstdc++, musl**: System libraries
- **Interface syscalls**: System call interface
- **Dấu hiệu mismatch**: Lỗi "GLIBC_2.x not found"

### Tiện ích & Shell
- **coreutils**: Basic file, shell and text manipulation utilities
- **bash/zsh**: Command shells
- **systemd**: System and service manager
- **package manager**: apt, dnf, pacman
- **Bootstrap**: busybox cho rescue commands

## Bảng Lệnh "7000 giờ" - Essential Commands

> **📚 Xem thêm:** [Command Line Mastery](advanced/system-admin/linux-cli.md) - Hướng dẫn đầy đủ về Command Line

### File/Directory Operations
```bash
ls -la                    # List files with details
cp -r source dest         # Copy recursively
mv old_name new_name      # Move/rename
rm -rf directory          # Remove recursively (dangerous!)
find / -name "pattern"    # Find files by name
locate filename           # Quick file location
```

### Text Processing
```bash
grep -E "pattern" file    # Extended regex search
awk '{print $2}' file     # Print second column
sed -n '5,10p' file       # Print lines 5-10
sort file | uniq -c       # Sort and count unique lines
```

### Process Management
```bash
ps auxf                   # Process tree view
top/htop                  # Real-time process monitor
kill -9 PID              # Force kill process
nice -n 10 command       # Run with lower priority
strace -p PID            # Trace system calls
```

### Network Operations
```bash
ip a                      # Show network interfaces
ss -tunlp                 # Show listening ports
ping -c4 host            # Test connectivity
curl -I url              # HTTP headers
scp user@host:file .     # Secure copy
```

### System Information
```bash
lscpu                     # CPU information
lsblk                     # Block devices
free -h                   # Memory usage
df -Th                    # Disk usage
uptime                    # System uptime
```

### Archive Operations
```bash
tar czvf archive.tar.gz dir/    # Create compressed archive
tar xzvf archive.tar.gz         # Extract archive
gzip -d file.gz                 # Decompress
zip -r archive.zip dir/         # Create zip archive
```

### Permissions & Security
```bash
chmod 640 file            # Set file permissions
chown user:group file     # Change ownership
umask 027                 # Set default permissions
sudo -i                   # Switch to root
```

### Monitoring & Performance
```bash
vmstat 1                  # Virtual memory statistics
iostat -x                 # I/O statistics
sar -n DEV 1             # Network device statistics
dstat -tcm               # Combined system stats
```

## Hardening 20 Bước - Security Checklist

### 1. System Updates
```bash
# Enable automatic security updates
apt install unattended-upgrades
dpkg-reconfigure unattended-upgrades
```

### 2. Service Management
```bash
# Disable unnecessary services
systemctl disable --now telnet
systemctl disable --now ftp
systemctl list-unit-files --state=enabled
```

### 3. SSH Hardening
```bash
# /etc/ssh/sshd_config
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
Protocol 2
AllowUsers specific_user
```

### 4. Firewall Configuration
```bash
# UFW (Uncomplicated Firewall)
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

### 5. Sudo Configuration
```bash
# /etc/sudoers
%sudo ALL=(ALL:ALL) ALL
Defaults timestamp_timeout=15
Defaults logfile=/var/log/sudo.log
```

### 6. Filesystem Security
```bash
# /etc/fstab - Add security options
/tmp /tmp tmpfs defaults,noexec,nodev,nosuid 0 0
/var/tmp /var/tmp tmpfs defaults,noexec,nodev,nosuid 0 0
```

### 7. SELinux/AppArmor
```bash
# Enable SELinux
setenforce 1
echo "SELINUX=enforcing" > /etc/selinux/config

# Or AppArmor
systemctl enable apparmor
systemctl start apparmor
```

### 8. Audit & Logging
```bash
# Install and configure auditd
apt install auditd
systemctl enable auditd

# Configure fail2ban
apt install fail2ban
systemctl enable fail2ban
```

## Troubleshooting Methodology

### Linux+ Methodology - 7 Steps
1. **Xác định vấn đề**: Thu thập symptoms, check logs
   ```bash
   journalctl -xe
   tail -f /var/log/syslog
   dmesg | tail
   ```

2. **Đưa ra giả thuyết**: So sánh baseline
   ```bash
   git diff /etc/config_file
   systemctl status service_name
   ```

3. **Kiểm tra giả thuyết**: Tái hiện, sử dụng tools
   ```bash
   strace -p PID
   lsof -p PID
   ss -tulpn
   ```

4. **Lập kế hoạch khắc phục**: Backup, maintenance window
   ```bash
   cp /etc/important_config /etc/important_config.backup
   ```

5. **Triển khai giải pháp**: Ansible/script automation

6. **Xác nhận & monitoring**: Verify fix works
   ```bash
   systemctl status service
   tail -f /var/log/application.log
   ```

7. **Ghi nhật ký & học kinh nghiệm**: Document for future

## Performance Tuning & Monitoring

### System Performance Tools
```bash
# CPU monitoring
top -p $(pgrep process_name)
htop
sar -u 1 10

# Memory analysis
free -h
cat /proc/meminfo
vmstat 1

# I/O performance
iostat -x 1
iotop
lsof | wc -l

# Network performance
iftop -i eth0
nethogs
ss -i
```

### Kernel Parameters Tuning
```bash
# /etc/sysctl.conf
vm.swappiness = 10
fs.file-max = 2097152
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_congestion_control = bbr

# Apply changes
sysctl -p
```

### Tuned Profiles
```bash
# Install tuned
yum install tuned

# List available profiles
tuned-adm list

# Set performance profile
tuned-adm profile latency-performance

# Create custom profile
mkdir /etc/tuned/custom-profile
```

## Shell Scripting Nâng cao

### Strict Mode & Best Practices
```bash
#!/bin/bash
# Strict mode
set -euo pipefail

# Trap cleanup
cleanup() {
    echo "Cleaning up..."
    rm -f /tmp/temp_file
}
trap cleanup EXIT

# Function example
process_file() {
    local file="$1"
    local output="$2"
    
    if [[ ! -f "$file" ]]; then
        echo "Error: File $file not found" >&2
        return 1
    fi
    
    # Process file
    awk '{print $1}' "$file" > "$output"
}

# Main logic
main() {
    local input_file="${1:-/dev/stdin}"
    local output_file="${2:-/dev/stdout}"
    
    process_file "$input_file" "$output_file"
}

# Call main with all arguments
main "$@"
```

### Advanced Bash Features
```bash
# Arrays
declare -a files=("file1.txt" "file2.txt" "file3.txt")
for file in "${files[@]}"; do
    echo "Processing $file"
done

# Associative arrays
declare -A config
config[host]="localhost"
config[port]="3306"
config[user]="admin"

# Here documents
cat << EOF > config.txt
Host: ${config[host]}
Port: ${config[port]}
User: ${config[user]}
EOF

# Process substitution
diff <(sort file1) <(sort file2)
```

## Container & Virtualization

### Namespace + cgroups Fundamentals
```bash
# Create new namespace
unshare --pid --fork --mount-proc bash

# Check namespaces
lsns
ls -la /proc/self/ns/

# cgroups v2
echo "+cpu +memory" > /sys/fs/cgroup/cgroup.subtree_control
mkdir /sys/fs/cgroup/mygroup
echo "100000" > /sys/fs/cgroup/mygroup/cpu.max
echo "128M" > /sys/fs/cgroup/mygroup/memory.max
```

### Docker Best Practices
```dockerfile
# Multi-stage build
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS production
WORKDIR /app

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy application
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .

# Security: Run as non-root
USER nextjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000
CMD ["npm", "start"]
```

## Advanced Linux Topics - New Knowledge from freeCodeCamp

### Linux Installation Methods

#### Primary OS Installation
```bash
# Download Ubuntu ISO from ubuntu.com/download/desktop
# Create bootable USB with Rufus
# Boot from USB and follow prompts
# Select "Try or install Ubuntu"
# Complete installation and restart
```

#### WSL2 on Windows
```bash
# Enable Windows Subsystem for Linux
# Run installation command
wsl --install
# Restart Windows machine
# Set up username and password
# Launch Ubuntu from Start menu
```

#### Virtual Machine Options
- Oracle VirtualBox
- VMware Workstation Player
- Multipass

#### Browser-based Solutions
- Replit (online code editor with Linux terminal)
- JSLinux (web-based Linux terminal)

### Advanced Text Editing

#### Vim Mastery
```bash
# Three Vim modes
# 1. Command Mode (default) - navigation
# 2. Insert Mode - editing
# 3. Visual Mode - text selection

# Navigation with hjkl
# h: left, j: down, k: up, l: right

# Mode switching
i               # Enter insert mode
ESC             # Return to command mode
v               # Visual mode
Shift+V         # Visual line mode
Ctrl+V          # Visual block mode

# Advanced navigation
gg              # Beginning of file
G               # End of file
0               # Beginning of line
$               # End of line

# Editing commands
dd              # Delete line
yy              # Copy line
p               # Paste
x               # Delete character

# Search and replace
/pattern        # Search forward
?pattern        # Search backward
n               # Next match
N               # Previous match
:%s/old/new/g   # Replace all occurrences

# Multiple windows
:split          # Horizontal split
:vsplit         # Vertical split
Ctrl+w + h/j/k/l # Navigate between splits
```

#### Nano Editor
```bash
# Key bindings
Ctrl+O          # Save file
Ctrl+X          # Exit
Ctrl+K          # Cut line
Ctrl+U          # Paste
Ctrl+W          # Search
Alt+A           # Select text
Alt+\           # Beginning of file
Alt+/           # End of file
Alt+G           # Go to line number
```

### Advanced File Management

#### Find Command Mastery
```bash
# Find by name
find /path -name "filename"
find /path -iname "filename"    # Case insensitive

# Find by type
find /path -type f              # Files only
find /path -type d              # Directories only
find /path -type l              # Symbolic links
find /path -type c              # Character devices
find /path -type b              # Block devices

# Find by size
find /path -size +100M          # Larger than 100MB
find /path -size -1G            # Smaller than 1GB

# Find by modification time
find /path -mtime -7            # Modified in last 7 days
find /path -mtime +30           # Modified more than 30 days ago

# Find and execute
find /path -name "*.tmp" -exec rm {} \;
find /path -name "*.log" -exec cp {} /backup/ \;
```

#### Advanced File Operations
```bash
# File comparison
diff file1 file2                # Show differences
diff -u file1 file2             # Unified format
diff -y file1 file2             # Side by side
diff -q file1 file2             # Quiet mode

# File statistics
wc filename                      # Lines, words, characters
wc -l filename                  # Line count only
wc -w filename                  # Word count only
wc -c filename                  # Character count only

# File viewing
head -n 20 filename             # First 20 lines
tail -n 50 filename             # Last 50 lines
tail -f filename                # Follow file changes
less filename                   # Interactive viewing
more filename                   # Page by page viewing
```

### Advanced Process Management

#### Process Monitoring
```bash
# Real-time monitoring
top                             # Basic process monitor
htop                            # Interactive process monitor
iotop                           # I/O monitoring
atop                            # Advanced system monitor

# Process information
ps auxf                         # Process tree
ps -ef                          # All processes
ps -p PID                       # Specific process
ps -u username                  # User processes

# Process tree
pstree                          # Process hierarchy
pstree -p                       # With PIDs
pstree -u                       # With users
```

#### Process Control
```bash
# Signal management
kill -l                         # List all signals
kill -HUP PID                  # Send HUP signal
kill -TERM PID                 # Send TERM signal
kill -KILL PID                 # Send KILL signal

# Process priority
nice -n 10 command             # Run with lower priority
renice 10 PID                  # Change running process priority

# Background jobs
command &                       # Run in background
bg                             # Resume background job
fg                             # Bring job to foreground
jobs                           # List background jobs
nohup command &                # Run immune to hangups
```

### Advanced Networking

#### Network Diagnostics
```bash
# Interface management
ip addr show                    # Show all interfaces
ip link set eth0 up/down        # Enable/disable interface
ethtool eth0                    # Physical layer info
nmcli device status             # NetworkManager status

# Connectivity testing
ping -c4 *******               # ICMP test
traceroute google.com           # Path tracing
mtr google.com                  # Continuous traceroute
nc -zv host 80                  # Port testing

# Socket analysis
ss -tuln                        # Socket statistics
ss -4 state established         # IPv4 established connections
lsof -i :80                     # Process using port 80
fuser -n tcp 80                 # Which process on port 80
```

#### Network Performance
```bash
# Bandwidth monitoring
iftop -i eth0                   # Real-time interface traffic
nethogs                         # Per-process network usage
vnstat -i eth0                  # Network statistics

# Network configuration
ip route show                   # Routing table
ip rule show                    # Routing rules
arp -a                          # ARP table

# Performance testing
iperf3 -c server_ip -t 30       # Client mode
iperf3 -s                       # Server mode
```

### Advanced Log Analysis

#### Log Processing
```bash
# Real-time log monitoring
tail -f /var/log/syslog         # Follow system log
journalctl -f                    # Follow systemd journal
journalctl -u service_name       # Service-specific logs

# Log filtering
grep "ERROR" /var/log/application.log
grep "ERROR" /var/log/application.log | wc -l
grep "ERROR" /var/log/application.log | tail -20

# Advanced log parsing
awk '/ERROR/ {print $1, $2, $NF}' /var/log/application.log
grep "ERROR" /var/log/application.log | awk '{print $1, $2}' | sort | uniq -c
awk '{print $1}' /var/log/access.log | sort | uniq -c | sort -nr
```

#### Log Rotation
```bash
# Manual log rotation
sudo logrotate -f /etc/logrotate.conf

# Log cleanup
sudo journalctl --vacuum-time=30d
sudo journalctl --vacuum-size=100M
```

### Automation & Cron Jobs

#### Advanced Cron Usage
```bash
# Crontab format
# minute hour day month weekday command
# 0-59   0-23  1-31 1-12  0-6

# Examples
0 2 * * * /usr/bin/backup.sh          # 2:00 AM daily
0 */6 * * * /usr/bin/check_status.sh  # Every 6 hours
0 0 * * 0 /usr/bin/weekly_report.sh   # Sunday 00:00
30 1 1 * * /usr/bin/monthly_cleanup.sh # 1:30 AM 1st of month
0 0 1 1 * /usr/bin/yearly_archive.sh  # New Year 00:00

# Special cron strings
@reboot    /usr/bin/startup_script.sh
@yearly    /usr/bin/yearly_backup.sh
@annually  /usr/bin/yearly_backup.sh
@monthly   /usr/bin/monthly_cleanup.sh
@weekly    /usr/bin/weekly_report.sh
@daily     /usr/bin/daily_backup.sh
@hourly    /usr/bin/hourly_check.sh

# Crontab management
crontab -e                          # Edit crontab
crontab -l                          # List crontab
crontab -r                          # Remove crontab
crontab -u username -e              # Edit user's crontab
```

#### Advanced Automation
```bash
# Systemd timers (alternative to cron)
# Create timer file
sudo nano /etc/systemd/system/backup.timer

[Unit]
Description=Run backup every day at 2 AM
Requires=backup.service

[Timer]
OnCalendar=*-*-* 02:00:00
Persistent=true

[Install]
WantedBy=timers.target

# Enable and start timer
sudo systemctl enable backup.timer
sudo systemctl start backup.timer
sudo systemctl list-timers
```

## Kết luận - Linux Expert Mindset

**Core Principles**:
- **Simplicity**: Giữ vững triết lý đơn giản - ghép nối
- **Automation**: Tự động hóa mọi thứ lặp lại
- **Security**: Bảo mật và hiệu năng luôn đồng hành
- **Continuous Learning**: Thói quen cập nhật, giám sát và kiểm định
- **Documentation**: Ghi chép và chia sẻ kiến thức

**Daily Practices**:
- Luyện tập shell hằng ngày
- Đọc log khi chưa có sự cố
- Monitor system health proactively
- Keep learning new tools and techniques
- Contribute to open source community

**Advanced Skills**:
- Master text editors (Vim/Nano)
- Advanced file operations and search
- Process and network diagnostics
- Log analysis and automation
- Performance tuning and security hardening

Linux mastery = **Philosophy + Practice + Automation + Security + Advanced Tools**

---

**Tài liệu liên quan:**
- [Linux Complete Guide](LINUX_COMPLETE_GUIDE.md) - Hướng dẫn toàn diện từ cơ bản đến nâng cao
- [Linux Advanced Resources](LINUX_ADVANCED_RESOURCES.md) - Tài nguyên nâng cao
- [Command Line Mastery](advanced/system-admin/linux-cli.md) - Thành thạo command line
