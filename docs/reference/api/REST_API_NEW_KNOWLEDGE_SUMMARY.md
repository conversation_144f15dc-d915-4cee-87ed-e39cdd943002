# 🚀 REST API Knowledge Update - FreeCodeCamp.org

## 📚 Source Information
- **Source**: [The REST API Handbook – How to Build, Test, Consume, and Document REST APIs](https://www.freecodecamp.org/news/build-consume-and-document-a-rest-api/)
- **Additional Source**: [REST API Design Best Practices – How to Build a REST API](https://www.freecodecamp.org/news/rest-api-design-best-practices-build-a-rest-api/)
- **Author**: German Cocca
- **Date Retrieved**: August 30, 2024
- **Content Type**: Comprehensive REST API Tutorial & Best Practices Guide

## 🎯 New Knowledge Sections

### 1. **What is REST?**
**Representational State Transfer (REST)** là một architectural style phổ biến để xây dựng web services và APIs.

#### **Main Characteristics:**
- **Stateless**: Mỗi request chứa tất cả thông tin cần thiết để xử lý
- **Resource-based**: Mỗi resource được định danh bởi URI duy nhất
- **Uniform Interface**: Interface chuẩn hóa cho tất cả endpoints
- **Cacheable**: Responses có thể được cache để cải thiện performance
- **Layered System**: Hỗ trợ intermediaries như proxies và gateways

#### **Pros:**
- Dễ học và sử dụng
- Khả năng mở rộng cao
- Linh hoạt cho nhiều loại ứng dụng
- Hỗ trợ rộng rãi từ development tools

#### **Cons:**
- Thiếu tiêu chuẩn nghiêm ngặt
- Chức năng hạn chế cho use cases phức tạp
- Vấn đề bảo mật nếu không implement đúng cách

### 2. **Building REST API with Node.js and Express**

#### **Architecture Pattern:**
```
Application Layer → Routes Layer → Controllers Layer → Model Layer → Persistence Layer
```

#### **Key Components:**

**App.js (Application Layer):**
```javascript
import express from 'express'
import cors from 'cors'
import petRoutes from './pets/routes/pets.routes.js'

const app = express()
const port = 3000

app.use(cors())
app.use(express.json())
app.use('/pets', petRoutes)

if (process.env.NODE_ENV !== 'test') {
    app.listen(port, () => console.log(`⚡️[server]: Server is running at https://localhost:${port}`))
}

export default app
```

**Routes (Routes Layer):**
```javascript
import express from "express";
import { listPets, getPet, editPet, addPet, deletePet } from "../controllers/pets.controllers.js";

const router = express.Router();

router.get("/", listPets);
router.get("/:id", getPet);
router.put("/:id", editPet);
router.post("/", addPet);
router.delete("/:id", deletePet);

export default router;
```

**Controllers (Controllers Layer):**
```javascript
import { getItem, listItems, editItem, addItem, deleteItem } from '../models/pets.models.js'

export const getPet = (req, res) => {
    try {
        const resp = getItem(parseInt(req.params.id))
        res.status(200).json(resp)
    } catch (err) {
        res.status(500).send(err)
    }
}

export const listPets = (req, res) => {
    try {
        const resp = listItems()
        res.status(200).json(resp)
    } catch (err) {
        res.status(500).send(err)
    }
}

export const editPet = (req, res) => {
    try {
        const resp = editItem(parseInt(req.params.id), req.body)
        res.status(200).json(resp)
    } catch (err) {
        res.status(500).send(err)
    }
}

export const addPet = (req, res) => {
    try {
        const resp = addItem(req.body)
        res.status(200).json(resp)
    } catch (err) {
        res.status(500).send(err)
    }
}

export const deletePet = (req, res) => {
    try {
        const resp = deleteItem(parseInt(req.params.id))
        res.status(200).json(resp)
    } catch (err) {
        res.status(500).send(err)
    }
}
```

**Models (Model Layer):**
```javascript
import db from '../../db/db.js'

export const getItem = (id) => {
    try {
        const pet = db?.pets?.filter(pet => pet?.id === id)[0]
        return pet
    } catch (err) {
        console.log('Error', err)
    }
}

export const listItems = () => {
    try {
        return db?.pets
    } catch (err) {
        console.log('Error', err)
    }
}

export const editItem = (id, data) => {
    try {
        const index = db.pets.findIndex(pet => pet.id === id)
        if (index === -1) throw new Error('Pet not found')
        else {
            db.pets[index] = data
            return db.pets[index]
        }        
    } catch (err) {
        console.log('Error', err)
    }
}

export const addItem = (data) => {
    try {  
        const newPet = { id: db.pets.length + 1, ...data }
        db.pets.push(newPet)
        return newPet
    } catch (err) {
        console.log('Error', err)
    }
}

export const deleteItem = (id) => {
    try {
        const index = db.pets.findIndex(pet => pet.id === id)
        if (index === -1) throw new Error('Pet not found')
        else {
            db.pets.splice(index, 1)
            return db.pets
        }
    } catch (error) {
        // Handle error
    }
}
```

**Database (Persistence Layer):**
```javascript
const db = {
    pets: [
        {
            id: 1,
            name: 'Rex',
            type: 'dog',
            age: 3,
            breed: 'labrador',
        },
        {
            id: 2,
            name: 'Fido',
            type: 'dog',
            age: 1,
            breed: 'poodle',
        },
        {
            id: 3,
            name: 'Mittens',
            type: 'cat',
            age: 2,
            breed: 'tabby',
        },
    ]
}

export default db
```

### 3. **Testing REST API with Supertest**

#### **Setup Dependencies:**
```json
{
  "devDependencies": {
    "@babel/core": "^7.21.4",
    "@babel/preset-env": "^7.21.4",
    "babel-jest": "^29.5.0",
    "jest": "^29.5.0",
    "jest-babel": "^1.0.1",
    "nodemon": "^2.0.22",
    "supertest": "^6.3.3"
  },
  "scripts": {
    "test": "jest"
  }
}
```

#### **Babel Configuration:**
```javascript
// babel.config.cjs
module.exports = {
    presets: [
      [
        '@babel/preset-env',
        {
          targets: {
            node: 'current',
          },
        },
      ],
    ],
  };
```

#### **Test Implementation:**
```javascript
import supertest from 'supertest'
import server from '../../app'
const requestWithSupertest = supertest(server)

describe('GET "/"', () => {
    test('GET "/" returns all pets', async () => {
        const res = await requestWithSupertest.get('/pets')
        expect(res.status).toEqual(200)
        expect(res.type).toEqual(expect.stringContaining('json'))
        expect(res.body).toEqual([
            {
                id: 1,
                name: 'Rex',
                type: 'dog',
                age: 3,
                breed: 'labrador',
            },
            // ... more pets
        ])
    })
})

describe('GET "/:id"', () => {
    test('GET "/:id" returns given pet', async () => {
        const res = await requestWithSupertest.get('/pets/1')
        expect(res.status).toEqual(200)
        expect(res.type).toEqual(expect.stringContaining('json'))
        expect(res.body).toEqual({
            id: 1,
            name: 'Rex',
            type: 'dog',
            age: 3,
            breed: 'labrador',
        })
    })
})

describe('PUT "/:id"', () => {
    test('PUT "/:id" updates pet and returns it', async () => {
        const res = await requestWithSupertest.put('/pets/1').send({
            id: 1,
            name: 'Rexo',
            type: 'dogo',
            age: 4,
            breed: 'doberman'
        })
        expect(res.status).toEqual(200)
        expect(res.type).toEqual(expect.stringContaining('json'))
        expect(res.body).toEqual({
            id: 1,
            name: 'Rexo',
            type: 'dogo',
            age: 4,
            breed: 'doberman'
        })
    })
})

describe('POST "/"', () => {
    test('POST "/" adds new pet and returns the added item', async () => {
        const res = await requestWithSupertest.post('/pets').send({
            name: 'Salame',
            type: 'cat',
            age: 6,
            breed: 'pinky'
        })
        expect(res.status).toEqual(200)
        expect(res.type).toEqual(expect.stringContaining('json'))
        expect(res.body).toEqual({
            id: 4,
            name: 'Salame',
            type: 'cat',
            age: 6,
            breed: 'pinky'
        })
    })
})

describe('DELETE "/:id"', () => {
    test('DELETE "/:id" deletes given pet and returns updated list', async () => {
        const res = await requestWithSupertest.delete('/pets/2')
        expect(res.status).toEqual(200)
        expect(res.type).toEqual(expect.stringContaining('json'))
        // Verify updated list
    })
})
```

### 4. **Consuming REST API with React Frontend**

#### **App.jsx (Main App):**
```javascript
import { Suspense, lazy, useState } from 'react'
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom'
import './App.css'

const PetList = lazy(() => import('./pages/PetList'))
const PetDetail = lazy(() => import('./pages/PetDetail'))
const EditPet = lazy(() => import('./pages/EditPet'))
const AddPet = lazy(() => import('./pages/AddPet'))

function App() {
  const [petToEdit, setPetToEdit] = useState(null)

  return (
    <div className="App">
      <Router>
        <h1>Pet shelter</h1>
        <Link to='/add'>
          <button>Add new pet</button>
        </Link>

        <Routes>
          <Route path='/' element={<Suspense fallback={<></>}><PetList /></Suspense>}/>
          <Route path='/:petId' element={<Suspense fallback={<></>}><PetDetail setPetToEdit={setPetToEdit} /></Suspense>}/>
          <Route path='/:petId/edit' element={<Suspense fallback={<></>}><EditPet petToEdit={petToEdit} /></Suspense>}/>
          <Route path='/add' element={<Suspense fallback={<></>}><AddPet /></Suspense>}/>
        </Routes>
      </Router>
    </div>
  )
}

export default App
```

#### **PetList.jsx (List Component):**
```javascript
import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import axios from 'axios'

function PetList() {
    const [pets, setPets] = useState([])

    const getPets = async () => {
        try {
            /* FETCH */
            // const response = await fetch('http://localhost:3000/pets')
            // const data = await response.json()
            // if (response.status === 200) setPets(data)

            /* AXIOS */
            const response = await axios.get('http://localhost:3000/pets')
            if (response.status === 200) setPets(response.data)

        } catch (error) {
            console.error('error', error)
        }
    }

    useEffect(() => { getPets() }, [])

    return (
        <>
            <h2>Pet List</h2>
            {pets?.map((pet) => {
                return (
                    <div key={pet?.id}>
                        <p>{pet?.name} - {pet?.type} - {pet?.breed}</p>
                        <Link to={`/${pet?.id}`}>
                            <button>Pet detail</button>
                        </Link>
                    </div>
                )
            })}
        </>
    )
}

export default PetList
```

#### **PetDetail.jsx (Detail Component):**
```javascript
import { useEffect, useState } from 'react'
import { useParams, Link } from 'react-router-dom'
import axios from 'axios'

function PetDetail({ setPetToEdit }) {
    const [pet, setPet] = useState([])
    const { petId } = useParams()

    const getPet = async () => {
        try {
            const response = await axios.get(`http://localhost:3000/pets/${petId}`)
            if (response.status === 200) {
                setPet(response.data)
                setPetToEdit(response.data)
            }
        } catch (error) {
            console.error('error', error)
        }
    }

    useEffect(() => { getPet() }, [])

    const deletePet = async () => {
        try {
            const response = await axios.delete(`http://localhost:3000/pets/${petId}`)
            if (response.status === 200) window.location.href = '/'
        } catch (error) {
            console.error('error', error)
        }
    }

    return (
        <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
            <h2>Pet Detail</h2>
            {pet && (
                <>
                    <p>Pet name: {pet.name}</p>
                    <p>Pet type: {pet.type}</p>
                    <p>Pet age: {pet.age}</p>
                    <p>Pet breed: {pet.breed}</p>

                    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        <Link to={`/${pet?.id}/edit`}>
                            <button style={{ marginRight: 10 }}>Edit pet</button>
                        </Link>
                        <button
                            style={{ marginLeft: 10 }}
                            onClick={() => deletePet()}
                        >
                            Delete pet
                        </button>
                    </div>
                </>
            )}
        </div>
    )
}

export default PetDetail
```

#### **AddPet.jsx (Add Component):**
```javascript
import React, { useState } from 'react'
import axios from 'axios'

function AddPet() {
    const [petName, setPetName] = useState()
    const [petType, setPetType] = useState()
    const [petAge, setPetAge] = useState()
    const [petBreed, setPetBreed] = useState()

    const addPet = async () => {
        try {
            const petData = {
                name: petName,
                type: petType,
                age: petAge,
                breed: petBreed
            }

            const response = await axios.post(
                'http://localhost:3000/pets/',
                petData,
                { headers: { 'Content-Type': 'application/json' } }
            )

            if (response.status === 200) window.location.href = `/${response.data.id}`

        } catch (error) {
            console.error('error', error)
        }
    }

    return (
        <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
            <h2>Add Pet</h2>

            <div style={{ display: 'flex', flexDirection: 'column', margin: 20 }}>
                <label>Pet name</label>
                <input type='text' value={petName} onChange={e => setPetName(e.target.value)} />
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', margin: 20 }}>
                <label>Pet type</label>
                <input type='text' value={petType} onChange={e => setPetType(e.target.value)} />
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', margin: 20 }}>
                <label>Pet age</label>
                <input type='text' value={petAge} onChange={e => setPetAge(e.target.value)} />
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', margin: 20 }}>
                <label>Pet breed</label>
                <input type='text' value={petBreed} onChange={e => setPetBreed(e.target.value)} />
            </div>

            <button
                style={{ marginTop: 30 }}
                onClick={() => addPet()}
            >
                Add pet
            </button>
        </div>
    )
}

export default AddPet
```

#### **EditPet.jsx (Edit Component):**
```javascript
import React, { useState } from 'react'
import axios from 'axios'

function EditPet({ petToEdit }) {
    const [petName, setPetName] = useState(petToEdit?.name)
    const [petType, setPetType] = useState(petToEdit?.type)
    const [petAge, setPetAge] = useState(petToEdit?.age)
    const [petBreed, setPetBreed] = useState(petToEdit?.breed)

    const editPet = async () => {
        try {
            const petData = {
                id: petToEdit.id,
                name: petName,
                type: petType,
                age: petAge,
                breed: petBreed
            }

            const response = await axios.put(
                `http://localhost:3000/pets/${petToEdit.id}`,
                petData,
                { headers: { 'Content-Type': 'application/json' } }
            )

            if (response.status === 200) {
                window.location.href = `/${petToEdit.id}`
            }
        } catch (error) {
            console.error('error', error)
        }
    }

    return (
        <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
            <h2>Edit Pet</h2>

            <div style={{ display: 'flex', flexDirection: 'column', margin: 20 }}>
                <label>Pet name</label>
                <input type='text' value={petName} onChange={e => setPetName(e.target.value)} />
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', margin: 20 }}>
                <label>Pet type</label>
                <input type='text' value={petType} onChange={e => setPetType(e.target.value)} />
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', margin: 20 }}>
                <label>Pet age</label>
                <input type='text' value={petAge} onChange={e => setPetAge(e.target.value)} />
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', margin: 20 }}>
                <label>Pet breed</label>
                <input type='text' value={petBreed} onChange={e => setPetBreed(e.target.value)} />
            </div>

            <button
                style={{ marginTop: 30 }}
                onClick={() => editPet()}
            >
                Save changes
            </button>
        </div>
    )
}

export default EditPet
```

### 5. **Documenting REST API with Swagger**

#### **Setup Dependencies:**
```bash
npm i swagger-jsdoc swagger-ui-express
```

#### **App.js Configuration:**
```javascript
import express from 'express'
import cors from 'cors'
import swaggerUI from 'swagger-ui-express'
import swaggerJSdoc from 'swagger-jsdoc'
import petRoutes from './pets/routes/pets.routes.js'

const app = express()
const port = 3000

// swagger definition
const swaggerSpec = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'Pets API',
            version: '1.0.0',
        },
        servers: [
            {
                url: `http://localhost:${port}`,
            }
        ]
    },
    apis: ['./pets/routes/*.js'],
}

/* Global middlewares */
app.use(cors())
app.use(express.json())
app.use(
    '/api-docs',
    swaggerUI.serve,
    swaggerUI.setup(swaggerJSdoc(swaggerSpec))
)

/* Routes */
app.use('/pets', petRoutes)

/* Server setup */
if (process.env.NODE_ENV !== 'test') {
    app.listen(port, () => console.log(`⚡️[server]: Server is running at https://localhost:${port}`))
}

export default app
```

#### **Swagger Documentation in Routes:**
```javascript
import express from "express";
import { listPets, getPet, editPet, addPet, deletePet } from "../controllers/pets.controllers.js";

const router = express.Router();

/**
 * @swagger
 * components:
 *  schemas:
 *     Pet:
 *      type: object
 *      properties:
 *          id:
 *              type: integer
 *              description: Pet id
 *          name:
 *              type: string
 *              description: Pet name
 *          age:
 *              type: integer
 *              description: Pet age
 *          type:
 *              type: string
 *              description: Pet type
 *          breed:
 *              type: string
 *              description: Pet breed
 *     example:
 *          id: 1
 *          name: Rexaurus
 *          age: 3
 *          breed: labrador
 *          type: dog
 */

/**
 * @swagger
 * /pets:
 *  get:
 *     summary: Get all pets
 *     description: Get all pets
 *     responses:
 *      200:
 *         description: Success
 *      500:
 *         description: Internal Server Error
 */
router.get("/", listPets);

/**
 * @swagger
 * /pets/{id}:
 *  get:
 *     summary: Get pet detail
 *     description: Get pet detail
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Pet id
 *     responses:
 *      200:
 *         description: Success
 *      500:
 *         description: Internal Server Error
 */
router.get("/:id", getPet);

/**
 * @swagger
 * /pets/{id}:
 *  put:
 *     summary: Edit pet
 *     description: Edit pet
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Pet id
 *     requestBody:
 *       description: A JSON object containing pet information
 *       content:
 *         application/json:
 *           schema:
 *              $ref: '#/components/schemas/Pet'
 *           example:
 *              name: Rexaurus
 *              age: 12
 *              breed: labrador
 *              type: dog
 *     responses:
 *     200:
 *        description: Success
 *     500:
 *       description: Internal Server Error
 */
router.put("/:id", editPet);

/**
 * @swagger
 * /pets:
 *  post:
 *      summary: Add pet
 *      description: Add pet
 *      requestBody:
 *          description: A JSON object containing pet information
 *          content:
 *             application/json:
 *                 schema:
 *                    $ref: '#/components/schemas/Pet'
 *                 example:
 *                    name: Rexaurus
 *                    age: 12
 *                    breed: labrador
 *                    type: dog
 *      responses:
 *      200:
 *          description: Success
 *      500:
 *          description: Internal Server Error
 */
router.post("/", addPet);

/**
 * @swagger
 * /pets/{id}:
 *  delete:
 *     summary: Delete pet
 *     description: Delete pet
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Pet id
 *     responses:
 *     200:
 *        description: Success
 *     500:
 *       description: Internal Server Error
 */
router.delete("/:id", deletePet);

export default router;
```

## 🔧 Implementation Tools & Technologies

### **Backend:**
- **Node.js**: JavaScript runtime environment
- **Express.js**: Web application framework
- **CORS**: Cross-origin resource sharing middleware
- **Swagger**: API documentation tools

### **Testing:**
- **Jest**: JavaScript testing framework
- **Supertest**: HTTP testing library
- **Babel**: JavaScript compiler for testing

### **Frontend:**
- **React**: JavaScript library for building user interfaces
- **React Router**: Routing library for React
- **Axios**: HTTP client library
- **Fetch API**: Native browser API for HTTP requests

### **Architecture:**
- **Layered Architecture**: Separation of concerns
- **RESTful Design**: Resource-based API design
- **CRUD Operations**: Create, Read, Update, Delete
- **MVC Pattern**: Model-View-Controller separation

## 📋 Key Learning Points

1. **REST Principles**: Understanding stateless, resource-based, uniform interface design
2. **Architecture Patterns**: Implementing layered architecture for scalability
3. **Testing Strategy**: Comprehensive API testing with Supertest and Jest
4. **Frontend Integration**: Consuming APIs with React and modern HTTP clients
5. **API Documentation**: Professional documentation with Swagger/OpenAPI
6. **Error Handling**: Proper error handling and status code management
7. **Data Validation**: Input validation and data integrity
8. **Performance**: Stateless design for better scalability

## 🚀 Best Practices Highlighted

- **Separation of Concerns**: Clear layer separation in architecture
- **Error Handling**: Consistent error responses and status codes
- **Testing**: Comprehensive test coverage for all endpoints
- **Documentation**: Self-documenting code with Swagger annotations
- **Code Organization**: Logical file structure and naming conventions
- **HTTP Standards**: Proper use of HTTP methods and status codes
- **Security**: CORS configuration and input validation
- **Performance**: Stateless design and efficient data handling

## 🎯 REST API Design Best Practices

### **6. **REST API Design Principles & Best Practices**

#### **Core REST Principles:**
- **Stateless**: Mỗi request chứa tất cả thông tin cần thiết
- **Resource-based**: Sử dụng nouns thay vì verbs trong URLs
- **Uniform Interface**: Sử dụng HTTP methods chuẩn (GET, POST, PUT, DELETE)
- **Cacheable**: Responses có thể được cache
- **Layered System**: Hỗ trợ intermediaries

#### **URL Design Best Practices:**
```
✅ Good URLs:
GET /users                    # Get all users
GET /users/123               # Get specific user
POST /users                  # Create new user
PUT /users/123               # Update user
DELETE /users/123            # Delete user

❌ Bad URLs:
GET /getUsers                # Verb in URL
POST /createUser             # Verb in URL
GET /user?id=123            # Query parameter for ID
```

#### **HTTP Methods Usage:**
- **GET**: Retrieve data (read-only)
- **POST**: Create new resource
- **PUT**: Update entire resource
- **PATCH**: Partial update
- **DELETE**: Remove resource

#### **Status Code Standards:**
```javascript
// Success Responses
200 OK                    // Request successful
201 Created              // Resource created
204 No Content           // Success but no content

// Client Error Responses
400 Bad Request          // Invalid request
401 Unauthorized         // Authentication required
403 Forbidden            // Access denied
404 Not Found            // Resource not found
422 Unprocessable Entity // Validation failed

// Server Error Responses
500 Internal Server Error // Server error
502 Bad Gateway          // Gateway error
503 Service Unavailable   // Service temporarily unavailable
```

#### **Response Format Standards:**
```javascript
// Success Response
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "message": "User retrieved successfully"
}

// Error Response
{
  "status": "error",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Email is required",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  }
}
```

#### **Pagination Best Practices:**
```javascript
// Pagination Response
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}

// Pagination Headers
X-Total-Count: 100
X-Page: 1
X-Limit: 10
```

#### **Filtering & Sorting:**
```javascript
// Query Parameters
GET /users?status=active&role=admin&sort=name&order=asc

// Response with metadata
{
  "data": [...],
  "filters": {
    "status": "active",
    "role": "admin"
  },
  "sorting": {
    "field": "name",
    "order": "asc"
  }
}
```

#### **Versioning Strategies:**
```javascript
// URL Versioning
GET /api/v1/users
GET /api/v2/users

// Header Versioning
Accept: application/vnd.company.app-v1+json

// Query Parameter Versioning
GET /api/users?version=1
```

#### **Security Best Practices:**
- **HTTPS**: Luôn sử dụng HTTPS trong production
- **Authentication**: JWT, OAuth 2.0, API Keys
- **Rate Limiting**: Giới hạn số request per minute/hour
- **Input Validation**: Validate tất cả input data
- **CORS**: Cấu hình CORS đúng cách
- **SQL Injection**: Sử dụng parameterized queries

#### **Performance Optimization:**
- **Caching**: HTTP caching headers, Redis caching
- **Compression**: Gzip/Brotli compression
- **Database Optimization**: Indexing, query optimization
- **CDN**: Sử dụng CDN cho static content
- **Load Balancing**: Distribute traffic across servers

## 🔗 Integration with Existing Knowledge

This new knowledge complements and extends the existing REST API knowledge in the workspace by providing:

1. **Practical Implementation Examples**: Complete working code examples
2. **Testing Strategies**: Comprehensive testing approaches
3. **Frontend Integration**: Real-world frontend consumption patterns
4. **Documentation Standards**: Professional API documentation practices
5. **Architecture Patterns**: Proven architectural approaches
6. **Tool Integration**: Modern development toolchains
7. **Best Practices**: Industry-standard implementation patterns
8. **Design Principles**: REST API design best practices
9. **Performance Guidelines**: Optimization strategies
10. **Security Standards**: Security best practices

## 📚 Next Steps for Integration

1. **Update Existing Documentation**: Integrate new knowledge into current files
2. **Enhance Code Examples**: Add practical examples to existing sections
3. **Expand Testing Coverage**: Implement testing strategies across existing APIs
4. **Improve Documentation**: Apply Swagger documentation patterns
5. **Architecture Refinement**: Apply layered architecture patterns
6. **Frontend Integration**: Add frontend consumption examples
7. **Best Practices**: Update existing guidelines with new insights
8. **Design Standards**: Implement REST API design best practices
9. **Performance Optimization**: Apply performance best practices
10. **Security Enhancement**: Implement security best practices