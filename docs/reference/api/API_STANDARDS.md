# 📋 **API STANDARDS GUIDE**

> **Complete guide to REST API and GraphQL design standards and best practices**

[![API Standards](https://img.shields.io/badge/API-Standards%20Compliant-blue)](API_STANDARDS.md)
[![REST](https://img.shields.io/badge/REST-Best%20Practices-green)](API_STANDARDS.md)
[![GraphQL](https://img.shields.io/badge/GraphQL-Federation-purple)](API_STANDARDS.md)

## 🎯 **OVERVIEW**

This guide establishes comprehensive standards for designing, implementing, and documenting APIs across our enterprise platform. It covers REST API design principles, GraphQL patterns, and API governance.

## 📋 **TABLE OF CONTENTS**

1. [REST API Design Principles](#rest-api-design-principles)
2. [URL Design Standards](#url-design-standards)
3. [HTTP Methods & Status Codes](#http-methods--status-codes)
4. [Request & Response Formats](#request--response-formats)
5. [GraphQL Standards](#graphql-standards)
6. [Authentication & Security](#authentication--security)
7. [Error Handling](#error-handling)
8. [API Documentation](#api-documentation)
9. [Versioning Strategy](#versioning-strategy)
10. [Performance & Optimization](#performance--optimization)

---

## **1. REST API DESIGN PRINCIPLES**

### **🎯 Core REST Principles**

```typescript
// REST API Characteristics
interface RESTPrinciples {
  stateless: "Each request contains all information needed to process it";
  resourceBased: "URLs represent resources, not actions";
  uniformInterface: "Standard HTTP methods for all operations";
  cacheable: "Responses can be cached for performance";
  layeredSystem: "Architecture can include intermediaries";
  clientServer: "Separation between client and server concerns";
}

// REST API Benefits
const RESTBenefits = {
  scalability: "Stateless design enables horizontal scaling",
  simplicity: "Easy to understand and implement",
  flexibility: "Supports multiple data formats",
  caching: "Built-in HTTP caching mechanisms",
  tooling: "Extensive tooling and library support",
};
```

### **🏗️ Layered Architecture Pattern**

```
┌─────────────────────────────────────────┐
│             🌐 Client Layer             │
│        (Web, Mobile, API Consumers)     │
├─────────────────────────────────────────┤
│             🔀 API Gateway              │
│     (Authentication, Rate Limiting)     │
├─────────────────────────────────────────┤
│             🎯 Route Layer              │
│         (Endpoint Definitions)          │
├─────────────────────────────────────────┤
│           🎮 Controller Layer           │
│       (Request/Response Handling)       │
├─────────────────────────────────────────┤
│           🏢 Service Layer              │
│         (Business Logic)                │
├─────────────────────────────────────────┤
│            📊 Model Layer               │
│        (Data Access & ORM)              │
├─────────────────────────────────────────┤
│           💾 Database Layer             │
│      (PostgreSQL, MongoDB, Redis)       │
└─────────────────────────────────────────┘
```

---

## **2. URL DESIGN STANDARDS**

### **✅ URL Design Best Practices**

```typescript
// URL Design Rules
const URLDesignStandards = {
  // ✅ Good URL Examples
  good: {
    collections: [
      "GET /api/v1/users",           // Get all users
      "GET /api/v1/users?page=1",   // Paginated users
      "POST /api/v1/users",         // Create new user
    ],
    resources: [
      "GET /api/v1/users/123",      // Get specific user
      "PUT /api/v1/users/123",      // Update user
      "DELETE /api/v1/users/123",   // Delete user
    ],
    nested: [
      "GET /api/v1/users/123/tasks",        // User's tasks
      "POST /api/v1/users/123/tasks",       // Create user's task
      "GET /api/v1/users/123/tasks/456",    // Specific user task
    ],
    filtering: [
      "GET /api/v1/users?status=active",    // Filter by status
      "GET /api/v1/users?role=admin&limit=10", // Multiple filters
      "GET /api/v1/tasks?assigned_to=123",  // Filter by assignment
    ],
  },

  // ❌ Bad URL Examples
  bad: [
    "GET /api/v1/getUsers",        // Verb in URL
    "POST /api/v1/createUser",     // Action in URL
    "GET /api/v1/user?id=123",     // Query param for ID
    "DELETE /api/v1/deleteUser/123", // Redundant verb
  ],
};
```

### **🎯 URL Structure Template**

```
https://api.enterprise.com/api/v1/{resource}/{id}/{sub-resource}/{sub-id}

Examples:
- GET    /api/v1/users                    # List users
- POST   /api/v1/users                    # Create user
- GET    /api/v1/users/123                # Get user
- PUT    /api/v1/users/123                # Update user
- DELETE /api/v1/users/123                # Delete user
- GET    /api/v1/users/123/tasks          # User's tasks
- POST   /api/v1/users/123/tasks          # Create task for user
```

---

## **3. HTTP METHODS & STATUS CODES**

### **🔧 HTTP Methods Usage**

```typescript
// HTTP Methods Standards
interface HTTPMethods {
  GET: {
    purpose: "Retrieve data without side effects";
    idempotent: true;
    cacheable: true;
    examples: ["GET /users", "GET /users/123"];
  };

  POST: {
    purpose: "Create new resources";
    idempotent: false;
    cacheable: false;
    examples: ["POST /users", "POST /auth/login"];
  };

  PUT: {
    purpose: "Update/replace entire resource";
    idempotent: true;
    cacheable: false;
    examples: ["PUT /users/123"];
  };

  PATCH: {
    purpose: "Partial update of resource";
    idempotent: true;
    cacheable: false;
    examples: ["PATCH /users/123"];
  };

  DELETE: {
    purpose: "Remove resources";
    idempotent: true;
    cacheable: false;
    examples: ["DELETE /users/123"];
  };
}
```

### **📊 HTTP Status Codes Standards**

```typescript
// Standard HTTP Status Codes
const StatusCodes = {
  // 2xx Success
  success: {
    200: "OK - Request successful",
    201: "Created - Resource created successfully",
    202: "Accepted - Request accepted for processing",
    204: "No Content - Successful with no response body",
  },

  // 3xx Redirection  
  redirection: {
    301: "Moved Permanently - Resource moved to new URL",
    302: "Found - Temporary redirect",
    304: "Not Modified - Resource hasn't changed",
  },

  // 4xx Client Errors
  clientErrors: {
    400: "Bad Request - Invalid request syntax",
    401: "Unauthorized - Authentication required",
    403: "Forbidden - Access denied",
    404: "Not Found - Resource doesn't exist",
    405: "Method Not Allowed - HTTP method not supported",
    409: "Conflict - Resource conflict",
    422: "Unprocessable Entity - Validation errors",
    429: "Too Many Requests - Rate limit exceeded",
  },

  // 5xx Server Errors
  serverErrors: {
    500: "Internal Server Error - Server error",
    501: "Not Implemented - Method not implemented",
    502: "Bad Gateway - Invalid response from upstream",
    503: "Service Unavailable - Server temporarily unavailable",
    504: "Gateway Timeout - Upstream server timeout",
  },
};
```

---

## **4. REQUEST & RESPONSE FORMATS**

### **📤 Request Format Standards**

```typescript
// Request Format Examples
interface RequestFormats {
  // POST/PUT Request Body
  createUser: {
    method: "POST";
    url: "/api/v1/users";
    headers: {
      "Content-Type": "application/json";
      "Authorization": "Bearer <token>";
    };
    body: {
      email: "<EMAIL>";
      name: "John Doe";
      role: "user";
    };
  };

  // Query Parameters
  listUsers: {
    method: "GET";
    url: "/api/v1/users?page=1&limit=20&status=active&sort=created_at:desc";
    parameters: {
      page: "Page number (default: 1)";
      limit: "Items per page (default: 20, max: 100)";
      status: "Filter by status";
      sort: "Sort field and direction";
    };
  };
}
```

### **📥 Response Format Standards**

```typescript
// Standard Response Structure
interface APIResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: ValidationError[];
  meta?: ResponseMeta;
}

interface ResponseMeta {
  page?: number;
  limit?: number;
  total?: number;
  totalPages?: number;
  hasNext?: boolean;
  hasPrev?: boolean;
}

// Success Response Examples
const SuccessResponses = {
  // Single Resource
  user: {
    success: true,
    data: {
      id: "123",
      email: "<EMAIL>",
      name: "John Doe",
      role: "user",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    },
    message: "User retrieved successfully",
  },

  // Collection with Pagination
  users: {
    success: true,
    data: [
      { id: "123", email: "<EMAIL>", name: "John Doe" },
      { id: "124", email: "<EMAIL>", name: "Jane Smith" },
    ],
    meta: {
      page: 1,
      limit: 20,
      total: 150,
      totalPages: 8,
      hasNext: true,
      hasPrev: false,
    },
    message: "Users retrieved successfully",
  },

  // Creation Response
  created: {
    success: true,
    data: {
      id: "125",
      email: "<EMAIL>",
      name: "New User",
    },
    message: "User created successfully",
  },
};
```

### **❌ Error Response Standards**

```typescript
// Error Response Structure
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  errors?: ValidationError[];
}

// Error Response Examples
const ErrorResponses = {
  // Validation Error (422)
  validation: {
    success: false,
    error: {
      code: "VALIDATION_ERROR",
      message: "The request contains invalid data",
    },
    errors: [
      {
        field: "email",
        code: "INVALID_FORMAT",
        message: "Email format is invalid",
      },
      {
        field: "password",
        code: "TOO_SHORT",
        message: "Password must be at least 8 characters",
      },
    ],
  },

  // Not Found Error (404)
  notFound: {
    success: false,
    error: {
      code: "RESOURCE_NOT_FOUND",
      message: "The requested user does not exist",
    },
  },

  // Unauthorized Error (401)
  unauthorized: {
    success: false,
    error: {
      code: "UNAUTHORIZED",
      message: "Authentication token is invalid or expired",
    },
  },

  // Server Error (500)
  serverError: {
    success: false,
    error: {
      code: "INTERNAL_SERVER_ERROR",
      message: "An unexpected error occurred",
      details: "Contact support if the problem persists",
    },
  },
};
```

---

## **5. GRAPHQL STANDARDS**

### **🎯 GraphQL Schema Design**

```graphql
# Type Definitions
type User {
  id: ID!
  email: String!
  name: String!
  role: Role!
  tasks: [Task!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Task {
  id: ID!
  title: String!
  description: String
  status: TaskStatus!
  assignee: User
  createdAt: DateTime!
  updatedAt: DateTime!
}

enum Role {
  ADMIN
  USER
  MODERATOR
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

# Input Types
input CreateUserInput {
  email: String!
  name: String!
  role: Role!
}

input UpdateUserInput {
  name: String
  role: Role
}

input TaskFilter {
  status: TaskStatus
  assigneeId: ID
}

# Query Root
type Query {
  # Get single user
  user(id: ID!): User
  
  # Get multiple users with filtering and pagination
  users(
    filter: UserFilter
    pagination: PaginationInput
  ): UserConnection!
  
  # Get tasks with filtering
  tasks(filter: TaskFilter): [Task!]!
}

# Mutation Root
type Mutation {
  # User mutations
  createUser(input: CreateUserInput!): User!
  updateUser(id: ID!, input: UpdateUserInput!): User!
  deleteUser(id: ID!): Boolean!
  
  # Task mutations
  createTask(input: CreateTaskInput!): Task!
  updateTask(id: ID!, input: UpdateTaskInput!): Task!
  assignTask(taskId: ID!, userId: ID!): Task!
}

# Subscription Root
type Subscription {
  # Real-time task updates
  taskUpdated(userId: ID): Task!
  
  # User notifications
  userNotification(userId: ID!): Notification!
}
```

### **🔗 GraphQL Federation**

```typescript
// User Service Schema
@Directive('@key(fields: "id")')
export class User {
  @Field(type => ID)
  id: string;

  @Field()
  email: string;

  @Field()
  name: string;

  @Field(type => Role)
  role: Role;
}

// Task Service Schema (extends User)
@Directive('@extends')
@Directive('@key(fields: "id")')
export class User {
  @Field(type => ID)
  @Directive('@external')
  id: string;

  @Field(type => [Task])
  tasks: Task[];
}

// Gateway Configuration
const gateway = new ApolloGateway({
  serviceList: [
    { name: 'users', url: 'http://localhost:4001/graphql' },
    { name: 'tasks', url: 'http://localhost:4002/graphql' },
    { name: 'notifications', url: 'http://localhost:4003/graphql' },
  ],
});
```

---

## **6. AUTHENTICATION & SECURITY**

### **🔐 JWT Authentication**

```typescript
// JWT Token Structure
interface JWTPayload {
  sub: string;           // User ID
  email: string;         // User email
  role: string;          // User role
  iat: number;           // Issued at
  exp: number;           // Expiration time
  aud: string;           // Audience
  iss: string;           // Issuer
}

// Authentication Headers
const AuthHeaders = {
  required: {
    "Authorization": "Bearer <jwt_token>",
    "Content-Type": "application/json",
  },
  optional: {
    "X-Request-ID": "unique-request-identifier",
    "X-API-Version": "v1",
  },
};

// Authentication Implementation
@Injectable()
export class AuthGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractToken(request);
    
    if (!token) {
      throw new UnauthorizedException('Token required');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token);
      request.user = await this.userService.findById(payload.sub);
      return true;
    } catch {
      throw new UnauthorizedException('Invalid token');
    }
  }
}
```

### **🛡️ API Security Best Practices**

```typescript
// Security Configuration
const SecurityStandards = {
  authentication: {
    tokenExpiry: "15 minutes for access tokens",
    refreshTokenExpiry: "7 days for refresh tokens",
    algorithm: "RS256 for JWT signing",
    secretRotation: "Monthly key rotation",
  },

  authorization: {
    rbac: "Role-based access control",
    scopes: "API scopes for fine-grained permissions",
    cors: "Strict CORS policy configuration",
  },

  inputValidation: {
    sanitization: "Sanitize all input data",
    validation: "Validate request schemas",
    rateLimit: "Rate limiting per endpoint",
    ipWhitelist: "IP whitelisting for admin APIs",
  },

  dataProtection: {
    encryption: "AES-256 for sensitive data",
    hashing: "bcrypt for passwords",
    tls: "TLS 1.3 for all communications",
    headers: "Security headers (HSTS, CSP, etc.)",
  },
};
```

---

## **7. ERROR HANDLING**

### **🚨 Error Handling Strategy**

```typescript
// Custom Error Classes
export class APIError extends Error {
  constructor(
    public readonly code: string,
    public readonly message: string,
    public readonly statusCode: number,
    public readonly details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export class ValidationError extends APIError {
  constructor(errors: FieldError[]) {
    super(
      'VALIDATION_ERROR',
      'The request contains invalid data',
      422,
      { errors }
    );
  }
}

export class NotFoundError extends APIError {
  constructor(resource: string, id: string) {
    super(
      'RESOURCE_NOT_FOUND',
      `${resource} with ID ${id} not found`,
      404
    );
  }
}

// Global Error Handler
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    let status = 500;
    let errorResponse: ErrorResponse;

    if (exception instanceof APIError) {
      status = exception.statusCode;
      errorResponse = {
        success: false,
        error: {
          code: exception.code,
          message: exception.message,
          details: exception.details,
        },
      };
    } else {
      errorResponse = {
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
        },
      };
    }

    // Log error for monitoring
    this.logger.error({
      message: exception.message,
      stack: exception.stack,
      url: request.url,
      method: request.method,
      userId: request.user?.id,
    });

    response.status(status).json(errorResponse);
  }
}
```

---

## **8. API DOCUMENTATION**

### **📚 OpenAPI/Swagger Standards**

```typescript
// Swagger Configuration
const swaggerConfig = new DocumentBuilder()
  .setTitle('Enterprise API')
  .setDescription(`
    🏗️ Enterprise-grade API implementing:
    
    **Architecture Patterns:**
    - Clean Architecture with DDD
    - CQRS with Event Sourcing
    - Microservices Communication
    
    **Security Features:**
    - JWT Authentication
    - Role-based Access Control
    - Rate Limiting
    - Input Validation
  `)
  .setVersion('1.0')
  .addBearerAuth()
  .addTag('Users', 'User management operations')
  .addTag('Tasks', 'Task management operations')
  .addTag('Auth', 'Authentication operations')
  .build();

// Controller Documentation
@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  @Post()
  @ApiOperation({ 
    summary: 'Create a new user',
    description: 'Creates a new user account with the provided information'
  })
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({ 
    status: 201, 
    description: 'User created successfully',
    type: UserResponseDto 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Validation error',
    type: ErrorResponseDto 
  })
  async create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }
}

// DTO Documentation
export class CreateUserDto {
  @ApiProperty({ 
    description: 'User email address',
    example: '<EMAIL>',
    format: 'email'
  })
  @IsEmail()
  email: string;

  @ApiProperty({ 
    description: 'User full name',
    example: 'John Doe',
    minLength: 2,
    maxLength: 100
  })
  @IsString()
  @Length(2, 100)
  name: string;

  @ApiProperty({ 
    description: 'User role',
    enum: Role,
    example: Role.USER
  })
  @IsEnum(Role)
  role: Role;
}
```

---

## **9. VERSIONING STRATEGY**

### **📈 API Versioning Approaches**

```typescript
// Versioning Strategies
const VersioningStrategies = {
  // 1. URL Path Versioning (Recommended)
  urlPath: {
    format: "/api/v1/users",
    pros: ["Clear and explicit", "Easy to cache", "Simple routing"],
    cons: ["URL changes", "Multiple endpoints"],
    example: "GET /api/v1/users vs GET /api/v2/users",
  },

  // 2. Header Versioning
  header: {
    format: "API-Version: v1",
    pros: ["Clean URLs", "Flexible"],
    cons: ["Less visible", "Caching complexity"],
    example: "headers: { 'API-Version': 'v1' }",
  },

  // 3. Query Parameter Versioning
  queryParam: {
    format: "/api/users?version=v1",
    pros: ["Simple implementation"],
    cons: ["Can be ignored", "Pollutes URLs"],
    example: "GET /api/users?version=v1",
  },
};

// Version Management Implementation
@Controller('api/v1/users')
export class UsersV1Controller {
  // Version 1 implementation
}

@Controller('api/v2/users')
export class UsersV2Controller {
  // Version 2 implementation with new features
}

// Deprecation Strategy
@ApiDeprecated()
@ApiHeader({
  name: 'Sunset',
  description: 'API deprecation date',
  required: false,
})
@Controller('api/v1/legacy')
export class LegacyController {
  @Get()
  @Header('Sunset', 'Sat, 31 Dec 2024 23:59:59 GMT')
  @Header('Deprecation', 'true')
  legacyEndpoint() {
    // Deprecated functionality
  }
}
```

---

## **10. PERFORMANCE & OPTIMIZATION**

### **⚡ Performance Best Practices**

```typescript
// Caching Strategy
@Injectable()
export class CacheService {
  // Redis caching implementation
  async get<T>(key: string): Promise<T | null> {
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  async set(key: string, value: any, ttl: number): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }

  async invalidate(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}

// Pagination Implementation
interface PaginationQuery {
  page?: number;
  limit?: number;
  sort?: string;
}

@Get()
async findAll(@Query() query: PaginationQuery) {
  const { page = 1, limit = 20, sort = 'id' } = query;
  const offset = (page - 1) * limit;

  const [users, total] = await Promise.all([
    this.usersService.findMany({ offset, limit, sort }),
    this.usersService.count(),
  ]);

  return {
    data: users,
    meta: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1,
    },
  };
}

// Rate Limiting
@UseGuards(ThrottlerGuard)
@Throttle(100, 60) // 100 requests per minute
@Controller('api/v1')
export class APIController {
  // Rate limited endpoints
}

// Database Query Optimization
@Injectable()
export class UsersService {
  async findWithTasks(userId: string) {
    // Use eager loading to prevent N+1 queries
    return this.userRepository.findOne({
      where: { id: userId },
      relations: ['tasks', 'profile'],
      select: ['id', 'email', 'name'], // Select only needed fields
    });
  }

  async findMany(options: FindOptions) {
    // Use database indexing and proper WHERE clauses
    return this.userRepository
      .createQueryBuilder('user')
      .where('user.status = :status', { status: 'active' })
      .orderBy('user.createdAt', 'DESC')
      .skip(options.offset)
      .take(options.limit)
      .getMany();
  }
}
```

### **📊 Performance Monitoring**

```typescript
// Response Time Monitoring
@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const start = Date.now();
    const request = context.switchToHttp().getRequest();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - start;
        
        // Log slow requests
        if (duration > 1000) {
          this.logger.warn(`Slow request: ${request.method} ${request.url} - ${duration}ms`);
        }

        // Metrics for monitoring
        this.metricsService.recordResponseTime(
          request.route?.path || request.url,
          request.method,
          duration
        );
      })
    );
  }
}
```

---

## **📚 QUICK REFERENCE**

### **🔧 Implementation Checklist**

```typescript
// API Implementation Checklist
const APIChecklist = {
  design: [
    "✅ Follow REST principles",
    "✅ Use noun-based URLs",
    "✅ Implement proper HTTP methods",
    "✅ Return appropriate status codes",
    "✅ Design consistent response formats",
  ],

  security: [
    "✅ Implement JWT authentication",
    "✅ Add role-based authorization",
    "✅ Validate all inputs",
    "✅ Implement rate limiting",
    "✅ Use HTTPS in production",
  ],

  documentation: [
    "✅ Add OpenAPI/Swagger docs",
    "✅ Document all endpoints",
    "✅ Provide request/response examples",
    "✅ Include error codes",
    "✅ Add authentication guides",
  ],

  performance: [
    "✅ Implement caching strategy",
    "✅ Add pagination for collections",
    "✅ Optimize database queries",
    "✅ Monitor response times",
    "✅ Set up error tracking",
  ],
};
```

### **📊 Status Code Quick Reference**

| Code | Meaning | Usage |
|------|---------|-------|
| 200 | OK | Successful GET, PUT, PATCH |
| 201 | Created | Successful POST |
| 204 | No Content | Successful DELETE |
| 400 | Bad Request | Invalid request syntax |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Access denied |
| 404 | Not Found | Resource doesn't exist |
| 422 | Unprocessable Entity | Validation errors |
| 500 | Internal Server Error | Server error |

---

## **🎯 NEXT STEPS**

1. **📖 Read Architecture Guide**: [ARCHITECTURE.md](ARCHITECTURE.md)
2. **🚀 Follow Quick Start**: [QUICK_START.md](QUICK_START.md)
3. **⚡ Check Services Guide**: [SERVICES_GUIDE.md](SERVICES_GUIDE.md)
4. **💡 Explore Examples**: [examples/rest-api/](examples/rest-api/)

---

> **🎯 These API standards ensure consistency, security, and performance across all services. Use this guide as your reference for all API design and implementation decisions!**