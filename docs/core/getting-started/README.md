# 📖 **GETTING STARTED - ENTERPRISE FOUNDATION**

> **🚀 Complete setup guide to get you from zero to productive in < 30 minutes**

[![Setup Time](https://img.shields.io/badge/Setup%20Time-<30%20min-brightgreen)](QUICK_START.md)
[![Difficulty](https://img.shields.io/badge/Difficulty-Beginner-green)](README.md)
[![Prerequisites](https://img.shields.io/badge/Prerequisites-Minimal-blue)](PREREQUISITES.md)

## 🎯 **QUICK NAVIGATION**

### **⚡ Essential Guides**
- **[🚀 Quick Start](QUICK_START.md)** - Get running in 30 minutes
- **[📋 Prerequisites](PREREQUISITES.md)** - System requirements & tools
- **[🔧 Environment Setup](ENVIRONMENT_SETUP.md)** - Development environment
- **[✅ Verification](VERIFICATION.md)** - Verify your setup

### **🎓 Learning Path**
- **[📚 Foundation Concepts](FOUNDATION_CONCEPTS.md)** - Core concepts overview
- **[🏗️ Architecture Basics](ARCHITECTURE_BASICS.md)** - System architecture introduction
- **[💻 First Steps](FIRST_STEPS.md)** - Your first implementation
- **[🧪 Testing Setup](TESTING_SETUP.md)** - Testing environment

## 🚀 **30-MINUTE QUICK START**

### **Step 1: Prerequisites Check (5 minutes)**
```bash
# Run the automated prerequisites checker
curl -fsSL https://raw.githubusercontent.com/enterprise-platform/main/scripts/check-prerequisites.sh | bash
```

### **Step 2: Environment Setup (15 minutes)**
```bash
# Clone and setup the enterprise platform
git clone <your-enterprise-platform-repo>
cd enterprise-platform

# Run the complete setup script
./scripts/setup/quick-start.sh
```

### **Step 3: Verification (5 minutes)**
```bash
# Verify everything is working
./scripts/monitoring/health-check.sh

# Access your platform
open http://localhost:3000
```

### **Step 4: First Implementation (5 minutes)**
```bash
# Create your first service
./tools/generators/create-service.sh my-first-service nestjs

# Start development
cd services/my-first-service
npm run dev
```

## 📋 **COMPLETE SETUP CHECKLIST**

### **🔧 System Requirements**
- [ ] **Operating System**: macOS, Linux, or Windows with WSL2
- [ ] **Memory**: Minimum 8GB RAM (16GB recommended)
- [ ] **Storage**: 50GB free space (SSD recommended)
- [ ] **Network**: Stable internet connection

### **📦 Required Software**
- [ ] **Docker**: Version 20.10+ with Docker Compose
- [ ] **Node.js**: Version 18+ LTS
- [ ] **Python**: Version 3.9+
- [ ] **Git**: Version 2.30+
- [ ] **Code Editor**: VS Code (recommended) or similar

### **⚙️ Development Tools**
- [ ] **Package Managers**: npm, pip, yarn (optional)
- [ ] **Database Tools**: PostgreSQL client, Redis CLI
- [ ] **API Testing**: Postman or similar
- [ ] **Terminal**: Modern terminal with shell support

## 🎯 **LEARNING OBJECTIVES**

By the end of this getting started phase, you will:

### **✅ Foundation Skills**
- [ ] Understand enterprise architecture principles
- [ ] Set up complete development environment
- [ ] Navigate the project structure effectively
- [ ] Run and test the platform locally

### **✅ Technical Skills**
- [ ] Use Docker for containerization
- [ ] Work with microservices architecture
- [ ] Implement basic CRUD operations
- [ ] Write and run tests

### **✅ Development Skills**
- [ ] Follow coding standards and best practices
- [ ] Use version control effectively
- [ ] Debug and troubleshoot issues
- [ ] Collaborate with team members

## 🏗️ PROJECT STRUCTURE OVERVIEW

For the complete, canonical project structure and principles, see:
- ../architecture/PROJECT_STRUCTURE.md

Summary:
- Monorepo layout with apps, services, libs, infrastructure, and more
- Clean Architecture layering per service, DDD-aligned boundaries
- Clear dependency direction and naming conventions

## 🚀 **FIRST IMPLEMENTATION**

### **🎯 Create Your First Service**

```bash
# Navigate to the project root
cd enterprise-platform

# Generate a new service
./tools/generators/create-service.sh my-first-service nestjs

# Navigate to the new service
cd services/my-first-service

# Install dependencies
npm install

# Start development server
npm run start:dev
```

### **🔧 Service Structure**

Your new service will have this structure:

```
my-first-service/
├── src/
│   ├── domain/                 # Business logic
│   ├── application/            # Use cases
│   ├── infrastructure/         # External concerns
│   └── interface/              # API controllers
├── tests/                      # Test files
├── Dockerfile                  # Container configuration
└── package.json                # Dependencies
```

### **🧪 Write Your First Test**

```typescript
// tests/unit/my-first-service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { MyFirstService } from './my-first.service';

describe('MyFirstService', () => {
  let service: MyFirstService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MyFirstService],
    }).compile();

    service = module.get<MyFirstService>(MyFirstService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return hello message', () => {
    const result = service.getHello();
    expect(result).toBe('Hello from MyFirstService!');
  });
});
```

## 📊 **VERIFICATION & TESTING**

### **✅ Health Checks**

```bash
# Check all services are running
curl http://localhost:3000/health          # API Gateway
curl http://localhost:3002/health          # User Service
curl http://localhost:3003/health          # AI Service

# Check databases
docker exec postgres psql -U dev_user -d enterprise_platform -c "SELECT COUNT(*) FROM users;"
docker exec redis redis-cli ping

# Check monitoring
open http://localhost:3001                  # Grafana
open http://localhost:9090                  # Prometheus
```

### **🧪 Run Tests**

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:cov

# Run specific test file
npm test -- my-first-service.spec.ts
```

## 🔧 **DEVELOPMENT WORKFLOW**

### **🌿 Git Workflow**

```bash
# Create feature branch
git checkout -b feature/my-first-feature

# Make changes and commit
git add .
git commit -m "feat: implement my first feature"

# Push and create PR
git push origin feature/my-first-feature
# Create Pull Request on GitHub
```

### **📝 Code Standards**

- **TypeScript**: Strict mode enabled
- **ESLint**: Code quality rules
- **Prettier**: Code formatting
- **Husky**: Pre-commit hooks
- **Commitizen**: Conventional commits

## 🚨 **TROUBLESHOOTING**

### **🔍 Common Issues**

#### **Docker Issues**
```bash
# Check Docker status
docker --version
docker-compose --version

# Restart Docker services
docker-compose down
docker-compose up -d
```

#### **Port Conflicts**
```bash
# Check what's using a port
lsof -i :3000
lsof -i :8000

# Kill process using port
kill -9 <PID>
```

#### **Database Connection Issues**
```bash
# Check database status
docker-compose ps

# View database logs
docker-compose logs postgres
docker-compose logs redis
```

### **📞 Getting Help**

- **📖 Documentation**: [Complete docs](docs/README.md)
- **🐛 Issues**: [GitHub Issues](https://github.com/your-org/enterprise-platform/issues)
- **💬 Discussions**: [GitHub Discussions](https://github.com/your-org/enterprise-platform/discussions)
- **📧 Email**: <EMAIL>

## 📚 **RELATED DOCUMENTATION**

- **[🏗️ Architecture Guide](../architecture/README.md)** - System design and patterns
- **[💻 Implementation Guide](../implementation/README.md)** - Development patterns
- **[🚀 Deployment Guide](../../guides/deployment/README.md)** - Production deployment
- **[🧪 Testing Strategy](../../guides/workflow/README.md)** - Testing approach

## 🎯 **NEXT STEPS**

1. **📖 Read [Architecture Guide](../architecture/README.md)** - Understand system design
2. **💻 Explore [Implementation Guide](../implementation/README.md)** - Learn development patterns
3. **🚀 Check [Deployment Guide](../../guides/deployment/README.md)** - Deploy to production
4. **🧪 Review [Testing Strategy](../../guides/workflow/README.md)** - Implement testing

---

> **🚀 You're now ready to start building enterprise-grade applications! This foundation will serve you throughout your career.**
