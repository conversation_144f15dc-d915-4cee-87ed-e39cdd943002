# 🚀 **<PERSON><PERSON><PERSON>K START GUIDE**

> **Get your enterprise platform running in under 5 minutes**

[![Setup Time](https://img.shields.io/badge/Setup%20Time-<5%20min-brightgreen)](QUICK_START.md)
[![One Command](https://img.shields.io/badge/Setup-One%20Command-blue)](QUICK_START.md)
[![Production Ready](https://img.shields.io/badge/Production-Ready-yellow)](QUICK_START.md)

## ⚡ **INSTANT SETUP (30 seconds)**

```bash
# 🎯 ONE-COMMAND COMPLETE SETUP
curl -fsSL https://raw.githubusercontent.com/enterprise-platform/main/scripts/setup.sh | bash

# 🌟 Success! Access your enterprise platform:
# - API Gateway: http://localhost:3000
# - AI Service: http://localhost:8000  
# - Monitoring: http://localhost:3001
# - Admin Panel: http://localhost:3002
```

## 📋 **TABLE OF CONTENTS**

1. [Prerequisites](#prerequisites)
2. [Automated Setup](#automated-setup)
3. [Manual Setup](#manual-setup)
4. [Service Configuration](#service-configuration)
5. [Verification](#verification)
6. [Troubleshooting](#troubleshooting)

---

## **1. PREREQUISITES**

### **💻 System Requirements**

```bash
# Minimum Hardware Requirements
CPU: 4 cores (8 cores recommended)
RAM: 8GB (16GB recommended)  
Storage: 50GB free space (SSD recommended)
Network: Stable internet connection
```

### **🛠️ Required Software**

| Software | Version | Installation |
|----------|---------|-------------|
| **Docker** | >= 20.10 | `curl -fsSL https://get.docker.com \| sh` |
| **Docker Compose** | >= 2.0 | Included with Docker Desktop |
| **Node.js** | >= 18.x LTS | `https://nodejs.org` |
| **Python** | >= 3.9 | `https://python.org` |
| **Git** | >= 2.30 | `https://git-scm.com` |

### **⚡ One-Command Prerequisites Check**

```bash
#!/bin/bash
# Prerequisites Checker Script

echo "🔍 Checking Prerequisites..."

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker not found. Installing..."
    curl -fsSL https://get.docker.com | sh
else
    echo "✅ Docker: $(docker --version)"
fi

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install Node.js 18+ LTS"
    exit 1
else
    echo "✅ Node.js: $(node --version)"
fi

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python not found. Please install Python 3.9+"
    exit 1
else
    echo "✅ Python: $(python3 --version)"
fi

echo "🎉 All prerequisites met!"
```

---

## **2. AUTOMATED SETUP**

### **🎯 Complete Platform Setup**

```bash
# Clone repository
git clone <repository-url>
cd enterprise-platform

# Run automated setup
./scripts/setup-complete.sh

# This script will:
# ✅ Install all dependencies
# ✅ Setup databases (PostgreSQL, Redis, MongoDB)
# ✅ Start all services
# ✅ Run health checks
# ✅ Setup monitoring stack
```

### **🐳 Docker-Based Setup**

```bash
# Start complete stack with Docker Compose
docker-compose up -d

# Services included:
# - PostgreSQL (port 5432)
# - Redis (port 6379)  
# - MongoDB (port 27017)
# - Qdrant Vector DB (port 6333)
# - API Gateway (port 3000)
# - AI Service (port 8000)
# - Monitoring (port 3001)
```

### **🔧 Environment Configuration**

```bash
# Create environment file
cp .env.example .env

# Edit configuration (optional)
nano .env

# Key configurations:
NODE_ENV=development
DATABASE_URL=postgresql://user:password@localhost:5432/enterprise_db
REDIS_URL=redis://localhost:6379
AI_SERVICE_URL=http://localhost:8000
JWT_SECRET=your_jwt_secret_here
```

---

## **3. MANUAL SETUP**

### **📦 Step-by-Step Installation**

```bash
# 1. Install dependencies
npm install
pip install -r requirements.txt

# 2. Setup databases
docker-compose up -d postgres redis mongodb qdrant

# 3. Run database migrations
npm run db:migrate

# 4. Seed test data
npm run db:seed

# 5. Start services individually
npm run start:gateway    # API Gateway (port 3000)
npm run start:ai        # AI Service (port 8000)
npm run start:web       # Web App (port 3001)
npm run start:admin     # Admin Panel (port 3002)
```

### **🔍 Database Setup**

```bash
# PostgreSQL setup
createdb enterprise_platform
psql -d enterprise_platform -f data/databases/postgresql/schema.sql

# Redis configuration
redis-cli config set maxmemory 2gb
redis-cli config set maxmemory-policy allkeys-lru

# MongoDB setup
mongosh --eval "db = db.getSiblingDB('enterprise_platform'); db.createUser({user: 'enterprise_user', pwd: 'password', roles: ['readWrite']})"
```

---

## **4. SERVICE CONFIGURATION**

### **🌐 Service Endpoints**

| Service | URL | Purpose | Health Check |
|---------|-----|---------|-------------|
| **API Gateway** | http://localhost:3000 | Main entry point | `/health` |
| **Web App** | http://localhost:3001 | Frontend application | `/` |
| **Admin Panel** | http://localhost:3002 | Admin dashboard | `/admin` |
| **AI Service** | http://localhost:8000 | AI/ML processing | `/health` |
| **Monitoring** | http://localhost:3001 | Grafana dashboard | `/grafana` |
| **API Docs** | http://localhost:3000/docs | Swagger documentation | `/docs` |

### **🔐 Default Credentials**

```bash
# Admin User
Email: <EMAIL>
Password: admin123

# Grafana Monitoring
Username: admin  
Password: admin123

# Database Connections
PostgreSQL: postgresql://enterprise_user:password@localhost:5432/enterprise_platform
Redis: redis://localhost:6379
MongoDB: **********************************************************************
```

### **⚙️ Service Configuration Files**

```bash
# API Gateway Configuration
apps/api-gateway/.env
apps/api-gateway/src/config/

# AI Service Configuration  
services/ai-service/.env
services/ai-service/app/config/

# Database Configurations
data/databases/postgresql/
data/databases/mongodb/
data/databases/redis/
```

---

## **5. VERIFICATION**

### **✅ Health Check Commands**

```bash
# Check all services
npm run health-check

# Individual service checks
curl http://localhost:3000/health     # API Gateway
curl http://localhost:8000/health     # AI Service
curl http://localhost:3001/health     # Web App
curl http://localhost:3002/health     # Admin Panel

# Database connections
npm run db:check                      # Check all databases
```

### **🧪 Run Tests**

```bash
# Run complete test suite
npm test

# Run specific test types
npm run test:unit                     # Unit tests
npm run test:integration              # Integration tests  
npm run test:e2e                      # End-to-end tests

# Run performance tests
npm run test:performance
```

### **📊 Monitoring Setup**

```bash
# Access monitoring dashboards
open http://localhost:3001/grafana   # Grafana dashboards
open http://localhost:9090           # Prometheus metrics
open http://localhost:16686          # Jaeger tracing

# Check system metrics
docker stats                         # Container resource usage
npm run metrics                      # Application metrics
```

---

## **6. TROUBLESHOOTING**

### **🔧 Common Issues**

#### **Port Conflicts**
```bash
# Check port usage
lsof -i :3000
lsof -i :8000

# Kill processes on ports
kill -9 $(lsof -t -i:3000)
kill -9 $(lsof -t -i:8000)

# Use different ports
PORT=3001 npm run start:gateway
```

#### **Database Connection Issues**
```bash
# Check database status
docker-compose ps

# Restart databases
docker-compose restart postgres redis mongodb

# Check database logs
docker-compose logs postgres
docker-compose logs redis
```

#### **Memory Issues**
```bash
# Check system memory
free -h

# Increase Docker memory (Docker Desktop)
# Settings > Resources > Memory > 8GB recommended

# Clean Docker resources
docker system prune -a
```

### **🚨 Reset Everything**

```bash
# Complete cleanup and restart
./scripts/cleanup.sh
./scripts/setup-complete.sh

# Nuclear option - remove all data
docker-compose down -v
rm -rf node_modules
npm install
docker-compose up -d
```

### **📞 Getting Help**

```bash
# Check logs
npm run logs                         # Application logs
docker-compose logs                  # Docker logs

# Debug mode
DEBUG=* npm run start:dev           # Enable debug logging

# Support resources
- Documentation: docs/README.md
- Architecture Guide: ARCHITECTURE.md  
- Service Guide: SERVICES_GUIDE.md
- GitHub Issues: github.com/your-org/enterprise-platform/issues
```

---

## **🎯 NEXT STEPS**

After successful setup:

1. **📖 Read Documentation**
   - [ARCHITECTURE.md](ARCHITECTURE.md) - System architecture
   - [SERVICES_GUIDE.md](SERVICES_GUIDE.md) - Service development
   - [API_STANDARDS.md](API_STANDARDS.md) - API guidelines

2. **🔧 Explore Services**
   - Try API endpoints: http://localhost:3000/docs
   - Test AI features: http://localhost:8000/docs
   - Use admin panel: http://localhost:3002

3. **👨‍💻 Start Development**
   - Create new service: `./tools/generators/create-service.sh`
   - Add new features: Follow patterns in `examples/`
   - Deploy to staging: `npm run deploy:staging`

4. **📊 Monitor Performance**
   - Setup alerts in Grafana
   - Configure log aggregation
   - Implement custom metrics

---

## **🎉 SUCCESS!**

Your enterprise platform is now running! 🚀

```bash
# Quick verification
curl http://localhost:3000/health

# Expected response:
{
  "status": "healthy",
  "services": {
    "database": "connected", 
    "redis": "connected",
    "ai-service": "running"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

> **💡 Pro Tip**: Bookmark the monitoring dashboard (http://localhost:3001) to keep track of your system's health and performance!

---

**📚 Need more help?** Check out our comprehensive [documentation](docs/README.md) or visit the [troubleshooting guide](docs/guides/troubleshooting/README.md).