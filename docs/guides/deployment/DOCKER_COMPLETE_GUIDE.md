# 🐳 **DOCKER COMPLETE HANDBOOK**

> **🚀 Comprehensive guide to Docker containerization, from basics to advanced production deployment**

[![Docker](https://img.shields.io/badge/Docker-Complete%20Guide-blue)](DOCKER_COMPLETE_GUIDE.md)
[![Containerization](https://img.shields.io/badge/Containerization-Production%20Ready-green)](#production-deployment)
[![Best Practices](https://img.shields.io/badge/Best%20Practices-Enterprise%20Grade-yellow)](#best-practices)

## 📚 **TABLE OF CONTENTS**

1. [**🚀 Getting Started**](#getting-started)
2. [**📦 Core Concepts**](#core-concepts)
3. [**🛠️ Docker Commands**](#docker-commands)
4. [**🏗️ Dockerfile Mastery**](#dockerfile-mastery)
5. [**🔧 Docker Compose**](#docker-compose)
6. [**📊 Image Management**](#image-management)
7. **Production Deployment**](#production-deployment)
8. **Best Practices**](#best-practices)
9. **Troubleshooting**](#troubleshooting)
10. **Advanced Topics**](#advanced-topics)

## 🚀 **GETTING STARTED**

### **What is Docker?**

Docker is a platform for developing, shipping, and running applications in containers. Containers are lightweight, portable, and self-contained units that can run anywhere Docker is installed.

### **Why Docker?**

- **Consistency**: Same environment across development, testing, and production
- **Isolation**: Applications run in isolated environments
- **Portability**: Run anywhere Docker is installed
- **Efficiency**: Lightweight compared to virtual machines
- **Scalability**: Easy to scale applications horizontally

### **Installation**

#### **Linux (Ubuntu/Debian)**
```bash
# Update package index
sudo apt-get update

# Install prerequisites
sudo apt-get install \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# Add Docker's official GPG key
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Set up repository
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Start Docker service
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER
```

#### **macOS**
```bash
# Using Homebrew
brew install --cask docker

# Or download from Docker Desktop website
# https://www.docker.com/products/docker-desktop
```

#### **Windows**
```bash
# Download Docker Desktop from
# https://www.docker.com/products/docker-desktop
```

### **Verify Installation**
```bash
# Check Docker version
docker --version

# Check Docker Compose version
docker compose version

# Run hello-world container
docker run hello-world
```

## 📦 **CORE CONCEPTS**

### **Docker Architecture**

```
┌─────────────────────────────────────────────────────────┐
│                    🖥️ DOCKER HOST                       │
├─────────────────────────────────────────────────────────┤
│                   🐳 DOCKER DAEMON                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │   Client    │ │   Images    │ │ Containers │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│                   🖥️ OPERATING SYSTEM                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │   Kernel    │ │   Drivers   │ │   System    │      │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

### **Key Components**

1. **Docker Client**: Command-line interface to interact with Docker
2. **Docker Daemon**: Background service that manages containers
3. **Docker Images**: Templates for creating containers
4. **Docker Containers**: Running instances of images
5. **Docker Registry**: Repository for storing and sharing images

### **Container vs Virtual Machine**

| Aspect | Container | Virtual Machine |
|--------|-----------|-----------------|
| **Size** | MBs | GBs |
| **Startup Time** | Seconds | Minutes |
| **Resource Usage** | Low | High |
| **Isolation** | Process level | OS level |
| **Portability** | High | Medium |

## 🛠️ **DOCKER COMMANDS**

### **Basic Commands**

```bash
# System information
docker version
docker info
docker system df

# Help
docker --help
docker <command> --help
```

### **Image Commands**

```bash
# List images
docker images
docker image ls

# Pull image from registry
docker pull ubuntu:latest
docker pull nginx:alpine

# Remove image
docker rmi ubuntu:latest
docker image rm nginx:alpine

# Build image from Dockerfile
docker build -t myapp:latest .

# Tag image
docker tag myapp:latest myapp:v1.0

# Save/load image
docker save -o myapp.tar myapp:latest
docker load -i myapp.tar
```

### **Container Commands**

```bash
# Run container
docker run ubuntu:latest
docker run -d --name mycontainer nginx:alpine
docker run -p 8080:80 nginx:alpine

# List containers
docker ps
docker ps -a

# Start/stop container
docker start mycontainer
docker stop mycontainer
docker restart mycontainer

# Remove container
docker rm mycontainer
docker rm -f mycontainer

# Execute command in running container
docker exec -it mycontainer bash
docker exec mycontainer ls -la

# View container logs
docker logs mycontainer
docker logs -f mycontainer

# Copy files
docker cp mycontainer:/app/file.txt ./
docker cp ./file.txt mycontainer:/app/
```

### **Network Commands**

```bash
# List networks
docker network ls

# Create network
docker network create mynetwork

# Inspect network
docker network inspect mynetwork

# Remove network
docker network rm mynetwork

# Connect container to network
docker network connect mynetwork mycontainer

# Disconnect container from network
docker network disconnect mynetwork mycontainer
```

### **Volume Commands**

```bash
# List volumes
docker volume ls

# Create volume
docker volume create myvolume

# Inspect volume
docker volume inspect myvolume

# Remove volume
docker volume rm myvolume

# Remove unused volumes
docker volume prune
```

### **System Commands**

```bash
# System information
docker system df
docker system info

# Clean up
docker system prune
docker system prune -a

# Docker daemon info
docker info
```

## 🏗️ **DOCKERFILE MASTERY**

### **Basic Dockerfile Structure**

```dockerfile
# Base image
FROM ubuntu:20.04

# Metadata
LABEL maintainer="<EMAIL>"
LABEL version="1.0"
LABEL description="My application"

# Set working directory
WORKDIR /app

# Copy files
COPY . .

# Install dependencies
RUN apt-get update && \
    apt-get install -y python3 python3-pip && \
    pip3 install -r requirements.txt

# Expose port
EXPOSE 8000

# Set environment variables
ENV PYTHONPATH=/app
ENV FLASK_ENV=production

# Run command
CMD ["python3", "app.py"]
```

### **Multi-Stage Builds**

```dockerfile
# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Production stage
FROM node:18-alpine AS production
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy application from builder
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .

# Switch to non-root user
USER nextjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000
CMD ["npm", "start"]
```

### **Dockerfile Best Practices**

#### **Layer Optimization**
```dockerfile
# ❌ Bad: Multiple layers
RUN apt-get update
RUN apt-get install -y python3
RUN apt-get install -y python3-pip

# ✅ Good: Single layer
RUN apt-get update && \
    apt-get install -y \
    python3 \
    python3-pip && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
```

#### **Security Considerations**
```dockerfile
# Create non-root user
RUN addgroup -g 1001 -S appuser
RUN adduser -S appuser -u 1001

# Copy files with correct ownership
COPY --chown=appuser:appuser . .

# Switch to non-root user
USER appuser

# Use specific base image versions
FROM ubuntu:20.04  # ✅ Specific version
# FROM ubuntu:latest  # ❌ Latest can change
```

#### **Caching Optimization**
```dockerfile
# Copy dependency files first
COPY package*.json ./
RUN npm install

# Copy source code after dependencies
COPY . .

# This way, if only source code changes, 
# npm install won't run again
```

### **Advanced Dockerfile Features**

#### **Build Arguments**
```dockerfile
ARG NODE_VERSION=18
FROM node:${NODE_VERSION}-alpine

ARG BUILD_DATE
ARG VCS_REF
LABEL org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.vcs-ref=$VCS_REF
```

#### **Health Checks**
```dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1
```

#### **Multi-Platform Builds**
```dockerfile
# Build for multiple architectures
FROM --platform=$BUILDPLATFORM node:18-alpine AS builder
# ... build steps
FROM --platform=$TARGETPLATFORM node:18-alpine AS production
# ... production steps
```

## 🔧 **DOCKER COMPOSE**

### **Basic docker-compose.yml**

```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
    depends_on:
      - db
    networks:
      - app-network

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres-data:
    driver: local
```

### **Advanced Compose Features**

#### **Environment Variables**
```yaml
services:
  web:
    build: .
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DATABASE_URL=${DATABASE_URL}
    env_file:
      - .env
      - .env.local
```

#### **Volumes and Bind Mounts**
```yaml
services:
  web:
    build: .
    volumes:
      # Named volume
      - postgres-data:/var/lib/postgresql/data
      # Bind mount
      - ./logs:/app/logs
      # Anonymous volume
      - /app/tmp
```

#### **Resource Limits**
```yaml
services:
  web:
    build: .
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
```

#### **Health Checks**
```yaml
services:
  web:
    build: .
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

### **Compose Commands**

```bash
# Start services
docker-compose up
docker-compose up -d  # Detached mode

# Stop services
docker-compose down

# View logs
docker-compose logs
docker-compose logs -f web

# Execute command in service
docker-compose exec web bash

# Scale service
docker-compose up --scale web=3

# Build services
docker-compose build
docker-compose build --no-cache

# Pull images
docker-compose pull
```

## 📊 **IMAGE MANAGEMENT**

### **Image Layers**

Docker images are built in layers. Each instruction in a Dockerfile creates a new layer:

```dockerfile
FROM ubuntu:20.04          # Layer 1: Base image
RUN apt-get update         # Layer 2: System updates
RUN apt-get install nginx  # Layer 3: Install nginx
COPY . /var/www/html      # Layer 4: Copy application
EXPOSE 80                 # Layer 5: Expose port
CMD ["nginx", "-g", "daemon off;"]  # Layer 6: Command
```

### **Image Optimization**

#### **Reduce Image Size**
```dockerfile
# Use alpine-based images
FROM node:18-alpine  # Instead of node:18

# Remove unnecessary files
RUN apt-get update && \
    apt-get install -y package && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Use .dockerignore
# .dockerignore
node_modules
npm-debug.log
.git
.env
```

#### **Multi-Stage Builds for Size Reduction**
```dockerfile
# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### **Image Security**

#### **Vulnerability Scanning**
```bash
# Scan image for vulnerabilities
docker scan myapp:latest

# Using Trivy
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image myapp:latest
```

#### **Base Image Security**
```dockerfile
# Use official images
FROM node:18-alpine  # ✅ Official Node.js image

# Pin specific versions
FROM node:18.17.0-alpine  # ✅ Specific version

# Regular updates
# Keep base images updated for security patches
```

## 🚀 **PRODUCTION DEPLOYMENT**

### **Production Dockerfile**

```dockerfile
# Multi-stage production build
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS production
WORKDIR /app

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy application
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .

# Security: Run as non-root
USER nextjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Security: Use non-root port
EXPOSE 3000

# Production command
CMD ["npm", "start"]
```

### **Production docker-compose.yml**

```yaml
version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - db
      - redis
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
```

### **Environment Configuration**

```bash
# .env file
NODE_ENV=production
DATABASE_URL=**********************************/myapp
REDIS_URL=redis://redis:6379
PORT=3000
```

### **Deployment Scripts**

```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 Starting deployment..."

# Pull latest changes
git pull origin main

# Build new image
docker-compose build --no-cache

# Stop existing services
docker-compose down

# Start services
docker-compose up -d

# Wait for health checks
echo "⏳ Waiting for services to be healthy..."
sleep 30

# Check health
docker-compose ps

echo "✅ Deployment completed successfully!"
```

## 🎯 **BEST PRACTICES**

### **Security Best Practices**

1. **Use Non-Root Users**
   ```dockerfile
   RUN adduser -S appuser
   USER appuser
   ```

2. **Minimize Base Image Size**
   ```dockerfile
   FROM alpine:latest  # Instead of ubuntu:latest
   ```

3. **Scan for Vulnerabilities**
   ```bash
   docker scan myapp:latest
   ```

4. **Use Multi-Stage Builds**
   ```dockerfile
   FROM node:18-alpine AS builder
   # ... build steps
   FROM node:18-alpine AS production
   # ... production steps
   ```

5. **Limit Container Capabilities**
   ```yaml
   services:
     web:
       security_opt:
         - no-new-privileges:true
       cap_drop:
         - ALL
   ```

### **Performance Best Practices**

1. **Optimize Layer Caching**
   ```dockerfile
   # Copy dependencies first
   COPY package*.json ./
   RUN npm install
   
   # Copy source code after
   COPY . .
   ```

2. **Use .dockerignore**
   ```
   node_modules
   .git
   .env
   *.log
   ```

3. **Multi-Stage Builds**
   ```dockerfile
   FROM node:18-alpine AS builder
   # Build application
   FROM nginx:alpine AS production
   # Copy built files
   ```

4. **Resource Limits**
   ```yaml
   deploy:
     resources:
       limits:
         cpus: '1.0'
         memory: 1G
   ```

### **Monitoring Best Practices**

1. **Health Checks**
   ```dockerfile
   HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
     CMD curl -f http://localhost:3000/health || exit 1
   ```

2. **Logging**
   ```yaml
   services:
     web:
       logging:
         driver: "json-file"
         options:
           max-size: "10m"
           max-file: "3"
   ```

3. **Metrics Collection**
   ```yaml
   services:
     prometheus:
       image: prom/prometheus
       ports:
         - "9090:9090"
   ```

## 🔧 **TROUBLESHOOTING**

### **Common Issues**

#### **Container Won't Start**
```bash
# Check container logs
docker logs container_name

# Check container status
docker ps -a

# Inspect container
docker inspect container_name
```

#### **Permission Issues**
```bash
# Fix file permissions
docker run -v $(pwd):/app -w /app ubuntu chown -R 1000:1000 /app

# Run as specific user
docker run -u 1000:1000 myapp
```

#### **Network Issues**
```bash
# Check network configuration
docker network ls
docker network inspect network_name

# Test connectivity
docker exec container_name ping hostname
```

#### **Storage Issues**
```bash
# Check disk space
docker system df

# Clean up unused resources
docker system prune -a

# Check volume usage
docker volume ls
docker volume inspect volume_name
```

### **Debugging Commands**

```bash
# Interactive debugging
docker run -it --rm ubuntu bash

# Execute in running container
docker exec -it container_name bash

# View container processes
docker top container_name

# Copy files from/to container
docker cp container_name:/path/file ./
docker cp ./file container_name:/path/
```

## 🚀 **ADVANCED TOPICS**

### **Docker Swarm**

```bash
# Initialize swarm
docker swarm init

# Create service
docker service create --name web --replicas 3 nginx:alpine

# Scale service
docker service scale web=5

# Update service
docker service update --image nginx:latest web
```

### **Docker Buildx**

```bash
# Enable buildx
docker buildx create --name mybuilder --use

# Build for multiple platforms
docker buildx build --platform linux/amd64,linux/arm64 -t myapp:latest .

# Build and push
docker buildx build --platform linux/amd64,linux/arm64 -t myapp:latest --push .
```

### **Docker Content Trust**

```bash
# Enable content trust
export DOCKER_CONTENT_TRUST=1

# Sign image
docker buildx build --push --provenance=false -t myapp:latest .

# Verify image
docker pull myapp:latest
```

### **Docker Extensions**

```bash
# Install extension
docker extension install docker/desktop-extension-template

# List extensions
docker extension ls

# Remove extension
docker extension uninstall extension_name
```

## 📚 **RESOURCES & REFERENCES**

### **Official Documentation**
- [Docker Documentation](https://docs.docker.com/)
- [Docker Hub](https://hub.docker.com/)
- [Docker Blog](https://www.docker.com/blog/)

### **Community Resources**
- [Docker Community](https://community.docker.com/)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/docker)
- [Reddit r/docker](https://www.reddit.com/r/docker/)

### **Books & Courses**
- "Docker in Action" by Jeff Nickoloff
- "Using Docker" by Adrian Mouat
- Docker Official Training Courses

### **Tools & Extensions**
- [Docker Desktop](https://www.docker.com/products/docker-desktop)
- [Portainer](https://www.portainer.io/) - Docker management UI
- [Lens](https://k8slens.dev/) - Kubernetes IDE
- [VS Code Docker Extension](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker)

---

## 🎯 **NEXT STEPS**

1. **🧪 Practice**: Build and run your first container
2. **📚 Learn**: Explore Docker Compose for multi-service applications
3. **🚀 Deploy**: Deploy your first containerized application
4. **☸️ Orchestrate**: Learn Kubernetes for container orchestration
5. **🔒 Secure**: Implement security best practices
6. **📊 Monitor**: Set up monitoring and observability

---

> **🐳 This Docker handbook provides comprehensive knowledge for containerizing applications from development to production. Keep learning and practicing to master Docker!**