# ⚡ **SERVICES GUIDE**

> **Complete guide to building and deploying enterprise microservices**

[![Services](https://img.shields.io/badge/Services-Enterprise%20Grade-blue)](SERVICES_GUIDE.md)
[![Templates](https://img.shields.io/badge/Templates-Production%20Ready-green)](SERVICES_GUIDE.md)
[![Patterns](https://img.shields.io/badge/Patterns-Battle%20Tested-yellow)](SERVICES_GUIDE.md)

## 🎯 **OVERVIEW**

This guide provides everything you need to build, deploy, and maintain enterprise-grade microservices using our proven patterns and templates.

## 📋 **TABLE OF CONTENTS**

1. [Service Architecture](#service-architecture)
2. [Technology Stack](#technology-stack)
3. [Service Templates](#service-templates)
4. [Implementation Patterns](#implementation-patterns)
5. [Communication Patterns](#communication-patterns)
6. [Testing Strategy](#testing-strategy)
7. [Deployment Guide](#deployment-guide)

---

## **1. SERVICE ARCHITECTURE**

### **🏛️ Clean Architecture Implementation**

```
service-name/
├── src/
│   ├── domain/              # 🎯 Domain Layer
│   │   ├── entities/        # Business entities with behavior
│   │   ├── value-objects/   # Immutable value objects
│   │   ├── events/          # Domain events
│   │   ├── services/        # Domain services
│   │   └── repositories/    # Repository interfaces
│   │
│   ├── application/         # 🎯 Application Layer
│   │   ├── commands/        # Command handlers (CQRS Write)
│   │   ├── queries/         # Query handlers (CQRS Read)
│   │   ├── services/        # Application services
│   │   ├── dtos/            # Data transfer objects
│   │   └── interfaces/      # Application interfaces
│   │
│   ├── infrastructure/      # 🎯 Infrastructure Layer
│   │   ├── database/        # Database implementations
│   │   ├── external/        # External service clients
│   │   ├── messaging/       # Message broker implementations
│   │   └── config/          # Configuration
│   │
│   └── interface/           # 🎯 Interface Layer
│       ├── controllers/     # REST API controllers
│       ├── middleware/      # Request/response middleware
│       └── validators/      # Input validators
├── tests/                   # Testing
├── Dockerfile              # Container definition
└── README.md               # Service documentation
```

### **🔄 Service Communication**

```typescript
// Event-Driven Communication Pattern
export abstract class DomainEvent {
  public readonly occurredOn: Date;
  public readonly eventId: string;

  constructor() {
    this.occurredOn = new Date();
    this.eventId = crypto.randomUUID();
  }
}

// Example Domain Event
export class UserCreatedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly email: string,
    public readonly name: string
  ) {
    super();
  }
}

// Event Handler
@Injectable()
export class UserCreatedEventHandler {
  @OnEvent('UserCreated')
  async handle(event: UserCreatedEvent): Promise<void> {
    await this.emailService.sendWelcomeEmail(event.email);
    await this.analyticsService.trackUserRegistration(event.userId);
  }
}
```

---

## **2. TECHNOLOGY STACK**

### **🎨 Service Technology Matrix**

| Service Type | Framework | Database | Purpose | Template |
|-------------|-----------|----------|---------|----------|
| **User Management** | NestJS + TypeScript | PostgreSQL + Redis | Authentication, profiles | `nestjs-service` |
| **AI/ML Processing** | FastAPI + Python | Qdrant + Redis | ML operations, embeddings | `fastapi-service` |
| **High Performance** | Go + Gin | Redis + PostgreSQL | Performance-critical APIs | `go-service` |
| **Real-time** | Node.js + WebSocket | Redis + MongoDB | Live notifications | `websocket-service` |

### **🔧 Technology by Service**

```typescript
const ServiceTechnologies = {
  userService: {
    runtime: "Node.js 18+",
    framework: "NestJS + TypeScript",
    database: "PostgreSQL + Redis",
    orm: "TypeORM with migrations",
    auth: "JWT + bcrypt + Passport",
    testing: "Jest + Supertest + TestContainers",
  },

  aiService: {
    runtime: "Python 3.11+",
    framework: "FastAPI + Pydantic",
    mlLibraries: "scikit-learn + transformers + langchain",
    vectorDb: "Qdrant + pgvector",
    testing: "pytest + httpx + factory_boy",
  },

  performanceService: {
    runtime: "Go 1.21+",
    framework: "Gin + Gorilla",
    database: "Redis + PostgreSQL",
    testing: "testify + httptest + dockertest",
  },
};
```

---

## **3. SERVICE TEMPLATES**

### **🔧 NestJS Service Template**

```bash
# Generate NestJS service
./tools/generators/create-service.sh --type=nestjs --name=user-service

# Template includes:
✅ Clean Architecture structure
✅ TypeScript with strict mode
✅ JWT Authentication & RBAC
✅ TypeORM with PostgreSQL & Redis
✅ Swagger/OpenAPI documentation
✅ Jest testing framework
✅ Docker containerization
✅ Health checks & monitoring
```

#### **NestJS Service Structure**

```typescript
// Domain Entity Example
@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  name: string;

  @Column()
  password: string;

  @Column({ default: true })
  isActive: boolean;
}

// Controller Example
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  async create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  async findOne(@Param('id') id: string) {
    return this.userService.findOne(id);
  }
}
```

### **🐍 FastAPI Service Template**

```bash
# Generate FastAPI service
./tools/generators/create-service.sh --type=fastapi --name=ai-service

# Template includes:
✅ FastAPI with Pydantic models
✅ Async/await support
✅ ML library integrations
✅ Vector database (Qdrant)
✅ Auto-generated OpenAPI docs
✅ pytest testing framework
✅ Docker containerization
✅ Background task processing
```

#### **FastAPI Service Structure**

```python
# Domain Model Example
from pydantic import BaseModel
from typing import Optional

class UserCreate(BaseModel):
    email: str
    name: str
    password: str

class UserResponse(BaseModel):
    id: str
    email: str
    name: str
    is_active: bool

# Controller Example
from fastapi import FastAPI, HTTPException, Depends

app = FastAPI(title="AI Service", version="1.0.0")

@app.post("/predict", response_model=PredictionResponse)
async def predict(
    request: PredictionRequest,
    current_user: User = Depends(get_current_user)
):
    try:
        result = await ai_service.predict(request.features)
        return PredictionResponse(
            prediction=result.prediction,
            confidence=result.confidence
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### **🔥 Go Service Template**

```bash
# Generate Go service
./tools/generators/create-service.sh --type=go --name=performance-service

# Template includes:
✅ Gin web framework
✅ GORM for database operations
✅ Redis integration
✅ Structured logging
✅ Graceful shutdown
✅ Docker containerization
✅ Comprehensive testing
```

#### **Go Service Structure**

```go
// Domain Model Example
type User struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    Email     string    `json:"email" gorm:"uniqueIndex"`
    Name      string    `json:"name"`
    Password  string    `json:"-"`
    IsActive  bool      `json:"is_active" gorm:"default:true"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}

// Controller Example
func (h *UserHandler) CreateUser(c *gin.Context) {
    var req CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }

    user, err := h.userService.Create(req)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }

    c.JSON(201, user)
}
```

---

## **4. IMPLEMENTATION PATTERNS**

### **🔐 Authentication & Authorization**

```typescript
// JWT Authentication Guard
@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly userService: UserService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('Token not provided');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token);
      const user = await this.userService.findById(payload.sub);
      
      if (!user || !user.isActive) {
        throw new UnauthorizedException('User not found or inactive');
      }

      request.user = user;
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }
}

// Role-Based Access Control
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) return true;

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some(role => user.roles?.includes(role));
  }
}
```

### **🗄️ Repository Pattern**

```typescript
// Repository Interface
export interface IUserRepository {
  save(user: User): Promise<void>;
  findById(id: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  delete(id: string): Promise<void>;
}

// Repository Implementation
@Injectable()
export class PostgresUserRepository implements IUserRepository {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private cacheService: CacheService
  ) {}

  async save(user: User): Promise<void> {
    await this.userRepository.save(user);
    await this.cacheService.delete(`user:${user.id}`);
  }

  async findById(id: string): Promise<User | null> {
    const cached = await this.cacheService.get(`user:${id}`);
    if (cached) return JSON.parse(cached);

    const user = await this.userRepository.findOne({ where: { id } });
    if (user) {
      await this.cacheService.set(`user:${id}`, JSON.stringify(user), 3600);
    }
    return user;
  }
}
```

### **📊 CQRS Pattern**

```typescript
// Command Pattern
export class CreateUserCommand {
  constructor(
    public readonly email: string,
    public readonly name: string,
    public readonly password: string
  ) {}
}

@CommandHandler(CreateUserCommand)
export class CreateUserHandler implements ICommandHandler<CreateUserCommand> {
  constructor(
    private readonly userRepository: IUserRepository,
    private readonly eventBus: EventBus
  ) {}

  async execute(command: CreateUserCommand): Promise<string> {
    // Business logic and validation
    const user = User.create(command.email, command.name, command.password);
    
    await this.userRepository.save(user);
    
    // Publish domain event
    await this.eventBus.publish(
      new UserCreatedEvent(user.id, user.email, user.name)
    );
    
    return user.id;
  }
}

// Query Pattern
export class GetUserQuery {
  constructor(public readonly userId: string) {}
}

@QueryHandler(GetUserQuery)
export class GetUserHandler implements IQueryHandler<GetUserQuery> {
  constructor(private readonly userRepository: IUserRepository) {}

  async execute(query: GetUserQuery): Promise<User | null> {
    return await this.userRepository.findById(query.userId);
  }
}
```

---

## **5. COMMUNICATION PATTERNS**

### **📨 Event-Driven Communication**

```typescript
// Event Types
interface UserRegisteredEvent {
  eventType: 'user.registered';
  userId: string;
  email: string;
  timestamp: Date;
}

interface TaskCompletedEvent {
  eventType: 'task.completed';
  taskId: string;
  userId: string;
  completedAt: Date;
}

// Event Publishing
@Injectable()
export class EventPublisher {
  constructor(private readonly kafkaClient: KafkaClient) {}

  async publishUserRegistered(userId: string, email: string): Promise<void> {
    const event: UserRegisteredEvent = {
      eventType: 'user.registered',
      userId,
      email,
      timestamp: new Date(),
    };

    await this.kafkaClient.publish('user-events', event);
  }
}

// Event Consumption
@Injectable()
export class NotificationEventHandler {
  @KafkaSubscribe('user-events')
  async handleUserEvents(event: UserRegisteredEvent): Promise<void> {
    if (event.eventType === 'user.registered') {
      await this.emailService.sendWelcomeEmail(event.email);
    }
  }
}
```

### **🔄 Synchronous Communication**

```typescript
// Service-to-Service HTTP Client
@Injectable()
export class UserServiceClient {
  constructor(private readonly httpService: HttpService) {}

  async getUserById(userId: string): Promise<User> {
    try {
      const response = await this.httpService
        .get(`${this.userServiceUrl}/users/${userId}`)
        .toPromise();
      
      return response.data;
    } catch (error) {
      throw new ServiceUnavailableException('User service unavailable');
    }
  }
}

// Circuit Breaker Pattern
@Injectable()
export class CircuitBreakerService {
  private failures = 0;
  private lastFailureTime?: Date;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  async call<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (this.shouldAttemptReset()) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
}
```

---

## **6. TESTING STRATEGY**

### **📊 Testing Pyramid**

```
E2E Tests (10%)     ← Service integration tests
Integration (20%)   ← Database + external services  
Unit Tests (70%)    ← Domain logic + use cases
```

### **🧪 Testing Implementation**

```typescript
// Unit Test Example
describe('UserService', () => {
  let service: UserService;
  let repository: jest.Mocked<IUserRepository>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: 'IUserRepository',
          useValue: createMockRepository(),
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get('IUserRepository');
  });

  it('should create a user successfully', async () => {
    const userData = {
      email: '<EMAIL>',
      name: 'Test User',
      password: 'password123',
    };

    repository.findByEmail.mockResolvedValue(null);
    repository.save.mockResolvedValue(undefined);

    const result = await service.createUser(userData);

    expect(result).toBeDefined();
    expect(repository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        email: userData.email,
        name: userData.name,
      })
    );
  });
});

// Integration Test Example
describe('UserController (Integration)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/users (POST)', () => {
    return request(app.getHttpServer())
      .post('/users')
      .send({
        email: '<EMAIL>',
        name: 'Test User',
        password: 'password123',
      })
      .expect(201)
      .expect((res) => {
        expect(res.body.id).toBeDefined();
        expect(res.body.email).toBe('<EMAIL>');
      });
  });
});
```

### **📋 Test Commands**

```bash
# Run all tests
npm test

# Run specific test types
npm run test:unit           # Unit tests
npm run test:integration    # Integration tests
npm run test:e2e           # End-to-end tests

# Run with coverage
npm run test:cov

# Run performance tests
npm run test:performance

# Run tests in watch mode
npm run test:watch
```

---

## **7. DEPLOYMENT GUIDE**

### **🐳 Docker Deployment**

```dockerfile
# Multi-stage Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS production
WORKDIR /app

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy application
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

USER nextjs
EXPOSE 3000
CMD ["npm", "start"]
```

### **☸️ Kubernetes Deployment**

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: enterprise-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: enterprise-platform/user-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### **🚀 CI/CD Pipeline**

```bash
# Deployment commands
npm run deploy:staging      # Deploy to staging
npm run deploy:production   # Deploy to production

# Health checks
npm run health-check        # Check service health
npm run smoke-test         # Run smoke tests

# Monitoring
npm run logs               # View application logs
npm run metrics           # View service metrics
```

---

## **📚 QUICK REFERENCE**

### **🔧 Service Generation**

```bash
# Generate new service
./tools/generators/create-service.sh my-service

# Available templates:
- nestjs-service    (TypeScript)
- fastapi-service   (Python)
- go-service        (Go)
- websocket-service (Node.js + WebSocket)
```

### **📊 Service Endpoints**

| Service | Local URL | Health Check | Documentation |
|---------|-----------|-------------|---------------|
| User Service | http://localhost:3001 | `/health` | `/docs` |
| AI Service | http://localhost:8000 | `/health` | `/docs` |
| Performance Service | http://localhost:3003 | `/health` | `/docs` |

### **🔍 Troubleshooting**

```bash
# Check service logs
docker-compose logs user-service

# Debug service
npm run debug:user-service

# Reset service
docker-compose restart user-service
```

---

## **🎯 NEXT STEPS**

1. **📖 Read Architecture Guide**: [ARCHITECTURE.md](ARCHITECTURE.md)
2. **🚀 Follow Quick Start**: [QUICK_START.md](QUICK_START.md)
3. **📋 Check API Standards**: [API_STANDARDS.md](API_STANDARDS.md)
4. **💡 Explore Examples**: [examples/](examples/)

---

> **🎯 This services guide provides everything you need to build scalable, maintainable microservices. Use these patterns and templates to accelerate your development while maintaining enterprise standards!**