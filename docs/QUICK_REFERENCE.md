# Quick Reference (Canonical)

Purpose
- Centralized quick tables for tasks, learning topics, and templates.
- Link-first design; details live in canonical docs.

1) Setup & Development
| Task                 | Documentation                                 | Time      |
| -------------------- | ---------------------------------------------- | --------- |
| Environment setup    | core/getting-started/README.md                 | 30 min    |
| Project creation     | templates/README.md                            | 15 min    |
| Development workflow | guides/workflow/README.md                      | As needed |
| Deployment           | guides/deployment/README.md                    | 45 min    |

2) Learning & Reference
| Topic                | Documentation                                            | Focus               |
| -------------------- | -------------------------------------------------------- | ------------------- |
| Programming basics   | reference/knowledge/fundamentals/README.md              | Core concepts       |
| Architecture patterns| core/architecture/README.md                              | System design       |
| Best practices       | reference/standards/README.md                            | Quality & standards |
| Troubleshooting      | resources/README.md                                      | Problem solving     |

3) Project Creation (Templates)
| Task             | Template                                        | Time  |
| ---------------- | ----------------------------------------------- | ----- |
| Web application  | templates/projects/fullstack-webapp/            | 5 min |
| Microservice     | templates/projects/microservice/                | 10 min|
| API service      | templates/projects/api-service/                  | 8 min |
| Mobile app       | templates/projects/mobile-app/                   | 15 min|

4) Configuration Setup (Templates)
| Task                  | Template                              | Time  |
| --------------------- | ------------------------------------- | ----- |
| Docker setup          | templates/configs/docker/             | 10 min|
| Kubernetes deployment | templates/configs/kubernetes/         | 20 min|
| Infrastructure setup  | templates/configs/terraform/          | 30 min|
| CI/CD pipeline        | templates/configs/ci-cd/              | 15 min|

5) Code Examples (Templates)
| Pattern              | Example                                  | Complexity |
| -------------------- | ---------------------------------------- | ---------- |
| Repository pattern   | templates/examples/README.md#repository  | Medium     |
| CQRS implementation  | templates/examples/README.md#cqrs        | High       |
| Testing strategies   | templates/examples/README.md#testing     | Medium     |
| Security patterns    | templates/examples/README.md#security    | High       |

Notes
- For full API patterns and standards see reference/api/README.md
- For learning tracks see docs/LEARNING_PATHS.md

