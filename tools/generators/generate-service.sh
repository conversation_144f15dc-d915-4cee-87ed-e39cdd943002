#!/bin/bash

# 🔧 Service Generator Script
# Generate new microservices from templates

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Emojis
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
GEAR="⚙️"
SPARKLES="✨"

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
TEMPLATES_DIR="$PROJECT_ROOT/templates/service"
SERVICES_DIR="$PROJECT_ROOT/services"

# Default values
SERVICE_TYPE=""
SERVICE_NAME=""
SERVICE_PORT=""
AUTHOR="$(git config user.name 2>/dev/null || echo "Developer")"
EMAIL="$(git config user.email 2>/dev/null || echo "<EMAIL>")"

# Function to print colored output
print_status() {
    local color=$1
    local emoji=$2
    local message=$3
    echo -e "${color}${emoji} ${message}${NC}"
}

print_success() { print_status "$GREEN" "$CHECK" "$1"; }
print_error() { print_status "$RED" "$CROSS" "$1"; }
print_warning() { print_status "$YELLOW" "⚠️" "$1"; }
print_info() { print_status "$BLUE" "ℹ️" "$1"; }
print_header() { print_status "$PURPLE" "$ROCKET" "$1"; }

# Function to show usage
show_usage() {
    echo "🔧 Service Generator"
    echo ""
    echo "Usage: $0 --type=<type> --name=<name> [options]"
    echo ""
    echo "Required:"
    echo "  --type=<type>     Service type (nestjs, fastapi, go, rust)"
    echo "  --name=<name>     Service name (kebab-case)"
    echo ""
    echo "Optional:"
    echo "  --port=<port>     Service port (auto-assigned if not specified)"
    echo "  --author=<name>   Author name (from git config if not specified)"
    echo "  --email=<email>   Author email (from git config if not specified)"
    echo "  --help, -h        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --type=nestjs --name=user-service"
    echo "  $0 --type=fastapi --name=ai-service --port=8001"
    echo "  $0 --type=go --name=performance-service --port=8002"
    echo ""
    echo "Available service types:"
    echo "  nestjs    - NestJS TypeScript service"
    echo "  fastapi   - FastAPI Python service"
    echo "  go        - Go service with Gin framework"
    echo "  rust      - Rust service with Axum framework"
    echo ""
}

# Function to validate service name
validate_service_name() {
    local name=$1

    # Check if name is provided
    if [ -z "$name" ]; then
        print_error "Service name is required"
        return 1
    fi

    # Check if name follows kebab-case convention
    if ! echo "$name" | grep -qE '^[a-z][a-z0-9-]*[a-z0-9]$'; then
        print_error "Service name must be in kebab-case (e.g., user-service, ai-service)"
        return 1
    fi

    # Check if service already exists
    if [ -d "$SERVICES_DIR/$name" ]; then
        print_error "Service '$name' already exists in $SERVICES_DIR"
        return 1
    fi

    return 0
}

# Function to validate service type
validate_service_type() {
    local type=$1
    local valid_types=("nestjs" "fastapi" "go" "rust")

    if [ -z "$type" ]; then
        print_error "Service type is required"
        return 1
    fi

    for valid_type in "${valid_types[@]}"; do
        if [ "$type" = "$valid_type" ]; then
            return 0
        fi
    done

    print_error "Invalid service type: $type"
    print_info "Valid types: ${valid_types[*]}"
    return 1
}

# Function to get next available port
get_next_port() {
    local base_port=3001
    local max_port=3999

    for ((port=base_port; port<=max_port; port++)); do
        if ! netstat -tuln 2>/dev/null | grep -q ":$port "; then
            echo "$port"
            return 0
        fi
    done

    # If no port found in range, return a high port
    echo "8000"
}

# Function to replace template variables
replace_template_variables() {
    local file=$1
    local service_name=$2
    local service_port=$3
    local author=$4
    local email=$5

    # Convert service name to different cases
    local service_name_pascal=$(echo "$service_name" | sed -r 's/(^|-)([a-z])/\U\2/g')
    local service_name_camel=$(echo "$service_name_pascal" | sed 's/^./\l&/')
    local service_name_upper=$(echo "$service_name" | tr '[:lower:]' '[:upper:]' | tr '-' '_')
    local service_name_title=$(echo "$service_name" | sed 's/-/ /g' | sed 's/\b\w/\U&/g')

    # Replace variables in file
    sed -i.bak \
        -e "s/{{SERVICE_NAME}}/$service_name/g" \
        -e "s/{{SERVICE_NAME_PASCAL}}/$service_name_pascal/g" \
        -e "s/{{SERVICE_NAME_CAMEL}}/$service_name_camel/g" \
        -e "s/{{SERVICE_NAME_UPPER}}/$service_name_upper/g" \
        -e "s/{{SERVICE_NAME_TITLE}}/$service_name_title/g" \
        -e "s/{{SERVICE_PORT}}/$service_port/g" \
        -e "s/{{AUTHOR}}/$author/g" \
        -e "s/{{EMAIL}}/$email/g" \
        -e "s/{{DATE}}/$(date +%Y-%m-%d)/g" \
        -e "s/{{YEAR}}/$(date +%Y)/g" \
        "$file"

    # Remove backup file
    rm -f "${file}.bak"
}

# Function to process template directory
process_template_directory() {
    local template_dir=$1
    local target_dir=$2
    local service_name=$3
    local service_port=$4
    local author=$5
    local email=$6

    # Copy template directory
    cp -r "$template_dir" "$target_dir"

    # Process all files in the target directory
    find "$target_dir" -type f -name "*.md" -o -name "*.json" -o -name "*.ts" -o -name "*.py" -o -name "*.go" -o -name "*.rs" -o -name "*.yml" -o -name "*.yaml" -o -name "*.toml" -o -name "*.env*" -o -name "Dockerfile*" | while read -r file; do
        replace_template_variables "$file" "$service_name" "$service_port" "$author" "$email"
    done

    # Make scripts executable
    find "$target_dir" -name "*.sh" -exec chmod +x {} \;
}

# Function to generate service
generate_service() {
    local type=$1
    local name=$2
    local port=$3
    local author=$4
    local email=$5

    print_header "Generating $type service: $name"

    # Validate inputs
    validate_service_type "$type" || return 1
    validate_service_name "$name" || return 1

    # Check if template exists
    local template_dir="$TEMPLATES_DIR/${type}-service"
    if [ ! -d "$template_dir" ]; then
        print_error "Template not found: $template_dir"
        print_info "Available templates:"
        ls -1 "$TEMPLATES_DIR" | grep -E ".*-service$" | sed 's/-service$//' | sed 's/^/  /'
        return 1
    fi

    # Assign port if not provided
    if [ -z "$port" ]; then
        port=$(get_next_port)
        print_info "Auto-assigned port: $port"
    fi

    # Create target directory
    local target_dir="$SERVICES_DIR/$name"
    mkdir -p "$target_dir"

    # Process template
    print_info "Processing template..."
    process_template_directory "$template_dir/template" "$target_dir" "$name" "$port" "$author" "$email"

    # Install dependencies if package.json exists
    if [ -f "$target_dir/package.json" ]; then
        print_info "Installing dependencies..."
        cd "$target_dir"
        npm install
        cd "$PROJECT_ROOT"
    fi

    # Install Python dependencies if requirements.txt exists
    if [ -f "$target_dir/requirements.txt" ]; then
        print_info "Installing Python dependencies..."
        cd "$target_dir"
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
        cd "$PROJECT_ROOT"
    fi

    print_success "Service '$name' generated successfully!"
    echo ""
    print_info "Service details:"
    echo "  📁 Location: $target_dir"
    echo "  🌐 Port: $port"
    echo "  🔧 Type: $type"
    echo "  👤 Author: $author"
    echo ""
    print_info "Next steps:"
    echo "  1. cd services/$name"
    echo "  2. Review and update configuration files"
    echo "  3. Start development: npm run start:dev (or equivalent)"
    echo "  4. Run tests: npm test (or equivalent)"
    echo ""
}

# Function to update docker-compose.yml
update_docker_compose() {
    local service_name=$1
    local service_port=$2
    local service_type=$3

    local compose_file="$PROJECT_ROOT/docker-compose.yml"

    if [ -f "$compose_file" ]; then
        print_info "Updating docker-compose.yml..."

        # Create backup
        cp "$compose_file" "${compose_file}.bak"

        # Add service to docker-compose.yml (simplified - in real implementation, use proper YAML parsing)
        cat >> "$compose_file" << EOF

  $service_name:
    build:
      context: ./services/$service_name
      dockerfile: Dockerfile
    ports:
      - "$service_port:$service_port"
    environment:
      - NODE_ENV=development
      - PORT=$service_port
    depends_on:
      - postgres
      - redis
    networks:
      - enterprise-network
EOF

        print_success "Docker Compose configuration updated"
    else
        print_warning "docker-compose.yml not found, skipping Docker configuration"
    fi
}

# Function to update root package.json scripts
update_root_package_json() {
    local service_name=$1

    local package_file="$PROJECT_ROOT/package.json"

    if [ -f "$package_file" ]; then
        print_info "Updating root package.json scripts..."

        # Add service-specific scripts (simplified - in real implementation, use proper JSON parsing)
        # This would require jq or similar tool for proper JSON manipulation
        print_info "Please manually add the following scripts to your root package.json:"
        echo "  \"$service_name:dev\": \"cd services/$service_name && npm run start:dev\","
        echo "  \"$service_name:build\": \"cd services/$service_name && npm run build\","
        echo "  \"$service_name:test\": \"cd services/$service_name && npm test\","
    fi
}

# Function to create service documentation
create_service_docs() {
    local service_name=$1
    local service_type=$2
    local service_port=$3

    local docs_dir="$PROJECT_ROOT/docs/services"
    mkdir -p "$docs_dir"

    local service_doc="$docs_dir/$service_name.md"

    cat > "$service_doc" << EOF
# $service_name Service

> Generated on $(date)

## Overview

This is a $service_type service generated from the enterprise platform template.

## Configuration

- **Port**: $service_port
- **Type**: $service_type
- **Location**: \`services/$service_name\`

## Development

\`\`\`bash
# Navigate to service directory
cd services/$service_name

# Install dependencies
npm install

# Start development server
npm run start:dev

# Run tests
npm test

# Build for production
npm run build
\`\`\`

## API Documentation

The service API documentation is available at:
- Development: http://localhost:$service_port/docs
- Swagger UI: http://localhost:$service_port/swagger

## Health Check

- Health endpoint: http://localhost:$service_port/health
- Metrics endpoint: http://localhost:$service_port/metrics

## Docker

\`\`\`bash
# Build Docker image
docker build -t $service_name ./services/$service_name

# Run container
docker run -p $service_port:$service_port $service_name

# Using Docker Compose
docker-compose up $service_name
\`\`\`

## Testing

\`\`\`bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e

# Coverage report
npm run test:coverage
\`\`\`

## Deployment

See the main deployment documentation for details on deploying this service to different environments.

## Monitoring

The service includes built-in monitoring and observability features:
- Structured logging
- Prometheus metrics
- Health checks
- Distributed tracing
EOF

    print_success "Service documentation created: $service_doc"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --type=*)
            SERVICE_TYPE="${1#*=}"
            shift
            ;;
        --name=*)
            SERVICE_NAME="${1#*=}"
            shift
            ;;
        --port=*)
            SERVICE_PORT="${1#*=}"
            shift
            ;;
        --author=*)
            AUTHOR="${1#*=}"
            shift
            ;;
        --email=*)
            EMAIL="${1#*=}"
            shift
            ;;
        --help|-h)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Validate required arguments
if [ -z "$SERVICE_TYPE" ] || [ -z "$SERVICE_NAME" ]; then
    print_error "Both --type and --name are required"
    echo ""
    show_usage
    exit 1
fi

# Main execution
main() {
    print_header "Enterprise Service Generator"
    echo ""

    # Generate the service
    generate_service "$SERVICE_TYPE" "$SERVICE_NAME" "$SERVICE_PORT" "$AUTHOR" "$EMAIL"

    # Update project configuration
    update_docker_compose "$SERVICE_NAME" "$SERVICE_PORT" "$SERVICE_TYPE"
    update_root_package_json "$SERVICE_NAME"
    create_service_docs "$SERVICE_NAME" "$SERVICE_TYPE" "$SERVICE_PORT"

    echo ""
    print_success "Service generation completed! 🎉"
    echo ""
    print_info "📋 Summary:"
    echo "  ✅ Service '$SERVICE_NAME' created"
    echo "  ✅ Dependencies installed"
    echo "  ✅ Docker configuration updated"
    echo "  ✅ Documentation created"
    echo ""
    print_info "🚀 Quick start:"
    echo "  cd services/$SERVICE_NAME"
    echo "  npm run start:dev"
    echo ""
    print_info "📖 Documentation: docs/services/$SERVICE_NAME.md"
    echo ""
}

# Run main function
main