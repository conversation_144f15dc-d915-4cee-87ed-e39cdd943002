# 🔧 **Development Tools - Automation & Code Generation**

> **Comprehensive development tools including code generators, linters, scripts, and automation utilities**

## 📋 **Overview**

The `tools/` directory contains all **development tools and utilities** that enhance developer productivity, maintain code quality, and automate repetitive tasks. These tools follow enterprise standards and integrate seamlessly with the development workflow.

## 🏗️ **Architecture Overview**

```
tools/
├── generators/          # 🏭 Code Generators & Templates
├── linters/            # 📏 Code Quality & Style Enforcement
└── scripts/            # 🤖 Automation Scripts & Utilities
```

## 📁 **Tools Directory & Guides**

| Tool Category | Technology Stack | Purpose | README Guide | Status |
|---------------|------------------|---------|--------------|--------|
| [🏭 **generators/**](generators/README.md) | Node.js + Templates | Code generation, scaffolding | [📖 Guide](generators/README.md) | 🔄 |
| [📏 **linters/**](linters/README.md) | ESLint + Prettier + Custom rules | Code quality enforcement | [📖 Guide](linters/README.md) | 🔄 |
| [🤖 **scripts/**](scripts/README.md) | Bash + Node.js + Python | Automation and utilities | [📖 Guide](scripts/README.md) | 🔄 |

## 🏭 **Code Generators**

**Automated code generation** for consistent development:

### **🔧 Key Features**
- ✅ **Service Generation** - Complete microservice scaffolding
- ✅ **Component Generation** - React/Vue component templates
- ✅ **API Generation** - REST/GraphQL API boilerplate
- ✅ **Database Generation** - Schema and migration generation
- ✅ **Test Generation** - Automated test file creation
- ✅ **Documentation Generation** - Auto-generated documentation

### **📁 Structure**
```
generators/
├── service/             # Service generators
│   ├── nestjs-service/
│   ├── fastapi-service/
│   └── gin-service/
├── component/           # Component generators
│   ├── react-component/
│   ├── vue-component/
│   └── angular-component/
├── api/                 # API generators
│   ├── rest-api/
│   ├── graphql-api/
│   └── grpc-api/
├── database/            # Database generators
│   ├── migration/
│   ├── model/
│   └── seed/
├── test/                # Test generators
│   ├── unit-test/
│   ├── integration-test/
│   └── e2e-test/
├── docs/                # Documentation generators
│   ├── api-docs/
│   ├── readme/
│   └── changelog/
└── templates/           # Base templates
    ├── typescript/
    ├── python/
    └── go/
```

### **🚀 Generator Usage**
```bash
# Generate new microservice
./tools/generators/create-service.sh user-management nestjs

# Generate React component
./tools/generators/create-component.sh UserProfile react

# Generate API endpoint
./tools/generators/create-api.sh users rest

# Generate database migration
./tools/generators/create-migration.sh add_user_roles

# Generate test suite
./tools/generators/create-test.sh UserService unit
```

### **🏗️ Service Generator Example**
```bash
#!/bin/bash
# create-service.sh

SERVICE_NAME=$1
SERVICE_TYPE=${2:-nestjs}
SERVICE_DIR="services/$SERVICE_NAME"

echo "🏭 Generating $SERVICE_TYPE service: $SERVICE_NAME"

# Create service directory structure
mkdir -p "$SERVICE_DIR"/{src/{domain,application,infrastructure,interface},test,docs}

# Copy template files
cp -r "tools/generators/service/$SERVICE_TYPE-service/template/"* "$SERVICE_DIR/"

# Replace placeholders
find "$SERVICE_DIR" -type f -name "*.ts" -o -name "*.json" -o -name "*.md" | \
    xargs sed -i "s/{{SERVICE_NAME}}/$SERVICE_NAME/g"

# Initialize package.json
cd "$SERVICE_DIR"
npm init -y
npm install

echo "✅ Service $SERVICE_NAME generated successfully!"
echo "📁 Location: $SERVICE_DIR"
echo "🚀 Next steps:"
echo "   cd $SERVICE_DIR"
echo "   npm run dev"
```

## 📏 **Code Quality & Linting**

**Automated code quality enforcement**:

### **🔧 Key Features**
- ✅ **ESLint Configuration** - TypeScript/JavaScript linting
- ✅ **Prettier Integration** - Consistent code formatting
- ✅ **Custom Rules** - Enterprise-specific linting rules
- ✅ **Pre-commit Hooks** - Automated quality checks
- ✅ **CI/CD Integration** - Quality gates in pipelines
- ✅ **Multi-language Support** - Python, Go, TypeScript linting

### **📁 Structure**
```
linters/
├── eslint/              # ESLint configurations
│   ├── base.js
│   ├── typescript.js
│   ├── react.js
│   └── node.js
├── prettier/            # Prettier configurations
│   ├── .prettierrc.js
│   └── .prettierignore
├── custom-rules/        # Custom linting rules
│   ├── enterprise-rules.js
│   ├── security-rules.js
│   └── performance-rules.js
├── python/              # Python linting
│   ├── .flake8
│   ├── .pylintrc
│   └── pyproject.toml
├── go/                  # Go linting
│   ├── .golangci.yml
│   └── gofmt.sh
└── hooks/               # Git hooks
    ├── pre-commit
    ├── pre-push
    └── commit-msg
```

### **⚙️ ESLint Configuration**
```javascript
// eslint/typescript.js
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    '@typescript-eslint/recommended-requiring-type-checking',
    'prettier',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: './tsconfig.json',
    tsconfigRootDir: __dirname,
  },
  plugins: ['@typescript-eslint', 'import', 'security'],
  rules: {
    // TypeScript specific rules
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-explicit-any': 'error',
    
    // Import rules
    'import/order': ['error', {
      'groups': ['builtin', 'external', 'internal', 'parent', 'sibling'],
      'newlines-between': 'always',
    }],
    
    // Security rules
    'security/detect-object-injection': 'error',
    'security/detect-sql-injection': 'error',
    
    // Custom enterprise rules
    'enterprise/no-console-log': 'error',
    'enterprise/require-error-handling': 'error',
  },
};
```

### **🎨 Prettier Configuration**
```javascript
// .prettierrc.js
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 80,
  tabWidth: 2,
  useTabs: false,
  bracketSpacing: true,
  arrowParens: 'avoid',
  endOfLine: 'lf',
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 120,
      },
    },
    {
      files: '*.md',
      options: {
        proseWrap: 'always',
      },
    },
  ],
};
```

### **🔧 Pre-commit Hook**
```bash
#!/bin/bash
# hooks/pre-commit

set -e

echo "🔍 Running pre-commit checks..."

# Run linting
echo "📏 Linting code..."
npm run lint

# Run type checking
echo "🔍 Type checking..."
npm run type-check

# Run tests
echo "🧪 Running tests..."
npm run test

# Check formatting
echo "🎨 Checking code formatting..."
npm run format:check

# Security audit
echo "🔒 Security audit..."
npm audit --audit-level moderate

echo "✅ All pre-commit checks passed!"
```

## 🤖 **Automation Scripts**

**Development automation** and utility scripts:

### **🔧 Key Features**
- ✅ **Environment Setup** - Automated development environment setup
- ✅ **Database Management** - Database operations automation
- ✅ **Deployment Scripts** - Automated deployment procedures
- ✅ **Monitoring Scripts** - Health checks and monitoring
- ✅ **Maintenance Scripts** - Cleanup and maintenance tasks
- ✅ **Performance Scripts** - Performance testing and optimization

### **📁 Structure**
```
scripts/
├── setup/               # Environment setup scripts
│   ├── install-dependencies.sh
│   ├── setup-database.sh
│   └── configure-environment.sh
├── database/            # Database management
│   ├── migrate.sh
│   ├── seed.sh
│   ├── backup.sh
│   └── restore.sh
├── deployment/          # Deployment automation
│   ├── deploy-staging.sh
│   ├── deploy-production.sh
│   └── rollback.sh
├── monitoring/          # Monitoring and health checks
│   ├── health-check.sh
│   ├── performance-test.sh
│   └── log-analysis.sh
├── maintenance/         # Maintenance tasks
│   ├── cleanup.sh
│   ├── optimize.sh
│   └── update-dependencies.sh
└── utilities/           # General utilities
    ├── generate-ssl.sh
    ├── create-user.sh
    └── export-data.sh
```

### **🚀 Setup Script Example**
```bash
#!/bin/bash
# setup/install-dependencies.sh

set -euo pipefail

echo "🚀 Setting up development environment..."

# Check system requirements
check_requirements() {
    echo "🔍 Checking system requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js is required but not installed"
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is required but not installed"
        exit 1
    fi
    
    echo "✅ System requirements met"
}

# Install dependencies
install_dependencies() {
    echo "📦 Installing dependencies..."
    
    # Install root dependencies
    npm install
    
    # Install app dependencies
    for app in apps/*/; do
        if [ -f "$app/package.json" ]; then
            echo "Installing dependencies for $app"
            (cd "$app" && npm install)
        fi
    done
    
    # Install service dependencies
    for service in services/*/; do
        if [ -f "$service/package.json" ]; then
            echo "Installing dependencies for $service"
            (cd "$service" && npm install)
        fi
    done
    
    echo "✅ Dependencies installed"
}

# Setup database
setup_database() {
    echo "🗄️ Setting up database..."
    
    # Start database containers
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    echo "⏳ Waiting for database to be ready..."
    sleep 10
    
    # Run migrations
    npm run migrate
    
    # Seed database
    npm run seed:dev
    
    echo "✅ Database setup complete"
}

# Setup environment
setup_environment() {
    echo "⚙️ Setting up environment..."
    
    # Copy environment files
    if [ ! -f .env ]; then
        cp .env.example .env
        echo "📝 Created .env file from template"
    fi
    
    # Generate JWT secret if not exists
    if ! grep -q "JWT_SECRET=" .env; then
        JWT_SECRET=$(openssl rand -base64 32)
        echo "JWT_SECRET=$JWT_SECRET" >> .env
        echo "🔑 Generated JWT secret"
    fi
    
    echo "✅ Environment setup complete"
}

# Main setup function
main() {
    check_requirements
    install_dependencies
    setup_database
    setup_environment
    
    echo "🎉 Development environment setup complete!"
    echo "🚀 Run 'npm run dev' to start the development server"
}

main "$@"
```

### **📊 Performance Testing Script**
```bash
#!/bin/bash
# monitoring/performance-test.sh

set -euo pipefail

API_URL=${API_URL:-http://localhost:3000}
CONCURRENT_USERS=${CONCURRENT_USERS:-10}
DURATION=${DURATION:-60}
OUTPUT_DIR=${OUTPUT_DIR:-./performance-results}

echo "🚀 Running performance tests..."
echo "API URL: $API_URL"
echo "Concurrent Users: $CONCURRENT_USERS"
echo "Duration: ${DURATION}s"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Run load test with Artillery
artillery run \
    --target "$API_URL" \
    --output "$OUTPUT_DIR/results.json" \
    tools/scripts/performance/load-test.yml

# Generate HTML report
artillery report \
    "$OUTPUT_DIR/results.json" \
    --output "$OUTPUT_DIR/report.html"

# Extract key metrics
node tools/scripts/performance/extract-metrics.js \
    "$OUTPUT_DIR/results.json" \
    > "$OUTPUT_DIR/metrics.txt"

echo "✅ Performance test complete"
echo "📊 Results: $OUTPUT_DIR/report.html"
echo "📈 Metrics: $OUTPUT_DIR/metrics.txt"
```

## 🔧 **Tool Configuration**

### **📋 Package.json Scripts**
```json
{
  "scripts": {
    "generate:service": "./tools/generators/create-service.sh",
    "generate:component": "./tools/generators/create-component.sh",
    "lint": "eslint . --ext .ts,.js --config tools/linters/eslint/typescript.js",
    "lint:fix": "npm run lint -- --fix",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "type-check": "tsc --noEmit",
    "setup": "./tools/scripts/setup/install-dependencies.sh",
    "db:migrate": "./tools/scripts/database/migrate.sh",
    "db:seed": "./tools/scripts/database/seed.sh",
    "test:performance": "./tools/scripts/monitoring/performance-test.sh",
    "deploy:staging": "./tools/scripts/deployment/deploy-staging.sh"
  }
}
```

### **⚙️ Tool Integration**
```yaml
# .github/workflows/quality.yml
name: Code Quality
on: [push, pull_request]

jobs:
  quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run linting
        run: npm run lint
        
      - name: Check formatting
        run: npm run format:check
        
      - name: Type checking
        run: npm run type-check
        
      - name: Run tests
        run: npm test
        
      - name: Security audit
        run: npm audit --audit-level moderate
```

## 🧪 **Tool Testing**

### **🔬 Testing Strategy**
```bash
# Test generators
npm run test:generators

# Test linting rules
npm run test:linters

# Test automation scripts
npm run test:scripts

# Integration testing
npm run test:tools:integration
```

### **📊 Tool Metrics**
- ✅ **Code Generation Speed** - Time to generate components
- ✅ **Linting Performance** - Linting execution time
- ✅ **Script Reliability** - Script success rate
- ✅ **Developer Productivity** - Time saved through automation
- ✅ **Code Quality Improvement** - Quality metrics over time

## 🔗 **Related Documentation**

- [🏗️ Project Structure](../docs/PROJECT_STRUCTURE.md)
- [⚡ Services Layer](../services/README.md)
- [🎯 Applications Layer](../apps/README.md)
- [🧪 Testing Guide](../tests/README.md)
- [📝 Templates](../templates/README.md)

## 🤝 **Contributing**

1. **Tool Development** - Create new development tools
2. **Generator Templates** - Add new code generation templates
3. **Linting Rules** - Enhance code quality rules
4. **Script Automation** - Automate repetitive tasks
5. **Documentation** - Document tool usage and configuration

---

> **Next Steps**: Explore individual tool category READMEs for detailed usage guides and start automating your development workflow.
