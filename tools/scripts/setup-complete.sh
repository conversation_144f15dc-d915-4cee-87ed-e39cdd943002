#!/bin/bash
# 🏗️ Enterprise Platform Complete Setup Script
# Usage: ./tools/scripts/setup-complete.sh

set -euo pipefail

# 🎨 Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 📝 Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# 🎯 Main setup function
main() {
    log "🚀 Starting Enterprise Platform Complete Setup..."
    
    # Phase 1: Environment Validation
    log "📋 Phase 1: Environment Validation"
    validate_environment
    
    # Phase 2: Create Environment File
    log "🔧 Phase 2: Environment Configuration"
    create_env_file
    
    # Phase 3: Create Docker Compose
    log "🐳 Phase 3: Docker Compose Setup"
    create_docker_compose
    
    # Phase 4: Core Infrastructure
    log "🏗️ Phase 4: Core Infrastructure Setup"
    setup_infrastructure
    
    # Phase 5: Application Services
    log "⚡ Phase 5: Application Services"
    setup_application_services
    
    # Phase 6: Monitoring & Observability
    log "📊 Phase 6: Monitoring Stack"
    setup_monitoring
    
    # Phase 7: Validation & Health Checks
    log "✅ Phase 7: Validation"
    validate_deployment
    
    log "🎉 Setup Complete! Your enterprise platform is ready!"
    show_access_info
}

# 🔍 Environment validation
validate_environment() {
    log "Validating prerequisites..."
    
    # Check required commands
    for cmd in docker node python3 git; do
        if ! command -v $cmd &> /dev/null; then
            error "$cmd is required but not installed. Please install it first."
        fi
        log "✅ Found $cmd: $(command -v $cmd)"
    done
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running. Please start Docker first."
    fi
    
    # Check available disk space (minimum 5GB)
    available_space=$(df . | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 5242880 ]; then
        warn "Low disk space. At least 5GB recommended"
    fi
    
    log "✅ Environment validation complete"
}

# 🔧 Create environment file
create_env_file() {
    if [ ! -f ".env" ]; then
        log "Creating .env file..."
        cat > .env << 'EOF'
# 🌍 Environment Configuration
NODE_ENV=development
TZ=UTC

# 🔗 Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=enterprise_platform
POSTGRES_USER=enterprise_user
POSTGRES_PASSWORD=secure_password_123

# 📝 Redis Configuration  
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password_123

# 🔒 Security Configuration
JWT_SECRET=super_secure_jwt_secret_key_256_bits_here
ENCRYPTION_KEY=32_byte_encryption_key_here_123456

# 📊 Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_PASSWORD=admin123

# 🌐 API Configuration
API_GATEWAY_PORT=3000
AI_SERVICE_PORT=8000
EOF
        log "✅ .env file created"
    else
        log "✅ .env file already exists"
    fi
}

# 🐳 Create comprehensive docker-compose
create_docker_compose() {
    if [ ! -f "docker-compose.enterprise.yml" ]; then
        log "Creating comprehensive docker-compose.enterprise.yml..."
        cat > docker-compose.enterprise.yml << 'EOF'
version: '3.8'

services:
  # 🗄️ Database Services
  postgres:
    image: postgres:15-alpine
    container_name: enterprise-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./data/databases/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: enterprise-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 🌐 API Gateway
  api-gateway:
    build:
      context: ./apps/api-gateway
      dockerfile: Dockerfile
    container_name: enterprise-api-gateway
    ports:
      - "${API_GATEWAY_PORT}:3000"
    environment:
      - NODE_ENV=${NODE_ENV}
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 🤖 AI Service
  ai-service:
    build:
      context: ./services/ai-service
      dockerfile: Dockerfile
    container_name: enterprise-ai-service
    ports:
      - "${AI_SERVICE_PORT}:8000"
    environment:
      - ENVIRONMENT=${NODE_ENV}
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 📊 Monitoring Services
  prometheus:
    image: prom/prometheus:latest
    container_name: enterprise-prometheus
    ports:
      - "${PROMETHEUS_PORT}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - enterprise-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: enterprise-grafana
    ports:
      - "${GRAFANA_PORT}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - enterprise-network
    restart: unless-stopped

  # 🔄 Load Balancer
  nginx:
    image: nginx:alpine
    container_name: enterprise-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api-gateway
      - ai-service
    networks:
      - enterprise-network
    restart: unless-stopped

networks:
  enterprise-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
EOF
        log "✅ docker-compose.enterprise.yml created"
    else
        log "✅ docker-compose.enterprise.yml already exists"
    fi
}

# 🏗️ Infrastructure setup
setup_infrastructure() {
    log "Setting up core infrastructure..."
    
    # Create network
    docker network create enterprise-network 2>/dev/null || true
    
    # Create volumes
    docker volume create postgres_data 2>/dev/null || true
    docker volume create redis_data 2>/dev/null || true
    docker volume create prometheus_data 2>/dev/null || true
    docker volume create grafana_data 2>/dev/null || true
    
    # Start database services
    log "Starting database services..."
    docker-compose -f docker-compose.enterprise.yml up -d postgres redis
    
    # Wait for databases to be ready
    log "Waiting for databases to be ready..."
    wait_for_service "localhost" "5432"
    wait_for_service "localhost" "6379"
    
    log "✅ Infrastructure setup complete"
}

# ⚡ Application services setup
setup_application_services() {
    log "Starting application services..."
    
    # Create basic Dockerfiles if they don't exist
    create_basic_dockerfiles
    
    # Start API Gateway (using existing deno app as placeholder)
    log "Starting API Gateway..."
    docker-compose up -d app-dev
    wait_for_service "localhost" "8000"
    
    # Start AI Service (create basic Python service)
    create_basic_ai_service
    
    log "✅ Application services started"
}

# 📊 Monitoring setup
setup_monitoring() {
    log "Setting up monitoring stack..."
    
    # Create monitoring directory structure
    mkdir -p monitoring/grafana/{dashboards,datasources}
    
    # Create basic Grafana datasource
    cat > monitoring/grafana/datasources/prometheus.yml << 'EOF'
apiVersion: 1
datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF
    
    # Start monitoring services
    docker-compose -f docker-compose.enterprise.yml up -d prometheus grafana
    wait_for_service "localhost" "9090"
    wait_for_service "localhost" "3001"
    
    log "✅ Monitoring stack ready"
}

# ✅ Deployment validation
validate_deployment() {
    log "Validating deployment..."
    
    # Check running containers
    log "Checking running containers..."
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    # Test basic connectivity
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        log "✅ Application is responding"
    else
        warn "Application health check failed"
    fi
    
    if curl -s http://localhost:9090/-/healthy > /dev/null 2>&1; then
        log "✅ Prometheus is healthy"
    else
        warn "Prometheus health check failed"
    fi
    
    if curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
        log "✅ Grafana is healthy"
    else
        warn "Grafana health check failed"
    fi
}

# 🎯 Helper functions
wait_for_service() {
    local host=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    log "Waiting for service on $host:$port..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z $host $port 2>/dev/null; then
            log "✅ Service on $host:$port is ready"
            return 0
        fi
        
        log "Attempt $attempt/$max_attempts - waiting for $host:$port..."
        sleep 2
        ((attempt++))
    done
    
    warn "Service on $host:$port failed to start within expected time"
}

create_basic_dockerfiles() {
    # Create basic structure for services if they don't exist
    if [ ! -d "apps/api-gateway" ]; then
        mkdir -p apps/api-gateway
        cat > apps/api-gateway/Dockerfile << 'EOF'
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
EOF
        cat > apps/api-gateway/package.json << 'EOF'
{
  "name": "api-gateway",
  "version": "1.0.0",
  "scripts": {
    "start": "node index.js"
  },
  "dependencies": {
    "express": "^4.18.0"
  }
}
EOF
        cat > apps/api-gateway/index.js << 'EOF'
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

app.get('/', (req, res) => {
  res.json({ message: 'Enterprise API Gateway', version: '1.0.0' });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`API Gateway listening on port ${port}`);
});
EOF
    fi
}

create_basic_ai_service() {
    if [ ! -d "services/ai-service" ]; then
        mkdir -p services/ai-service
        cat > services/ai-service/Dockerfile << 'EOF'
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "main.py"]
EOF
        cat > services/ai-service/requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
EOF
        cat > services/ai-service/main.py << 'EOF'
from fastapi import FastAPI
import uvicorn

app = FastAPI(title="Enterprise AI Service", version="1.0.0")

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "ai-service"}

@app.get("/")
async def root():
    return {"message": "Enterprise AI/ML Service", "version": "1.0.0"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF
    fi
}

show_access_info() {
    cat << EOF

🎉 ${GREEN}Enterprise Platform Setup Complete!${NC}

📋 ${BLUE}Access Information:${NC}
┌─────────────────────────────────────────────────────┐
│ 🌐 Application:    http://localhost:8000           │
│ 🤖 AI Service:     http://localhost:8000           │
│ 📊 Grafana:        http://localhost:3001           │
│ 📈 Prometheus:     http://localhost:9090           │
│ 🔍 Health Check:   http://localhost:8000/health    │
└─────────────────────────────────────────────────────┘

🔑 ${YELLOW}Default Credentials:${NC}
┌─────────────────────────────────────────────────────┐
│ Grafana:  admin / admin123                          │
│ Database: enterprise_user / secure_password_123     │
└─────────────────────────────────────────────────────┘

📖 ${BLUE}Next Steps:${NC}
1. Check application health: curl http://localhost:8000/health
2. Explore Grafana dashboards at http://localhost:3001
3. View logs: docker-compose logs -f
4. Read documentation in docs/ folder

🚀 ${GREEN}Happy coding!${NC}

EOF
}

# Run main function
main "$@"