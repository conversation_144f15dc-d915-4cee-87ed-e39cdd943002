#!/bin/bash

# 🚀 Enterprise Platform Setup Script
# 
# Theo knowledge base về DevOps & Automation:
# - Infrastructure as Code setup
# - Development environment automation
# - Security best practices
# - Error handling và logging

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# ================================
# 🎨 Colors & Formatting
# ================================
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ================================
# 📝 Logging Functions
# ================================
log_info() {
    echo -e "${BLUE}ℹ️  INFO:${NC} $1"
}

log_success() {
    echo -e "${GREEN}✅ SUCCESS:${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}⚠️  WARNING:${NC} $1"
}

log_error() {
    echo -e "${RED}❌ ERROR:${NC} $1"
}

log_step() {
    echo -e "${PURPLE}🔄 STEP:${NC} $1"
}

# ================================
# 🔧 Configuration
# ================================
PROJECT_NAME="enterprise-platform"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
DOCKER_COMPOSE_FILE="docker-compose.yml"
MONITORING_COMPOSE_FILE="infrastructure/monitoring/docker-compose.monitoring.yml"

# Default values
SKIP_DOCKER_BUILD=false
SKIP_DEPENDENCIES=false
SKIP_DATABASE_SETUP=false
SKIP_MONITORING=false
ENVIRONMENT="development"

# ================================
# 📋 Help Function
# ================================
show_help() {
    cat << EOF
🚀 Enterprise Platform Setup Script

USAGE:
    ./setup.sh [OPTIONS]

OPTIONS:
    -h, --help              Show this help message
    -e, --environment ENV   Set environment (development|staging|production)
    --skip-docker          Skip Docker build steps
    --skip-deps            Skip dependency installation
    --skip-db              Skip database setup
    --skip-monitoring      Skip monitoring stack setup
    --clean                Clean all containers and volumes before setup

EXAMPLES:
    ./setup.sh                          # Full development setup
    ./setup.sh -e production            # Production setup
    ./setup.sh --skip-docker --skip-db  # Quick setup without Docker/DB
    ./setup.sh --clean                  # Clean setup

REQUIREMENTS:
    - Docker & Docker Compose
    - Node.js 18+
    - Python 3.9+
    - Git

EOF
}

# ================================
# 🔍 Prerequisites Check
# ================================
check_prerequisites() {
    log_step "Checking prerequisites..."
    
    local missing_tools=()
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        missing_tools+=("docker-compose")
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        missing_tools+=("node")
    else
        local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$node_version" -lt 18 ]; then
            log_warning "Node.js version $node_version detected. Recommended: 18+"
        fi
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        missing_tools+=("python3")
    else
        local python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1-2)
        if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)" 2>/dev/null; then
            log_warning "Python version $python_version detected. Recommended: 3.9+"
        fi
    fi
    
    # Check Git
    if ! command -v git &> /dev/null; then
        missing_tools+=("git")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install the missing tools and run the script again."
        exit 1
    fi
    
    log_success "All prerequisites satisfied!"
}

# ================================
# 🧹 Cleanup Function
# ================================
cleanup_environment() {
    log_step "Cleaning up existing environment..."
    
    # Stop all containers
    if [ -f "$DOCKER_COMPOSE_FILE" ]; then
        docker-compose -f "$DOCKER_COMPOSE_FILE" down -v --remove-orphans 2>/dev/null || true
    fi
    
    if [ -f "$MONITORING_COMPOSE_FILE" ]; then
        docker-compose -f "$MONITORING_COMPOSE_FILE" down -v --remove-orphans 2>/dev/null || true
    fi
    
    # Remove project-specific containers
    docker ps -a --filter "name=${PROJECT_NAME}" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    
    # Remove project-specific volumes
    docker volume ls --filter "name=${PROJECT_NAME}" --format "{{.Name}}" | xargs -r docker volume rm 2>/dev/null || true
    
    # Clean up dangling images
    docker image prune -f 2>/dev/null || true
    
    log_success "Environment cleaned up!"
}

# ================================
# 📦 Install Dependencies
# ================================
install_dependencies() {
    if [ "$SKIP_DEPENDENCIES" = true ]; then
        log_info "Skipping dependency installation..."
        return
    fi
    
    log_step "Installing dependencies..."
    
    # Install root dependencies
    if [ -f "package.json" ]; then
        log_info "Installing Node.js dependencies..."
        npm ci --silent
    fi
    
    # Install API Gateway dependencies
    if [ -f "apps/api-gateway/package.json" ]; then
        log_info "Installing API Gateway dependencies..."
        cd apps/api-gateway
        npm ci --silent
        cd "$PROJECT_ROOT"
    fi
    
    # Install Python dependencies for AI service
    if [ -f "services/ai-service/requirements.txt" ]; then
        log_info "Installing AI Service dependencies..."
        cd services/ai-service
        
        # Create virtual environment if it doesn't exist
        if [ ! -d "venv" ]; then
            python3 -m venv venv
        fi
        
        # Activate virtual environment and install dependencies
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
        deactivate
        
        cd "$PROJECT_ROOT"
    fi
    
    log_success "Dependencies installed!"
}

# ================================
# 🐳 Docker Setup
# ================================
setup_docker() {
    if [ "$SKIP_DOCKER_BUILD" = true ]; then
        log_info "Skipping Docker build..."
        return
    fi
    
    log_step "Setting up Docker environment..."
    
    # Create Docker network if it doesn't exist
    if ! docker network ls | grep -q "${PROJECT_NAME}-network"; then
        log_info "Creating Docker network..."
        docker network create "${PROJECT_NAME}-network" 2>/dev/null || true
    fi
    
    # Build Docker images
    if [ -f "$DOCKER_COMPOSE_FILE" ]; then
        log_info "Building Docker images..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" build --parallel
    fi
    
    log_success "Docker environment ready!"
}

# ================================
# 🗄️ Database Setup
# ================================
setup_database() {
    if [ "$SKIP_DATABASE_SETUP" = true ]; then
        log_info "Skipping database setup..."
        return
    fi
    
    log_step "Setting up databases..."
    
    # Start database services
    if [ -f "$DOCKER_COMPOSE_FILE" ]; then
        log_info "Starting database services..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d postgres redis
        
        # Wait for PostgreSQL to be ready
        log_info "Waiting for PostgreSQL to be ready..."
        timeout 60 bash -c 'until docker-compose exec -T postgres pg_isready -U postgres; do sleep 2; done'
        
        # Run database migrations
        log_info "Running database migrations..."
        # Add migration commands here when available
        
        # Seed initial data
        log_info "Seeding initial data..."
        # Add seeding commands here when available
    fi
    
    log_success "Database setup complete!"
}

# ================================
# 📊 Monitoring Setup
# ================================
setup_monitoring() {
    if [ "$SKIP_MONITORING" = true ]; then
        log_info "Skipping monitoring setup..."
        return
    fi
    
    log_step "Setting up monitoring stack..."
    
    if [ -f "$MONITORING_COMPOSE_FILE" ]; then
        log_info "Starting monitoring services..."
        docker-compose -f "$MONITORING_COMPOSE_FILE" up -d
        
        # Wait for services to be ready
        log_info "Waiting for monitoring services to be ready..."
        sleep 10
        
        # Check if Grafana is accessible
        if curl -f http://localhost:3001/api/health &>/dev/null; then
            log_success "Grafana is ready at http://localhost:3001"
        else
            log_warning "Grafana might not be ready yet. Check http://localhost:3001"
        fi
        
        # Check if Prometheus is accessible
        if curl -f http://localhost:9090/-/ready &>/dev/null; then
            log_success "Prometheus is ready at http://localhost:9090"
        else
            log_warning "Prometheus might not be ready yet. Check http://localhost:9090"
        fi
    fi
    
    log_success "Monitoring stack setup complete!"
}

# ================================
# 🚀 Start Services
# ================================
start_services() {
    log_step "Starting application services..."
    
    if [ -f "$DOCKER_COMPOSE_FILE" ]; then
        log_info "Starting all services..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
        
        # Wait for services to be healthy
        log_info "Waiting for services to be healthy..."
        sleep 15
        
        # Check service health
        check_service_health
    fi
    
    log_success "All services started!"
}

# ================================
# 🏥 Health Check
# ================================
check_service_health() {
    log_step "Checking service health..."
    
    local services=(
        "API Gateway:http://localhost:3000/health"
        "Grafana:http://localhost:3001/api/health"
        "Prometheus:http://localhost:9090/-/ready"
    )
    
    for service in "${services[@]}"; do
        local name="${service%%:*}"
        local url="${service##*:}"
        
        if curl -f "$url" &>/dev/null; then
            log_success "$name is healthy"
        else
            log_warning "$name is not responding at $url"
        fi
    done
}

# ================================
# 📋 Show Summary
# ================================
show_summary() {
    log_success "🎉 Enterprise Platform setup complete!"
    
    echo ""
    echo -e "${CYAN}📋 SETUP SUMMARY${NC}"
    echo "================================"
    echo -e "Environment: ${YELLOW}$ENVIRONMENT${NC}"
    echo -e "Project Root: ${YELLOW}$PROJECT_ROOT${NC}"
    echo ""
    echo -e "${CYAN}🔗 ACCESS POINTS${NC}"
    echo "================================"
    echo -e "API Gateway:     ${GREEN}http://localhost:3000${NC}"
    echo -e "API Docs:        ${GREEN}http://localhost:3000/docs${NC}"
    echo -e "Grafana:         ${GREEN}http://localhost:3001${NC} (admin/admin123)"
    echo -e "Prometheus:      ${GREEN}http://localhost:9090${NC}"
    echo -e "Jaeger:          ${GREEN}http://localhost:16686${NC}"
    echo -e "Kibana:          ${GREEN}http://localhost:5601${NC}"
    echo ""
    echo -e "${CYAN}🛠️  USEFUL COMMANDS${NC}"
    echo "================================"
    echo "View logs:       docker-compose logs -f"
    echo "Stop services:   docker-compose down"
    echo "Restart:         docker-compose restart"
    echo "Clean up:        ./setup.sh --clean"
    echo ""
}

# ================================
# 🎯 Main Function
# ================================
main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            --skip-docker)
                SKIP_DOCKER_BUILD=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPENDENCIES=true
                shift
                ;;
            --skip-db)
                SKIP_DATABASE_SETUP=true
                shift
                ;;
            --skip-monitoring)
                SKIP_MONITORING=true
                shift
                ;;
            --clean)
                cleanup_environment
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Show banner
    echo -e "${PURPLE}"
    echo "🚀 Enterprise Platform Setup"
    echo "================================"
    echo -e "${NC}"
    
    # Run setup steps
    check_prerequisites
    install_dependencies
    setup_docker
    setup_database
    setup_monitoring
    start_services
    show_summary
}

# ================================
# 🚀 Execute Main Function
# ================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
