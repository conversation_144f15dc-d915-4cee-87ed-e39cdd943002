# 🏗️ Linh-Bui Enterprise NestJS Structure: Complete Master Documentation

> **The definitive guide to building enterprise-grade applications with custom NestJS CRUD architecture, advanced authentication, and comprehensive business modules**

---

## 📋 Table of Contents

### 🎯 [Getting Started](#getting-started)
- [Executive Summary](#executive-summary)
- [Architecture Overview](#architecture-overview)
- [Quick Start Guide](#quick-start-guide)
- [Installation & Setup](#installation--setup)

### 🏛️ [Core Architecture](#core-architecture)
- [System Design](#system-design)
- [SOLID Principles Implementation](#solid-principles-implementation)
- [Design Patterns](#design-patterns)
- [Directory Structure](#directory-structure)

### 🧠 [Core Modules](#core-modules)
- [Base CRUD System](#base-crud-system)
- [Authentication & Authorization](#authentication--authorization)
- [Database & TypeORM](#database--typeorm)
- [Redis Integration](#redis-integration)
- [Mailer System](#mailer-system)
- [Socket Gateway](#socket-gateway)
- [Cloudinary Integration](#cloudinary-integration)
- [Exception Handling](#exception-handling)

### ⚡ [Business Modules](#business-modules)
- [User Management](#user-management)
- [Job Management](#job-management)
- [Worker Management](#worker-management)
- [Vehicle Management](#vehicle-management)
- [Audit Logging](#audit-logging)
- [File Management](#file-management)
- [Public APIs](#public-apis)

### 🔧 [Advanced Features](#advanced-features)
- [Custom CRUD Implementation](#custom-crud-implementation)
- [Middleware & Interceptors](#middleware--interceptors)
- [Security Implementation](#security-implementation)
- [Performance Optimization](#performance-optimization)

### 📚 [Implementation Guide](#implementation-guide)
- [Entity Creation](#entity-creation)
- [Service Implementation](#service-implementation)
- [Controller Setup](#controller-setup)
- [Module Configuration](#module-configuration)

### 🚀 [Deployment & Operations](#deployment--operations)
- [Environment Configuration](#environment-configuration)
- [Database Migrations](#database-migrations)
- [Production Setup](#production-setup)
- [Monitoring & Logging](#monitoring--logging)

---

## 🎯 Getting Started

### Executive Summary

The Linh-Bui Enterprise NestJS Structure represents a sophisticated, production-ready implementation of enterprise-grade CRUD operations built on top of custom NestJS architecture. This comprehensive system combines advanced authentication, real-time communication, file management, and business-specific modules to provide a complete foundation for scalable applications.

**Key Benefits:**
- ✅ **Enterprise Architecture**: Clean, modular design with SOLID principles
- ✅ **Custom CRUD System**: Extended functionality beyond standard CRUD operations
- ✅ **Advanced Authentication**: JWT-based auth with role management and permissions
- ✅ **Real-time Features**: Socket.IO integration with Redis adapter
- ✅ **File Management**: Cloudinary integration for media handling
- ✅ **Audit Trail**: Comprehensive logging and tracking system
- ✅ **Business Logic**: Domain-specific modules for job, worker, and vehicle management
- ✅ **Production Ready**: Redis caching, email notifications, and monitoring

**Target Audience:**
- **Enterprise Architects** building scalable business applications
- **Senior Developers** implementing complex domain logic
- **DevOps Engineers** deploying production systems
- **Technical Leaders** evaluating architectural decisions

### Architecture Overview

The system follows a layered architecture with clear separation of concerns:

```typescript
┌─────────────────────────────────────────────────────────┐
│                    🌐 PRESENTATION LAYER                │
│  Controllers │ Guards │ Interceptors │ Middleware       │
├─────────────────────────────────────────────────────────┤
│                   ⚡ APPLICATION LAYER                   │
│  Services │ DTOs │ Business Logic │ Use Cases           │
├─────────────────────────────────────────────────────────┤
│                    🧠 DOMAIN LAYER                      │
│  Entities │ Value Objects │ Domain Events │ Rules       │
├─────────────────────────────────────────────────────────┤
│                 🏗️ INFRASTRUCTURE LAYER                 │
│  Database │ Redis │ Mailer │ Socket │ External APIs     │
└─────────────────────────────────────────────────────────┘
```

### Quick Start Guide

#### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd linh-bui-structure

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env
# Configure database, redis, and other services
```

#### 2. Database Configuration

```typescript
// src/core/database/typeorm.config.ts
export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: configService.get('DB_HOST'),
  port: configService.get('DB_PORT'),
  username: configService.get('DB_USER'),
  password: configService.get('DB_PASSWORD'),
  database: configService.get('DB_NAME'),
  entities: ['dist/**/*.entity.{ts,js}'],
  migrations: ['dist/migrations/*{.ts,.js}'],
  logging: ['error'],
};
```

#### 3. Basic Entity Example

```typescript
// Example: User Entity
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';

@Entity('users')
export class User extends DocEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ default: true })
  isActive: boolean;
}
```

#### 4. Service Implementation

```typescript
// Example: User Service
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { User } from './user.entity';

@Injectable()
export class UserService extends BaseCrudService<User> {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {
    super(userRepository);
  }

  // Custom business logic methods
  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email } });
  }
}
```

#### 5. Controller Setup

```typescript
// Example: User Controller
import { Controller } from '@nestjs/common';
import { Crud } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { User } from './user.entity';
import { UserService } from './user.service';

@Crud({
  model: { type: User },
  query: {
    maxLimit: 100,
    cache: 2000,
  },
})
@Controller('users')
export class UserController extends BaseCrudController<User> {
  constructor(public service: UserService) {
    super(service);
  }
}
```

### Installation & Setup

#### Prerequisites

- Node.js 16+ and npm/yarn
- PostgreSQL database
- Redis server
- Cloudinary account (for file uploads)
- SMTP server (for email notifications)

#### Environment Variables

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=enterprise_app

# Redis Configuration
REDIS_COMMON=redis://localhost:6379/0

# JWT Configuration
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-app-password
```

#### Development Setup

```bash
# Install dependencies
npm install

# Run database migrations
npm run migration:run

# Seed initial data
npm run seed:run

# Start development server
npm run start:dev
```

---

## 🏛️ Core Architecture

### System Design

The Linh-Bui Enterprise Structure implements a sophisticated multi-layered architecture:

#### 1. Core Layer (`src/core/`)
- **Base Classes**: Abstract CRUD services and controllers
- **Authentication**: JWT-based auth with role management
- **Database**: TypeORM configuration and utilities
- **Redis**: Caching and session management
- **Mailer**: Email notification system
- **Socket**: Real-time communication
- **Exception**: Custom exception handling

#### 2. Business Layer (`src/modules/`)
- **Domain Modules**: User, Job, Worker, Vehicle management
- **Audit System**: Comprehensive logging and tracking
- **File Management**: Upload and media handling
- **Public APIs**: External-facing endpoints

#### 3. Infrastructure Layer
- **Database**: PostgreSQL with TypeORM
- **Cache**: Redis for performance optimization
- **Storage**: Cloudinary for media files
- **Communication**: Socket.IO for real-time features

### SOLID Principles Implementation

#### Single Responsibility Principle (SRP) ✅
```

### Design Patterns

#### 1. Repository Pattern ⭐⭐⭐
```typescript
// BaseCrudService implements Repository pattern
export class BaseCrudService<T> extends TypeOrmCrudService<T> {
  constructor(protected repo: Repository<T>) {
    super(repo);
  }

  // Encapsulates data access logic
  async findWithRelations(id: string, relations: string[]): Promise<T> {
    return this.repo.findOne({ where: { id }, relations });
  }
}
```

#### 2. Factory Pattern ⭐⭐⭐
```typescript
// Exception factory for creating specific exceptions
export class ExceptionFactory {
  static createDuplicatedEntryException(field: string): DuplicatedEntryException {
    return new DuplicatedEntryException(`Duplicated entry for field: ${field}`);
  }

  static createNotNullEntryException(field: string): NotNullEntryException {
    return new NotNullEntryException(`Field ${field} cannot be null`);
  }
}
```

#### 3. Strategy Pattern ⭐⭐⭐
```typescript
// Different authentication strategies
interface AuthStrategy {
  authenticate(credentials: any): Promise<User>;
}

class JwtAuthStrategy implements AuthStrategy {
  async authenticate(token: string): Promise<User> {
    // JWT authentication logic
  }
}

class LocalAuthStrategy implements AuthStrategy {
  async authenticate(credentials: SigninDTO): Promise<User> {
    // Local authentication logic
  }
}
```

#### 4. Observer Pattern ⭐⭐⭐
```typescript
// Audit logging interceptor observes all requests
@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Log all operations for audit trail
    return next.handle().pipe(
      tap(() => this.logOperation(context))
    );
  }
}
```

#### 5. Decorator Pattern ⭐⭐⭐
```typescript
// Custom decorators extend functionality
export function ApiAuth() {
  return applyDecorators(
    UseGuards(JwtAuthGuard),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({ description: 'Unauthorized' })
  );
}
```

### Directory Structure

```
linh-bui-structure/
├── 📦 package.json                  # Dependencies and scripts
├── 📋 tsconfig.json                 # TypeScript configuration
├── 🔧 nest-cli.json                 # NestJS CLI configuration
├── 🐳 Dockerfile                    # Container configuration
├── 📄 .env.example                  # Environment template
│
├── 🗄️ migrations/                   # Database migrations
├── 🌱 seeder/                       # Database seeders
│
├── 🏗️ src/                          # Main source code
│   ├── 📱 app.controller.ts         # Root controller
│   ├── 📱 app.module.ts             # Root module
│   ├── 📱 app.service.ts            # Root service
│   ├── 🚀 main.ts                   # Application entry point
│   │
│   ├── 🧠 core/                     # Core infrastructure
│   │   ├── 🔐 auth/                 # Authentication system
│   │   │   ├── auth.controller.ts   # Auth endpoints
│   │   │   ├── auth.service.ts      # Auth business logic
│   │   │   ├── auth.entity.ts       # Auth entity
│   │   │   ├── auth.module.ts       # Auth module
│   │   │   ├── decorators/          # Auth decorators
│   │   │   ├── dto/                 # Auth DTOs
│   │   │   ├── guards/              # Auth guards
│   │   │   ├── strategies/          # Auth strategies
│   │   │   └── utils/               # Auth utilities
│   │   │
│   │   ├── 🏗️ base/                 # Base classes
│   │   │   ├── base-crud.controller.ts  # Base CRUD controller
│   │   │   ├── base-crud.service.ts     # Base CRUD service
│   │   │   ├── base.module.ts           # Base module
│   │   │   ├── doc.entity.ts            # Base document entity
│   │   │   └── decorator/               # Base decorators
│   │   │
│   │   ├── ☁️ cloudinary/            # File upload service
│   │   │   ├── cloudinary.module.ts
│   │   │   ├── cloudinary.provider.ts
│   │   │   └── cloudinary.service.ts
│   │   │
│   │   ├── 🔧 common/               # Common utilities
│   │   │   ├── all-exceptions.filter.ts
│   │   │   ├── common-http.status.ts
│   │   │   └── common.utils.ts
│   │   │
│   │   ├── ⚙️ config/               # Configuration
│   │   │   ├── config.service.ts
│   │   │   ├── constants.ts
│   │   │   └── interfaces/
│   │   │
│   │   ├── 📊 crud/                 # Custom CRUD implementation
│   │   │   ├── crud/                # Core CRUD functionality
│   │   │   ├── crud-request/        # Request parsing
│   │   │   ├── crud-typeorm/        # TypeORM integration
│   │   │   └── util/                # CRUD utilities
│   │   │
│   │   ├── 🔒 crypto/               # Cryptography utilities
│   │   ├── 🗄️ database/             # Database configuration
│   │   ├── 🛠️ dev-util/             # Development utilities
│   │   ├── ⚠️ exception/            # Exception handling
│   │   ├── 📧 mailer/               # Email service
│   │   ├── 🔄 middlewares/          # Custom middleware
│   │   ├── 🔴 redis/                # Redis integration
│   │   ├── 📋 resource/             # Resource management
│   │   └── 🔌 socket/               # WebSocket gateway
│   │
│   └── 📦 modules/                  # Business modules
│       ├── main.module.ts           # Main modules aggregator
│       ├── 👤 user/                 # User management
│       ├── 🔑 token/                # Token management
│       ├── 💼 job/                  # Job management
│       ├── 👷 worker/               # Worker management
│       ├── 🚗 vehicle/              # Vehicle management
│       ├── 📝 audit-log/            # Audit logging
│       ├── 📁 file/                 # File management
│       ├── 🌐 public/               # Public APIs
│       ├── 📅 absent/               # Absence management
│       ├── 📋 job-arrange/          # Job arrangement
│       └── 👁️ public-view-code/     # Public view codes
│
└── 📚 docs/                         # Documentation
    └── MASTER_DOCUMENTATION.md      # This file
```

---

## 🧠 Core Modules

### Base CRUD System

The foundation of the application is built on a sophisticated CRUD system that extends the standard NestJS CRUD functionality:

#### BaseCrudService

```typescript
// src/core/base/base-crud.service.ts
export class BaseCrudService<T> extends TypeOrmCrudService<T> {
  public entityUserPerm;
  private currentMetadata;
  private currentEntity;
  private currentPropMaps;

  constructor(repo: Repository<T>) {
    super(repo);
    this.currentMetadata = this.repo.metadata;
    this.currentEntity = this.currentMetadata?.target;
    this.currentPropMaps = this.repo.metadata.propertiesMap;
  }

  // Enhanced CRUD operations with error handling
  async getMany(req: CrudRequest): Promise<GetManyDefaultResponse<T> | T[]> {
    try {
      return super.getMany(req);
    } catch (error) {
      this.filterWriteException(error);
    }
  }

  async getOne(req: CrudRequest): Promise<T> {
    try {
      return super.getOne(req);
    } catch (error) {
      this.filterWriteException(error);
    }
  }

  // Custom exception filtering
  private filterWriteException(error: any): void {
    if (error.code === '23505') { // Unique constraint violation
      throw new DuplicatedEntryException(error.detail);
    }
    if (error.code === '23502') { // Not null constraint violation
      throw new NotNullEntryException(error.detail);
    }
    throw error;
  }
}
```

#### BaseCrudController

```typescript
// src/core/base/base-crud.controller.ts
export class BaseCrudController<T> implements CrudController<T> {
  constructor(public service: BaseCrudService<T>) {}

  get base(): CrudController<T> {
    return this;
  }
}
```

#### Base Entity

```typescript
// src/core/base/doc.entity.ts
export abstract class DocEntity {
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  updatedBy?: string;
}
```

### Authentication & Authorization

The authentication system provides comprehensive JWT-based authentication with role management:

#### Auth Entity

```typescript
// src/core/auth/auth.entity.ts
export enum AuthType {
  ADMIN = 'admin',
  USER = 'user',
  WORKER = 'worker',
}

@Entity('auth')
export class Auth extends DocEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  username: string;

  @Column()
  password: string;

  @Column({
    type: 'enum',
    enum: AuthType,
    default: AuthType.USER,
  })
  type: AuthType;

  @Column({ default: true })
  isActive: boolean;

  @OneToOne(() => User, user => user.auth)
  user: User;
}
```

#### Auth Service

```typescript
// src/core/auth/auth.service.ts
@Injectable()
export class AuthService extends BaseCrudService<Auth> {
  constructor(
    @InjectRepository(Auth) repo: Repository<Auth>,
    @InjectRepository(User) private userRepo: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly tokenService: TokenService,
    private readonly mailerService: MailerService,
  ) {
    super(repo);
  }

  // Sign in with credentials
  async signin(signinDto: SigninDTO): Promise<any> {
    const { username, password } = signinDto;

    const auth = await this.repo.findOne({
      where: { username },
      relations: ['user', 'user.role'],
    });

    if (!auth || !auth.isActive) {
      throw new InvalidCredentialsException();
    }

    const isPasswordValid = await bcrypt.compare(password, auth.password);
    if (!isPasswordValid) {
      throw new InvalidCredentialsException();
    }

    // Generate JWT token
    const payload = new AuthSignedTokenPayload(auth.user.id, auth.user.role);
    const token = this.jwtService.sign(payload.getPayload());

    return {
      access_token: token,
      user: auth.user,
      expires_in: jwtConstants.expiresIn,
    };
  }

  // Password reset functionality
  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    const { email } = resetPasswordDto;

    const user = await this.userRepo.findOne({ where: { email } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Generate reset token
    const resetToken = md5(user.email + Date.now());

    // Save token to database
    await this.tokenService.createOne({
      userId: user.id,
      token: resetToken,
      type: TypeToken.RESET_PASSWORD,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    });

    // Send reset email
    const template = handlebars.compile(resetPasswordTemplate);
    const html = template({ resetToken, userName: user.firstName });

    await this.mailerService.sendMail({
      to: user.email,
      subject: 'Password Reset Request',
      html,
    });
  }
}
```

#### JWT Strategy

```typescript
// src/core/auth/strategies/jwt.strategy.ts
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly userService: UserService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtConstants.secret,
    });
  }

  async validate(payload: any): Promise<User> {
    const user = await this.userService.findOne(payload.id);
    if (!user || !user.isActive) {
      throw new UnauthorizedException();
    }
    return user;
  }
}
```

### Database & TypeORM

The database layer uses TypeORM with PostgreSQL for robust data persistence:

#### Database Configuration

```typescript
// src/core/database/typeorm.config.ts
export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: configService.get('DB_HOST'),
  port: configService.get('DB_PORT'),
  username: configService.get('DB_USER'),
  password: configService.get('DB_PASSWORD'),
  database: configService.get('DB_NAME'),
  entities: ['dist/**/*.entity.{ts,js}'],
  logging: ['error'],
  migrations: ['dist/migrations/*{.ts,.js}'],
};
```

#### Database Module

```typescript
// src/core/database/database.module.ts
@Module({
  imports: [
    TypeOrmModule.forRoot(dataSourceOptions),
  ],
  providers: [
    {
      provide: 'DATABASE_CONNECTION',
      useFactory: async () => {
        const dataSource = new DataSource(dataSourceOptions);
        return dataSource.initialize();
      },
    },
  ],
  exports: ['DATABASE_CONNECTION'],
})
export class DatabaseModule {}
```

### Redis Integration

Redis is used for caching, session management, and real-time features:

#### Redis Configuration

```typescript
// src/core/redis/redis.module.ts
@Module({})
export class RedisModule {
  static forRootAsync(options: {
    useFactory: () => RedisModuleOptions[];
  }): DynamicModule {
    return {
      module: RedisModule,
      imports: [RedisCoreModule.forRootAsync(options)],
      exports: [RedisCoreModule],
    };
  }
}
```

#### Redis Service

```typescript
// src/core/redis/redis.service.ts
@Injectable()
export class RedisService {
  constructor(
    @Inject(REDIS_CLIENT) private readonly redisClient: Redis,
  ) {}

  async get(key: string): Promise<string | null> {
    return this.redisClient.get(key);
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.redisClient.setex(key, ttl, value);
    } else {
      await this.redisClient.set(key, value);
    }
  }

  async del(key: string): Promise<void> {
    await this.redisClient.del(key);
  }

  async exists(key: string): Promise<boolean> {
    const result = await this.redisClient.exists(key);
    return result === 1;
  }
}
```

### Mailer System

Comprehensive email system with template support:

#### Mailer Service

```typescript
// src/core/mailer/mailer.service.ts
@Injectable()
export class MailerService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.MAIL_HOST,
      port: parseInt(process.env.MAIL_PORT),
      secure: false,
      auth: {
        user: process.env.MAIL_USER,
        pass: process.env.MAIL_PASS,
      },
    });
  }

  async sendMail(options: {
    to: string;
    subject: string;
    html: string;
    attachments?: any[];
  }): Promise<void> {
    try {
      await this.transporter.sendMail({
        from: process.env.MAIL_FROM,
        ...options,
      });
    } catch (error) {
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async sendWelcomeEmail(user: User): Promise<void> {
    const template = handlebars.compile(welcomeTemplate);
    const html = template({ userName: user.firstName });

    await this.sendMail({
      to: user.email,
      subject: 'Welcome to Our Platform',
      html,
    });
  }
}
```

#### Email Templates

```typescript
// src/core/mailer/templates/index.ts
export const resetPasswordTemplate = `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2>Password Reset Request</h2>
  <p>Hello {{userName}},</p>
  <p>You have requested to reset your password. Click the link below to reset it:</p>
  <a href="{{resetLink}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
    Reset Password
  </a>
  <p>This link will expire in 24 hours.</p>
  <p>If you didn't request this, please ignore this email.</p>
</div>
`;

export const welcomeTemplate = `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2>Welcome to Our Platform!</h2>
  <p>Hello {{userName}},</p>
  <p>Welcome to our platform. We're excited to have you on board!</p>
  <p>Get started by exploring our features and setting up your profile.</p>
</div>
`;
```

### Socket Gateway

Real-time communication using Socket.IO with Redis adapter:

#### Socket Gateway

```typescript
// src/core/socket/socket.gateway.ts
@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class SocketGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer() server: Server;

  constructor(
    private readonly eventsService: EventsService,
  ) {}

  afterInit(server: Server) {
    // Setup Redis adapter for scaling
    const redisAdapter = createAdapter(
      createClient({ url: process.env.REDIS_SOCKET }),
      createClient({ url: process.env.REDIS_SOCKET })
    );
    server.adapter(redisAdapter);
  }

  handleConnection(client: Socket) {
    console.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('join-room')
  handleJoinRoom(client: Socket, room: string) {
    client.join(room);
    client.emit('joined-room', room);
  }

  @SubscribeMessage('leave-room')
  handleLeaveRoom(client: Socket, room: string) {
    client.leave(room);
    client.emit('left-room', room);
  }

  // Emit events to specific rooms
  emitToRoom(room: string, event: string, data: any) {
    this.server.to(room).emit(event, data);
  }

  // Broadcast to all connected clients
  broadcast(event: string, data: any) {
    this.server.emit(event, data);
  }
}
```

#### Events Service

```typescript
// src/core/socket/events.service.ts
@Injectable()
export class EventsService {
  constructor(
    private readonly socketGateway: SocketGateway,
  ) {}

  // Job-related events
  notifyJobCreated(jobId: string, data: any) {
    this.socketGateway.emitToRoom(`job-${jobId}`, 'job-created', data);
  }

  notifyJobUpdated(jobId: string, data: any) {
    this.socketGateway.emitToRoom(`job-${jobId}`, 'job-updated', data);
  }

  notifyJobAssigned(workerId: string, jobData: any) {
    this.socketGateway.emitToRoom(`worker-${workerId}`, 'job-assigned', jobData);
  }

  // Worker-related events
  notifyWorkerStatusChanged(workerId: string, status: string) {
    this.socketGateway.broadcast('worker-status-changed', {
      workerId,
      status,
      timestamp: new Date(),
    });
  }
}
```

### Cloudinary Integration

File upload and media management using Cloudinary:

#### Cloudinary Service

```typescript
// src/core/cloudinary/cloudinary.service.ts
@Injectable()
export class CloudinaryService {
  constructor() {
    cloudinary.config({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
      api_key: process.env.CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET,
    });
  }

  async uploadImage(file: Express.Multer.File, folder?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder: folder || 'uploads',
          resource_type: 'auto',
          transformation: [
            { width: 1000, height: 1000, crop: 'limit' },
            { quality: 'auto' },
            { fetch_format: 'auto' },
          ],
        },
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result);
          }
        }
      );

      uploadStream.end(file.buffer);
    });
  }

  async deleteImage(publicId: string): Promise<any> {
    return cloudinary.uploader.destroy(publicId);
  }

  async uploadVideo(file: Express.Multer.File, folder?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder: folder || 'videos',
          resource_type: 'video',
          transformation: [
            { quality: 'auto' },
            { fetch_format: 'auto' },
          ],
        },
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result);
          }
        }
      );

      uploadStream.end(file.buffer);
    });
  }
}
```

### Exception Handling

Comprehensive exception handling system with custom exceptions:

#### Custom Exceptions

```typescript
// src/core/exception/core/duplicated-entry.exception.ts
export class DuplicatedEntryException extends BadRequestException {
  constructor(message?: string) {
    super(message || 'Duplicated entry detected');
  }
}

// src/core/exception/core/not-null-entry.exception.ts
export class NotNullEntryException extends BadRequestException {
  constructor(message?: string) {
    super(message || 'Required field cannot be null');
  }
}

// src/core/exception/core/unauthorized.exception.ts
export class UnauthorizedException extends HttpException {
  constructor(message?: string) {
    super(message || 'Unauthorized access', HttpStatus.UNAUTHORIZED);
  }
}
```

#### Global Exception Filter

```typescript
// src/core/common/all-exceptions.filter.ts
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      message = exception.message;
    } else if (exception instanceof Error) {
      message = exception.message;
    }

    const errorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message,
    };

    this.logger.error(
      `${request.method} ${request.url}`,
      JSON.stringify(errorResponse),
      'AllExceptionsFilter'
    );

    response.status(status).json(errorResponse);
  }
}
```

---

## ⚡ Business Modules

### User Management

Comprehensive user management with role-based access control:

#### User Entity

```typescript
// src/modules/user/user.entity.ts
@Entity('users')
export class User extends DocEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  avatar: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  lastLoginAt: Date;

  @ManyToOne(() => Role, role => role.users)
  @JoinColumn({ name: 'roleId' })
  role: Role;

  @Column({ nullable: true })
  roleId: string;

  @OneToOne(() => Auth, auth => auth.user)
  auth: Auth;

  @OneToMany(() => Job, job => job.assignedUser)
  assignedJobs: Job[];

  // Virtual properties
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }
}
```

#### User Service

```typescript
// src/modules/user/user.service.ts
@Injectable()
export class UserService extends BaseCrudService<User> {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly authService: AuthService,
    private readonly mailerService: MailerService,
  ) {
    super(userRepository);
  }

  async createUserWithAuth(createUserDto: CreateUserDto): Promise<User> {
    const { email, password, ...userData } = createUserDto;

    // Check if user already exists
    const existingUser = await this.findByEmail(email);
    if (existingUser) {
      throw new DuplicatedEntryException('Email already exists');
    }

    // Create user
    const user = this.userRepository.create(userData);
    const savedUser = await this.userRepository.save(user);

    // Create auth record
    const hashedPassword = await bcrypt.hash(password, 12);
    await this.authService.createOne({
      username: email,
      password: hashedPassword,
      userId: savedUser.id,
      type: AuthType.USER,
    });

    // Send welcome email
    await this.mailerService.sendWelcomeEmail(savedUser);

    return savedUser;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email },
      relations: ['role', 'auth'],
    });
  }

  async updateProfile(userId: string, updateData: UpdateUserDto): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    Object.assign(user, updateData);
    return this.userRepository.save(user);
  }

  async deactivateUser(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.isActive = false;
    return this.userRepository.save(user);
  }
}
```

#### User Controller

```typescript
// src/modules/user/user.controller.ts
@Crud({
  model: { type: User },
  dto: {
    create: CreateUserDto,
    update: UpdateUserDto,
  },
  query: {
    maxLimit: 100,
    cache: 2000,
    join: {
      role: { eager: true },
      assignedJobs: { eager: false },
    },
  },
  routes: {
    only: ['getManyBase', 'getOneBase', 'createOneBase', 'updateOneBase'],
    getManyBase: {
      decorators: [UseGuards(JwtAuthGuard)],
    },
    getOneBase: {
      decorators: [UseGuards(JwtAuthGuard)],
    },
    createOneBase: {
      decorators: [UseGuards(JwtAuthGuard), Roles('admin')],
    },
    updateOneBase: {
      decorators: [UseGuards(JwtAuthGuard)],
    },
  },
})
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserController extends BaseCrudController<User> {
  constructor(public service: UserService) {
    super(service);
  }

  @Post('create-with-auth')
  @Roles('admin')
  @ApiOperation({ summary: 'Create user with authentication' })
  async createUserWithAuth(@Body() createUserDto: CreateUserDto) {
    return this.service.createUserWithAuth(createUserDto);
  }

  @Get('profile')
  @ApiOperation({ summary: 'Get current user profile' })
  async getProfile(@Request() req) {
    return this.service.findOne(req.user.id);
  }

  @Patch('profile')
  @ApiOperation({ summary: 'Update current user profile' })
  async updateProfile(@Request() req, @Body() updateUserDto: UpdateUserDto) {
    return this.service.updateProfile(req.user.id, updateUserDto);
  }

  @Post(':id/deactivate')
  @Roles('admin')
  @ApiOperation({ summary: 'Deactivate user' })
  async deactivateUser(@Param('id') id: string) {
    return this.service.deactivateUser(id);
  }
}
```

### Job Management

Comprehensive job management system with worker assignment:

#### Job Entity

```typescript
// src/modules/job/job.entity.ts
export enum JobStatus {
  PENDING = 'pending',
  ASSIGNED = 'assigned',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum JobPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('jobs')
export class Job extends DocEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column('text')
  description: string;

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.PENDING,
  })
  status: JobStatus;

  @Column({
    type: 'enum',
    enum: JobPriority,
    default: JobPriority.MEDIUM,
  })
  priority: JobPriority;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  estimatedCost: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  actualCost: number;

  @Column({ nullable: true })
  scheduledDate: Date;

  @Column({ nullable: true })
  completedDate: Date;

  @Column('text', { nullable: true })
  notes: string;

  @Column('simple-json', { nullable: true })
  location: {
    address: string;
    latitude: number;
    longitude: number;
  };

  // Relationships
  @ManyToOne(() => JobType, jobType => jobType.jobs)
  @JoinColumn({ name: 'jobTypeId' })
  jobType: JobType;

  @Column()
  jobTypeId: string;

  @ManyToOne(() => User, user => user.assignedJobs)
  @JoinColumn({ name: 'assignedUserId' })
  assignedUser: User;

  @Column({ nullable: true })
  assignedUserId: string;

  @ManyToMany(() => Worker, worker => worker.jobs)
  @JoinTable({
    name: 'job_workers',
    joinColumn: { name: 'jobId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'workerId', referencedColumnName: 'id' },
  })
  workers: Worker[];

  @OneToMany(() => JobArrange, jobArrange => jobArrange.job)
  arrangements: JobArrange[];
}
```

#### Job Service

```typescript
// src/modules/job/job.service.ts
@Injectable()
export class JobService extends BaseCrudService<Job> {
  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly eventsService: EventsService,
    private readonly workerService: WorkerService,
  ) {
    super(jobRepository);
  }

  async createJob(createJobDto: CreateJobDto): Promise<Job> {
    const job = this.jobRepository.create(createJobDto);
    const savedJob = await this.jobRepository.save(job);

    // Notify via socket
    this.eventsService.notifyJobCreated(savedJob.id, savedJob);

    return savedJob;
  }

  async assignWorkers(jobId: string, workerIds: string[]): Promise<Job> {
    const job = await this.jobRepository.findOne({
      where: { id: jobId },
      relations: ['workers'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    const workers = await this.workerService.findByIds(workerIds);
    job.workers = workers;
    job.status = JobStatus.ASSIGNED;

    const updatedJob = await this.jobRepository.save(job);

    // Notify workers
    workers.forEach(worker => {
      this.eventsService.notifyJobAssigned(worker.id, updatedJob);
    });

    return updatedJob;
  }

  async updateJobStatus(jobId: string, status: JobStatus): Promise<Job> {
    const job = await this.jobRepository.findOne({ where: { id: jobId } });
    if (!job) {
      throw new NotFoundException('Job not found');
    }

    job.status = status;
    if (status === JobStatus.COMPLETED) {
      job.completedDate = new Date();
    }

    const updatedJob = await this.jobRepository.save(job);
    this.eventsService.notifyJobUpdated(jobId, updatedJob);

    return updatedJob;
  }

  async getJobsByStatus(status: JobStatus): Promise<Job[]> {
    return this.jobRepository.find({
      where: { status },
      relations: ['jobType', 'assignedUser', 'workers'],
      order: { createdAt: 'DESC' },
    });
  }

  async getJobsInRadius(
    latitude: number,
    longitude: number,
    radiusKm: number
  ): Promise<Job[]> {
    return this.jobRepository
      .createQueryBuilder('job')
      .where(
        `ST_DWithin(
          ST_MakePoint(CAST(job.location->>'longitude' AS FLOAT), CAST(job.location->>'latitude' AS FLOAT))::geography,
          ST_MakePoint(:longitude, :latitude)::geography,
          :radius
        )`,
        { latitude, longitude, radius: radiusKm * 1000 }
      )
      .getMany();
  }
}
```

### Worker Management

Worker management with specialization and vehicle assignment:

#### Worker Entity

```typescript
// src/modules/worker/worker.entity.ts
export enum WorkerStatus {
  AVAILABLE = 'available',
  BUSY = 'busy',
  OFFLINE = 'offline',
  ON_BREAK = 'on_break',
}

@Entity('workers')
export class Worker extends DocEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  employeeId: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ unique: true })
  email: string;

  @Column()
  phone: string;

  @Column({
    type: 'enum',
    enum: WorkerStatus,
    default: WorkerStatus.AVAILABLE,
  })
  status: WorkerStatus;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  hourlyRate: number;

  @Column({ nullable: true })
  profileImage: string;

  @Column('simple-json', { nullable: true })
  currentLocation: {
    latitude: number;
    longitude: number;
    timestamp: Date;
  };

  // Relationships
  @ManyToMany(() => Specialization, specialization => specialization.workers)
  @JoinTable({
    name: 'worker_specializations',
    joinColumn: { name: 'workerId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'specializationId', referencedColumnName: 'id' },
  })
  specializations: Specialization[];

  @ManyToOne(() => Vehicle, vehicle => vehicle.workers)
  @JoinColumn({ name: 'vehicleId' })
  vehicle: Vehicle;

  @Column({ nullable: true })
  vehicleId: string;

  @ManyToMany(() => Job, job => job.workers)
  jobs: Job[];

  @OneToMany(() => Absent, absent => absent.worker)
  absences: Absent[];

  // Virtual properties
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  get isAvailable(): boolean {
    return this.status === WorkerStatus.AVAILABLE;
  }
}
```

### Audit Logging

Comprehensive audit trail system:

#### Audit Log Entity

```typescript
// src/modules/audit-log/audit-log.entity.ts
export enum AuditAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  READ = 'read',
  LOGIN = 'login',
  LOGOUT = 'logout',
}

@Entity('audit_logs')
export class AuditLog extends DocEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: AuditAction,
  })
  action: AuditAction;

  @Column()
  entityName: string;

  @Column({ nullable: true })
  entityId: string;

  @Column('simple-json', { nullable: true })
  oldValues: any;

  @Column('simple-json', { nullable: true })
  newValues: any;

  @Column()
  userId: string;

  @Column()
  userEmail: string;

  @Column()
  ipAddress: string;

  @Column()
  userAgent: string;

  @Column({ nullable: true })
  endpoint: string;

  @Column({ nullable: true })
  method: string;

  @Column('simple-json', { nullable: true })
  metadata: any;
}
```

#### Audit Log Interceptor

```typescript
// src/modules/audit-log/interceptors/audit-log.interceptor.ts
@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
  constructor(
    private readonly auditLogService: AuditLogService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { user, ip, headers, method, url, body } = request;

    // Skip logging for certain endpoints
    if (this.shouldSkipLogging(url)) {
      return next.handle();
    }

    const startTime = Date.now();

    return next.handle().pipe(
      tap(async (response) => {
        try {
          const endTime = Date.now();
          const duration = endTime - startTime;

          await this.auditLogService.createAuditLog({
            action: this.mapMethodToAction(method),
            entityName: this.extractEntityName(url),
            entityId: this.extractEntityId(url),
            userId: user?.id,
            userEmail: user?.email,
            ipAddress: ip,
            userAgent: headers['user-agent'],
            endpoint: url,
            method,
            metadata: {
              duration,
              requestBody: this.sanitizeBody(body),
              responseSize: JSON.stringify(response).length,
            },
          });
        } catch (error) {
          // Log error but don't fail the request
          console.error('Audit logging failed:', error);
        }
      }),
      catchError((error) => {
        // Log error operations as well
        this.auditLogService.createAuditLog({
          action: AuditAction.READ,
          entityName: this.extractEntityName(url),
          userId: user?.id,
          userEmail: user?.email,
          ipAddress: ip,
          userAgent: headers['user-agent'],
          endpoint: url,
          method,
          metadata: {
            error: error.message,
            stack: error.stack,
          },
        }).catch(() => {}); // Ignore audit logging errors

        return throwError(error);
      })
    );
  }

  private shouldSkipLogging(url: string): boolean {
    const skipPatterns = ['/health', '/metrics', '/favicon.ico'];
    return skipPatterns.some(pattern => url.includes(pattern));
  }

  private mapMethodToAction(method: string): AuditAction {
    switch (method.toUpperCase()) {
      case 'POST': return AuditAction.CREATE;
      case 'PUT':
      case 'PATCH': return AuditAction.UPDATE;
      case 'DELETE': return AuditAction.DELETE;
      default: return AuditAction.READ;
    }
  }

  private extractEntityName(url: string): string {
    const segments = url.split('/').filter(Boolean);
    return segments[0] || 'unknown';
  }

  private extractEntityId(url: string): string | null {
    const segments = url.split('/').filter(Boolean);
    const idPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return segments.find(segment => idPattern.test(segment)) || null;
  }

  private sanitizeBody(body: any): any {
    if (!body) return null;

    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key'];

    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}
```

### File Management

Comprehensive file upload and management system:

#### File Entity

```typescript
// src/modules/file/file.entity.ts
export enum FileType {
  IMAGE = 'image',
  VIDEO = 'video',
  DOCUMENT = 'document',
  AUDIO = 'audio',
}

@Entity('files')
export class File extends DocEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  originalName: string;

  @Column()
  fileName: string;

  @Column()
  mimeType: string;

  @Column('bigint')
  size: number;

  @Column({
    type: 'enum',
    enum: FileType,
  })
  type: FileType;

  @Column()
  url: string;

  @Column({ nullable: true })
  publicId: string; // Cloudinary public ID

  @Column('simple-json', { nullable: true })
  metadata: {
    width?: number;
    height?: number;
    duration?: number;
    format?: string;
  };

  @Column()
  uploadedBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'uploadedBy' })
  uploader: User;
}
```

#### File Service

```typescript
// src/modules/file/file.service.ts
@Injectable()
export class FileService extends BaseCrudService<File> {
  constructor(
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,
    private readonly cloudinaryService: CloudinaryService,
  ) {
    super(fileRepository);
  }

  async uploadFile(
    file: Express.Multer.File,
    userId: string,
    folder?: string
  ): Promise<File> {
    try {
      // Upload to Cloudinary
      const uploadResult = await this.cloudinaryService.uploadImage(file, folder);

      // Determine file type
      const fileType = this.determineFileType(file.mimetype);

      // Save file record
      const fileRecord = this.fileRepository.create({
        originalName: file.originalname,
        fileName: uploadResult.public_id,
        mimeType: file.mimetype,
        size: file.size,
        type: fileType,
        url: uploadResult.secure_url,
        publicId: uploadResult.public_id,
        uploadedBy: userId,
        metadata: {
          width: uploadResult.width,
          height: uploadResult.height,
          format: uploadResult.format,
        },
      });

      return this.fileRepository.save(fileRecord);
    } catch (error) {
      throw new BadRequestException(`File upload failed: ${error.message}`);
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    const file = await this.fileRepository.findOne({ where: { id: fileId } });
    if (!file) {
      throw new NotFoundException('File not found');
    }

    try {
      // Delete from Cloudinary
      if (file.publicId) {
        await this.cloudinaryService.deleteImage(file.publicId);
      }

      // Delete from database
      await this.fileRepository.remove(file);
    } catch (error) {
      throw new BadRequestException(`File deletion failed: ${error.message}`);
    }
  }

  async getFilesByUser(userId: string): Promise<File[]> {
    return this.fileRepository.find({
      where: { uploadedBy: userId },
      order: { createdAt: 'DESC' },
    });
  }

  private determineFileType(mimeType: string): FileType {
    if (mimeType.startsWith('image/')) return FileType.IMAGE;
    if (mimeType.startsWith('video/')) return FileType.VIDEO;
    if (mimeType.startsWith('audio/')) return FileType.AUDIO;
    return FileType.DOCUMENT;
  }
}
```

---

## 🔧 Advanced Features

### Custom CRUD Implementation

The system includes a custom CRUD implementation that extends the standard NestJS CRUD functionality:

#### Custom CRUD Decorators

```typescript
// src/core/crud/crud/decorators/crud.decorator.ts
export function Crud(options: CrudOptions): ClassDecorator {
  return (target: any) => {
    // Store CRUD metadata
    Reflect.defineMetadata(CRUD_OPTIONS_TOKEN, options, target);

    // Generate routes
    CrudRoutesFactory.create(target, options);

    // Setup Swagger documentation
    SwaggerHelper.createApiDocs(target, options);

    // Apply interceptors
    UseInterceptors(CrudRequestInterceptor, CrudResponseInterceptor)(target);
  };
}
```

#### Custom Request Parser

```typescript
// src/core/crud/crud-request/request-query.parser.ts
export class RequestQueryParser {
  static create(): RequestQueryParser {
    return new RequestQueryParser();
  }

  parse(query: any, options?: ParserOptions): ParsedRequestParams {
    const parsed: ParsedRequestParams = {
      fields: this.parseFields(query.fields),
      filter: this.parseFilter(query.filter),
      or: this.parseFilter(query.or),
      join: this.parseJoin(query.join),
      sort: this.parseSort(query.sort),
      limit: this.parseLimit(query.limit, options?.maxLimit),
      offset: this.parseOffset(query.offset),
      page: this.parsePage(query.page),
      cache: this.parseCache(query.cache),
      includeDeleted: this.parseIncludeDeleted(query.includeDeleted),
    };

    return this.validateParsedQuery(parsed, options);
  }

  private parseFilter(filter: any): QueryFilter[] {
    if (!filter) return [];

    const filters = Array.isArray(filter) ? filter : [filter];
    return filters.map(f => this.parseFilterCondition(f));
  }

  private parseFilterCondition(condition: string): QueryFilter {
    const parts = condition.split('||');
    if (parts.length !== 3) {
      throw new BadRequestException('Invalid filter format');
    }

    return {
      field: parts[0],
      operator: parts[1] as QueryFilterOperator,
      value: this.parseFilterValue(parts[2], parts[1]),
    };
  }
}
```

### Middleware & Interceptors

#### Execution Time Middleware

```typescript
// src/core/middlewares/executionTime.middleware.ts
@Injectable()
export class ExecutionTimeMiddleware implements NestMiddleware {
  private readonly logger = new Logger(ExecutionTimeMiddleware.name);

  use(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();

    res.on('finish', () => {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Log slow requests
      if (duration > 1000) {
        this.logger.warn(
          `Slow request: ${req.method} ${req.originalUrl} - ${duration}ms`,
          {
            method: req.method,
            url: req.originalUrl,
            duration,
            userAgent: req.get('User-Agent'),
            ip: req.ip,
          }
        );
      }

      // Add execution time header
      res.setHeader('X-Execution-Time', `${duration}ms`);
    });

    next();
  }
}
```

### Security Implementation

#### JWT Auth Guard

```typescript
// src/core/auth/guards/jwt-auth.guard.ts
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(
      IS_PUBLIC_KEY,
      [context.getHandler(), context.getClass()]
    );

    if (isPublic) {
      return true;
    }

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    if (err || !user) {
      throw err || new UnauthorizedException('Invalid token');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new UnauthorizedException('User account is deactivated');
    }

    return user;
  }
}
```

#### Roles Guard

```typescript
// src/core/auth/guards/roles.guard.ts
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()]
    );

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    if (!user) {
      return false;
    }

    return requiredRoles.some(role => user.role?.name === role);
  }
}
```

### Performance Optimization

#### Caching Strategy

```typescript
// src/core/cache/cache.interceptor.ts
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(
    private readonly redisService: RedisService,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const cacheKey = this.generateCacheKey(request);

    // Only cache GET requests
    if (request.method !== 'GET') {
      return next.handle();
    }

    // Try to get from cache
    const cachedResult = await this.redisService.get(cacheKey);
    if (cachedResult) {
      return of(JSON.parse(cachedResult));
    }

    // Execute request and cache result
    return next.handle().pipe(
      tap(async (response) => {
        await this.redisService.set(
          cacheKey,
          JSON.stringify(response),
          300 // 5 minutes TTL
        );
      })
    );
  }

  private generateCacheKey(request: any): string {
    const { url, query, user } = request;
    return `cache:${url}:${JSON.stringify(query)}:${user?.id || 'anonymous'}`;
  }
}
```

---

## 📚 Implementation Guide

### Entity Creation

#### Step 1: Create Base Entity

```typescript
// src/modules/example/example.entity.ts
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { IsString, IsOptional, IsBoolean } from 'class-validator';

@Entity('examples')
export class Example extends DocEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsString()
  name: string;

  @Column('text', { nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @Column({ default: true })
  @IsBoolean()
  isActive: boolean;

  // Add relationships as needed
  @ManyToOne(() => User, user => user.examples)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  userId: string;
}
```

#### Step 2: Create DTOs

```typescript
// src/modules/example/dto/create-example.dto.ts
import { IsString, IsOptional, IsBoolean, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateExampleDto {
  @ApiProperty({ description: 'Example name' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Example description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Is active', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'User ID' })
  @IsUUID()
  userId: string;
}

// src/modules/example/dto/update-example.dto.ts
import { PartialType } from '@nestjs/swagger';
import { CreateExampleDto } from './create-example.dto';

export class UpdateExampleDto extends PartialType(CreateExampleDto) {}
```

### Service Implementation

#### Step 3: Create Service

```typescript
// src/modules/example/example.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Example } from './example.entity';
import { CreateExampleDto } from './dto/create-example.dto';
import { UpdateExampleDto } from './dto/update-example.dto';

@Injectable()
export class ExampleService extends BaseCrudService<Example> {
  constructor(
    @InjectRepository(Example)
    private readonly exampleRepository: Repository<Example>,
  ) {
    super(exampleRepository);
  }

  // Custom business logic methods
  async findByName(name: string): Promise<Example | null> {
    return this.exampleRepository.findOne({
      where: { name },
      relations: ['user'],
    });
  }

  async findByUser(userId: string): Promise<Example[]> {
    return this.exampleRepository.find({
      where: { userId },
      relations: ['user'],
      order: { createdAt: 'DESC' },
    });
  }

  async activateExample(id: string): Promise<Example> {
    const example = await this.exampleRepository.findOne({ where: { id } });
    if (!example) {
      throw new NotFoundException('Example not found');
    }

    example.isActive = true;
    return this.exampleRepository.save(example);
  }

  async deactivateExample(id: string): Promise<Example> {
    const example = await this.exampleRepository.findOne({ where: { id } });
    if (!example) {
      throw new NotFoundException('Example not found');
    }

    example.isActive = false;
    return this.exampleRepository.save(example);
  }
}
```

### Controller Setup

#### Step 4: Create Controller

```typescript
// src/modules/example/example.controller.ts
import {
  Controller,
  Get,
  Post,
  Patch,
  Param,
  Body,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Crud } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { JwtAuthGuard } from 'src/core/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/core/auth/guards/roles.guard';
import { Roles } from 'src/core/auth/decorators/roles.decorator';
import { Example } from './example.entity';
import { ExampleService } from './example.service';
import { CreateExampleDto } from './dto/create-example.dto';
import { UpdateExampleDto } from './dto/update-example.dto';

@ApiTags('Examples')
@ApiBearerAuth()
@Controller('examples')
@UseGuards(JwtAuthGuard, RolesGuard)
@Crud({
  model: { type: Example },
  dto: {
    create: CreateExampleDto,
    update: UpdateExampleDto,
  },
  query: {
    maxLimit: 100,
    cache: 2000,
    join: {
      user: { eager: true, exclude: ['password'] },
    },
  },
  routes: {
    only: ['getManyBase', 'getOneBase', 'createOneBase', 'updateOneBase'],
    getManyBase: {
      decorators: [Roles('admin', 'user')],
    },
    getOneBase: {
      decorators: [Roles('admin', 'user')],
    },
    createOneBase: {
      decorators: [Roles('admin')],
    },
    updateOneBase: {
      decorators: [Roles('admin', 'user')],
    },
  },
})
export class ExampleController extends BaseCrudController<Example> {
  constructor(public service: ExampleService) {
    super(service);
  }

  @Get('by-name/:name')
  @Roles('admin', 'user')
  @ApiOperation({ summary: 'Find example by name' })
  @ApiResponse({ status: 200, description: 'Example found' })
  async findByName(@Param('name') name: string) {
    return this.service.findByName(name);
  }

  @Get('by-user/:userId')
  @Roles('admin', 'user')
  @ApiOperation({ summary: 'Find examples by user' })
  @ApiResponse({ status: 200, description: 'Examples found' })
  async findByUser(@Param('userId') userId: string) {
    return this.service.findByUser(userId);
  }

  @Post(':id/activate')
  @Roles('admin')
  @ApiOperation({ summary: 'Activate example' })
  @ApiResponse({ status: 200, description: 'Example activated' })
  async activate(@Param('id') id: string) {
    return this.service.activateExample(id);
  }

  @Post(':id/deactivate')
  @Roles('admin')
  @ApiOperation({ summary: 'Deactivate example' })
  @ApiResponse({ status: 200, description: 'Example deactivated' })
  async deactivate(@Param('id') id: string) {
    return this.service.deactivateExample(id);
  }
}
```

### Module Configuration

#### Step 5: Create Module

```typescript
// src/modules/example/example.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Example } from './example.entity';
import { ExampleService } from './example.service';
import { ExampleController } from './example.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Example])],
  providers: [ExampleService],
  controllers: [ExampleController],
  exports: [ExampleService],
})
export class ExampleModule {}
```

#### Step 6: Register in Main Module

```typescript
// src/modules/main.module.ts
import { Module } from '@nestjs/common';
import { BaseModule } from 'src/core/base/base.module';
import { ExampleModule } from './example/example.module';
// ... other imports

@BaseModule({
  imports: [
    // ... other modules
    ExampleModule,
  ],
})
export class MainModules {}
```

---

## 🚀 Deployment & Operations

### Environment Configuration

#### Production Environment Variables

```bash
# Database Configuration
DB_HOST=production-db-host
DB_PORT=5432
DB_USER=app_user
DB_PASSWORD=secure_password
DB_NAME=production_db

# Redis Configuration
REDIS_COMMON=redis://redis-cluster:6379/0
REDIS_SOCKET=redis://redis-cluster:6379/1

# JWT Configuration
JWT_SECRET=super-secure-jwt-secret-key
JWT_EXPIRES_IN=24h

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=production-cloud
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Email Configuration
MAIL_HOST=smtp.sendgrid.net
MAIL_PORT=587
MAIL_USER=apikey
MAIL_PASS=sendgrid-api-key
MAIL_FROM=<EMAIL>

# Application Configuration
NODE_ENV=production
PORT=3000
API_PREFIX=api/v1

# Monitoring
LOG_LEVEL=info
SENTRY_DSN=your-sentry-dsn
```

### Database Migrations

#### Migration Commands

```bash
# Generate migration
npm run migration:generate -- --name CreateExampleTable

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert

# Show migration status
npm run migration:show
```

#### Example Migration

```typescript
// migrations/1234567890-CreateExampleTable.ts
import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateExampleTable1234567890 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'examples',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'userId',
            type: 'uuid',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'timestamp',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['userId'],
            referencedTableName: 'users',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            name: 'IDX_EXAMPLE_NAME',
            columnNames: ['name'],
          },
          {
            name: 'IDX_EXAMPLE_USER',
            columnNames: ['userId'],
          },
        ],
      }),
      true
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('examples');
  }
}
```

### Production Setup

#### Docker Configuration

```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:18-alpine AS production

WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package*.json ./

EXPOSE 3000

CMD ["node", "dist/main"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - postgres
      - redis
    networks:
      - app-network

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: production_db
      POSTGRES_USER: app_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### Monitoring & Logging

#### Health Check Endpoint

```typescript
// src/health/health.controller.ts
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import {
  HealthCheck,
  HealthCheckService,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
} from '@nestjs/terminus';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private memory: MemoryHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  @ApiOperation({ summary: 'Health check' })
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
    ]);
  }
}
```

This comprehensive documentation provides a complete guide to the Linh-Bui Enterprise NestJS Structure, covering all aspects from architecture and core modules to implementation guides and deployment strategies. The system represents a sophisticated, production-ready implementation that extends standard CRUD operations with enterprise-grade features including authentication, real-time communication, file management, and comprehensive audit logging.typescript
// Each service has a single responsibility
export class UserService extends BaseCrudService<User> {
  // Only handles User-related operations
}

export class AuthService extends BaseCrudService<Auth> {
  // Only handles authentication operations
}

export class MailerService {
  // Only handles email operations
}
```

#### Open/Closed Principle (OCP) ✅
```typescript
// Base classes are open for extension, closed for modification
export class BaseCrudService<T> extends TypeOrmCrudService<T> {
  // Base functionality that can be extended
}

export class UserService extends BaseCrudService<User> {
  // Extends base functionality without modifying it
  async findByEmail(email: string): Promise<User | null> {
    return this.repo.findOne({ where: { email } });
  }
}
```

#### Liskov Substitution Principle (LSP) ✅
```typescript
// Any BaseCrudService can be substituted for its parent
function processEntity<T>(service: BaseCrudService<T>): Promise<T[]> {
  return service.getMany(req); // Works with any implementation
}
```

#### Interface Segregation Principle (ISP) ✅
```typescript
// Separate interfaces for different concerns
interface IUserReader {
  findByEmail(email: string): Promise<User | null>;
  findById(id: string): Promise<User | null>;
}

interface IUserWriter {
  create(userData: CreateUserDto): Promise<User>;
  update(id: string, userData: UpdateUserDto): Promise<User>;
}
```

#### Dependency Inversion Principle (DIP) ✅
```typescript
// High-level modules depend on abstractions
export class AuthService extends BaseCrudService<Auth> {
  constructor(
    @InjectRepository(Auth) repo: Repository<Auth>,
    private readonly jwtService: JwtService,      // Abstraction
    private readonly userService: UserService,   // Abstraction
    private readonly tokenService: TokenService, //Raction
  ) {
    super(repo);
  }
}
```
