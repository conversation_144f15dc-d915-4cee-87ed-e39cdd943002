import {
  ConnectedSocket,
  MessageBody,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server } from 'socket.io';
import { EventsName } from './events.constant';
import { OnModuleInit } from '@nestjs/common';
import { getManager } from 'typeorm';
import { RedisService } from '../redis/redis.service';
import { COMMON_CACHE_NAME } from 'src/app.module';
import Redis from 'ioredis';
import { findIndex, remove } from 'lodash';
import { Job } from 'src/modules/job/job.entity';
import * as moment from 'moment';

@WebSocketGateway({ cors: true })
export class SocketGateWay implements OnModuleInit {
  private redisClient: Redis;
  constructor(readonly redisService: RedisService) {
    this.redisClient = this.redisService.getClient(COMMON_CACHE_NAME);
  }
  @WebSocketServer()
  server: Server;

  onModuleInit() {
    this.server.on('connection', (socket) => {
      console.log(
        'Total socket Id:',
        Array.from(this.server.sockets.adapter.sids.keys()).length,
        // '\nSockets:',
        // Array.from(this.server.sockets.adapter.sids.keys()),
      );
      // console.log('socket: ', socket.id, ' Connected');
    });
  }

  handleConnection(@ConnectedSocket() client: any) {
    client.join('calendar');
  }

  async handleDisconnect(@ConnectedSocket() client: any) {
    // console.log(`${client.id} DISCONNECTED`);

    //get user form socketId
    const userId = await this.redisClient.get(`SOCKET_USER:${client.id}`);
    const jobId = await this.redisClient.get(`SOCKET_JOB:${client.id}`);
    const socketOfUser =
      JSON.parse(
        (await this.redisClient.get(`USER_SOCKET:${userId}-${jobId}`)) || null,
      ) || [];

    const socketIds = [...socketOfUser].filter((s) => s !== client.id);

    if (socketIds.length === 0) {
      if (userId && jobId) {
        const existsJobIdRoom =
          (await this.redisClient.get(`JOB:${jobId}`)) || null;

        let listMember = [];
        if (existsJobIdRoom) {
          listMember = remove(JSON.parse(existsJobIdRoom), function (i) {
            return i.id !== userId;
          });

          await this.redisClient.set(
            `JOB:${jobId}`,
            JSON.stringify(listMember),
            'EX',
            1000 * 60 * 60 * 1,
          );
        }
        this.server.sockets.to(jobId).emit(EventsName.JOB_UPDATING, listMember);
        await this.redisClient.del([`USER_SOCKET:${userId}-${jobId}`]);
      }
    } else {
      await this.redisClient.set(
        `USER_SOCKET:${userId}-${jobId}`,
        JSON.stringify(socketIds),
      );
    }

    client.leave('calendar');
  }

  @SubscribeMessage(EventsName.JOB_START_UPDATE)
  async onJobStartUpdate(
    @ConnectedSocket() client: any,
    @MessageBody() body: { userId: string; jobId: string },
  ) {
    const user = await getManager('default')
      .getRepository('user')
      .findOne({
        where: { id: body.userId },
        relations: ['avatar'],
      });

    const socketIds =
      JSON.parse(
        (await this.redisClient.get(
          `USER_SOCKET:${body.userId}-${body.jobId}`,
        )) || null,
      ) || [];

    await this.redisClient.set(
      `USER_SOCKET:${body.userId}-${body.jobId}`,
      JSON.stringify([...socketIds, client.id]),
    );

    // handle user join room
    let existsJobIdRoom =
      (await this.redisClient.get(`JOB:${body.jobId}`)) || null;

    let listMember = [];
    if (existsJobIdRoom) {
      existsJobIdRoom = JSON.parse(existsJobIdRoom);

      listMember =
        findIndex(existsJobIdRoom, {
          id: body.userId,
        }) < 0
          ? [
              ...existsJobIdRoom,
              {
                id: user.id,
                name: user.name,
                email: user.email,
                avatar: user.avatar?.url,
              },
            ]
          : [...existsJobIdRoom];

      await this.redisClient.set(
        `JOB:${body.jobId}`,
        JSON.stringify(listMember),
      );
    } else {
      listMember = [
        {
          id: user.id,
          name: user.name,
          email: user.email,
          avatar: user.avatar?.url,
        },
      ];
      await this.redisClient.set(
        `JOB:${body.jobId}`,
        JSON.stringify(listMember),
      );
    }
    client.join(body.jobId);

    //handle one user have many tab edit job

    await this.redisClient.set(`SOCKET_USER:${client.id}`, body.userId);
    await this.redisClient.set(`SOCKET_JOB:${client.id}`, body.jobId);
    // let existsUserIdRoom =
    //   (await this.redisClient.get(`USER:${body.userId}`)) || null;
    // existsUserIdRoom = JSON.parse(existsUserIdRoom);

    // const listSocketIds =
    //   existsUserIdRoom?.length > 0
    //     ? [...existsUserIdRoom, client.id]
    //     : [client.id];

    // await this.redisClient.set(
    //   `USER:${body.userId}`,
    //   JSON.stringify(listSocketIds),
    // );

    // await this.redisClient.set(`JOB_USER:${body.userId}`, body.userId);

    this.server.sockets
      .to(body.jobId)
      .emit(EventsName.JOB_UPDATING, listMember);
  }

  @SubscribeMessage(EventsName.JOB_END_UPDATE)
  async onJobEndUpdate(
    @ConnectedSocket() client: any,
    @MessageBody() body: any,
  ) {
    client.leave(body.jobId);
    const socketOfUser =
      JSON.parse(
        (await this.redisClient.get(
          `USER_SOCKET:${body.userId}-${body.jobId}`,
        )) || null,
      ) || [];

    const socketIds = [...socketOfUser].filter((s) => s !== client.id);
    let listMember = [];
    const existsJobIdRoom =
      (await this.redisClient.get(`JOB:${body.jobId}`)) || null;
    if (socketIds.length === 0) {
      if (existsJobIdRoom) {
        listMember = remove(JSON.parse(existsJobIdRoom), function (i) {
          return i.id !== body.userId;
        });

        await this.redisClient.set(
          `JOB:${body.jobId}`,
          JSON.stringify(listMember),
        );
      }
      this.server.sockets
        .to(body.jobId)
        .emit(EventsName.JOB_UPDATING, listMember);
      await this.redisClient.del([`USER_SOCKET:${body.userId}-${body.jobId}`]);
    } else {
      listMember = JSON.parse(existsJobIdRoom);
      await this.redisClient.set(
        `USER_SOCKET:${body.userId}-${body.jobId}`,
        JSON.stringify(socketIds),
      );
    }

    // const existsJobIdRoom =
    //   (await this.redisClient.get(`JOB:${body.jobId}`)) || null;

    // let listMember = [];
    // if (existsJobIdRoom) {
    //   listMember = remove(JSON.parse(existsJobIdRoom), function (i) {
    //     return i.id !== body.userId;
    //   });

    //   await this.redisClient.set(
    //     `JOB:${body.jobId}`,
    //     JSON.stringify(listMember),
    //   );
    // }

    //handle one user have many tab edit job
    await this.redisClient.del([
      `SOCKET_USER:${client.id}`,
      `SOCKET_JOB:${client.id}`,
    ]);

    // let existsUserIdRoom =
    //   (await this.redisClient.get(`USER:${body.userId}`)) || null;
    // existsUserIdRoom = JSON.parse(existsUserIdRoom);

    // if (existsUserIdRoom?.length > 0) {
    //   const listSocketIds = remove(existsUserIdRoom, function (i) {
    //     return i !== client.id;
    //   });
    //   if (listSocketIds?.length > 0) {
    //     await this.redisClient.set(
    //       `USER:${body.userId}`,
    //       JSON.stringify(listSocketIds),
    //     );
    //   } else {
    //     await this.redisClient.del([`USER:${body.userId}`]);
    //   }
    // }

    this.server.sockets
      .to(body.jobId)
      .emit(EventsName.JOB_UPDATING, listMember);
  }

  async sendMessWhenUpdatedJob(
    userId: string,
    job: Job,
    socketId: string,
    action: string,
    userMoreTwoJob: any[],
    type?: string,
  ) {
    const schedules = [moment(job.startTime).format('DD-MM-YYYY')];
    console.log('job.startTime: ', moment(job.startTime).format('DD-MM-YYYY'));

    // const start = moment(job.startTime).startOf('day').toISOString();
    // const end = moment(job.endTime).endOf('day').toISOString();
    // const diff = moment(end).diff(moment(start).format('YYYY-MM-DD'), 'day');
    // for (let i = -1; i <= diff + 1; i++) {
    //   schedules.push(moment(start).add(i, 'day').format('DD-MM-YYYY'));
    // }

    // this.redisClient.del('JOB_LIST_CACHE');
    //
    // this.server.to(socketId).emit(EventsName.CALENDAR_RELOAD, {
    //   userId,
    //   jobId: job.id,
    //   schedules,
    //   socketId,
    // });

    Array.from(this.server.sockets.adapter.sids.keys()).forEach((sId) => {
      if (sId !== socketId) {
        this.server.to(sId).emit(EventsName.CALENDAR_RELOAD, {
          userId,
          jobId: job.id,
          schedules,
          socketId,
          job,
          action,
          userMoreTwoJob,
          type,
        });
      }
    });
    // this.server.emit(EventsName.CALENDAR_RELOAD, {
    //   userId,
    //   jobId: job.id,
    //   schedules,
    //   socketId,
    //   job,
    //   action,
    //   userMoreTwoJob,
    // });
  }

  sendNoticeArrangeJob(data: {
    jobId: string;
    pos: number;
    dayArrange: string;
    socketId?: string;
  }) {
    // this.redisClient.del('JOB_LIST_CACHE');
    // this.server.to(data.socketId).emit(EventsName.ARRANGE_JOB, data);
    this.server.emit(EventsName.ARRANGE_JOB, data);
  }

  sendNoticeWorkerAbsent({
    workerId,
    startTime,
    endTime,
  }: {
    workerId?: string;
    startTime: string;
    endTime: string;
  }) {
    const schedules = [];

    const start = moment(startTime).endOf('day').toISOString();
    const end = moment(endTime).endOf('day').toISOString();
    const diff = moment(end).diff(moment(start).format('YYYY-MM-DD'), 'day');
    for (let i = 0; i <= diff; i++) {
      schedules.push(moment(start).add(i, 'day').format('DD-MM-YYYY'));
    }

    this.server.emit(EventsName.RELOAD_UNASSIGNED_WORKER, {
      workerId,
      schedules,
    });
  }
}
