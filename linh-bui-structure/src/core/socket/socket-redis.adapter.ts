// import { INestApplicationContext } from '@nestjs/common';
// import { IoAdapter } from '@nestjs/platform-socket.io';
// import { Server, ServerOptions } from 'socket.io';
// import { createClient } from 'redis';
// import { createAdapter } from '@socket.io/redis-adapter';

// export class RedisIoAdapter extends IoAdapter {
//   private adapterConstructor: ReturnType<typeof createAdapter>;

//   async connectToRedis(): Promise<void> {
//     console.log(
//       `node-redis version is ${require('redis/package.json').version}`,
//     );
//     const pubClient = createClient({
//       host: 'localhost',
//       port: 5432,
//       password: process.env.REDIS_PASSWORD,
//     });
//     const subClient = pubClient.duplicate();

//     // await Promise.all([pubClient.connect(), subClient.connect()]);

//     this.adapterConstructor = createAdapter(pubClient, subClient);
//   }

//   createIOServer(port: number, options?: ServerOptions): any {
//     const server = super.createIOServer(port, options);
//     server.adapter(this.adapterConstructor);
//     return server;
//   }
// }
