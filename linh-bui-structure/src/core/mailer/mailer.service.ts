import { Injectable } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';

@Injectable()
export class MailerService {
  constructor() {
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  }

  async sendMail(data: Omit<sgMail.MailDataRequired, 'from'>) {
    const { to, subject, text, html } = data;

    return new Promise((resolve) => {
      sgMail
        .send({
          from: process.env.SENDGRID_MAIL_FROM,
          to,
          subject,
          text,
          html,
        })
        .then(() => {
          console.log('@@@: Send mail success');
          resolve({
            status: 'success',
          });
        })
        .catch((err) => {
          console.log('@@@ Send mail error', err['response']['body']);
          resolve({
            status: 'failed',
          });
        });
    });
  }
}
