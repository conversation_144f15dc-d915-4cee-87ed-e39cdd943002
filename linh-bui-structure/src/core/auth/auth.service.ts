import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Auth, AuthType } from './auth.entity';
import { Repository, getManager } from 'typeorm';
import { BaseCrudService } from '../base/base-crud.service';
import { SigninDTO } from './dto/signin.dto';
import { User } from 'src/modules/user/user.entity';
import { UnauthorizedException } from '../exception/core/unauthorized.exception';
import { JwtService } from '@nestjs/jwt';
import { Role } from 'src/modules/role/role.entity';
import { jwtConstants } from './constants';
import { TokenService } from 'src/modules/token/token.service';
import { md5 } from '../crypto/crypto.provider';
import { Token, TypeToken } from 'src/modules/token/token.entity';

import * as bcrypt from 'bcryptjs';
import { CommonHttpStatus } from '../common/common-http.status';
import { UserService } from 'src/modules/user/user.service';
import { InvalidCredentialsException } from '../exception/core/invalidCredentials.exception';
import { checkIfValidUUID } from '../common/common.utils';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { waMessage } from '../exception/exception.messages.contants';
import { MailerService } from '../mailer/mailer.service';
import * as handlebars from 'handlebars';
import { resetPasswordTemplate } from '../mailer/templates';

export class AuthSignedTokenPayload {
  id: string;
  role: Role;

  constructor(id: string, role: Role) {
    this.id = id;
    this.role = role;
  }

  getPayload() {
    return {
      id: this.id,
      ...(this.role ? { role: this.role } : {}),
    };
  }
}

@Injectable()
export class AuthService extends BaseCrudService<Auth> {
  constructor(
    @InjectRepository(Auth) repo: Repository<Auth>,

    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly tokenService: TokenService,
    private readonly userService: UserService,
    private readonly mailerService: MailerService,
  ) {
    super(repo);
  }

  async signin(req, res, dto: SigninDTO) {
    try {
      // check user exist
      const user = await this.userRepository.findOne({
        where: { email: dto['username'] },
        relations: ['role'],
      });

      // throw new BadRequestException('error');
      if (!user) {
        return new InvalidCredentialsException().sendWithRes(res);
      }

      const tokens = await this.generateToken(req, res, user);

      res.send(tokens);
    } catch (error) {
      throw new BadRequestException(error?.response || error);
    }
  }

  async me(req) {
    const me = await this.userService.findOne({
      where: { id: req.user['id'] },
      relations: ['role', 'avatar'],
      select: {
        role: {
          id: true,
          name: true,
        },
        avatar: {
          id: true,
          url: true,
        },
      },
    });

    return me;
  }

  async validateUser(username: string, password: string): Promise<any> {
    const user = await this.amIActive(username, true);
    let validUser = false;

    if (user?.auths?.[0]) {
      const authorized = bcrypt.compareSync(password, user.auths[0].password);

      if (authorized) {
        validUser = true;
      }
    }

    if (validUser) {
      const { auths, ...result } = user;
      return result;
    }

    return null;
  }
  async checkStatusLogin(userId: string) {
    const token = await this.tokenService.findOne({
      where: { user: { id: userId } },
    });

    return token;
  }
  async grantTokenWithRefreshToken(req, res) {
    const refreshToken = req.user?.refreshToken;
    const tokens = await this.refreshToken(req, res, refreshToken);
    res.send(tokens);
  }

  private async refreshToken(req, res, refreshToken: string) {
    try {
      const verifiedToken = await this.validateToken(refreshToken);
      const user = await this.amIActive(verifiedToken.id, false);
      if (user) {
        return this.generateToken(req, res, user);
      }
    } catch (err) {
      res.status(err?.status || CommonHttpStatus.BAD_REQUEST);
    }
  }

  async signout(req, res) {
    const userId = req.user.id;
    // remove token from db
    await this.tokenService.revokeToken(userId);

    res.status(CommonHttpStatus.OK).send(true);
  }

  private async validateToken(token: string) {
    if (!token) {
      throw new UnauthorizedException();
    }

    const verifiedToken = this.jwtService.verify(token);
    if (verifiedToken?.refreshToken) {
      // Check if token in database
      const tokenRecord: Token = await this.isTokenInDB(token);
      if (!tokenRecord?.id) {
        throw new UnauthorizedException();
      }
    }

    return verifiedToken;
  }

  private async isTokenInDB(token: string): Promise<Token> {
    const verifiedToken = this.jwtService.verify(token);
    const tokenRecord = await this.tokenService.findOne({
      where: [
        { tokenString: md5(token) },
        {
          type: verifiedToken['refreshToken']
            ? 'REFRESH_TOKEN'
            : 'ACCESS_TOKEN',
        },
      ],
    });

    if (!tokenRecord) {
      throw new BadRequestException();
    }

    return tokenRecord;
  }

  private async amIActive(
    userNameOrId: string,
    withAuth: boolean,
  ): Promise<any> {
    const user = checkIfValidUUID(userNameOrId)
      ? await this.userService.findById(userNameOrId, [
          ...(withAuth ? ['auths'] : []),
          'role',
        ])
      : await this.userService.findByEmail(userNameOrId, [
          ...(withAuth ? ['auths'] : []),
          'role',
        ]);

    return user;
  }

  // private checkIfValidUUID(str) {
  //   // Regular expression to check if string is a valid UUID
  //   const regexExp =
  //     /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi;

  //   return regexExp.test(str);
  // }

  private async generateToken(req, res, user: User) {
    const accessToken = this.jwtService.sign(
      new AuthSignedTokenPayload(user.id, user?.role).getPayload(),
      {
        expiresIn: jwtConstants.expire.access,
      },
    );

    const refreshToken = this.jwtService.sign(
      {
        refreshToken: true,
        id: user.id,
        role: user?.role,
      },
      { expiresIn: jwtConstants.expire.refresh * 1000 },
    );

    await this.tokenService.storeToken(
      req,
      md5(refreshToken),
      user.id,
      TypeToken.REFRESH_TOKEN,
      new Date(Date.now() + jwtConstants.expire.refresh * 1000),
    );

    return {
      accessToken,
      refreshToken,
    };
  }

  async setPassword(userId: string, password: string): Promise<boolean> {
    // check if user got Auth
    const userAuth = await this.repo.findOne({
      where: {
        user: {
          id: userId,
        },
        type: AuthType.SYSTEM,
      },
    });

    const salt = bcrypt.genSaltSync(10);
    const hashedPassword = bcrypt.hashSync(password, salt);
    if (userAuth) {
      // update password
      const { affected } = await this.repo.update(
        {
          id: userAuth.id,
        },
        { password: hashedPassword },
      );

      return affected > 0;
    } else {
      const result = await this.repo.insert({
        password: hashedPassword,
        type: AuthType.SYSTEM,
        user: {
          id: userId,
        },
      });

      return true;
    }
  }

  async delete(userId: string) {
    return this.repo.delete({ user: { id: userId } });
  }

  async forceResetPassword(
    userId: string,
    dto: ResetPasswordDto,
    req: Request,
  ) {
    // valid userId is uuid
    if (!checkIfValidUUID(userId)) {
      throw new BadRequestException({
        message: `validation failed. userId must be uuid`,
      });
    }
    // check not me
    if (userId === req['user']['id']) {
      throw new BadRequestException(
        waMessage.exception.cannotUpdateYourself.message,
      );
    }

    // Check user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role'],
    });
    if (!user) {
      throw new BadRequestException(waMessage.exception.userNotFound.message);
    }

    // Check role level
    if (req['user']['role']['level'] <= user.role.level) {
      throw new BadRequestException(waMessage.exception.invalidRole.message);
    }

    // Check if token exist in db
    const tokenRecord = await this.repo.findOne({
      relations: ['user', 'user.auths'],
      where: {
        user: { id: userId },
      },
    });

    // remove token from db
    await this.tokenService.revokeToken(userId);

    // let's update password
    const salt = bcrypt.genSaltSync(10);
    const hashedPassword = bcrypt.hashSync(dto.password, salt);

    await getManager().transaction(async (manager) => {
      const sysAuth = tokenRecord?.user?.auths?.filter(
        (a: Auth) => a.type === AuthType.SYSTEM,
      )?.[0];

      if (sysAuth) {
        await manager.save(Auth, {
          id: sysAuth?.id,
          password: hashedPassword,
        });
      } else {
        await manager.save(Auth, {
          user: {
            id: userId,
          },
          type: AuthType.SYSTEM,
          password: hashedPassword,
        });
      }

      // REMOVE old token
      if (tokenRecord.id) {
        await manager.softDelete(Token, tokenRecord.id);
      }

      // send mail when reset pass success
      const contentTemplate = handlebars.compile(resetPasswordTemplate);

      const html = contentTemplate({
        username: user.email,
        password: dto.password,
        publicKey: process.env.ESSENTIAL_URL,
      });

      await this.mailerService.sendMail({
        to: user.email,
        html,
        subject: 'Reset Password',
        text: 'Reset Password',
      });
    });
  }
}
