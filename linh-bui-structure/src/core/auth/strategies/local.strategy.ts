import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../auth.service';
import { InvalidCredentialsException } from 'src/core/exception/core/invalidCredentials.exception';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy, 'local') {
  constructor(private readonly authService: AuthService) {
    super();
  }

  async validate(username: string, password: string): Promise<any> {
    // Got rate-limit on auth module to protect from bruteforce
    const user = await this.authService.validateUser(username, password);
    if (!user) {
      throw new InvalidCredentialsException();
    }
    return user;
  }
}
