import { forwardRef, Global, Module, NestModule } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ConfigService } from '../config/config.service';
import { AuthController } from './auth.controller';
import { Auth } from './auth.entity';
import { AuthService } from './auth.service';
import { getJWTConfig } from './constants';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { TokenModule } from 'src/modules/token/token.module';
import { UserModule } from 'src/modules/user/user.module';
import { TokenService } from 'src/modules/token/token.service';
import { Token } from 'src/modules/token/token.entity';
import { User } from 'src/modules/user/user.entity';
import { JwtRefreshStrategy } from './strategies/jwt-refresh.strategy';
import { MailerService } from '../mailer/mailer.service';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([Auth, Token, User]),
    JwtModule.register({
      secret: 'JWT_SECRET_KEY',
      signOptions: { expiresIn: '60m' },
    }),
    PassportModule,
    TokenModule,
    forwardRef(() => UserModule),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    TokenService,
    LocalStrategy,
    JwtStrategy,
    JwtRefreshStrategy,
    MailerService,
  ],
  exports: [AuthService],
})
export class AuthModule implements NestModule {
  configure() {}
}
