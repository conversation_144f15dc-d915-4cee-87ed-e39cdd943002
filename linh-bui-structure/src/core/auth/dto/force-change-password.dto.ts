import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, Matches } from 'class-validator';
import { waMessage } from 'src/core/exception/exception.messages.contants';

export class ForceChangePasswordDto {
  @IsNotEmpty()
  @ApiProperty()
  newPassword: string;

  @IsNotEmpty()
  @ApiProperty()
  confirmPassword: string;

  @IsOptional()
  @ApiProperty({ default: false })
  requireChangePassword: boolean;
}
