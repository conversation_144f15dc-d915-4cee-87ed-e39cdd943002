import { IGrantPerm } from 'src/core/base/decorator/grantPerm.decorator';
import { IResourceGroup } from 'src/core/resource/resource.group';

export interface ResourceOptions {
  index?: number;
  alias?: string;
  prefix?: string;
  icon?: string;
  title?: string;
  noList?: boolean;
  customRepository?: string;
  noStatistic?: boolean;
  isPublic?: boolean;
  grantPerm?: IGrantPerm[];
  connection?: string;
  group?: IResourceGroup;
}
