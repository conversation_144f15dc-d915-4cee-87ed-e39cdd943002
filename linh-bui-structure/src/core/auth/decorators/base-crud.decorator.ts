import {
  applyDecorators,
  Controller,
  SetMetadata,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { BaseRouteName, Crud, CrudOptions, Feature } from 'src/core/crud/crud';
import { kebabCase } from 'lodash';
import { GrantPerm } from 'src/core/base/decorator/grantPerm.decorator';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { JwtGuard } from '../guards/jwt.guard';
import { ResourceOptions } from './resource.interface';
import { CrudMethods } from '../guards/acl-crud.guard';

export function BaseCrud(crudOptions: CrudOptions, options?: ResourceOptions) {
  const prefix = options?.prefix;
  const apiTag = options?.alias || crudOptions.model.type.name;
  const dangerousRoutes = ['replaceOneBase', 'createManyBase'];
  const only = crudOptions?.routes?.only?.map((i) => String(i));
  const exclude = crudOptions?.routes?.exclude?.map((i) => String(i));
  const defineRoutes =
    only ||
    CrudMethods.map((i) => String(i)).filter((i) => !exclude?.includes(i));

  if (
    crudOptions?.routes &&
    defineRoutes.filter((i) => dangerousRoutes.includes(i)).length > 0
  ) {
    throw new Error(
      `Dangerous routes are not allowed in admin's ${apiTag} crud`,
    );
  }

  // if no sort then sort by created date
  if (!crudOptions?.query?.sort) {
    crudOptions.query = {
      ...crudOptions?.query,
      sort: [
        {
          field: 'created',
          order: 'DESC',
        },
      ],
    };
  }

  // read resource routes
  let routes = crudOptions?.routes?.only || CrudMethods;
  if (crudOptions?.routes?.exclude?.length > 0) {
    routes = routes.filter(
      (r: BaseRouteName) => !crudOptions?.routes?.exclude.includes(r),
    );
  }

  return applyDecorators(
    SetMetadata('resource', {
      name: apiTag,
      routes,
      query: crudOptions?.query,
      ...(options ? { options } : {}),
    }),
    ApiBearerAuth(),
    ApiTags(apiTag),
    Controller(prefix ? `${prefix}/${kebabCase(apiTag)}` : kebabCase(apiTag)),
    Feature(kebabCase(apiTag)),
    Crud(crudOptions),
    // UseGuards(JwtAuthGuard),
    ...(options?.isPublic ? [] : [UseGuards(JwtAuthGuard)]),
  );
}
