import * as crypto from 'crypto';
// import * as forge from 'node-forge';

export const genUUID = (target: string, scope?: string): string => {
  const hashStatus = md5(`${target}@${scope}`);
  return `${hashStatus.substr(0, 8)}-${hashStatus.substr(
    8,
    4,
  )}-${hashStatus.substr(12, 4)}-${hashStatus.substr(
    16,
    4,
  )}-${hashStatus.substr(20, 12)}`;
};

export const sha256 = (content: string) => {
  return crypto.createHash('sha256').update(content, 'utf8').digest('hex');
};

export const hmacSHA256 = (content, key) => {
  return crypto.createHmac('sha256', key).update(content, 'utf8').digest('hex');
};

export const md5 = (content: string) => {
  return crypto.createHash('md5').update(content, 'utf8').digest('hex');
};

export const nodeRsaKeysInfo = () => {
  const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: 1024,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem',
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem',
    },
  });

  return {
    publicDer: publicKey,
    privateDer: privateKey,
  };
};

export const nodeRsaSign = (data, privateKey) => {
  try {
    const sign = crypto.createSign('SHA256');
    sign.write(data);
    sign.end();

    const key = crypto.createPrivateKey({
      key: privateKey,
      format: 'pem',
      type: 'pkcs8',
    });

    const signature = sign.sign(
      {
        key,
      },
      'base64',
    );
    return signature;
  } catch (err) {
    console.log('Sign Error', err);
  }
};

export const nodeRsaVerify = (data, publicKey, signature) => {
  try {
    const verify = crypto.createVerify('SHA256');
    verify.write(data);
    verify.end();

    const key = crypto.createPublicKey({
      key: publicKey,
      format: 'pem',
      type: 'spki',
    });
    return verify.verify(
      {
        key,
      },
      signature,
      'base64',
    );
  } catch (err) {
    console.log('Verify Error', err);
  }
};

// export const rsaEncrypt = (publicDer, data) => {
export const randomString = (length: number): string => {
  let result = '';
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};
