import { genUUID } from 'src/core/crypto/crypto.provider';

export const RoleBuiltIn = {
  SUPER_ADMIN: genUUID('Superadmin', 'Role'),
  ADMIN: genUUID('Admin', 'Role'),
  STAFF: genUUID('Staff', 'Role'),
};

export const SpecializaitonBuiltIn = {
  Drivers: genUUID('Drivers', 'Specializaiton'),
  Welders: genUUID('Welders', 'Specializaiton'),
  ScaffoldErector: genUUID('Scaffold Erector', 'Specializaiton'),
  ScaffoldSup: genUUID('Scaffold Sup', 'Specializaiton'),
  BCSS: genUUID('BCSS', 'Specializaiton'),
  MWAH: genUUID('MWAH', 'Specializaiton'),
  WAHSup: genUUID('WAH Sup', 'Specializaiton'),
  LiftingSup: genUUID('Lifting Sup', 'Specializaiton'),
  RiggerSignal: genUUID('Rigger & Signal', 'Specializaiton'),
  FireWatch: genUUID('Fire Watch', 'Specializaiton'),
  SMO: genUUID('SMO', 'Specializaiton'),
  FirstAid: genUUID('First Aid', 'Specializaiton'),
  ConfinedSpace: genUUID('Confined Space', 'Specializaiton'),
  ProcessPlant: genUUID('Process Plant', 'Specializaiton'),
  Electrical: genUUID('Electrical', 'Specializaiton'),
  ScissorLift: genUUID('Scissor Lift', 'Specializaiton'),
  Any: genUUID('Any', 'Specializaiton'),
};
