import { ObjectLiteral } from 'src/core/crud/util';
import {
  Query<PERSON><PERSON>s,
  QueryFilter,
  QueryJoin,
  QuerySort,
  SCondition,
} from '../types';

export interface ParsedRequestParams {
  fields: QueryFields;
  paramsFilter: QueryFilter[];
  authPersist: ObjectLiteral;
  search: SCondition;
  filter: QueryFilter[];
  or: QueryFilter[];
  join: QueryJoin[];
  sort: QuerySort[];
  limit: number;
  offset: number;
  page: number;
  cache: number;
  includeDeleted: number;
}
