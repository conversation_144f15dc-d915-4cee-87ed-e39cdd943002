export * from './crud-controller.interface';
export * from './crud-options.interface';
export * from './auth-options.interface';
export * from './params-options.interface';
export * from './query-options.interface';
export * from './routes-options.interface';
export * from './base-route.interface';
export * from './crud-request.interface';
export * from './model-options.interface';
export * from './create-many-dto.interface';
export * from './get-many-default-response.interface';
export * from './crud-global-config.interface';
export * from './dto-options.interface';
export * from './serialize-options.interface';
