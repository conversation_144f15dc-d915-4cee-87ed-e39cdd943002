import { BadRequestException } from '@nestjs/common';
import { QueryFilter } from '../crud-request';
import { ObjectLiteral, isNil } from '../util';

export function safeRequire<T = any>(path: string, loader?: () => T): T | null {
  try {
    /* istanbul ignore next */
    const pack = loader ? loader() : require(path);
    return pack;
  } catch (_) {
    /* istanbul ignore next */
    return null;
  }
}

function checkFilterIsArray(cond: QueryFilter, withLength?: boolean) {
  /* istanbul ignore if */
  if (
    !Array.isArray(cond.value) ||
    !cond.value.length ||
    (!isNil(withLength) ? withLength : false)
  ) {
    throw new BadRequestException(`Invalid column '${cond.field}' value`);
  }
}

export function getQueryStringAndParams({
  queryFilter,
  field,
  param,
  dbName = 'mysql',
}: {
  queryFilter: QueryFilter;
  field: string;
  param: string;
  dbName?: string;
}) {
  let str: string;
  let params: ObjectLiteral;

  const likeOperator =
    dbName === 'postgres' ? 'ILIKE' : /* istanbul ignore next */ 'LIKE';

  switch (queryFilter.operator) {
    case '$eq':
      str = `${field} = :${param}`;
      break;

    case '$ne':
      str = `${field} != :${param}`;
      break;

    case '$gt':
      str = `${field} > :${param}`;
      break;

    case '$lt':
      str = `${field} < :${param}`;
      break;

    case '$gte':
      str = `${field} >= :${param}`;
      break;

    case '$lte':
      str = `${field} <= :${param}`;
      break;

    case '$starts':
      str = `${field} LIKE :${param}`;
      params = { [param]: `${queryFilter.value}%` };
      break;

    case '$ends':
      str = `${field} LIKE :${param}`;
      params = { [param]: `%${queryFilter.value}` };
      break;

    case '$cont':
      str = `${field} LIKE :${param}`;
      params = { [param]: `%${queryFilter.value}%` };
      break;

    case '$excl':
      str = `${field} NOT LIKE :${param}`;
      params = { [param]: `%${queryFilter.value}%` };
      break;

    case '$in':
      checkFilterIsArray(queryFilter);
      str = `${field} IN (:...${param})`;
      break;

    case '$notin':
      checkFilterIsArray(queryFilter);
      str = `${field} NOT IN (:...${param})`;
      break;

    case '$isnull':
      str = `${field} IS NULL`;
      params = {};
      break;

    case '$notnull':
      str = `${field} IS NOT NULL`;
      params = {};
      break;

    case '$between':
      checkFilterIsArray(queryFilter, queryFilter.value.length !== 2);
      str = `${field} BETWEEN :${param}0 AND :${param}1`;
      params = {
        [`${param}0`]: queryFilter.value[0],
        [`${param}1`]: queryFilter.value[1],
      };
      break;

    // Case insensitive
    case '$eqL':
      str = `LOWER(${field}) = :${param}`;
      break;

    case '$neL':
      str = `LOWER(${field}) != :${param}`;
      break;

    case '$startsL':
      str = `LOWER(${field}) ${likeOperator} :${param}`;
      params = { [param]: `${queryFilter.value}%` };
      break;

    case '$endsL':
      str = `LOWER(${field}) ${likeOperator} :${param}`;
      params = { [param]: `%${queryFilter.value}` };
      break;

    case '$contL':
      str = `LOWER(${field}) ${likeOperator} :${param}`;
      params = { [param]: `%${queryFilter.value}%` };
      break;

    case '$exclL':
      str = `LOWER(${field}) NOT ${likeOperator} :${param}`;
      params = { [param]: `%${queryFilter.value}%` };
      break;

    case '$inL':
      checkFilterIsArray(queryFilter);
      str = `LOWER(${field}) IN (:...${param})`;
      break;

    case '$notinL':
      checkFilterIsArray(queryFilter);
      str = `LOWER(${field}) NOT IN (:...${param})`;
      break;

    /* istanbul ignore next */
    default:
      str = `${field} = :${param}`;
      break;
  }

  if (typeof params === 'undefined') {
    params = { [param]: queryFilter.value };
  }

  return { str, params };
}
