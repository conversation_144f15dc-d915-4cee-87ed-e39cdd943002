import { RoleBuiltIn } from 'src/core/database/type/index';
import { IGrantPerm } from 'src/core/base/decorator/grantPerm.decorator';

export const resourcePerm: IGrantPerm[] = [
  {
    roleId: RoleBuiltIn.SUPER_ADMIN,
    create: false,
    read: true,
    update: true,
    delete: false,
    list: true,
  },
  {
    roleId: RoleBuiltIn.ADMIN,
    create: false,
    read: true,
    update: false,
    delete: false,
    list: false,
  },
  {
    roleId: RoleBuiltIn.STAFF,
    create: false,
    read: true,
    update: false,
    list: false,
    delete: false,
  },
];
