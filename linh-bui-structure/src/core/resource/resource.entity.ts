import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { kebabCase } from 'lodash';
import {
  Column,
  Entity,
  Index,
  OneToMany,
  Tree,
  TreeChildren,
  TreeParent,
  VersionColumn,
} from 'typeorm';
import { DocEntity } from '../base/doc.entity';
import { md5 } from '../crypto/crypto.provider';
import { Exclude } from 'class-transformer';
import { AuditLog } from 'src/modules/audit-log/audit-log.entity';

export const getResourceId = (type: string): string => {
  const hashStatus = md5(`${kebabCase(type)}@resource`);
  return `${hashStatus.slice(0, 8)}-${hashStatus.slice(
    8,
    4,
  )}-${hashStatus.slice(12, 4)}-${hashStatus.slice(16, 4)}-${hashStatus.slice(
    20,
    12,
  )}`;
};

@Entity()
@Tree('nested-set')
@Index(['name', 'group'], { unique: true })
export class Resource extends DocEntity {
  static getResourceId(type: string) {
    return getResourceId(type);
  }

  @Column()
  @ApiProperty()
  name: string;

  @Column({ nullable: true })
  @ApiProperty({ required: false, readOnly: true })
  title: string;

  @Column({
    default: false,
  })
  @ApiProperty()
  list: boolean;

  @Column({
    default: false,
  })
  @ApiProperty()
  update: boolean;

  @Column({
    default: false,
  })
  @ApiProperty()
  create: boolean;

  @Column({
    default: false,
  })
  @ApiProperty()
  read: boolean;

  @Column({
    default: false,
  })
  @ApiProperty()
  delete: boolean;

  @Column({
    default: false,
  })
  @ApiProperty()
  @IsOptional()
  export: boolean;

  @Column({
    default: 0,
  })
  @ApiProperty({
    required: false,
    properties: {
      hide: { default: true },
    },
  })
  index: number;

  @Column({
    nullable: true,
  })
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  icon: string;

  @Column({
    default: false,
  })
  @ApiProperty({
    required: false,
    properties: {
      hide: { default: true },
    },
  })
  group: boolean;

  @Column({
    default: false,
  })
  @ApiProperty({
    properties: {
      hide: { default: true },
    },
  })
  deprecated: boolean;

  @Column({
    nullable: true,
  })
  @ApiProperty({
    required: false,
    properties: {
      hide: { default: true },
    },
  })
  customRepository: string;

  @Column({
    default: false,
  })
  @ApiProperty({
    required: false,
    properties: {
      hide: { default: true },
    },
  })
  noStatistic: boolean;

  @VersionColumn({
    default: 0,
  })
  @Exclude({ toPlainOnly: true })
  versionMeta?: number;

  // @OneToMany((__type) => Perm, (perm) => perm.resource, { nullable: false })
  // @ApiProperty({
  //   type: () => Perm,
  //   required: false,
  //   readOnly: true,
  //   example: null,
  //   properties: {
  //     hide: { default: true },
  //   },
  // })
  // perms: Perm[];

  @TreeChildren()
  @ApiProperty({
    type: () => [Resource],
    required: false,
    readOnly: true,
    example: null,
    properties: {
      hide: { default: true },
    },
  })
  children: Resource[];

  @TreeParent()
  @ApiProperty({
    type: () => Resource,
    example: null,
    properties: {
      hide: { default: true },
    },
  })
  parent: Resource;

  @Column({
    nullable: true,
  })
  @ApiProperty({
    properties: {
      hide: { default: true },
    },
  })
  @IsOptional()
  prefix: string;

  @OneToMany((_type) => AuditLog, (a) => a.resource)
  auditLogs: AuditLog[];
}
