import { Get, Req, UseInterceptors } from '@nestjs/common';
import {
  CrudController,
  CrudRequest,
  CrudRequestInterceptor,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';

import { UpdateResourceDto } from './dto/update-resource.dto';
import { Resource } from './resource.entity';
import { ResourceGroup } from './resource.group';
import { resourcePerm } from './resource.perm';
import { ResourceService } from './resource.service';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';

@BaseCrud(
  {
    model: {
      type: Resource,
    },
    dto: {
      update: UpdateResourceDto,
    },
    query: {
      filter: [
        {
          field: 'group',
          operator: '$eq',
          value: '0',
        },
      ],
      sort: [
        {
          field: 'index',
          order: 'ASC',
        },
      ],
      join: {
        children: {
          eager: true,
        },
      },
    },
    routes: {
      only: ['getManyBase', 'getOneBase', 'updateOneBase'],
    },
  },
  {
    // isPublic: true,
    // grantPerm: resourcePerm,
  },
)
export class ResourceController implements CrudController<Resource> {
  constructor(public service: ResourceService) {}
  get base(): CrudController<Resource> {
    return this;
  }

  @Override()
  getMany(@ParsedRequest() req: CrudRequest) {
    return this.service.getMany(req);
  }

  // @Get('/permitted')
  // @UseInterceptors(CrudRequestInterceptor)
  // getPermittedResources(@ParsedRequest() crudRequest: CrudRequest, @Req() req) {
  //   return this.service.getPermittedResources(req);
  // }

  // @Get('/allowed')
  // getAllowedResources(@Req() req) {
  //   return this.service.getAllowedResources(req);
  // }
}
