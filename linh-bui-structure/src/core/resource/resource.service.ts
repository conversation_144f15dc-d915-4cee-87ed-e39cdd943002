import { waMessage } from 'src/core/exception/exception.messages.contants';
import { RoleBuiltIn } from 'src/core/database/type';
import { UpdateResourceDto } from './dto/update-resource.dto';
import { UnauthorizedException } from './../exception/core/unauthorized.exception';
import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { GetManyDefaultResponse, CrudRequest } from 'src/core/crud/crud';

import { kebabCase, snakeCase } from 'lodash';
import { Repository, getManager, Not, In } from 'typeorm';
import { AuthService } from '../auth/auth.service';
import { Resource } from './resource.entity';
import { ResourceGroup } from './resource.group';
import { BaseCrudService } from '../base/base-crud.service';
import { UserService } from 'src/modules/user/user.service';

@Injectable()
export class ResourceService extends BaseCrudService<Resource> {
  constructor(
    @InjectRepository(Resource) repo: Repository<Resource>,
    private readonly authService: AuthService,
    private readonly userService: UserService,
    private readonly reflector: Reflector,
  ) {
    super(repo);
  }

  // private formatChildrenResource(reportSchemas, children) {
  //   const result = {
  //     isAcl: this.checkAcl(children.name),
  //     reportInterface: reportSchemas[children.name],
  //     ...children,
  //   };
  //   return result;
  // }

  // async getPermittedResources(
  //   req,
  // ): Promise<GetManyDefaultResponse<Resource> | Resource[]> {
  //   const { user, resources, reportSchemas } = await this.getResources(req);

  //   const myProfile = await this.userService.findOne(user['id'], {
  //     relations: ['role'],
  //   });

  //   const myPerms = await this.permService.find({
  //     where: {
  //       role: myProfile.role,
  //     },
  //     relations: ['role', 'resource'],
  //   });

  //   const myPermittedResources = myPerms
  //     .filter((p) => p.delete || p.read || p.update || p.create || p.list)
  //     .map((p) => p.resource.id);

  //   return resources.map((r) => {
  //     return {
  //       ...r,
  //       children: r.children
  //         .filter((child) => {
  //           return child.list && myPermittedResources.includes(child.id);
  //         })
  //         .sort((a, b) => a.index - b.index)
  //         .map((child) => this.formatChildrenResource(reportSchemas, child)),
  //     };
  //   });
  // }

  // async getAllowedResources(
  //   req,
  // ): Promise<GetManyDefaultResponse<Resource> | Resource[]> {
  //   const { resources, reportSchemas } = await this.getResources(req);

  //   return resources.map((r) => {
  //     return {
  //       ...r,
  //       children: r.children.map((child) =>
  //         this.formatChildrenResource(reportSchemas, child),
  //       ),
  //     };
  //   });
  // }

  // private checkAcl(resourceName) {
  //   const target = snakeCase(resourceName);
  //   let acl = false;

  //   try {
  //     const resourceRepo = getManager(WealthConnection.MAIN_DB).getRepository(
  //       target,
  //     );

  //     // Retrieve naga-acl metadata
  //     acl = Reflect.getMetadata(
  //       'naga-acl',
  //       resourceRepo.metadata?.target as Function,
  //     );
  //   } finally {
  //     return acl;
  //   }
  // }

  // private async getResources(req) {
  //   const adminModules = (global as any).AdminModules;
  //   const controllers = [];

  //   adminModules.forEach((m) => {
  //     const mControllers = this.reflector.get('controllers', m) || [];
  //     controllers.push(...mControllers);
  //   });

  //   const reportControllers = controllers
  //     .map((c) => ({
  //       name: kebabCase(c.name.slice(0, c.name.indexOf('Controller'))),
  //       structure: this.reflector.get('reportInterface', c),
  //     }))
  //     .filter((i) => !!i.structure);

  //   const reportSchemas = reportControllers.reduce((acc, cur) => {
  //     const reportInterface: IReportInterface = cur.structure;

  //     const filter = reportInterface.filter?.map((filter) => {
  //       return {
  //         ...filter,
  //         default:
  //           typeof filter.default === 'function'
  //             ? filter.default()
  //             : filter.default,
  //       };
  //     });

  //     return {
  //       ...acc,
  //       [cur.name]: {
  //         filter,
  //         response: reportInterface.response,
  //         hideOnMenu: reportInterface.hideOnMenu,
  //       },
  //     };
  //   }, {});

  //   const user = this.authService.decode(req?.cookies?.accessToken);

  //   if (!user) {
  //     throw new UnauthorizedException();
  //   }

  //   const resources = await this.repo
  //     .find({
  //       where: {
  //         parent: {
  //           id: ResourceGroup.ROOT.id,
  //         },
  //         name: Not(ResourceGroup.OTHERS.name),
  //       },
  //       relations: ['children'],
  //       order: {
  //         index: 'ASC',
  //       },
  //     })
  //     .then((data) => {
  //       if (!isEnableCheatMode) {
  //         return data.map(({ children, ...resource }) => {
  //           return {
  //             ...resource,
  //             children: children.filter(
  //               (resource) =>
  //                 ![
  //                   kebabCase(CheatMode.name),
  //                   kebabCase(CheatModeApply.name),
  //                   kebabCase(CheatModeTemplate.name),
  //                   kebabCase(CheatModeLink.name),
  //                 ].includes(resource.name),
  //             ),
  //           };
  //         });
  //       }
  //       return data;
  //     });

  //   return { resources, reportSchemas, user };
  // }
}
