import { CacheModule, Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ResourceController } from './resource.controller';
import { Resource } from './resource.entity';
import { ResourceService } from './resource.service';
import { UserModule } from 'src/modules/user/user.module';

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([Resource]), UserModule],
  controllers: [ResourceController],
  providers: [ResourceService],
  exports: [ResourceService],
})
export class ResourceModule {}
