import { BadRequestException, ForbiddenException } from '@nestjs/common';
import {
  CreateManyDto,
  CrudRequest,
  GetManyDefaultResponse,
} from 'src/core/crud/crud';
import { DeepPartial, Repository } from 'typeorm';
import { isEmpty, keys } from 'lodash';
import { TypeOrmCrudService } from '../crud/crud-typeorm';
import { DuplicatedEntryException } from '../exception/core/duplicated-entry.exception';
import { NotNullEntryException } from '../exception/core/not-null-entry.exception';

export class BaseCrudService<T> extends TypeOrmCrudService<T> {
  public entityUserPerm;
  private currentMetadata;
  private currentEntity;
  private currentPropMaps;
  constructor(repo: Repository<T>) {
    super(repo);

    // Get current resource metadata
    this.currentMetadata = this.repo.metadata;
    this.currentEntity = this.currentMetadata?.target;
    this.currentPropMaps = this.repo.metadata.propertiesMap;
  }

  /*******************************************************************************
   * NAME :            getMany(req: CrudRequest): Promise<T[]>
   * DESCRIPTION :     Get many base CRUD method
   *******************************************************************************/
  async getMany(req: CrudRequest): Promise<GetManyDefaultResponse<T> | T[]> {
    try {
      return super.getMany(req);
    } catch (error) {
      this.filterWriteException(error);
    }
  }

  /*******************************************************************************
   * NAME :            getOne(req: CrudRequest): Promise<T>
   * DESCRIPTION :     Get one base CRUD method
   *******************************************************************************/
  async getOne(req: CrudRequest): Promise<T> {
    try {
      return super.getOne(req);
    } catch (error) {
      this.filterWriteException(error);
    }
  }

  /*******************************************************************************
   * NAME :            deleteOne(req: CrudRequest): Promise<void | T>
   * DESCRIPTION :     Delete one CRUD method
   *******************************************************************************/
  async deleteOne(req: CrudRequest): Promise<void | T> {
    const target = await this.getOne(req);
    // store for soft-deleting as target id will be removed afterd entityManager.remove
    const targetId = target?.['id'];

    const queryRunner = this.repo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction('SERIALIZABLE');

    try {
      // Check can remove first for has relation entities?
      const deletedEntity = await queryRunner.manager.remove(
        this.repo.metadata.tableName,
        target,
      );
      if (deletedEntity) {
        await queryRunner.rollbackTransaction();
        await queryRunner.startTransaction('SERIALIZABLE');
        await queryRunner.manager.softDelete(
          this.repo.metadata.tableName,
          targetId,
        );
      }
      // commit transaction now:
      await queryRunner.commitTransaction();
    } catch (err) {
      // since we have errors let's rollback changes we made
      await queryRunner.rollbackTransaction();
      // ER_ROW_IS_REFERENCED_2
      // if (err?.errno === 1451) {
      //   const parsedError = err?.sqlMessage?.match(
      //     /.*\(\`.*\`.(\`.*\`), CONSTRAINT \`/i,
      //   );
      //   throw new ReferencedEntryDeletionException(parsedError?.[1]);
      // }

      throw err;
    } finally {
      // you need to release query runner which is manually created:
      await queryRunner.release();
    }
  }

  /*******************************************************************************
   * NAME :            createMany
   * DESCRIPTION :     Forbidden method
   *******************************************************************************/
  createMany(
    _req: CrudRequest,
    _dto: CreateManyDto<DeepPartial<T>>,
  ): Promise<T[]> {
    throw new ForbiddenException();
  }

  /*******************************************************************************
   * NAME :            createOne(req: CrudRequest, dto: DeepPartial<T>): Promise<T>
   * DESCRIPTION :     Create one base CRUD method
   * NOTES :           - Validate merchant permissions
   *                   - Create new record (with merchantId if is merchant)
   *******************************************************************************/
  async createOne(req: CrudRequest, dto: DeepPartial<T>): Promise<T> {
    try {
      return await super.createOne(req, dto);
    } catch (error) {
      this.filterWriteException(error, dto);
    }
  }

  /*******************************************************************************
   * NAME :            updateOne(req: CrudRequest, dto: DeepPartial<T>): Promise<T>
   * DESCRIPTION :     Update one base CRUD method
   *******************************************************************************/
  async updateOne(req: CrudRequest, dto: DeepPartial<T>): Promise<T> {
    try {
      return await super.updateOne(req, dto);
    } catch (error) {
      this.filterWriteException(error, dto);
    }
  }

  // Req Param Utility
  reqParams(req) {
    return {
      reset: () => {
        req.parsed.paramsFilter = [];
        req.parsed.search = {};
        req.parsed.filter = [];
      },
      paramsFilter: (field) => ({
        get: () =>
          req.parsed.paramsFilter.find((p) => p['field'] === field).value,
        set: (value) => {
          req.parsed.paramsFilter.find((p) => p['field'] === field).value =
            value;
        },
      }),
      search: (field) => ({
        set: (value) => {
          req.parsed.search['$and'].find((p) => p[field])[field] = {
            $eq: value,
          };
        },
        push: (value) => {
          req.parsed.search['$and'].push({
            [field]: value,
          });
        },
      }),
    };
  }

  public handleDuplicateEntryError(error, dto) {
    const errorMatchedData = error?.detail?.match(
      /Key (.*)=(.*) already exists./i,
    );

    // Get `Duplicate field`
    const errorMsgKey = errorMatchedData?.[1]?.replace(/[\(,\)]/g, '');

    throw new DuplicatedEntryException(errorMsgKey);
  }

  public filterWriteException(error, dto = {}) {
    // Handle Column cannot be null
    if (+error?.code === 23502) {
      throw new NotNullEntryException(error?.column);
    }

    // Handle Duplicate entry by CREATE/UPDATE resource
    if (+error?.code === 23505 && !isEmpty(dto)) {
      try {
        this.handleDuplicateEntryError(error, dto);
      } catch (dupEntryErr) {
        throw dupEntryErr;
      }
    }

    throw error;
  }
}
