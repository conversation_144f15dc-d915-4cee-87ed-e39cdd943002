import { HttpException } from '@nestjs/common';
import { CommonHttpStatus } from '../../common/common-http.status';
import { waMessage } from '../exception.messages.contants';

export class DuplicatedEntryException extends HttpException {
  constructor(msg: string) {
    super(
      {
        message: waMessage.exception.duplicatedEntryException.message,
        args: {
          key: msg,
        },
      },
      CommonHttpStatus.NOT_ACCEPTABLE,
    );
  }
}
