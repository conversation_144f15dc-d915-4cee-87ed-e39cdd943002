import { HttpException } from '@nestjs/common';
import { CommonHttpStatus } from '../../common/common-http.status';
import { waMessage } from '../exception.messages.contants';

export class NotNullEntryException extends HttpException {
  constructor(msg: string) {
    super(
      {
        message: waMessage.exception.notNullEntryException.message,
        args: {
          key: msg,
        },
      },
      CommonHttpStatus.NOT_ACCEPTABLE,
    );
  }
}
