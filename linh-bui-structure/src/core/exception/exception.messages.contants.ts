export const waMessage = {
  exception: {
    duplicatedEntryException: {
      key: 'wa.exception.duplicatedEntryException',
      message: 'Duplicated %{key}',
    },
    unAuthorized: {
      key: 'wa.exception.unAuthorized',
      message: 'UnAuthorized',
    },
    notNullEntryException: {
      key: 'wa.exception.notNullEntryException',
      message: '%{key} cannot be null',
    },
    InvalidPassword: {
      key: 'wa.exception.notNullEntryException',
      message: '%{key} cannot be null',
    },
    roleNotFound: {
      key: 'wa.exception.roleNotFound',
      message: 'Role is not found',
    },
    invalidRole: {
      key: 'wa.exception.invalidRole',
      message:
        'You cannot create or update users with the same level or higher!',
    },
    cannotUpdateYourself: {
      key: 'wa.exception.cannotUpdateYourself',
      message:
        'Oops!! you cannot update your self, please update via your profile or contact our customer support',
    },
    userNotFound: {
      key: 'wa.exception.userNotFound',
      message: 'User Not Found',
    },
    notDelMyUser: {
      key: 'wa.exception.notDelMyUser',
      message: 'You cannot delete your account',
    },
    notDelSameLevelOrHigher: {
      key: 'wa.exception.notDelSameLevelOrHigher',
      message: 'You cannot delete users with the same level or higher!',
    },
    invalidCredential: {
      key: 'wa.exception.invalidCredential',
      message: 'Invalid credentials',
    },
    invalidImage: {
      key: 'wa.exception.invalidImage',
      message: 'Only accept file format (.png, .jpeg, .jpg, .gif, .bmp)',
    },
  },
};
