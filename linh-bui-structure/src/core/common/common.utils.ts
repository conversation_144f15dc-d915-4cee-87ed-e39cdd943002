import { QueryFilter, RequestQueryException } from '../crud/crud-request';
import { isNil } from '../crud/util';

export function parseFilters(filters: string[]) {
  filters = Array.isArray(filters) ? filters : [filters];
  return filters.reduce((obj, filter) => {
    const [key, op, value] = filter.split('||');
    obj[key] = { op: op, value: value };
    return obj;
  }, {});
}

export function parseSearch(search: string) {
  try {
    if (isNil(search)) {
      return undefined;
    }

    const data = JSON.parse(search);

    if (!isObject(data)) {
      throw new Error();
    }

    return data;
  } catch (err) {
    throw new RequestQueryException('Invalid search param. JSON expected');
  }
}

// export function paramParser(name: string): QueryFilter {
//   validateParamOption(this._paramsOptions, name);
//   const option = this._paramsOptions[name];

//   if (option.disabled) {
//     return undefined;
//   }

//   let value = this._params[name];

//   switch (option.type) {
//     case 'number':
//       value = this.parseValue(value);
//       validateNumeric(value, `param ${name}`);
//       break;
//     case 'uuid':
//       validateUUID(value, name);
//       break;
//     default:
//       break;
//   }

//   return { field: option.field, operator: '$eq', value };
// }

export const getCustomPaginationLimit = (limitRequest?: number) => {
  if (!limitRequest || limitRequest <= 0 || limitRequest > 300) {
    return 300;
  }

  return limitRequest;
};

export const handlePaging = async ({
  masterQuery,
  offset,
  page,
  limit,
  sort,
  transformer,
}: any) => {
  const total = (await masterQuery.execute()).length;

  if (sort) {
    if (sort.field !== 'id') {
      masterQuery?.orderBy(sort.field, sort.order);
    }
  }

  const customLimit = getCustomPaginationLimit(limit);

  if (limit) {
    masterQuery.limit(customLimit);
  }

  if (+page || offset) {
    masterQuery.offset((page - 1) * customLimit || offset || 0);
  }

  const dataQuery = await masterQuery.execute();

  const data = transformer ? transformer(dataQuery) : dataQuery;

  if (page != null || offset != null) {
    return {
      data,
      count: data.length || customLimit,
      total,
      page: +page || 1,
      pageCount: Math.ceil(total / customLimit),
    };
  }

  return data;
};

export const customHandlePaging = async ({
  array,
  offset,
  page,
  limit,
  transformer,
}: any) => {
  const total = array.length;
  const customLimit = getCustomPaginationLimit(limit);

  const dataPaginate = array.slice(
    (page - 1) * customLimit,
    page * customLimit,
  );
  const data = transformer ? transformer(dataPaginate) : dataPaginate;
  if (page != null || offset != null) {
    return {
      data,
      count: data.length,
      total,
      page: +page || 1,
      pageCount: Math.ceil(total / customLimit),
    };
  }

  return transformer ? transformer(array) : array;
};

export const checkIfValidUUID = (str) => {
  // Regular expression to check if string is a valid UUID
  const regexExp =
    /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi;

  return regexExp.test(str);
};

export const isDate = (d) => d instanceof Date;
export const isEmpty = (o) => Object.keys(o).length === 0;
export const isObject = (o) => o != null && typeof o === 'object';
export const hasOwnProperty = (o, ...args) =>
  Object.prototype.hasOwnProperty.call(o, ...args);
export const isEmptyObject = (o) => isObject(o) && isEmpty(o);

const updatedDiff = (lhs, rhs) => {
  if (isDate(lhs) || isDate(rhs)) {
    if (new Date(lhs).valueOf() == new Date(rhs).valueOf()) return {};
    return rhs;
  }
  if (lhs === rhs) return {};

  if (!isObject(lhs) || !isObject(rhs)) return rhs;

  const l = lhs;
  const r = rhs;

  if (isDate(l) || isDate(r)) {
    if (new Date(l).valueOf() == new Date(r).valueOf()) return {};
    return r;
  }

  return Object.keys(r).reduce((acc, key) => {
    if (hasOwnProperty(l, key)) {
      const difference = updatedDiff(l[key], r[key]);

      // If the difference is empty, and the lhs is an empty object or the rhs is not an empty object
      if (
        isEmptyObject(difference) &&
        !isDate(difference) &&
        (isEmptyObject(l[key]) || !isEmptyObject(r[key]))
      ) {
        return acc; // return no diff
      }

      if (typeof difference !== 'object') {
        acc[key] = { from: l[key], to: difference };
      } else {
        acc[key] = difference;
      }
      return acc;
    }

    return acc;
  }, {});
};

const deletedDiff = (lhs, rhs) => {
  if (lhs === rhs || !isObject(lhs) || !isObject(rhs)) return {};

  const l = lhs;
  const r = rhs;

  return Object.keys(l).reduce((acc, key) => {
    if (hasOwnProperty(r, key)) {
      const difference = deletedDiff(l[key], r[key]);

      if (isObject(difference) && isEmpty(difference)) return acc;
      if (typeof difference !== 'object') {
        acc[key] = { from: l[key] };
      } else {
        acc[key] = difference;
      }
      return acc;
    } else {
      acc[key] = { from: l[key] };
    }

    return acc;
  }, {});
};

const addedDiff = (lhs, rhs) => {
  if (lhs === rhs || !isObject(lhs) || !isObject(rhs)) return {};

  const l = lhs;
  const r = rhs;

  return Object.keys(r).reduce((acc, key) => {
    if (hasOwnProperty(l, key)) {
      const difference = addedDiff(l[key], r[key]);

      if (isObject(difference) && isEmpty(difference)) return acc;

      if (typeof difference !== 'object') {
        acc[key] = { to: difference };
      } else {
        acc[key] = difference;
      }
      return acc;
    } else {
      acc[key] = { to: r[key] };
    }

    return acc;
  }, {});
};

export const detailedDiff = (lhs, rhs) => ({
  added: addedDiff(lhs, rhs),
  deleted: deletedDiff(lhs, rhs),
  updated: updatedDiff(lhs, rhs),
});
