import {
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';

@Catch()
export class AllExceptionsFilter extends BaseExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest();
    const response = ctx.getResponse();
    const statusCode =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;
    const error =
      exception instanceof HttpException
        ? exception.message || exception.message
        : 'Internal server error';
    const responseError =
      exception instanceof HttpException ? exception.getResponse() : {};
    const message = responseError?.['message'];

    if (statusCode === 400) {
      response.status(statusCode).json({
        statusCode,
        message: Array.isArray(message) ? message[0] : message,
        error: 'Bad Request',
      });
    }

    super.catch(exception, host);
  }
}
