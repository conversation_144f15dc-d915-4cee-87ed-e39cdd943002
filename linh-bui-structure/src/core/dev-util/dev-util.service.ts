import { Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { kebabCase } from 'lodash';

import {
  Connection,
  EntityMetadata,
  getManager,
  In,
  Migration,
  Not,
} from 'typeorm';

import { genUUID, md5 } from '../crypto/crypto.provider';
import { Resource } from '../resource/resource.entity';
import { ResourceGroup } from '../resource/resource.group';

interface IDevUtilService {
  migrateDatabase(): Promise<Migration[]>;
  syncResource(): Promise<void>;
}

@Injectable()
export class DevUtilService implements IDevUtilService {
  constructor(
    private readonly connection: Connection,
    private readonly reflector: Reflector,
  ) {}

  async migrateDatabase(): Promise<Migration[]> {
    return this.connection.runMigrations({
      transaction: 'each',
    });
  }

  async syncResource(): Promise<void> {
    const mainModules = (global as any).mainModules;
    const controllers = [];

    mainModules.forEach((m) => {
      const mControllers = this.reflector.get('controllers', m) || [];
      controllers.push(...mControllers);
    });

    const schemas = controllers
      .map((c) => this.reflector.get('resource', c))
      .filter((i) => !!i)
      .map((resource) => {
        return {
          id: genUUID(kebabCase(resource.name), 'resource'),
          name: kebabCase(resource.name),
          create: resource.routes?.includes('createOneBase'),
          update: resource.routes?.includes('updateOneBase'),
          list:
            !resource?.options?.noList &&
            (resource.routes?.includes('getManyBase') ||
              resource.routes?.includes('exportBase')),
          read:
            resource.routes?.includes('getOneBase') ||
            resource.routes?.includes('getManyBase'),
          delete: resource.routes?.includes('deleteOneBase'),
          title: resource?.title || resource.name,
          ...resource?.options,
        };
      });

    await this.connection.transaction(async (entityManager) => {
      // const resourceVersion =
      //   await this.settingService.getSystemSettingForBackendLogicDontUseItIncorrectly(
      //     'resource_version',
      //     entityManager,
      //   );

      const versionMeta = +1 + 1;
      const resourceHash = md5('ads');
      // await this.settingService.getSystemSettingForBackendLogicDontUseItIncorrectly(
      //   'resource_hash',
      //   entityManager,
      // );

      const newResourceHash = md5(
        [ResourceGroup.ROOT.id, JSON.stringify(schemas)].join(),
      );

      if (resourceHash === newResourceHash) {
        return;
      }

      // Create root group if not exist
      await entityManager
        .createQueryBuilder()
        .insert()
        .into(Resource)
        .values({
          index: -1,
          versionMeta,
          group: true,
          ...ResourceGroup.ROOT,
        })
        .orUpdate(['versionMeta'], ['id'])
        .execute();

      const groups = Object.values(ResourceGroup).filter((i) => !i['root']);
      // Update group to db
      await entityManager
        .createQueryBuilder()
        .insert()
        .into(Resource)
        .values(
          groups.map((group) => ({
            id: genUUID(kebabCase(group['name']), 'resource-group'),
            name: kebabCase(group['name']),
            title: group['title'] || group['name'],
            index: group.index,
            icon: group['icon'],
            versionMeta,
            group: true,
            parent: {
              id: ResourceGroup.ROOT.id,
            },
          })),
        )
        .orUpdate(
          [
            'name',
            'versionMeta',
            'index',
            'icon',
            'title',
            'group',
            'parentId',
          ],
          ['name', 'group'],
        )
        .execute();

      const resources = Array.from(new Set(schemas));
      await entityManager
        .createQueryBuilder()
        .insert()
        .into(Resource)
        .values(
          resources.map((s) => {
            return {
              ...s,
              versionMeta,
              group: false,
              parent: {
                id: genUUID(kebabCase(s.group['name']), 'resource-group'),
              },
            };
          }),
        )
        .orUpdate(
          [
            'versionMeta',
            'parentId',
            'create',
            'update',
            'list',
            'read',
            'index',
            'delete',
            'title',
            'icon',
          ],
          ['name', 'group'],
        )
        .execute();

      // Purge obsoleted resource
      const currentResources = await entityManager.find(Resource, {
        where: {
          versionMeta,
        },
        select: ['id'],
      });

      // await entityManager.delete(Perm, {
      //   resource: {
      //     id: Not(In(currentResources.map((i) => i.id))),
      //   },
      // });

      await entityManager.delete(Resource, {
        id: Not(In(currentResources.map((i) => i.id))),
      });
      // Purge obsoleted resource completed

      // update new resource_version
      // await this.settingService.updateSystemSettingForBackendLogicDontUseItIncorrectly(
      //   'resource_version',
      //   versionMeta.toString(),
      //   entityManager,
      // );

      // update new resource_hash
      // await this.settingService.updateSystemSettingForBackendLogicDontUseItIncorrectly(
      //   'resource_hash',
      //   newResourceHash,
      //   entityManager,
      // );
    });
  }
}
