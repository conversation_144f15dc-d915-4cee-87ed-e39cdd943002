import { Controller } from '@nestjs/common';
import { DevUtilService } from './dev-util.service';

@Controller('dev-util')
export class DevUtilController {
  constructor(private readonly devUtilService: DevUtilService) {}

  // @Post('sync/resource')
  // @UseGuards(SuperGuard)
  // async syncResource() {
  //   return this.devUtilService.syncResource();
  // }

  // @Post('sync/perm')
  // @UseGuards(SuperGuard)
  // async syncPerm() {
  //   return this.devUtilService.syncPerm();
  // }
}
