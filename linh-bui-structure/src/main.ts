import { HttpAdapterHost, NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as pkg from '../package.json';
import { ValidationPipe } from '@nestjs/common';
import { AllExceptionsFilter } from './core/common/all-exceptions.filter';
import { createConnection } from 'typeorm';
import { Resource } from './core/resource/resource.entity';
import { dataSourceOptions } from './core/database/typeorm.config';
// import { RedisIoAdapter } from './core/socket/socket-redis.adapter';
// import { config } from 'aws-sdk';

async function bootstrap() {
  await createConnection(dataSourceOptions);

  const app = await NestFactory.create(AppModule, { cors: true });

  const baseDocument = new DocumentBuilder()
    .setTitle(pkg.name)
    .setDescription(pkg.description)
    .setVersion(pkg.version)
    .addBearerAuth();
  app.useGlobalPipes(new ValidationPipe());

  // enable webSockets
  // const redisIoAdapter = new RedisIoAdapter(app);
  // await redisIoAdapter.connectToRedis();

  // app.useWebSocketAdapter(redisIoAdapter);

  // await .getRepository(RegisterCodeEntity)
  const reflector: Reflector = new Reflector();
  const MainModules = require('./modules/main.module').MainModules;
  const mainModules = reflector.get('modules', MainModules);

  (global as any).mainModules = mainModules;

  // Exception global custom
  const { httpAdapter } = app.get(HttpAdapterHost);
  app.useGlobalFilters(new AllExceptionsFilter(httpAdapter));

  // Api document
  const apiDocument = SwaggerModule.createDocument(app, baseDocument.build());

  SwaggerModule.setup('documents', app, apiDocument, {
    swaggerOptions: {
      docExpansion: 'none',
    },
  });

  //Connecting to AWS through SDK
  // config.update({
  //   accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  //   region: process.env.AWS_REGION,
  // });

  await app.listen(3000);
}
bootstrap();
