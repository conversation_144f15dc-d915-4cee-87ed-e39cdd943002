import { Module } from '@nestjs/common';
import { UserModule } from './user/user.module';
import { TokenModule } from './token/token.module';
import { AuthModule } from 'src/core/auth/auth.module';
import { JobTypeModule } from './job-type/job-type.module';
import { SpecializationModule } from './specialization/specialization.module';
import { VehicleModule } from './vehicle/vehicle.module';
import { WorkerModule } from './worker/worker.module';
import { DevUtilModule } from 'src/core/dev-util/dev-util.module';
import { BaseModule } from 'src/core/base/base.module';
import { JobModule } from './job/job.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AuditLogInterceptor } from './audit-log/interceptors/audit-log.interceptor';
import { AuditLogModule } from './audit-log/audit-log.module';
import { MailerModule } from 'src/core/mailer/mailer.module';
import { FileModule } from './file/file.module';
import { PublicModule } from './public/public.module';
import { AbsentModule } from './absent/absent.module';
import { JobArrangeModule } from './job-arrange/job-arrange.module';
import { PublicViewCodeModule } from './public-view-code/public-view-code.module';

@BaseModule({
  imports: [
    DevUtilModule,
    MailerModule,
    AuthModule,
    UserModule,
    TokenModule,
    JobTypeModule,
    SpecializationModule,
    VehicleModule,
    WorkerModule,
    JobModule,
    AuditLogModule,
    PublicModule,
    PublicViewCodeModule,
    AbsentModule,
    JobArrangeModule,

    FileModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: AuditLogInterceptor,
    },
  ],
})
export class MainModules {}
