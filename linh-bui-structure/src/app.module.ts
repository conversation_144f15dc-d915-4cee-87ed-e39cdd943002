import {
  DynamicModule,
  MiddlewareConsumer,
  Module,
  NestModule,
  OnModuleInit,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { dataSourceOptions } from './core/database/typeorm.config';
import { MainModules } from './modules/main.module';
import { DevUtilService } from './core/dev-util/dev-util.service';
import { ConnectionOptions } from 'typeorm';
import { RedisModule } from './core/redis/redis.module';
import { ExecutionTimeMiddleware } from './core/middlewares/executionTime.middleware';

export const COMMON_CACHE_DURATION = 60; // 60 seconds
export const COMMON_CACHE_NAME = 'COMMON_CACHE_NAME';
// export const REDIS_LIMIT_RATE = 'REDIS_LIMIT_RATE';

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forRoot({
      // name: 'default',
      ...dataSourceOptions,
    }),
    RedisModule.forRootAsync({
      useFactory: () => {
        return [
          {
            name: COMMON_CACHE_NAME,
            url: process.env.REDIS_COMMON,
          },
          // {
          //   name: REDIS_LIMIT_RATE,
          //   url: process.env.REDIS_LIMIT_RATE,
          // },
        ];
      },
    }),
    MainModules,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements OnModuleInit, NestModule {
  constructor(private readonly devUtilService: DevUtilService) {}
  static forRoot(connOptions: ConnectionOptions): DynamicModule {
    return {
      module: AppModule,
      controllers: [AppController],
      imports: [TypeOrmModule.forRoot(connOptions)],
      providers: [AppService],
    };
  }
  async onModuleInit() {
    // update resources to db
    // await this.devUtilService.syncResource();
  }
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ExecutionTimeMiddleware).forRoutes('*');
  }
}
