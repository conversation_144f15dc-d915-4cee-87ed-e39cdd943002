# 🏗️ Enterprise Platform Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# 🌍 ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=development
LOG_LEVEL=info
DEBUG=enterprise:*

# =============================================================================
# 🗄️ DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database
DATABASE_URL=postgresql://enterprise_user:enterprise_password@localhost:5432/enterprise_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=enterprise_db
DATABASE_USER=enterprise_user
DATABASE_PASSWORD=enterprise_password
DATABASE_SSL=false
DATABASE_LOGGING=true
DATABASE_SYNCHRONIZE=false
DATABASE_MIGRATIONS_RUN=true

# Database Pool Configuration
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
DATABASE_POOL_IDLE_TIMEOUT=30000
DATABASE_POOL_ACQUIRE_TIMEOUT=60000

# =============================================================================
# 🔴 REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TTL=3600
REDIS_MAX_RETRIES=3

# =============================================================================
# 🔐 AUTHENTICATION & SECURITY
# =============================================================================
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# API Keys
API_KEY_SECRET=your-api-key-secret-change-this-in-production
ENCRYPTION_KEY=your-32-character-encryption-key-here

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# 🌐 SERVICE CONFIGURATION
# =============================================================================
# API Gateway
API_GATEWAY_PORT=3000
API_GATEWAY_HOST=localhost
API_GATEWAY_PREFIX=/api/v1

# AI Service
AI_SERVICE_URL=http://localhost:8000
AI_SERVICE_PORT=8000
AI_SERVICE_HOST=localhost
AI_SERVICE_TIMEOUT=30000

# =============================================================================
# 🤖 AI/ML CONFIGURATION
# =============================================================================
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2048
OPENAI_TEMPERATURE=0.7

# Hugging Face Configuration
HUGGINGFACE_API_KEY=your-huggingface-api-key-here
HUGGINGFACE_MODEL=microsoft/DialoGPT-medium

# Model Processing Limits
AI_MAX_CONCURRENT_REQUESTS=10
AI_REQUEST_TIMEOUT=30000
AI_QUEUE_SIZE=100

# =============================================================================
# 📊 MONITORING & OBSERVABILITY
# =============================================================================
# Prometheus Metrics
PROMETHEUS_PORT=9090
PROMETHEUS_ENDPOINT=/metrics
METRICS_ENABLED=true

# Grafana
GRAFANA_PORT=3001
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123

# Jaeger Tracing
JAEGER_ENDPOINT=http://localhost:14268/api/traces
JAEGER_AGENT_HOST=localhost
JAEGER_AGENT_PORT=6832
TRACING_ENABLED=true

# Health Checks
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# 📧 EXTERNAL SERVICES
# =============================================================================
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# File Storage
STORAGE_TYPE=local
STORAGE_PATH=./uploads
AWS_S3_BUCKET=enterprise-platform-storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# =============================================================================
# 🔧 DEVELOPMENT SETTINGS
# =============================================================================
# Hot Reload
HOT_RELOAD=true
WATCH_FILES=true

# Debug Settings
DEBUG_SQL=false
DEBUG_REDIS=false
DEBUG_HTTP=false

# Test Configuration
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_db
TEST_REDIS_URL=redis://localhost:6380

# =============================================================================
# 🚀 PRODUCTION SETTINGS
# =============================================================================
# SSL Configuration
SSL_ENABLED=false
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# Performance
CLUSTER_MODE=false
WORKER_PROCESSES=auto
KEEP_ALIVE_TIMEOUT=5000
BODY_LIMIT=10mb

# Security Headers
HELMET_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true

# Compression
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6
