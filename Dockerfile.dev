# Development Dockerfile with hot-reload
FROM denoland/deno:alpine

# Use existing deno user from base image

WORKDIR /app

# Copy dependency files
COPY deno.json deno.lock ./

# Cache dependencies
RUN deno cache --lock=deno.lock deno.json

# Copy source code
COPY src/ ./src/
COPY tests/ ./tests/

# Switch to non-root user
USER deno

# Expose port
EXPOSE 8000

# Development command with watch mode and debugging
CMD ["deno", "run", "--watch", "--allow-net=0.0.0.0:8000", "--allow-read=/app", "--allow-env", "--allow-write=/app", "src/app.ts"] 