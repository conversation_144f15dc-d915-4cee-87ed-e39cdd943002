{"name": "smart-task-management", "version": "1.0.0", "exports": "./src/app.ts", "imports": {"oak": "https://deno.land/x/oak@v12.6.1/mod.ts", "djwt": "https://deno.land/x/djwt@v2.9.1/mod.ts", "bcrypt": "https://deno.land/x/bcrypt@v0.4.1/mod.ts", "postgres": "https://deno.land/x/postgres@v0.17.0/mod.ts", "redis": "https://deno.land/x/redis@v0.32.1/mod.ts", "zod": "https://deno.land/x/zod@v3.22.4/mod.ts", "dotenv": "https://deno.land/x/dotenv@v3.2.2/mod.ts", "@std/": "https://deno.land/std@0.208.0/"}, "tasks": {"dev": "deno run --allow-net --allow-env --allow-read --watch src/app.ts", "start": "deno run --allow-net --allow-env --allow-read src/app.ts", "test": "deno test --allow-net --allow-env --allow-read", "test:watch": "deno test --allow-net --allow-env --allow-read --watch", "lint": "deno lint", "fmt": "deno fmt", "check": "deno check src/**/*.ts", "cache": "deno cache --reload src/app.ts"}, "compilerOptions": {"lib": ["deno.window"], "strict": true}, "lint": {"rules": {"tags": ["recommended"]}}, "fmt": {"files": {"include": ["src/", "tests/"]}, "options": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": true, "singleQuote": true}}}