# 🚀 **Deployment Configurations - Multi-Environment CI/CD**

> **Comprehensive deployment configurations for development, staging, and production environments**

## 📋 **Overview**

The `deployment/` directory contains all **deployment configurations and CI/CD pipelines** for managing application deployments across multiple environments. Built with GitOps principles and enterprise-grade deployment strategies.

## 🏗️ **Architecture Overview**

```
deployment/
├── environments/       # 🌍 Environment-Specific Configurations
├── ci-cd/             # 🔄 CI/CD Pipeline Configurations
└── secrets/           # 🔐 Secret Management & Configurations
```

## 📁 **Deployment Components & Guides**

| Component | Technology Stack | Purpose | README Guide | Status |
|-----------|------------------|---------|--------------|--------|
| [🌍 **environments/**](environments/README.md) | Kubernetes + Helm + Kustomize | Environment configurations | [📖 Guide](environments/README.md) | 🔄 |
| [🔄 **ci-cd/**](ci-cd/README.md) | GitHub Actions + ArgoCD + GitOps | CI/CD pipelines | [📖 Guide](ci-cd/README.md) | 🔄 |
| [🔐 **secrets/**](secrets/README.md) | Vault + External Secrets + SOPS | Secret management | [📖 Guide](secrets/README.md) | 🔄 |

## 🌍 **Environment Configurations**

**Multi-environment deployment** with consistent configurations:

### **🔧 Key Features**
- ✅ **Environment Isolation** - Separate configurations per environment
- ✅ **Configuration Management** - Environment-specific settings
- ✅ **Resource Scaling** - Environment-appropriate resource allocation
- ✅ **Feature Flags** - Environment-based feature toggles
- ✅ **Database Configurations** - Environment-specific database settings
- ✅ **Monitoring Setup** - Environment-tailored monitoring

### **📁 Structure**
```
environments/
├── development/         # Development environment
│   ├── kustomization.yaml
│   ├── configmap.yaml
│   ├── secrets.yaml
│   ├── ingress.yaml
│   └── values.yaml
├── staging/             # Staging environment
│   ├── kustomization.yaml
│   ├── configmap.yaml
│   ├── secrets.yaml
│   ├── ingress.yaml
│   └── values.yaml
├── production/          # Production environment
│   ├── kustomization.yaml
│   ├── configmap.yaml
│   ├── secrets.yaml
│   ├── ingress.yaml
│   └── values.yaml
└── base/                # Base configurations
    ├── deployment.yaml
    ├── service.yaml
    ├── configmap.yaml
    └── kustomization.yaml
```

### **⚙️ Environment Configuration Example**
```yaml
# environments/production/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: enterprise-platform-prod

resources:
  - ../base
  - ingress.yaml

patchesStrategicMerge:
  - configmap.yaml
  - secrets.yaml

replicas:
  - name: api-gateway
    count: 3
  - name: user-service
    count: 2
  - name: ai-service
    count: 2

images:
  - name: api-gateway
    newTag: v1.2.3
  - name: user-service
    newTag: v1.1.5
  - name: ai-service
    newTag: v2.0.1

configMapGenerator:
  - name: app-config
    files:
      - config/production.env
    options:
      disableNameSuffixHash: true

secretGenerator:
  - name: app-secrets
    files:
      - secrets/production.env
    type: Opaque
    options:
      disableNameSuffixHash: true
```

### **🔧 Environment-Specific Values**
```yaml
# environments/production/values.yaml
global:
  environment: production
  domain: api.company.com
  
replicaCount:
  apiGateway: 3
  userService: 2
  aiService: 2

resources:
  apiGateway:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 2Gi
  
  userService:
    requests:
      cpu: 250m
      memory: 512Mi
    limits:
      cpu: 500m
      memory: 1Gi

database:
  host: prod-postgres.company.com
  port: 5432
  name: enterprise_platform_prod
  ssl: true
  connectionPool:
    min: 5
    max: 20

redis:
  host: prod-redis.company.com
  port: 6379
  cluster: true
  ssl: true

monitoring:
  enabled: true
  prometheus:
    scrapeInterval: 15s
  grafana:
    enabled: true
  jaeger:
    enabled: true

security:
  tls:
    enabled: true
    certManager: true
  networkPolicies:
    enabled: true
  podSecurityPolicy:
    enabled: true

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80
```

## 🔄 **CI/CD Pipelines**

**Automated deployment** with GitOps principles:

### **🔧 Key Features**
- ✅ **GitOps Workflow** - Git-based deployment automation
- ✅ **Multi-Stage Pipeline** - Build, test, deploy stages
- ✅ **Quality Gates** - Automated quality checks
- ✅ **Security Scanning** - Vulnerability and compliance checks
- ✅ **Rollback Procedures** - Automated rollback on failure
- ✅ **Deployment Strategies** - Blue-green, canary, rolling updates
- ✅ **Continuous Integration** - Automated builds and testing on every commit
- ✅ **Continuous Delivery** - Always deployable software with manual approval
- ✅ **Continuous Deployment** - Fully automated production deployments
- ✅ **Pipeline as Code** - Version-controlled pipeline configurations
- ✅ **Multi-Environment Support** - Development, staging, and production
- ✅ **Security & Compliance** - Automated security scanning and compliance checks
- ✅ **Monitoring & Observability** - Real-time deployment monitoring and alerting

### **📁 Structure**
```
ci-cd/
├── github-actions/      # GitHub Actions workflows
│   ├── build.yml
│   ├── test.yml
│   ├── deploy.yml
│   ├── security.yml
│   └── release.yml
├── argocd/              # ArgoCD applications
│   ├── applications/
│   │   ├── api-gateway.yaml
│   │   ├── user-service.yaml
│   │   └── ai-service.yaml
│   ├── projects/
│   │   ├── development.yaml
│   │   ├── staging.yaml
│   │   └── production.yaml
│   └── repositories/
│       └── enterprise-platform.yaml
├── jenkins/             # Jenkins pipelines (alternative)
│   ├── Jenkinsfile
│   └── pipeline-library/
└── scripts/             # Deployment scripts
    ├── build-images.sh
    ├── run-tests.sh
    ├── deploy-to-k8s.sh
    └── rollback.sh
```

### **🔄 GitHub Actions Workflow**
```yaml
# ci-cd/github-actions/deploy.yml
name: Deploy to Kubernetes

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build:
    runs-on: ubuntu-latest
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        
      - name: Log in to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            
      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  test:
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run unit tests
        run: npm run test:unit
        
      - name: Run integration tests
        run: npm run test:integration
        
      - name: Run security audit
        run: npm audit --audit-level moderate
        
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: test-results/

  security:
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ needs.build.outputs.image-tag }}
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build, test, security]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'
          
      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBECONFIG_STAGING }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig
          
      - name: Deploy to staging
        run: |
          cd deployment/environments/staging
          kustomize edit set image api-gateway=${{ needs.build.outputs.image-tag }}
          kubectl apply -k .
          
      - name: Wait for deployment
        run: |
          kubectl rollout status deployment/api-gateway -n enterprise-platform-staging --timeout=600s
          
      - name: Run smoke tests
        run: |
          ./deployment/scripts/smoke-tests.sh staging

  deploy-production:
    runs-on: ubuntu-latest
    needs: [build, test, security, deploy-staging]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Deploy to production
        run: |
          # Update ArgoCD application
          cd deployment/argocd/applications
          yq eval '.spec.source.targetRevision = "${{ github.sha }}"' -i api-gateway.yaml
          git add .
          git commit -m "Deploy ${{ github.sha }} to production"
          git push
          
      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

### **🔄 ArgoCD Application**
```yaml
# ci-cd/argocd/applications/api-gateway.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: api-gateway
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: enterprise-platform
  source:
    repoURL: https://github.com/company/enterprise-platform
    targetRevision: main
    path: deployment/environments/production
  destination:
    server: https://kubernetes.default.svc
    namespace: enterprise-platform-prod
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
```

## 🔐 **Secret Management**

**Secure secret handling** across environments:

### **🔧 Key Features**
- ✅ **External Secrets** - Kubernetes External Secrets Operator
- ✅ **HashiCorp Vault** - Centralized secret management
- ✅ **SOPS Encryption** - Git-stored encrypted secrets
- ✅ **Rotation Policies** - Automated secret rotation
- ✅ **Audit Logging** - Secret access auditing
- ✅ **Least Privilege** - Minimal secret access permissions

### **📁 Structure**
```
secrets/
├── vault/               # Vault configurations
│   ├── policies/
│   │   ├── api-gateway.hcl
│   │   ├── user-service.hcl
│   │   └── ai-service.hcl
│   ├── auth/
│   │   ├── kubernetes.json
│   │   └── jwt.json
│   └── secrets/
│       ├── database.json
│       ├── api-keys.json
│       └── certificates.json
├── external-secrets/    # External Secrets configurations
│   ├── secret-store.yaml
│   ├── cluster-secret-store.yaml
│   └── external-secrets/
│       ├── database-secret.yaml
│       ├── api-keys-secret.yaml
│       └── tls-secret.yaml
├── sops/                # SOPS encrypted secrets
│   ├── .sops.yaml
│   ├── development.enc.yaml
│   ├── staging.enc.yaml
│   └── production.enc.yaml
└── scripts/             # Secret management scripts
    ├── rotate-secrets.sh
    ├── backup-vault.sh
    └── audit-access.sh
```

### **🔐 External Secret Example**
```yaml
# secrets/external-secrets/database-secret.yaml
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: database-secret
  namespace: enterprise-platform-prod
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: vault-secret-store
    kind: SecretStore
  target:
    name: database-secret
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        DATABASE_URL: "postgresql://{{ .username }}:{{ .password }}@{{ .host }}:{{ .port }}/{{ .database }}"
        DB_USERNAME: "{{ .username }}"
        DB_PASSWORD: "{{ .password }}"
  data:
    - secretKey: username
      remoteRef:
        key: database/production
        property: username
    - secretKey: password
      remoteRef:
        key: database/production
        property: password
    - secretKey: host
      remoteRef:
        key: database/production
        property: host
    - secretKey: port
      remoteRef:
        key: database/production
        property: port
    - secretKey: database
      remoteRef:
        key: database/production
        property: database
```

## 🚀 **Deployment Strategies**

### **🔄 Blue-Green Deployment**
```yaml
# Blue-Green deployment configuration
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: api-gateway
spec:
  replicas: 3
  strategy:
    blueGreen:
      activeService: api-gateway-active
      previewService: api-gateway-preview
      autoPromotionEnabled: false
      scaleDownDelaySeconds: 30
      prePromotionAnalysis:
        templates:
        - templateName: success-rate
        args:
        - name: service-name
          value: api-gateway-preview
      postPromotionAnalysis:
        templates:
        - templateName: success-rate
        args:
        - name: service-name
          value: api-gateway-active
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: api-gateway:latest
        ports:
        - containerPort: 3000
```

### **🎯 Canary Deployment**
```yaml
# Canary deployment configuration
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: api-gateway
spec:
  replicas: 5
  strategy:
    canary:
      steps:
      - setWeight: 20
      - pause: {duration: 10m}
      - setWeight: 40
      - pause: {duration: 10m}
      - setWeight: 60
      - pause: {duration: 10m}
      - setWeight: 80
      - pause: {duration: 10m}
      canaryService: api-gateway-canary
      stableService: api-gateway-stable
      trafficRouting:
        istio:
          virtualService:
            name: api-gateway-vs
            routes:
            - primary
      analysis:
        templates:
        - templateName: success-rate
        - templateName: latency
        args:
        - name: service-name
          value: api-gateway-canary
```

## 🔗 **Related Documentation**

- [🏗️ Project Structure](../docs/PROJECT_STRUCTURE.md)
- [🏗️ Infrastructure](../infrastructure/README.md)
- [🤖 Scripts](../scripts/README.md)
- [📊 Monitoring](../monitoring/README.md)
- [☁️ DevOps Guide](../docs/07-knowledge-base/05-devops-cloud/README.md)
- [🔄 Complete CI/CD Guide](../docs/guides/deployment/CICD_PIPELINE.md) - Comprehensive CI/CD implementation

## 🤝 **Contributing**

1. **Pipeline Development** - Enhance CI/CD pipelines
2. **Environment Configuration** - Add new environments
3. **Secret Management** - Improve secret handling
4. **Deployment Strategies** - Implement new deployment patterns
5. **Documentation** - Document deployment procedures

---

> **Next Steps**: Explore individual deployment component READMEs for detailed setup and configuration guides.
