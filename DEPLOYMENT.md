# 🚀 Enterprise Platform Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying the Enterprise AI Platform to production environments. The platform consists of multiple microservices orchestrated with Docker Compose and includes monitoring, security, and scalability features.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   API Gateway   │    │   AI Service    │
│     (Nginx)     │────│    (NestJS)     │────│   (FastAPI)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │   PostgreSQL    │    │     Redis       │
         │              │   (Database)    │    │    (Cache)      │
         │              └─────────────────┘    └─────────────────┘
         │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Prometheus    │    │    Grafana      │    │   ELK Stack     │
│  (Metrics)      │    │ (Dashboards)    │    │   (Logging)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Prerequisites

### System Requirements
- **CPU**: Minimum 4 cores, Recommended 8+ cores
- **RAM**: Minimum 8GB, Recommended 16GB+
- **Storage**: Minimum 50GB SSD, Recommended 100GB+ SSD
- **Network**: Stable internet connection for AI model downloads

### Software Requirements
- Docker Engine 20.10+
- Docker Compose 2.0+
- Git 2.30+
- Node.js 18+ (for development)
- Python 3.11+ (for development)

### External Services
- OpenAI API Key (for GPT models)
- HuggingFace API Key (optional, for additional models)
- SMTP Server (for email notifications)
- SSL Certificates (for HTTPS)

## 🚀 Quick Start

### 1. Clone Repository
```bash
git clone https://github.com/enterprise/platform.git
cd platform
```

### 2. Environment Setup
```bash
# Copy environment templates
cp apps/api-gateway/.env.example apps/api-gateway/.env
cp services/ai-service/.env.example services/ai-service/.env
cp .env.example .env

# Edit environment files with your configuration
nano .env
nano apps/api-gateway/.env
nano services/ai-service/.env
```

### 3. Configure Secrets
```bash
# Generate secure JWT secrets
openssl rand -hex 32  # Use for JWT_SECRET
openssl rand -hex 32  # Use for JWT_REFRESH_SECRET

# Update .env files with:
# - Database passwords
# - Redis passwords
# - API keys
# - SSL certificate paths
```

### 4. Build and Deploy
```bash
# Build all services
docker-compose build

# Start the platform
docker-compose up -d

# Check service health
docker-compose ps
docker-compose logs -f
```

### 5. Verify Deployment
```bash
# Check API Gateway
curl http://localhost:3000/health

# Check AI Service
curl http://localhost:8000/health

# Check Prometheus metrics
curl http://localhost:9090/metrics

# Access Grafana dashboard
open http://localhost:3001
```

## 🔒 Security Configuration

### SSL/TLS Setup
```bash
# Generate self-signed certificates (development)
mkdir -p ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/private.key \
  -out ssl/certificate.crt

# For production, use Let's Encrypt or commercial certificates
```

### Firewall Configuration
```bash
# Allow only necessary ports
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 3000   # Block direct API Gateway access
ufw deny 8000   # Block direct AI Service access
ufw deny 5432   # Block direct database access
ufw deny 6379   # Block direct Redis access
ufw enable
```

### Database Security
```bash
# Create database backup user
docker-compose exec postgres psql -U enterprise_user -d enterprise_db -c "
CREATE USER backup_user WITH PASSWORD 'backup_password';
GRANT CONNECT ON DATABASE enterprise_db TO backup_user;
GRANT USAGE ON SCHEMA public TO backup_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO backup_user;
"
```

## 📊 Monitoring Setup

### Prometheus Configuration
```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:9090']
  
  - job_name: 'ai-service'
    static_configs:
      - targets: ['ai-service:9091']
  
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
```

### Grafana Dashboards
```bash
# Import pre-built dashboards
curl -X POST \
  ************************************/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @monitoring/grafana/dashboards/api-gateway.json
```

### Log Aggregation
```bash
# Configure log shipping to ELK stack
docker-compose exec logstash logstash-plugin install logstash-input-beats
```

## 🔄 Backup and Recovery

### Database Backup
```bash
# Create automated backup script
cat > scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/enterprise_db_$DATE.sql"

pg_dump -h postgres -U enterprise_user -d enterprise_db > $BACKUP_FILE
gzip $BACKUP_FILE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
EOF

chmod +x scripts/backup.sh

# Schedule with cron
echo "0 2 * * * /path/to/scripts/backup.sh" | crontab -
```

### Data Recovery
```bash
# Restore from backup
gunzip -c /backup/enterprise_db_20240101_020000.sql.gz | \
  docker-compose exec -T postgres psql -U enterprise_user -d enterprise_db
```

## 📈 Scaling and Performance

### Horizontal Scaling
```yaml
# docker-compose.override.yml
version: '3.8'
services:
  api-gateway:
    deploy:
      replicas: 3
  
  ai-service:
    deploy:
      replicas: 2
```

### Load Balancing
```nginx
# nginx/conf.d/load-balancer.conf
upstream api_gateway {
    server api-gateway-1:3000;
    server api-gateway-2:3000;
    server api-gateway-3:3000;
}

upstream ai_service {
    server ai-service-1:8000;
    server ai-service-2:8000;
}
```

### Database Optimization
```sql
-- Performance tuning queries
ANALYZE;
VACUUM;
REINDEX DATABASE enterprise_db;

-- Monitor slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;
```

## 🚨 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check logs
docker-compose logs service-name

# Check resource usage
docker stats

# Restart specific service
docker-compose restart service-name
```

#### Database Connection Issues
```bash
# Test database connectivity
docker-compose exec api-gateway npm run db:test

# Check database logs
docker-compose logs postgres

# Reset database connections
docker-compose restart postgres
```

#### High Memory Usage
```bash
# Monitor memory usage
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Adjust memory limits in docker-compose.yml
```

### Performance Issues
```bash
# Check API response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:3000/health

# Monitor database performance
docker-compose exec postgres pg_stat_activity

# Check Redis performance
docker-compose exec redis redis-cli info stats
```

## 🔧 Maintenance

### Regular Tasks
```bash
# Weekly maintenance script
cat > scripts/maintenance.sh << 'EOF'
#!/bin/bash

# Update Docker images
docker-compose pull

# Clean up unused images
docker image prune -f

# Backup database
./scripts/backup.sh

# Restart services with zero downtime
docker-compose up -d --force-recreate --no-deps api-gateway
docker-compose up -d --force-recreate --no-deps ai-service

# Check service health
curl -f http://localhost:3000/health
curl -f http://localhost:8000/health
EOF

chmod +x scripts/maintenance.sh
```

### Updates and Patches
```bash
# Update application code
git pull origin main

# Rebuild and deploy
docker-compose build --no-cache
docker-compose up -d

# Run database migrations
docker-compose exec api-gateway npm run migration:run
```

## 📞 Support

### Health Checks
- API Gateway: `http://localhost:3000/health`
- AI Service: `http://localhost:8000/health`
- Prometheus: `http://localhost:9090/-/healthy`
- Grafana: `http://localhost:3001/api/health`

### Monitoring URLs
- Grafana Dashboards: `http://localhost:3001`
- Prometheus Metrics: `http://localhost:9090`
- Kibana Logs: `http://localhost:5601`

### Emergency Contacts
- Platform Team: <EMAIL>
- DevOps Team: <EMAIL>
- Security Team: <EMAIL>

---

## 📚 Additional Resources

- [API Documentation](./docs/api.md)
- [Development Guide](./docs/development.md)
- [Security Guidelines](./docs/security.md)
- [Performance Tuning](./docs/performance.md)
