# 🧪 Pytest Configuration - Comprehensive Python Testing Setup
#
# Implements enterprise-grade testing configuration with:
# - Test discovery and execution settings
# - Coverage reporting and thresholds
# - Fixture and plugin configurations
# - Performance and memory monitoring
# - Parallel test execution

[tool:pytest]

# ================================
# 🎯 Test Discovery
# ================================
testpaths = tests app
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# ================================
# 📁 File Patterns
# ================================
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-report=json:coverage.json
    --cov-fail-under=80
    --cov-branch
    --junitxml=reports/junit.xml
    --html=reports/report.html
    --self-contained-html
    --maxfail=5
    --disable-warnings

# ================================
# 📊 Coverage Configuration
# ================================
[coverage:run]
source = app
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */migrations/*
    */venv/*
    */env/*
    */.venv/*
    */node_modules/*
    */conftest.py
    */setup.py
    */manage.py
    app/main.py

branch = True
parallel = True

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
skip_covered = False
precision = 2

[coverage:html]
directory = htmlcov
title = AI Service Coverage Report

[coverage:xml]
output = coverage.xml

# ================================
# 🔧 Test Execution
# ================================
minversion = 7.0
required_plugins = 
    pytest-cov>=4.0.0
    pytest-asyncio>=0.21.0
    pytest-mock>=3.10.0
    pytest-html>=3.1.0
    pytest-xdist>=3.0.0
    pytest-benchmark>=4.0.0

# Async configuration
asyncio_mode = auto

# Parallel execution
# -n auto uses all available CPU cores
# -n 4 uses 4 workers
# Uncomment for parallel execution:
# addopts = -n auto

# ================================
# 🏷️ Test Markers
# ================================
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    fast: Fast running tests
    api: API endpoint tests
    database: Database related tests
    cache: Cache related tests
    auth: Authentication tests
    embedding: Embedding service tests
    chat: Chat service tests
    model: Model management tests
    performance: Performance tests
    security: Security tests
    smoke: Smoke tests
    regression: Regression tests
    external: Tests requiring external services
    mock: Tests using mocks
    real: Tests using real services

# ================================
# 🔍 Test Filtering
# ================================
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# ================================
# 📝 Logging Configuration
# ================================
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = tests.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d %(funcName)s(): %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# ================================
# ⏱️ Timeout Configuration
# ================================
timeout = 300
timeout_method = thread

# ================================
# 🎭 Mock Configuration
# ================================
mock_use_standalone_module = true

# ================================
# 📈 Performance Configuration
# ================================
# Benchmark configuration
benchmark-only = false
benchmark-sort = mean
benchmark-group-by = group
benchmark-timer = time.perf_counter
benchmark-disable-gc = false
benchmark-skip = false
benchmark-warmup = false
benchmark-warmup-iterations = 100000
benchmark-max-time = 1.0
benchmark-min-rounds = 5
benchmark-min-time = 0.000005
benchmark-columns = min,max,mean,stddev,median,iqr,outliers,ops,rounds,iterations

# ================================
# 🔧 Plugin Configuration
# ================================
# HTML report configuration
html_report_title = AI Service Test Report
html_report_description = Comprehensive test results for the Enterprise AI Service

# JUnit XML configuration
junit_suite_name = ai-service-tests
junit_logging = system-out
junit_log_passing_tests = true
junit_duration_report = total

# ================================
# 🌍 Environment Configuration
# ================================
env = 
    ENVIRONMENT = test
    LOG_LEVEL = DEBUG
    DATABASE_URL = postgresql://test_user:test_password@localhost:5432/test_ai_db
    REDIS_URL = redis://localhost:6379/15
    OPENAI_API_KEY = test-key
    MOCK_EXTERNAL_APIS = true
    CACHE_TTL_SECONDS = 60
    RATE_LIMIT_ENABLED = false

# ================================
# 🔄 Test Collection
# ================================
collect_ignore = [
    "setup.py",
    "conftest.py",
    "migrations",
    "alembic",
    ".git",
    ".pytest_cache",
    "__pycache__",
    "*.pyc",
    ".coverage",
    "htmlcov",
    "reports",
]

# ================================
# 🎯 Test Selection
# ================================
# Example test selection patterns:
# Run only unit tests: pytest -m unit
# Run only fast tests: pytest -m fast
# Run everything except slow tests: pytest -m "not slow"
# Run API tests: pytest -m api
# Run integration tests: pytest -m integration

# ================================
# 🔧 Advanced Configuration
# ================================
# Cache configuration
cache_dir = .pytest_cache

# Doctest configuration
doctest_optionflags = NORMALIZE_WHITESPACE IGNORE_EXCEPTION_DETAIL ELLIPSIS

# Import mode
python_paths = .

# ================================
# 🚀 Performance Optimization
# ================================
# Disable plugins for faster execution (uncomment if needed)
# addopts = -p no:warnings -p no:doctest

# Use faster assertion rewriting
python_files = test_*.py *_test.py check_*.py
python_classes = Test* Check*
python_functions = test_* check_*
