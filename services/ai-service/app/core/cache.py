"""
🔴 Cache Client - Redis-based Caching Service

Implements enterprise-grade caching with:
- Redis connection management
- Async operations for performance
- Serialization/deserialization
- Cache invalidation strategies
- Connection pooling and retry logic
"""

import asyncio
import json
import pickle
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
import hashlib

import redis.asyncio as redis
from redis.asyncio import ConnectionPool
import structlog

from .config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


class CacheClient:
    """
    Async Redis cache client with enterprise features
    """
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.connection_pool: Optional[ConnectionPool] = None
        self._connected = False
    
    async def connect(self) -> None:
        """Initialize Redis connection"""
        try:
            # Create connection pool
            self.connection_pool = ConnectionPool.from_url(
                settings.REDIS_URL,
                max_connections=20,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30
            )
            
            # Create Redis client
            self.redis_client = redis.Redis(
                connection_pool=self.connection_pool,
                decode_responses=False  # We'll handle encoding ourselves
            )
            
            # Test connection
            await self.redis_client.ping()
            self._connected = True
            
            logger.info("Redis cache client connected successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            self._connected = False
            raise
    
    async def disconnect(self) -> None:
        """Close Redis connection"""
        try:
            if self.redis_client:
                await self.redis_client.close()
            if self.connection_pool:
                await self.connection_pool.disconnect()
            
            self._connected = False
            logger.info("Redis cache client disconnected")
            
        except Exception as e:
            logger.error(f"Error disconnecting from Redis: {str(e)}")
    
    def _serialize_value(self, value: Any, serialization_method: str = "json") -> bytes:
        """Serialize value for storage"""
        try:
            if serialization_method == "json":
                return json.dumps(value, default=str).encode('utf-8')
            elif serialization_method == "pickle":
                return pickle.dumps(value)
            else:
                return str(value).encode('utf-8')
        except Exception as e:
            logger.warning(f"Serialization failed: {str(e)}")
            return str(value).encode('utf-8')
    
    def _deserialize_value(self, value: bytes, serialization_method: str = "json") -> Any:
        """Deserialize value from storage"""
        try:
            if serialization_method == "json":
                return json.loads(value.decode('utf-8'))
            elif serialization_method == "pickle":
                return pickle.loads(value)
            else:
                return value.decode('utf-8')
        except Exception as e:
            logger.warning(f"Deserialization failed: {str(e)}")
            return value.decode('utf-8') if isinstance(value, bytes) else value
    
    def _get_key(self, key: str) -> str:
        """Get prefixed cache key"""
        prefix = getattr(settings, 'REDIS_KEY_PREFIX', 'ai-service:')
        return f"{prefix}{key}"
    
    async def get(self, key: str, serialization_method: str = "json") -> Optional[Any]:
        """Get value from cache"""
        if not self._connected or not self.redis_client:
            return None
        
        try:
            prefixed_key = self._get_key(key)
            value = await self.redis_client.get(prefixed_key)
            
            if value is None:
                return None
            
            return self._deserialize_value(value, serialization_method)
            
        except Exception as e:
            logger.warning(f"Cache get failed for key {key}: {str(e)}")
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        serialization_method: str = "json"
    ) -> bool:
        """Set value in cache"""
        if not self._connected or not self.redis_client:
            return False
        
        try:
            prefixed_key = self._get_key(key)
            serialized_value = self._serialize_value(value, serialization_method)
            
            if ttl:
                await self.redis_client.setex(prefixed_key, ttl, serialized_value)
            else:
                await self.redis_client.set(prefixed_key, serialized_value)
            
            return True
            
        except Exception as e:
            logger.warning(f"Cache set failed for key {key}: {str(e)}")
            return False
    
    async def setex(
        self,
        key: str,
        ttl: int,
        value: Any,
        serialization_method: str = "json"
    ) -> bool:
        """Set value with expiration"""
        return await self.set(key, value, ttl, serialization_method)
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        if not self._connected or not self.redis_client:
            return False
        
        try:
            prefixed_key = self._get_key(key)
            result = await self.redis_client.delete(prefixed_key)
            return result > 0
            
        except Exception as e:
            logger.warning(f"Cache delete failed for key {key}: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        if not self._connected or not self.redis_client:
            return False
        
        try:
            prefixed_key = self._get_key(key)
            result = await self.redis_client.exists(prefixed_key)
            return result > 0
            
        except Exception as e:
            logger.warning(f"Cache exists check failed for key {key}: {str(e)}")
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration for existing key"""
        if not self._connected or not self.redis_client:
            return False
        
        try:
            prefixed_key = self._get_key(key)
            result = await self.redis_client.expire(prefixed_key, ttl)
            return result
            
        except Exception as e:
            logger.warning(f"Cache expire failed for key {key}: {str(e)}")
            return False
    
    async def ttl(self, key: str) -> int:
        """Get time to live for key"""
        if not self._connected or not self.redis_client:
            return -1
        
        try:
            prefixed_key = self._get_key(key)
            return await self.redis_client.ttl(prefixed_key)
            
        except Exception as e:
            logger.warning(f"Cache TTL check failed for key {key}: {str(e)}")
            return -1
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment numeric value"""
        if not self._connected or not self.redis_client:
            return None
        
        try:
            prefixed_key = self._get_key(key)
            return await self.redis_client.incrby(prefixed_key, amount)
            
        except Exception as e:
            logger.warning(f"Cache increment failed for key {key}: {str(e)}")
            return None
    
    async def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """Get multiple values from cache"""
        if not self._connected or not self.redis_client:
            return {}
        
        try:
            prefixed_keys = [self._get_key(key) for key in keys]
            values = await self.redis_client.mget(prefixed_keys)
            
            result = {}
            for i, key in enumerate(keys):
                if values[i] is not None:
                    try:
                        result[key] = self._deserialize_value(values[i])
                    except Exception:
                        result[key] = None
                else:
                    result[key] = None
            
            return result
            
        except Exception as e:
            logger.warning(f"Cache get_many failed: {str(e)}")
            return {}
    
    async def set_many(self, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set multiple values in cache"""
        if not self._connected or not self.redis_client:
            return False
        
        try:
            pipe = self.redis_client.pipeline()
            
            for key, value in mapping.items():
                prefixed_key = self._get_key(key)
                serialized_value = self._serialize_value(value)
                
                if ttl:
                    pipe.setex(prefixed_key, ttl, serialized_value)
                else:
                    pipe.set(prefixed_key, serialized_value)
            
            await pipe.execute()
            return True
            
        except Exception as e:
            logger.warning(f"Cache set_many failed: {str(e)}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear keys matching pattern"""
        if not self._connected or not self.redis_client:
            return 0
        
        try:
            prefixed_pattern = self._get_key(pattern)
            keys = await self.redis_client.keys(prefixed_pattern)
            
            if keys:
                return await self.redis_client.delete(*keys)
            return 0
            
        except Exception as e:
            logger.warning(f"Cache clear_pattern failed for pattern {pattern}: {str(e)}")
            return 0
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on cache"""
        try:
            if not self._connected or not self.redis_client:
                return {
                    "status": "error",
                    "message": "Not connected to Redis"
                }
            
            # Test basic operations
            test_key = "health_check_test"
            test_value = {"timestamp": datetime.utcnow().isoformat()}
            
            # Test set/get
            await self.set(test_key, test_value, ttl=60)
            retrieved_value = await self.get(test_key)
            
            # Clean up
            await self.delete(test_key)
            
            if retrieved_value == test_value:
                return {
                    "status": "ok",
                    "message": "Cache is healthy"
                }
            else:
                return {
                    "status": "error",
                    "message": "Cache data integrity issue"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"Cache health check failed: {str(e)}"
            }


# Global cache client instance
_cache_client: Optional[CacheClient] = None


async def get_cache_client() -> Optional[CacheClient]:
    """Get the global cache client instance"""
    global _cache_client
    
    if _cache_client is None:
        _cache_client = CacheClient()
        try:
            await _cache_client.connect()
        except Exception as e:
            logger.error(f"Failed to initialize cache client: {str(e)}")
            _cache_client = None
    
    return _cache_client


async def close_cache_client() -> None:
    """Close the global cache client"""
    global _cache_client
    
    if _cache_client:
        await _cache_client.disconnect()
        _cache_client = None
