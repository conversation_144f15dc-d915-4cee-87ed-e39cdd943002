"""
📝 Logging Configuration - Structured Logging Setup

Implements enterprise-grade logging with:
- Structured JSON logging
- Correlation ID tracking
- Performance monitoring
- Error tracking and alerting
"""

import logging
import sys
from typing import Any, Dict, Optional
from datetime import datetime

import structlog
from structlog.stdlib import Logger<PERSON>actory
from structlog.dev import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from structlog.processors import <PERSON><PERSON><PERSON><PERSON><PERSON>, TimeStamper, add_log_level, StackInfoRenderer

from .config import get_settings


def setup_logging() -> None:
    """
    Configure structured logging for the application
    """
    settings = get_settings()
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )
    
    # Configure structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        add_log_level,
        TimeStamper(fmt="iso"),
        StackInfoRenderer(),
        structlog.processors.format_exc_info,
    ]
    
    # Use JSON renderer for production, console for development
    if settings.ENVIRONMENT == "production":
        processors.append(JSONRenderer())
    else:
        processors.append(ConsoleRenderer(colors=True))
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a configured logger instance
    """
    return structlog.get_logger(name)


class CorrelationIdProcessor:
    """
    Processor to add correlation ID to log records
    """
    
    def __init__(self, correlation_id_key: str = "correlation_id"):
        self.correlation_id_key = correlation_id_key
    
    def __call__(self, logger, method_name, event_dict):
        # Add correlation ID if available in context
        # This would be set by middleware
        correlation_id = getattr(logger, '_correlation_id', None)
        if correlation_id:
            event_dict[self.correlation_id_key] = correlation_id
        return event_dict


class PerformanceLogger:
    """
    Logger for performance monitoring
    """
    
    def __init__(self):
        self.logger = get_logger("performance")
    
    def log_request_duration(
        self,
        endpoint: str,
        method: str,
        duration_ms: float,
        status_code: int,
        user_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        """Log request performance metrics"""
        self.logger.info(
            "Request completed",
            endpoint=endpoint,
            method=method,
            duration_ms=duration_ms,
            status_code=status_code,
            user_id=user_id,
            correlation_id=correlation_id,
            timestamp=datetime.utcnow().isoformat(),
        )
    
    def log_model_inference(
        self,
        model_name: str,
        input_tokens: int,
        output_tokens: int,
        duration_ms: float,
        success: bool,
        error: Optional[str] = None,
    ):
        """Log model inference performance"""
        self.logger.info(
            "Model inference completed",
            model_name=model_name,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            duration_ms=duration_ms,
            success=success,
            error=error,
            timestamp=datetime.utcnow().isoformat(),
        )


class SecurityLogger:
    """
    Logger for security events
    """
    
    def __init__(self):
        self.logger = get_logger("security")
    
    def log_authentication_attempt(
        self,
        user_id: Optional[str],
        success: bool,
        ip_address: str,
        user_agent: str,
        error: Optional[str] = None,
    ):
        """Log authentication attempts"""
        self.logger.info(
            "Authentication attempt",
            user_id=user_id,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            error=error,
            timestamp=datetime.utcnow().isoformat(),
        )
    
    def log_rate_limit_exceeded(
        self,
        ip_address: str,
        endpoint: str,
        limit: int,
        window_seconds: int,
    ):
        """Log rate limit violations"""
        self.logger.warning(
            "Rate limit exceeded",
            ip_address=ip_address,
            endpoint=endpoint,
            limit=limit,
            window_seconds=window_seconds,
            timestamp=datetime.utcnow().isoformat(),
        )
    
    def log_suspicious_activity(
        self,
        activity_type: str,
        details: Dict[str, Any],
        ip_address: str,
        user_id: Optional[str] = None,
    ):
        """Log suspicious activities"""
        self.logger.warning(
            "Suspicious activity detected",
            activity_type=activity_type,
            details=details,
            ip_address=ip_address,
            user_id=user_id,
            timestamp=datetime.utcnow().isoformat(),
        )


class BusinessLogger:
    """
    Logger for business events and metrics
    """
    
    def __init__(self):
        self.logger = get_logger("business")
    
    def log_api_usage(
        self,
        user_id: str,
        endpoint: str,
        tokens_used: int,
        cost: float,
        success: bool,
    ):
        """Log API usage for billing and analytics"""
        self.logger.info(
            "API usage recorded",
            user_id=user_id,
            endpoint=endpoint,
            tokens_used=tokens_used,
            cost=cost,
            success=success,
            timestamp=datetime.utcnow().isoformat(),
        )
    
    def log_model_performance(
        self,
        model_name: str,
        accuracy: float,
        latency_p95: float,
        throughput: float,
        error_rate: float,
    ):
        """Log model performance metrics"""
        self.logger.info(
            "Model performance metrics",
            model_name=model_name,
            accuracy=accuracy,
            latency_p95=latency_p95,
            throughput=throughput,
            error_rate=error_rate,
            timestamp=datetime.utcnow().isoformat(),
        )


# Global logger instances
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()
business_logger = BusinessLogger()


def configure_uvicorn_logging():
    """
    Configure uvicorn logging to use structured logging
    """
    # Disable uvicorn access logs (we'll handle them in middleware)
    uvicorn_access = logging.getLogger("uvicorn.access")
    uvicorn_access.disabled = True
    
    # Configure uvicorn error logging
    uvicorn_error = logging.getLogger("uvicorn.error")
    uvicorn_error.handlers = []
    
    # Add structured handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter("%(message)s"))
    uvicorn_error.addHandler(handler)
