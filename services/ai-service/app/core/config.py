"""
🔧 AI Service Configuration - Environment-based Settings

Implements comprehensive configuration management with:
- Type-safe environment variables
- Validation and defaults
- Environment-specific overrides
- Security-first approach
"""

import os
from functools import lru_cache
from typing import List, Optional
from pydantic import BaseSettings, Field, validator


class Settings(BaseSettings):
    """
    Application settings with environment variable support
    """
    
    # ================================
    # 🌍 Environment Settings
    # ================================
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # ================================
    # 🌐 Server Configuration
    # ================================
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    API_PREFIX: str = Field(default="/api/v1", env="API_PREFIX")
    
    # ================================
    # 🤖 AI/ML Configuration
    # ================================
    # OpenAI Configuration
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    OPENAI_MODEL: str = Field(default="gpt-3.5-turbo", env="OPENAI_MODEL")
    OPENAI_MAX_TOKENS: int = Field(default=2048, env="OPENAI_MAX_TOKENS")
    OPENAI_TEMPERATURE: float = Field(default=0.7, env="OPENAI_TEMPERATURE")
    OPENAI_TIMEOUT: int = Field(default=30, env="OPENAI_TIMEOUT")
    
    # Hugging Face Configuration
    HUGGINGFACE_API_KEY: Optional[str] = Field(default=None, env="HUGGINGFACE_API_KEY")
    HUGGINGFACE_MODEL: str = Field(default="microsoft/DialoGPT-medium", env="HUGGINGFACE_MODEL")
    HUGGINGFACE_CACHE_DIR: str = Field(default="./models/cache", env="HUGGINGFACE_CACHE_DIR")
    
    # Local Model Configuration
    LOCAL_MODEL_PATH: str = Field(default="./models/local", env="LOCAL_MODEL_PATH")
    MODEL_CACHE_SIZE: int = Field(default=5, env="MODEL_CACHE_SIZE")
    MODEL_WARM_UP: bool = Field(default=True, env="MODEL_WARM_UP")
    
    # ================================
    # 🔄 Processing Configuration
    # ================================
    MAX_CONCURRENT_REQUESTS: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    REQUEST_TIMEOUT: int = Field(default=30, env="REQUEST_TIMEOUT")
    QUEUE_SIZE: int = Field(default=100, env="QUEUE_SIZE")
    BATCH_SIZE: int = Field(default=32, env="BATCH_SIZE")
    BATCH_TIMEOUT: int = Field(default=5, env="BATCH_TIMEOUT")
    
    # Memory Management
    MAX_MEMORY_USAGE: int = Field(default=2048, env="MAX_MEMORY_USAGE")  # MB
    MEMORY_CHECK_INTERVAL: int = Field(default=60, env="MEMORY_CHECK_INTERVAL")
    
    # GPU Configuration
    USE_GPU: bool = Field(default=False, env="USE_GPU")
    GPU_MEMORY_FRACTION: float = Field(default=0.8, env="GPU_MEMORY_FRACTION")
    
    # ================================
    # 🗄️ Database Configuration
    # ================================
    DATABASE_URL: Optional[str] = Field(default=None, env="DATABASE_URL")
    DATABASE_HOST: str = Field(default="localhost", env="DATABASE_HOST")
    DATABASE_PORT: int = Field(default=5432, env="DATABASE_PORT")
    DATABASE_NAME: str = Field(default="ai_service_db", env="DATABASE_NAME")
    DATABASE_USER: str = Field(default="ai_user", env="DATABASE_USER")
    DATABASE_PASSWORD: str = Field(default="ai_password", env="DATABASE_PASSWORD")
    
    # ================================
    # 🔴 Redis Configuration
    # ================================
    REDIS_URL: Optional[str] = Field(default=None, env="REDIS_URL")
    REDIS_HOST: str = Field(default="localhost", env="REDIS_HOST")
    REDIS_PORT: int = Field(default=6379, env="REDIS_PORT")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    REDIS_DB: int = Field(default=1, env="REDIS_DB")
    REDIS_TTL: int = Field(default=3600, env="REDIS_TTL")
    
    # Cache Configuration
    CACHE_ENABLED: bool = Field(default=True, env="CACHE_ENABLED")
    CACHE_TTL: int = Field(default=1800, env="CACHE_TTL")
    RESPONSE_CACHE_ENABLED: bool = Field(default=True, env="RESPONSE_CACHE_ENABLED")
    
    # ================================
    # 🔐 Security Configuration
    # ================================
    API_KEY: str = Field(default="your-ai-service-api-key", env="API_KEY")
    JWT_SECRET: Optional[str] = Field(default=None, env="JWT_SECRET")
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=3600, env="RATE_LIMIT_WINDOW")
    
    # Input Validation
    MAX_INPUT_LENGTH: int = Field(default=10000, env="MAX_INPUT_LENGTH")
    MIN_INPUT_LENGTH: int = Field(default=1, env="MIN_INPUT_LENGTH")
    SANITIZE_INPUT: bool = Field(default=True, env="SANITIZE_INPUT")
    
    # ================================
    # 📊 Monitoring Configuration
    # ================================
    # Prometheus
    PROMETHEUS_ENABLED: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    PROMETHEUS_PORT: int = Field(default=9091, env="PROMETHEUS_PORT")
    METRICS_PREFIX: str = Field(default="ai_service_", env="METRICS_PREFIX")
    
    # Health Checks
    HEALTH_CHECK_ENABLED: bool = Field(default=True, env="HEALTH_CHECK_ENABLED")
    HEALTH_CHECK_TIMEOUT: int = Field(default=5, env="HEALTH_CHECK_TIMEOUT")
    
    # Logging
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    LOG_FILE_ENABLED: bool = Field(default=False, env="LOG_FILE_ENABLED")
    LOG_FILE_PATH: str = Field(default="./logs/ai-service.log", env="LOG_FILE_PATH")
    
    # ================================
    # 🌐 CORS Configuration
    # ================================
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:3001"],
        env="CORS_ORIGINS"
    )
    
    # ================================
    # 🧪 Development Configuration
    # ================================
    AUTO_RELOAD: bool = Field(default=True, env="AUTO_RELOAD")
    MOCK_EXTERNAL_APIS: bool = Field(default=False, env="MOCK_EXTERNAL_APIS")
    
    # ================================
    # 📁 File Configuration
    # ================================
    MAX_FILE_SIZE: str = Field(default="10MB", env="MAX_FILE_SIZE")
    UPLOAD_PATH: str = Field(default="./uploads", env="UPLOAD_PATH")
    TEMP_PATH: str = Field(default="./temp", env="TEMP_PATH")
    
    # ================================
    # 🔧 Algorithm Configuration
    # ================================
    ALGORITHM_TIMEOUT: int = Field(default=60, env="ALGORITHM_TIMEOUT")
    ALGORITHM_CACHE_ENABLED: bool = Field(default=True, env="ALGORITHM_CACHE_ENABLED")
    PATTERN_DETECTION: bool = Field(default=True, env="PATTERN_DETECTION")
    
    # ================================
    # Validators
    # ================================
    
    @validator('CORS_ORIGINS', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    @validator('LOG_LEVEL')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'LOG_LEVEL must be one of {valid_levels}')
        return v.upper()
    
    @validator('ENVIRONMENT')
    def validate_environment(cls, v):
        valid_envs = ['development', 'staging', 'production', 'test']
        if v not in valid_envs:
            raise ValueError(f'ENVIRONMENT must be one of {valid_envs}')
        return v
    
    @validator('OPENAI_TEMPERATURE')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('OPENAI_TEMPERATURE must be between 0.0 and 2.0')
        return v
    
    @validator('GPU_MEMORY_FRACTION')
    def validate_gpu_memory(cls, v):
        if not 0.1 <= v <= 1.0:
            raise ValueError('GPU_MEMORY_FRACTION must be between 0.1 and 1.0')
        return v
    
    # ================================
    # Properties
    # ================================
    
    @property
    def is_development(self) -> bool:
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        return self.ENVIRONMENT == "production"
    
    @property
    def database_url_complete(self) -> str:
        if self.DATABASE_URL:
            return self.DATABASE_URL
        return f"postgresql://{self.DATABASE_USER}:{self.DATABASE_PASSWORD}@{self.DATABASE_HOST}:{self.DATABASE_PORT}/{self.DATABASE_NAME}"
    
    @property
    def redis_url_complete(self) -> str:
        if self.REDIS_URL:
            return self.REDIS_URL
        auth = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        return f"redis://{auth}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached settings instance
    """
    return Settings()
