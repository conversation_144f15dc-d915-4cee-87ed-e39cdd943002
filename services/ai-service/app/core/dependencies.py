"""
🔗 Dependencies - FastAPI Dependency Injection

Implements dependency injection for:
- Service instances and singletons
- Database connections
- Cache clients
- Authentication and authorization
- Request context and correlation IDs
"""

from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, Request, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
import structlog

from .config import get_settings
from .cache import get_cache_client, CacheClient
from ..services.vector_store import VectorStoreService
from ..services.llm_service import LLMService
from ..services.model_manager import ModelManager

logger = structlog.get_logger(__name__)
settings = get_settings()

# Security scheme
security = HTTPBearer(auto_error=False)


# ================================
# 🔐 Authentication Dependencies
# ================================

async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[Dict[str, Any]]:
    """
    Extract and validate user from JWT token
    """
    if not credentials:
        return None
    
    try:
        # Decode JWT token
        payload = jwt.decode(
            credentials.credentials,
            settings.JWT_SECRET,
            algorithms=["HS256"]
        )
        
        user_data = {
            "user_id": payload.get("sub"),
            "email": payload.get("email"),
            "roles": payload.get("roles", []),
            "permissions": payload.get("permissions", [])
        }
        
        return user_data
        
    except jwt.ExpiredSignatureError:
        logger.warning("JWT token has expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error validating JWT token: {str(e)}")
        return None


async def require_authentication(
    current_user: Optional[Dict[str, Any]] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Require valid authentication
    """
    if not current_user:
        raise HTTPException(
            status_code=401,
            detail="Authentication required"
        )
    
    return current_user


async def require_permission(permission: str):
    """
    Factory function to create permission-checking dependency
    """
    async def check_permission(
        current_user: Dict[str, Any] = Depends(require_authentication)
    ) -> Dict[str, Any]:
        user_permissions = current_user.get("permissions", [])
        
        if permission not in user_permissions and "*" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"Permission '{permission}' required"
            )
        
        return current_user
    
    return check_permission


# ================================
# 📡 Request Context Dependencies
# ================================

async def get_correlation_id(
    x_correlation_id: Optional[str] = Header(None),
    request: Request = None
) -> str:
    """
    Get or generate correlation ID for request tracking
    """
    if x_correlation_id:
        return x_correlation_id
    
    # Try to get from request state (set by middleware)
    if request and hasattr(request.state, 'correlation_id'):
        return request.state.correlation_id
    
    # Generate new correlation ID
    import uuid
    return str(uuid.uuid4())


async def get_request_context(
    request: Request,
    correlation_id: str = Depends(get_correlation_id),
    current_user: Optional[Dict[str, Any]] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get comprehensive request context
    """
    return {
        "correlation_id": correlation_id,
        "user_id": current_user.get("user_id") if current_user else None,
        "ip_address": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent"),
        "endpoint": str(request.url.path),
        "method": request.method,
        "timestamp": request.state.start_time if hasattr(request.state, 'start_time') else None
    }


# ================================
# 🗄️ Service Dependencies
# ================================

# Global service instances
_vector_store_service: Optional[VectorStoreService] = None
_llm_service: Optional[LLMService] = None
_model_manager: Optional[ModelManager] = None


async def get_vector_store() -> VectorStoreService:
    """
    Get vector store service instance
    """
    global _vector_store_service
    
    if _vector_store_service is None:
        _vector_store_service = VectorStoreService(settings)
        await _vector_store_service.initialize()
    
    return _vector_store_service


async def get_llm_service() -> LLMService:
    """
    Get LLM service instance
    """
    global _llm_service
    
    if _llm_service is None:
        _llm_service = LLMService(settings)
        await _llm_service.initialize()
    
    return _llm_service


async def get_model_manager() -> ModelManager:
    """
    Get model manager instance
    """
    global _model_manager
    
    if _model_manager is None:
        _model_manager = ModelManager(settings)
        await _model_manager.initialize()
    
    return _model_manager


# ================================
# 🔴 Cache Dependencies
# ================================

async def get_cache() -> Optional[CacheClient]:
    """
    Get cache client instance
    """
    try:
        return await get_cache_client()
    except Exception as e:
        logger.warning(f"Cache not available: {str(e)}")
        return None


# ================================
# 📊 Rate Limiting Dependencies
# ================================

async def check_rate_limit(
    request: Request,
    current_user: Optional[Dict[str, Any]] = Depends(get_current_user),
    cache: Optional[CacheClient] = Depends(get_cache)
) -> None:
    """
    Check rate limits for the current request
    """
    if not cache or not settings.RATE_LIMIT_ENABLED:
        return
    
    # Determine rate limit key
    if current_user:
        rate_limit_key = f"rate_limit:user:{current_user['user_id']}"
        limit = settings.RATE_LIMIT_PER_USER
    else:
        ip_address = request.client.host if request.client else "unknown"
        rate_limit_key = f"rate_limit:ip:{ip_address}"
        limit = settings.RATE_LIMIT_REQUESTS
    
    window = settings.RATE_LIMIT_WINDOW
    
    try:
        # Get current count
        current_count = await cache.get(rate_limit_key) or 0
        
        if current_count >= limit:
            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded",
                headers={"Retry-After": str(window)}
            )
        
        # Increment counter
        if current_count == 0:
            await cache.setex(rate_limit_key, window, 1)
        else:
            await cache.increment(rate_limit_key)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.warning(f"Rate limiting check failed: {str(e)}")
        # Don't block requests if rate limiting fails


# ================================
# 🔧 Validation Dependencies
# ================================

async def validate_content_type(
    request: Request,
    allowed_types: list = ["application/json", "text/plain"]
) -> None:
    """
    Validate request content type
    """
    content_type = request.headers.get("content-type", "").split(";")[0]
    
    if content_type not in allowed_types:
        raise HTTPException(
            status_code=415,
            detail=f"Unsupported content type. Allowed: {', '.join(allowed_types)}"
        )


async def validate_request_size(
    request: Request,
    max_size: int = 10 * 1024 * 1024  # 10MB default
) -> None:
    """
    Validate request body size
    """
    content_length = request.headers.get("content-length")
    
    if content_length and int(content_length) > max_size:
        raise HTTPException(
            status_code=413,
            detail=f"Request too large. Maximum size: {max_size} bytes"
        )


# ================================
# 🏥 Health Check Dependencies
# ================================

async def get_service_health() -> Dict[str, Any]:
    """
    Get health status of all services
    """
    health_status = {
        "cache": {"status": "unknown"},
        "vector_store": {"status": "unknown"},
        "llm_service": {"status": "unknown"},
        "model_manager": {"status": "unknown"}
    }
    
    # Check cache health
    try:
        cache = await get_cache()
        if cache:
            cache_health = await cache.health_check()
            health_status["cache"] = cache_health
        else:
            health_status["cache"] = {"status": "unavailable"}
    except Exception as e:
        health_status["cache"] = {"status": "error", "error": str(e)}
    
    # Check vector store health
    try:
        vector_store = await get_vector_store()
        # Implement health check method in VectorStoreService
        health_status["vector_store"] = {"status": "ok"}
    except Exception as e:
        health_status["vector_store"] = {"status": "error", "error": str(e)}
    
    # Check LLM service health
    try:
        llm_service = await get_llm_service()
        # Implement health check method in LLMService
        health_status["llm_service"] = {"status": "ok"}
    except Exception as e:
        health_status["llm_service"] = {"status": "error", "error": str(e)}
    
    # Check model manager health
    try:
        model_manager = await get_model_manager()
        # Implement health check method in ModelManager
        health_status["model_manager"] = {"status": "ok"}
    except Exception as e:
        health_status["model_manager"] = {"status": "error", "error": str(e)}
    
    return health_status


# ================================
# 🧹 Cleanup Dependencies
# ================================

async def cleanup_services() -> None:
    """
    Cleanup all service instances
    """
    global _vector_store_service, _llm_service, _model_manager
    
    try:
        if _vector_store_service:
            await _vector_store_service.cleanup()
            _vector_store_service = None
        
        if _llm_service:
            await _llm_service.cleanup()
            _llm_service = None
        
        if _model_manager:
            await _model_manager.cleanup()
            _model_manager = None
            
        logger.info("All services cleaned up successfully")
        
    except Exception as e:
        logger.error(f"Error during service cleanup: {str(e)}")


# ================================
# 🎯 Custom Dependencies
# ================================

def create_permission_dependency(required_permissions: list):
    """
    Create a dependency that requires multiple permissions
    """
    async def check_permissions(
        current_user: Dict[str, Any] = Depends(require_authentication)
    ) -> Dict[str, Any]:
        user_permissions = current_user.get("permissions", [])
        
        # Check if user has all required permissions
        missing_permissions = []
        for permission in required_permissions:
            if permission not in user_permissions and "*" not in user_permissions:
                missing_permissions.append(permission)
        
        if missing_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"Missing permissions: {', '.join(missing_permissions)}"
            )
        
        return current_user
    
    return check_permissions


def create_role_dependency(required_roles: list):
    """
    Create a dependency that requires specific roles
    """
    async def check_roles(
        current_user: Dict[str, Any] = Depends(require_authentication)
    ) -> Dict[str, Any]:
        user_roles = current_user.get("roles", [])
        
        # Check if user has any of the required roles
        has_required_role = any(role in user_roles for role in required_roles)
        
        if not has_required_role:
            raise HTTPException(
                status_code=403,
                detail=f"One of these roles required: {', '.join(required_roles)}"
            )
        
        return current_user
    
    return check_roles
