"""
🎯 Recommendations API - Similarity Search and Recommendations

Implements recommendation endpoints with:
- Vector similarity search
- Content-based recommendations
- Collaborative filtering
- Algorithm pattern matching
- Performance optimization suggestions
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
import time

from ...core.logging import get_logger, performance_logger, business_logger
from ...core.dependencies import get_vector_store, get_request_context
from ...services.vector_store import VectorStoreService
from ...services.embedding_service import get_embedding_service
from ...models.schemas import (
    SimilaritySearchRequest,
    SimilaritySearchResponse,
    SearchResult,
    ErrorResponse
)

router = APIRouter(prefix="/recommendations", tags=["recommendations"])
logger = get_logger(__name__)


@router.post(
    "/similar",
    response_model=SimilaritySearchResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request"},
        429: {"model": ErrorResponse, "description": "Rate limit exceeded"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    summary="Find similar content",
    description="Find content similar to the provided query using vector similarity search"
)
async def find_similar_content(
    request: SimilaritySearchRequest,
    background_tasks: BackgroundTasks,
    vector_store: VectorStoreService = Depends(get_vector_store),
    context: dict = Depends(get_request_context)
):
    """
    Find content similar to the query using vector similarity search
    """
    start_time = time.time()
    
    try:
        logger.info(
            "Finding similar content",
            query_length=len(request.query),
            top_k=request.top_k,
            threshold=request.threshold,
            user_id=request.user_id,
            correlation_id=context.get("correlation_id")
        )
        
        # Generate embedding for the query
        embedding_service = get_embedding_service()
        embedding_result = await embedding_service.generate_embeddings(
            text=request.query,
            model="text-embedding-ada-002",
            normalize=True
        )
        
        query_vector = embedding_result["embedding"]
        
        # Search for similar vectors
        similar_results = await vector_store.search_similar(
            query_vector=query_vector,
            top_k=request.top_k,
            threshold=request.threshold
        )
        
        # Convert to response format
        search_results = []
        for result in similar_results:
            search_result = SearchResult(
                id=result["id"],
                content=result["metadata"].get("content", ""),
                score=result["score"],
                metadata=result["metadata"]
            )
            search_results.append(search_result)
        
        search_time = (time.time() - start_time) * 1000
        
        # Log performance metrics
        performance_logger.log_request_duration(
            endpoint="/recommendations/similar",
            method="POST",
            duration_ms=search_time,
            status_code=200,
            user_id=request.user_id,
            correlation_id=context.get("correlation_id")
        )
        
        # Log business metrics in background
        if request.user_id:
            background_tasks.add_task(
                business_logger.log_api_usage,
                user_id=request.user_id,
                endpoint="/recommendations/similar",
                tokens_used=embedding_result["usage"]["total_tokens"],
                cost=0.0001 * (embedding_result["usage"]["total_tokens"] / 1000),
                success=True
            )
        
        response = SimilaritySearchResponse(
            results=search_results,
            query=request.query,
            total_results=len(search_results),
            search_time_ms=search_time
        )
        
        logger.info(
            "Similar content found successfully",
            results_count=len(search_results),
            search_time_ms=search_time,
            correlation_id=context.get("correlation_id")
        )
        
        return response
        
    except Exception as e:
        search_time = (time.time() - start_time) * 1000
        
        logger.error(
            "Failed to find similar content",
            error=str(e),
            search_time_ms=search_time,
            correlation_id=context.get("correlation_id")
        )
        
        # Log failed business metrics
        if request.user_id:
            background_tasks.add_task(
                business_logger.log_api_usage,
                user_id=request.user_id,
                endpoint="/recommendations/similar",
                tokens_used=0,
                cost=0.0,
                success=False
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to find similar content: {str(e)}"
        )


@router.get(
    "/algorithms/{algorithm_type}",
    summary="Get algorithm recommendations",
    description="Get recommended algorithms for specific problem types"
)
async def get_algorithm_recommendations(
    algorithm_type: str,
    difficulty: Optional[str] = None,
    context: dict = Depends(get_request_context)
):
    """
    Get algorithm recommendations for specific problem types
    """
    try:
        # Mock algorithm recommendations
        recommendations = {
            "array": [
                {
                    "name": "Two Pointers",
                    "description": "Efficient technique for array problems with sorted data",
                    "time_complexity": "O(n)",
                    "space_complexity": "O(1)",
                    "use_cases": ["Finding pairs", "Removing duplicates", "Palindrome checking"],
                    "difficulty": "Easy to Medium",
                    "examples": ["Two Sum II", "Remove Duplicates", "Valid Palindrome"]
                },
                {
                    "name": "Sliding Window",
                    "description": "Technique for problems involving subarrays or substrings",
                    "time_complexity": "O(n)",
                    "space_complexity": "O(1) to O(k)",
                    "use_cases": ["Maximum subarray", "Substring problems", "Fixed-size windows"],
                    "difficulty": "Medium",
                    "examples": ["Longest Substring", "Maximum Sum Subarray", "Minimum Window"]
                }
            ],
            "tree": [
                {
                    "name": "Depth-First Search (DFS)",
                    "description": "Recursive traversal technique for tree problems",
                    "time_complexity": "O(n)",
                    "space_complexity": "O(h)",
                    "use_cases": ["Tree traversal", "Path finding", "Subtree problems"],
                    "difficulty": "Easy to Hard",
                    "examples": ["Binary Tree Paths", "Maximum Depth", "Validate BST"]
                },
                {
                    "name": "Breadth-First Search (BFS)",
                    "description": "Level-order traversal for tree problems",
                    "time_complexity": "O(n)",
                    "space_complexity": "O(w)",
                    "use_cases": ["Level traversal", "Shortest path", "Tree width"],
                    "difficulty": "Easy to Medium",
                    "examples": ["Level Order Traversal", "Binary Tree Width", "Minimum Depth"]
                }
            ],
            "graph": [
                {
                    "name": "Dijkstra's Algorithm",
                    "description": "Shortest path algorithm for weighted graphs",
                    "time_complexity": "O((V + E) log V)",
                    "space_complexity": "O(V)",
                    "use_cases": ["Shortest path", "Network routing", "GPS navigation"],
                    "difficulty": "Medium to Hard",
                    "examples": ["Network Delay Time", "Cheapest Flights", "Path with Maximum Probability"]
                }
            ],
            "dynamic_programming": [
                {
                    "name": "Memoization",
                    "description": "Top-down approach with caching",
                    "time_complexity": "Varies",
                    "space_complexity": "O(n) to O(n²)",
                    "use_cases": ["Overlapping subproblems", "Recursive optimization"],
                    "difficulty": "Medium to Hard",
                    "examples": ["Fibonacci", "Climbing Stairs", "Coin Change"]
                }
            ]
        }
        
        algorithm_recommendations = recommendations.get(algorithm_type.lower(), [])
        
        # Filter by difficulty if specified
        if difficulty:
            algorithm_recommendations = [
                algo for algo in algorithm_recommendations
                if difficulty.lower() in algo["difficulty"].lower()
            ]
        
        return {
            "algorithm_type": algorithm_type,
            "difficulty_filter": difficulty,
            "recommendations": algorithm_recommendations,
            "total_count": len(algorithm_recommendations)
        }
        
    except Exception as e:
        logger.error(f"Failed to get algorithm recommendations: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get algorithm recommendations: {str(e)}"
        )


@router.get(
    "/patterns",
    summary="Get coding patterns",
    description="Get recommended coding patterns and techniques"
)
async def get_coding_patterns():
    """
    Get recommended coding patterns and techniques
    """
    patterns = [
        {
            "name": "Two Pointers",
            "category": "Array/String",
            "description": "Use two pointers moving towards each other or in the same direction",
            "when_to_use": "Sorted arrays, palindromes, pair finding",
            "time_complexity": "O(n)",
            "space_complexity": "O(1)",
            "examples": ["Two Sum II", "3Sum", "Container With Most Water"]
        },
        {
            "name": "Sliding Window",
            "category": "Array/String",
            "description": "Maintain a window of elements and slide it across the data",
            "when_to_use": "Subarray/substring problems, fixed or variable window size",
            "time_complexity": "O(n)",
            "space_complexity": "O(1) to O(k)",
            "examples": ["Longest Substring Without Repeating", "Minimum Window Substring"]
        },
        {
            "name": "Fast & Slow Pointers",
            "category": "Linked List",
            "description": "Use two pointers moving at different speeds",
            "when_to_use": "Cycle detection, finding middle element",
            "time_complexity": "O(n)",
            "space_complexity": "O(1)",
            "examples": ["Linked List Cycle", "Find Middle of Linked List"]
        },
        {
            "name": "Merge Intervals",
            "category": "Intervals",
            "description": "Sort intervals and merge overlapping ones",
            "when_to_use": "Overlapping intervals, scheduling problems",
            "time_complexity": "O(n log n)",
            "space_complexity": "O(n)",
            "examples": ["Merge Intervals", "Insert Interval", "Meeting Rooms"]
        },
        {
            "name": "Tree DFS",
            "category": "Tree",
            "description": "Depth-first traversal of tree structures",
            "when_to_use": "Tree traversal, path problems, subtree operations",
            "time_complexity": "O(n)",
            "space_complexity": "O(h)",
            "examples": ["Binary Tree Paths", "Path Sum", "Diameter of Binary Tree"]
        },
        {
            "name": "Tree BFS",
            "category": "Tree",
            "description": "Breadth-first traversal using queue",
            "when_to_use": "Level-order traversal, shortest path in trees",
            "time_complexity": "O(n)",
            "space_complexity": "O(w)",
            "examples": ["Binary Tree Level Order", "Binary Tree Right Side View"]
        },
        {
            "name": "Dynamic Programming",
            "category": "Optimization",
            "description": "Break down problems into overlapping subproblems",
            "when_to_use": "Optimization problems, overlapping subproblems",
            "time_complexity": "Varies",
            "space_complexity": "O(n) to O(n²)",
            "examples": ["Fibonacci", "Coin Change", "Longest Common Subsequence"]
        }
    ]
    
    return {
        "patterns": patterns,
        "total_count": len(patterns),
        "categories": list(set(pattern["category"] for pattern in patterns))
    }
