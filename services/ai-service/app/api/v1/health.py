"""
🏥 AI Service Health Check Endpoints

Implements comprehensive health monitoring with:
- Basic health status
- Detailed system health
- Model availability checks
- Performance metrics
"""

import asyncio
import psutil
import time
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import structlog

from app.core.config import get_settings, Settings

logger = structlog.get_logger(__name__)

router = APIRouter()


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str
    timestamp: str
    uptime: float
    version: str = "1.0.0"


class DetailedHealthResponse(BaseModel):
    """Detailed health check response model"""
    status: str
    timestamp: str
    uptime: float
    version: str = "1.0.0"
    system: Dict[str, Any]
    services: Dict[str, Any]
    models: Dict[str, Any]
    performance: Dict[str, Any]


@router.get("/", response_model=HealthResponse)
async def health_check():
    """
    Basic health check endpoint
    Returns simple health status
    """
    try:
        return HealthResponse(
            status="healthy",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S UTC", time.gmtime()),
            uptime=time.time() - psutil.boot_time()
        )
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@router.get("/detailed", response_model=DetailedHealthResponse)
async def detailed_health_check(settings: Settings = Depends(get_settings)):
    """
    Detailed health check endpoint
    Returns comprehensive system health information
    """
    try:
        # System information
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        cpu_percent = psutil.cpu_percent(interval=1)
        
        system_info = {
            "cpu": {
                "usage_percent": cpu_percent,
                "count": psutil.cpu_count(),
                "load_avg": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            }
        }
        
        # Service checks
        services_status = {
            "database": await check_database_connection(settings),
            "redis": await check_redis_connection(settings),
            "external_apis": await check_external_apis(settings)
        }
        
        # Model availability
        models_status = {
            "embedding_model": await check_embedding_model(),
            "llm_model": await check_llm_model(),
            "local_models": await check_local_models()
        }
        
        # Performance metrics
        performance_metrics = {
            "avg_response_time": await get_avg_response_time(),
            "requests_per_minute": await get_requests_per_minute(),
            "error_rate": await get_error_rate(),
            "cache_hit_rate": await get_cache_hit_rate()
        }
        
        # Determine overall status
        overall_status = "healthy"
        if any(service["status"] != "healthy" for service in services_status.values()):
            overall_status = "degraded"
        if any(model["status"] != "available" for model in models_status.values()):
            overall_status = "degraded"
        if system_info["memory"]["percent"] > 90 or system_info["disk"]["percent"] > 90:
            overall_status = "degraded"
        
        return DetailedHealthResponse(
            status=overall_status,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S UTC", time.gmtime()),
            uptime=time.time() - psutil.boot_time(),
            system=system_info,
            services=services_status,
            models=models_status,
            performance=performance_metrics
        )
        
    except Exception as e:
        logger.error("Detailed health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@router.get("/ready")
async def readiness_check(settings: Settings = Depends(get_settings)):
    """
    Kubernetes readiness probe
    Checks if service is ready to serve traffic
    """
    try:
        # Check critical dependencies
        db_status = await check_database_connection(settings)
        redis_status = await check_redis_connection(settings)
        models_status = await check_critical_models()
        
        if (db_status["status"] == "healthy" and 
            redis_status["status"] == "healthy" and 
            models_status["status"] == "ready"):
            return {"status": "ready"}
        else:
            raise HTTPException(status_code=503, detail="Service not ready")
            
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service not ready")


@router.get("/live")
async def liveness_check():
    """
    Kubernetes liveness probe
    Checks if service is alive and responsive
    """
    try:
        # Simple check to ensure the service is responsive
        memory = psutil.virtual_memory()
        if memory.percent > 95:  # If memory usage is too high
            raise HTTPException(status_code=503, detail="High memory usage")
        
        return {"status": "alive"}
        
    except Exception as e:
        logger.error("Liveness check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service not alive")


# Helper functions for health checks

async def check_database_connection(settings: Settings) -> Dict[str, Any]:
    """Check database connectivity"""
    try:
        # Simulate database check
        # In real implementation, you would test actual database connection
        await asyncio.sleep(0.01)  # Simulate connection time
        
        return {
            "status": "healthy",
            "response_time": "10ms",
            "host": settings.DATABASE_HOST,
            "port": settings.DATABASE_PORT
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "host": settings.DATABASE_HOST,
            "port": settings.DATABASE_PORT
        }


async def check_redis_connection(settings: Settings) -> Dict[str, Any]:
    """Check Redis connectivity"""
    try:
        # Simulate Redis check
        # In real implementation, you would test actual Redis connection
        await asyncio.sleep(0.01)  # Simulate connection time
        
        return {
            "status": "healthy",
            "response_time": "5ms",
            "host": settings.REDIS_HOST,
            "port": settings.REDIS_PORT
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "host": settings.REDIS_HOST,
            "port": settings.REDIS_PORT
        }


async def check_external_apis(settings: Settings) -> Dict[str, Any]:
    """Check external API connectivity"""
    try:
        # Check OpenAI API if configured
        if settings.OPENAI_API_KEY:
            # Simulate API check
            await asyncio.sleep(0.05)
        
        return {
            "status": "healthy",
            "openai": "available" if settings.OPENAI_API_KEY else "not_configured",
            "huggingface": "available" if settings.HUGGINGFACE_API_KEY else "not_configured"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


async def check_embedding_model() -> Dict[str, Any]:
    """Check embedding model availability"""
    try:
        # Simulate model check
        await asyncio.sleep(0.02)
        
        return {
            "status": "available",
            "model": "sentence-transformers/all-MiniLM-L6-v2",
            "loaded": True
        }
    except Exception as e:
        return {
            "status": "unavailable",
            "error": str(e)
        }


async def check_llm_model() -> Dict[str, Any]:
    """Check LLM model availability"""
    try:
        # Simulate model check
        await asyncio.sleep(0.02)
        
        return {
            "status": "available",
            "model": "gpt-3.5-turbo",
            "loaded": True
        }
    except Exception as e:
        return {
            "status": "unavailable",
            "error": str(e)
        }


async def check_local_models() -> Dict[str, Any]:
    """Check local models availability"""
    try:
        # Simulate local models check
        await asyncio.sleep(0.01)
        
        return {
            "status": "available",
            "count": 2,
            "models": ["embedding_model", "classification_model"]
        }
    except Exception as e:
        return {
            "status": "unavailable",
            "error": str(e)
        }


async def check_critical_models() -> Dict[str, Any]:
    """Check critical models for readiness"""
    try:
        embedding_status = await check_embedding_model()
        llm_status = await check_llm_model()
        
        if (embedding_status["status"] == "available" and 
            llm_status["status"] == "available"):
            return {"status": "ready"}
        else:
            return {"status": "not_ready"}
    except Exception:
        return {"status": "not_ready"}


# Performance metrics helpers (simplified implementations)

async def get_avg_response_time() -> float:
    """Get average response time"""
    # In real implementation, this would come from metrics storage
    return 150.5  # ms


async def get_requests_per_minute() -> int:
    """Get requests per minute"""
    # In real implementation, this would come from metrics storage
    return 45


async def get_error_rate() -> float:
    """Get error rate percentage"""
    # In real implementation, this would come from metrics storage
    return 0.5  # %


async def get_cache_hit_rate() -> float:
    """Get cache hit rate percentage"""
    # In real implementation, this would come from metrics storage
    return 85.2  # %
