"""
💬 Chat API - LLM Chat Completion Endpoints

Implements chat completion endpoints with:
- OpenAI-compatible chat completions
- Streaming responses
- Context management
- Usage tracking and billing
- Error handling and validation
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import StreamingResponse
import time
import json

from ...core.logging import get_logger, performance_logger, business_logger
from ...core.dependencies import get_llm_service, get_request_context
from ...services.llm_service import LLMService
from ...models.schemas import (
    ChatCompletionRequest,
    ChatCompletionResponse,
    ErrorResponse,
    ChatMessage
)

router = APIRouter(prefix="/chat", tags=["chat"])
logger = get_logger(__name__)


@router.post(
    "/completions",
    response_model=ChatCompletionResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request"},
        429: {"model": ErrorResponse, "description": "Rate limit exceeded"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    summary="Create chat completion",
    description="Generate AI chat completions using various LLM models"
)
async def create_chat_completion(
    request: ChatCompletionRequest,
    background_tasks: BackgroundTasks,
    llm_service: LLMService = Depends(get_llm_service),
    context: dict = Depends(get_request_context)
):
    """
    Create a chat completion using the specified model
    """
    start_time = time.time()
    
    try:
        logger.info(
            "Creating chat completion",
            model=request.model,
            message_count=len(request.messages),
            user_id=request.user_id,
            correlation_id=context.get("correlation_id")
        )
        
        # Handle streaming vs non-streaming
        if request.stream:
            return StreamingResponse(
                _stream_chat_completion(request, llm_service, context, background_tasks),
                media_type="text/plain"
            )
        else:
            # Generate completion
            result = await llm_service.chat_completion(
                messages=request.messages,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                stream=False
            )
            
            processing_time = (time.time() - start_time) * 1000
            
            # Log performance metrics
            performance_logger.log_model_inference(
                model_name=request.model,
                input_tokens=result["usage"]["prompt_tokens"],
                output_tokens=result["usage"]["completion_tokens"],
                duration_ms=processing_time,
                success=True
            )
            
            # Log business metrics in background
            if request.user_id:
                background_tasks.add_task(
                    business_logger.log_api_usage,
                    user_id=request.user_id,
                    endpoint="/chat/completions",
                    tokens_used=result["usage"]["total_tokens"],
                    cost=_calculate_chat_cost(result["usage"]["total_tokens"], request.model),
                    success=True
                )
            
            response = ChatCompletionResponse(
                id=result["id"],
                object=result["object"],
                created=result["created"],
                model=result["model"],
                choices=result["choices"],
                usage=result["usage"]
            )
            
            logger.info(
                "Chat completion created successfully",
                model=request.model,
                tokens_used=result["usage"]["total_tokens"],
                processing_time_ms=processing_time,
                correlation_id=context.get("correlation_id")
            )
            
            return response
            
    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        
        logger.error(
            "Failed to create chat completion",
            error=str(e),
            model=request.model,
            processing_time_ms=processing_time,
            correlation_id=context.get("correlation_id")
        )
        
        # Log failed business metrics
        if request.user_id:
            background_tasks.add_task(
                business_logger.log_api_usage,
                user_id=request.user_id,
                endpoint="/chat/completions",
                tokens_used=0,
                cost=0.0,
                success=False
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create chat completion: {str(e)}"
        )


async def _stream_chat_completion(
    request: ChatCompletionRequest,
    llm_service: LLMService,
    context: dict,
    background_tasks: BackgroundTasks
):
    """
    Stream chat completion responses
    """
    try:
        # Generate streaming completion
        result = await llm_service.chat_completion(
            messages=request.messages,
            model=request.model,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            stream=True
        )
        
        stream = result["stream"]
        total_tokens = 0
        
        async for chunk in stream:
            if chunk.choices:
                # Format chunk for streaming
                chunk_data = {
                    "id": chunk.id,
                    "object": "chat.completion.chunk",
                    "created": chunk.created,
                    "model": chunk.model,
                    "choices": [
                        {
                            "index": choice.index,
                            "delta": {
                                "role": choice.delta.role if choice.delta.role else None,
                                "content": choice.delta.content if choice.delta.content else ""
                            },
                            "finish_reason": choice.finish_reason
                        }
                        for choice in chunk.choices
                    ]
                }
                
                yield f"data: {json.dumps(chunk_data)}\n\n"
                
                # Track tokens (approximate)
                if chunk.choices[0].delta.content:
                    total_tokens += len(chunk.choices[0].delta.content.split())
        
        # Send final chunk
        yield "data: [DONE]\n\n"
        
        # Log metrics in background
        if request.user_id:
            background_tasks.add_task(
                business_logger.log_api_usage,
                user_id=request.user_id,
                endpoint="/chat/completions",
                tokens_used=total_tokens,
                cost=_calculate_chat_cost(total_tokens, request.model),
                success=True
            )
        
    except Exception as e:
        logger.error(f"Streaming chat completion failed: {str(e)}")
        error_chunk = {
            "error": {
                "message": str(e),
                "type": "server_error"
            }
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"


@router.get(
    "/models",
    summary="List available chat models",
    description="Get list of available chat completion models"
)
async def list_chat_models():
    """
    List available chat completion models
    """
    models = [
        {
            "id": "gpt-3.5-turbo",
            "object": "model",
            "created": 1677610602,
            "owned_by": "openai",
            "capabilities": ["chat", "completion"],
            "max_tokens": 4096,
            "cost_per_1k_tokens": {
                "input": 0.0015,
                "output": 0.002
            }
        },
        {
            "id": "gpt-4",
            "object": "model",
            "created": 1687882411,
            "owned_by": "openai",
            "capabilities": ["chat", "completion"],
            "max_tokens": 8192,
            "cost_per_1k_tokens": {
                "input": 0.03,
                "output": 0.06
            }
        },
        {
            "id": "gpt-4-turbo",
            "object": "model",
            "created": 1712361441,
            "owned_by": "openai",
            "capabilities": ["chat", "completion"],
            "max_tokens": 128000,
            "cost_per_1k_tokens": {
                "input": 0.01,
                "output": 0.03
            }
        }
    ]
    
    return {
        "object": "list",
        "data": models
    }


def _calculate_chat_cost(tokens: int, model: str) -> float:
    """
    Calculate the cost of chat completion based on tokens and model
    """
    # Pricing per 1K tokens (example rates)
    pricing = {
        "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
        "gpt-4": {"input": 0.03, "output": 0.06},
        "gpt-4-turbo": {"input": 0.01, "output": 0.03},
    }
    
    rates = pricing.get(model, {"input": 0.002, "output": 0.002})
    
    # For simplicity, assume 50/50 split between input and output tokens
    input_tokens = tokens * 0.5
    output_tokens = tokens * 0.5
    
    input_cost = (input_tokens / 1000) * rates["input"]
    output_cost = (output_tokens / 1000) * rates["output"]
    
    return input_cost + output_cost
