"""
🔤 Embeddings API - Text Embedding Generation

Implements text embedding endpoints with:
- Multiple embedding models support
- Batch processing capabilities
- Caching for performance
- Usage tracking and monitoring
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field, validator
import time
import asyncio

from ...core.logging import get_logger, performance_logger, business_logger
from ...core.config import get_settings
from ...services.embedding_service import EmbeddingService, get_embedding_service
from ...models.schemas import EmbeddingRequest, EmbeddingResponse, ErrorResponse

router = APIRouter(prefix="/embeddings", tags=["embeddings"])
logger = get_logger(__name__)
settings = get_settings()


class BatchEmbeddingRequest(BaseModel):
    """Request model for batch embedding generation"""
    
    texts: List[str] = Field(
        ...,
        min_items=1,
        max_items=100,
        description="List of texts to generate embeddings for"
    )
    model: str = Field(
        default="text-embedding-ada-002",
        description="Embedding model to use"
    )
    normalize: bool = Field(
        default=True,
        description="Whether to normalize the embeddings"
    )
    user_id: Optional[str] = Field(
        None,
        description="User ID for tracking and billing"
    )
    
    @validator('texts')
    def validate_texts(cls, v):
        for text in v:
            if len(text.strip()) == 0:
                raise ValueError("Text cannot be empty")
            if len(text) > 10000:
                raise ValueError("Text cannot exceed 10,000 characters")
        return v


class BatchEmbeddingResponse(BaseModel):
    """Response model for batch embedding generation"""
    
    embeddings: List[List[float]] = Field(
        ...,
        description="Generated embeddings for each input text"
    )
    model: str = Field(
        ...,
        description="Model used for generation"
    )
    dimensions: int = Field(
        ...,
        description="Embedding dimensions"
    )
    usage: dict = Field(
        ...,
        description="Token usage information"
    )
    processing_time_ms: float = Field(
        ...,
        description="Processing time in milliseconds"
    )


@router.post(
    "/generate",
    response_model=EmbeddingResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request"},
        429: {"model": ErrorResponse, "description": "Rate limit exceeded"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    summary="Generate text embeddings",
    description="Generate vector embeddings for a single text input using specified model"
)
async def generate_embeddings(
    request: EmbeddingRequest,
    background_tasks: BackgroundTasks,
    embedding_service: EmbeddingService = Depends(get_embedding_service)
):
    """
    Generate embeddings for a single text input
    """
    start_time = time.time()
    
    try:
        logger.info(
            "Generating embeddings",
            model=request.model,
            text_length=len(request.text),
            user_id=request.user_id
        )
        
        # Generate embeddings
        result = await embedding_service.generate_embeddings(
            text=request.text,
            model=request.model,
            normalize=request.normalize
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        # Log performance metrics
        performance_logger.log_model_inference(
            model_name=request.model,
            input_tokens=result["usage"]["prompt_tokens"],
            output_tokens=0,  # Embeddings don't have output tokens
            duration_ms=processing_time,
            success=True
        )
        
        # Log business metrics in background
        if request.user_id:
            background_tasks.add_task(
                business_logger.log_api_usage,
                user_id=request.user_id,
                endpoint="/embeddings/generate",
                tokens_used=result["usage"]["total_tokens"],
                cost=calculate_embedding_cost(result["usage"]["total_tokens"], request.model),
                success=True
            )
        
        response = EmbeddingResponse(
            embeddings=[result["embedding"]],
            model=result["model"],
            dimensions=len(result["embedding"]),
            usage=result["usage"],
            processing_time_ms=processing_time
        )
        
        logger.info(
            "Embeddings generated successfully",
            model=request.model,
            dimensions=response.dimensions,
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        
        logger.error(
            "Failed to generate embeddings",
            error=str(e),
            model=request.model,
            processing_time_ms=processing_time
        )
        
        # Log failed business metrics
        if request.user_id:
            background_tasks.add_task(
                business_logger.log_api_usage,
                user_id=request.user_id,
                endpoint="/embeddings/generate",
                tokens_used=0,
                cost=0.0,
                success=False
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate embeddings: {str(e)}"
        )


@router.post(
    "/batch",
    response_model=BatchEmbeddingResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request"},
        429: {"model": ErrorResponse, "description": "Rate limit exceeded"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    summary="Generate batch embeddings",
    description="Generate vector embeddings for multiple texts in a single request"
)
async def generate_batch_embeddings(
    request: BatchEmbeddingRequest,
    background_tasks: BackgroundTasks,
    embedding_service: EmbeddingService = Depends(get_embedding_service)
):
    """
    Generate embeddings for multiple texts in batch
    """
    start_time = time.time()
    
    try:
        logger.info(
            "Generating batch embeddings",
            model=request.model,
            batch_size=len(request.texts),
            user_id=request.user_id
        )
        
        # Generate embeddings for all texts
        tasks = [
            embedding_service.generate_embeddings(
                text=text,
                model=request.model,
                normalize=request.normalize
            )
            for text in request.texts
        ]
        
        results = await asyncio.gather(*tasks)
        
        processing_time = (time.time() - start_time) * 1000
        
        # Aggregate results
        embeddings = [result["embedding"] for result in results]
        total_tokens = sum(result["usage"]["total_tokens"] for result in results)
        dimensions = len(embeddings[0]) if embeddings else 0
        
        # Log performance metrics
        performance_logger.log_model_inference(
            model_name=request.model,
            input_tokens=total_tokens,
            output_tokens=0,
            duration_ms=processing_time,
            success=True
        )
        
        # Log business metrics in background
        if request.user_id:
            background_tasks.add_task(
                business_logger.log_api_usage,
                user_id=request.user_id,
                endpoint="/embeddings/batch",
                tokens_used=total_tokens,
                cost=calculate_embedding_cost(total_tokens, request.model),
                success=True
            )
        
        response = BatchEmbeddingResponse(
            embeddings=embeddings,
            model=request.model,
            dimensions=dimensions,
            usage={
                "prompt_tokens": total_tokens,
                "total_tokens": total_tokens
            },
            processing_time_ms=processing_time
        )
        
        logger.info(
            "Batch embeddings generated successfully",
            model=request.model,
            batch_size=len(embeddings),
            dimensions=dimensions,
            processing_time_ms=processing_time
        )
        
        return response
        
    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        
        logger.error(
            "Failed to generate batch embeddings",
            error=str(e),
            model=request.model,
            batch_size=len(request.texts),
            processing_time_ms=processing_time
        )
        
        # Log failed business metrics
        if request.user_id:
            background_tasks.add_task(
                business_logger.log_api_usage,
                user_id=request.user_id,
                endpoint="/embeddings/batch",
                tokens_used=0,
                cost=0.0,
                success=False
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate batch embeddings: {str(e)}"
        )


def calculate_embedding_cost(tokens: int, model: str) -> float:
    """
    Calculate the cost of embedding generation based on tokens and model
    """
    # Pricing per 1K tokens (example rates)
    pricing = {
        "text-embedding-ada-002": 0.0001,
        "text-embedding-3-small": 0.00002,
        "text-embedding-3-large": 0.00013,
    }
    
    rate = pricing.get(model, 0.0001)  # Default rate
    return (tokens / 1000) * rate
