"""
🤖 AI/ML Service - FastAPI Implementation

Theo knowledge base về:
- AI-Native Architecture: Vector embeddings, LLM orchestration
- Clean Architecture: Domain-driven design cho AI services
- Performance: Async processing, caching, optimization
- MLOps: Model serving, monitoring, versioning
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import make_asgi_app
import structlog

from app.core.config import get_settings
from app.core.logging import setup_logging
from app.core.dependencies import get_vector_store, get_llm_service
from app.api.v1 import embeddings, chat, recommendations, health
from app.services.model_manager import ModelManager
from app.services.vector_store import VectorStoreService
from app.services.llm_service import LLMService
from app.middleware.auth import AuthMiddleware
from app.middleware.rate_limit import RateLimitMiddleware
from app.middleware.monitoring import MonitoringMiddleware

# Setup structured logging
setup_logging()
logger = structlog.get_logger(__name__)

# Global services
model_manager: ModelManager = None
vector_store: VectorStoreService = None
llm_service: LLMService = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan management
    Theo Startup/Shutdown patterns
    """
    # Startup
    logger.info("🚀 Starting AI/ML Service...")
    
    try:
        # Initialize global services
        global model_manager, vector_store, llm_service
        
        settings = get_settings()
        
        # Initialize Model Manager
        logger.info("📦 Initializing Model Manager...")
        model_manager = ModelManager(settings)
        await model_manager.initialize()
        
        # Initialize Vector Store
        logger.info("🔍 Initializing Vector Store...")
        vector_store = VectorStoreService(settings)
        await vector_store.initialize()
        
        # Initialize LLM Service
        logger.info("🧠 Initializing LLM Service...")
        llm_service = LLMService(settings)
        await llm_service.initialize()
        
        # Warm up models
        logger.info("🔥 Warming up models...")
        await model_manager.warm_up_models()
        
        logger.info("✅ AI/ML Service started successfully!")
        
        yield
        
    except Exception as e:
        logger.error("❌ Failed to start AI/ML Service", error=str(e))
        raise
    
    # Shutdown
    logger.info("🛑 Shutting down AI/ML Service...")
    
    try:
        if model_manager:
            await model_manager.cleanup()
        if vector_store:
            await vector_store.cleanup()
        if llm_service:
            await llm_service.cleanup()
            
        logger.info("✅ AI/ML Service shutdown complete!")
        
    except Exception as e:
        logger.error("❌ Error during shutdown", error=str(e))


def create_app() -> FastAPI:
    """
    Application Factory Pattern
    Theo Clean Architecture principles
    """
    settings = get_settings()
    
    # Create FastAPI app
    app = FastAPI(
        title="Enterprise AI/ML Service",
        description="""
        🤖 **Enterprise-grade AI/ML Service** implementing:
        
        **AI-Native Architecture:**
        - Vector embeddings cho semantic search
        - LLM orchestration cho intelligent responses
        - Multi-modal AI capabilities
        - Real-time inference serving
        
        **MLOps Features:**
        - Model versioning & deployment
        - A/B testing framework
        - Performance monitoring
        - Automated retraining pipelines
        
        **Enterprise Capabilities:**
        - High-performance async processing
        - Horizontal scaling support
        - Comprehensive monitoring
        - Security & compliance ready
        """,
        version="1.0.0",
        docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
        redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
        lifespan=lifespan
    )
    
    # ================================
    # 🔧 Middleware Configuration
    # ================================
    
    # CORS Middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Compression Middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Custom Middleware
    app.add_middleware(MonitoringMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(AuthMiddleware)
    
    # ================================
    # 📊 Metrics Endpoint
    # ================================
    
    # Prometheus metrics
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)
    
    # ================================
    # 🛣️ API Routes
    # ================================
    
    # Health checks
    app.include_router(
        health.router,
        prefix="/health",
        tags=["Health"]
    )
    
    # API v1 routes
    app.include_router(
        embeddings.router,
        prefix="/api/v1/embeddings",
        tags=["Embeddings"]
    )
    
    app.include_router(
        chat.router,
        prefix="/api/v1/chat",
        tags=["Chat & LLM"]
    )
    
    app.include_router(
        recommendations.router,
        prefix="/api/v1/recommendations",
        tags=["Recommendations"]
    )
    
    # ================================
    # 🚨 Exception Handlers
    # ================================
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc):
        logger.warning(
            "HTTP exception occurred",
            status_code=exc.status_code,
            detail=exc.detail,
            path=request.url.path
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "code": exc.status_code,
                    "message": exc.detail,
                    "type": "http_error"
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc):
        logger.error(
            "Unhandled exception occurred",
            error=str(exc),
            path=request.url.path,
            exc_info=True
        )
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": 500,
                    "message": "Internal server error",
                    "type": "internal_error"
                }
            }
        )
    
    # ================================
    # 🎯 Root Endpoint
    # ================================
    
    @app.get("/")
    async def root():
        """Root endpoint với service information"""
        return {
            "service": "Enterprise AI/ML Service",
            "version": "1.0.0",
            "status": "operational",
            "features": [
                "Vector Embeddings",
                "LLM Chat Completion",
                "Semantic Search",
                "Recommendation Engine",
                "Multi-modal AI",
                "Real-time Inference"
            ],
            "endpoints": {
                "health": "/health",
                "docs": "/docs",
                "metrics": "/metrics",
                "api": "/api/v1"
            }
        }
    
    return app


# Create app instance
app = create_app()


# ================================
# 🚀 Development Server
# ================================

if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development",
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
        workers=1 if settings.ENVIRONMENT == "development" else 4
    )


"""
🎯 Key Features Implemented:

✅ **AI-Native Architecture**
   - Vector embeddings service
   - LLM orchestration
   - Multi-modal capabilities
   - Real-time inference

✅ **MLOps Integration**
   - Model management & versioning
   - Performance monitoring
   - A/B testing ready
   - Automated pipelines

✅ **Enterprise Features**
   - Async processing
   - Horizontal scaling
   - Comprehensive monitoring
   - Security middleware

✅ **Performance Optimization**
   - Model warm-up
   - Response caching
   - Connection pooling
   - Batch processing

✅ **Observability**
   - Structured logging
   - Prometheus metrics
   - Health checks
   - Error tracking

✅ **Developer Experience**
   - Auto-generated docs
   - Type safety
   - Hot reload
   - Comprehensive error handling

🔗 **API Endpoints:**
- GET /health - Health checks
- POST /api/v1/embeddings/generate - Generate embeddings
- POST /api/v1/chat/completions - LLM chat completions
- POST /api/v1/recommendations/similar - Similarity search
- GET /metrics - Prometheus metrics
- GET /docs - API documentation
"""
