"""
🚦 Rate Limiting Middleware - Request Rate Control

Implements rate limiting with:
- Per-user and per-IP rate limiting
- Sliding window algorithm
- Redis-based storage
- Configurable limits and windows
- Rate limit headers
"""

import time
from typing import Optional, Dict, Any
from fastapi import Request, HTTPException
import structlog

from ..core.config import get_settings
from ..core.cache import get_cache_client
from ..core.logging import security_logger

logger = structlog.get_logger(__name__)
settings = get_settings()


class RateLimitMiddleware:
    """Rate limiting middleware for FastAPI"""
    
    def __init__(self, app):
        self.app = app
        self.enabled = getattr(settings, 'RATE_LIMIT_ENABLED', True)
        self.default_limit = getattr(settings, 'RATE_LIMIT_REQUESTS', 100)
        self.default_window = getattr(settings, 'RATE_LIMIT_WINDOW', 3600)
        self.per_user_limit = getattr(settings, 'RATE_LIMIT_PER_USER', 50)
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http" or not self.enabled:
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        
        # Skip rate limiting for certain paths
        if self._should_skip_rate_limit(request.url.path):
            await self.app(scope, receive, send)
            return
        
        # Check rate limit
        await self._check_rate_limit(request)
        
        await self.app(scope, receive, send)
    
    def _should_skip_rate_limit(self, path: str) -> bool:
        """Check if rate limiting should be skipped for this path"""
        skip_paths = [
            "/health",
            "/metrics",
            "/docs",
            "/redoc",
            "/openapi.json"
        ]
        
        return any(path.startswith(skip_path) for skip_path in skip_paths)
    
    async def _check_rate_limit(self, request: Request) -> None:
        """Check and enforce rate limits"""
        try:
            cache_client = await get_cache_client()
            if not cache_client:
                # If cache is not available, skip rate limiting
                logger.warning("Cache not available, skipping rate limiting")
                return
            
            # Determine rate limit key and limits
            user = getattr(request.state, 'user', None)
            
            if user:
                # User-based rate limiting
                rate_limit_key = f"rate_limit:user:{user['user_id']}"
                limit = self.per_user_limit
                identifier = user['user_id']
            else:
                # IP-based rate limiting
                ip_address = request.client.host if request.client else "unknown"
                rate_limit_key = f"rate_limit:ip:{ip_address}"
                limit = self.default_limit
                identifier = ip_address
            
            window = self.default_window
            
            # Implement sliding window rate limiting
            current_time = int(time.time())
            window_start = current_time - window
            
            # Use Redis sorted set for sliding window
            pipe = cache_client.redis_client.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(rate_limit_key, 0, window_start)
            
            # Count current requests
            pipe.zcard(rate_limit_key)
            
            # Add current request
            pipe.zadd(rate_limit_key, {str(current_time): current_time})
            
            # Set expiration
            pipe.expire(rate_limit_key, window)
            
            results = await pipe.execute()
            current_count = results[1]
            
            # Check if limit exceeded
            if current_count >= limit:
                # Log rate limit violation
                security_logger.log_rate_limit_exceeded(
                    ip_address=request.client.host if request.client else "unknown",
                    endpoint=request.url.path,
                    limit=limit,
                    window_seconds=window
                )
                
                # Calculate retry after
                retry_after = window
                
                raise HTTPException(
                    status_code=429,
                    detail="Rate limit exceeded",
                    headers={
                        "X-RateLimit-Limit": str(limit),
                        "X-RateLimit-Remaining": "0",
                        "X-RateLimit-Reset": str(current_time + window),
                        "Retry-After": str(retry_after)
                    }
                )
            
            # Add rate limit headers to response
            remaining = max(0, limit - current_count - 1)
            request.state.rate_limit_headers = {
                "X-RateLimit-Limit": str(limit),
                "X-RateLimit-Remaining": str(remaining),
                "X-RateLimit-Reset": str(current_time + window)
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Rate limiting check failed: {str(e)}")
            # Don't block requests if rate limiting fails


class TokenBucketRateLimit:
    """Token bucket rate limiting implementation"""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.tokens = capacity
        self.last_refill = time.time()
    
    async def consume(self, tokens: int = 1) -> bool:
        """Try to consume tokens from the bucket"""
        now = time.time()
        
        # Refill tokens
        time_passed = now - self.last_refill
        self.tokens = min(
            self.capacity,
            self.tokens + time_passed * self.refill_rate
        )
        self.last_refill = now
        
        # Check if we have enough tokens
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        
        return False


class SlidingWindowRateLimit:
    """Sliding window rate limiting implementation"""
    
    def __init__(self, limit: int, window_seconds: int):
        self.limit = limit
        self.window_seconds = window_seconds
        self.requests = []
    
    async def is_allowed(self) -> bool:
        """Check if request is allowed under rate limit"""
        now = time.time()
        window_start = now - self.window_seconds
        
        # Remove old requests
        self.requests = [req_time for req_time in self.requests if req_time > window_start]
        
        # Check if under limit
        if len(self.requests) < self.limit:
            self.requests.append(now)
            return True
        
        return False


async def add_rate_limit_headers(request: Request, response):
    """Add rate limit headers to response"""
    if hasattr(request.state, 'rate_limit_headers'):
        for header, value in request.state.rate_limit_headers.items():
            response.headers[header] = value
