"""
🔐 Authentication Middleware - JWT Token Validation

Implements authentication middleware with:
- JWT token validation
- User context extraction
- Permission checking
- Rate limiting integration
- Security logging
"""

import time
from typing import Optional, Dict, Any
from fastapi import Request, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
import structlog

from ..core.config import get_settings
from ..core.logging import security_logger

logger = structlog.get_logger(__name__)
settings = get_settings()
security = HTTPBearer(auto_error=False)


class AuthMiddleware:
    """Authentication middleware for FastAPI"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        
        # Skip authentication for certain paths
        if self._should_skip_auth(request.url.path):
            await self.app(scope, receive, send)
            return
        
        # Extract and validate token
        user_context = await self._validate_token(request)
        
        # Add user context to request state
        if user_context:
            request.state.user = user_context
        
        await self.app(scope, receive, send)
    
    def _should_skip_auth(self, path: str) -> bool:
        """Check if authentication should be skipped for this path"""
        skip_paths = [
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/metrics"
        ]
        
        return any(path.startswith(skip_path) for skip_path in skip_paths)
    
    async def _validate_token(self, request: Request) -> Optional[Dict[str, Any]]:
        """Validate JWT token and extract user context"""
        try:
            # Get token from Authorization header
            auth_header = request.headers.get("authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header.split(" ")[1]
            
            # Decode and validate JWT
            payload = jwt.decode(
                token,
                settings.JWT_SECRET,
                algorithms=["HS256"]
            )
            
            user_context = {
                "user_id": payload.get("sub"),
                "email": payload.get("email"),
                "username": payload.get("username"),
                "roles": payload.get("roles", []),
                "permissions": payload.get("permissions", []),
                "exp": payload.get("exp"),
                "iat": payload.get("iat")
            }
            
            # Log successful authentication
            security_logger.log_authentication_attempt(
                user_id=user_context["user_id"],
                success=True,
                ip_address=request.client.host if request.client else "unknown",
                user_agent=request.headers.get("user-agent", "unknown")
            )
            
            return user_context
            
        except jwt.ExpiredSignatureError:
            security_logger.log_authentication_attempt(
                user_id=None,
                success=False,
                ip_address=request.client.host if request.client else "unknown",
                user_agent=request.headers.get("user-agent", "unknown"),
                error="Token expired"
            )
            return None
            
        except jwt.InvalidTokenError as e:
            security_logger.log_authentication_attempt(
                user_id=None,
                success=False,
                ip_address=request.client.host if request.client else "unknown",
                user_agent=request.headers.get("user-agent", "unknown"),
                error=f"Invalid token: {str(e)}"
            )
            return None
            
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return None


def get_current_user(request: Request) -> Optional[Dict[str, Any]]:
    """Get current user from request state"""
    return getattr(request.state, 'user', None)


def require_authentication(request: Request) -> Dict[str, Any]:
    """Require valid authentication"""
    user = get_current_user(request)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Authentication required"
        )
    return user


def require_permission(permission: str):
    """Factory function to create permission-checking dependency"""
    def check_permission(request: Request) -> Dict[str, Any]:
        user = require_authentication(request)
        user_permissions = user.get("permissions", [])
        
        if permission not in user_permissions and "*" not in user_permissions:
            security_logger.log_suspicious_activity(
                activity_type="unauthorized_access_attempt",
                details={
                    "required_permission": permission,
                    "user_permissions": user_permissions,
                    "endpoint": request.url.path
                },
                ip_address=request.client.host if request.client else "unknown",
                user_id=user.get("user_id")
            )
            
            raise HTTPException(
                status_code=403,
                detail=f"Permission '{permission}' required"
            )
        
        return user
    
    return check_permission


def require_role(role: str):
    """Factory function to create role-checking dependency"""
    def check_role(request: Request) -> Dict[str, Any]:
        user = require_authentication(request)
        user_roles = user.get("roles", [])
        
        if role not in user_roles:
            security_logger.log_suspicious_activity(
                activity_type="unauthorized_role_access",
                details={
                    "required_role": role,
                    "user_roles": user_roles,
                    "endpoint": request.url.path
                },
                ip_address=request.client.host if request.client else "unknown",
                user_id=user.get("user_id")
            )
            
            raise HTTPException(
                status_code=403,
                detail=f"Role '{role}' required"
            )
        
        return user
    
    return check_role
