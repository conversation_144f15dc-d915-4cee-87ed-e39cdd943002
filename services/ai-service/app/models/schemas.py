"""
📝 Pydantic Schemas - Request/Response Models for AI Service

Implements comprehensive data validation with:
- Request/response models for all endpoints
- Input validation and sanitization
- Type safety and documentation
- Error handling schemas
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field, validator, root_validator
import uuid


# ================================
# 🔧 Base Schemas
# ================================

class BaseResponse(BaseModel):
    """Base response model with common fields"""
    
    success: bool = True
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ErrorResponse(BaseModel):
    """Error response model"""
    
    success: bool = False
    error: Dict[str, Any] = Field(
        ...,
        description="Error details including code, message, and type"
    )
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class TokenUsage(BaseModel):
    """Token usage information"""
    
    prompt_tokens: int = Field(..., ge=0, description="Number of tokens in the prompt")
    completion_tokens: Optional[int] = Field(None, ge=0, description="Number of tokens in the completion")
    total_tokens: int = Field(..., ge=0, description="Total number of tokens used")
    
    @root_validator
    def validate_total_tokens(cls, values):
        prompt_tokens = values.get('prompt_tokens', 0)
        completion_tokens = values.get('completion_tokens', 0)
        total_tokens = values.get('total_tokens', 0)
        
        expected_total = prompt_tokens + (completion_tokens or 0)
        if total_tokens != expected_total:
            values['total_tokens'] = expected_total
        
        return values


# ================================
# 🔤 Embedding Schemas
# ================================

class EmbeddingRequest(BaseModel):
    """Request model for embedding generation"""
    
    text: str = Field(
        ...,
        min_length=1,
        max_length=10000,
        description="Text to generate embeddings for"
    )
    model: str = Field(
        default="text-embedding-ada-002",
        description="Embedding model to use"
    )
    normalize: bool = Field(
        default=True,
        description="Whether to normalize the embeddings"
    )
    user_id: Optional[str] = Field(
        None,
        description="User ID for tracking and billing"
    )
    
    @validator('text')
    def validate_text(cls, v):
        if not v.strip():
            raise ValueError("Text cannot be empty or only whitespace")
        return v.strip()
    
    @validator('model')
    def validate_model(cls, v):
        allowed_models = [
            "text-embedding-ada-002",
            "text-embedding-3-small",
            "text-embedding-3-large",
            "sentence-transformers/all-MiniLM-L6-v2",
            "sentence-transformers/all-mpnet-base-v2"
        ]
        if v not in allowed_models:
            raise ValueError(f"Model must be one of: {', '.join(allowed_models)}")
        return v


class EmbeddingResponse(BaseResponse):
    """Response model for embedding generation"""
    
    embeddings: List[List[float]] = Field(
        ...,
        description="Generated embeddings for the input text"
    )
    model: str = Field(
        ...,
        description="Model used for generation"
    )
    dimensions: int = Field(
        ...,
        ge=1,
        description="Embedding dimensions"
    )
    usage: TokenUsage = Field(
        ...,
        description="Token usage information"
    )
    processing_time_ms: Optional[float] = Field(
        None,
        ge=0,
        description="Processing time in milliseconds"
    )


# ================================
# 💬 Chat Completion Schemas
# ================================

class ChatRole(str, Enum):
    """Chat message roles"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


class ChatMessage(BaseModel):
    """Chat message model"""
    
    role: ChatRole = Field(..., description="Message role")
    content: str = Field(
        ...,
        min_length=1,
        max_length=50000,
        description="Message content"
    )
    name: Optional[str] = Field(
        None,
        max_length=64,
        description="Optional name for the message sender"
    )
    
    @validator('content')
    def validate_content(cls, v):
        if not v.strip():
            raise ValueError("Message content cannot be empty")
        return v.strip()


class ChatCompletionRequest(BaseModel):
    """Request model for chat completion"""
    
    messages: List[ChatMessage] = Field(
        ...,
        min_items=1,
        max_items=100,
        description="List of messages in the conversation"
    )
    model: str = Field(
        default="gpt-3.5-turbo",
        description="Model to use for completion"
    )
    max_tokens: Optional[int] = Field(
        None,
        ge=1,
        le=4096,
        description="Maximum number of tokens to generate"
    )
    temperature: Optional[float] = Field(
        0.7,
        ge=0.0,
        le=2.0,
        description="Sampling temperature"
    )
    top_p: Optional[float] = Field(
        1.0,
        ge=0.0,
        le=1.0,
        description="Nucleus sampling parameter"
    )
    stream: bool = Field(
        default=False,
        description="Whether to stream the response"
    )
    user_id: Optional[str] = Field(
        None,
        description="User ID for tracking and billing"
    )
    
    @validator('messages')
    def validate_messages(cls, v):
        if not v:
            raise ValueError("At least one message is required")
        
        # Check for alternating user/assistant pattern (optional validation)
        user_messages = [msg for msg in v if msg.role == ChatRole.USER]
        if not user_messages:
            raise ValueError("At least one user message is required")
        
        return v


class ChatChoice(BaseModel):
    """Chat completion choice"""
    
    index: int = Field(..., ge=0, description="Choice index")
    message: ChatMessage = Field(..., description="Generated message")
    finish_reason: Optional[str] = Field(
        None,
        description="Reason why the generation stopped"
    )


class ChatCompletionResponse(BaseResponse):
    """Response model for chat completion"""
    
    id: str = Field(..., description="Unique completion ID")
    object: str = Field(default="chat.completion", description="Object type")
    created: int = Field(..., description="Unix timestamp of creation")
    model: str = Field(..., description="Model used for completion")
    choices: List[ChatChoice] = Field(..., description="Generated choices")
    usage: TokenUsage = Field(..., description="Token usage information")


# ================================
# 🔍 Similarity Search Schemas
# ================================

class SimilaritySearchRequest(BaseModel):
    """Request model for similarity search"""
    
    query: str = Field(
        ...,
        min_length=1,
        max_length=10000,
        description="Search query text"
    )
    top_k: int = Field(
        default=10,
        ge=1,
        le=100,
        description="Number of results to return"
    )
    threshold: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Minimum similarity threshold"
    )
    filters: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional filters for search"
    )
    user_id: Optional[str] = Field(
        None,
        description="User ID for tracking"
    )
    
    @validator('query')
    def validate_query(cls, v):
        if not v.strip():
            raise ValueError("Query cannot be empty")
        return v.strip()


class SearchResult(BaseModel):
    """Individual search result"""
    
    id: str = Field(..., description="Result ID")
    content: str = Field(..., description="Result content")
    score: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Similarity score"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional result metadata"
    )


class SimilaritySearchResponse(BaseResponse):
    """Response model for similarity search"""
    
    results: List[SearchResult] = Field(
        ...,
        description="Search results"
    )
    query: str = Field(..., description="Original search query")
    total_results: int = Field(
        ...,
        ge=0,
        description="Total number of results found"
    )
    search_time_ms: float = Field(
        ...,
        ge=0,
        description="Search time in milliseconds"
    )


# ================================
# 🧮 Algorithm Analysis Schemas
# ================================

class ProgrammingLanguage(str, Enum):
    """Supported programming languages"""
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    PYTHON = "python"
    JAVA = "java"
    CPP = "cpp"
    CSHARP = "csharp"


class AnalysisType(str, Enum):
    """Types of code analysis"""
    COMPLEXITY = "complexity"
    PATTERNS = "patterns"
    OPTIMIZATION = "optimization"
    QUALITY = "quality"
    ALL = "all"


class AlgorithmAnalysisRequest(BaseModel):
    """Request model for algorithm analysis"""
    
    code: str = Field(
        ...,
        min_length=10,
        max_length=100000,
        description="Code to analyze"
    )
    language: ProgrammingLanguage = Field(
        ...,
        description="Programming language of the code"
    )
    analysis_type: AnalysisType = Field(
        default=AnalysisType.ALL,
        description="Type of analysis to perform"
    )
    user_id: Optional[str] = Field(
        None,
        description="User ID for tracking"
    )
    
    @validator('code')
    def validate_code(cls, v):
        if not v.strip():
            raise ValueError("Code cannot be empty")
        return v.strip()


class ComplexityAnalysis(BaseModel):
    """Time/space complexity analysis"""
    
    best_case: str = Field(..., description="Best case complexity")
    average_case: str = Field(..., description="Average case complexity")
    worst_case: str = Field(..., description="Worst case complexity")
    explanation: str = Field(..., description="Complexity explanation")
    confidence: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Analysis confidence score"
    )


class CodePattern(BaseModel):
    """Detected code pattern"""
    
    pattern: str = Field(..., description="Pattern name")
    description: str = Field(..., description="Pattern description")
    confidence: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Detection confidence"
    )
    location: Dict[str, int] = Field(
        ...,
        description="Pattern location in code"
    )


class OptimizationSuggestion(BaseModel):
    """Code optimization suggestion"""
    
    type: str = Field(..., description="Optimization type")
    description: str = Field(..., description="Optimization description")
    impact: str = Field(..., description="Expected impact level")
    effort: str = Field(..., description="Implementation effort level")
    example: Optional[str] = Field(None, description="Example implementation")


class CodeQualityMetrics(BaseModel):
    """Code quality metrics"""
    
    readability: float = Field(..., ge=0.0, le=100.0)
    maintainability: float = Field(..., ge=0.0, le=100.0)
    testability: float = Field(..., ge=0.0, le=100.0)
    performance: float = Field(..., ge=0.0, le=100.0)
    overall: float = Field(..., ge=0.0, le=100.0)


class AlgorithmAnalysisResponse(BaseResponse):
    """Response model for algorithm analysis"""
    
    time_complexity: ComplexityAnalysis = Field(
        ...,
        description="Time complexity analysis"
    )
    space_complexity: ComplexityAnalysis = Field(
        ...,
        description="Space complexity analysis"
    )
    patterns: List[CodePattern] = Field(
        ...,
        description="Detected code patterns"
    )
    optimizations: List[OptimizationSuggestion] = Field(
        ...,
        description="Optimization suggestions"
    )
    code_quality: CodeQualityMetrics = Field(
        ...,
        description="Code quality metrics"
    )
    analysis_time_ms: float = Field(
        ...,
        ge=0,
        description="Analysis time in milliseconds"
    )
