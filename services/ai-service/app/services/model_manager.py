"""
🤖 Model Manager - AI/ML Model Management Service

Implements comprehensive model management with:
- Model loading and caching
- Version control and A/B testing
- Performance monitoring
- Resource optimization
- Hot-swapping capabilities
"""

import asyncio
import os
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from pathlib import Path
import hashlib

import torch
import numpy as np
from transformers import AutoModel, AutoTokenizer
from sentence_transformers import SentenceTransformer
import structlog

from ..core.config import get_settings
from ..core.cache import get_cache_client

logger = structlog.get_logger(__name__)


class ModelInfo:
    """Model information and metadata"""
    
    def __init__(
        self,
        name: str,
        model_type: str,
        version: str,
        path: str,
        config: Dict[str, Any]
    ):
        self.name = name
        self.model_type = model_type
        self.version = version
        self.path = path
        self.config = config
        self.loaded_at: Optional[datetime] = None
        self.last_used: Optional[datetime] = None
        self.usage_count = 0
        self.memory_usage = 0
        self.model_instance = None
        self.tokenizer = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            "name": self.name,
            "model_type": self.model_type,
            "version": self.version,
            "path": self.path,
            "config": self.config,
            "loaded_at": self.loaded_at.isoformat() if self.loaded_at else None,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "usage_count": self.usage_count,
            "memory_usage": self.memory_usage,
            "is_loaded": self.model_instance is not None
        }


class ModelManager:
    """
    Centralized model management service
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.models: Dict[str, ModelInfo] = {}
        self.cache_client = None
        self.model_cache_size = getattr(settings, 'MODEL_CACHE_SIZE', 5)
        self.model_path = Path(getattr(settings, 'MODEL_STORAGE_PATH', './models'))
        self.warm_up_enabled = getattr(settings, 'MODEL_WARM_UP', True)
        self.preload_enabled = getattr(settings, 'MODEL_PRELOAD', True)
        
        # Performance tracking
        self.performance_metrics = {}
        
        # Ensure model directory exists
        self.model_path.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize the model manager"""
        try:
            logger.info("Initializing Model Manager...")
            
            # Initialize cache client
            self.cache_client = await get_cache_client()
            
            # Load model configurations
            await self._load_model_configs()
            
            # Preload models if enabled
            if self.preload_enabled:
                await self._preload_models()
            
            logger.info("Model Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Model Manager: {str(e)}")
            raise
    
    async def _load_model_configs(self) -> None:
        """Load model configurations from files"""
        config_file = self.model_path / "models.json"
        
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    configs = json.load(f)
                
                for model_config in configs.get('models', []):
                    model_info = ModelInfo(
                        name=model_config['name'],
                        model_type=model_config['type'],
                        version=model_config.get('version', '1.0.0'),
                        path=model_config['path'],
                        config=model_config.get('config', {})
                    )
                    self.models[model_info.name] = model_info
                
                logger.info(f"Loaded {len(self.models)} model configurations")
                
            except Exception as e:
                logger.error(f"Failed to load model configurations: {str(e)}")
        else:
            # Create default configuration
            await self._create_default_config()
    
    async def _create_default_config(self) -> None:
        """Create default model configuration"""
        default_models = [
            {
                "name": "sentence-transformer-mini",
                "type": "embedding",
                "version": "1.0.0",
                "path": "sentence-transformers/all-MiniLM-L6-v2",
                "config": {
                    "dimensions": 384,
                    "max_sequence_length": 512,
                    "normalize_embeddings": True
                }
            },
            {
                "name": "sentence-transformer-base",
                "type": "embedding",
                "version": "1.0.0",
                "path": "sentence-transformers/all-mpnet-base-v2",
                "config": {
                    "dimensions": 768,
                    "max_sequence_length": 512,
                    "normalize_embeddings": True
                }
            }
        ]
        
        for model_config in default_models:
            model_info = ModelInfo(
                name=model_config['name'],
                model_type=model_config['type'],
                version=model_config['version'],
                path=model_config['path'],
                config=model_config['config']
            )
            self.models[model_info.name] = model_info
        
        # Save configuration
        config_file = self.model_path / "models.json"
        with open(config_file, 'w') as f:
            json.dump({"models": [info.to_dict() for info in self.models.values()]}, f, indent=2)
    
    async def _preload_models(self) -> None:
        """Preload commonly used models"""
        preload_models = [
            "sentence-transformer-mini",
            "sentence-transformer-base"
        ]
        
        for model_name in preload_models:
            if model_name in self.models:
                try:
                    await self.load_model(model_name)
                    logger.info(f"Preloaded model: {model_name}")
                except Exception as e:
                    logger.warning(f"Failed to preload model {model_name}: {str(e)}")
    
    async def load_model(self, model_name: str) -> ModelInfo:
        """Load a model into memory"""
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found in configuration")
        
        model_info = self.models[model_name]
        
        # Check if already loaded
        if model_info.model_instance is not None:
            model_info.last_used = datetime.utcnow()
            return model_info
        
        try:
            logger.info(f"Loading model: {model_name}")
            start_time = datetime.utcnow()
            
            # Check cache size and unload if necessary
            await self._manage_cache_size()
            
            # Load based on model type
            if model_info.model_type == "embedding":
                model_info.model_instance = SentenceTransformer(model_info.path)
                model_info.tokenizer = model_info.model_instance.tokenizer
            elif model_info.model_type == "transformer":
                model_info.model_instance = AutoModel.from_pretrained(model_info.path)
                model_info.tokenizer = AutoTokenizer.from_pretrained(model_info.path)
            else:
                raise ValueError(f"Unsupported model type: {model_info.model_type}")
            
            # Update metadata
            model_info.loaded_at = datetime.utcnow()
            model_info.last_used = datetime.utcnow()
            
            # Estimate memory usage
            model_info.memory_usage = self._estimate_memory_usage(model_info.model_instance)
            
            load_time = (datetime.utcnow() - start_time).total_seconds()
            logger.info(f"Model {model_name} loaded successfully in {load_time:.2f}s")
            
            # Track performance
            self._track_model_performance(model_name, "load", load_time)
            
            return model_info
            
        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {str(e)}")
            raise
    
    async def unload_model(self, model_name: str) -> None:
        """Unload a model from memory"""
        if model_name not in self.models:
            return
        
        model_info = self.models[model_name]
        
        if model_info.model_instance is not None:
            try:
                # Clear model from memory
                del model_info.model_instance
                del model_info.tokenizer
                
                model_info.model_instance = None
                model_info.tokenizer = None
                model_info.memory_usage = 0
                
                # Force garbage collection
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                logger.info(f"Model {model_name} unloaded successfully")
                
            except Exception as e:
                logger.error(f"Error unloading model {model_name}: {str(e)}")
    
    async def get_model(self, model_name: str) -> ModelInfo:
        """Get a model, loading it if necessary"""
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found")
        
        model_info = self.models[model_name]
        
        # Load if not already loaded
        if model_info.model_instance is None:
            await self.load_model(model_name)
        
        # Update usage statistics
        model_info.last_used = datetime.utcnow()
        model_info.usage_count += 1
        
        return model_info
    
    async def _manage_cache_size(self) -> None:
        """Manage model cache size by unloading least recently used models"""
        loaded_models = [
            info for info in self.models.values()
            if info.model_instance is not None
        ]
        
        if len(loaded_models) >= self.model_cache_size:
            # Sort by last used time (oldest first)
            loaded_models.sort(key=lambda x: x.last_used or datetime.min)
            
            # Unload oldest models
            models_to_unload = loaded_models[:len(loaded_models) - self.model_cache_size + 1]
            
            for model_info in models_to_unload:
                await self.unload_model(model_info.name)
                logger.info(f"Unloaded model {model_info.name} due to cache size limit")
    
    def _estimate_memory_usage(self, model) -> int:
        """Estimate memory usage of a model in bytes"""
        try:
            if hasattr(model, 'get_memory_footprint'):
                return model.get_memory_footprint()
            
            # Rough estimation based on parameters
            total_params = 0
            if hasattr(model, 'parameters'):
                for param in model.parameters():
                    total_params += param.numel()
            
            # Assume 4 bytes per parameter (float32)
            return total_params * 4
            
        except Exception:
            return 0
    
    def _track_model_performance(self, model_name: str, operation: str, duration: float) -> None:
        """Track model performance metrics"""
        if model_name not in self.performance_metrics:
            self.performance_metrics[model_name] = {
                "load_times": [],
                "inference_times": [],
                "usage_count": 0,
                "error_count": 0
            }
        
        metrics = self.performance_metrics[model_name]
        
        if operation == "load":
            metrics["load_times"].append(duration)
        elif operation == "inference":
            metrics["inference_times"].append(duration)
        
        # Keep only recent metrics (last 100 operations)
        for key in ["load_times", "inference_times"]:
            if len(metrics[key]) > 100:
                metrics[key] = metrics[key][-100:]
    
    async def warm_up_models(self) -> None:
        """Warm up models by running test inferences"""
        if not self.warm_up_enabled:
            return
        
        logger.info("Warming up models...")
        
        for model_name, model_info in self.models.items():
            try:
                if model_info.model_type == "embedding":
                    # Load and test embedding model
                    model = await self.get_model(model_name)
                    test_text = "This is a test sentence for model warm-up."
                    
                    start_time = datetime.utcnow()
                    _ = model.model_instance.encode(test_text)
                    warm_up_time = (datetime.utcnow() - start_time).total_seconds()
                    
                    logger.info(f"Model {model_name} warmed up in {warm_up_time:.2f}s")
                    
            except Exception as e:
                logger.warning(f"Failed to warm up model {model_name}: {str(e)}")
    
    async def get_model_stats(self) -> Dict[str, Any]:
        """Get comprehensive model statistics"""
        stats = {
            "total_models": len(self.models),
            "loaded_models": len([m for m in self.models.values() if m.model_instance is not None]),
            "total_memory_usage": sum(m.memory_usage for m in self.models.values()),
            "models": {}
        }
        
        for name, model_info in self.models.items():
            model_stats = model_info.to_dict()
            
            # Add performance metrics
            if name in self.performance_metrics:
                perf = self.performance_metrics[name]
                model_stats["performance"] = {
                    "avg_load_time": np.mean(perf["load_times"]) if perf["load_times"] else 0,
                    "avg_inference_time": np.mean(perf["inference_times"]) if perf["inference_times"] else 0,
                    "total_usage": perf["usage_count"],
                    "error_count": perf["error_count"]
                }
            
            stats["models"][name] = model_stats
        
        return stats
    
    async def cleanup(self) -> None:
        """Cleanup all loaded models"""
        logger.info("Cleaning up Model Manager...")
        
        for model_name in list(self.models.keys()):
            await self.unload_model(model_name)
        
        self.models.clear()
        self.performance_metrics.clear()
        
        logger.info("Model Manager cleanup completed")
