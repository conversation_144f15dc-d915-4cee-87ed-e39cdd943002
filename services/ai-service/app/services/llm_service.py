"""
🧠 LLM Service - Large Language Model Integration

Implements LLM capabilities with:
- Multiple LLM provider support (OpenAI, HuggingFace, local)
- Chat completion and text generation
- Streaming responses
- Token management and cost tracking
- Error handling and retry logic
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime

import openai
import structlog
from ..core.config import get_settings
from ..models.schemas import ChatMessage, ChatCompletionRequest, ChatCompletionResponse

logger = structlog.get_logger(__name__)


class LLMService:
    """Large Language Model service"""
    
    def __init__(self, settings):
        self.settings = settings
        self.openai_client = None
        if settings.OPENAI_API_KEY:
            self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def initialize(self) -> None:
        """Initialize LLM service"""
        logger.info("Initializing LLM Service...")
        # Test connection if OpenAI is available
        if self.openai_client:
            try:
                # Test with a simple request
                await self.openai_client.models.list()
                logger.info("OpenAI connection verified")
            except Exception as e:
                logger.warning(f"OpenAI connection failed: {str(e)}")
        
        logger.info("LLM Service initialized")
    
    async def chat_completion(
        self,
        messages: List[ChatMessage],
        model: str = "gpt-3.5-turbo",
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Generate chat completion"""
        try:
            if not self.openai_client:
                raise ValueError("OpenAI client not available")
            
            # Convert messages to OpenAI format
            openai_messages = [
                {"role": msg.role.value, "content": msg.content}
                for msg in messages
            ]
            
            start_time = time.time()
            
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=openai_messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream
            )
            
            processing_time = (time.time() - start_time) * 1000
            
            if stream:
                return {"stream": response, "processing_time_ms": processing_time}
            else:
                return {
                    "id": response.id,
                    "object": response.object,
                    "created": response.created,
                    "model": response.model,
                    "choices": [
                        {
                            "index": choice.index,
                            "message": {
                                "role": choice.message.role,
                                "content": choice.message.content
                            },
                            "finish_reason": choice.finish_reason
                        }
                        for choice in response.choices
                    ],
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    },
                    "processing_time_ms": processing_time
                }
                
        except Exception as e:
            logger.error(f"Chat completion failed: {str(e)}")
            raise
    
    async def cleanup(self) -> None:
        """Cleanup LLM service"""
        if self.openai_client:
            await self.openai_client.close()
        logger.info("LLM Service cleaned up")
