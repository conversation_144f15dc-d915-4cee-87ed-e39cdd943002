"""
🔤 Embedding Service - Text Embedding Generation Service

Implements embedding generation with:
- Multiple provider support (OpenAI, HuggingFace, local models)
- Caching for performance optimization
- Error handling and retry logic
- Usage tracking and monitoring
"""

import asyncio
import hashlib
import json
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
import time

import openai
import numpy as np
from sentence_transformers import SentenceTransformer
import tiktoken

from ..core.config import get_settings
from ..core.logging import get_logger
from ..core.cache import get_cache_client

logger = get_logger(__name__)
settings = get_settings()


class EmbeddingProvider(ABC):
    """Abstract base class for embedding providers"""
    
    @abstractmethod
    async def generate_embedding(
        self,
        text: str,
        model: str,
        normalize: bool = True
    ) -> Dict[str, Any]:
        """Generate embedding for a single text"""
        pass
    
    @abstractmethod
    def get_supported_models(self) -> List[str]:
        """Get list of supported models"""
        pass


class OpenAIEmbeddingProvider(EmbeddingProvider):
    """OpenAI embedding provider"""
    
    def __init__(self):
        self.client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.supported_models = [
            "text-embedding-ada-002",
            "text-embedding-3-small",
            "text-embedding-3-large"
        ]
    
    async def generate_embedding(
        self,
        text: str,
        model: str,
        normalize: bool = True
    ) -> Dict[str, Any]:
        """Generate embedding using OpenAI API"""
        
        if model not in self.supported_models:
            raise ValueError(f"Model {model} not supported by OpenAI provider")
        
        try:
            # Count tokens for usage tracking
            encoding = tiktoken.encoding_for_model(model)
            token_count = len(encoding.encode(text))
            
            # Generate embedding
            response = await self.client.embeddings.create(
                input=text,
                model=model
            )
            
            embedding = response.data[0].embedding
            
            # Normalize if requested
            if normalize:
                embedding = self._normalize_embedding(embedding)
            
            return {
                "embedding": embedding,
                "model": model,
                "usage": {
                    "prompt_tokens": token_count,
                    "total_tokens": token_count
                }
            }
            
        except Exception as e:
            logger.error(f"OpenAI embedding generation failed: {str(e)}")
            raise
    
    def get_supported_models(self) -> List[str]:
        return self.supported_models
    
    def _normalize_embedding(self, embedding: List[float]) -> List[float]:
        """Normalize embedding vector"""
        norm = np.linalg.norm(embedding)
        if norm == 0:
            return embedding
        return (np.array(embedding) / norm).tolist()


class HuggingFaceEmbeddingProvider(EmbeddingProvider):
    """HuggingFace embedding provider"""
    
    def __init__(self):
        self.supported_models = [
            "sentence-transformers/all-MiniLM-L6-v2",
            "sentence-transformers/all-mpnet-base-v2",
            "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
        ]
        self.models = {}  # Cache for loaded models
    
    async def generate_embedding(
        self,
        text: str,
        model: str,
        normalize: bool = True
    ) -> Dict[str, Any]:
        """Generate embedding using HuggingFace models"""
        
        if model not in self.supported_models:
            raise ValueError(f"Model {model} not supported by HuggingFace provider")
        
        try:
            # Load model if not cached
            if model not in self.models:
                self.models[model] = SentenceTransformer(model)
            
            model_instance = self.models[model]
            
            # Generate embedding
            embedding = model_instance.encode(
                text,
                normalize_embeddings=normalize,
                convert_to_numpy=True
            )
            
            # Estimate token count (rough approximation)
            token_count = len(text.split()) * 1.3  # Rough estimate
            
            return {
                "embedding": embedding.tolist(),
                "model": model,
                "usage": {
                    "prompt_tokens": int(token_count),
                    "total_tokens": int(token_count)
                }
            }
            
        except Exception as e:
            logger.error(f"HuggingFace embedding generation failed: {str(e)}")
            raise
    
    def get_supported_models(self) -> List[str]:
        return self.supported_models


class LocalEmbeddingProvider(EmbeddingProvider):
    """Local embedding provider for custom models"""
    
    def __init__(self):
        self.supported_models = ["local-embedding-model"]
        # Initialize your local models here
    
    async def generate_embedding(
        self,
        text: str,
        model: str,
        normalize: bool = True
    ) -> Dict[str, Any]:
        """Generate embedding using local models"""
        
        # Implement your local embedding logic here
        # This is a placeholder implementation
        
        # For demo purposes, return a random embedding
        import random
        embedding = [random.random() for _ in range(384)]
        
        if normalize:
            norm = np.linalg.norm(embedding)
            if norm > 0:
                embedding = (np.array(embedding) / norm).tolist()
        
        return {
            "embedding": embedding,
            "model": model,
            "usage": {
                "prompt_tokens": len(text.split()),
                "total_tokens": len(text.split())
            }
        }
    
    def get_supported_models(self) -> List[str]:
        return self.supported_models


class EmbeddingService:
    """Main embedding service that coordinates different providers"""
    
    def __init__(self):
        self.providers = {}
        self.cache_client = get_cache_client()
        
        # Initialize providers based on configuration
        if settings.OPENAI_API_KEY:
            self.providers["openai"] = OpenAIEmbeddingProvider()
        
        self.providers["huggingface"] = HuggingFaceEmbeddingProvider()
        self.providers["local"] = LocalEmbeddingProvider()
        
        # Model to provider mapping
        self.model_providers = {}
        for provider_name, provider in self.providers.items():
            for model in provider.get_supported_models():
                self.model_providers[model] = provider_name
    
    async def generate_embeddings(
        self,
        text: str,
        model: str = "text-embedding-ada-002",
        normalize: bool = True,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Generate embeddings for text using specified model
        """
        
        # Check cache first
        if use_cache:
            cache_key = self._get_cache_key(text, model, normalize)
            cached_result = await self._get_from_cache(cache_key)
            if cached_result:
                logger.debug(f"Cache hit for embedding generation: {model}")
                return cached_result
        
        # Find appropriate provider
        provider_name = self.model_providers.get(model)
        if not provider_name:
            raise ValueError(f"Model {model} is not supported")
        
        provider = self.providers[provider_name]
        
        # Generate embedding
        start_time = time.time()
        result = await provider.generate_embedding(text, model, normalize)
        generation_time = time.time() - start_time
        
        # Add metadata
        result["generation_time_ms"] = generation_time * 1000
        result["provider"] = provider_name
        result["cached"] = False
        
        # Cache the result
        if use_cache:
            await self._save_to_cache(cache_key, result)
        
        logger.info(
            f"Embedding generated successfully",
            model=model,
            provider=provider_name,
            generation_time_ms=result["generation_time_ms"],
            dimensions=len(result["embedding"])
        )
        
        return result
    
    def get_available_models(self) -> Dict[str, List[str]]:
        """Get all available models grouped by provider"""
        models = {}
        for provider_name, provider in self.providers.items():
            models[provider_name] = provider.get_supported_models()
        return models
    
    def _get_cache_key(self, text: str, model: str, normalize: bool) -> str:
        """Generate cache key for embedding"""
        content = f"{text}:{model}:{normalize}"
        return f"embedding:{hashlib.md5(content.encode()).hexdigest()}"
    
    async def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get embedding from cache"""
        try:
            if self.cache_client:
                cached_data = await self.cache_client.get(cache_key)
                if cached_data:
                    result = json.loads(cached_data)
                    result["cached"] = True
                    return result
        except Exception as e:
            logger.warning(f"Cache retrieval failed: {str(e)}")
        return None
    
    async def _save_to_cache(self, cache_key: str, result: Dict[str, Any]) -> None:
        """Save embedding to cache"""
        try:
            if self.cache_client:
                # Remove non-serializable fields
                cache_data = {k: v for k, v in result.items() if k != "cached"}
                await self.cache_client.setex(
                    cache_key,
                    settings.CACHE_TTL_SECONDS,
                    json.dumps(cache_data)
                )
        except Exception as e:
            logger.warning(f"Cache save failed: {str(e)}")


# Global service instance
_embedding_service = None


def get_embedding_service() -> EmbeddingService:
    """Get the global embedding service instance"""
    global _embedding_service
    if _embedding_service is None:
        _embedding_service = EmbeddingService()
    return _embedding_service
