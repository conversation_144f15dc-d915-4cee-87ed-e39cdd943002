# 🐳 AI Service Dockerfile - Production Ready FastAPI
# Multi-stage build optimized for Python ML workloads

# ================================
# 🏗️ Base Stage - Common Dependencies
# ================================
FROM python:3.11-slim AS base

# Set environment variables for Python
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    curl \
    wget \
    git \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd --create-home --shell /bin/bash --uid 1001 aiuser

# Set working directory
WORKDIR /app

# ================================
# 🧪 Dependencies Stage
# ================================
FROM base AS dependencies

# Copy requirements
COPY requirements.txt requirements-dev.txt ./

# Install Python dependencies
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements.txt

# ================================
# 🛠️ Development Stage  
# ================================
FROM dependencies AS development

# Install development dependencies
RUN pip install -r requirements-dev.txt

# Install additional development tools
RUN apt-get update && apt-get install -y \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/models /app/uploads /app/tmp && \
    chown -R aiuser:aiuser /app

# Switch to non-root user
USER aiuser

# Set development environment
ENV ENVIRONMENT=development \
    LOG_LEVEL=DEBUG \
    HOST=0.0.0.0 \
    PORT=8000

# Expose port
EXPOSE 8000

# Start development server with auto-reload
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]

# ================================
# 🚀 Production Stage
# ================================
FROM base AS production

# Install only production dependencies
COPY requirements.txt ./
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements.txt && \
    pip cache purge

# Copy application code
COPY app ./app
COPY models ./models
COPY scripts ./scripts

# Create necessary directories and set permissions
RUN mkdir -p /app/logs /app/models /app/uploads /app/tmp && \
    chown -R aiuser:aiuser /app && \
    chmod -R 755 /app

# Security hardening
RUN find /app -type f -exec chmod 644 {} \; && \
    find /app -type d -exec chmod 755 {} \; && \
    chmod +x /app/scripts/*.py 2>/dev/null || true

# Switch to non-root user
USER aiuser

# Set production environment
ENV ENVIRONMENT=production \
    LOG_LEVEL=INFO \
    HOST=0.0.0.0 \
    PORT=8000 \
    WORKERS=4

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose port
EXPOSE 8000

# Use Gunicorn for production with multiple workers
CMD ["gunicorn", "app.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000", "--timeout", "120", "--keep-alive", "5", "--max-requests", "1000", "--max-requests-jitter", "100"]

# ================================
# 🧠 GPU Stage - For GPU-enabled deployments
# ================================
FROM nvidia/cuda:12.0-runtime-ubuntu22.04 AS gpu

# Install Python and system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    gcc \
    g++ \
    curl \
    wget \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create symlink for python
RUN ln -s /usr/bin/python3 /usr/bin/python

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    CUDA_VISIBLE_DEVICES=0

# Create non-root user
RUN useradd --create-home --shell /bin/bash --uid 1001 aiuser

# Set working directory
WORKDIR /app

# Copy and install requirements
COPY requirements-gpu.txt ./
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements-gpu.txt

# Copy application
COPY app ./app
COPY models ./models
COPY scripts ./scripts

# Create directories and set permissions
RUN mkdir -p /app/logs /app/models /app/uploads /app/tmp && \
    chown -R aiuser:aiuser /app

# Switch to non-root user
USER aiuser

# Set GPU environment
ENV ENVIRONMENT=production \
    LOG_LEVEL=INFO \
    HOST=0.0.0.0 \
    PORT=8000 \
    CUDA_VISIBLE_DEVICES=0 \
    TORCH_CUDA_ARCH_LIST="7.0 7.5 8.0 8.6"

# Health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=180s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose port
EXPOSE 8000

# Start with GPU-optimized settings
CMD ["gunicorn", "app.main:app", "-w", "2", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000", "--timeout", "300", "--keep-alive", "10"]

# ================================
# 🧪 Testing Stage
# ================================
FROM dependencies AS testing

# Install testing dependencies
RUN pip install -r requirements-dev.txt

# Install additional testing tools
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy test files
COPY tests ./tests
COPY pytest.ini ./
COPY .coveragerc ./

# Copy application code
COPY app ./app

# Create test directories
RUN mkdir -p /app/test-results /app/coverage && \
    chown -R aiuser:aiuser /app

# Switch to non-root user  
USER aiuser

# Set test environment
ENV ENVIRONMENT=test \
    LOG_LEVEL=ERROR \
    PYTHONPATH=/app

# Run tests by default
CMD ["python", "-m", "pytest", "tests/", "-v", "--cov=app", "--cov-report=html:/app/coverage", "--cov-report=term", "--junitxml=/app/test-results/junit.xml"]