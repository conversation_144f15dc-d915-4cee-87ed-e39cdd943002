# 🤖 AI/ML Service Dependencies - Production Ready
# 
# Theo knowledge base về AI/ML và Python ecosystem:
# - FastAPI cho high-performance API
# - Modern ML libraries với GPU support
# - Vector databases cho semantic search
# - MLOps tools cho model management

# ================================
# 🚀 Web Framework
# ================================
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# ================================
# 🧠 Machine Learning Core
# ================================
# Deep Learning Frameworks
torch==2.1.0
torchvision==0.16.0
torchaudio==0.16.0
tensorflow==2.15.0
transformers==4.36.0
sentence-transformers==2.2.2

# Traditional ML
scikit-learn==1.3.2
xgboost==2.0.2
lightgbm==4.1.0
catboost==1.2.2

# Scientific Computing
numpy==1.24.4
pandas==2.1.4
scipy==1.11.4
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# ================================
# 🔍 Vector Databases & Search
# ================================
# Vector Databases
qdrant-client==1.7.0
pinecone-client==2.2.4
weaviate-client==3.25.3
chromadb==0.4.18

# Search & Embeddings
faiss-cpu==1.7.4  # Use faiss-gpu for GPU support
opensearch-py==2.4.0
elasticsearch==8.11.0

# ================================
# 🗣️ Natural Language Processing
# ================================
# Language Models
openai==1.3.7
anthropic==0.7.8
langchain==0.0.350
langchain-community==0.0.1
llama-index==0.9.13

# NLP Libraries
spacy==3.7.2
nltk==3.8.1
textblob==0.17.1
gensim==4.3.2

# Text Processing
beautifulsoup4==4.12.2
lxml==4.9.3
python-docx==1.1.0
PyPDF2==3.0.1

# ================================
# 🖼️ Computer Vision
# ================================
opencv-python==********
Pillow==10.1.0
imageio==2.33.1
albumentations==1.3.1

# ================================
# 📊 Data Processing & Analysis
# ================================
# Data Manipulation
polars==0.20.2  # Faster alternative to pandas
dask==2023.12.0
pyarrow==14.0.2

# Database Connectors
psycopg2-binary==2.9.9
pymongo==4.6.0
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.13.1

# ================================
# 🔧 MLOps & Model Management
# ================================
# Experiment Tracking
mlflow==2.8.1
wandb==0.16.1
tensorboard==2.15.1

# Model Serving
bentoml==1.1.10
seldon-core==1.18.0

# Model Optimization
onnx==1.15.0
onnxruntime==1.16.3
optimum==1.16.0

# ================================
# ⚡ Performance & Optimization
# ================================
# Async & Concurrency
asyncio==3.4.3
aiohttp==3.9.1
aiofiles==23.2.1
asyncpg==0.29.0

# Caching
redis==5.0.1
diskcache==5.6.3

# Parallel Processing
joblib==1.3.2
multiprocessing-logging==0.3.4

# ================================
# 🔒 Security & Authentication
# ================================
# Authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Security
cryptography==41.0.8
bcrypt==4.1.2

# ================================
# 📈 Monitoring & Observability
# ================================
# Metrics & Monitoring
prometheus-client==0.19.0
structlog==23.2.0
python-json-logger==2.0.7

# Health Checks
httpx==0.25.2
requests==2.31.0

# ================================
# 🧪 Testing & Development
# ================================
# Testing Framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2  # For testing FastAPI

# Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5

# ================================
# 🌍 Environment & Configuration
# ================================
# Environment Management
python-dotenv==1.0.0
pydantic-settings==2.1.0

# Configuration
click==8.1.7
typer==0.9.0
rich==13.7.0

# ================================
# 📦 Utilities
# ================================
# Date & Time
python-dateutil==2.8.2
pytz==2023.3

# File Processing
openpyxl==3.1.2
xlsxwriter==3.1.9

# Validation
cerberus==1.3.5
marshmallow==3.20.1

# ================================
# 🐳 Production Dependencies
# ================================
# WSGI/ASGI Servers
gunicorn==21.2.0
uvicorn[standard]==0.24.0

# Process Management
supervisor==4.2.5

# ================================
# 📋 Version Constraints
# ================================
# Python version requirement
# python_requires = ">=3.9,<3.12"

# ================================
# 🎯 Installation Commands:
# ================================

# Basic installation:
# pip install -r requirements.txt

# Development installation:
# pip install -r requirements.txt -r requirements-dev.txt

# GPU support (replace faiss-cpu with faiss-gpu):
# pip install faiss-gpu

# Production installation:
# pip install --no-dev -r requirements.txt

# ================================
# 🔧 Optional GPU Dependencies:
# ================================

# For CUDA support (uncomment if using GPU):
# torch==2.1.0+cu118 -f https://download.pytorch.org/whl/torch_stable.html
# torchvision==0.16.0+cu118 -f https://download.pytorch.org/whl/torch_stable.html
# torchaudio==0.16.0+cu118 -f https://download.pytorch.org/whl/torch_stable.html
# tensorflow-gpu==2.15.0
# faiss-gpu==1.7.4

# ================================
# 📊 Package Categories Summary:
# ================================

# Core ML: 15 packages
# Data Processing: 12 packages  
# Vector/Search: 8 packages
# NLP: 10 packages
# Computer Vision: 5 packages
# MLOps: 8 packages
# Performance: 6 packages
# Security: 5 packages
# Monitoring: 4 packages
# Testing: 6 packages
# Utilities: 8 packages

# Total: ~87 carefully selected packages for enterprise AI/ML service
