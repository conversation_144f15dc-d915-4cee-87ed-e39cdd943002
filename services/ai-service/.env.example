# 🤖 AI Service Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# 🌍 ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=development
LOG_LEVEL=info
DEBUG=true
PYTHONPATH=/app

# =============================================================================
# 🌐 SERVER CONFIGURATION
# =============================================================================
HOST=0.0.0.0
PORT=8000
API_PREFIX=/api/v1
API_VERSION=1.0.0
API_TITLE=Enterprise AI Service
API_DESCRIPTION=Advanced AI/ML service for enterprise platform
DOCS_URL=/docs
REDOC_URL=/redoc

# =============================================================================
# 🤖 AI/ML MODEL CONFIGURATION
# =============================================================================
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2048
OPENAI_TEMPERATURE=0.7
OPENAI_TOP_P=1.0
OPENAI_FREQUENCY_PENALTY=0.0
OPENAI_PRESENCE_PENALTY=0.0
OPENAI_TIMEOUT=30

# Hugging Face Configuration
HUGGINGFACE_API_KEY=your-huggingface-api-key-here
HUGGINGFACE_MODEL=microsoft/DialoGPT-medium
HUGGINGFACE_CACHE_DIR=./models/cache
HUGGINGFACE_TIMEOUT=30

# Local Model Configuration
LOCAL_MODEL_PATH=./models/local
MODEL_CACHE_SIZE=5
MODEL_WARM_UP=true
MODEL_PRELOAD=true

# =============================================================================
# 🔄 PROCESSING CONFIGURATION
# =============================================================================
# Request Processing
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
QUEUE_SIZE=100
BATCH_SIZE=32
BATCH_TIMEOUT=5

# Memory Management
MAX_MEMORY_USAGE=2048
MEMORY_CHECK_INTERVAL=60
GARBAGE_COLLECTION_THRESHOLD=0.8

# GPU Configuration
USE_GPU=false
GPU_MEMORY_FRACTION=0.8
GPU_ALLOW_GROWTH=true

# =============================================================================
# 🗄️ DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL (for storing AI results, user preferences, etc.)
DATABASE_URL=postgresql://ai_user:ai_password@localhost:5432/ai_service_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=ai_service_db
DATABASE_USER=ai_user
DATABASE_PASSWORD=ai_password
DATABASE_SSL=false
DATABASE_ECHO=false

# Connection Pool
DATABASE_POOL_SIZE=5
DATABASE_POOL_MAX_OVERFLOW=10
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# =============================================================================
# 🔴 REDIS CONFIGURATION
# =============================================================================
# Redis for caching AI responses and session management
REDIS_URL=redis://localhost:6379/1
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1
REDIS_TTL=3600
REDIS_KEY_PREFIX=ai-service:

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=1800
CACHE_MAX_SIZE=1000
RESPONSE_CACHE_ENABLED=true
RESPONSE_CACHE_TTL=3600

# =============================================================================
# 🔐 SECURITY CONFIGURATION
# =============================================================================
# API Security
API_KEY_HEADER=x-api-key
API_KEY=your-ai-service-api-key-change-this-in-production
BEARER_TOKEN_VALIDATION=true
JWT_SECRET=your-jwt-secret-for-ai-service

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600
RATE_LIMIT_PER_USER=50

# Input Validation
MAX_INPUT_LENGTH=10000
MIN_INPUT_LENGTH=1
ALLOWED_CONTENT_TYPES=text/plain,application/json
SANITIZE_INPUT=true

# =============================================================================
# 📊 MONITORING & OBSERVABILITY
# =============================================================================
# Metrics
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9091
PROMETHEUS_ENDPOINT=/metrics
METRICS_PREFIX=ai_service_

# Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_ENDPOINT=/health
HEALTH_CHECK_TIMEOUT=5
HEALTH_CHECK_INTERVAL=30

# Logging
LOG_FORMAT=json
LOG_TIMESTAMP=true
LOG_CORRELATION_ID=true
LOG_FILE_ENABLED=false
LOG_FILE_PATH=./logs/ai-service.log
LOG_FILE_MAX_SIZE=10MB
LOG_FILE_BACKUP_COUNT=5

# Request Tracing
TRACING_ENABLED=true
JAEGER_AGENT_HOST=localhost
JAEGER_AGENT_PORT=6831
TRACE_SAMPLE_RATE=0.1

# =============================================================================
# 🚀 PERFORMANCE OPTIMIZATION
# =============================================================================
# Async Configuration
ASYNC_WORKERS=4
ASYNC_TIMEOUT=30
ASYNC_KEEP_ALIVE=2

# Connection Pooling
HTTP_POOL_CONNECTIONS=10
HTTP_POOL_MAXSIZE=10
HTTP_POOL_BLOCK=false

# Caching Strategy
CACHE_STRATEGY=lru
CACHE_COMPRESSION=true
CACHE_SERIALIZATION=pickle

# =============================================================================
# 🧪 DEVELOPMENT & TESTING
# =============================================================================
# Development Settings
AUTO_RELOAD=true
DEBUG_MODE=true
PROFILING_ENABLED=false

# Testing Configuration
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_ai_db
TEST_REDIS_URL=redis://localhost:6380/2
TEST_API_KEY=test-api-key
MOCK_EXTERNAL_APIS=true

# Model Testing
TEST_MODEL_PATH=./tests/fixtures/models
BENCHMARK_ENABLED=false
PERFORMANCE_TESTS=false

# =============================================================================
# 📁 FILE & STORAGE CONFIGURATION
# =============================================================================
# File Upload
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=txt,json,csv,pdf
UPLOAD_PATH=./uploads
TEMP_PATH=./temp

# Model Storage
MODEL_STORAGE_PATH=./models
MODEL_DOWNLOAD_TIMEOUT=300
MODEL_VERIFICATION=true

# =============================================================================
# 🌐 EXTERNAL SERVICES
# =============================================================================
# API Gateway Communication
API_GATEWAY_URL=http://localhost:3000
API_GATEWAY_TIMEOUT=10
API_GATEWAY_RETRIES=3

# Third-party APIs
EXTERNAL_API_TIMEOUT=30
EXTERNAL_API_RETRIES=3
EXTERNAL_API_BACKOFF=exponential

# =============================================================================
# 🔧 ALGORITHM PROCESSING
# =============================================================================
# Algorithm Analysis
ALGORITHM_TIMEOUT=60
MAX_ALGORITHM_COMPLEXITY=1000000
ALGORITHM_CACHE_ENABLED=true
ALGORITHM_CACHE_TTL=7200

# Code Analysis
CODE_ANALYSIS_ENABLED=true
SYNTAX_VALIDATION=true
PERFORMANCE_ANALYSIS=true
SECURITY_SCAN=true

# Pattern Recognition
PATTERN_DETECTION=true
PATTERN_CONFIDENCE_THRESHOLD=0.7
MAX_PATTERNS_PER_ANALYSIS=10

# =============================================================================
# 🚀 PRODUCTION SETTINGS
# =============================================================================
# SSL Configuration
SSL_ENABLED=false
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# Production Optimization
PRELOAD_MODELS=true
OPTIMIZE_MEMORY=true
ENABLE_COMPRESSION=true
COMPRESSION_LEVEL=6

# Error Handling
DETAILED_ERRORS=false
ERROR_TRACKING=true
SENTRY_DSN=your-sentry-dsn-here

# Graceful Shutdown
GRACEFUL_SHUTDOWN_TIMEOUT=30
CLEANUP_ON_EXIT=true
