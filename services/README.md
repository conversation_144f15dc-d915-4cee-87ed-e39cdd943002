# ⚡ **Microservices Layer - Domain-Driven Architecture**

> **Enterprise-grade microservices with Clean Architecture, Domain-Driven Design, and Event-Driven Communication**

## 📋 **Overview**

The `services/` directory contains all **domain-specific microservices** that implement the core business logic of our enterprise platform. Each service follows Clean Architecture principles, Domain-Driven Design patterns, and communicates through event-driven mechanisms.

## 🏗️ **Architecture Overview**

```
services/
├── user-service/           # 👥 User Management & Authentication
├── ai-service/            # 🤖 AI/ML Processing & Vector Operations
├── analytics-service/     # 📊 Data Analytics & Business Intelligence
├── notification-service/  # 📨 Real-time Notifications & Messaging
├── file-service/         # 📁 File Management & Storage
└── performance-service/   # ⚡ High-Performance Operations
```

## 📁 **Services Directory & Guides**

| Service | Technology Stack | Domain | README Guide | Status |
|---------|------------------|--------|--------------|--------|
| [👥 **user-service/**](user-service/README.md) | NestJS + TypeScript + PostgreSQL | User management, auth, profiles | [📖 Guide](user-service/README.md) | 🔄 |
| [🤖 **ai-service/**](ai-service/README.md) | FastAPI + Python + Vector DB | AI/ML, embeddings, LLM integration | [📖 Guide](ai-service/README.md) | 🔄 |
| [📊 **analytics-service/**](analytics-service/README.md) | FastAPI + Python + ClickHouse | Data analytics, reporting, BI | [📖 Guide](analytics-service/README.md) | 🔄 |
| [📨 **notification-service/**](notification-service/README.md) | Go + Gin + WebSocket | Real-time notifications, messaging | [📖 Guide](notification-service/README.md) | 🔄 |
| [📁 **file-service/**](file-service/README.md) | Go + Gin + S3 | File upload, storage, CDN | [📖 Guide](file-service/README.md) | 🔄 |
| [⚡ **performance-service/**](performance-service/README.md) | Go + Gin + Redis | High-performance operations | [📖 Guide](performance-service/README.md) | 🔄 |

## 🏛️ **Clean Architecture Implementation**

Each service follows **Clean Architecture** with clear separation of concerns:

```
service-template/
├── src/
│   ├── domain/              # 🎯 Domain Layer (Core Business Logic)
│   │   ├── entities/        # Business entities with behavior
│   │   ├── value-objects/   # Immutable value objects
│   │   ├── aggregates/      # Aggregate roots with consistency
│   │   ├── events/          # Domain events for loose coupling
│   │   └── repositories/    # Repository interfaces (contracts)
│   │
│   ├── application/         # 🔧 Application Layer (Use Cases)
│   │   ├── use-cases/       # Application use cases
│   │   ├── commands/        # Command objects (write operations)
│   │   ├── queries/         # Query objects (read operations)
│   │   ├── dtos/            # Data transfer objects
│   │   └── interfaces/      # Application service interfaces
│   │
│   ├── infrastructure/      # 🏗️ Infrastructure Layer (External)
│   │   ├── repositories/    # Repository implementations
│   │   ├── external/        # Third-party service integrations
│   │   ├── database/        # Database access layer
│   │   ├── messaging/       # Message broker implementations
│   │   └── config/          # Configuration management
│   │
│   └── interface/           # 🌐 Interface Layer (API/Controllers)
│       ├── controllers/     # API controllers
│       ├── middleware/      # Request/response middleware
│       ├── validators/      # Input validation
│       ├── serializers/     # Response serialization
│       └── routes/          # Route definitions
```

## 🔄 **Event-Driven Communication**

Services communicate through **asynchronous events** using Apache Kafka:

### **📨 Event Types**
```typescript
// Domain Events
interface UserRegisteredEvent {
  eventType: 'user.registered';
  aggregateId: string;
  userId: string;
  email: string;
  timestamp: Date;
}

interface TaskCompletedEvent {
  eventType: 'task.completed';
  aggregateId: string;
  taskId: string;
  userId: string;
  completedAt: Date;
}

// Integration Events
interface NotificationRequestedEvent {
  eventType: 'notification.requested';
  userId: string;
  type: 'email' | 'push' | 'sms';
  content: string;
  metadata: Record<string, any>;
}
```

### **🔄 Event Flow Example**
```
1. User Service → UserRegisteredEvent → Kafka
2. Notification Service ← Kafka ← Consumes event
3. Notification Service → Sends welcome email
4. Analytics Service ← Kafka ← Tracks user registration
```

## 🛠️ **Technology Stack by Service**

### **👥 User Service (TypeScript/NestJS)**
- **Framework**: NestJS + TypeScript
- **Database**: PostgreSQL + Redis
- **ORM**: TypeORM with migrations
- **Authentication**: JWT + bcrypt
- **Validation**: class-validator + class-transformer
- **Testing**: Jest + Supertest

### **🤖 AI Service (Python/FastAPI)**
- **Framework**: FastAPI + Python 3.11+
- **ML Libraries**: scikit-learn, transformers, langchain
- **Vector Database**: Qdrant + pgvector
- **LLM Integration**: OpenAI, Anthropic, Hugging Face
- **Data Processing**: pandas, numpy, scipy
- **Testing**: pytest + httpx

### **📊 Analytics Service (Python/FastAPI)**
- **Framework**: FastAPI + Python 3.11+
- **Database**: ClickHouse + PostgreSQL
- **Data Processing**: pandas, polars, dask
- **Visualization**: plotly, matplotlib
- **ETL**: Apache Airflow
- **Testing**: pytest + factory_boy

### **📨 Notification Service (Go/Gin)**
- **Framework**: Gin + Go 1.21+
- **Real-time**: WebSocket + Server-Sent Events
- **Message Queue**: Redis Streams + Kafka
- **Push Notifications**: Firebase, APNs
- **Email**: SendGrid, AWS SES
- **Testing**: testify + httptest

### **📁 File Service (Go/Gin)**
- **Framework**: Gin + Go 1.21+
- **Storage**: AWS S3, Google Cloud Storage
- **CDN**: CloudFront, CloudFlare
- **Image Processing**: ImageMagick, Sharp
- **Virus Scanning**: ClamAV
- **Testing**: testify + minio (S3 mock)

### **⚡ Performance Service (Go/Gin)**
- **Framework**: Gin + Go 1.21+
- **Cache**: Redis Cluster
- **Database**: PostgreSQL + Read Replicas
- **Monitoring**: Prometheus + Grafana
- **Profiling**: pprof + trace
- **Testing**: testify + benchmarks

## 🔐 **Security Implementation**

### **🛡️ Service-to-Service Authentication**
```typescript
// JWT Service Token
interface ServiceToken {
  iss: string;      // Issuer (service name)
  aud: string;      // Audience (target service)
  sub: string;      // Subject (service account)
  exp: number;      // Expiration
  iat: number;      // Issued at
  scope: string[];  // Permissions
}

// mTLS Configuration
const tlsConfig = {
  cert: '/etc/ssl/certs/service.crt',
  key: '/etc/ssl/private/service.key',
  ca: '/etc/ssl/certs/ca.crt',
  verifyPeer: true,
};
```

### **🔒 Data Protection**
- ✅ **Encryption at Rest** - Database-level encryption
- ✅ **Encryption in Transit** - TLS 1.3 for all communications
- ✅ **PII Protection** - Field-level encryption for sensitive data
- ✅ **Audit Logging** - Comprehensive audit trails
- ✅ **Input Validation** - Strict input sanitization

## 📊 **Monitoring & Observability**

### **📈 Metrics Collection**
```typescript
// Prometheus Metrics
const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
});

const businessMetrics = new Counter({
  name: 'business_events_total',
  help: 'Total number of business events',
  labelNames: ['event_type', 'service'],
});
```

### **📝 Structured Logging**
```typescript
// Winston Logger Configuration
const logger = winston.createLogger({
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'user-service',
    version: process.env.APP_VERSION,
  },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
});
```

### **🔍 Distributed Tracing**
```typescript
// OpenTelemetry Configuration
import { NodeSDK } from '@opentelemetry/sdk-node';
import { jaegerExporter } from '@opentelemetry/exporter-jaeger';

const sdk = new NodeSDK({
  traceExporter: new jaegerExporter({
    endpoint: 'http://jaeger:14268/api/traces',
  }),
  instrumentations: [
    getNodeAutoInstrumentations(),
  ],
});

sdk.start();
```

## 🧪 **Testing Strategy**

### **📊 Testing Pyramid**
```
E2E Tests (10%)     ← Service integration tests
Integration (20%)   ← Database + external services
Unit Tests (70%)    ← Domain logic + use cases
```

### **🔧 Testing Tools by Language**
- **TypeScript/NestJS**: Jest + Supertest + TestContainers
- **Python/FastAPI**: pytest + httpx + factory_boy
- **Go/Gin**: testify + httptest + dockertest

### **📋 Test Categories**
```bash
# Unit Tests - Domain logic
npm run test:unit

# Integration Tests - Database + external services
npm run test:integration

# E2E Tests - Full service workflows
npm run test:e2e

# Contract Tests - API contracts
npm run test:contract

# Performance Tests - Load testing
npm run test:performance
```

## 🚀 **Development Workflow**

### **🔄 Local Development**
```bash
# Start all services with Docker Compose
docker-compose up -d

# Start individual service
cd services/user-service
npm run dev

# Run tests
npm run test

# Check service health
curl http://localhost:3001/health
```

### **📦 Service Template**
```bash
# Generate new service from template
./tools/generators/create-service.sh my-new-service

# Available templates:
# - nestjs-service (TypeScript)
# - fastapi-service (Python)
# - gin-service (Go)
```

## 🔄 **CI/CD Pipeline**

### **🚀 Deployment Pipeline**
```yaml
# .github/workflows/service-deploy.yml
name: Service Deployment
on:
  push:
    paths: ['services/**']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Tests
        run: |
          cd services/${{ matrix.service }}
          npm test
    strategy:
      matrix:
        service: [user-service, ai-service, analytics-service]

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Kubernetes
        run: kubectl apply -f k8s/
```

### **📊 Quality Gates**
- ✅ **Test Coverage** - Minimum 80% coverage
- ✅ **Code Quality** - SonarQube analysis
- ✅ **Security Scan** - Snyk vulnerability check
- ✅ **Performance** - Load testing benchmarks
- ✅ **Documentation** - API documentation generation

## 🔗 **Related Documentation**

- [🏗️ Project Structure](../docs/PROJECT_STRUCTURE.md)
- [🎯 Applications Layer](../apps/README.md)
- [📚 Shared Libraries](../libs/README.md)
- [🏗️ Infrastructure](../infrastructure/README.md)
- [🧪 Testing Guide](../tests/README.md)
- [🧠 Knowledge Base](../docs/07-knowledge-base/README.md)

## 🤝 **Contributing**

1. **Choose Service** - Select the service to work on
2. **Follow Architecture** - Implement Clean Architecture patterns
3. **Write Tests** - Maintain high test coverage
4. **Document APIs** - Update OpenAPI specifications
5. **Submit PR** - Include tests and documentation

---

> **Next Steps**: Explore individual service READMEs for detailed implementation guides and start building your microservices architecture.
