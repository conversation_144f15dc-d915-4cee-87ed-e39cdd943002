/**
 * 🔗 Shared Types Library - TypeScript Interfaces and Types
 * 
 * Provides common types and interfaces shared across services:
 * - API request/response types
 * - Domain entity interfaces
 * - Common utility types
 * - Error handling types
 */

// ================================
// 🌐 Common API Types
// ================================

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
  path: string;
  method: string;
  statusCode: number;
  correlationId?: string;
  meta?: PaginationMeta;
}

export interface ApiError {
  success: false;
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
  method: string;
  correlationId?: string;
  details?: any;
}

export interface PaginationMeta {
  total?: number;
  page?: number;
  limit?: number;
  hasNext?: boolean;
  hasPrev?: boolean;
  totalPages?: number;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

// ================================
// 🔐 Authentication Types
// ================================

export interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  roles: Role[];
  permissions: Permission[];
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  isActive: boolean;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

// ================================
// 🤖 AI Service Types
// ================================

export interface EmbeddingRequest {
  text: string | string[];
  model?: string;
  normalize?: boolean;
}

export interface EmbeddingResponse {
  embeddings: number[][];
  model: string;
  dimensions: number;
  usage: {
    totalTokens: number;
    promptTokens: number;
  };
}

export interface ChatCompletionRequest {
  messages: ChatMessage[];
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stream?: boolean;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  name?: string;
}

export interface ChatCompletionResponse {
  id: string;
  object: 'chat.completion';
  created: number;
  model: string;
  choices: ChatChoice[];
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface ChatChoice {
  index: number;
  message: ChatMessage;
  finishReason: 'stop' | 'length' | 'content_filter' | null;
}

export interface SimilaritySearchRequest {
  query: string;
  topK?: number;
  threshold?: number;
  filters?: Record<string, any>;
}

export interface SimilaritySearchResponse {
  results: SimilarityResult[];
  query: string;
  totalResults: number;
  searchTime: number;
}

export interface SimilarityResult {
  id: string;
  content: string;
  score: number;
  metadata?: Record<string, any>;
}

// ================================
// 🧮 Algorithm Types
// ================================

export interface AlgorithmAnalysisRequest {
  code: string;
  language: 'javascript' | 'typescript' | 'python' | 'java' | 'cpp';
  analysisType: 'complexity' | 'patterns' | 'optimization' | 'all';
}

export interface AlgorithmAnalysisResponse {
  timeComplexity: ComplexityAnalysis;
  spaceComplexity: ComplexityAnalysis;
  patterns: PatternDetection[];
  optimizations: OptimizationSuggestion[];
  codeQuality: CodeQualityMetrics;
}

export interface ComplexityAnalysis {
  bestCase: string;
  averageCase: string;
  worstCase: string;
  explanation: string;
  confidence: number;
}

export interface PatternDetection {
  pattern: string;
  description: string;
  confidence: number;
  location: {
    startLine: number;
    endLine: number;
  };
}

export interface OptimizationSuggestion {
  type: 'performance' | 'memory' | 'readability' | 'maintainability';
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  example?: string;
}

export interface CodeQualityMetrics {
  readability: number;
  maintainability: number;
  testability: number;
  performance: number;
  overall: number;
}

// ================================
// 📊 Health Check Types
// ================================

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
}

export interface DetailedHealthStatus extends HealthStatus {
  system: SystemHealth;
  services: Record<string, ServiceHealth>;
  dependencies: Record<string, DependencyHealth>;
}

export interface SystemHealth {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
}

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime?: number;
  lastCheck: string;
  error?: string;
}

export interface DependencyHealth {
  status: 'available' | 'unavailable';
  responseTime?: number;
  version?: string;
  error?: string;
}

// ================================
// 🔧 Configuration Types
// ================================

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl: boolean;
  poolSize: number;
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  ttl: number;
}

export interface SecurityConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  bcryptRounds: number;
  rateLimitWindow: number;
  rateLimitMax: number;
}

// ================================
// 🛠️ Utility Types
// ================================

export type Environment = 'development' | 'staging' | 'production' | 'test';

export type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'verbose';

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD';

export type SortOrder = 'asc' | 'desc';

export interface TimeRange {
  start: Date;
  end: Date;
}

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface FileUpload {
  filename: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

// ================================
// 🎯 Result Types
// ================================

export type Result<T, E = Error> = Success<T> | Failure<E>;

export interface Success<T> {
  success: true;
  data: T;
}

export interface Failure<E> {
  success: false;
  error: E;
}

// Helper functions for Result type
export const success = <T>(data: T): Success<T> => ({ success: true, data });
export const failure = <E>(error: E): Failure<E> => ({ success: false, error });

// ================================
// 📝 Event Types
// ================================

export interface DomainEvent {
  id: string;
  type: string;
  aggregateId: string;
  aggregateType: string;
  version: number;
  data: any;
  metadata: {
    timestamp: Date;
    userId?: string;
    correlationId?: string;
  };
}

export interface EventHandler<T extends DomainEvent = DomainEvent> {
  handle(event: T): Promise<void>;
}

// ================================
// 🔍 Search Types
// ================================

export interface SearchQuery {
  query: string;
  filters?: Record<string, any>;
  facets?: string[];
  pagination?: PaginationQuery;
  highlight?: boolean;
}

export interface SearchResult<T = any> {
  items: T[];
  total: number;
  facets?: Record<string, FacetResult[]>;
  suggestions?: string[];
  searchTime: number;
}

export interface FacetResult {
  value: string;
  count: number;
}

// ================================
// 📈 Metrics Types
// ================================

export interface MetricPoint {
  timestamp: Date;
  value: number;
  labels?: Record<string, string>;
}

export interface MetricSeries {
  name: string;
  points: MetricPoint[];
  unit?: string;
  description?: string;
}
