/**
 * 📝 Shared Types - Common TypeScript Interfaces and Types
 * 
 * Provides type definitions for:
 * - API request/response models
 * - Authentication and authorization
 * - AI service interfaces
 * - Database entities
 * - Common utilities
 */

// ================================
// 🔐 Authentication Types
// ================================

export interface JWTPayload {
  userId: string;
  email: string;
  roles: string[];
  permissions: string[];
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface AuthUser {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  isVerified: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
}

// ================================
// 🏥 Health Check Types
// ================================

export interface HealthStatus {
  status: 'ok' | 'error' | 'degraded';
  timestamp: string;
  uptime: number;
  environment?: string;
  version?: string;
  error?: string;
}

export interface DetailedHealthStatus extends HealthStatus {
  services: Record<string, ServiceHealth>;
  system: SystemHealth;
}

export interface ServiceHealth {
  status: 'ok' | 'error' | 'degraded';
  responseTime?: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface SystemHealth {
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
}

// ================================
// 🤖 AI Service Types
// ================================

export interface EmbeddingRequest {
  text: string;
  model?: string;
  normalize?: boolean;
  userId?: string;
}

export interface EmbeddingResponse {
  embeddings: number[][];
  model: string;
  dimensions: number;
  usage: TokenUsage;
  processingTimeMs?: number;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  name?: string;
}

export interface ChatCompletionRequest {
  messages: ChatMessage[];
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stream?: boolean;
  userId?: string;
}

export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: ChatChoice[];
  usage: TokenUsage;
}

export interface ChatChoice {
  index: number;
  message: ChatMessage;
  finishReason: 'stop' | 'length' | 'content_filter' | null;
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens?: number;
  totalTokens: number;
}

export interface SimilaritySearchRequest {
  query: string;
  topK?: number;
  threshold?: number;
  filters?: Record<string, any>;
  userId?: string;
}

export interface SimilaritySearchResponse {
  results: SearchResult[];
  query: string;
  totalResults: number;
  searchTime: number;
}

export interface SearchResult {
  id: string;
  content: string;
  score: number;
  metadata?: Record<string, any>;
}

export interface AlgorithmAnalysisRequest {
  code: string;
  language: 'javascript' | 'typescript' | 'python' | 'java' | 'cpp';
  analysisType: 'complexity' | 'patterns' | 'optimization' | 'all';
  userId?: string;
}

export interface AlgorithmAnalysisResponse {
  timeComplexity: ComplexityAnalysis;
  spaceComplexity: ComplexityAnalysis;
  patterns: CodePattern[];
  optimizations: OptimizationSuggestion[];
  codeQuality: CodeQualityMetrics;
}

export interface ComplexityAnalysis {
  bestCase: string;
  averageCase: string;
  worstCase: string;
  explanation: string;
  confidence: number;
}

export interface CodePattern {
  pattern: string;
  description: string;
  confidence: number;
  location: {
    startLine: number;
    endLine: number;
  };
}

export interface OptimizationSuggestion {
  type: 'performance' | 'memory' | 'readability' | 'maintainability';
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  example?: string;
}

export interface CodeQualityMetrics {
  readability: number;
  maintainability: number;
  testability: number;
  performance: number;
  overall: number;
}

// ================================
// 📊 API Request Types
// ================================

export interface APIRequestLog {
  id: string;
  correlationId: string;
  userId?: string;
  endpoint: string;
  method: string;
  statusCode: number;
  success: boolean;
  responseTime: number;
  requestSize?: number;
  responseSize?: number;
  ipAddress?: string;
  userAgent?: string;
  error?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export interface APIMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  errorRate: number;
  requestsPerSecond: number;
}

// ================================
// 🗄️ Database Types
// ================================

export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export interface User extends BaseEntity {
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  phoneNumber?: string;
  timezone?: string;
  locale: string;
  isActive: boolean;
  isVerified: boolean;
  emailVerifiedAt?: Date;
  lastLoginAt?: Date;
  lastLoginIp?: string;
  failedLoginAttempts: number;
  lockedUntil?: Date;
  passwordChangedAt?: Date;
  twoFactorEnabled: boolean;
  preferences?: UserPreferences;
  metadata?: Record<string, any>;
}

export interface UserPreferences {
  theme?: 'light' | 'dark';
  notifications?: {
    email?: boolean;
    push?: boolean;
    sms?: boolean;
  };
  privacy?: {
    profileVisible?: boolean;
    activityVisible?: boolean;
  };
}

export interface Role extends BaseEntity {
  name: string;
  displayName: string;
  description?: string;
  isActive: boolean;
  level: number;
  category?: string;
  metadata?: RoleMetadata;
}

export interface RoleMetadata {
  color?: string;
  icon?: string;
  features?: string[];
  restrictions?: string[];
}

export interface Permission extends BaseEntity {
  name: string;
  displayName: string;
  resource: string;
  action: string;
  description?: string;
  category?: string;
  isActive: boolean;
  priority: number;
  conditions?: PermissionConditions;
  metadata?: PermissionMetadata;
}

export interface PermissionConditions {
  timeRestrictions?: {
    startTime?: string;
    endTime?: string;
    daysOfWeek?: number[];
  };
  ipRestrictions?: string[];
  locationRestrictions?: string[];
  resourceFilters?: Record<string, any>;
}

export interface PermissionMetadata {
  icon?: string;
  color?: string;
  group?: string;
  dependencies?: string[];
  conflicts?: string[];
}

// ================================
// 🔧 Utility Types
// ================================

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ErrorResponse {
  success: false;
  statusCode: number;
  message: string;
  error: string;
  timestamp: string;
  path: string;
  correlationId?: string;
  details?: any;
}

export interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
  timestamp: string;
  correlationId?: string;
}

// ================================
// 🎯 Event Types
// ================================

export interface DomainEvent {
  id: string;
  type: string;
  aggregateId: string;
  aggregateType: string;
  version: number;
  data: Record<string, any>;
  metadata: {
    userId?: string;
    correlationId?: string;
    causationId?: string;
    timestamp: Date;
  };
}

export interface UserRegisteredEvent extends DomainEvent {
  type: 'UserRegistered';
  data: {
    userId: string;
    email: string;
    username: string;
  };
}

export interface UserLoginEvent extends DomainEvent {
  type: 'UserLogin';
  data: {
    userId: string;
    ipAddress: string;
    userAgent: string;
    success: boolean;
  };
}

// ================================
// 📈 Monitoring Types
// ================================

export interface MetricPoint {
  timestamp: Date;
  value: number;
  labels?: Record<string, string>;
}

export interface PerformanceMetrics {
  responseTime: MetricPoint[];
  throughput: MetricPoint[];
  errorRate: MetricPoint[];
  cpuUsage: MetricPoint[];
  memoryUsage: MetricPoint[];
}

// Type guards and utilities
export const isErrorResponse = (response: any): response is ErrorResponse => {
  return response && response.success === false;
};

export const isSuccessResponse = <T>(response: any): response is SuccessResponse<T> => {
  return response && response.success === true;
};

// Export all types
export * from './auth.types';
export * from './api.types';
export * from './database.types';
