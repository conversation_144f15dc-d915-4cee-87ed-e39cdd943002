/**
 * 🎯 Result Pattern Implementation - Functional Error Handling
 * 
 * Theo knowledge base về Error Handling và Functional Programming:
 * - Explicit error handling thay vì exceptions
 * - Type safety cho success/failure cases
 * - Composable operations
 * - Railway-oriented programming
 */

export class Result<T> {
  private constructor(
    private readonly _isSuccess: boolean,
    private readonly _error?: string,
    private readonly _value?: T
  ) {}

  /**
   * Create successful result
   * Theo Factory pattern
   */
  public static ok<U>(value?: U): Result<U> {
    return new Result<U>(true, undefined, value);
  }

  /**
   * Create failed result
   * Theo Factory pattern
   */
  public static fail<U>(error: string): Result<U> {
    return new Result<U>(false, error);
  }

  /**
   * Combine multiple results - All must succeed
   * Theo Functional Programming: Applicative pattern
   */
  public static combine<T>(results: Result<T>[]): Result<T[]> {
    const failures = results.filter(r => r.isFailure);
    
    if (failures.length > 0) {
      const errors = failures.map(f => f.error).join(', ');
      return Result.fail<T[]>(errors);
    }

    const values = results.map(r => r.getValue());
    return Result.ok<T[]>(values);
  }

  /**
   * Try-catch wrapper cho async operations
   * Theo Error Handling best practices
   */
  public static async try<T>(
    operation: () => Promise<T>
  ): Promise<Result<T>> {
    try {
      const value = await operation();
      return Result.ok(value);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return Result.fail<T>(message);
    }
  }

  /**
   * Check if result is successful
   */
  public get isSuccess(): boolean {
    return this._isSuccess;
  }

  /**
   * Check if result is failure
   */
  public get isFailure(): boolean {
    return !this._isSuccess;
  }

  /**
   * Get error message (only for failures)
   */
  public get error(): string {
    if (this._isSuccess) {
      throw new Error('Cannot get error from successful result');
    }
    return this._error!;
  }

  /**
   * Get value (only for successes)
   */
  public getValue(): T {
    if (!this._isSuccess) {
      throw new Error('Cannot get value from failed result');
    }
    return this._value!;
  }

  /**
   * Get value or default
   * Theo Null Object pattern
   */
  public getValueOrDefault(defaultValue: T): T {
    return this._isSuccess ? this._value! : defaultValue;
  }

  /**
   * Map operation - Transform success value
   * Theo Functor pattern
   */
  public map<U>(fn: (value: T) => U): Result<U> {
    if (this.isFailure) {
      return Result.fail<U>(this._error!);
    }
    
    try {
      const newValue = fn(this._value!);
      return Result.ok(newValue);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return Result.fail<U>(message);
    }
  }

  /**
   * FlatMap operation - Chain operations that return Results
   * Theo Monad pattern (Railway-oriented programming)
   */
  public flatMap<U>(fn: (value: T) => Result<U>): Result<U> {
    if (this.isFailure) {
      return Result.fail<U>(this._error!);
    }
    
    try {
      return fn(this._value!);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return Result.fail<U>(message);
    }
  }

  /**
   * Async map operation
   */
  public async mapAsync<U>(fn: (value: T) => Promise<U>): Promise<Result<U>> {
    if (this.isFailure) {
      return Result.fail<U>(this._error!);
    }
    
    try {
      const newValue = await fn(this._value!);
      return Result.ok(newValue);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return Result.fail<U>(message);
    }
  }

  /**
   * Async flatMap operation
   */
  public async flatMapAsync<U>(
    fn: (value: T) => Promise<Result<U>>
  ): Promise<Result<U>> {
    if (this.isFailure) {
      return Result.fail<U>(this._error!);
    }
    
    try {
      return await fn(this._value!);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return Result.fail<U>(message);
    }
  }

  /**
   * Execute side effect on success
   * Theo Command pattern
   */
  public onSuccess(fn: (value: T) => void): Result<T> {
    if (this.isSuccess) {
      fn(this._value!);
    }
    return this;
  }

  /**
   * Execute side effect on failure
   * Theo Command pattern
   */
  public onFailure(fn: (error: string) => void): Result<T> {
    if (this.isFailure) {
      fn(this._error!);
    }
    return this;
  }

  /**
   * Match pattern - Handle both success and failure
   * Theo Pattern Matching
   */
  public match<U>(
    onSuccess: (value: T) => U,
    onFailure: (error: string) => U
  ): U {
    return this.isSuccess 
      ? onSuccess(this._value!) 
      : onFailure(this._error!);
  }

  /**
   * Convert to JSON for serialization
   */
  public toJSON(): object {
    return {
      isSuccess: this._isSuccess,
      error: this._error,
      value: this._value
    };
  }

  /**
   * Create Result from JSON
   */
  public static fromJSON<T>(json: any): Result<T> {
    if (json.isSuccess) {
      return Result.ok<T>(json.value);
    } else {
      return Result.fail<T>(json.error);
    }
  }
}

/**
 * 🎯 Usage Examples:
 * 
 * ```typescript
 * // Basic usage
 * function divide(a: number, b: number): Result<number> {
 *   if (b === 0) {
 *     return Result.fail('Division by zero');
 *   }
 *   return Result.ok(a / b);
 * }
 * 
 * const result = divide(10, 2);
 * if (result.isSuccess) {
 *   console.log(result.getValue()); // 5
 * } else {
 *   console.log(result.error);
 * }
 * 
 * // Chaining operations
 * const chainResult = divide(10, 2)
 *   .map(x => x * 2)
 *   .flatMap(x => divide(x, 5))
 *   .map(x => Math.round(x));
 * 
 * // Pattern matching
 * const message = chainResult.match(
 *   value => `Result: ${value}`,
 *   error => `Error: ${error}`
 * );
 * 
 * // Async operations
 * async function fetchUser(id: string): Promise<Result<User>> {
 *   return Result.try(async () => {
 *     const response = await fetch(`/api/users/${id}`);
 *     if (!response.ok) {
 *       throw new Error('User not found');
 *     }
 *     return response.json();
 *   });
 * }
 * 
 * // Combining results
 * const results = [
 *   divide(10, 2),
 *   divide(20, 4),
 *   divide(30, 6)
 * ];
 * 
 * const combined = Result.combine(results);
 * if (combined.isSuccess) {
 *   console.log(combined.getValue()); // [5, 5, 5]
 * }
 * ```
 * 
 * 🔑 Key Benefits:
 * - Explicit error handling
 * - Type safety
 * - Composable operations
 * - No exceptions thrown
 * - Railway-oriented programming
 * - Functional programming patterns
 */
