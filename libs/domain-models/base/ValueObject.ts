/**
 * 🎨 Base Value Object Class - Domain-Driven Design Implementation
 * 
 * Theo knowledge base về DDD và Immutability principles:
 * - Value Objects are immutable
 * - Equality based on attributes, not identity
 * - No side effects in methods
 * - Encapsulate validation logic
 * 
 * @template T - Type của value được wrap
 */

export abstract class ValueObject<T> {
  protected readonly _value: T;

  /**
   * Constructor - Protected để enforce factory methods
   * Theo SOLID principles: SRP - Value Object chỉ wrap và validate value
   */
  protected constructor(value: T) {
    this._value = Object.freeze(value); // Ensure immutability
  }

  /**
   * Getter cho value - Immutable access
   * Theo Encapsulation principle: Controlled access to internal state
   */
  public get value(): T {
    return this._value;
  }

  /**
   * Equality comparison based on value
   * Theo DDD: Value Objects are equal if they have same value
   * 
   * @param other - Value Object khác để so sánh
   * @returns true nếu cùng value
   */
  public equals(other: ValueObject<T>): boolean {
    if (!other || !(other instanceof ValueObject)) {
      return false;
    }

    if (this === other) {
      return true;
    }

    return this.deepEquals(this._value, other._value);
  }

  /**
   * Deep equality check cho complex objects
   * Theo Defensive Programming: Handle all edge cases
   */
  private deepEquals(a: T, b: T): boolean {
    if (a === b) return true;
    
    if (a == null || b == null) return false;
    
    if (typeof a !== typeof b) return false;

    // Handle arrays
    if (Array.isArray(a) && Array.isArray(b)) {
      if (a.length !== b.length) return false;
      return a.every((item, index) => this.deepEquals(item as T, b[index] as T));
    }

    // Handle objects
    if (typeof a === 'object' && typeof b === 'object') {
      const keysA = Object.keys(a as object);
      const keysB = Object.keys(b as object);
      
      if (keysA.length !== keysB.length) return false;
      
      return keysA.every(key => 
        keysB.includes(key) && 
        this.deepEquals((a as any)[key], (b as any)[key])
      );
    }

    return false;
  }

  /**
   * Hash code based on value
   * Useful cho collections và caching
   */
  public hashCode(): string {
    return `${this.constructor.name}_${JSON.stringify(this._value)}`;
  }

  /**
   * String representation
   * Useful cho debugging và logging
   */
  public toString(): string {
    return `${this.constructor.name}(${JSON.stringify(this._value)})`;
  }

  /**
   * Validation helper - Template method pattern
   * Subclasses override để implement specific validation
   */
  protected static validate<T>(value: T): string | null {
    if (value == null) {
      return 'Value cannot be null or undefined';
    }
    return null;
  }
}

/**
 * 🎯 Usage Examples:
 * 
 * ```typescript
 * // Simple Value Object
 * class Email extends ValueObject<string> {
 *   private constructor(value: string) {
 *     super(value);
 *   }
 * 
 *   public static create(email: string): Result<Email> {
 *     const validation = this.validateEmail(email);
 *     if (validation) {
 *       return Result.fail(validation);
 *     }
 *     return Result.ok(new Email(email));
 *   }
 * 
 *   private static validateEmail(email: string): string | null {
 *     const baseValidation = this.validate(email);
 *     if (baseValidation) return baseValidation;
 * 
 *     const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
 *     if (!emailRegex.test(email)) {
 *       return 'Invalid email format';
 *     }
 * 
 *     if (email.length > 254) {
 *       return 'Email too long';
 *     }
 * 
 *     return null;
 *   }
 * 
 *   public getDomain(): string {
 *     return this._value.split('@')[1];
 *   }
 * }
 * 
 * // Complex Value Object
 * interface AddressData {
 *   street: string;
 *   city: string;
 *   zipCode: string;
 *   country: string;
 * }
 * 
 * class Address extends ValueObject<AddressData> {
 *   private constructor(value: AddressData) {
 *     super(value);
 *   }
 * 
 *   public static create(data: AddressData): Result<Address> {
 *     const validation = this.validateAddress(data);
 *     if (validation) {
 *       return Result.fail(validation);
 *     }
 *     return Result.ok(new Address(data));
 *   }
 * 
 *   private static validateAddress(data: AddressData): string | null {
 *     const baseValidation = this.validate(data);
 *     if (baseValidation) return baseValidation;
 * 
 *     if (!data.street?.trim()) return 'Street is required';
 *     if (!data.city?.trim()) return 'City is required';
 *     if (!data.zipCode?.trim()) return 'Zip code is required';
 *     if (!data.country?.trim()) return 'Country is required';
 * 
 *     return null;
 *   }
 * 
 *   public getFullAddress(): string {
 *     return `${this._value.street}, ${this._value.city}, ${this._value.zipCode}, ${this._value.country}`;
 *   }
 * 
 *   public isInCountry(country: string): boolean {
 *     return this._value.country.toLowerCase() === country.toLowerCase();
 *   }
 * }
 * ```
 * 
 * 🔑 Key Benefits:
 * - Immutability ensures thread safety
 * - Validation logic encapsulated
 * - Type safety với strongly typed values
 * - Clear equality semantics
 * - No side effects
 * - Rich domain behavior
 */
