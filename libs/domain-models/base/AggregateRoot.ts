/**
 * 🏰 Aggregate Root Base Class - Domain-Driven Design Implementation
 * 
 * Theo knowledge base về DDD và Event-Driven Architecture:
 * - Aggregate Root là entry point duy nhất vào aggregate
 * - Đảm bảo consistency boundaries
 * - Quản lý domain events
 * - Enforce business invariants
 * 
 * @template TId - Type của aggregate identifier
 */

import { Entity } from './Entity';
import { DomainEvent } from './DomainEvent';

export abstract class AggregateRoot<TId> extends Entity<TId> {
  private _version: number = 0;
  private _isDeleted: boolean = false;

  /**
   * Constructor - Protected để enforce factory methods
   * Theo SOLID principles: SRP - Aggregate Root quản lý consistency boundary
   */
  protected constructor(id: TId) {
    super(id);
  }

  /**
   * Version cho Optimistic Concurrency Control
   * Theo knowledge base về Database concurrency patterns
   */
  public get version(): number {
    return this._version;
  }

  /**
   * Soft delete flag
   * Theo best practices: Soft delete thay vì hard delete
   */
  public get isDeleted(): boolean {
    return this._isDeleted;
  }

  /**
   * Increment version - G<PERSON><PERSON> khi có thay đổi state
   * Theo Optimistic Locking pattern
   */
  protected incrementVersion(): void {
    this._version++;
  }

  /**
   * Mark as deleted - Soft delete implementation
   * Theo Data Integrity principles
   */
  protected markAsDeleted(): void {
    this._isDeleted = true;
    this.incrementVersion();
    this.addDomainEvent(new AggregateDeletedEvent(this.id, this.constructor.name));
  }

  /**
   * Add domain event với version increment
   * Theo Event Sourcing patterns
   */
  protected addDomainEvent(event: DomainEvent): void {
    super.addDomainEvent(event);
    this.incrementVersion();
  }

  /**
   * Validate aggregate invariants
   * Template method - Subclasses override để implement specific rules
   * Theo Template Method pattern
   */
  protected abstract validateInvariants(): string[];

  /**
   * Check if aggregate is in valid state
   * Theo Defensive Programming: Always validate before operations
   */
  public isValid(): boolean {
    const violations = this.validateInvariants();
    return violations.length === 0;
  }

  /**
   * Get validation errors
   * Theo Error Handling best practices
   */
  public getValidationErrors(): string[] {
    return this.validateInvariants();
  }

  /**
   * Apply domain event - Event Sourcing support
   * Theo CQRS và Event Sourcing patterns
   */
  public applyEvent(event: DomainEvent): void {
    this.when(event);
    this.incrementVersion();
  }

  /**
   * Event handler - Template method
   * Subclasses override để handle specific events
   */
  protected abstract when(event: DomainEvent): void;

  /**
   * Create snapshot cho Event Sourcing
   * Theo Snapshot pattern for performance
   */
  public createSnapshot(): AggregateSnapshot<TId> {
    return {
      aggregateId: this.id,
      aggregateType: this.constructor.name,
      version: this._version,
      data: this.getSnapshotData(),
      createdAt: new Date(),
      isDeleted: this._isDeleted
    };
  }

  /**
   * Get snapshot data - Template method
   * Subclasses override để provide specific data
   */
  protected abstract getSnapshotData(): any;

  /**
   * Restore from snapshot
   * Theo Event Sourcing restoration patterns
   */
  public static fromSnapshot<T extends AggregateRoot<any>>(
    snapshot: AggregateSnapshot<any>,
    constructor: new (...args: any[]) => T
  ): T {
    // Implementation depends on specific aggregate
    throw new Error('fromSnapshot must be implemented by concrete aggregate');
  }
}

/**
 * 📸 Aggregate Snapshot Interface
 * Theo Event Sourcing patterns cho performance optimization
 */
export interface AggregateSnapshot<TId> {
  aggregateId: TId;
  aggregateType: string;
  version: number;
  data: any;
  createdAt: Date;
  isDeleted: boolean;
}

/**
 * 🗑️ Aggregate Deleted Event
 * Standard domain event cho soft delete
 */
export class AggregateDeletedEvent extends DomainEvent {
  constructor(
    public readonly aggregateId: any,
    public readonly aggregateType: string
  ) {
    super();
  }
}

/**
 * 🎯 Usage Example:
 * 
 * ```typescript
 * class OrderId extends ValueObject<string> {
 *   // Implementation...
 * }
 * 
 * class Order extends AggregateRoot<OrderId> {
 *   private items: OrderItem[] = [];
 *   private status: OrderStatus = OrderStatus.Draft;
 *   private customerId: CustomerId;
 *   private totalAmount: Money;
 * 
 *   private constructor(
 *     id: OrderId,
 *     customerId: CustomerId
 *   ) {
 *     super(id);
 *     this.customerId = customerId;
 *     this.totalAmount = Money.zero();
 *   }
 * 
 *   public static create(customerId: CustomerId): Result<Order> {
 *     const id = OrderId.create(generateId());
 *     const order = new Order(id.getValue(), customerId);
 *     
 *     order.addDomainEvent(new OrderCreatedEvent(order.id, customerId));
 *     
 *     return Result.ok(order);
 *   }
 * 
 *   // Business methods
 *   public addItem(productId: ProductId, quantity: number, price: Money): Result<void> {
 *     if (this.status !== OrderStatus.Draft) {
 *       return Result.fail('Cannot add items to non-draft order');
 *     }
 * 
 *     const item = OrderItem.create(productId, quantity, price);
 *     if (item.isFailure) {
 *       return Result.fail(item.error);
 *     }
 * 
 *     this.items.push(item.getValue());
 *     this.recalculateTotal();
 *     
 *     this.addDomainEvent(new OrderItemAddedEvent(this.id, item.getValue()));
 *     
 *     return Result.ok();
 *   }
 * 
 *   public confirm(): Result<void> {
 *     if (this.items.length === 0) {
 *       return Result.fail('Cannot confirm empty order');
 *     }
 * 
 *     this.status = OrderStatus.Confirmed;
 *     this.addDomainEvent(new OrderConfirmedEvent(this.id, this.totalAmount));
 *     
 *     return Result.ok();
 *   }
 * 
 *   // Invariants validation
 *   protected validateInvariants(): string[] {
 *     const violations: string[] = [];
 * 
 *     if (this.items.length > 100) {
 *       violations.push('Order cannot have more than 100 items');
 *     }
 * 
 *     if (this.totalAmount.isNegative()) {
 *       violations.push('Order total cannot be negative');
 *     }
 * 
 *     return violations;
 *   }
 * 
 *   // Event handling
 *   protected when(event: DomainEvent): void {
 *     if (event instanceof OrderItemAddedEvent) {
 *       // Handle event application
 *     }
 *     // Handle other events...
 *   }
 * 
 *   // Snapshot support
 *   protected getSnapshotData(): any {
 *     return {
 *       items: this.items.map(item => item.toSnapshot()),
 *       status: this.status,
 *       customerId: this.customerId.value,
 *       totalAmount: this.totalAmount.value
 *     };
 *   }
 * 
 *   private recalculateTotal(): void {
 *     this.totalAmount = this.items.reduce(
 *       (total, item) => total.add(item.getSubtotal()),
 *       Money.zero()
 *     );
 *   }
 * }
 * ```
 * 
 * 🔑 Key Benefits:
 * - Consistency boundary enforcement
 * - Domain events cho loose coupling
 * - Version control cho concurrency
 * - Soft delete support
 * - Event sourcing ready
 * - Business invariants validation
 */
