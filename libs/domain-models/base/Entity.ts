/**
 * 🏛️ Base Entity Class - Domain-Driven Design Implementation
 * 
 * Theo knowledge base về DDD và OOP principles:
 * - Entity có identity duy nhất (ID)
 * - Encapsulation: Hide internal state
 * - Equality based on ID, not attributes
 * - Immutable ID after creation
 * 
 * @template TId - Type của identifier (strongly typed)
 */

import { DomainEvent } from './DomainEvent';

export abstract class Entity<TId> {
  protected readonly _id: TId;
  private _domainEvents: DomainEvent[] = [];

  /**
   * Constructor - Protected để enforce factory methods
   * Theo SOLID principles: SRP - Entity chỉ quản lý identity và domain events
   */
  protected constructor(id: TId) {
    this._id = id;
  }

  /**
   * Getter cho ID - Immutable sau khi tạo
   * Theo Encapsulation principle: Controlled access to internal state
   */
  public get id(): TId {
    return this._id;
  }

  /**
   * Equality comparison based on ID
   * Theo DDD: Entities are equal if they have same ID
   * 
   * @param other - Entity kh<PERSON><PERSON> để so sánh
   * @returns true nếu cùng ID
   */
  public equals(other: Entity<TId>): boolean {
    if (!other || !(other instanceof Entity)) {
      return false;
    }

    if (this === other) {
      return true;
    }

    return this._id === other._id;
  }

  /**
   * Add domain event - Event-Driven Architecture
   * Theo knowledge base về Event-Driven patterns
   * 
   * @param event - Domain event to add
   */
  protected addDomainEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }

  /**
   * Get all domain events
   * Theo Observer pattern: Allow external systems to react to domain changes
   * 
   * @returns Array of domain events
   */
  public getDomainEvents(): DomainEvent[] {
    return [...this._domainEvents]; // Return copy để maintain immutability
  }

  /**
   * Clear domain events - Thường gọi sau khi publish events
   * Theo Event Sourcing patterns
   */
  public clearDomainEvents(): void {
    this._domainEvents = [];
  }

  /**
   * Hash code based on ID
   * Useful cho collections và caching
   */
  public hashCode(): string {
    return `${this.constructor.name}_${String(this._id)}`;
  }

  /**
   * String representation
   * Useful cho debugging và logging
   */
  public toString(): string {
    return `${this.constructor.name}(${String(this._id)})`;
  }
}

/**
 * 🎯 Usage Example:
 * 
 * ```typescript
 * class UserId extends ValueObject<string> {
 *   private constructor(value: string) {
 *     super(value);
 *   }
 * 
 *   public static create(id: string): Result<UserId> {
 *     // Validation logic
 *     return Result.ok(new UserId(id));
 *   }
 * }
 * 
 * class User extends Entity<UserId> {
 *   private constructor(
 *     id: UserId,
 *     private email: Email,
 *     private profile: UserProfile
 *   ) {
 *     super(id);
 *   }
 * 
 *   public static create(
 *     email: string,
 *     profileData: UserProfileData
 *   ): Result<User> {
 *     // Factory method với validation
 *     const emailResult = Email.create(email);
 *     if (emailResult.isFailure) {
 *       return Result.fail(emailResult.error);
 *     }
 * 
 *     const profile = UserProfile.create(profileData);
 *     if (profile.isFailure) {
 *       return Result.fail(profile.error);
 *     }
 * 
 *     const id = UserId.create(generateId());
 *     const user = new User(id.getValue(), emailResult.getValue(), profile.getValue());
 *     
 *     // Domain event
 *     user.addDomainEvent(new UserCreatedEvent(user.id, user.email));
 *     
 *     return Result.ok(user);
 *   }
 * 
 *   // Business methods
 *   public changeEmail(newEmail: string): Result<void> {
 *     const emailResult = Email.create(newEmail);
 *     if (emailResult.isFailure) {
 *       return Result.fail(emailResult.error);
 *     }
 * 
 *     this.email = emailResult.getValue();
 *     this.addDomainEvent(new UserEmailChangedEvent(this.id, this.email));
 *     
 *     return Result.ok();
 *   }
 * }
 * ```
 * 
 * 🔑 Key Benefits:
 * - Type safety với strongly typed IDs
 * - Domain events cho loose coupling
 * - Immutable identity
 * - Clear equality semantics
 * - Encapsulation của business logic
 */
