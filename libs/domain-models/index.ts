/**
 * 🏗️ Domain Models Library - Core Business Entities and Value Objects
 * 
 * Implements Domain-Driven Design patterns with:
 * - Aggregate roots and entities
 * - Value objects
 * - Domain events
 * - Business rules and invariants
 */

import { DomainEvent } from '../shared-types';

// ================================
// 🔧 Base Classes
// ================================

export abstract class Entity {
  protected constructor(public readonly id: string) {}

  equals(other: Entity): boolean {
    return this.id === other.id;
  }
}

export abstract class AggregateRoot extends Entity {
  private _domainEvents: DomainEvent[] = [];

  protected addDomainEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }

  public getUncommittedEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }

  public markEventsAsCommitted(): void {
    this._domainEvents = [];
  }
}

export abstract class ValueObject {
  abstract equals(other: ValueObject): boolean;
}

// ================================
// 👤 User Domain
// ================================

export class UserId extends ValueObject {
  constructor(public readonly value: string) {
    super();
    if (!value || value.trim().length === 0) {
      throw new Error('UserId cannot be empty');
    }
  }

  equals(other: UserId): boolean {
    return this.value === other.value;
  }
}

export class Email extends ValueObject {
  constructor(public readonly value: string) {
    super();
    if (!this.isValidEmail(value)) {
      throw new Error('Invalid email format');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  equals(other: Email): boolean {
    return this.value.toLowerCase() === other.value.toLowerCase();
  }
}

export class Password extends ValueObject {
  constructor(public readonly hashedValue: string) {
    super();
    if (!hashedValue || hashedValue.trim().length === 0) {
      throw new Error('Password hash cannot be empty');
    }
  }

  equals(other: Password): boolean {
    return this.hashedValue === other.hashedValue;
  }
}

export class UserProfile extends ValueObject {
  constructor(
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly avatar?: string
  ) {
    super();
    if (!firstName || firstName.trim().length === 0) {
      throw new Error('First name is required');
    }
    if (!lastName || lastName.trim().length === 0) {
      throw new Error('Last name is required');
    }
  }

  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  equals(other: UserProfile): boolean {
    return (
      this.firstName === other.firstName &&
      this.lastName === other.lastName &&
      this.avatar === other.avatar
    );
  }
}

export class User extends AggregateRoot {
  private constructor(
    id: string,
    public readonly email: Email,
    public readonly password: Password,
    public readonly profile: UserProfile,
    public readonly roles: Role[],
    public readonly isActive: boolean = true,
    public readonly createdAt: Date = new Date(),
    public readonly lastLoginAt?: Date
  ) {
    super(id);
  }

  static create(
    id: string,
    email: Email,
    password: Password,
    profile: UserProfile
  ): User {
    const user = new User(id, email, password, profile, []);
    
    user.addDomainEvent({
      id: crypto.randomUUID(),
      type: 'UserCreated',
      aggregateId: id,
      aggregateType: 'User',
      version: 1,
      data: {
        email: email.value,
        profile: profile,
      },
      metadata: {
        timestamp: new Date(),
      },
    });

    return user;
  }

  assignRole(role: Role): void {
    if (this.hasRole(role)) {
      throw new Error('User already has this role');
    }

    this.roles.push(role);
    
    this.addDomainEvent({
      id: crypto.randomUUID(),
      type: 'UserRoleAssigned',
      aggregateId: this.id,
      aggregateType: 'User',
      version: 1,
      data: {
        roleId: role.id,
        roleName: role.name,
      },
      metadata: {
        timestamp: new Date(),
      },
    });
  }

  removeRole(role: Role): void {
    const index = this.roles.findIndex(r => r.equals(role));
    if (index === -1) {
      throw new Error('User does not have this role');
    }

    this.roles.splice(index, 1);
    
    this.addDomainEvent({
      id: crypto.randomUUID(),
      type: 'UserRoleRemoved',
      aggregateId: this.id,
      aggregateType: 'User',
      version: 1,
      data: {
        roleId: role.id,
        roleName: role.name,
      },
      metadata: {
        timestamp: new Date(),
      },
    });
  }

  hasRole(role: Role): boolean {
    return this.roles.some(r => r.equals(role));
  }

  hasPermission(permission: Permission): boolean {
    return this.roles.some(role => role.hasPermission(permission));
  }

  recordLogin(): void {
    this.addDomainEvent({
      id: crypto.randomUUID(),
      type: 'UserLoggedIn',
      aggregateId: this.id,
      aggregateType: 'User',
      version: 1,
      data: {
        loginTime: new Date(),
      },
      metadata: {
        timestamp: new Date(),
      },
    });
  }
}

// ================================
// 🔐 Authorization Domain
// ================================

export class RoleId extends ValueObject {
  constructor(public readonly value: string) {
    super();
    if (!value || value.trim().length === 0) {
      throw new Error('RoleId cannot be empty');
    }
  }

  equals(other: RoleId): boolean {
    return this.value === other.value;
  }
}

export class PermissionId extends ValueObject {
  constructor(public readonly value: string) {
    super();
    if (!value || value.trim().length === 0) {
      throw new Error('PermissionId cannot be empty');
    }
  }

  equals(other: PermissionId): boolean {
    return this.value === other.value;
  }
}

export class Permission extends Entity {
  constructor(
    id: string,
    public readonly name: string,
    public readonly resource: string,
    public readonly action: string,
    public readonly description?: string
  ) {
    super(id);
    if (!name || name.trim().length === 0) {
      throw new Error('Permission name is required');
    }
    if (!resource || resource.trim().length === 0) {
      throw new Error('Permission resource is required');
    }
    if (!action || action.trim().length === 0) {
      throw new Error('Permission action is required');
    }
  }

  get fullName(): string {
    return `${this.resource}:${this.action}`;
  }
}

export class Role extends Entity {
  constructor(
    id: string,
    public readonly name: string,
    public readonly permissions: Permission[],
    public readonly description?: string,
    public readonly isActive: boolean = true
  ) {
    super(id);
    if (!name || name.trim().length === 0) {
      throw new Error('Role name is required');
    }
  }

  hasPermission(permission: Permission): boolean {
    return this.permissions.some(p => p.equals(permission));
  }

  addPermission(permission: Permission): void {
    if (this.hasPermission(permission)) {
      throw new Error('Role already has this permission');
    }
    this.permissions.push(permission);
  }

  removePermission(permission: Permission): void {
    const index = this.permissions.findIndex(p => p.equals(permission));
    if (index === -1) {
      throw new Error('Role does not have this permission');
    }
    this.permissions.splice(index, 1);
  }
}

// ================================
// 🤖 AI Domain
// ================================

export class ModelId extends ValueObject {
  constructor(public readonly value: string) {
    super();
    if (!value || value.trim().length === 0) {
      throw new Error('ModelId cannot be empty');
    }
  }

  equals(other: ModelId): boolean {
    return this.value === other.value;
  }
}

export class ModelVersion extends ValueObject {
  constructor(public readonly value: string) {
    super();
    if (!value || value.trim().length === 0) {
      throw new Error('Model version cannot be empty');
    }
    if (!this.isValidVersion(value)) {
      throw new Error('Invalid version format');
    }
  }

  private isValidVersion(version: string): boolean {
    const versionRegex = /^\d+\.\d+\.\d+$/;
    return versionRegex.test(version);
  }

  equals(other: ModelVersion): boolean {
    return this.value === other.value;
  }
}

export class AIModel extends AggregateRoot {
  constructor(
    id: string,
    public readonly name: string,
    public readonly version: ModelVersion,
    public readonly type: 'embedding' | 'llm' | 'classification' | 'other',
    public readonly provider: string,
    public readonly isActive: boolean = true,
    public readonly createdAt: Date = new Date()
  ) {
    super(id);
    if (!name || name.trim().length === 0) {
      throw new Error('Model name is required');
    }
    if (!provider || provider.trim().length === 0) {
      throw new Error('Model provider is required');
    }
  }

  activate(): void {
    if (this.isActive) {
      throw new Error('Model is already active');
    }

    this.addDomainEvent({
      id: crypto.randomUUID(),
      type: 'ModelActivated',
      aggregateId: this.id,
      aggregateType: 'AIModel',
      version: 1,
      data: {
        modelName: this.name,
        version: this.version.value,
      },
      metadata: {
        timestamp: new Date(),
      },
    });
  }

  deactivate(): void {
    if (!this.isActive) {
      throw new Error('Model is already inactive');
    }

    this.addDomainEvent({
      id: crypto.randomUUID(),
      type: 'ModelDeactivated',
      aggregateId: this.id,
      aggregateType: 'AIModel',
      version: 1,
      data: {
        modelName: this.name,
        version: this.version.value,
      },
      metadata: {
        timestamp: new Date(),
      },
    });
  }
}

// ================================
// 📊 Analytics Domain
// ================================

export class RequestId extends ValueObject {
  constructor(public readonly value: string) {
    super();
    if (!value || value.trim().length === 0) {
      throw new Error('RequestId cannot be empty');
    }
  }

  equals(other: RequestId): boolean {
    return this.value === other.value;
  }
}

export class APIRequest extends AggregateRoot {
  constructor(
    id: string,
    public readonly userId: UserId,
    public readonly endpoint: string,
    public readonly method: string,
    public readonly timestamp: Date,
    public readonly responseTime: number,
    public readonly statusCode: number,
    public readonly success: boolean
  ) {
    super(id);
  }

  static create(
    id: string,
    userId: UserId,
    endpoint: string,
    method: string,
    responseTime: number,
    statusCode: number
  ): APIRequest {
    const request = new APIRequest(
      id,
      userId,
      endpoint,
      method,
      new Date(),
      responseTime,
      statusCode,
      statusCode >= 200 && statusCode < 300
    );

    request.addDomainEvent({
      id: crypto.randomUUID(),
      type: 'APIRequestCompleted',
      aggregateId: id,
      aggregateType: 'APIRequest',
      version: 1,
      data: {
        userId: userId.value,
        endpoint,
        method,
        responseTime,
        statusCode,
        success: request.success,
      },
      metadata: {
        timestamp: new Date(),
      },
    });

    return request;
  }
}
