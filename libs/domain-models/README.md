# 🏛️ Domain Models Library

> **Domain-Driven Design (DDD) Implementation** - Thể hiện business logic và domain knowledge

## 📋 Tổng Quan

Thư viện này implement **Domain-Driven Design patterns** theo knowledge base:

### **Core DDD Concepts**
- **Entities**: Đ<PERSON><PERSON> tượng có identity duy nhất
- **Value Objects**: Đ<PERSON><PERSON> tượng bất biến, được định nghĩa bởi attributes
- **Aggregates**: Cluster của entities và value objects
- **Aggregate Roots**: Entry point duy nhất vào aggregate
- **Domain Services**: Business logic không thuộc về entity nào
- **Domain Events**: Sự kiện quan trọng trong domain
- **Repositories**: Interface để truy cập aggregates

## 🏗️ Architecture Patterns

### **Clean Architecture Layers**
```
Domain Layer (Core)
├── Entities/           # Business objects với identity
├── ValueObjects/       # Immutable objects
├── Aggregates/         # Consistency boundaries  
├── DomainServices/     # Domain logic
├── DomainEvents/       # Domain events
├── Repositories/       # Data access interfaces
└── Specifications/     # Business rules
```

### **SOLID Principles Implementation**
- **SRP**: Mỗi class có một responsibility duy nhất
- **OCP**: Open for extension, closed for modification
- **LSP**: Liskov Substitution Principle
- **ISP**: Interface Segregation Principle  
- **DIP**: Dependency Inversion Principle

## 🎯 Usage Examples

### **Entity Example**
```typescript
// User Entity với identity và business logic
export class User extends Entity<UserId> {
  private constructor(
    id: UserId,
    private email: Email,
    private profile: UserProfile,
    private permissions: UserPermissions
  ) {
    super(id);
  }

  // Factory method - Domain logic
  public static create(
    email: string,
    profile: UserProfileData
  ): Result<User> {
    // Validation logic
    // Business rules
    // Return Result pattern
  }
}
```

### **Value Object Example**
```typescript
// Email Value Object - Immutable
export class Email extends ValueObject<string> {
  private constructor(value: string) {
    super(value);
  }

  public static create(email: string): Result<Email> {
    // Email validation logic
    // Business rules for email format
  }
}
```

### **Aggregate Example**
```typescript
// Order Aggregate Root
export class Order extends AggregateRoot<OrderId> {
  private items: OrderItem[] = [];
  private status: OrderStatus;

  // Business methods
  public addItem(item: OrderItem): Result<void> {
    // Business logic
    // Raise domain events
  }

  public complete(): Result<void> {
    // State transition logic
    this.addDomainEvent(new OrderCompletedEvent(this.id));
  }
}
```

## 🔒 Security & Validation

### **Input Validation**
- Tất cả inputs được validate tại domain boundary
- Business rules enforcement
- Type safety với TypeScript

### **Domain Events Security**
- Event payload sanitization
- Authorization checks
- Audit logging

## 🧪 Testing Strategy

### **Unit Testing**
- Test business logic isolation
- Mock external dependencies
- Property-based testing

### **Domain Testing Patterns**
```typescript
describe('User Domain', () => {
  it('should create valid user with business rules', () => {
    // Arrange
    const email = '<EMAIL>';
    const profile = { name: 'Test User' };

    // Act
    const result = User.create(email, profile);

    // Assert
    expect(result.isSuccess).toBe(true);
    expect(result.getValue().getEmail().value).toBe(email);
  });
});
```

## 📚 Knowledge Base References

### **Theoretical Foundation**
- **Domain-Driven Design** (Eric Evans)
- **Clean Architecture** (Robert Martin)
- **Enterprise Patterns** (Martin Fowler)

### **Implementation Patterns**
- **Repository Pattern** - Data access abstraction
- **Specification Pattern** - Business rules encapsulation
- **Factory Pattern** - Object creation logic
- **Observer Pattern** - Domain events
- **Strategy Pattern** - Algorithm variations

## 🚀 Best Practices

### **Domain Modeling**
1. **Ubiquitous Language** - Consistent terminology
2. **Bounded Context** - Clear boundaries
3. **Aggregate Design** - Consistency boundaries
4. **Event Storming** - Domain discovery

### **Code Quality**
1. **Immutability** - Value objects are immutable
2. **Encapsulation** - Hide internal state
3. **Validation** - Always validate inputs
4. **Error Handling** - Result pattern for errors

## 🔄 Integration Patterns

### **Application Layer Integration**
```typescript
// Application Service using Domain
export class UserApplicationService {
  constructor(
    private userRepository: IUserRepository,
    private eventBus: IEventBus
  ) {}

  async createUser(command: CreateUserCommand): Promise<Result<void>> {
    // Use domain objects
    const user = User.create(command.email, command.profile);
    
    if (user.isFailure) {
      return Result.fail(user.error);
    }

    await this.userRepository.save(user.getValue());
    
    // Publish domain events
    await this.eventBus.publishAll(user.getValue().getDomainEvents());
    
    return Result.ok();
  }
}
```

## 📈 Performance Considerations

### **Aggregate Loading**
- Lazy loading strategies
- Aggregate size optimization
- Caching patterns

### **Event Processing**
- Asynchronous event handling
- Event sourcing patterns
- CQRS implementation

---

**🎯 Mục tiêu**: Tạo ra domain layer vững chắc, dễ hiểu và maintainable theo best practices từ knowledge base.
