/**
 * 🧮 Data Structures & Algorithms Implementation
 * 
 * Theo knowledge base về Computer Science fundamentals:
 * - Implement các cấu trúc dữ liệu cơ bản
 * - Time & Space complexity analysis
 * - Generic implementations với TypeScript
 * - Production-ready với error handling
 */

/**
 * 📚 Stack Implementation - LIFO (Last In, First Out)
 * Time Complexity: O(1) cho push, pop, peek
 * Space Complexity: O(n)
 */
export class Stack<T> {
  private items: T[] = [];

  /**
   * Add element to top of stack
   * @param item - Element to add
   */
  public push(item: T): void {
    this.items.push(item);
  }

  /**
   * Remove and return top element
   * @returns Top element or undefined if empty
   */
  public pop(): T | undefined {
    return this.items.pop();
  }

  /**
   * View top element without removing
   * @returns Top element or undefined if empty
   */
  public peek(): T | undefined {
    return this.items[this.items.length - 1];
  }

  /**
   * Check if stack is empty
   */
  public isEmpty(): boolean {
    return this.items.length === 0;
  }

  /**
   * Get stack size
   */
  public size(): number {
    return this.items.length;
  }

  /**
   * Clear all elements
   */
  public clear(): void {
    this.items = [];
  }

  /**
   * Convert to array (bottom to top)
   */
  public toArray(): T[] {
    return [...this.items];
  }
}

/**
 * 🚶 Queue Implementation - FIFO (First In, First Out)
 * Time Complexity: O(1) cho enqueue, dequeue, front
 * Space Complexity: O(n)
 */
export class Queue<T> {
  private items: T[] = [];
  private frontIndex = 0;

  /**
   * Add element to rear of queue
   * @param item - Element to add
   */
  public enqueue(item: T): void {
    this.items.push(item);
  }

  /**
   * Remove and return front element
   * @returns Front element or undefined if empty
   */
  public dequeue(): T | undefined {
    if (this.isEmpty()) {
      return undefined;
    }

    const item = this.items[this.frontIndex];
    this.frontIndex++;

    // Reset array when it gets too sparse
    if (this.frontIndex > this.items.length / 2) {
      this.items = this.items.slice(this.frontIndex);
      this.frontIndex = 0;
    }

    return item;
  }

  /**
   * View front element without removing
   * @returns Front element or undefined if empty
   */
  public front(): T | undefined {
    return this.isEmpty() ? undefined : this.items[this.frontIndex];
  }

  /**
   * Check if queue is empty
   */
  public isEmpty(): boolean {
    return this.frontIndex >= this.items.length;
  }

  /**
   * Get queue size
   */
  public size(): number {
    return this.items.length - this.frontIndex;
  }

  /**
   * Clear all elements
   */
  public clear(): void {
    this.items = [];
    this.frontIndex = 0;
  }

  /**
   * Convert to array (front to rear)
   */
  public toArray(): T[] {
    return this.items.slice(this.frontIndex);
  }
}

/**
 * 🔗 Linked List Node
 */
class ListNode<T> {
  constructor(
    public data: T,
    public next: ListNode<T> | null = null
  ) {}
}

/**
 * 🔗 Linked List Implementation
 * Time Complexity: O(1) cho insert/delete at head, O(n) cho search
 * Space Complexity: O(n)
 */
export class LinkedList<T> {
  private head: ListNode<T> | null = null;
  private _size = 0;

  /**
   * Add element to beginning
   * @param data - Element to add
   */
  public prepend(data: T): void {
    const newNode = new ListNode(data, this.head);
    this.head = newNode;
    this._size++;
  }

  /**
   * Add element to end
   * @param data - Element to add
   */
  public append(data: T): void {
    const newNode = new ListNode(data);

    if (!this.head) {
      this.head = newNode;
    } else {
      let current = this.head;
      while (current.next) {
        current = current.next;
      }
      current.next = newNode;
    }
    this._size++;
  }

  /**
   * Remove first occurrence of element
   * @param data - Element to remove
   * @returns true if removed, false if not found
   */
  public remove(data: T): boolean {
    if (!this.head) return false;

    if (this.head.data === data) {
      this.head = this.head.next;
      this._size--;
      return true;
    }

    let current = this.head;
    while (current.next && current.next.data !== data) {
      current = current.next;
    }

    if (current.next) {
      current.next = current.next.next;
      this._size--;
      return true;
    }

    return false;
  }

  /**
   * Find element in list
   * @param data - Element to find
   * @returns true if found, false otherwise
   */
  public contains(data: T): boolean {
    let current = this.head;
    while (current) {
      if (current.data === data) {
        return true;
      }
      current = current.next;
    }
    return false;
  }

  /**
   * Get list size
   */
  public size(): number {
    return this._size;
  }

  /**
   * Check if list is empty
   */
  public isEmpty(): boolean {
    return this._size === 0;
  }

  /**
   * Convert to array
   */
  public toArray(): T[] {
    const result: T[] = [];
    let current = this.head;
    while (current) {
      result.push(current.data);
      current = current.next;
    }
    return result;
  }

  /**
   * Clear all elements
   */
  public clear(): void {
    this.head = null;
    this._size = 0;
  }
}

/**
 * 🗂️ Hash Table Implementation
 * Time Complexity: O(1) average cho get/set/delete, O(n) worst case
 * Space Complexity: O(n)
 */
export class HashTable<K, V> {
  private buckets: Array<Array<[K, V]>>;
  private _size = 0;
  private capacity: number;
  private readonly loadFactorThreshold = 0.75;

  constructor(initialCapacity = 16) {
    this.capacity = initialCapacity;
    this.buckets = new Array(this.capacity);
    for (let i = 0; i < this.capacity; i++) {
      this.buckets[i] = [];
    }
  }

  /**
   * Hash function - Simple string hash
   */
  private hash(key: K): number {
    const str = String(key);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash) % this.capacity;
  }

  /**
   * Set key-value pair
   */
  public set(key: K, value: V): void {
    const index = this.hash(key);
    const bucket = this.buckets[index];

    // Check if key already exists
    for (let i = 0; i < bucket.length; i++) {
      if (bucket[i][0] === key) {
        bucket[i][1] = value;
        return;
      }
    }

    // Add new key-value pair
    bucket.push([key, value]);
    this._size++;

    // Resize if load factor exceeded
    if (this._size > this.capacity * this.loadFactorThreshold) {
      this.resize();
    }
  }

  /**
   * Get value by key
   */
  public get(key: K): V | undefined {
    const index = this.hash(key);
    const bucket = this.buckets[index];

    for (const [k, v] of bucket) {
      if (k === key) {
        return v;
      }
    }

    return undefined;
  }

  /**
   * Delete key-value pair
   */
  public delete(key: K): boolean {
    const index = this.hash(key);
    const bucket = this.buckets[index];

    for (let i = 0; i < bucket.length; i++) {
      if (bucket[i][0] === key) {
        bucket.splice(i, 1);
        this._size--;
        return true;
      }
    }

    return false;
  }

  /**
   * Check if key exists
   */
  public has(key: K): boolean {
    return this.get(key) !== undefined;
  }

  /**
   * Get all keys
   */
  public keys(): K[] {
    const result: K[] = [];
    for (const bucket of this.buckets) {
      for (const [key] of bucket) {
        result.push(key);
      }
    }
    return result;
  }

  /**
   * Get all values
   */
  public values(): V[] {
    const result: V[] = [];
    for (const bucket of this.buckets) {
      for (const [, value] of bucket) {
        result.push(value);
      }
    }
    return result;
  }

  /**
   * Get size
   */
  public size(): number {
    return this._size;
  }

  /**
   * Check if empty
   */
  public isEmpty(): boolean {
    return this._size === 0;
  }

  /**
   * Clear all elements
   */
  public clear(): void {
    this.buckets = new Array(this.capacity);
    for (let i = 0; i < this.capacity; i++) {
      this.buckets[i] = [];
    }
    this._size = 0;
  }

  /**
   * Resize hash table when load factor exceeded
   */
  private resize(): void {
    const oldBuckets = this.buckets;
    this.capacity *= 2;
    this.buckets = new Array(this.capacity);
    for (let i = 0; i < this.capacity; i++) {
      this.buckets[i] = [];
    }
    this._size = 0;

    // Rehash all elements
    for (const bucket of oldBuckets) {
      for (const [key, value] of bucket) {
        this.set(key, value);
      }
    }
  }
}

/**
 * 🎯 Usage Examples:
 * 
 * ```typescript
 * // Stack usage
 * const stack = new Stack<number>();
 * stack.push(1);
 * stack.push(2);
 * console.log(stack.pop()); // 2
 * console.log(stack.peek()); // 1
 * 
 * // Queue usage
 * const queue = new Queue<string>();
 * queue.enqueue('first');
 * queue.enqueue('second');
 * console.log(queue.dequeue()); // 'first'
 * console.log(queue.front()); // 'second'
 * 
 * // Linked List usage
 * const list = new LinkedList<number>();
 * list.append(1);
 * list.append(2);
 * list.prepend(0);
 * console.log(list.toArray()); // [0, 1, 2]
 * 
 * // Hash Table usage
 * const map = new HashTable<string, number>();
 * map.set('key1', 100);
 * map.set('key2', 200);
 * console.log(map.get('key1')); // 100
 * console.log(map.has('key2')); // true
 * ```
 */
