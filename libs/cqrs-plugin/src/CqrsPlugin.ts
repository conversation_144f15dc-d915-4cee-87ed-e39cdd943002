/**
 * CQRS Plugin System - TOS Case Study Implementation
 * Resolves Read-Write contradiction through dialectical architecture
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

// Core Interfaces following DIP
export interface INoSqlStore<T> {
  getById(id: string, collection: string): Promise<T | null>;
  upsert(id: string, data: T, collection: string): Promise<void>;
  bulkUpsert(items: Array<{id: string, data: T}>, collection: string): Promise<void>;
  delete(id: string, collection: string): Promise<void>;
  bulkDelete(ids: string[], collection: string): Promise<void>;
  health(): Promise<boolean>;
}

export interface ICacheRegistry {
  isResourceCached(resourceType: string): Promise<boolean>;
  getCacheConfig(resourceType: string): Promise<CacheConfig | null>;
  enableResource(resourceType: string, config: CacheConfig): Promise<void>;
  disableResource(resourceType: string): Promise<void>;
  reload(): Promise<void>;
}

export interface IResourceRouter {
  route<T>(
    resourceType: string, 
    id: string, 
    legacyHandler: () => Promise<T>
  ): Promise<T>;
}

// Domain Models
export interface CacheConfig {
  resourceType: string;
  enabled: boolean;
  provider: 'mongo' | 'redis' | 'dynamo';
  collection: string;
  projectionVersion: string;
  denormalizeFields: string[];
  indexFields: string[];
}

export interface DomainEvent {
  readonly eventId: string;
  readonly eventType: string;
  readonly resourceType: string;
  readonly resourceId: string;
  readonly aggregateVersion: number;
  readonly payload: any;
  readonly metadata: EventMetadata;
  readonly occurredAt: Date;
}

export interface EventMetadata {
  readonly userId?: string;
  readonly correlationId: string;
  readonly causationId?: string;
  readonly idempotencyKey: string;
}

// 1. Registry Module - O(1) resource lookup with singleton pattern
@Injectable()
export class CacheRegistryService implements ICacheRegistry {
  private readonly logger = new Logger(CacheRegistryService.name);
  private readonly registryCache = new Map<string, CacheConfig>();
  private readonly TTL_SECONDS = 300; // 5 minutes
  private lastReload = Date.now();

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.initializeRegistry();
    this.setupHotReload();
  }

  async isResourceCached(resourceType: string): Promise<boolean> {
    await this.ensureFresh();
    const config = this.registryCache.get(resourceType);
    return config?.enabled ?? false;
  }

  async getCacheConfig(resourceType: string): Promise<CacheConfig | null> {
    await this.ensureFresh();
    return this.registryCache.get(resourceType) || null;
  }

  async enableResource(resourceType: string, config: CacheConfig): Promise<void> {
    // Persist to database
    await this.persistConfig(config);
    
    // Update in-memory cache
    this.registryCache.set(resourceType, { ...config, enabled: true });
    
    // Emit event for backfill
    this.eventEmitter.emit('cache.resource.enabled', {
      resourceType,
      config,
      timestamp: new Date()
    });
    
    this.logger.log(`Resource ${resourceType} enabled for caching`);
  }

  async disableResource(resourceType: string): Promise<void> {
    const config = this.registryCache.get(resourceType);
    if (!config) return;

    // Update database
    await this.persistConfig({ ...config, enabled: false });
    
    // Remove from cache
    this.registryCache.delete(resourceType);
    
    // Emit event for data purge
    this.eventEmitter.emit('cache.resource.disabled', {
      resourceType,
      config,
      timestamp: new Date()
    });
    
    this.logger.log(`Resource ${resourceType} disabled and data purged`);
  }

  async reload(): Promise<void> {
    const configs = await this.loadConfigsFromDatabase();
    this.registryCache.clear();
    
    for (const config of configs.filter(c => c.enabled)) {
      this.registryCache.set(config.resourceType, config);
    }
    
    this.lastReload = Date.now();
    this.logger.log(`Registry reloaded with ${this.registryCache.size} active resources`);
  }

  private async ensureFresh(): Promise<void> {
    if (Date.now() - this.lastReload > this.TTL_SECONDS * 1000) {
      await this.reload();
    }
  }

  private setupHotReload(): void {
    // Listen for configuration changes
    this.eventEmitter.on('cache.config.changed', async () => {
      await this.reload();
    });
  }

  private async initializeRegistry(): Promise<void> {
    await this.reload();
  }

  private async persistConfig(config: CacheConfig): Promise<void> {
    // Implementation depends on your database choice
    // This would typically use Repository pattern
  }

  private async loadConfigsFromDatabase(): Promise<CacheConfig[]> {
    // Implementation depends on your database choice
    return []; // Placeholder
  }
}

// 2. Router Decorator - Intelligent routing with fallback
@Injectable()
export class ResourceRouterService implements IResourceRouter {
  private readonly logger = new Logger(ResourceRouterService.name);

  constructor(
    private readonly registry: ICacheRegistry,
    private readonly noSqlStore: INoSqlStore<any>
  ) {}

  async route<T>(
    resourceType: string, 
    id: string, 
    legacyHandler: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      // O(1) lookup in registry
      const isCached = await this.registry.isResourceCached(resourceType);
      
      if (!isCached) {
        this.logger.debug(`Resource ${resourceType} not cached, using legacy path`);
        return await this.executeLegacyPath(legacyHandler, resourceType, id, startTime);
      }

      // Try NoSQL path first
      const config = await this.registry.getCacheConfig(resourceType);
      if (!config) {
        return await this.executeLegacyPath(legacyHandler, resourceType, id, startTime);
      }

      const cachedData = await this.noSqlStore.getById(id, config.collection);
      
      if (cachedData) {
        this.recordMetrics('cache_hit', resourceType, Date.now() - startTime);
        this.logger.debug(`Cache hit for ${resourceType}:${id}`);
        return cachedData as T;
      }

      // Cache miss - fallback to legacy with optional warm-up
      this.recordMetrics('cache_miss', resourceType, Date.now() - startTime);
      this.logger.warn(`Cache miss for ${resourceType}:${id}, falling back to legacy`);
      
      const result = await legacyHandler();
      
      // Optionally trigger warm-up (fire and forget)
      this.triggerWarmUp(resourceType, id, result, config).catch(err => 
        this.logger.error(`Warm-up failed for ${resourceType}:${id}`, err)
      );
      
      return result;

    } catch (error) {
      this.logger.error(`Routing error for ${resourceType}:${id}`, error);
      this.recordMetrics('routing_error', resourceType, Date.now() - startTime);
      
      // Always fallback to legacy on error
      return await this.executeLegacyPath(legacyHandler, resourceType, id, startTime);
    }
  }

  private async executeLegacyPath<T>(
    legacyHandler: () => Promise<T>,
    resourceType: string,
    id: string,
    startTime: number
  ): Promise<T> {
    const result = await legacyHandler();
    this.recordMetrics('legacy_path', resourceType, Date.now() - startTime);
    return result;
  }

  private async triggerWarmUp<T>(
    resourceType: string,
    id: string,
    data: T,
    config: CacheConfig
  ): Promise<void> {
    const denormalizedData = this.denormalizeData(data, config.denormalizeFields);
    await this.noSqlStore.upsert(id, denormalizedData, config.collection);
    this.logger.debug(`Warmed up cache for ${resourceType}:${id}`);
  }

  private denormalizeData(data: any, fields: string[]): any {
    if (!fields.length) return data;
    
    const denormalized: any = {};
    for (const field of fields) {
      if (data[field] !== undefined) {
        denormalized[field] = data[field];
      }
    }
    return denormalized;
  }

  private recordMetrics(operation: string, resourceType: string, latency: number): void {
    // Integration with Prometheus metrics
    // this.metricsService.recordOperation(operation, resourceType, latency);
  }
}

// 3. Sync Engine - Event-driven synchronization with resilience patterns
@Injectable()
export class SyncEngineService {
  private readonly logger = new Logger(SyncEngineService.name);
  private readonly circuitBreakers = new Map<string, CircuitBreaker>();
  
  constructor(
    private readonly registry: ICacheRegistry,
    private readonly noSqlStore: INoSqlStore<any>,
    private readonly eventEmitter: EventEmitter2
  ) {
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.eventEmitter.on('domain.*.created', this.handleDomainEvent.bind(this));
    this.eventEmitter.on('domain.*.updated', this.handleDomainEvent.bind(this));
    this.eventEmitter.on('domain.*.deleted', this.handleDomainEvent.bind(this));
    
    this.eventEmitter.on('cache.resource.enabled', this.handleResourceEnabled.bind(this));
    this.eventEmitter.on('cache.resource.disabled', this.handleResourceDisabled.bind(this));
  }

  async handleDomainEvent(event: DomainEvent): Promise<void> {
    const { resourceType, resourceId, eventType } = event;
    
    // Check if resource is cached
    const isCached = await this.registry.isResourceCached(resourceType);
    if (!isCached) {
      this.logger.debug(`Ignoring event for uncached resource: ${resourceType}`);
      return;
    }

    const config = await this.registry.getCacheConfig(resourceType);
    if (!config) return;

    // Get or create circuit breaker for this resource type
    const breaker = this.getCircuitBreaker(resourceType);
    
    try {
      await breaker.execute(async () => {
        await this.syncResource(event, config);
      });
    } catch (error) {
      this.logger.error(`Sync failed for ${resourceType}:${resourceId}`, error);
      
      // Send to DLQ for retry
      await this.sendToDLQ(event, error);
    }
  }

  private async syncResource(event: DomainEvent, config: CacheConfig): Promise<void> {
    const { eventType, resourceId, payload } = event;
    
    // Idempotency check
    if (await this.isEventProcessed(event.eventId)) {
      this.logger.debug(`Event ${event.eventId} already processed, skipping`);
      return;
    }

    switch (eventType) {
      case 'created':
      case 'updated':
        const denormalizedData = this.denormalizePayload(payload, config);
        await this.noSqlStore.upsert(resourceId, denormalizedData, config.collection);
        break;
        
      case 'deleted':
        await this.noSqlStore.delete(resourceId, config.collection);
        break;
        
      default:
        this.logger.warn(`Unknown event type: ${eventType}`);
        return;
    }
    
    // Mark event as processed
    await this.markEventProcessed(event.eventId);
    
    this.logger.debug(`Synced ${eventType} event for ${config.resourceType}:${resourceId}`);
  }

  private async handleResourceEnabled(event: { resourceType: string, config: CacheConfig }): Promise<void> {
    this.logger.log(`Starting backfill for resource: ${event.resourceType}`);
    // Trigger backfill job
    await this.triggerBackfill(event.resourceType, event.config);
  }

  private async handleResourceDisabled(event: { resourceType: string, config: CacheConfig }): Promise<void> {
    this.logger.log(`Starting purge for resource: ${event.resourceType}`);
    // Purge all data for this resource type
    await this.purgeResourceData(event.resourceType, event.config);
  }

  private getCircuitBreaker(resourceType: string): CircuitBreaker {
    if (!this.circuitBreakers.has(resourceType)) {
      this.circuitBreakers.set(resourceType, new CircuitBreaker({
        failureThreshold: 5,
        resetTimeout: 60000, // 1 minute
        monitoringPeriod: 10000 // 10 seconds
      }));
    }
    return this.circuitBreakers.get(resourceType)!;
  }

  private denormalizePayload(payload: any, config: CacheConfig): any {
    const denormalized: any = { ...payload };
    
    // Apply projection based on config
    if (config.denormalizeFields.length > 0) {
      const filtered: any = {};
      for (const field of config.denormalizeFields) {
        if (payload[field] !== undefined) {
          filtered[field] = payload[field];
        }
      }
      return filtered;
    }
    
    return denormalized;
  }

  private async isEventProcessed(eventId: string): Promise<boolean> {
    // Check processed events store (Redis/DB)
    return false; // Placeholder
  }

  private async markEventProcessed(eventId: string): Promise<void> {
    // Store in processed events (with TTL)
  }

  private async sendToDLQ(event: DomainEvent, error: Error): Promise<void> {
    // Send to dead letter queue for manual retry
    this.logger.error(`Sending event ${event.eventId} to DLQ`, error);
  }

  private async triggerBackfill(resourceType: string, config: CacheConfig): Promise<void> {
    // Implementation depends on data source
    // Could use batching with checkpoint for large datasets
  }

  private async purgeResourceData(resourceType: string, config: CacheConfig): Promise<void> {
    // Remove all data for this resource type from NoSQL store
    this.logger.log(`Purging all data for resource type: ${resourceType}`);
  }
}

// Circuit Breaker Implementation
class CircuitBreaker {
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failures = 0;
  private lastFailureTime = 0;
  
  constructor(private config: {
    failureThreshold: number;
    resetTimeout: number;
    monitoringPeriod: number;
  }) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.config.resetTimeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.config.failureThreshold) {
      this.state = 'OPEN';
    }
  }
}

// Factory for creating CQRS Plugin instances
export class CqrsPluginFactory {
  static create(
    registry: ICacheRegistry,
    noSqlStore: INoSqlStore<any>,
    eventEmitter: EventEmitter2
  ) {
    const router = new ResourceRouterService(registry, noSqlStore);
    const syncEngine = new SyncEngineService(registry, noSqlStore, eventEmitter);
    
    return {
      registry,
      router,
      syncEngine,
      noSqlStore
    };
  }
}

export {
  CacheRegistryService,
  ResourceRouterService,
  SyncEngineService
};