<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TOS Framework - Interactive</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', sans-serif; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: #333; min-height: 100vh; }
        .container { display: flex; min-height: 100vh; }
        .sidebar { width: 280px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); padding: 2rem 0; overflow-y: auto; }
        .nav-item { display: block; padding: 0.75rem 1rem; text-decoration: none; color: #555; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .nav-item:hover, .nav-item.active { background: rgba(30, 60, 114, 0.1); border-left-color: #1e3c72; color: #1e3c72; }
        .main-content { flex: 1; padding: 2rem; overflow-y: auto; }
        .content-section { display: none; background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 2rem; box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37); }
        .content-section.active { display: block; animation: fadeIn 0.5s ease-in-out; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .section-title { font-size: 2rem; color: #1e3c72; margin-bottom: 1rem; border-bottom: 3px solid #2a5298; padding-bottom: 0.5rem; }
        .principle-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1.5rem; border-radius: 8px; margin-bottom: 1rem; }
        .step-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin-top: 2rem; }
        .step-card { background: white; border: 2px solid #e0e0e0; border-radius: 8px; padding: 1.5rem; }
        .btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; }
        .progress-bar { width: 100%; height: 20px; background: #e0e0e0; border-radius: 10px; overflow: hidden; margin: 1rem 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <h1 style="color: #1e3c72; text-align: center; margin-bottom: 2rem;">TOS Framework</h1>
            <a href="#" class="nav-item active" data-section="overview">Overview</a>
            <a href="#" class="nav-item" data-section="principles">Meta Principles</a>
            <a href="#" class="nav-item" data-section="process">12-Step Process</a>
            <a href="#" class="nav-item" data-section="decision">Decision Matrix</a>
            <a href="#" class="nav-item" data-section="practice">Practice Tracker</a>
        </nav>

        <main class="main-content">
            <section id="overview" class="content-section active">
                <h2 class="section-title">Thinking Operating System</h2>
                <div class="principle-card">
                    <h3>🧠 Metacognitive Framework</h3>
                    <p>Integrates dialectical materialism with cognitive science for systematic problem-solving.</p>
                </div>
                <canvas id="overviewChart" style="height: 400px; margin: 2rem 0;"></canvas>
            </section>

            <section id="principles" class="content-section">
                <h2 class="section-title">Six Meta Principles</h2>
                <div class="step-container">
                    <div class="step-card">
                        <h3>1. Subject Principle</h3>
                        <p>Every entity has State (what it is) and Behavior (what it does).</p>
                    </div>
                    <div class="step-card">
                        <h3>2. Contradiction Principle</h3>
                        <p>Development comes from resolving internal contradictions.</p>
                    </div>
                    <div class="step-card">
                        <h3>3. Quantity-Quality</h3>
                        <p>Quantitative changes force qualitative transformation.</p>
                    </div>
                </div>
            </section>

            <section id="process" class="content-section">
                <h2 class="section-title">12-Step Process</h2>
                <div class="progress-bar">
                    <div class="progress-fill" id="stepProgress" style="width: 25%"></div>
                </div>
                <div id="currentStep">
                    <h3>Step 1: Define</h3>
                    <p>Identify core contradictions using 5W1H analysis.</p>
                </div>
                <button class="btn" onclick="nextStep()">Next Step</button>
            </section>

            <section id="decision" class="content-section">
                <h2 class="section-title">Decision Matrix</h2>
                <div>
                    <input type="text" id="alternativeInput" placeholder="Add alternative">
                    <button class="btn" onclick="addAlternative()">Add</button>
                </div>
                <div id="matrixDisplay"></div>
            </section>

            <section id="practice" class="content-section">
                <h2 class="section-title">Practice Tracker</h2>
                <div class="step-container">
                    <div class="step-card">
                        <h3>Morning Reflection</h3>
                        <p>10 minutes framework review</p>
                        <button class="btn" onclick="markComplete('morning')">Complete</button>
                    </div>
                    <div class="step-card">
                        <h3>Decision Logging</h3>
                        <p>Record decisions using ADR</p>
                        <button class="btn" onclick="markComplete('decisions')">Complete</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        let currentStepIndex = 0;
        let alternatives = [];
        
        const steps = [
            'Define: Identify contradictions',
            'Measure: Quantify problems', 
            'Analyze: Root cause analysis',
            'First Principles: Decompose',
            'Systems View: Map relationships',
            'Hypothesize: Generate alternatives',
            'Evaluate: Decision matrix',
            'Decide: Select optimal',
            'Design: Detailed plan',
            'Implement: Execute',
            'Verify: Test results',
            'Improve: Learn and iterate'
        ];

        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(n => n.classList.remove('active'));
                document.querySelectorAll('.content-section').forEach(s => s.classList.remove('active'));
                item.classList.add('active');
                document.getElementById(item.dataset.section).classList.add('active');
            });
        });

        // Initialize chart
        window.onload = function() {
            const ctx = document.getElementById('overviewChart').getContext('2d');
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['Perception', 'Memory', 'Reasoning', 'Decision', 'Metacognition'],
                    datasets: [{
                        label: 'Current',
                        data: [7, 8, 6, 7, 5],
                        backgroundColor: 'rgba(102, 126, 234, 0.2)',
                        borderColor: 'rgba(102, 126, 234, 1)'
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false }
            });
        };

        function nextStep() {
            currentStepIndex = (currentStepIndex + 1) % steps.length;
            updateStepDisplay();
        }

        function updateStepDisplay() {
            document.getElementById('stepProgress').style.width = ((currentStepIndex + 1) / steps.length * 100) + '%';
            document.getElementById('currentStep').innerHTML = `
                <h3>Step ${currentStepIndex + 1}</h3>
                <p>${steps[currentStepIndex]}</p>
            `;
        }

        function addAlternative() {
            const input = document.getElementById('alternativeInput');
            if (input.value.trim()) {
                alternatives.push(input.value.trim());
                input.value = '';
                updateMatrix();
            }
        }

        function updateMatrix() {
            const display = document.getElementById('matrixDisplay');
            display.innerHTML = alternatives.map(alt => `<div style="margin: 0.5rem 0; padding: 0.5rem; background: #f0f0f0; border-radius: 4px;">${alt}</div>`).join('');
        }

        function markComplete(activity) {
            const progress = JSON.parse(localStorage.getItem('tosProgress') || '{}');
            const today = new Date().toDateString();
            if (!progress[today]) progress[today] = {};
            progress[today][activity] = true;
            localStorage.setItem('tosProgress', JSON.stringify(progress));
            alert(activity + ' marked as complete!');
        }
    </script>
</body>
</html>