# 🐳 Production Dockerfile - API Gateway
# Multi-stage build for optimized production image

# ================================
# 🏗️ Build Stage
# ================================
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeapp -u 1001

# Copy package files
COPY package*.json ./
COPY apps/api-gateway/package*.json ./apps/api-gateway/
COPY libs/ ./libs/

# Install dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Copy source code
COPY apps/api-gateway ./apps/api-gateway
COPY libs ./libs
COPY tsconfig.json ./

# Build the application
WORKDIR /app/apps/api-gateway
RUN npm run build

# ================================
# 🚀 Production Stage
# ================================
FROM node:20-alpine AS production

# Install security updates
RUN apk upgrade --no-cache && \
    apk add --no-cache \
    dumb-init \
    tini \
    curl \
    ca-certificates

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeapp -u 1001

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=nodeapp:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nodeapp:nodejs /app/apps/api-gateway/dist ./dist
COPY --from=builder --chown=nodeapp:nodejs /app/apps/api-gateway/package*.json ./

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads /app/tmp && \
    chown -R nodeapp:nodejs /app

# Set environment variables
ENV NODE_ENV=production \
    PORT=3000 \
    LOG_LEVEL=info \
    UPLOADS_DIR=/app/uploads \
    TMP_DIR=/app/tmp

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Security hardening
RUN rm -rf /tmp/* /var/cache/apk/* && \
    chmod 755 /app && \
    find /app -type f -exec chmod 644 {} \; && \
    find /app -type d -exec chmod 755 {} \; && \
    chmod +x /usr/bin/dumb-init

# Switch to non-root user
USER nodeapp

# Expose port
EXPOSE 3000

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/main.js"]

# ================================
# 🛠️ Development Stage
# ================================
FROM node:20-alpine AS development

# Install development dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl \
    bash \
    vim

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeapp -u 1001

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY apps/api-gateway/package*.json ./apps/api-gateway/
COPY libs/ ./libs/

# Install all dependencies (including dev)
RUN npm ci && npm cache clean --force

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads /app/tmp && \
    chown -R nodeapp:nodejs /app

# Copy source code
COPY apps/api-gateway ./apps/api-gateway
COPY libs ./libs
COPY tsconfig.json ./

# Set environment
ENV NODE_ENV=development \
    PORT=3000 \
    LOG_LEVEL=debug

# Switch to non-root user
USER nodeapp

EXPOSE 3000

# Start in development mode with hot reload
CMD ["npm", "run", "start:dev"]

# ================================
# 🧪 Testing Stage
# ================================
FROM development AS testing

# Switch back to root for test setup
USER root

# Install additional testing tools
RUN apk add --no-cache \
    chromium \
    firefox

# Set up Chrome for Puppeteer
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Install global testing tools
RUN npm install -g \
    jest \
    @playwright/test \
    artillery \
    k6

# Copy test files
COPY apps/api-gateway/test ./apps/api-gateway/test
COPY apps/api-gateway/jest.config.js ./apps/api-gateway/
COPY tests ./tests

# Create test directories
RUN mkdir -p /app/coverage /app/test-results && \
    chown -R nodeapp:nodejs /app

# Switch back to non-root user
USER nodeapp

# Set test environment
ENV NODE_ENV=test \
    LOG_LEVEL=error

# Run tests by default
CMD ["npm", "run", "test:ci"]