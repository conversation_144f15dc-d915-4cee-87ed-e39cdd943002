# 🌐 API Gateway Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# 🌍 ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=development
LOG_LEVEL=info
DEBUG=api-gateway:*

# =============================================================================
# 🌐 SERVER CONFIGURATION
# =============================================================================
PORT=3000
HOST=localhost
API_PREFIX=/api/v1
API_VERSION=1.0.0
API_TITLE=Enterprise Platform API Gateway
API_DESCRIPTION=Comprehensive API Gateway for Enterprise Platform Services

# =============================================================================
# 🗄️ DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgresql://enterprise_user:enterprise_password@localhost:5432/enterprise_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=enterprise_db
DATABASE_USER=enterprise_user
DATABASE_PASSWORD=enterprise_password
DATABASE_SSL=false
DATABASE_LOGGING=true
DATABASE_SYNCHRONIZE=false
DATABASE_MIGRATIONS_RUN=true
DATABASE_MIGRATIONS_TABLE_NAME=migrations

# Connection Pool
DATABASE_POOL_SIZE=10
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
DATABASE_POOL_IDLE=10000
DATABASE_POOL_ACQUIRE=60000
DATABASE_POOL_CREATE=30000
DATABASE_POOL_DESTROY=5000

# =============================================================================
# 🔴 REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TTL=3600
REDIS_KEY_PREFIX=api-gateway:

# Session Configuration
SESSION_SECRET=your-session-secret-change-this-in-production
SESSION_TTL=86400
SESSION_STORE=redis

# =============================================================================
# 🔐 AUTHENTICATION & SECURITY
# =============================================================================
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=enterprise-platform
JWT_AUDIENCE=api-gateway

# API Security
API_KEY_HEADER=x-api-key
API_KEY_SECRET=your-api-key-secret-change-this-in-production
BEARER_TOKEN_HEADER=authorization

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:8080
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false
RATE_LIMIT_SKIP_FAILED_REQUESTS=false

# =============================================================================
# 🤝 SERVICE DISCOVERY & COMMUNICATION
# =============================================================================
# AI Service Configuration
AI_SERVICE_URL=http://localhost:8000
AI_SERVICE_TIMEOUT=30000
AI_SERVICE_RETRIES=3
AI_SERVICE_RETRY_DELAY=1000
AI_SERVICE_HEALTH_CHECK=/health
AI_SERVICE_API_PREFIX=/api/v1

# Service Registry
SERVICE_REGISTRY_ENABLED=true
SERVICE_REGISTRY_TTL=30000
SERVICE_REGISTRY_HEARTBEAT=10000

# Circuit Breaker
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60000
CIRCUIT_BREAKER_RESET_TIMEOUT=30000

# =============================================================================
# 📊 MONITORING & OBSERVABILITY
# =============================================================================
# Metrics
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
PROMETHEUS_ENDPOINT=/metrics
METRICS_PREFIX=api_gateway_

# Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_ENDPOINT=/health
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Logging
LOG_FORMAT=json
LOG_TIMESTAMP=true
LOG_COLORIZE=false
LOG_FILE_ENABLED=false
LOG_FILE_PATH=./logs/api-gateway.log
LOG_FILE_MAX_SIZE=10m
LOG_FILE_MAX_FILES=5

# Request Logging
REQUEST_LOGGING_ENABLED=true
REQUEST_LOGGING_SKIP_PATHS=/health,/metrics,/favicon.ico

# =============================================================================
# 🔧 MIDDLEWARE CONFIGURATION
# =============================================================================
# Compression
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6
COMPRESSION_THRESHOLD=1024

# Body Parser
BODY_LIMIT=10mb
JSON_LIMIT=10mb
URL_ENCODED_LIMIT=10mb
URL_ENCODED_EXTENDED=true

# Security Headers
HELMET_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true

# =============================================================================
# 🚀 PERFORMANCE & OPTIMIZATION
# =============================================================================
# Caching
CACHE_ENABLED=true
CACHE_TTL=300
CACHE_MAX_SIZE=100
CACHE_CHECK_PERIOD=600

# Keep Alive
KEEP_ALIVE_TIMEOUT=5000
HEADERS_TIMEOUT=60000

# Graceful Shutdown
GRACEFUL_SHUTDOWN_TIMEOUT=10000
SHUTDOWN_SIGNALS=SIGTERM,SIGINT

# =============================================================================
# 🧪 DEVELOPMENT & TESTING
# =============================================================================
# Hot Reload
HOT_RELOAD=true
WATCH_FILES=true

# API Documentation
SWAGGER_ENABLED=true
SWAGGER_PATH=/docs
SWAGGER_JSON_PATH=/docs-json

# Debug Settings
DEBUG_SQL=false
DEBUG_REDIS=false
DEBUG_HTTP=false
DEBUG_ROUTES=false

# Test Configuration
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_db
TEST_REDIS_URL=redis://localhost:6380
TEST_PORT=3001

# =============================================================================
# 🔒 PRODUCTION SECURITY
# =============================================================================
# SSL/TLS
SSL_ENABLED=false
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem
SSL_CA_PATH=./ssl/ca.pem

# Trust Proxy
TRUST_PROXY=false
PROXY_TIMEOUT=30000

# Request Validation
VALIDATION_ENABLED=true
VALIDATION_WHITELIST=true
VALIDATION_FORBID_NON_WHITELISTED=true
VALIDATION_SKIP_MISSING_PROPERTIES=false
VALIDATION_TRANSFORM=true
