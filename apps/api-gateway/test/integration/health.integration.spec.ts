/**
 * 🧪 Health Module Integration Tests
 * 
 * Integration tests for health endpoints:
 * - Full application context testing
 * - HTTP endpoint testing
 * - Database and Redis integration
 * - Performance and load testing
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { HealthModule } from '../../src/modules/health/health.module';
import { TestUtils, testConfig } from '../setup';

describe('Health Integration Tests', () => {
  let app: INestApplication;
  let httpServer: any;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [() => testConfig],
        }),
        HealthModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
    httpServer = app.getHttpServer();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /health', () => {
    it('should return basic health status', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health')
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        status: 'healthy',
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        version: '1.0.0',
      });
    });

    it('should return health status within acceptable time', async () => {
      // Arrange
      const startTime = Date.now();

      // Act
      await request(httpServer)
        .get('/health')
        .expect(200);

      // Assert
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(500); // Should respond within 500ms
    });

    it('should handle multiple concurrent requests', async () => {
      // Arrange
      const requests = Array.from({ length: 10 }, () =>
        request(httpServer).get('/health')
      );

      // Act
      const responses = await Promise.all(requests);

      // Assert
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.status).toBe('healthy');
      });
    });
  });

  describe('GET /health/detailed', () => {
    it('should return detailed health information', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health/detailed')
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        status: expect.stringMatching(/^(healthy|degraded)$/),
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        version: '1.0.0',
        system: {
          cpu: {
            usage_percent: expect.any(Number),
            count: expect.any(Number),
            load_avg: expect.any(Array),
          },
          memory: {
            total: expect.any(Number),
            available: expect.any(Number),
            used: expect.any(Number),
            percent: expect.any(Number),
          },
          disk: {
            total: expect.any(Number),
            used: expect.any(Number),
            free: expect.any(Number),
            percent: expect.any(Number),
          },
        },
        services: {
          database: {
            status: expect.stringMatching(/^(healthy|unhealthy)$/),
            responseTime: expect.any(String),
            host: expect.any(String),
            port: expect.any(Number),
          },
          redis: {
            status: expect.stringMatching(/^(healthy|unhealthy)$/),
            responseTime: expect.any(String),
            host: expect.any(String),
            port: expect.any(Number),
          },
          external_apis: {
            status: expect.stringMatching(/^(healthy|unhealthy)$/),
            openai: expect.stringMatching(/^(available|not_configured)$/),
            huggingface: expect.stringMatching(/^(available|not_configured)$/),
          },
        },
        models: {
          embedding_model: {
            status: expect.stringMatching(/^(available|unavailable)$/),
            model: expect.any(String),
            loaded: expect.any(Boolean),
          },
          llm_model: {
            status: expect.stringMatching(/^(available|unavailable)$/),
            model: expect.any(String),
            loaded: expect.any(Boolean),
          },
          local_models: {
            status: expect.stringMatching(/^(available|unavailable)$/),
            count: expect.any(Number),
            models: expect.any(Array),
          },
        },
        performance: {
          avg_response_time: expect.any(Number),
          requests_per_minute: expect.any(Number),
          error_rate: expect.any(Number),
          cache_hit_rate: expect.any(Number),
        },
      });
    });

    it('should validate system metrics are within reasonable ranges', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health/detailed')
        .expect(200);

      // Assert
      const { system } = response.body;
      expect(system.cpu.usage_percent).toBeGreaterThanOrEqual(0);
      expect(system.cpu.usage_percent).toBeLessThanOrEqual(100);
      expect(system.memory.percent).toBeGreaterThanOrEqual(0);
      expect(system.memory.percent).toBeLessThanOrEqual(100);
      expect(system.disk.percent).toBeGreaterThanOrEqual(0);
      expect(system.disk.percent).toBeLessThanOrEqual(100);
    });
  });

  describe('GET /health/ready', () => {
    it('should return readiness status', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health/ready')
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        status: 'ready',
      });
    });

    it('should return 503 when dependencies are not ready', async () => {
      // This test would require mocking dependencies to be unavailable
      // For now, we'll test the happy path
      const response = await request(httpServer)
        .get('/health/ready');

      expect([200, 503]).toContain(response.status);
    });
  });

  describe('GET /health/live', () => {
    it('should return liveness status', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health/live')
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        status: 'alive',
      });
    });

    it('should respond quickly for liveness probe', async () => {
      // Arrange
      const startTime = Date.now();

      // Act
      await request(httpServer)
        .get('/health/live')
        .expect(200);

      // Assert
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(100); // Should respond within 100ms
    });
  });

  describe('GET /health/info', () => {
    it('should return application information', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health/info')
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        name: expect.any(String),
        version: expect.any(String),
        environment: 'test',
        uptime: expect.any(Number),
        timestamp: expect.any(String),
        nodeVersion: expect.any(String),
        platform: expect.any(String),
        architecture: expect.any(String),
        memory: {
          used: expect.any(Number),
          total: expect.any(Number),
          external: expect.any(Number),
          rss: expect.any(Number),
        },
        cpu: {
          usage: expect.any(Object),
        },
        config: expect.any(Object),
        services: expect.any(Object),
      });
    });

    it('should not expose sensitive configuration', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health/info')
        .expect(200);

      // Assert
      const responseString = JSON.stringify(response.body);
      expect(responseString).not.toContain('password');
      expect(responseString).not.toContain('secret');
      expect(responseString).not.toContain('key');
    });
  });

  describe('Error handling', () => {
    it('should handle invalid health endpoints gracefully', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health/invalid-endpoint')
        .expect(404);

      // Assert
      expect(response.body).toHaveProperty('statusCode', 404);
    });

    it('should return proper error format', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health/invalid-endpoint')
        .expect(404);

      // Assert
      expect(response.body).toEqual({
        success: false,
        statusCode: 404,
        message: expect.any(String),
        error: expect.any(String),
        timestamp: expect.any(String),
        path: '/health/invalid-endpoint',
        method: 'GET',
        correlationId: expect.any(String),
      });
    });
  });

  describe('Performance tests', () => {
    it('should handle load testing for basic health check', async () => {
      // Arrange
      const concurrentRequests = 50;
      const requests = Array.from({ length: concurrentRequests }, () =>
        request(httpServer).get('/health')
      );

      // Act
      const startTime = Date.now();
      const responses = await Promise.all(requests);
      const duration = Date.now() - startTime;

      // Assert
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should maintain response time under load', async () => {
      // Arrange
      const requests = Array.from({ length: 20 }, async () => {
        const startTime = Date.now();
        const response = await request(httpServer).get('/health');
        const duration = Date.now() - startTime;
        return { response, duration };
      });

      // Act
      const results = await Promise.all(requests);

      // Assert
      results.forEach(({ response, duration }) => {
        expect(response.status).toBe(200);
        expect(duration).toBeLessThan(1000); // Each request should complete within 1 second
      });

      const avgDuration = results.reduce((sum, { duration }) => sum + duration, 0) / results.length;
      expect(avgDuration).toBeLessThan(500); // Average should be under 500ms
    });
  });

  describe('Headers and CORS', () => {
    it('should include proper security headers', async () => {
      // Act
      const response = await request(httpServer)
        .get('/health')
        .expect(200);

      // Assert
      expect(response.headers).toHaveProperty('x-correlation-id');
    });

    it('should handle CORS preflight requests', async () => {
      // Act
      const response = await request(httpServer)
        .options('/health')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'GET');

      // Assert
      expect([200, 204]).toContain(response.status);
    });
  });
});
