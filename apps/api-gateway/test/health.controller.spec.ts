/**
 * 🧪 Health Controller Tests - Unit Tests for Health Check Endpoints
 * 
 * Tests health check functionality with:
 * - Basic health endpoint testing
 * - Database connectivity checks
 * - Service dependency validation
 * - Performance monitoring
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as request from 'supertest';

import { HealthController } from '../src/modules/health/health.controller';
import { HealthService } from '../src/modules/health/health.service';
import { DatabaseService } from '../src/infrastructure/database/database.service';

describe('HealthController', () => {
  let app: INestApplication;
  let healthController: HealthController;
  let healthService: HealthService;
  let databaseService: DatabaseService;

  // Mock services
  const mockDatabaseService = {
    checkConnection: jest.fn(),
    getDatabaseStats: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockHealthService = {
    getHealthStatus: jest.fn(),
    getDetailedHealthStatus: jest.fn(),
    getSystemInfo: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        {
          provide: HealthService,
          useValue: mockHealthService,
        },
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    app = module.createNestApplication();
    await app.init();

    healthController = module.get<HealthController>(HealthController);
    healthService = module.get<HealthService>(HealthService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('GET /health', () => {
    it('should return basic health status', async () => {
      // Arrange
      const expectedResponse = {
        status: 'ok',
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        environment: 'test',
      };

      mockHealthService.getHealthStatus.mockResolvedValue(expectedResponse);

      // Act & Assert
      const response = await request(app.getHttpServer())
        .get('/health')
        .expect(200);

      expect(response.body).toEqual(expectedResponse);
      expect(mockHealthService.getHealthStatus).toHaveBeenCalledTimes(1);
    });

    it('should handle health check failures gracefully', async () => {
      // Arrange
      const errorResponse = {
        status: 'error',
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        error: 'Service unavailable',
      };

      mockHealthService.getHealthStatus.mockResolvedValue(errorResponse);

      // Act & Assert
      const response = await request(app.getHttpServer())
        .get('/health')
        .expect(200); // Health endpoint should always return 200

      expect(response.body.status).toBe('error');
      expect(response.body.error).toBeDefined();
    });

    it('should include correlation ID in response headers', async () => {
      // Arrange
      mockHealthService.getHealthStatus.mockResolvedValue({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: 12345,
      });

      // Act & Assert
      const response = await request(app.getHttpServer())
        .get('/health')
        .expect(200);

      expect(response.headers['x-correlation-id']).toBeDefined();
    });
  });

  describe('GET /health/detailed', () => {
    it('should return detailed health status', async () => {
      // Arrange
      const detailedResponse = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: 12345,
        services: {
          database: { status: 'ok', responseTime: 50 },
          redis: { status: 'ok', responseTime: 10 },
          aiService: { status: 'ok', responseTime: 200 },
        },
        system: {
          memory: { used: 100, total: 1000 },
          cpu: { usage: 25 },
          disk: { used: 500, total: 2000 },
        },
      };

      mockHealthService.getDetailedHealthStatus.mockResolvedValue(detailedResponse);

      // Act & Assert
      const response = await request(app.getHttpServer())
        .get('/health/detailed')
        .expect(200);

      expect(response.body).toEqual(detailedResponse);
      expect(response.body.services).toBeDefined();
      expect(response.body.system).toBeDefined();
    });

    it('should handle partial service failures', async () => {
      // Arrange
      const partialFailureResponse = {
        status: 'degraded',
        timestamp: new Date().toISOString(),
        uptime: 12345,
        services: {
          database: { status: 'ok', responseTime: 50 },
          redis: { status: 'error', error: 'Connection timeout' },
          aiService: { status: 'ok', responseTime: 200 },
        },
      };

      mockHealthService.getDetailedHealthStatus.mockResolvedValue(partialFailureResponse);

      // Act & Assert
      const response = await request(app.getHttpServer())
        .get('/health/detailed')
        .expect(200);

      expect(response.body.status).toBe('degraded');
      expect(response.body.services.redis.status).toBe('error');
    });
  });

  describe('GET /health/ready', () => {
    it('should return 200 when all services are ready', async () => {
      // Arrange
      mockDatabaseService.checkConnection.mockResolvedValue({
        isConnected: true,
        responseTime: 50,
      });

      mockHealthService.getHealthStatus.mockResolvedValue({
        status: 'ok',
      });

      // Act & Assert
      await request(app.getHttpServer())
        .get('/health/ready')
        .expect(200);
    });

    it('should return 503 when services are not ready', async () => {
      // Arrange
      mockDatabaseService.checkConnection.mockResolvedValue({
        isConnected: false,
        responseTime: 5000,
        error: 'Connection failed',
      });

      // Act & Assert
      await request(app.getHttpServer())
        .get('/health/ready')
        .expect(503);
    });
  });

  describe('GET /health/live', () => {
    it('should return 200 when application is alive', async () => {
      // Act & Assert
      await request(app.getHttpServer())
        .get('/health/live')
        .expect(200);
    });

    it('should include minimal response for liveness probe', async () => {
      // Act & Assert
      const response = await request(app.getHttpServer())
        .get('/health/live')
        .expect(200);

      expect(response.body).toEqual({
        status: 'ok',
        timestamp: expect.any(String),
      });
    });
  });

  describe('Performance Tests', () => {
    it('should respond to health check within acceptable time', async () => {
      // Arrange
      const startTime = Date.now();
      
      mockHealthService.getHealthStatus.mockResolvedValue({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: 12345,
      });

      // Act
      await request(app.getHttpServer())
        .get('/health')
        .expect(200);

      // Assert
      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(1000); // Should respond within 1 second
    });

    it('should handle concurrent health check requests', async () => {
      // Arrange
      mockHealthService.getHealthStatus.mockResolvedValue({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: 12345,
      });

      // Act
      const requests = Array(10).fill(null).map(() =>
        request(app.getHttpServer()).get('/health')
      );

      const responses = await Promise.all(requests);

      // Assert
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.status).toBe('ok');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle service exceptions gracefully', async () => {
      // Arrange
      mockHealthService.getHealthStatus.mockRejectedValue(
        new Error('Service temporarily unavailable')
      );

      // Act & Assert
      const response = await request(app.getHttpServer())
        .get('/health')
        .expect(500);

      expect(response.body.message).toContain('Health check failed');
    });

    it('should not expose sensitive information in error responses', async () => {
      // Arrange
      mockHealthService.getHealthStatus.mockRejectedValue(
        new Error('Database password: secret123')
      );

      // Act & Assert
      const response = await request(app.getHttpServer())
        .get('/health')
        .expect(500);

      expect(response.body.message).not.toContain('secret123');
      expect(response.body.message).not.toContain('password');
    });
  });
});
