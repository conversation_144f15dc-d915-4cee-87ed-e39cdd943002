/**
 * 🧪 Test Setup - Global Test Configuration
 * 
 * Provides global test setup and utilities:
 * - Environment configuration for testing
 * - Database setup and teardown
 * - Mock services and utilities
 * - Common test helpers
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { INestApplication } from '@nestjs/common';
import { ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';

// Mock external dependencies
jest.mock('ioredis');
jest.mock('typeorm');

// Global test configuration
const testConfig = {
  database: {
    host: 'localhost',
    port: 5433, // Different port for test database
    database: 'test_enterprise_db',
    username: 'test_user',
    password: 'test_password',
    synchronize: true,
    dropSchema: true,
    logging: false,
  },
  redis: {
    host: 'localhost',
    port: 6380, // Different port for test Redis
    db: 15, // Different database for tests
  },
  jwt: {
    secret: 'test-jwt-secret-key-for-testing-only',
    expiresIn: '1h',
  },
  environment: 'test',
};

// Global test utilities
export class TestUtils {
  /**
   * Create test application instance
   */
  static async createTestApp(moduleMetadata: any): Promise<INestApplication> {
    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [() => testConfig],
        }),
        ...moduleMetadata.imports,
      ],
      controllers: moduleMetadata.controllers || [],
      providers: moduleMetadata.providers || [],
    }).compile();

    const app = moduleRef.createNestApplication();
    
    // Apply global pipes
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    await app.init();
    return app;
  }

  /**
   * Create test request agent
   */
  static createTestAgent(app: INestApplication) {
    return request(app.getHttpServer());
  }

  /**
   * Generate test JWT token
   */
  static generateTestToken(payload: any = {}): string {
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      {
        userId: 'test-user-id',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read:own', 'write:own'],
        ...payload,
      },
      testConfig.jwt.secret,
      { expiresIn: testConfig.jwt.expiresIn }
    );
  }

  /**
   * Generate admin test token
   */
  static generateAdminToken(): string {
    return this.generateTestToken({
      userId: 'admin-user-id',
      email: '<EMAIL>',
      roles: ['admin'],
      permissions: ['*:*'],
    });
  }

  /**
   * Create test user data
   */
  static createTestUser(overrides: any = {}) {
    return {
      id: 'test-user-id',
      email: '<EMAIL>',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User',
      roles: ['user'],
      permissions: ['read:own', 'write:own'],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  /**
   * Create test API response
   */
  static createTestApiResponse<T>(data: T, overrides: any = {}) {
    return {
      success: true,
      data,
      timestamp: new Date().toISOString(),
      path: '/test',
      method: 'GET',
      statusCode: 200,
      correlationId: 'test-correlation-id',
      ...overrides,
    };
  }

  /**
   * Create test error response
   */
  static createTestErrorResponse(message: string, statusCode: number = 400) {
    return {
      success: false,
      statusCode,
      message,
      error: 'Test Error',
      timestamp: new Date().toISOString(),
      path: '/test',
      method: 'GET',
      correlationId: 'test-correlation-id',
    };
  }

  /**
   * Wait for specified time (for async operations)
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clean up test data
   */
  static async cleanup(): Promise<void> {
    // Clean up any test data, connections, etc.
    // This will be called after each test
  }
}

// Mock services
export class MockRedisService {
  private store = new Map<string, any>();

  async get(key: string): Promise<any> {
    return this.store.get(key);
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    this.store.set(key, value);
    if (ttl) {
      setTimeout(() => this.store.delete(key), ttl * 1000);
    }
  }

  async del(key: string): Promise<void> {
    this.store.delete(key);
  }

  async exists(key: string): Promise<boolean> {
    return this.store.has(key);
  }

  async flushall(): Promise<void> {
    this.store.clear();
  }

  async ping(): Promise<string> {
    return 'PONG';
  }
}

export class MockDatabaseService {
  private entities = new Map<string, any[]>();

  async find(entityName: string, conditions?: any): Promise<any[]> {
    const entities = this.entities.get(entityName) || [];
    if (!conditions) return entities;
    
    return entities.filter(entity => {
      return Object.keys(conditions).every(key => entity[key] === conditions[key]);
    });
  }

  async findOne(entityName: string, conditions: any): Promise<any> {
    const entities = await this.find(entityName, conditions);
    return entities[0] || null;
  }

  async save(entityName: string, entity: any): Promise<any> {
    const entities = this.entities.get(entityName) || [];
    const existingIndex = entities.findIndex(e => e.id === entity.id);
    
    if (existingIndex >= 0) {
      entities[existingIndex] = { ...entities[existingIndex], ...entity };
    } else {
      entities.push({ id: this.generateId(), ...entity });
    }
    
    this.entities.set(entityName, entities);
    return entity;
  }

  async remove(entityName: string, id: string): Promise<void> {
    const entities = this.entities.get(entityName) || [];
    const filteredEntities = entities.filter(e => e.id !== id);
    this.entities.set(entityName, filteredEntities);
  }

  async clear(entityName?: string): Promise<void> {
    if (entityName) {
      this.entities.delete(entityName);
    } else {
      this.entities.clear();
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

// Global test mocks
export const mockConfigService = {
  get: jest.fn((key: string, defaultValue?: any) => {
    const config = {
      'database.host': testConfig.database.host,
      'database.port': testConfig.database.port,
      'redis.host': testConfig.redis.host,
      'redis.port': testConfig.redis.port,
      'jwt.secret': testConfig.jwt.secret,
      'environment': testConfig.environment,
    };
    return config[key] || defaultValue;
  }),
};

export const mockJwtService = {
  sign: jest.fn((payload: any) => 'mock-jwt-token'),
  verify: jest.fn((token: string) => ({
    userId: 'test-user-id',
    email: '<EMAIL>',
    roles: ['user'],
  })),
  decode: jest.fn((token: string) => ({
    userId: 'test-user-id',
    email: '<EMAIL>',
    roles: ['user'],
  })),
};

export const mockLogger = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
};

// Global setup and teardown
beforeEach(async () => {
  // Reset all mocks before each test
  jest.clearAllMocks();
});

afterEach(async () => {
  // Clean up after each test
  await TestUtils.cleanup();
});

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for integration tests
jest.setTimeout(30000);

// Export test configuration
export { testConfig };
