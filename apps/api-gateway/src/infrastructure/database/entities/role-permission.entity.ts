/**
 * 🔐 Role Permission Entity - Many-to-Many Junction Table
 * 
 * Implements role-permission relationships with:
 * - Many-to-many relationship between roles and permissions
 * - Conditional permissions with constraints
 * - Audit trail with timestamps
 * - Permission inheritance support
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Index,
} from 'typeorm';

import { Role } from './role.entity';
import { Permission } from './permission.entity';

@Entity('role_permissions')
@Index(['roleId', 'permissionId'], { unique: true })
@Index(['roleId'])
@Index(['permissionId'])
@Index(['createdAt'])
export class RolePermission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // ================================
  // 🔗 Relationships
  // ================================

  @Column('uuid')
  roleId: string;

  @Column('uuid')
  permissionId: string;

  @ManyToOne(() => Role, role => role.rolePermissions, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'roleId' })
  role: Role;

  @ManyToOne(() => Permission, permission => permission.rolePermissions, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'permissionId' })
  permission: Permission;

  // ================================
  // 🔧 Permission Configuration
  // ================================

  @Column('boolean', { default: true })
  isGranted: boolean;

  @Column('boolean', { default: true })
  isActive: boolean;

  @Column('varchar', { length: 255, nullable: true })
  grantedBy: string;

  @Column('text', { nullable: true })
  grantReason: string;

  @Column('timestamp', { nullable: true })
  expiresAt: Date;

  // ================================
  // 🎯 Conditional Permissions
  // ================================

  @Column('jsonb', { nullable: true })
  conditions: {
    timeRestrictions?: {
      startTime?: string;
      endTime?: string;
      daysOfWeek?: number[];
      timezone?: string;
    };
    ipRestrictions?: string[];
    locationRestrictions?: string[];
    resourceFilters?: Record<string, any>;
    customConditions?: Record<string, any>;
  };

  @Column('jsonb', { nullable: true })
  constraints: {
    maxUsage?: number;
    usageWindow?: string; // e.g., 'daily', 'weekly', 'monthly'
    rateLimits?: {
      requests?: number;
      window?: string;
    };
    resourceLimits?: Record<string, any>;
  };

  // ================================
  // 📊 Usage Tracking
  // ================================

  @Column('integer', { default: 0 })
  usageCount: number;

  @Column('timestamp', { nullable: true })
  lastUsedAt: Date;

  @Column('jsonb', { nullable: true })
  usageStats: {
    dailyUsage?: Record<string, number>;
    weeklyUsage?: Record<string, number>;
    monthlyUsage?: Record<string, number>;
  };

  // ================================
  // 🔄 Inheritance
  // ================================

  @Column('boolean', { default: false })
  isInherited: boolean;

  @Column('uuid', { nullable: true })
  inheritedFromRoleId: string;

  @Column('integer', { default: 0 })
  inheritanceLevel: number;

  // ================================
  // 📝 Metadata
  // ================================

  @Column('jsonb', { nullable: true })
  metadata: {
    source?: string;
    category?: string;
    priority?: number;
    tags?: string[];
    customData?: Record<string, any>;
  };

  // ================================
  // 🕒 Timestamps
  // ================================

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @DeleteDateColumn({
    type: 'timestamp',
    nullable: true,
  })
  deletedAt: Date;

  // ================================
  // 🔧 Helper Methods
  // ================================

  /**
   * Check if the permission is currently valid
   */
  isValid(): boolean {
    return (
      this.isActive &&
      this.isGranted &&
      !this.isExpired() &&
      !this.deletedAt &&
      this.isWithinTimeRestrictions() &&
      !this.isUsageLimitExceeded()
    );
  }

  /**
   * Check if the permission is expired
   */
  isExpired(): boolean {
    if (!this.expiresAt) {
      return false;
    }
    return new Date() > this.expiresAt;
  }

  /**
   * Check if current time is within allowed time restrictions
   */
  isWithinTimeRestrictions(): boolean {
    if (!this.conditions?.timeRestrictions) {
      return true;
    }

    const now = new Date();
    const restrictions = this.conditions.timeRestrictions;

    // Check day of week restrictions
    if (restrictions.daysOfWeek && restrictions.daysOfWeek.length > 0) {
      const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      if (!restrictions.daysOfWeek.includes(currentDay)) {
        return false;
      }
    }

    // Check time of day restrictions
    if (restrictions.startTime && restrictions.endTime) {
      const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
      if (currentTime < restrictions.startTime || currentTime > restrictions.endTime) {
        return false;
      }
    }

    return true;
  }

  /**
   * Check if usage limit is exceeded
   */
  isUsageLimitExceeded(): boolean {
    if (!this.constraints?.maxUsage) {
      return false;
    }

    return this.usageCount >= this.constraints.maxUsage;
  }

  /**
   * Increment usage count
   */
  incrementUsage(): void {
    this.usageCount++;
    this.lastUsedAt = new Date();
  }

  /**
   * Check if IP address is allowed
   */
  isIpAllowed(ipAddress: string): boolean {
    if (!this.conditions?.ipRestrictions || this.conditions.ipRestrictions.length === 0) {
      return true;
    }

    return this.conditions.ipRestrictions.some(allowedIp => {
      // Simple IP matching - in production, use proper CIDR matching
      return ipAddress === allowedIp || allowedIp === '*';
    });
  }

  /**
   * Get remaining usage count
   */
  getRemainingUsage(): number | null {
    if (!this.constraints?.maxUsage) {
      return null;
    }

    return Math.max(0, this.constraints.maxUsage - this.usageCount);
  }

  /**
   * Get permission priority (higher number = higher priority)
   */
  getPriority(): number {
    return this.metadata?.priority || 0;
  }
}
