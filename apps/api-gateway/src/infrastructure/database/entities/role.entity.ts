/**
 * 🔐 Role Entity - Role-Based Access Control
 * 
 * Implements role entity with:
 * - Role hierarchy and permissions
 * - User associations
 * - Audit trails
 * - Flexible permission system
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Permission } from './permission.entity';

@Entity('roles')
@Index(['name'], { unique: true })
@Index(['isActive'])
@Index(['level'])
export class Role {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 100 })
  @Index()
  name: string;

  @Column({ length: 100 })
  displayName: string;

  @Column({ length: 500, nullable: true })
  description?: string;

  @Column({ default: true })
  @Index()
  isActive: boolean;

  @Column({ default: 0 })
  @Index()
  level: number; // Role hierarchy level (0 = highest, e.g., super admin)

  @Column({ length: 50, nullable: true })
  category?: string; // e.g., 'system', 'business', 'custom'

  @Column({ type: 'jsonb', nullable: true })
  metadata?: {
    color?: string;
    icon?: string;
    features?: string[];
    restrictions?: string[];
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  @Index()
  deletedAt?: Date;

  // Relationships
  @ManyToMany(() => User, user => user.roles)
  users: User[];

  @ManyToMany(() => Permission, permission => permission.roles, { eager: true })
  @JoinTable({
    name: 'role_permissions',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' },
  })
  permissions: Permission[];

  // Virtual properties
  get userCount(): number {
    return this.users?.length || 0;
  }

  get permissionCount(): number {
    return this.permissions?.length || 0;
  }

  get permissionNames(): string[] {
    return this.permissions?.map(p => `${p.resource}:${p.action}`) || [];
  }

  // Methods
  hasPermission(resource: string, action: string): boolean {
    const requiredPermission = `${resource}:${action}`;
    const wildcardPermission = `${resource}:*`;
    const adminPermission = '*:*';
    
    return this.permissionNames.includes(requiredPermission) ||
           this.permissionNames.includes(wildcardPermission) ||
           this.permissionNames.includes(adminPermission);
  }

  hasAnyPermission(permissions: Array<{ resource: string; action: string }>): boolean {
    return permissions.some(({ resource, action }) => 
      this.hasPermission(resource, action)
    );
  }

  hasAllPermissions(permissions: Array<{ resource: string; action: string }>): boolean {
    return permissions.every(({ resource, action }) => 
      this.hasPermission(resource, action)
    );
  }

  addPermission(permission: Permission): void {
    if (!this.permissions) {
      this.permissions = [];
    }
    
    if (!this.permissions.find(p => p.id === permission.id)) {
      this.permissions.push(permission);
    }
  }

  removePermission(permission: Permission): void {
    if (this.permissions) {
      this.permissions = this.permissions.filter(p => p.id !== permission.id);
    }
  }

  isSystemRole(): boolean {
    return this.category === 'system';
  }

  isHigherThan(otherRole: Role): boolean {
    return this.level < otherRole.level;
  }

  isLowerThan(otherRole: Role): boolean {
    return this.level > otherRole.level;
  }

  isSameLevel(otherRole: Role): boolean {
    return this.level === otherRole.level;
  }

  canManageRole(otherRole: Role): boolean {
    // Can only manage roles at lower levels
    return this.isHigherThan(otherRole) && !otherRole.isSystemRole();
  }

  softDelete(): void {
    this.deletedAt = new Date();
    this.isActive = false;
  }

  restore(): void {
    this.deletedAt = null;
    this.isActive = true;
  }

  // Static methods for common roles
  static createAdminRole(): Partial<Role> {
    return {
      name: 'admin',
      displayName: 'Administrator',
      description: 'Full system administrator with all permissions',
      level: 0,
      category: 'system',
      metadata: {
        color: '#dc2626',
        icon: 'shield-check',
        features: ['user_management', 'system_config', 'audit_logs'],
      },
    };
  }

  static createUserRole(): Partial<Role> {
    return {
      name: 'user',
      displayName: 'User',
      description: 'Standard user with basic permissions',
      level: 100,
      category: 'business',
      metadata: {
        color: '#059669',
        icon: 'user',
        features: ['profile_management', 'basic_access'],
      },
    };
  }

  static createModeratorRole(): Partial<Role> {
    return {
      name: 'moderator',
      displayName: 'Moderator',
      description: 'Content moderator with limited administrative permissions',
      level: 50,
      category: 'business',
      metadata: {
        color: '#d97706',
        icon: 'user-check',
        features: ['content_moderation', 'user_support'],
      },
    };
  }

  // Validation
  static validateName(name: string): boolean {
    // Role name should be lowercase, alphanumeric with underscores
    const nameRegex = /^[a-z0-9_]{2,50}$/;
    return nameRegex.test(name);
  }

  static validateLevel(level: number): boolean {
    return Number.isInteger(level) && level >= 0 && level <= 1000;
  }

  // Serialization
  toJSON(): Partial<Role> {
    return {
      id: this.id,
      name: this.name,
      displayName: this.displayName,
      description: this.description,
      isActive: this.isActive,
      level: this.level,
      category: this.category,
      metadata: this.metadata,
      permissionCount: this.permissionCount,
      userCount: this.userCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  toSummary(): Partial<Role> {
    return {
      id: this.id,
      name: this.name,
      displayName: this.displayName,
      level: this.level,
      category: this.category,
      isActive: this.isActive,
    };
  }
}
