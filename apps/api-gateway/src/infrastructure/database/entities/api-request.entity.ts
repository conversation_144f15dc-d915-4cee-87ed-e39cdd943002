/**
 * 📊 API Request Entity - Request Analytics and Monitoring
 * 
 * Implements API request tracking with:
 * - Request/response logging
 * - Performance metrics
 * - User activity tracking
 * - Error monitoring
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

@Entity('api_requests')
@Index(['userId'])
@Index(['endpoint'])
@Index(['method'])
@Index(['statusCode'])
@Index(['createdAt'])
@Index(['success'])
@Index(['correlationId'])
export class APIRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'correlation_id', length: 100 })
  @Index()
  correlationId: string;

  @Column({ name: 'user_id', type: 'uuid', nullable: true })
  @Index()
  userId?: string;

  @Column({ length: 500 })
  @Index()
  endpoint: string;

  @Column({ length: 10 })
  @Index()
  method: string; // GET, POST, PUT, DELETE, etc.

  @Column({ name: 'status_code' })
  @Index()
  statusCode: number;

  @Column({ default: true })
  @Index()
  success: boolean;

  @Column({ name: 'response_time' })
  responseTime: number; // in milliseconds

  @Column({ name: 'request_size', nullable: true })
  requestSize?: number; // in bytes

  @Column({ name: 'response_size', nullable: true })
  responseSize?: number; // in bytes

  @Column({ name: 'ip_address', length: 45, nullable: true })
  @Index()
  ipAddress?: string; // IPv4 or IPv6

  @Column({ name: 'user_agent', length: 1000, nullable: true })
  userAgent?: string;

  @Column({ length: 100, nullable: true })
  @Index()
  referer?: string;

  @Column({ length: 50, nullable: true })
  @Index()
  country?: string;

  @Column({ length: 100, nullable: true })
  @Index()
  city?: string;

  @Column({ type: 'jsonb', nullable: true })
  headers?: Record<string, string>;

  @Column({ type: 'jsonb', nullable: true })
  queryParams?: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  requestBody?: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  responseBody?: Record<string, any>;

  @Column({ name: 'error_message', length: 1000, nullable: true })
  errorMessage?: string;

  @Column({ name: 'error_stack', type: 'text', nullable: true })
  errorStack?: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: {
    apiVersion?: string;
    clientVersion?: string;
    deviceType?: 'mobile' | 'desktop' | 'tablet' | 'bot';
    browser?: string;
    os?: string;
    cached?: boolean;
    rateLimited?: boolean;
    authenticated?: boolean;
    roles?: string[];
    permissions?: string[];
  };

  @CreateDateColumn({ name: 'created_at' })
  @Index()
  createdAt: Date;

  // Relationships
  @ManyToOne(() => User, user => user.apiRequests, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  // Virtual properties
  get isError(): boolean {
    return this.statusCode >= 400;
  }

  get isServerError(): boolean {
    return this.statusCode >= 500;
  }

  get isClientError(): boolean {
    return this.statusCode >= 400 && this.statusCode < 500;
  }

  get isSlowRequest(): boolean {
    return this.responseTime > 1000; // Slower than 1 second
  }

  get performanceCategory(): 'fast' | 'normal' | 'slow' | 'very_slow' {
    if (this.responseTime < 100) return 'fast';
    if (this.responseTime < 500) return 'normal';
    if (this.responseTime < 2000) return 'slow';
    return 'very_slow';
  }

  get httpMethodCategory(): 'read' | 'write' | 'delete' | 'other' {
    switch (this.method.toUpperCase()) {
      case 'GET':
      case 'HEAD':
      case 'OPTIONS':
        return 'read';
      case 'POST':
      case 'PUT':
      case 'PATCH':
        return 'write';
      case 'DELETE':
        return 'delete';
      default:
        return 'other';
    }
  }

  get sizeCategory(): 'small' | 'medium' | 'large' | 'very_large' {
    const totalSize = (this.requestSize || 0) + (this.responseSize || 0);
    if (totalSize < 1024) return 'small'; // < 1KB
    if (totalSize < 10240) return 'medium'; // < 10KB
    if (totalSize < 102400) return 'large'; // < 100KB
    return 'very_large'; // >= 100KB
  }

  // Methods
  isFromBot(): boolean {
    if (!this.userAgent) return false;
    
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /googlebot/i, /bingbot/i, /slurp/i, /duckduckbot/i,
      /facebookexternalhit/i, /twitterbot/i, /linkedinbot/i,
    ];
    
    return botPatterns.some(pattern => pattern.test(this.userAgent));
  }

  isFromMobile(): boolean {
    return this.metadata?.deviceType === 'mobile';
  }

  isAuthenticated(): boolean {
    return this.metadata?.authenticated === true;
  }

  wasRateLimited(): boolean {
    return this.metadata?.rateLimited === true;
  }

  wasCached(): boolean {
    return this.metadata?.cached === true;
  }

  hasRole(roleName: string): boolean {
    return this.metadata?.roles?.includes(roleName) || false;
  }

  hasPermission(permission: string): boolean {
    return this.metadata?.permissions?.includes(permission) || false;
  }

  // Static analysis methods
  static getPopularEndpoints(requests: APIRequest[], limit: number = 10): Array<{
    endpoint: string;
    count: number;
    avgResponseTime: number;
    errorRate: number;
  }> {
    const endpointStats = new Map();
    
    requests.forEach(req => {
      const key = `${req.method} ${req.endpoint}`;
      if (!endpointStats.has(key)) {
        endpointStats.set(key, {
          endpoint: key,
          count: 0,
          totalResponseTime: 0,
          errorCount: 0,
        });
      }
      
      const stats = endpointStats.get(key);
      stats.count++;
      stats.totalResponseTime += req.responseTime;
      if (req.isError) stats.errorCount++;
    });
    
    return Array.from(endpointStats.values())
      .map(stats => ({
        endpoint: stats.endpoint,
        count: stats.count,
        avgResponseTime: Math.round(stats.totalResponseTime / stats.count),
        errorRate: Math.round((stats.errorCount / stats.count) * 100),
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  static getErrorAnalysis(requests: APIRequest[]): {
    totalErrors: number;
    errorRate: number;
    errorsByStatus: Record<number, number>;
    commonErrors: Array<{ message: string; count: number }>;
  } {
    const errors = requests.filter(req => req.isError);
    const errorsByStatus = {};
    const errorMessages = new Map();
    
    errors.forEach(req => {
      // Count by status code
      errorsByStatus[req.statusCode] = (errorsByStatus[req.statusCode] || 0) + 1;
      
      // Count error messages
      if (req.errorMessage) {
        const count = errorMessages.get(req.errorMessage) || 0;
        errorMessages.set(req.errorMessage, count + 1);
      }
    });
    
    const commonErrors = Array.from(errorMessages.entries())
      .map(([message, count]) => ({ message, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
    
    return {
      totalErrors: errors.length,
      errorRate: Math.round((errors.length / requests.length) * 100),
      errorsByStatus,
      commonErrors,
    };
  }

  static getPerformanceMetrics(requests: APIRequest[]): {
    avgResponseTime: number;
    medianResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    slowRequestCount: number;
    slowRequestRate: number;
  } {
    const responseTimes = requests.map(req => req.responseTime).sort((a, b) => a - b);
    const slowRequests = requests.filter(req => req.isSlowRequest);
    
    const percentile = (arr: number[], p: number) => {
      const index = Math.ceil(arr.length * p) - 1;
      return arr[index] || 0;
    };
    
    return {
      avgResponseTime: Math.round(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length),
      medianResponseTime: percentile(responseTimes, 0.5),
      p95ResponseTime: percentile(responseTimes, 0.95),
      p99ResponseTime: percentile(responseTimes, 0.99),
      slowRequestCount: slowRequests.length,
      slowRequestRate: Math.round((slowRequests.length / requests.length) * 100),
    };
  }

  // Validation
  static validateEndpoint(endpoint: string): boolean {
    return endpoint.length > 0 && endpoint.length <= 500;
  }

  static validateMethod(method: string): boolean {
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
    return validMethods.includes(method.toUpperCase());
  }

  static validateStatusCode(statusCode: number): boolean {
    return Number.isInteger(statusCode) && statusCode >= 100 && statusCode < 600;
  }

  // Serialization
  toJSON(): Partial<APIRequest> {
    return {
      id: this.id,
      correlationId: this.correlationId,
      userId: this.userId,
      endpoint: this.endpoint,
      method: this.method,
      statusCode: this.statusCode,
      success: this.success,
      responseTime: this.responseTime,
      requestSize: this.requestSize,
      responseSize: this.responseSize,
      ipAddress: this.ipAddress,
      performanceCategory: this.performanceCategory,
      httpMethodCategory: this.httpMethodCategory,
      sizeCategory: this.sizeCategory,
      isError: this.isError,
      isSlowRequest: this.isSlowRequest,
      createdAt: this.createdAt,
    };
  }

  toSummary(): Partial<APIRequest> {
    return {
      id: this.id,
      endpoint: this.endpoint,
      method: this.method,
      statusCode: this.statusCode,
      responseTime: this.responseTime,
      success: this.success,
      createdAt: this.createdAt,
    };
  }
}
