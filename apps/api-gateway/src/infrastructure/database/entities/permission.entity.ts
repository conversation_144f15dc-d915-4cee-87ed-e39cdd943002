/**
 * 🔑 Permission Entity - Fine-grained Access Control
 * 
 * Implements permission entity with:
 * - Resource-action based permissions
 * - Hierarchical permission structure
 * - Role associations
 * - Flexible permission system
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  Index,
  Unique,
} from 'typeorm';
import { Role } from './role.entity';

@Entity('permissions')
@Unique(['resource', 'action'])
@Index(['resource'])
@Index(['action'])
@Index(['category'])
@Index(['isActive'])
export class Permission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  @Index()
  name: string; // e.g., 'read_users', 'write_posts'

  @Column({ length: 100 })
  displayName: string; // e.g., 'Read Users', 'Write Posts'

  @Column({ length: 100 })
  @Index()
  resource: string; // e.g., 'users', 'posts', 'settings'

  @Column({ length: 50 })
  @Index()
  action: string; // e.g., 'read', 'write', 'delete', 'admin'

  @Column({ length: 500, nullable: true })
  description?: string;

  @Column({ length: 50, nullable: true })
  @Index()
  category?: string; // e.g., 'user_management', 'content', 'system'

  @Column({ default: true })
  @Index()
  isActive: boolean;

  @Column({ default: 0 })
  priority: number; // Higher priority permissions override lower ones

  @Column({ type: 'jsonb', nullable: true })
  conditions?: {
    // Conditional permissions based on context
    timeRestrictions?: {
      startTime?: string;
      endTime?: string;
      daysOfWeek?: number[];
    };
    ipRestrictions?: string[];
    locationRestrictions?: string[];
    resourceFilters?: Record<string, any>;
  };

  @Column({ type: 'jsonb', nullable: true })
  metadata?: {
    icon?: string;
    color?: string;
    group?: string;
    dependencies?: string[]; // Other permissions this depends on
    conflicts?: string[]; // Permissions that conflict with this one
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  @Index()
  deletedAt?: Date;

  // Relationships
  @ManyToMany(() => Role, role => role.permissions)
  roles: Role[];

  // Virtual properties
  get fullName(): string {
    return `${this.resource}:${this.action}`;
  }

  get roleCount(): number {
    return this.roles?.length || 0;
  }

  // Methods
  isWildcard(): boolean {
    return this.action === '*' || this.resource === '*';
  }

  isAdminPermission(): boolean {
    return this.resource === '*' && this.action === '*';
  }

  matches(resource: string, action: string): boolean {
    const resourceMatch = this.resource === '*' || this.resource === resource;
    const actionMatch = this.action === '*' || this.action === action;
    return resourceMatch && actionMatch;
  }

  isHigherPriorityThan(other: Permission): boolean {
    return this.priority > other.priority;
  }

  hasTimeRestrictions(): boolean {
    return !!(this.conditions?.timeRestrictions);
  }

  hasIpRestrictions(): boolean {
    return !!(this.conditions?.ipRestrictions?.length);
  }

  hasLocationRestrictions(): boolean {
    return !!(this.conditions?.locationRestrictions?.length);
  }

  isValidAtTime(date: Date = new Date()): boolean {
    if (!this.hasTimeRestrictions()) return true;

    const restrictions = this.conditions.timeRestrictions;
    
    // Check day of week
    if (restrictions.daysOfWeek?.length) {
      const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
      if (!restrictions.daysOfWeek.includes(dayOfWeek)) {
        return false;
      }
    }

    // Check time range
    if (restrictions.startTime && restrictions.endTime) {
      const currentTime = date.toTimeString().slice(0, 5); // HH:MM format
      if (currentTime < restrictions.startTime || currentTime > restrictions.endTime) {
        return false;
      }
    }

    return true;
  }

  isValidForIp(ipAddress: string): boolean {
    if (!this.hasIpRestrictions()) return true;
    
    return this.conditions.ipRestrictions.some(allowedIp => {
      // Simple IP matching - in production, you'd want CIDR support
      return allowedIp === ipAddress || allowedIp === '*';
    });
  }

  hasDependencies(): boolean {
    return !!(this.metadata?.dependencies?.length);
  }

  hasConflicts(): boolean {
    return !!(this.metadata?.conflicts?.length);
  }

  getDependencies(): string[] {
    return this.metadata?.dependencies || [];
  }

  getConflicts(): string[] {
    return this.metadata?.conflicts || [];
  }

  softDelete(): void {
    this.deletedAt = new Date();
    this.isActive = false;
  }

  restore(): void {
    this.deletedAt = null;
    this.isActive = true;
  }

  // Static factory methods for common permissions
  static createReadPermission(resource: string): Partial<Permission> {
    return {
      name: `read_${resource}`,
      displayName: `Read ${resource.charAt(0).toUpperCase() + resource.slice(1)}`,
      resource,
      action: 'read',
      description: `Permission to read ${resource}`,
      category: 'read',
      priority: 10,
    };
  }

  static createWritePermission(resource: string): Partial<Permission> {
    return {
      name: `write_${resource}`,
      displayName: `Write ${resource.charAt(0).toUpperCase() + resource.slice(1)}`,
      resource,
      action: 'write',
      description: `Permission to create and update ${resource}`,
      category: 'write',
      priority: 20,
    };
  }

  static createDeletePermission(resource: string): Partial<Permission> {
    return {
      name: `delete_${resource}`,
      displayName: `Delete ${resource.charAt(0).toUpperCase() + resource.slice(1)}`,
      resource,
      action: 'delete',
      description: `Permission to delete ${resource}`,
      category: 'delete',
      priority: 30,
    };
  }

  static createAdminPermission(resource: string): Partial<Permission> {
    return {
      name: `admin_${resource}`,
      displayName: `Admin ${resource.charAt(0).toUpperCase() + resource.slice(1)}`,
      resource,
      action: 'admin',
      description: `Full administrative access to ${resource}`,
      category: 'admin',
      priority: 100,
    };
  }

  static createSuperAdminPermission(): Partial<Permission> {
    return {
      name: 'super_admin',
      displayName: 'Super Administrator',
      resource: '*',
      action: '*',
      description: 'Full system access - all resources and actions',
      category: 'system',
      priority: 1000,
      metadata: {
        icon: 'shield-check',
        color: '#dc2626',
        group: 'system',
      },
    };
  }

  // Common permission sets
  static createCRUDPermissions(resource: string): Partial<Permission>[] {
    return [
      this.createReadPermission(resource),
      this.createWritePermission(resource),
      this.createDeletePermission(resource),
    ];
  }

  static createUserManagementPermissions(): Partial<Permission>[] {
    return [
      ...this.createCRUDPermissions('users'),
      ...this.createCRUDPermissions('roles'),
      ...this.createCRUDPermissions('permissions'),
      {
        name: 'manage_user_roles',
        displayName: 'Manage User Roles',
        resource: 'users',
        action: 'manage_roles',
        description: 'Permission to assign and remove roles from users',
        category: 'user_management',
        priority: 50,
      },
    ];
  }

  static createContentPermissions(): Partial<Permission>[] {
    return [
      ...this.createCRUDPermissions('posts'),
      ...this.createCRUDPermissions('comments'),
      {
        name: 'moderate_content',
        displayName: 'Moderate Content',
        resource: 'content',
        action: 'moderate',
        description: 'Permission to moderate user-generated content',
        category: 'content',
        priority: 40,
      },
    ];
  }

  // Validation
  static validateResourceAction(resource: string, action: string): boolean {
    const resourceRegex = /^[a-z_*][a-z0-9_*]*$/;
    const actionRegex = /^[a-z_*][a-z0-9_*]*$/;
    return resourceRegex.test(resource) && actionRegex.test(action);
  }

  static validateName(name: string): boolean {
    const nameRegex = /^[a-z0-9_]{2,100}$/;
    return nameRegex.test(name);
  }

  // Serialization
  toJSON(): Partial<Permission> {
    return {
      id: this.id,
      name: this.name,
      displayName: this.displayName,
      resource: this.resource,
      action: this.action,
      description: this.description,
      category: this.category,
      isActive: this.isActive,
      priority: this.priority,
      fullName: this.fullName,
      roleCount: this.roleCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  toSummary(): Partial<Permission> {
    return {
      id: this.id,
      name: this.name,
      displayName: this.displayName,
      fullName: this.fullName,
      category: this.category,
      isActive: this.isActive,
    };
  }
}
