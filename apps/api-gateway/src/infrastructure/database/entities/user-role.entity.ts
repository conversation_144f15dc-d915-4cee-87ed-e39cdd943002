/**
 * 👥 User Role Entity - Many-to-Many Junction Table
 * 
 * Implements user-role relationships with:
 * - Many-to-many relationship between users and roles
 * - Audit trail with timestamps
 * - Soft delete capability
 * - Additional metadata support
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Index,
} from 'typeorm';

import { User } from './user.entity';
import { Role } from './role.entity';

@Entity('user_roles')
@Index(['userId', 'roleId'], { unique: true })
@Index(['userId'])
@Index(['roleId'])
@Index(['createdAt'])
export class UserRole {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // ================================
  // 🔗 Relationships
  // ================================

  @Column('uuid')
  userId: string;

  @Column('uuid')
  roleId: string;

  @ManyToOne(() => User, user => user.userRoles, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Role, role => role.userRoles, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'roleId' })
  role: Role;

  // ================================
  // 📊 Metadata
  // ================================

  @Column('varchar', { length: 255, nullable: true })
  assignedBy: string;

  @Column('text', { nullable: true })
  assignmentReason: string;

  @Column('timestamp', { nullable: true })
  expiresAt: Date;

  @Column('boolean', { default: true })
  isActive: boolean;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  // ================================
  // 🕒 Timestamps
  // ================================

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @DeleteDateColumn({
    type: 'timestamp',
    nullable: true,
  })
  deletedAt: Date;

  // ================================
  // 🔧 Helper Methods
  // ================================

  /**
   * Check if the role assignment is expired
   */
  isExpired(): boolean {
    if (!this.expiresAt) {
      return false;
    }
    return new Date() > this.expiresAt;
  }

  /**
   * Check if the role assignment is currently valid
   */
  isValid(): boolean {
    return this.isActive && !this.isExpired() && !this.deletedAt;
  }

  /**
   * Get assignment duration in days
   */
  getAssignmentDuration(): number | null {
    if (!this.expiresAt) {
      return null;
    }
    const diffTime = this.expiresAt.getTime() - this.createdAt.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Get remaining days until expiration
   */
  getRemainingDays(): number | null {
    if (!this.expiresAt) {
      return null;
    }
    const diffTime = this.expiresAt.getTime() - new Date().getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
