/**
 * 🌱 Seed Data Migration - Initial System Data
 * 
 * Creates essential system data:
 * - Default permissions for all resources
 * - System roles (admin, user, moderator)
 * - Default admin user
 * - Role-permission assignments
 */

import { MigrationInterface, QueryRunner } from 'typeorm';
import * as bcrypt from 'bcrypt';

export class SeedData1703002000000 implements MigrationInterface {
  name = 'SeedData1703002000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Insert default permissions
    await this.insertPermissions(queryRunner);
    
    // Insert default roles
    await this.insertRoles(queryRunner);
    
    // Assign permissions to roles
    await this.assignPermissionsToRoles(queryRunner);
    
    // Create default admin user
    await this.createDefaultAdmin(queryRunner);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove all seed data
    await queryRunner.query('DELETE FROM user_roles');
    await queryRunner.query('DELETE FROM role_permissions');
    await queryRunner.query('DELETE FROM users');
    await queryRunner.query('DELETE FROM roles');
    await queryRunner.query('DELETE FROM permissions');
  }

  private async insertPermissions(queryRunner: QueryRunner): Promise<void> {
    const permissions = [
      // System permissions
      {
        name: 'super_admin',
        display_name: 'Super Administrator',
        resource: '*',
        action: '*',
        description: 'Full system access - all resources and actions',
        category: 'system',
        priority: 1000,
      },
      
      // User management permissions
      {
        name: 'read_users',
        display_name: 'Read Users',
        resource: 'users',
        action: 'read',
        description: 'Permission to view user information',
        category: 'user_management',
        priority: 10,
      },
      {
        name: 'write_users',
        display_name: 'Write Users',
        resource: 'users',
        action: 'write',
        description: 'Permission to create and update users',
        category: 'user_management',
        priority: 20,
      },
      {
        name: 'delete_users',
        display_name: 'Delete Users',
        resource: 'users',
        action: 'delete',
        description: 'Permission to delete users',
        category: 'user_management',
        priority: 30,
      },
      {
        name: 'admin_users',
        display_name: 'Admin Users',
        resource: 'users',
        action: 'admin',
        description: 'Full administrative access to users',
        category: 'user_management',
        priority: 100,
      },
      
      // Role management permissions
      {
        name: 'read_roles',
        display_name: 'Read Roles',
        resource: 'roles',
        action: 'read',
        description: 'Permission to view roles',
        category: 'user_management',
        priority: 10,
      },
      {
        name: 'write_roles',
        display_name: 'Write Roles',
        resource: 'roles',
        action: 'write',
        description: 'Permission to create and update roles',
        category: 'user_management',
        priority: 20,
      },
      {
        name: 'delete_roles',
        display_name: 'Delete Roles',
        resource: 'roles',
        action: 'delete',
        description: 'Permission to delete roles',
        category: 'user_management',
        priority: 30,
      },
      
      // Permission management permissions
      {
        name: 'read_permissions',
        display_name: 'Read Permissions',
        resource: 'permissions',
        action: 'read',
        description: 'Permission to view permissions',
        category: 'user_management',
        priority: 10,
      },
      {
        name: 'write_permissions',
        display_name: 'Write Permissions',
        resource: 'permissions',
        action: 'write',
        description: 'Permission to create and update permissions',
        category: 'user_management',
        priority: 20,
      },
      
      // AI service permissions
      {
        name: 'ai_embeddings',
        display_name: 'AI Embeddings',
        resource: 'ai',
        action: 'embeddings',
        description: 'Permission to generate text embeddings',
        category: 'ai',
        priority: 10,
      },
      {
        name: 'ai_chat',
        display_name: 'AI Chat',
        resource: 'ai',
        action: 'chat',
        description: 'Permission to use AI chat completions',
        category: 'ai',
        priority: 10,
      },
      {
        name: 'ai_search',
        display_name: 'AI Search',
        resource: 'ai',
        action: 'search',
        description: 'Permission to perform similarity search',
        category: 'ai',
        priority: 10,
      },
      {
        name: 'ai_analysis',
        display_name: 'AI Analysis',
        resource: 'ai',
        action: 'analysis',
        description: 'Permission to analyze algorithms',
        category: 'ai',
        priority: 10,
      },
      {
        name: 'ai_read',
        display_name: 'AI Read',
        resource: 'ai',
        action: 'read',
        description: 'Permission to read AI service information',
        category: 'ai',
        priority: 5,
      },
      {
        name: 'ai_usage',
        display_name: 'AI Usage',
        resource: 'ai',
        action: 'usage',
        description: 'Permission to view AI usage statistics',
        category: 'ai',
        priority: 5,
      },
      {
        name: 'ai_health',
        display_name: 'AI Health',
        resource: 'ai',
        action: 'health',
        description: 'Permission to check AI service health',
        category: 'ai',
        priority: 5,
      },
      
      // Profile permissions
      {
        name: 'read_own_profile',
        display_name: 'Read Own Profile',
        resource: 'profile',
        action: 'read',
        description: 'Permission to read own profile',
        category: 'profile',
        priority: 10,
      },
      {
        name: 'write_own_profile',
        display_name: 'Write Own Profile',
        resource: 'profile',
        action: 'write',
        description: 'Permission to update own profile',
        category: 'profile',
        priority: 10,
      },
    ];

    for (const permission of permissions) {
      await queryRunner.query(
        `INSERT INTO permissions (name, display_name, resource, action, description, category, priority, is_active, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [
          permission.name,
          permission.display_name,
          permission.resource,
          permission.action,
          permission.description,
          permission.category,
          permission.priority,
        ],
      );
    }
  }

  private async insertRoles(queryRunner: QueryRunner): Promise<void> {
    const roles = [
      {
        name: 'super_admin',
        display_name: 'Super Administrator',
        description: 'Full system administrator with all permissions',
        level: 0,
        category: 'system',
        metadata: JSON.stringify({
          color: '#dc2626',
          icon: 'shield-check',
          features: ['user_management', 'system_config', 'audit_logs', 'ai_services'],
        }),
      },
      {
        name: 'admin',
        display_name: 'Administrator',
        description: 'System administrator with most permissions',
        level: 10,
        category: 'system',
        metadata: JSON.stringify({
          color: '#ea580c',
          icon: 'shield',
          features: ['user_management', 'ai_services'],
        }),
      },
      {
        name: 'moderator',
        display_name: 'Moderator',
        description: 'Content moderator with limited administrative permissions',
        level: 50,
        category: 'business',
        metadata: JSON.stringify({
          color: '#d97706',
          icon: 'user-check',
          features: ['content_moderation', 'user_support'],
        }),
      },
      {
        name: 'user',
        display_name: 'User',
        description: 'Standard user with basic permissions',
        level: 100,
        category: 'business',
        metadata: JSON.stringify({
          color: '#059669',
          icon: 'user',
          features: ['profile_management', 'basic_ai_access'],
        }),
      },
    ];

    for (const role of roles) {
      await queryRunner.query(
        `INSERT INTO roles (name, display_name, description, level, category, metadata, is_active, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [
          role.name,
          role.display_name,
          role.description,
          role.level,
          role.category,
          role.metadata,
        ],
      );
    }
  }

  private async assignPermissionsToRoles(queryRunner: QueryRunner): Promise<void> {
    // Super admin gets all permissions
    await queryRunner.query(`
      INSERT INTO role_permissions (role_id, permission_id)
      SELECT r.id, p.id
      FROM roles r, permissions p
      WHERE r.name = 'super_admin'
    `);

    // Admin gets most permissions except super admin
    const adminPermissions = [
      'read_users', 'write_users', 'admin_users',
      'read_roles', 'write_roles',
      'read_permissions',
      'ai_embeddings', 'ai_chat', 'ai_search', 'ai_analysis', 'ai_read', 'ai_usage', 'ai_health',
      'read_own_profile', 'write_own_profile',
    ];

    for (const permissionName of adminPermissions) {
      await queryRunner.query(`
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'admin' AND p.name = $1
      `, [permissionName]);
    }

    // Moderator gets limited permissions
    const moderatorPermissions = [
      'read_users',
      'ai_read', 'ai_usage', 'ai_health',
      'read_own_profile', 'write_own_profile',
    ];

    for (const permissionName of moderatorPermissions) {
      await queryRunner.query(`
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'moderator' AND p.name = $1
      `, [permissionName]);
    }

    // User gets basic permissions
    const userPermissions = [
      'ai_embeddings', 'ai_chat', 'ai_search', 'ai_analysis', 'ai_read', 'ai_usage',
      'read_own_profile', 'write_own_profile',
    ];

    for (const permissionName of userPermissions) {
      await queryRunner.query(`
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'user' AND p.name = $1
      `, [permissionName]);
    }
  }

  private async createDefaultAdmin(queryRunner: QueryRunner): Promise<void> {
    // Create default admin user
    const hashedPassword = await bcrypt.hash('Admin123!', 12);
    
    await queryRunner.query(
      `INSERT INTO users (email, username, password, first_name, last_name, is_active, is_verified, email_verified_at, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [
        '<EMAIL>',
        'admin',
        hashedPassword,
        'System',
        'Administrator',
      ],
    );

    // Assign super_admin role to the default admin user
    await queryRunner.query(`
      INSERT INTO user_roles (user_id, role_id)
      SELECT u.id, r.id
      FROM users u, roles r
      WHERE u.username = 'admin' AND r.name = 'super_admin'
    `);
  }
}
