/**
 * 🗄️ Database Migration: User Roles and Permissions Schema
 * 
 * Advanced RBAC (Role-Based Access Control) schema:
 * - Roles management
 * - Fine-grained permissions
 * - User-role associations
 * - Permission inheritance
 */

import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreateUserRolesAndPermissions1709520000000 implements MigrationInterface {
  name = 'CreateUserRolesAndPermissions1709520000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create permissions table
    await queryRunner.createTable(
      new Table({
        name: 'permissions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            isUnique: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'resource',
            type: 'varchar',
            length: '50',
            comment: 'Resource being protected (e.g., users, posts, admin)',
          },
          {
            name: 'action',
            type: 'varchar',
            length: '50',
            comment: 'Action being performed (e.g., create, read, update, delete)',
          },
          {
            name: 'conditions',
            type: 'jsonb',
            isNullable: true,
            comment: 'Additional conditions for permission (e.g., own_resource_only)',
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          new Index('IDX_permissions_resource_action', ['resource', 'action']),
          new Index('IDX_permissions_is_active', ['is_active']),
        ],
      }),
      true
    );

    // Create roles table
    await queryRunner.createTable(
      new Table({
        name: 'roles',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '50',
            isUnique: true,
          },
          {
            name: 'display_name',
            type: 'varchar',
            length: '100',
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'level',
            type: 'integer',
            default: 0,
            comment: 'Hierarchy level (0=lowest, higher numbers = higher privileges)',
          },
          {
            name: 'is_system_role',
            type: 'boolean',
            default: false,
            comment: 'Whether this is a system-defined role that cannot be deleted',
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          new Index('IDX_roles_level', ['level']),
          new Index('IDX_roles_is_active', ['is_active']),
        ],
      }),
      true
    );

    // Create role_permissions junction table
    await queryRunner.createTable(
      new Table({
        name: 'role_permissions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'role_id',
            type: 'uuid',
          },
          {
            name: 'permission_id',
            type: 'uuid',
          },
          {
            name: 'granted_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'granted_by',
            type: 'uuid',
            isNullable: true,
            comment: 'User ID who granted this permission',
          },
        ],
        indices: [
          new Index('IDX_role_permissions_role_id', ['role_id']),
          new Index('IDX_role_permissions_permission_id', ['permission_id']),
          new Index('IDX_role_permissions_unique', ['role_id', 'permission_id'], { isUnique: true }),
        ],
      }),
      true
    );

    // Create user_roles junction table
    await queryRunner.createTable(
      new Table({
        name: 'user_roles',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'user_id',
            type: 'uuid',
          },
          {
            name: 'role_id',
            type: 'uuid',
          },
          {
            name: 'assigned_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'assigned_by',
            type: 'uuid',
            isNullable: true,
            comment: 'User ID who assigned this role',
          },
          {
            name: 'expires_at',
            type: 'timestamp with time zone',
            isNullable: true,
            comment: 'Optional expiration date for temporary roles',
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
        ],
        indices: [
          new Index('IDX_user_roles_user_id', ['user_id']),
          new Index('IDX_user_roles_role_id', ['role_id']),
          new Index('IDX_user_roles_unique', ['user_id', 'role_id'], { isUnique: true }),
          new Index('IDX_user_roles_expires_at', ['expires_at']),
          new Index('IDX_user_roles_is_active', ['is_active']),
        ],
      }),
      true
    );

    // Add foreign key constraints
    await queryRunner.createForeignKey(
      'role_permissions',
      new ForeignKey({
        columnNames: ['role_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'roles',
        onDelete: 'CASCADE',
        name: 'FK_role_permissions_role_id',
      })
    );

    await queryRunner.createForeignKey(
      'role_permissions',
      new ForeignKey({
        columnNames: ['permission_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'permissions',
        onDelete: 'CASCADE',
        name: 'FK_role_permissions_permission_id',
      })
    );

    await queryRunner.createForeignKey(
      'role_permissions',
      new ForeignKey({
        columnNames: ['granted_by'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        name: 'FK_role_permissions_granted_by',
      })
    );

    await queryRunner.createForeignKey(
      'user_roles',
      new ForeignKey({
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
        name: 'FK_user_roles_user_id',
      })
    );

    await queryRunner.createForeignKey(
      'user_roles',
      new ForeignKey({
        columnNames: ['role_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'roles',
        onDelete: 'CASCADE',
        name: 'FK_user_roles_role_id',
      })
    );

    await queryRunner.createForeignKey(
      'user_roles',
      new ForeignKey({
        columnNames: ['assigned_by'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        name: 'FK_user_roles_assigned_by',
      })
    );

    // Insert default permissions
    await queryRunner.query(`
      INSERT INTO permissions (name, description, resource, action) VALUES
      ('read:own_profile', 'Read own user profile', 'users', 'read'),
      ('update:own_profile', 'Update own user profile', 'users', 'update'),
      ('read:users', 'Read all user profiles', 'users', 'read'),
      ('create:users', 'Create new users', 'users', 'create'),
      ('update:users', 'Update any user profile', 'users', 'update'),
      ('delete:users', 'Delete users', 'users', 'delete'),
      ('read:roles', 'Read role information', 'roles', 'read'),
      ('manage:roles', 'Manage roles and permissions', 'roles', 'manage'),
      ('read:admin', 'Access admin dashboard', 'admin', 'read'),
      ('manage:admin', 'Full admin management', 'admin', 'manage'),
      ('read:api_logs', 'View API request logs', 'logs', 'read'),
      ('manage:api_logs', 'Manage API request logs', 'logs', 'manage');
    `);

    // Insert default roles
    await queryRunner.query(`
      INSERT INTO roles (name, display_name, description, level, is_system_role) VALUES
      ('user', 'User', 'Standard user role with basic permissions', 0, true),
      ('moderator', 'Moderator', 'Moderator role with elevated permissions', 50, true),
      ('admin', 'Administrator', 'Administrator role with full system access', 100, true),
      ('super_admin', 'Super Administrator', 'Super administrator with system-level access', 200, true);
    `);

    // Assign permissions to roles
    await queryRunner.query(`
      INSERT INTO role_permissions (role_id, permission_id)
      SELECT r.id, p.id
      FROM roles r, permissions p
      WHERE (r.name = 'user' AND p.name IN ('read:own_profile', 'update:own_profile'))
         OR (r.name = 'moderator' AND p.name IN ('read:own_profile', 'update:own_profile', 'read:users'))
         OR (r.name = 'admin' AND p.name NOT IN ('manage:admin'))
         OR (r.name = 'super_admin');
    `);

    // Update existing users to have the 'user' role by default
    await queryRunner.query(`
      INSERT INTO user_roles (user_id, role_id)
      SELECT u.id, r.id
      FROM users u, roles r
      WHERE r.name = 'user'
      AND NOT EXISTS (SELECT 1 FROM user_roles ur WHERE ur.user_id = u.id);
    `);

    // Create function to automatically assign default role to new users
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION assign_default_user_role()
      RETURNS TRIGGER AS $$
      BEGIN
        INSERT INTO user_roles (user_id, role_id)
        SELECT NEW.id, r.id
        FROM roles r
        WHERE r.name = 'user'
        ON CONFLICT (user_id, role_id) DO NOTHING;
        
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create trigger to automatically assign default role
    await queryRunner.query(`
      CREATE TRIGGER trigger_assign_default_user_role
      AFTER INSERT ON users
      FOR EACH ROW
      EXECUTE FUNCTION assign_default_user_role();
    `);

    // Create function to check if user has permission
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION user_has_permission(
        input_user_id UUID,
        input_permission_name VARCHAR
      )
      RETURNS BOOLEAN AS $$
      BEGIN
        RETURN EXISTS (
          SELECT 1
          FROM user_roles ur
          JOIN roles r ON ur.role_id = r.id
          JOIN role_permissions rp ON r.id = rp.role_id
          JOIN permissions p ON rp.permission_id = p.id
          WHERE ur.user_id = input_user_id
            AND ur.is_active = true
            AND (ur.expires_at IS NULL OR ur.expires_at > CURRENT_TIMESTAMP)
            AND r.is_active = true
            AND p.name = input_permission_name
            AND p.is_active = true
        );
      END;
      $$ LANGUAGE plpgsql;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop triggers and functions
    await queryRunner.query('DROP TRIGGER IF EXISTS trigger_assign_default_user_role ON users;');
    await queryRunner.query('DROP FUNCTION IF EXISTS assign_default_user_role();');
    await queryRunner.query('DROP FUNCTION IF EXISTS user_has_permission(UUID, VARCHAR);');

    // Drop foreign keys
    await queryRunner.dropForeignKey('user_roles', 'FK_user_roles_assigned_by');
    await queryRunner.dropForeignKey('user_roles', 'FK_user_roles_role_id');
    await queryRunner.dropForeignKey('user_roles', 'FK_user_roles_user_id');
    await queryRunner.dropForeignKey('role_permissions', 'FK_role_permissions_granted_by');
    await queryRunner.dropForeignKey('role_permissions', 'FK_role_permissions_permission_id');
    await queryRunner.dropForeignKey('role_permissions', 'FK_role_permissions_role_id');

    // Drop tables
    await queryRunner.dropTable('user_roles');
    await queryRunner.dropTable('role_permissions');
    await queryRunner.dropTable('roles');
    await queryRunner.dropTable('permissions');
  }
}