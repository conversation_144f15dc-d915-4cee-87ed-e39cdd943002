/**
 * 🔍 Database Migration: Audit Trail and Session Management
 * 
 * Advanced enterprise features:
 * - Comprehensive audit logging
 * - Session management
 * - Data change tracking
 * - Security event logging
 */

import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreateAuditAndSessions1709530000000 implements MigrationInterface {
  name = 'CreateAuditAndSessions1709530000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create audit_logs table for comprehensive change tracking
    await queryRunner.createTable(
      new Table({
        name: 'audit_logs',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: true,
            comment: 'User who performed the action',
          },
          {
            name: 'session_id',
            type: 'varchar',
            length: '255',
            isNullable: true,
            comment: 'Session identifier',
          },
          {
            name: 'entity_name',
            type: 'varchar',
            length: '100',
            comment: 'Name of the entity being modified',
          },
          {
            name: 'entity_id',
            type: 'varchar',
            length: '100',
            comment: 'ID of the specific entity instance',
          },
          {
            name: 'action',
            type: 'varchar',
            length: '20',
            comment: 'Action performed (CREATE, UPDATE, DELETE, READ)',
          },
          {
            name: 'old_values',
            type: 'jsonb',
            isNullable: true,
            comment: 'Previous values before change',
          },
          {
            name: 'new_values',
            type: 'jsonb',
            isNullable: true,
            comment: 'New values after change',
          },
          {
            name: 'changed_fields',
            type: 'text[]',
            isNullable: true,
            comment: 'Array of field names that were changed',
          },
          {
            name: 'ip_address',
            type: 'inet',
            isNullable: true,
            comment: 'IP address of the request',
          },
          {
            name: 'user_agent',
            type: 'text',
            isNullable: true,
            comment: 'User agent string',
          },
          {
            name: 'request_id',
            type: 'varchar',
            length: '100',
            isNullable: true,
            comment: 'Request correlation ID',
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
            comment: 'Additional context metadata',
          },
          {
            name: 'created_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          new Index('IDX_audit_logs_user_id', ['user_id']),
          new Index('IDX_audit_logs_entity', ['entity_name', 'entity_id']),
          new Index('IDX_audit_logs_action', ['action']),
          new Index('IDX_audit_logs_created_at', ['created_at']),
          new Index('IDX_audit_logs_session_id', ['session_id']),
          new Index('IDX_audit_logs_request_id', ['request_id']),
          new Index('IDX_audit_logs_ip_address', ['ip_address']),
        ],
      }),
      true
    );

    // Create user_sessions table for session management
    await queryRunner.createTable(
      new Table({
        name: 'user_sessions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'session_token',
            type: 'varchar',
            length: '255',
            isUnique: true,
          },
          {
            name: 'user_id',
            type: 'uuid',
          },
          {
            name: 'refresh_token',
            type: 'varchar',
            length: '255',
            isUnique: true,
          },
          {
            name: 'ip_address',
            type: 'inet',
          },
          {
            name: 'user_agent',
            type: 'text',
          },
          {
            name: 'device_fingerprint',
            type: 'varchar',
            length: '255',
            isNullable: true,
            comment: 'Browser/device fingerprint for security',
          },
          {
            name: 'location_country',
            type: 'varchar',
            length: '2',
            isNullable: true,
            comment: 'ISO country code',
          },
          {
            name: 'location_city',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'last_activity_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'expires_at',
            type: 'timestamp with time zone',
          },
          {
            name: 'created_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'revoked_at',
            type: 'timestamp with time zone',
            isNullable: true,
          },
          {
            name: 'revoked_by',
            type: 'uuid',
            isNullable: true,
            comment: 'User who revoked this session',
          },
          {
            name: 'revoked_reason',
            type: 'varchar',
            length: '100',
            isNullable: true,
            comment: 'Reason for session revocation',
          },
        ],
        indices: [
          new Index('IDX_user_sessions_session_token', ['session_token']),
          new Index('IDX_user_sessions_refresh_token', ['refresh_token']),
          new Index('IDX_user_sessions_user_id', ['user_id']),
          new Index('IDX_user_sessions_is_active', ['is_active']),
          new Index('IDX_user_sessions_expires_at', ['expires_at']),
          new Index('IDX_user_sessions_last_activity', ['last_activity_at']),
          new Index('IDX_user_sessions_ip_address', ['ip_address']),
        ],
      }),
      true
    );

    // Create security_events table for security monitoring
    await queryRunner.createTable(
      new Table({
        name: 'security_events',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'event_type',
            type: 'varchar',
            length: '50',
            comment: 'Type of security event (login_failed, suspicious_activity, etc.)',
          },
          {
            name: 'severity',
            type: 'varchar',
            length: '20',
            default: "'INFO'",
            comment: 'Event severity: INFO, WARNING, CRITICAL',
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'session_id',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'ip_address',
            type: 'inet',
          },
          {
            name: 'user_agent',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
            comment: 'Additional event data',
          },
          {
            name: 'risk_score',
            type: 'integer',
            default: 0,
            comment: 'Risk score 0-100',
          },
          {
            name: 'is_resolved',
            type: 'boolean',
            default: false,
          },
          {
            name: 'resolved_by',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'resolved_at',
            type: 'timestamp with time zone',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          new Index('IDX_security_events_event_type', ['event_type']),
          new Index('IDX_security_events_severity', ['severity']),
          new Index('IDX_security_events_user_id', ['user_id']),
          new Index('IDX_security_events_ip_address', ['ip_address']),
          new Index('IDX_security_events_created_at', ['created_at']),
          new Index('IDX_security_events_risk_score', ['risk_score']),
          new Index('IDX_security_events_is_resolved', ['is_resolved']),
        ],
      }),
      true
    );

    // Create data_retention_policies table
    await queryRunner.createTable(
      new Table({
        name: 'data_retention_policies',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'table_name',
            type: 'varchar',
            length: '100',
          },
          {
            name: 'retention_period_days',
            type: 'integer',
            comment: 'Number of days to retain data',
          },
          {
            name: 'archive_before_delete',
            type: 'boolean',
            default: true,
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'last_cleanup_at',
            type: 'timestamp with time zone',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          new Index('IDX_data_retention_table_name', ['table_name']),
          new Index('IDX_data_retention_is_active', ['is_active']),
        ],
      }),
      true
    );

    // Add foreign key constraints
    await queryRunner.createForeignKey(
      'audit_logs',
      new ForeignKey({
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        name: 'FK_audit_logs_user_id',
      })
    );

    await queryRunner.createForeignKey(
      'user_sessions',
      new ForeignKey({
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
        name: 'FK_user_sessions_user_id',
      })
    );

    await queryRunner.createForeignKey(
      'user_sessions',
      new ForeignKey({
        columnNames: ['revoked_by'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        name: 'FK_user_sessions_revoked_by',
      })
    );

    await queryRunner.createForeignKey(
      'security_events',
      new ForeignKey({
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        name: 'FK_security_events_user_id',
      })
    );

    await queryRunner.createForeignKey(
      'security_events',
      new ForeignKey({
        columnNames: ['resolved_by'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        name: 'FK_security_events_resolved_by',
      })
    );

    // Insert default data retention policies
    await queryRunner.query(`
      INSERT INTO data_retention_policies (table_name, retention_period_days, archive_before_delete) VALUES
      ('audit_logs', 2555, true),        -- 7 years
      ('security_events', 1095, true),   -- 3 years
      ('user_sessions', 30, false),      -- 30 days
      ('api_request_logs', 90, true);    -- 90 days
    `);

    // Create trigger function for audit logging
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION create_audit_log()
      RETURNS TRIGGER AS $$
      DECLARE
        old_data JSONB;
        new_data JSONB;
        changed_fields TEXT[] := '{}';
        field_name TEXT;
      BEGIN
        -- Determine the action
        IF TG_OP = 'DELETE' THEN
          old_data = row_to_json(OLD)::JSONB;
          new_data = NULL;
        ELSIF TG_OP = 'UPDATE' THEN
          old_data = row_to_json(OLD)::JSONB;
          new_data = row_to_json(NEW)::JSONB;
          
          -- Identify changed fields
          FOR field_name IN SELECT jsonb_object_keys(new_data) LOOP
            IF old_data->field_name != new_data->field_name THEN
              changed_fields := array_append(changed_fields, field_name);
            END IF;
          END LOOP;
        ELSIF TG_OP = 'INSERT' THEN
          old_data = NULL;
          new_data = row_to_json(NEW)::JSONB;
        END IF;

        -- Insert audit log
        INSERT INTO audit_logs (
          entity_name,
          entity_id,
          action,
          old_values,
          new_values,
          changed_fields,
          created_at
        ) VALUES (
          TG_TABLE_NAME,
          COALESCE(NEW.id::TEXT, OLD.id::TEXT),
          TG_OP,
          old_data,
          new_data,
          CASE WHEN array_length(changed_fields, 1) > 0 THEN changed_fields ELSE NULL END,
          CURRENT_TIMESTAMP
        );

        RETURN COALESCE(NEW, OLD);
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create triggers for audit logging on critical tables
    await queryRunner.query(`
      CREATE TRIGGER trigger_audit_users
      AFTER INSERT OR UPDATE OR DELETE ON users
      FOR EACH ROW EXECUTE FUNCTION create_audit_log();
    `);

    await queryRunner.query(`
      CREATE TRIGGER trigger_audit_user_roles
      AFTER INSERT OR UPDATE OR DELETE ON user_roles
      FOR EACH ROW EXECUTE FUNCTION create_audit_log();
    `);

    await queryRunner.query(`
      CREATE TRIGGER trigger_audit_roles
      AFTER INSERT OR UPDATE OR DELETE ON roles
      FOR EACH ROW EXECUTE FUNCTION create_audit_log();
    `);

    // Create function to clean up expired sessions
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
      RETURNS INTEGER AS $$
      DECLARE
        deleted_count INTEGER;
      BEGIN
        DELETE FROM user_sessions
        WHERE expires_at < CURRENT_TIMESTAMP
           OR (is_active = false AND revoked_at < CURRENT_TIMESTAMP - INTERVAL '7 days');
        
        GET DIAGNOSTICS deleted_count = ROW_COUNT;
        
        RETURN deleted_count;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create function to update session activity
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_session_activity(
        input_session_token VARCHAR,
        input_ip_address INET
      )
      RETURNS BOOLEAN AS $$
      BEGIN
        UPDATE user_sessions
        SET last_activity_at = CURRENT_TIMESTAMP,
            ip_address = COALESCE(input_ip_address, ip_address)
        WHERE session_token = input_session_token
          AND is_active = true
          AND expires_at > CURRENT_TIMESTAMP;
        
        RETURN FOUND;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create function to log security events
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION log_security_event(
        input_event_type VARCHAR,
        input_severity VARCHAR,
        input_user_id UUID,
        input_ip_address INET,
        input_description TEXT,
        input_metadata JSONB DEFAULT NULL,
        input_risk_score INTEGER DEFAULT 0
      )
      RETURNS UUID AS $$
      DECLARE
        event_id UUID;
      BEGIN
        INSERT INTO security_events (
          event_type,
          severity,
          user_id,
          ip_address,
          description,
          metadata,
          risk_score
        ) VALUES (
          input_event_type,
          input_severity,
          input_user_id,
          input_ip_address,
          input_description,
          input_metadata,
          input_risk_score
        ) RETURNING id INTO event_id;
        
        RETURN event_id;
      END;
      $$ LANGUAGE plpgsql;
    `);

    -- Create materialized view for session analytics
    await queryRunner.query(`
      CREATE MATERIALIZED VIEW session_analytics AS
      SELECT
        DATE_TRUNC('day', created_at) as session_date,
        COUNT(*) as total_sessions,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(DISTINCT ip_address) as unique_ips,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_sessions,
        AVG(EXTRACT(EPOCH FROM (COALESCE(revoked_at, CURRENT_TIMESTAMP) - created_at)) / 3600) as avg_duration_hours
      FROM user_sessions
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY DATE_TRUNC('day', created_at)
      ORDER BY session_date;
      
      CREATE UNIQUE INDEX ON session_analytics (session_date);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop materialized view
    await queryRunner.query('DROP MATERIALIZED VIEW IF EXISTS session_analytics;');

    // Drop triggers
    await queryRunner.query('DROP TRIGGER IF EXISTS trigger_audit_roles ON roles;');
    await queryRunner.query('DROP TRIGGER IF EXISTS trigger_audit_user_roles ON user_roles;');
    await queryRunner.query('DROP TRIGGER IF EXISTS trigger_audit_users ON users;');

    // Drop functions
    await queryRunner.query('DROP FUNCTION IF EXISTS log_security_event(VARCHAR, VARCHAR, UUID, INET, TEXT, JSONB, INTEGER);');
    await queryRunner.query('DROP FUNCTION IF EXISTS update_session_activity(VARCHAR, INET);');
    await queryRunner.query('DROP FUNCTION IF EXISTS cleanup_expired_sessions();');
    await queryRunner.query('DROP FUNCTION IF EXISTS create_audit_log();');

    // Drop foreign keys
    await queryRunner.dropForeignKey('security_events', 'FK_security_events_resolved_by');
    await queryRunner.dropForeignKey('security_events', 'FK_security_events_user_id');
    await queryRunner.dropForeignKey('user_sessions', 'FK_user_sessions_revoked_by');
    await queryRunner.dropForeignKey('user_sessions', 'FK_user_sessions_user_id');
    await queryRunner.dropForeignKey('audit_logs', 'FK_audit_logs_user_id');

    // Drop tables
    await queryRunner.dropTable('data_retention_policies');
    await queryRunner.dropTable('security_events');
    await queryRunner.dropTable('user_sessions');
    await queryRunner.dropTable('audit_logs');
  }
}