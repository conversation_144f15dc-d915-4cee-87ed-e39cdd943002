/**
 * 🔧 Common Module - Shared Utilities and Services
 * 
 * Provides common functionality across the application:
 * - Shared services and utilities
 * - Common interceptors and filters
 * - Validation pipes and guards
 * - Cross-cutting concerns
 */

import { Module, Global } from '@nestjs/common';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { ErrorsInterceptor } from './interceptors/errors.interceptor';
import { TimeoutInterceptor } from './interceptors/timeout.interceptor';
import { TransformInterceptor } from './interceptors/transform.interceptor';
import { AllExceptionsFilter } from './filters/all-exceptions.filter';

@Global()
@Module({
  providers: [
    LoggingInterceptor,
    ErrorsInterceptor,
    TimeoutInterceptor,
    TransformInterceptor,
    AllExceptionsFilter,
  ],
  exports: [
    LoggingInterceptor,
    ErrorsInterceptor,
    TimeoutInterceptor,
    TransformInterceptor,
    AllExceptionsFilter,
  ],
})
export class CommonModule {}
