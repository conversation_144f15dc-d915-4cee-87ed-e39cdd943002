/**
 * 🔒 Security Service - Enterprise Security and Threat Protection
 * 
 * Implements comprehensive security features with:
 * - Input validation and sanitization
 * - SQL injection prevention
 * - XSS protection
 * - Rate limiting and DDoS protection
 * - Suspicious activity detection
 * - Security audit logging
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as bcrypt from 'bcrypt';
import * as validator from 'validator';
import * as DOMPurify from 'isomorphic-dompurify';

export interface SecurityEvent {
  type: 'suspicious_activity' | 'failed_login' | 'rate_limit_exceeded' | 'invalid_input' | 'unauthorized_access';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ipAddress: string;
  userAgent?: string;
  endpoint?: string;
  details: Record<string, any>;
  timestamp: Date;
}

export interface ValidationResult {
  isValid: boolean;
  sanitizedValue?: any;
  errors: string[];
  warnings: string[];
}

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);
  private readonly suspiciousIps = new Set<string>();
  private readonly failedAttempts = new Map<string, { count: number; lastAttempt: Date }>();
  private readonly rateLimitStore = new Map<string, { count: number; resetTime: Date }>();

  constructor(private readonly configService: ConfigService) {}

  // ================================
  // 🔐 Password Security
  // ================================

  /**
   * Hash password with bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    const rounds = this.configService.get<number>('auth.bcrypt.rounds', 12);
    return bcrypt.hash(password, rounds);
  }

  /**
   * Verify password against hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      this.logger.error('Password verification failed', error.stack);
      return false;
    }
  }

  /**
   * Validate password strength
   */
  validatePasswordStrength(password: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    const minLength = this.configService.get<number>('auth.password.minLength', 8);
    const requireUppercase = this.configService.get<boolean>('auth.password.requireUppercase', true);
    const requireLowercase = this.configService.get<boolean>('auth.password.requireLowercase', true);
    const requireNumbers = this.configService.get<boolean>('auth.password.requireNumbers', true);
    const requireSymbols = this.configService.get<boolean>('auth.password.requireSymbols', true);

    // Length check
    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }

    // Character requirements
    if (requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (requireSymbols && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Common password patterns
    const commonPatterns = [
      /^(.)\1+$/, // All same character
      /^(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i,
      /^(password|123456|qwerty|admin|root|user|guest)/i,
    ];

    for (const pattern of commonPatterns) {
      if (pattern.test(password)) {
        warnings.push('Password contains common patterns that may be easily guessed');
        break;
      }
    }

    // Entropy check
    const entropy = this.calculatePasswordEntropy(password);
    if (entropy < 50) {
      warnings.push('Password has low entropy and may be vulnerable to attacks');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Calculate password entropy
   */
  private calculatePasswordEntropy(password: string): number {
    const charSets = [
      { regex: /[a-z]/, size: 26 },
      { regex: /[A-Z]/, size: 26 },
      { regex: /[0-9]/, size: 10 },
      { regex: /[^a-zA-Z0-9]/, size: 32 },
    ];

    let charSetSize = 0;
    for (const charSet of charSets) {
      if (charSet.regex.test(password)) {
        charSetSize += charSet.size;
      }
    }

    return password.length * Math.log2(charSetSize);
  }

  // ================================
  // 🧹 Input Sanitization
  // ================================

  /**
   * Sanitize HTML input to prevent XSS
   */
  sanitizeHtml(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: [],
    });
  }

  /**
   * Sanitize SQL input to prevent injection
   */
  sanitizeSqlInput(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    // Remove or escape dangerous SQL characters
    return input
      .replace(/'/g, "''")
      .replace(/;/g, '')
      .replace(/--/g, '')
      .replace(/\/\*/g, '')
      .replace(/\*\//g, '')
      .replace(/xp_/gi, '')
      .replace(/sp_/gi, '')
      .trim();
  }

  /**
   * Validate and sanitize email
   */
  validateEmail(email: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!email || typeof email !== 'string') {
      errors.push('Email is required');
      return { isValid: false, errors, warnings };
    }

    const sanitizedEmail = validator.normalizeEmail(email.trim().toLowerCase());

    if (!validator.isEmail(sanitizedEmail)) {
      errors.push('Invalid email format');
    }

    // Check for suspicious patterns
    if (sanitizedEmail.includes('..') || sanitizedEmail.includes('++')) {
      warnings.push('Email contains suspicious patterns');
    }

    // Check domain
    const domain = sanitizedEmail.split('@')[1];
    if (domain && this.isSuspiciousDomain(domain)) {
      warnings.push('Email domain appears suspicious');
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: sanitizedEmail,
      errors,
      warnings,
    };
  }

  /**
   * Validate and sanitize URL
   */
  validateUrl(url: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!url || typeof url !== 'string') {
      errors.push('URL is required');
      return { isValid: false, errors, warnings };
    }

    const trimmedUrl = url.trim();

    if (!validator.isURL(trimmedUrl, {
      protocols: ['http', 'https'],
      require_protocol: true,
    })) {
      errors.push('Invalid URL format');
    }

    // Check for suspicious patterns
    if (this.containsSuspiciousUrlPatterns(trimmedUrl)) {
      warnings.push('URL contains suspicious patterns');
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: trimmedUrl,
      errors,
      warnings,
    };
  }

  // ================================
  // 🚨 Threat Detection
  // ================================

  /**
   * Detect suspicious activity
   */
  detectSuspiciousActivity(
    ipAddress: string,
    userAgent?: string,
    endpoint?: string,
    userId?: string,
  ): boolean {
    // Check if IP is already flagged
    if (this.suspiciousIps.has(ipAddress)) {
      return true;
    }

    // Check for suspicious user agents
    if (userAgent && this.isSuspiciousUserAgent(userAgent)) {
      this.flagSuspiciousIp(ipAddress);
      this.logSecurityEvent({
        type: 'suspicious_activity',
        severity: 'medium',
        userId,
        ipAddress,
        userAgent,
        endpoint,
        details: { reason: 'suspicious_user_agent' },
        timestamp: new Date(),
      });
      return true;
    }

    // Check for rapid requests from same IP
    if (this.isRapidRequests(ipAddress)) {
      this.flagSuspiciousIp(ipAddress);
      this.logSecurityEvent({
        type: 'suspicious_activity',
        severity: 'high',
        userId,
        ipAddress,
        userAgent,
        endpoint,
        details: { reason: 'rapid_requests' },
        timestamp: new Date(),
      });
      return true;
    }

    return false;
  }

  /**
   * Track failed login attempts
   */
  trackFailedLogin(identifier: string, ipAddress: string): boolean {
    const key = `${identifier}:${ipAddress}`;
    const now = new Date();
    const maxAttempts = this.configService.get<number>('auth.account.maxLoginAttempts', 5);
    const lockoutDuration = this.configService.get<number>('auth.account.lockoutDuration', 900) * 1000; // Convert to ms

    const attempts = this.failedAttempts.get(key) || { count: 0, lastAttempt: now };

    // Reset if lockout period has passed
    if (now.getTime() - attempts.lastAttempt.getTime() > lockoutDuration) {
      attempts.count = 0;
    }

    attempts.count++;
    attempts.lastAttempt = now;
    this.failedAttempts.set(key, attempts);

    if (attempts.count >= maxAttempts) {
      this.flagSuspiciousIp(ipAddress);
      this.logSecurityEvent({
        type: 'failed_login',
        severity: 'high',
        ipAddress,
        details: { identifier, attempts: attempts.count },
        timestamp: now,
      });
      return true; // Account locked
    }

    return false;
  }

  /**
   * Check rate limiting
   */
  checkRateLimit(key: string, limit: number, windowMs: number): boolean {
    const now = new Date();
    const entry = this.rateLimitStore.get(key);

    if (!entry || now.getTime() > entry.resetTime.getTime()) {
      // Reset window
      this.rateLimitStore.set(key, {
        count: 1,
        resetTime: new Date(now.getTime() + windowMs),
      });
      return false;
    }

    entry.count++;
    
    if (entry.count > limit) {
      this.logSecurityEvent({
        type: 'rate_limit_exceeded',
        severity: 'medium',
        ipAddress: key.split(':')[0] || 'unknown',
        details: { key, limit, count: entry.count },
        timestamp: now,
      });
      return true; // Rate limit exceeded
    }

    return false;
  }

  // ================================
  // 🔧 Utility Methods
  // ================================

  private isSuspiciousDomain(domain: string): boolean {
    const suspiciousDomains = [
      'tempmail.org',
      '10minutemail.com',
      'guerrillamail.com',
      'mailinator.com',
    ];

    return suspiciousDomains.some(suspicious => domain.includes(suspicious));
  }

  private containsSuspiciousUrlPatterns(url: string): boolean {
    const suspiciousPatterns = [
      /javascript:/i,
      /data:/i,
      /vbscript:/i,
      /<script/i,
      /onload=/i,
      /onerror=/i,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(url));
  }

  private isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /^$/,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  private isRapidRequests(ipAddress: string): boolean {
    // This is a simplified implementation
    // In production, use a more sophisticated rate limiting algorithm
    const key = `rapid:${ipAddress}`;
    return this.checkRateLimit(key, 100, 60000); // 100 requests per minute
  }

  private flagSuspiciousIp(ipAddress: string): void {
    this.suspiciousIps.add(ipAddress);
    
    // Auto-remove after 24 hours
    setTimeout(() => {
      this.suspiciousIps.delete(ipAddress);
    }, 24 * 60 * 60 * 1000);
  }

  private logSecurityEvent(event: SecurityEvent): void {
    this.logger.warn('Security event detected', {
      type: event.type,
      severity: event.severity,
      userId: event.userId,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      endpoint: event.endpoint,
      details: event.details,
      timestamp: event.timestamp,
    });

    // In production, send to security monitoring system
    // this.securityMonitoringService.reportEvent(event);
  }

  // ================================
  // 🔑 Cryptographic Functions
  // ================================

  /**
   * Generate secure random token
   */
  generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate HMAC signature
   */
  generateHmacSignature(data: string, secret: string): string {
    return crypto.createHmac('sha256', secret).update(data).digest('hex');
  }

  /**
   * Verify HMAC signature
   */
  verifyHmacSignature(data: string, signature: string, secret: string): boolean {
    const expectedSignature = this.generateHmacSignature(data, secret);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex'),
    );
  }

  /**
   * Encrypt sensitive data
   */
  encryptData(data: string, key: string): { encrypted: string; iv: string } {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-cbc', key);
    cipher.setAutoPadding(true);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return {
      encrypted,
      iv: iv.toString('hex'),
    };
  }

  /**
   * Decrypt sensitive data
   */
  decryptData(encryptedData: string, key: string, iv: string): string {
    const decipher = crypto.createDecipher('aes-256-cbc', key);
    decipher.setAutoPadding(true);
    
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
