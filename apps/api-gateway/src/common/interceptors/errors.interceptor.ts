/**
 * 🚨 Error Handling Interceptor - Centralized Error Processing
 * 
 * Implements comprehensive error handling with:
 * - Error transformation and standardization
 * - Security-aware error responses
 * - Error logging and monitoring
 * - Performance impact tracking
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

interface ErrorResponse {
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
  correlationId?: string;
  details?: any;
}

@Injectable()
export class ErrorsInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ErrorsInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    return next.handle().pipe(
      catchError((error) => {
        const correlationId = request['correlationId'];
        const { method, url, ip } = request;
        const timestamp = new Date().toISOString();

        // Determine if this is a known HTTP exception
        const isHttpException = error instanceof HttpException;
        const statusCode = isHttpException 
          ? error.getStatus() 
          : HttpStatus.INTERNAL_SERVER_ERROR;

        // Extract error message
        let message: string | string[];
        let errorName: string;

        if (isHttpException) {
          const errorResponse = error.getResponse();
          if (typeof errorResponse === 'object' && errorResponse !== null) {
            message = (errorResponse as any).message || error.message;
            errorName = (errorResponse as any).error || error.name;
          } else {
            message = errorResponse as string;
            errorName = error.name;
          }
        } else {
          message = 'Internal server error';
          errorName = 'InternalServerError';
        }

        // Create standardized error response
        const errorResponse: ErrorResponse = {
          statusCode,
          message,
          error: errorName,
          timestamp,
          path: url,
          correlationId,
        };

        // Add details for development environment
        if (process.env.NODE_ENV === 'development' && !isHttpException) {
          errorResponse.details = {
            stack: error.stack,
            originalMessage: error.message,
          };
        }

        // Log error with appropriate level
        const logData = {
          message: 'Request Error',
          correlationId,
          method,
          url,
          ip,
          statusCode,
          errorName,
          errorMessage: error.message,
          timestamp,
        };

        if (statusCode >= 500) {
          // Server errors - log as error with stack trace
          this.logger.error({
            ...logData,
            stack: error.stack,
          });
        } else if (statusCode >= 400) {
          // Client errors - log as warning
          this.logger.warn(logData);
        } else {
          // Other errors - log as info
          this.logger.log(logData);
        }

        // Set response status code
        response.status(statusCode);

        // Return standardized error
        return throwError(() => new HttpException(errorResponse, statusCode));
      }),
    );
  }
}
