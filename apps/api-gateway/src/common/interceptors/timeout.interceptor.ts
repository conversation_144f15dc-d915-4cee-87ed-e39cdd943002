/**
 * ⏱️ Timeout Interceptor - Request Timeout Management
 * 
 * Implements request timeout handling with:
 * - Configurable timeout values
 * - Graceful timeout responses
 * - Performance monitoring
 * - Resource cleanup
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  RequestTimeoutException,
  Logger,
} from '@nestjs/common';
import { Observable, throwError, TimeoutError } from 'rxjs';
import { timeout, catchError } from 'rxjs/operators';
import { Request } from 'express';

@Injectable()
export class TimeoutInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TimeoutInterceptor.name);

  constructor(private readonly timeoutValue: number = 30000) {} // Default 30 seconds

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const correlationId = request['correlationId'];
    const { method, url } = request;

    return next.handle().pipe(
      timeout(this.timeoutValue),
      catchError((error) => {
        if (error instanceof TimeoutError) {
          // Log timeout occurrence
          this.logger.warn({
            message: 'Request Timeout',
            correlationId,
            method,
            url,
            timeout: this.timeoutValue,
            timestamp: new Date().toISOString(),
          });

          // Return standardized timeout error
          return throwError(
            () => new RequestTimeoutException({
              statusCode: 408,
              message: `Request timeout after ${this.timeoutValue}ms`,
              error: 'Request Timeout',
              timestamp: new Date().toISOString(),
              path: url,
              correlationId,
            }),
          );
        }

        // Re-throw other errors
        return throwError(() => error);
      }),
    );
  }
}
