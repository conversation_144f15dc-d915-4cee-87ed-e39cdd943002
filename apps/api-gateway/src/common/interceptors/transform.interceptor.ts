/**
 * 🔄 Transform Interceptor - Response Transformation
 * 
 * Implements standardized response transformation with:
 * - Consistent response format
 * - Data serialization
 * - Metadata injection
 * - Performance metrics
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request, Response } from 'express';

export interface StandardResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
  path: string;
  method: string;
  statusCode: number;
  correlationId?: string;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasNext?: boolean;
    hasPrev?: boolean;
  };
}

@Injectable()
export class TransformInterceptor<T>
  implements NestInterceptor<T, StandardResponse<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<StandardResponse<T>> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    
    const { method, url } = request;
    const correlationId = request['correlationId'];
    const statusCode = response.statusCode;

    return next.handle().pipe(
      map((data) => {
        // Skip transformation for health checks and raw responses
        const skipPaths = ['/health', '/metrics', '/docs'];
        const shouldSkip = skipPaths.some(path => url.includes(path));
        
        if (shouldSkip || data?.skipTransform) {
          return data;
        }

        // Handle different response types
        let transformedData = data;
        let message: string | undefined;
        let meta: any;

        // Extract message and meta if present
        if (data && typeof data === 'object') {
          if ('message' in data) {
            message = data.message;
            delete data.message;
          }
          if ('meta' in data) {
            meta = data.meta;
            delete data.meta;
          }
          if ('data' in data) {
            transformedData = data.data;
          }
        }

        // Create standardized response
        const standardResponse: StandardResponse<T> = {
          success: statusCode >= 200 && statusCode < 300,
          data: transformedData,
          timestamp: new Date().toISOString(),
          path: url,
          method,
          statusCode,
          correlationId,
        };

        // Add optional fields
        if (message) {
          standardResponse.message = message;
        }
        if (meta) {
          standardResponse.meta = meta;
        }

        return standardResponse;
      }),
    );
  }
}
