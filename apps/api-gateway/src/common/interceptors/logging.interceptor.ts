/**
 * 📊 Logging Interceptor - Request/Response Logging
 * 
 * Implements comprehensive request/response logging with:
 * - Structured logging format
 * - Performance metrics
 * - Error tracking
 * - Correlation IDs
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const startTime = Date.now();

    // Generate correlation ID for request tracking
    const correlationId = uuidv4();
    request['correlationId'] = correlationId;
    response.setHeader('X-Correlation-ID', correlationId);

    // Extract request information
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || 'Unknown';
    const contentLength = headers['content-length'] || '0';

    // Skip logging for health checks and metrics endpoints
    const skipPaths = ['/health', '/metrics', '/favicon.ico'];
    const shouldSkip = skipPaths.some(path => url.includes(path));

    if (!shouldSkip) {
      this.logger.log({
        message: 'Incoming Request',
        correlationId,
        method,
        url,
        ip,
        userAgent,
        contentLength: parseInt(contentLength, 10),
        timestamp: new Date().toISOString(),
      });
    }

    return next.handle().pipe(
      tap((data) => {
        const duration = Date.now() - startTime;
        const { statusCode } = response;

        if (!shouldSkip) {
          this.logger.log({
            message: 'Request Completed',
            correlationId,
            method,
            url,
            statusCode,
            duration,
            responseSize: JSON.stringify(data || {}).length,
            timestamp: new Date().toISOString(),
          });
        }

        // Log slow requests (> 1 second)
        if (duration > 1000) {
          this.logger.warn({
            message: 'Slow Request Detected',
            correlationId,
            method,
            url,
            duration,
            threshold: 1000,
            timestamp: new Date().toISOString(),
          });
        }
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        const { statusCode } = response;

        this.logger.error({
          message: 'Request Failed',
          correlationId,
          method,
          url,
          statusCode,
          duration,
          error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
          },
          timestamp: new Date().toISOString(),
        });

        throw error;
      }),
    );
  }
}
