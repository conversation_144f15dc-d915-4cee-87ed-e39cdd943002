/**
 * 🛡️ Security Headers Middleware
 * 
 * Comprehensive security headers implementation:
 * - OWASP security headers
 * - CSP (Content Security Policy)
 * - HSTS (HTTP Strict Transport Security)
 * - Feature Policy / Permissions Policy
 * - Dynamic security policies based on environment
 */

import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

interface SecurityConfig {
  csp: {
    enabled: boolean;
    directives: Record<string, string[]>;
    reportUri?: string;
    reportOnly?: boolean;
  };
  hsts: {
    enabled: boolean;
    maxAge: number;
    includeSubDomains: boolean;
    preload: boolean;
  };
  noSniff: boolean;
  frameOptions: 'DENY' | 'SAMEORIGIN' | string;
  xssProtection: boolean;
  referrerPolicy: string;
  permissionsPolicy: Record<string, string[]>;
}

@Injectable()
export class SecurityHeadersMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SecurityHeadersMiddleware.name);
  private readonly securityConfig: SecurityConfig;

  constructor(private readonly configService: ConfigService) {
    this.securityConfig = this.buildSecurityConfig();
  }

  use(req: Request, res: Response, next: NextFunction): void {
    try {
      this.setSecurityHeaders(req, res);
      next();
    } catch (error) {
      this.logger.error('Error setting security headers:', error);
      next(); // Continue even if header setting fails
    }
  }

  private buildSecurityConfig(): SecurityConfig {
    const isProduction = this.configService.get('NODE_ENV') === 'production';
    const isDevelopment = this.configService.get('NODE_ENV') === 'development';

    return {
      csp: {
        enabled: this.configService.get('SECURITY_CSP_ENABLED', true),
        reportOnly: this.configService.get('SECURITY_CSP_REPORT_ONLY', !isProduction),
        reportUri: this.configService.get('SECURITY_CSP_REPORT_URI'),
        directives: {
          'default-src': ["'self'"],
          'script-src': [
            "'self'",
            "'unsafe-inline'", // Only for development
            ...(isDevelopment ? ["'unsafe-eval'"] : []),
            'https://cdn.jsdelivr.net',
            'https://unpkg.com',
          ],
          'style-src': [
            "'self'",
            "'unsafe-inline'",
            'https://fonts.googleapis.com',
            'https://cdn.jsdelivr.net',
          ],
          'font-src': [
            "'self'",
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
          ],
          'img-src': [
            "'self'",
            'data:',
            'https:',
            'blob:',
          ],
          'connect-src': [
            "'self'",
            ...(isDevelopment ? ['ws:', 'wss:'] : []),
            'https://api.github.com',
            this.configService.get('AI_SERVICE_URL', 'http://localhost:8000'),
          ],
          'media-src': ["'self'"],
          'object-src': ["'none'"],
          'child-src': ["'self'"],
          'worker-src': ["'self'", 'blob:'],
          'manifest-src': ["'self'"],
          'base-uri': ["'self'"],
          'form-action': ["'self'"],
          'frame-ancestors': ["'none'"],
          'upgrade-insecure-requests': [],
        },
      },
      hsts: {
        enabled: this.configService.get('SECURITY_HSTS_ENABLED', isProduction),
        maxAge: this.configService.get('SECURITY_HSTS_MAX_AGE', 31536000), // 1 year
        includeSubDomains: this.configService.get('SECURITY_HSTS_INCLUDE_SUBDOMAINS', true),
        preload: this.configService.get('SECURITY_HSTS_PRELOAD', true),
      },
      noSniff: this.configService.get('SECURITY_NO_SNIFF', true),
      frameOptions: this.configService.get('SECURITY_FRAME_OPTIONS', 'DENY'),
      xssProtection: this.configService.get('SECURITY_XSS_PROTECTION', true),
      referrerPolicy: this.configService.get('SECURITY_REFERRER_POLICY', 'strict-origin-when-cross-origin'),
      permissionsPolicy: {
        'geolocation': ["'none'"],
        'microphone': ["'none'"],
        'camera': ["'none'"],
        'payment': ["'none'"],
        'usb': ["'none'"],
        'magnetometer': ["'none'"],
        'gyroscope': ["'none'"],
        'speaker': ["'self'"],
        'vibrate': ["'none'"],
        'fullscreen': ["'self'"],
        'sync-xhr': ["'none'"],
      },
    };
  }

  private setSecurityHeaders(req: Request, res: Response): void {
    // Content Security Policy
    if (this.securityConfig.csp.enabled) {
      this.setCSPHeader(res);
    }

    // HTTP Strict Transport Security
    if (this.securityConfig.hsts.enabled) {
      this.setHSTSHeader(res);
    }

    // X-Content-Type-Options
    if (this.securityConfig.noSniff) {
      res.setHeader('X-Content-Type-Options', 'nosniff');
    }

    // X-Frame-Options
    res.setHeader('X-Frame-Options', this.securityConfig.frameOptions);

    // X-XSS-Protection
    if (this.securityConfig.xssProtection) {
      res.setHeader('X-XSS-Protection', '1; mode=block');
    }

    // Referrer Policy
    res.setHeader('Referrer-Policy', this.securityConfig.referrerPolicy);

    // Permissions Policy (Feature Policy)
    this.setPermissionsPolicyHeader(res);

    // Additional security headers
    this.setAdditionalSecurityHeaders(req, res);
  }

  private setCSPHeader(res: Response): void {
    const directives = Object.entries(this.securityConfig.csp.directives)
      .map(([directive, sources]) => {
        if (sources.length === 0) {
          return directive;
        }
        return `${directive} ${sources.join(' ')}`;
      })
      .join('; ');

    let cspValue = directives;

    // Add report URI if configured
    if (this.securityConfig.csp.reportUri) {
      cspValue += `; report-uri ${this.securityConfig.csp.reportUri}`;
    }

    const headerName = this.securityConfig.csp.reportOnly
      ? 'Content-Security-Policy-Report-Only'
      : 'Content-Security-Policy';

    res.setHeader(headerName, cspValue);
  }

  private setHSTSHeader(res: Response): void {
    let hstsValue = `max-age=${this.securityConfig.hsts.maxAge}`;

    if (this.securityConfig.hsts.includeSubDomains) {
      hstsValue += '; includeSubDomains';
    }

    if (this.securityConfig.hsts.preload) {
      hstsValue += '; preload';
    }

    res.setHeader('Strict-Transport-Security', hstsValue);
  }

  private setPermissionsPolicyHeader(res: Response): void {
    const policies = Object.entries(this.securityConfig.permissionsPolicy)
      .map(([feature, allowlist]) => {
        const allowlistStr = allowlist.join(' ');
        return `${feature}=(${allowlistStr})`;
      })
      .join(', ');

    res.setHeader('Permissions-Policy', policies);
  }

  private setAdditionalSecurityHeaders(req: Request, res: Response): void {
    // X-Powered-By - Remove or customize
    res.removeHeader('X-Powered-By');
    res.setHeader('X-Powered-By', 'Enterprise API Gateway');

    // Server header - Minimize information disclosure
    res.setHeader('Server', 'nginx');

    // Cross-Origin headers for API endpoints
    if (req.path.startsWith('/api/')) {
      res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
      res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
      res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');
    }

    // Cache control for sensitive endpoints
    if (this.isSensitiveEndpoint(req.path)) {
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
    }

    // DNS Prefetch Control
    res.setHeader('X-DNS-Prefetch-Control', 'off');

    // Download Options (IE specific)
    res.setHeader('X-Download-Options', 'noopen');

    // Permitted Cross-Domain Policies
    res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');

    // Content-Type Options
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // Add request ID for tracking
    const requestId = req.headers['x-request-id'] || this.generateRequestId();
    res.setHeader('X-Request-ID', requestId);

    // Rate limit info (if available)
    if ((req as any).rateLimit) {
      const rateLimit = (req as any).rateLimit;
      res.setHeader('X-RateLimit-Limit', rateLimit.limit);
      res.setHeader('X-RateLimit-Remaining', rateLimit.remaining);
      res.setHeader('X-RateLimit-Reset', rateLimit.reset);
    }
  }

  private isSensitiveEndpoint(path: string): boolean {
    const sensitivePatterns = [
      '/api/v1/auth/',
      '/api/v1/admin/',
      '/api/v1/users/',
      '/api/v1/profile',
      '/api/v1/settings',
    ];

    return sensitivePatterns.some(pattern => path.startsWith(pattern));
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public methods for dynamic configuration

  updateCSPDirective(directive: string, sources: string[]): void {
    this.securityConfig.csp.directives[directive] = sources;
    this.logger.log(`Updated CSP directive: ${directive}`);
  }

  addCSPSource(directive: string, source: string): void {
    if (!this.securityConfig.csp.directives[directive]) {
      this.securityConfig.csp.directives[directive] = [];
    }
    
    if (!this.securityConfig.csp.directives[directive].includes(source)) {
      this.securityConfig.csp.directives[directive].push(source);
      this.logger.log(`Added CSP source ${source} to directive ${directive}`);
    }
  }

  removeCSPSource(directive: string, source: string): void {
    if (this.securityConfig.csp.directives[directive]) {
      const index = this.securityConfig.csp.directives[directive].indexOf(source);
      if (index > -1) {
        this.securityConfig.csp.directives[directive].splice(index, 1);
        this.logger.log(`Removed CSP source ${source} from directive ${directive}`);
      }
    }
  }

  enableHSTS(maxAge?: number): void {
    this.securityConfig.hsts.enabled = true;
    if (maxAge) {
      this.securityConfig.hsts.maxAge = maxAge;
    }
    this.logger.log('HSTS enabled');
  }

  disableHSTS(): void {
    this.securityConfig.hsts.enabled = false;
    this.logger.log('HSTS disabled');
  }

  updatePermissionsPolicy(feature: string, allowlist: string[]): void {
    this.securityConfig.permissionsPolicy[feature] = allowlist;
    this.logger.log(`Updated permissions policy for feature: ${feature}`);
  }

  getSecurityConfig(): SecurityConfig {
    return { ...this.securityConfig };
  }
}