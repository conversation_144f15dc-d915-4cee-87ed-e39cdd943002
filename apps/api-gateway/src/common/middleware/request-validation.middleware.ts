/**
 * 🔒 Request Validation Middleware
 * 
 * Advanced input validation and sanitization:
 * - SQL injection prevention
 * - XSS protection
 * - NoSQL injection prevention
 * - Request size limits
 * - File upload validation
 * - Input sanitization
 */

import { Injectable, NestMiddleware, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import * as DOMPurify from 'isomorphic-dompurify';
import * as validator from 'validator';

interface ValidationConfig {
  maxBodySize: number;
  maxUrlLength: number;
  maxHeaderSize: number;
  allowedFileTypes: string[];
  maxFileSize: number;
  sanitizeHtml: boolean;
  blockSqlInjection: boolean;
  blockNoSqlInjection: boolean;
  blockXss: boolean;
  validateEmails: boolean;
  validateUrls: boolean;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedData?: any;
}

@Injectable()
export class RequestValidationMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RequestValidationMiddleware.name);
  private readonly config: ValidationConfig;

  // Common attack patterns
  private readonly sqlInjectionPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(\'|\")(\s*)(UNION|SELECT|INSERT|UPDATE|DELETE)/i,
    /(\/\*[\s\S]*?\*\/)/,
    /(\-\-)/,
    /(;(\s*)DROP)/i,
  ];

  private readonly nosqlInjectionPatterns = [
    /\$where/i,
    /\$ne/i,
    /\$gt/i,
    /\$lt/i,
    /\$regex/i,
    /\$exists/i,
    /\$type/i,
    /\$in.*\(/i,
    /\$nin.*\(/i,
  ];

  private readonly xssPatterns = [
    /<script[\s\S]*?>[\s\S]*?<\/script>/gi,
    /<iframe[\s\S]*?>[\s\S]*?<\/iframe>/gi,
    /<object[\s\S]*?>[\s\S]*?<\/object>/gi,
    /<embed[\s\S]*?>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /on\w+\s*=/gi,
  ];

  constructor(private readonly configService: ConfigService) {
    this.config = this.buildValidationConfig();
  }

  use(req: Request, res: Response, next: NextFunction): void {
    try {
      const validationResult = this.validateRequest(req);
      
      if (!validationResult.isValid) {
        this.handleValidationError(req, res, validationResult.errors);
        return;
      }

      // Apply sanitized data if available
      if (validationResult.sanitizedData) {
        req.body = validationResult.sanitizedData;
      }

      next();
    } catch (error) {
      this.logger.error('Request validation error:', error);
      this.handleValidationError(req, res, ['Internal validation error']);
    }
  }

  private buildValidationConfig(): ValidationConfig {
    return {
      maxBodySize: this.configService.get('VALIDATION_MAX_BODY_SIZE', 10 * 1024 * 1024), // 10MB
      maxUrlLength: this.configService.get('VALIDATION_MAX_URL_LENGTH', 2048),
      maxHeaderSize: this.configService.get('VALIDATION_MAX_HEADER_SIZE', 8192),
      allowedFileTypes: this.configService.get('VALIDATION_ALLOWED_FILE_TYPES', [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'text/plain',
        'application/json',
      ]),
      maxFileSize: this.configService.get('VALIDATION_MAX_FILE_SIZE', 50 * 1024 * 1024), // 50MB
      sanitizeHtml: this.configService.get('VALIDATION_SANITIZE_HTML', true),
      blockSqlInjection: this.configService.get('VALIDATION_BLOCK_SQL_INJECTION', true),
      blockNoSqlInjection: this.configService.get('VALIDATION_BLOCK_NOSQL_INJECTION', true),
      blockXss: this.configService.get('VALIDATION_BLOCK_XSS', true),
      validateEmails: this.configService.get('VALIDATION_VALIDATE_EMAILS', true),
      validateUrls: this.configService.get('VALIDATION_VALIDATE_URLS', true),
    };
  }

  private validateRequest(req: Request): ValidationResult {
    const errors: string[] = [];

    // Validate URL length
    if (req.url.length > this.config.maxUrlLength) {
      errors.push(`URL length exceeds maximum allowed (${this.config.maxUrlLength})`);
    }

    // Validate headers
    const headerValidation = this.validateHeaders(req.headers);
    if (!headerValidation.isValid) {
      errors.push(...headerValidation.errors);
    }

    // Validate query parameters
    const queryValidation = this.validateQueryParams(req.query);
    if (!queryValidation.isValid) {
      errors.push(...queryValidation.errors);
    }

    // Validate request body
    let sanitizedBody = req.body;
    if (req.body && Object.keys(req.body).length > 0) {
      const bodyValidation = this.validateBody(req.body);
      if (!bodyValidation.isValid) {
        errors.push(...bodyValidation.errors);
      } else {
        sanitizedBody = bodyValidation.sanitizedData || req.body;
      }
    }

    // Validate file uploads
    if ((req as any).files || (req as any).file) {
      const fileValidation = this.validateFiles(req);
      if (!fileValidation.isValid) {
        errors.push(...fileValidation.errors);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: sanitizedBody,
    };
  }

  private validateHeaders(headers: any): ValidationResult {
    const errors: string[] = [];
    
    // Check header size
    const headerSize = JSON.stringify(headers).length;
    if (headerSize > this.config.maxHeaderSize) {
      errors.push(`Headers size exceeds maximum allowed (${this.config.maxHeaderSize})`);
    }

    // Validate specific headers
    if (headers['user-agent'] && headers['user-agent'].length > 1000) {
      errors.push('User-Agent header is too long');
    }

    // Check for suspicious header values
    for (const [key, value] of Object.entries(headers)) {
      if (typeof value === 'string') {
        if (this.containsSqlInjection(value)) {
          errors.push(`SQL injection detected in header: ${key}`);
        }
        if (this.containsXss(value)) {
          errors.push(`XSS attempt detected in header: ${key}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private validateQueryParams(query: any): ValidationResult {
    const errors: string[] = [];

    for (const [key, value] of Object.entries(query)) {
      if (typeof value === 'string') {
        // Check for injection attacks
        if (this.config.blockSqlInjection && this.containsSqlInjection(value)) {
          errors.push(`SQL injection detected in query parameter: ${key}`);
        }

        if (this.config.blockNoSqlInjection && this.containsNoSqlInjection(value)) {
          errors.push(`NoSQL injection detected in query parameter: ${key}`);
        }

        if (this.config.blockXss && this.containsXss(value)) {
          errors.push(`XSS attempt detected in query parameter: ${key}`);
        }

        // Validate specific parameter types
        if (key === 'email' && this.config.validateEmails && !validator.isEmail(value)) {
          errors.push(`Invalid email format in parameter: ${key}`);
        }

        if (key.includes('url') && this.config.validateUrls && !validator.isURL(value, {
          protocols: ['http', 'https'],
          require_protocol: true,
        })) {
          errors.push(`Invalid URL format in parameter: ${key}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private validateBody(body: any): ValidationResult {
    const errors: string[] = [];
    let sanitizedData: any = null;

    try {
      // Deep clone for sanitization
      sanitizedData = JSON.parse(JSON.stringify(body));

      const validation = this.validateObjectRecursively(sanitizedData, 'body');
      if (!validation.isValid) {
        errors.push(...validation.errors);
      }

      // Additional body-level validations
      const bodySize = JSON.stringify(body).length;
      if (bodySize > this.config.maxBodySize) {
        errors.push(`Request body size exceeds maximum allowed (${this.config.maxBodySize})`);
      }

    } catch (error) {
      errors.push('Invalid JSON format in request body');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: errors.length === 0 ? sanitizedData : null,
    };
  }

  private validateObjectRecursively(obj: any, path: string): ValidationResult {
    const errors: string[] = [];

    if (typeof obj === 'string') {
      // SQL injection check
      if (this.config.blockSqlInjection && this.containsSqlInjection(obj)) {
        errors.push(`SQL injection detected in field: ${path}`);
      }

      // NoSQL injection check
      if (this.config.blockNoSqlInjection && this.containsNoSqlInjection(obj)) {
        errors.push(`NoSQL injection detected in field: ${path}`);
      }

      // XSS check
      if (this.config.blockXss && this.containsXss(obj)) {
        errors.push(`XSS attempt detected in field: ${path}`);
      }

      // HTML sanitization
      if (this.config.sanitizeHtml && this.containsHtml(obj)) {
        try {
          const sanitized = DOMPurify.sanitize(obj, {
            ALLOWED_TAGS: [],
            ALLOWED_ATTR: [],
          });
          
          // Update the value in place
          if (sanitized !== obj) {
            this.setObjectPath(obj, path, sanitized);
          }
        } catch (sanitizeError) {
          this.logger.warn(`HTML sanitization failed for field: ${path}`, sanitizeError);
        }
      }
    } else if (Array.isArray(obj)) {
      obj.forEach((item, index) => {
        const itemValidation = this.validateObjectRecursively(item, `${path}[${index}]`);
        if (!itemValidation.isValid) {
          errors.push(...itemValidation.errors);
        }
      });
    } else if (obj && typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        const fieldValidation = this.validateObjectRecursively(obj[key], `${path}.${key}`);
        if (!fieldValidation.isValid) {
          errors.push(...fieldValidation.errors);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private validateFiles(req: Request): ValidationResult {
    const errors: string[] = [];
    const files = (req as any).files || [(req as any).file];

    if (!files) {
      return { isValid: true, errors: [] };
    }

    const fileArray = Array.isArray(files) ? files : [files];

    fileArray.forEach((file, index) => {
      if (!file) return;

      // Check file size
      if (file.size > this.config.maxFileSize) {
        errors.push(`File ${index} size exceeds maximum allowed (${this.config.maxFileSize})`);
      }

      // Check file type
      if (!this.config.allowedFileTypes.includes(file.mimetype)) {
        errors.push(`File ${index} type not allowed: ${file.mimetype}`);
      }

      // Check filename for path traversal
      if (file.originalname && file.originalname.includes('..')) {
        errors.push(`File ${index} name contains path traversal attempt`);
      }

      // Additional security checks for uploaded files
      if (file.buffer) {
        const fileContent = file.buffer.toString('utf8', 0, Math.min(file.buffer.length, 1024));
        
        if (this.containsSqlInjection(fileContent) || this.containsXss(fileContent)) {
          errors.push(`File ${index} contains potentially malicious content`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private containsSqlInjection(input: string): boolean {
    return this.sqlInjectionPatterns.some(pattern => pattern.test(input));
  }

  private containsNoSqlInjection(input: string): boolean {
    return this.nosqlInjectionPatterns.some(pattern => pattern.test(input));
  }

  private containsXss(input: string): boolean {
    return this.xssPatterns.some(pattern => pattern.test(input));
  }

  private containsHtml(input: string): boolean {
    return /<[^>]*>/g.test(input);
  }

  private setObjectPath(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current)) {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  private handleValidationError(req: Request, res: Response, errors: string[]): void {
    // Log security event
    this.logger.warn(
      `Request validation failed from IP ${req.ip}: ${errors.join(', ')}`,
      {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        path: req.path,
        method: req.method,
        errors,
      },
    );

    res.status(HttpStatus.BAD_REQUEST).json({
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: 'Request validation failed',
      timestamp: new Date().toISOString(),
      path: req.url,
    });
  }

  // Public methods for configuration

  updateAllowedFileTypes(types: string[]): void {
    this.config.allowedFileTypes = types;
    this.logger.log(`Updated allowed file types: ${types.join(', ')}`);
  }

  addAllowedFileType(type: string): void {
    if (!this.config.allowedFileTypes.includes(type)) {
      this.config.allowedFileTypes.push(type);
      this.logger.log(`Added allowed file type: ${type}`);
    }
  }

  removeAllowedFileType(type: string): void {
    const index = this.config.allowedFileTypes.indexOf(type);
    if (index > -1) {
      this.config.allowedFileTypes.splice(index, 1);
      this.logger.log(`Removed allowed file type: ${type}`);
    }
  }

  updateMaxFileSize(size: number): void {
    this.config.maxFileSize = size;
    this.logger.log(`Updated max file size: ${size}`);
  }

  getValidationConfig(): ValidationConfig {
    return { ...this.config };
  }
}