/**
 * 🛡️ Advanced Rate Limiting Middleware
 * 
 * Enterprise-grade rate limiting with:
 * - Multiple rate limiting strategies (sliding window, token bucket)
 * - User-specific and IP-based limiting
 * - Dynamic rate adjustment based on user tier
 * - Redis-backed distributed rate limiting
 * - Comprehensive monitoring and alerting
 */

import { Injectable, NestMiddleware, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';

interface RateLimitRule {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
  handler?: (req: Request, res: Response) => void;
}

interface UserTierLimits {
  [tier: string]: RateLimitRule;
}

@Injectable()
export class AdvancedRateLimitMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AdvancedRateLimitMiddleware.name);
  
  private readonly defaultLimits: RateLimitRule = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  };

  private readonly userTierLimits: UserTierLimits = {
    free: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100,
    },
    premium: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 1000,
    },
    enterprise: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 10000,
    },
  };

  private readonly endpointLimits: { [endpoint: string]: RateLimitRule } = {
    '/auth/login': {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5, // Strict limit for login attempts
    },
    '/auth/register': {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3, // Very strict for registration
    },
    '/auth/forgot-password': {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3,
    },
    '/upload': {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10,
    },
  };

  constructor(
    @InjectRedis() private readonly redis: Redis,
    private readonly configService: ConfigService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const result = await this.checkRateLimit(req);
      
      if (!result.allowed) {
        await this.handleRateLimitExceeded(req, res, result);
        return;
      }

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': result.limit.toString(),
        'X-RateLimit-Remaining': Math.max(0, result.limit - result.current).toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
        'X-RateLimit-Window': result.windowMs.toString(),
      });

      next();
    } catch (error) {
      this.logger.error('Rate limiting error:', error);
      // Fail open - don't block requests if rate limiter fails
      next();
    }
  }

  private async checkRateLimit(req: Request): Promise<{
    allowed: boolean;
    current: number;
    limit: number;
    resetTime: number;
    windowMs: number;
  }> {
    const limits = this.getLimitsForRequest(req);
    const key = this.generateKey(req, limits);
    
    // Use sliding window log algorithm for more accurate rate limiting
    const now = Date.now();
    const windowStart = now - limits.windowMs;

    // Remove expired entries and count current requests
    const pipeline = this.redis.pipeline();
    pipeline.zremrangebyscore(key, '-inf', windowStart);
    pipeline.zcard(key);
    pipeline.expire(key, Math.ceil(limits.windowMs / 1000));
    
    const results = await pipeline.exec();
    const current = results[1][1] as number;

    const allowed = current < limits.maxRequests;
    
    if (allowed) {
      // Add current request to the window
      await this.redis.zadd(key, now, `${now}-${Math.random()}`);
    }

    // Log rate limit metrics for monitoring
    await this.logRateLimitMetrics(req, {
      key,
      current,
      limit: limits.maxRequests,
      allowed,
      windowMs: limits.windowMs,
    });

    return {
      allowed,
      current: allowed ? current + 1 : current,
      limit: limits.maxRequests,
      resetTime: windowStart + limits.windowMs,
      windowMs: limits.windowMs,
    };
  }

  private getLimitsForRequest(req: Request): RateLimitRule {
    const endpoint = this.getEndpointPattern(req.path);
    
    // Check for endpoint-specific limits first
    if (this.endpointLimits[endpoint]) {
      return { ...this.defaultLimits, ...this.endpointLimits[endpoint] };
    }

    // Check for user tier limits
    const user = (req as any).user;
    if (user?.tier && this.userTierLimits[user.tier]) {
      return { ...this.defaultLimits, ...this.userTierLimits[user.tier] };
    }

    return this.defaultLimits;
  }

  private generateKey(req: Request, limits: RateLimitRule): string {
    if (limits.keyGenerator) {
      return `rate_limit:${limits.keyGenerator(req)}`;
    }

    const user = (req as any).user;
    const userId = user?.id;
    const ip = this.getClientIp(req);
    
    // Use user ID if authenticated, otherwise IP
    const identifier = userId || ip;
    const endpoint = this.getEndpointPattern(req.path);
    
    return `rate_limit:${endpoint}:${identifier}`;
  }

  private getEndpointPattern(path: string): string {
    // Normalize paths to patterns for consistent rate limiting
    return path
      .replace(/\/\d+/g, '/:id') // Replace numeric IDs
      .replace(/\/[a-f0-9-]{36}/g, '/:uuid') // Replace UUIDs
      .toLowerCase();
  }

  private getClientIp(req: Request): string {
    return (
      req.headers['cf-connecting-ip'] ||
      req.headers['x-real-ip'] ||
      req.headers['x-forwarded-for'] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    ) as string;
  }

  private async handleRateLimitExceeded(
    req: Request,
    res: Response,
    result: { limit: number; current: number; resetTime: number; windowMs: number },
  ): Promise<void> {
    const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);

    // Log security event for potential abuse
    await this.logSecurityEvent(req, {
      type: 'rate_limit_exceeded',
      details: {
        limit: result.limit,
        current: result.current,
        retryAfter,
      },
    });

    res.status(HttpStatus.TOO_MANY_REQUESTS);
    res.set({
      'Retry-After': retryAfter.toString(),
      'X-RateLimit-Limit': result.limit.toString(),
      'X-RateLimit-Remaining': '0',
      'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
    });

    res.json({
      statusCode: HttpStatus.TOO_MANY_REQUESTS,
      error: 'Too Many Requests',
      message: `Rate limit exceeded. Try again in ${retryAfter} seconds.`,
      retryAfter,
    });
  }

  private async logRateLimitMetrics(
    req: Request,
    metrics: {
      key: string;
      current: number;
      limit: number;
      allowed: boolean;
      windowMs: number;
    },
  ): Promise<void> {
    const metricsData = {
      timestamp: new Date().toISOString(),
      endpoint: req.path,
      method: req.method,
      ip: this.getClientIp(req),
      userId: (req as any).user?.id,
      userAgent: req.headers['user-agent'],
      ...metrics,
    };

    // Store metrics for monitoring and analytics
    await this.redis.lpush(
      'rate_limit_metrics',
      JSON.stringify(metricsData),
    );

    // Keep only recent metrics (last 24 hours)
    await this.redis.ltrim('rate_limit_metrics', 0, 10000);

    // Alert if usage is high
    if (metrics.current / metrics.limit > 0.8) {
      this.logger.warn(
        `High rate limit usage: ${metrics.current}/${metrics.limit} for ${metrics.key}`,
      );
    }
  }

  private async logSecurityEvent(
    req: Request,
    event: { type: string; details: any },
  ): Promise<void> {
    const securityEvent = {
      timestamp: new Date().toISOString(),
      type: event.type,
      ip: this.getClientIp(req),
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      userId: (req as any).user?.id,
      details: event.details,
    };

    await this.redis.lpush(
      'security_events',
      JSON.stringify(securityEvent),
    );

    this.logger.warn(
      `Security event: ${event.type} from IP ${securityEvent.ip}`,
      securityEvent,
    );
  }

  // Additional methods for rate limit management

  async getRateLimitStatus(identifier: string, endpoint?: string): Promise<any> {
    const pattern = endpoint ? `rate_limit:${endpoint}:${identifier}` : `rate_limit:*:${identifier}`;
    const keys = await this.redis.keys(pattern);
    
    const status = {};
    for (const key of keys) {
      const count = await this.redis.zcard(key);
      const ttl = await this.redis.ttl(key);
      status[key] = { count, ttl };
    }
    
    return status;
  }

  async resetRateLimit(identifier: string, endpoint?: string): Promise<boolean> {
    const pattern = endpoint ? `rate_limit:${endpoint}:${identifier}` : `rate_limit:*:${identifier}`;
    const keys = await this.redis.keys(pattern);
    
    if (keys.length > 0) {
      await this.redis.del(...keys);
      this.logger.log(`Reset rate limits for pattern: ${pattern}`);
      return true;
    }
    
    return false;
  }

  async getMetrics(hours: number = 1): Promise<any[]> {
    const metrics = await this.redis.lrange('rate_limit_metrics', 0, -1);
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    return metrics
      .map(m => JSON.parse(m))
      .filter(m => new Date(m.timestamp) > cutoff)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }
}