/**
 * 🚨 All Exceptions Filter - Global Exception Handler
 * 
 * Implements comprehensive exception handling with:
 * - Standardized error responses
 * - Security-aware error messages
 * - Logging and monitoring
 * - Performance tracking
 */

import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

interface ErrorResponse {
  success: boolean;
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
  method: string;
  correlationId?: string;
  details?: any;
}

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const correlationId = request['correlationId'];
    const { method, url, ip } = request;
    const timestamp = new Date().toISOString();

    let statusCode: number;
    let message: string | string[];
    let errorName: string;
    let details: any;

    // Handle different types of exceptions
    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      const errorResponse = exception.getResponse();
      
      if (typeof errorResponse === 'object' && errorResponse !== null) {
        message = (errorResponse as any).message || exception.message;
        errorName = (errorResponse as any).error || exception.name;
        details = (errorResponse as any).details;
      } else {
        message = errorResponse as string;
        errorName = exception.name;
      }
    } else if (exception instanceof Error) {
      statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      message = process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : exception.message;
      errorName = 'InternalServerError';
      
      if (process.env.NODE_ENV === 'development') {
        details = {
          stack: exception.stack,
          name: exception.name,
        };
      }
    } else {
      statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      message = 'Unknown error occurred';
      errorName = 'UnknownError';
      
      if (process.env.NODE_ENV === 'development') {
        details = { exception: String(exception) };
      }
    }

    // Create standardized error response
    const errorResponse: ErrorResponse = {
      success: false,
      statusCode,
      message,
      error: errorName,
      timestamp,
      path: url,
      method,
      correlationId,
    };

    // Add details for development environment
    if (details && process.env.NODE_ENV === 'development') {
      errorResponse.details = details;
    }

    // Log error with appropriate level
    const logData = {
      message: 'Exception caught by global filter',
      correlationId,
      method,
      url,
      ip,
      statusCode,
      errorName,
      errorMessage: exception instanceof Error ? exception.message : String(exception),
      timestamp,
    };

    if (statusCode >= 500) {
      // Server errors - log as error with stack trace
      this.logger.error({
        ...logData,
        stack: exception instanceof Error ? exception.stack : undefined,
      });
    } else if (statusCode >= 400) {
      // Client errors - log as warning
      this.logger.warn(logData);
    } else {
      // Other errors - log as info
      this.logger.log(logData);
    }

    // Send response
    response.status(statusCode).json(errorResponse);
  }
}
