/**
 * 📊 Prometheus Metrics Service - Enterprise Monitoring and Observability
 * 
 * Implements comprehensive metrics collection with:
 * - Custom business metrics
 * - Performance monitoring
 * - Error tracking and alerting
 * - Resource utilization monitoring
 * - SLA and SLO tracking
 */

import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as promClient from 'prom-client';

@Injectable()
export class PrometheusService implements OnModuleInit {
  private readonly register: promClient.Registry;
  private readonly prefix: string;

  // ================================
  // 📊 Core Metrics
  // ================================
  
  // HTTP Request Metrics
  private readonly httpRequestDuration: promClient.Histogram;
  private readonly httpRequestTotal: promClient.Counter;
  private readonly httpRequestSize: promClient.Histogram;
  private readonly httpResponseSize: promClient.Histogram;
  
  // Database Metrics
  private readonly dbConnectionsActive: promClient.Gauge;
  private readonly dbConnectionsIdle: promClient.Gauge;
  private readonly dbQueryDuration: promClient.Histogram;
  private readonly dbQueryTotal: promClient.Counter;
  
  // Cache Metrics
  private readonly cacheHitTotal: promClient.Counter;
  private readonly cacheMissTotal: promClient.Counter;
  private readonly cacheOperationDuration: promClient.Histogram;
  
  // Business Metrics
  private readonly userRegistrations: promClient.Counter;
  private readonly userLogins: promClient.Counter;
  private readonly apiCallsTotal: promClient.Counter;
  private readonly aiRequestsTotal: promClient.Counter;
  private readonly aiRequestDuration: promClient.Histogram;
  private readonly aiTokensUsed: promClient.Counter;
  
  // System Metrics
  private readonly memoryUsage: promClient.Gauge;
  private readonly cpuUsage: promClient.Gauge;
  private readonly activeConnections: promClient.Gauge;
  private readonly errorRate: promClient.Gauge;

  constructor(private readonly configService: ConfigService) {
    this.register = new promClient.Registry();
    this.prefix = this.configService.get<string>('monitoring.prometheus.prefix', 'api_gateway_');

    // ================================
    // 🔧 Initialize Metrics
    // ================================

    // HTTP Request Metrics
    this.httpRequestDuration = new promClient.Histogram({
      name: `${this.prefix}http_request_duration_seconds`,
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route', 'status_code', 'user_id'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
      registers: [this.register],
    });

    this.httpRequestTotal = new promClient.Counter({
      name: `${this.prefix}http_requests_total`,
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code', 'user_id'],
      registers: [this.register],
    });

    this.httpRequestSize = new promClient.Histogram({
      name: `${this.prefix}http_request_size_bytes`,
      help: 'Size of HTTP requests in bytes',
      labelNames: ['method', 'route'],
      buckets: [100, 1000, 10000, 100000, 1000000],
      registers: [this.register],
    });

    this.httpResponseSize = new promClient.Histogram({
      name: `${this.prefix}http_response_size_bytes`,
      help: 'Size of HTTP responses in bytes',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [100, 1000, 10000, 100000, 1000000],
      registers: [this.register],
    });

    // Database Metrics
    this.dbConnectionsActive = new promClient.Gauge({
      name: `${this.prefix}db_connections_active`,
      help: 'Number of active database connections',
      registers: [this.register],
    });

    this.dbConnectionsIdle = new promClient.Gauge({
      name: `${this.prefix}db_connections_idle`,
      help: 'Number of idle database connections',
      registers: [this.register],
    });

    this.dbQueryDuration = new promClient.Histogram({
      name: `${this.prefix}db_query_duration_seconds`,
      help: 'Duration of database queries in seconds',
      labelNames: ['operation', 'table', 'success'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5],
      registers: [this.register],
    });

    this.dbQueryTotal = new promClient.Counter({
      name: `${this.prefix}db_queries_total`,
      help: 'Total number of database queries',
      labelNames: ['operation', 'table', 'success'],
      registers: [this.register],
    });

    // Cache Metrics
    this.cacheHitTotal = new promClient.Counter({
      name: `${this.prefix}cache_hits_total`,
      help: 'Total number of cache hits',
      labelNames: ['cache_type', 'key_pattern'],
      registers: [this.register],
    });

    this.cacheMissTotal = new promClient.Counter({
      name: `${this.prefix}cache_misses_total`,
      help: 'Total number of cache misses',
      labelNames: ['cache_type', 'key_pattern'],
      registers: [this.register],
    });

    this.cacheOperationDuration = new promClient.Histogram({
      name: `${this.prefix}cache_operation_duration_seconds`,
      help: 'Duration of cache operations in seconds',
      labelNames: ['operation', 'cache_type'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
      registers: [this.register],
    });

    // Business Metrics
    this.userRegistrations = new promClient.Counter({
      name: `${this.prefix}user_registrations_total`,
      help: 'Total number of user registrations',
      labelNames: ['source', 'success'],
      registers: [this.register],
    });

    this.userLogins = new promClient.Counter({
      name: `${this.prefix}user_logins_total`,
      help: 'Total number of user logins',
      labelNames: ['method', 'success'],
      registers: [this.register],
    });

    this.apiCallsTotal = new promClient.Counter({
      name: `${this.prefix}api_calls_total`,
      help: 'Total number of API calls',
      labelNames: ['endpoint', 'user_id', 'success'],
      registers: [this.register],
    });

    this.aiRequestsTotal = new promClient.Counter({
      name: `${this.prefix}ai_requests_total`,
      help: 'Total number of AI service requests',
      labelNames: ['service_type', 'model', 'user_id', 'success'],
      registers: [this.register],
    });

    this.aiRequestDuration = new promClient.Histogram({
      name: `${this.prefix}ai_request_duration_seconds`,
      help: 'Duration of AI service requests in seconds',
      labelNames: ['service_type', 'model'],
      buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60],
      registers: [this.register],
    });

    this.aiTokensUsed = new promClient.Counter({
      name: `${this.prefix}ai_tokens_used_total`,
      help: 'Total number of AI tokens used',
      labelNames: ['service_type', 'model', 'user_id'],
      registers: [this.register],
    });

    // System Metrics
    this.memoryUsage = new promClient.Gauge({
      name: `${this.prefix}memory_usage_bytes`,
      help: 'Memory usage in bytes',
      labelNames: ['type'],
      registers: [this.register],
    });

    this.cpuUsage = new promClient.Gauge({
      name: `${this.prefix}cpu_usage_percent`,
      help: 'CPU usage percentage',
      registers: [this.register],
    });

    this.activeConnections = new promClient.Gauge({
      name: `${this.prefix}active_connections`,
      help: 'Number of active connections',
      labelNames: ['type'],
      registers: [this.register],
    });

    this.errorRate = new promClient.Gauge({
      name: `${this.prefix}error_rate`,
      help: 'Error rate percentage',
      labelNames: ['service', 'error_type'],
      registers: [this.register],
    });
  }

  async onModuleInit() {
    // Register default metrics
    promClient.collectDefaultMetrics({
      register: this.register,
      prefix: this.prefix,
    });

    // Start system metrics collection
    this.startSystemMetricsCollection();
  }

  // ================================
  // 📊 Metric Recording Methods
  // ================================

  recordHttpRequest(
    method: string,
    route: string,
    statusCode: number,
    duration: number,
    requestSize?: number,
    responseSize?: number,
    userId?: string,
  ) {
    const labels = { method, route, status_code: statusCode.toString(), user_id: userId || 'anonymous' };
    
    this.httpRequestDuration.observe(labels, duration);
    this.httpRequestTotal.inc(labels);
    
    if (requestSize) {
      this.httpRequestSize.observe({ method, route }, requestSize);
    }
    
    if (responseSize) {
      this.httpResponseSize.observe({ method, route, status_code: statusCode.toString() }, responseSize);
    }
  }

  recordDatabaseQuery(
    operation: string,
    table: string,
    duration: number,
    success: boolean,
  ) {
    const labels = { operation, table, success: success.toString() };
    
    this.dbQueryDuration.observe(labels, duration);
    this.dbQueryTotal.inc(labels);
  }

  recordCacheOperation(
    operation: 'hit' | 'miss' | 'set' | 'delete',
    cacheType: string,
    keyPattern?: string,
    duration?: number,
  ) {
    const labels = { cache_type: cacheType, key_pattern: keyPattern || 'unknown' };
    
    if (operation === 'hit') {
      this.cacheHitTotal.inc(labels);
    } else if (operation === 'miss') {
      this.cacheMissTotal.inc(labels);
    }
    
    if (duration) {
      this.cacheOperationDuration.observe({ operation, cache_type: cacheType }, duration);
    }
  }

  recordUserRegistration(source: string, success: boolean) {
    this.userRegistrations.inc({ source, success: success.toString() });
  }

  recordUserLogin(method: string, success: boolean) {
    this.userLogins.inc({ method, success: success.toString() });
  }

  recordApiCall(endpoint: string, userId: string, success: boolean) {
    this.apiCallsTotal.inc({ endpoint, user_id: userId, success: success.toString() });
  }

  recordAiRequest(
    serviceType: string,
    model: string,
    duration: number,
    tokensUsed: number,
    userId: string,
    success: boolean,
  ) {
    const labels = { service_type: serviceType, model, user_id: userId };
    
    this.aiRequestsTotal.inc({ ...labels, success: success.toString() });
    this.aiRequestDuration.observe({ service_type: serviceType, model }, duration);
    this.aiTokensUsed.inc(labels, tokensUsed);
  }

  updateDatabaseConnections(active: number, idle: number) {
    this.dbConnectionsActive.set(active);
    this.dbConnectionsIdle.set(idle);
  }

  updateSystemMetrics(memoryUsed: number, memoryTotal: number, cpuUsage: number) {
    this.memoryUsage.set({ type: 'used' }, memoryUsed);
    this.memoryUsage.set({ type: 'total' }, memoryTotal);
    this.cpuUsage.set(cpuUsage);
  }

  updateActiveConnections(type: string, count: number) {
    this.activeConnections.set({ type }, count);
  }

  updateErrorRate(service: string, errorType: string, rate: number) {
    this.errorRate.set({ service, error_type: errorType }, rate);
  }

  // ================================
  // 📈 System Metrics Collection
  // ================================

  private startSystemMetricsCollection() {
    setInterval(() => {
      this.collectSystemMetrics();
    }, 10000); // Collect every 10 seconds
  }

  private collectSystemMetrics() {
    const memUsage = process.memoryUsage();
    
    this.memoryUsage.set({ type: 'rss' }, memUsage.rss);
    this.memoryUsage.set({ type: 'heap_used' }, memUsage.heapUsed);
    this.memoryUsage.set({ type: 'heap_total' }, memUsage.heapTotal);
    this.memoryUsage.set({ type: 'external' }, memUsage.external);
  }

  // ================================
  // 📊 Metrics Export
  // ================================

  async getMetrics(): Promise<string> {
    return this.register.metrics();
  }

  getRegister(): promClient.Registry {
    return this.register;
  }

  // ================================
  // 🔧 Utility Methods
  // ================================

  createCustomCounter(name: string, help: string, labelNames?: string[]): promClient.Counter {
    return new promClient.Counter({
      name: `${this.prefix}${name}`,
      help,
      labelNames,
      registers: [this.register],
    });
  }

  createCustomGauge(name: string, help: string, labelNames?: string[]): promClient.Gauge {
    return new promClient.Gauge({
      name: `${this.prefix}${name}`,
      help,
      labelNames,
      registers: [this.register],
    });
  }

  createCustomHistogram(
    name: string,
    help: string,
    labelNames?: string[],
    buckets?: number[],
  ): promClient.Histogram {
    return new promClient.Histogram({
      name: `${this.prefix}${name}`,
      help,
      labelNames,
      buckets,
      registers: [this.register],
    });
  }
}
