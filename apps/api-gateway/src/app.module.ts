/**
 * 🏗️ Application Root Module - Enterprise NestJS Architecture
 * 
 * Implements Clean Architecture with:
 * - Domain-driven design modules
 * - Infrastructure layer separation
 * - Cross-cutting concerns
 * - Configuration management
 */

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { CacheModule } from '@nestjs/cache-manager';
import { HealthModule } from './modules/health/health.module';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { AiServiceModule } from './modules/ai-service/ai-service.module';
import { CommonModule } from './common/common.module';
import { DatabaseModule } from './infrastructure/database/database.module';
import { RedisModule } from './infrastructure/redis/redis.module';
import { LoggerModule } from './infrastructure/logger/logger.module';
import { MetricsModule } from './infrastructure/metrics/metrics.module';
import configuration from './config/configuration';
import { validationSchema } from './config/validation';

@Module({
  imports: [
    // 🔧 Configuration Management
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      validationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: false,
      },
      expandVariables: true,
      cache: true,
    }),

    // 🛡️ Rate Limiting & Throttling
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get<number>('RATE_LIMIT_WINDOW_MS', 900000), // 15 minutes
        limit: configService.get<number>('RATE_LIMIT_MAX_REQUESTS', 100),
        ignoreUserAgents: [/googlebot/gi, /bingbot/gi],
      }),
    }),

    // 🗄️ Caching Layer
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get<number>('CACHE_TTL', 300), // 5 minutes
        max: configService.get<number>('CACHE_MAX_SIZE', 100),
        isGlobal: true,
      }),
    }),

    // 🏗️ Infrastructure Modules
    DatabaseModule,
    RedisModule,
    LoggerModule,
    MetricsModule,

    // 🔧 Common Utilities
    CommonModule,

    // 🏥 Health Monitoring
    HealthModule,

    // 🔐 Authentication & Authorization
    AuthModule,

    // 👥 User Management
    UsersModule,

    // 🤖 AI Service Integration
    AiServiceModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    const environment = this.configService.get<string>('NODE_ENV', 'development');
    const port = this.configService.get<number>('PORT', 3000);
    
    console.log(`🏗️ AppModule initialized for ${environment} environment on port ${port}`);
  }
}
