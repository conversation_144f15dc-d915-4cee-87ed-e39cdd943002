/**
 * 🚀 API Gateway Main Entry Point - Enterprise NestJS Application
 * 
 * Theo knowledge base về:
 * - Clean Architecture: Interface layer
 * - Security by Design: HTTPS, CORS, Rate limiting
 * - Observability: Logging, metrics, health checks
 * - Performance: Compression, caching
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import compression from 'compression';
import { AppModule } from './app.module';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { ErrorsInterceptor } from './common/interceptors/errors.interceptor';
import { TimeoutInterceptor } from './common/interceptors/timeout.interceptor';

/**
 * Bootstrap Application
 * Theo Enterprise Application patterns
 */
async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    // Create NestJS application
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    const configService = app.get(ConfigService);
    const port = configService.get<number>('PORT', 3000);
    const environment = configService.get<string>('NODE_ENV', 'development');

    // 🔒 Security Middleware - Defense in Depth
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
    }));

    // 🗜️ Performance Optimization
    app.use(compression());

    // 🌐 CORS Configuration
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', 'http://localhost:3001'),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      credentials: true,
    });

    // 🔍 Global Validation Pipe - Input Validation
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true, // Strip unknown properties
        forbidNonWhitelisted: true, // Throw error for unknown properties
        transform: true, // Auto-transform payloads
        transformOptions: {
          enableImplicitConversion: true,
        },
        disableErrorMessages: environment === 'production',
      }),
    );

    // 📊 Global Interceptors - Cross-cutting concerns
    app.useGlobalInterceptors(
      new LoggingInterceptor(),
      new ErrorsInterceptor(),
      new TimeoutInterceptor(30000), // 30 second timeout
    );

    // 📚 API Documentation - OpenAPI/Swagger
    if (environment !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('Enterprise API Gateway')
        .setDescription(`
          🏗️ Enterprise-grade API Gateway implementing:
          
          **Architecture Patterns:**
          - Clean Architecture with DDD
          - CQRS with Event Sourcing
          - Microservices Communication
          - API Gateway Pattern
          
          **Security Features:**
          - OAuth2 + JWT Authentication
          - Multi-layered Access Control (RBAC, ABAC, ReBAC)
          - Rate Limiting & Throttling
          - Input Validation & Sanitization
          
          **Performance Features:**
          - Response Caching
          - Request/Response Compression
          - Connection Pooling
          - Load Balancing
          
          **Observability:**
          - Structured Logging
          - Distributed Tracing
          - Metrics Collection
          - Health Checks
        `)
        .setVersion('1.0.0')
        .addBearerAuth(
          {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
          },
          'JWT-auth',
        )
        .addTag('Authentication', 'User authentication and authorization')
        .addTag('Users', 'User management operations')
        .addTag('Health', 'Application health and monitoring')
        .addTag('GraphQL', 'GraphQL API endpoint')
        .addServer(`http://localhost:${port}`, 'Development server')
        .addServer('https://api.enterprise.com', 'Production server')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          displayRequestDuration: true,
          docExpansion: 'none',
          filter: true,
          showRequestHeaders: true,
        },
        customSiteTitle: 'Enterprise API Gateway - Documentation',
        customfavIcon: '/favicon.ico',
        customCss: `
          .swagger-ui .topbar { display: none }
          .swagger-ui .info .title { color: #1890ff }
        `,
      });

      logger.log(`📚 API Documentation available at: http://localhost:${port}/api/docs`);
    }

    // 🏥 Health Check Endpoint
    app.enableShutdownHooks();

    // 🚀 Start Server
    await app.listen(port, '0.0.0.0');

    logger.log(`🚀 API Gateway started successfully!`);
    logger.log(`🌍 Environment: ${environment}`);
    logger.log(`🔗 Server running on: http://localhost:${port}`);
    logger.log(`📊 Health check: http://localhost:${port}/health`);
    logger.log(`🎯 GraphQL Playground: http://localhost:${port}/graphql`);
    
    // 📈 Performance Monitoring
    if (environment === 'production') {
      logger.log(`📈 Metrics endpoint: http://localhost:${port}/metrics`);
    }

  } catch (error) {
    logger.error('❌ Failed to start application', error);
    process.exit(1);
  }
}

/**
 * Graceful Shutdown Handler
 * Theo Operational Excellence practices
 */
process.on('SIGTERM', () => {
  const logger = new Logger('Shutdown');
  logger.log('🛑 SIGTERM received, shutting down gracefully...');
});

process.on('SIGINT', () => {
  const logger = new Logger('Shutdown');
  logger.log('🛑 SIGINT received, shutting down gracefully...');
});

process.on('unhandledRejection', (reason, promise) => {
  const logger = new Logger('UnhandledRejection');
  logger.error('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  const logger = new Logger('UncaughtException');
  logger.error('🚨 Uncaught Exception:', error);
  process.exit(1);
});

// Start the application
bootstrap();

/**
 * 🎯 Key Features Implemented:
 * 
 * ✅ **Security by Design**
 *    - Helmet for security headers
 *    - CORS configuration
 *    - Input validation & sanitization
 *    - Rate limiting (configured in modules)
 * 
 * ✅ **Performance Optimization**
 *    - Response compression
 *    - Request timeout handling
 *    - Connection pooling (in database modules)
 * 
 * ✅ **Observability**
 *    - Structured logging
 *    - Request/response interceptors
 *    - Health checks
 *    - API documentation
 * 
 * ✅ **Enterprise Patterns**
 *    - Clean Architecture layers
 *    - Dependency injection
 *    - Configuration management
 *    - Graceful shutdown
 * 
 * ✅ **Developer Experience**
 *    - Auto-generated API docs
 *    - Type safety with TypeScript
 *    - Hot reload in development
 *    - Comprehensive error handling
 */
