/**
 * 🔧 Application Configuration - Environment-based Settings
 * 
 * Centralized configuration management with:
 * - Type-safe environment variables
 * - Default values and validation
 * - Environment-specific overrides
 * - Security-first approach
 */

export default () => ({
  // 🌍 Environment Settings
  environment: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT, 10) || 3000,
  host: process.env.HOST || 'localhost',
  apiPrefix: process.env.API_PREFIX || '/api/v1',

  // 🗄️ Database Configuration
  database: {
    url: process.env.DATABASE_URL,
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
    name: process.env.DATABASE_NAME || 'enterprise_db',
    username: process.env.DATABASE_USER || 'enterprise_user',
    password: process.env.DATABASE_PASSWORD || 'enterprise_password',
    ssl: process.env.DATABASE_SSL === 'true',
    logging: process.env.DATABASE_LOGGING === 'true',
    synchronize: process.env.DATABASE_SYNCHRONIZE === 'true',
    migrationsRun: process.env.DATABASE_MIGRATIONS_RUN === 'true',
    pool: {
      min: parseInt(process.env.DATABASE_POOL_MIN, 10) || 2,
      max: parseInt(process.env.DATABASE_POOL_MAX, 10) || 10,
      idleTimeoutMillis: parseInt(process.env.DATABASE_POOL_IDLE_TIMEOUT, 10) || 30000,
      acquireTimeoutMillis: parseInt(process.env.DATABASE_POOL_ACQUIRE_TIMEOUT, 10) || 60000,
    },
  },

  // 🔴 Redis Configuration
  redis: {
    url: process.env.REDIS_URL,
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB, 10) || 0,
    ttl: parseInt(process.env.REDIS_TTL, 10) || 3600,
    maxRetries: parseInt(process.env.REDIS_MAX_RETRIES, 10) || 3,
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'api-gateway:',
  },

  // 🔐 Authentication & Security
  auth: {
    jwt: {
      secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
      expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key-change-this-in-production',
      refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
      issuer: process.env.JWT_ISSUER || 'enterprise-platform',
      audience: process.env.JWT_AUDIENCE || 'api-gateway',
    },
    apiKey: {
      header: process.env.API_KEY_HEADER || 'x-api-key',
      secret: process.env.API_KEY_SECRET || 'your-api-key-secret-change-this-in-production',
    },
    session: {
      secret: process.env.SESSION_SECRET || 'your-session-secret-change-this-in-production',
      ttl: parseInt(process.env.SESSION_TTL, 10) || 86400,
      store: process.env.SESSION_STORE || 'redis',
    },
  },

  // 🌐 CORS Configuration
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
    methods: process.env.CORS_METHODS?.split(',') || ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    credentials: process.env.CORS_CREDENTIALS === 'true',
    maxAge: parseInt(process.env.CORS_MAX_AGE, 10) || 86400,
  },

  // 🛡️ Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 900000, // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS === 'true',
    skipFailedRequests: process.env.RATE_LIMIT_SKIP_FAILED_REQUESTS === 'true',
  },

  // 🤖 AI Service Configuration
  aiService: {
    url: process.env.AI_SERVICE_URL || 'http://localhost:8000',
    timeout: parseInt(process.env.AI_SERVICE_TIMEOUT, 10) || 30000,
    retries: parseInt(process.env.AI_SERVICE_RETRIES, 10) || 3,
    retryDelay: parseInt(process.env.AI_SERVICE_RETRY_DELAY, 10) || 1000,
    healthCheck: process.env.AI_SERVICE_HEALTH_CHECK || '/health',
    apiPrefix: process.env.AI_SERVICE_API_PREFIX || '/api/v1',
  },

  // 📊 Monitoring & Observability
  monitoring: {
    prometheus: {
      enabled: process.env.PROMETHEUS_ENABLED === 'true',
      port: parseInt(process.env.PROMETHEUS_PORT, 10) || 9090,
      endpoint: process.env.PROMETHEUS_ENDPOINT || '/metrics',
      prefix: process.env.METRICS_PREFIX || 'api_gateway_',
    },
    healthCheck: {
      enabled: process.env.HEALTH_CHECK_ENABLED === 'true',
      endpoint: process.env.HEALTH_CHECK_ENDPOINT || '/health',
      timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT, 10) || 5000,
      interval: parseInt(process.env.HEALTH_CHECK_INTERVAL, 10) || 30000,
    },
    logging: {
      level: process.env.LOG_LEVEL || 'info',
      format: process.env.LOG_FORMAT || 'json',
      timestamp: process.env.LOG_TIMESTAMP === 'true',
      colorize: process.env.LOG_COLORIZE === 'true',
      file: {
        enabled: process.env.LOG_FILE_ENABLED === 'true',
        path: process.env.LOG_FILE_PATH || './logs/api-gateway.log',
        maxSize: process.env.LOG_FILE_MAX_SIZE || '10m',
        maxFiles: parseInt(process.env.LOG_FILE_MAX_FILES, 10) || 5,
      },
    },
    tracing: {
      enabled: process.env.TRACING_ENABLED === 'true',
      jaegerEndpoint: process.env.JAEGER_ENDPOINT || 'http://localhost:14268/api/traces',
      jaegerAgentHost: process.env.JAEGER_AGENT_HOST || 'localhost',
      jaegerAgentPort: parseInt(process.env.JAEGER_AGENT_PORT, 10) || 6832,
    },
  },

  // 🗜️ Performance Configuration
  performance: {
    compression: {
      enabled: process.env.COMPRESSION_ENABLED === 'true',
      level: parseInt(process.env.COMPRESSION_LEVEL, 10) || 6,
      threshold: parseInt(process.env.COMPRESSION_THRESHOLD, 10) || 1024,
    },
    cache: {
      enabled: process.env.CACHE_ENABLED === 'true',
      ttl: parseInt(process.env.CACHE_TTL, 10) || 300,
      maxSize: parseInt(process.env.CACHE_MAX_SIZE, 10) || 100,
      checkPeriod: parseInt(process.env.CACHE_CHECK_PERIOD, 10) || 600,
    },
    keepAlive: {
      timeout: parseInt(process.env.KEEP_ALIVE_TIMEOUT, 10) || 5000,
      headersTimeout: parseInt(process.env.HEADERS_TIMEOUT, 10) || 60000,
    },
  },

  // 🔒 Security Configuration
  security: {
    helmet: {
      enabled: process.env.HELMET_ENABLED === 'true',
      csp: process.env.CSP_ENABLED === 'true',
      hsts: {
        enabled: process.env.HSTS_ENABLED === 'true',
        maxAge: parseInt(process.env.HSTS_MAX_AGE, 10) || 31536000,
        includeSubDomains: process.env.HSTS_INCLUDE_SUBDOMAINS === 'true',
      },
    },
    ssl: {
      enabled: process.env.SSL_ENABLED === 'true',
      certPath: process.env.SSL_CERT_PATH || './ssl/cert.pem',
      keyPath: process.env.SSL_KEY_PATH || './ssl/key.pem',
      caPath: process.env.SSL_CA_PATH || './ssl/ca.pem',
    },
    validation: {
      enabled: process.env.VALIDATION_ENABLED === 'true',
      whitelist: process.env.VALIDATION_WHITELIST === 'true',
      forbidNonWhitelisted: process.env.VALIDATION_FORBID_NON_WHITELISTED === 'true',
      skipMissingProperties: process.env.VALIDATION_SKIP_MISSING_PROPERTIES === 'true',
      transform: process.env.VALIDATION_TRANSFORM === 'true',
    },
  },

  // 🧪 Development & Testing
  development: {
    hotReload: process.env.HOT_RELOAD === 'true',
    watchFiles: process.env.WATCH_FILES === 'true',
    swagger: {
      enabled: process.env.SWAGGER_ENABLED === 'true',
      path: process.env.SWAGGER_PATH || '/docs',
      jsonPath: process.env.SWAGGER_JSON_PATH || '/docs-json',
    },
    debug: {
      sql: process.env.DEBUG_SQL === 'true',
      redis: process.env.DEBUG_REDIS === 'true',
      http: process.env.DEBUG_HTTP === 'true',
      routes: process.env.DEBUG_ROUTES === 'true',
    },
  },

  // 🧪 Testing Configuration
  testing: {
    database: {
      url: process.env.TEST_DATABASE_URL,
    },
    redis: {
      url: process.env.TEST_REDIS_URL,
    },
    port: parseInt(process.env.TEST_PORT, 10) || 3001,
  },
});
