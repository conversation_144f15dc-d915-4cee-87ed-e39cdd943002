/**
 * 🔍 Configuration Validation Schema - Environment Variables Validation
 * 
 * Ensures all required environment variables are present and valid:
 * - Type validation for all config values
 * - Required vs optional variables
 * - Default value enforcement
 * - Security validation
 */

import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  // 🌍 Environment Settings
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test', 'staging')
    .default('development'),
  PORT: Joi.number().port().default(3000),
  HOST: Joi.string().default('localhost'),
  API_PREFIX: Joi.string().default('/api/v1'),

  // 🗄️ Database Configuration
  DATABASE_URL: Joi.string().uri().optional(),
  DATABASE_HOST: Joi.string().default('localhost'),
  DATABASE_PORT: Joi.number().port().default(5432),
  DATABASE_NAME: Joi.string().default('enterprise_db'),
  DATABASE_USER: Joi.string().default('enterprise_user'),
  DATABASE_PASSWORD: Joi.string().min(8).required(),
  DATABASE_SSL: Joi.boolean().default(false),
  DATABASE_LOGGING: Joi.boolean().default(true),
  DATABASE_SYNCHRONIZE: Joi.boolean().default(false),
  DATABASE_MIGRATIONS_RUN: Joi.boolean().default(true),
  DATABASE_POOL_MIN: Joi.number().min(1).default(2),
  DATABASE_POOL_MAX: Joi.number().min(1).default(10),
  DATABASE_POOL_IDLE_TIMEOUT: Joi.number().min(1000).default(30000),
  DATABASE_POOL_ACQUIRE_TIMEOUT: Joi.number().min(1000).default(60000),

  // 🔴 Redis Configuration
  REDIS_URL: Joi.string().uri().optional(),
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().port().default(6379),
  REDIS_PASSWORD: Joi.string().optional(),
  REDIS_DB: Joi.number().min(0).max(15).default(0),
  REDIS_TTL: Joi.number().min(1).default(3600),
  REDIS_MAX_RETRIES: Joi.number().min(0).default(3),
  REDIS_KEY_PREFIX: Joi.string().default('api-gateway:'),

  // 🔐 Authentication & Security
  JWT_SECRET: Joi.string().min(32).required(),
  JWT_EXPIRES_IN: Joi.string().default('24h'),
  JWT_REFRESH_SECRET: Joi.string().min(32).required(),
  JWT_REFRESH_EXPIRES_IN: Joi.string().default('7d'),
  JWT_ISSUER: Joi.string().default('enterprise-platform'),
  JWT_AUDIENCE: Joi.string().default('api-gateway'),
  API_KEY_HEADER: Joi.string().default('x-api-key'),
  API_KEY_SECRET: Joi.string().min(16).required(),
  SESSION_SECRET: Joi.string().min(32).required(),
  SESSION_TTL: Joi.number().min(300).default(86400), // Min 5 minutes
  SESSION_STORE: Joi.string().valid('redis', 'memory').default('redis'),

  // 🌐 CORS Configuration
  CORS_ORIGIN: Joi.string().default('http://localhost:3000,http://localhost:3001'),
  CORS_METHODS: Joi.string().default('GET,POST,PUT,DELETE,PATCH,OPTIONS'),
  CORS_CREDENTIALS: Joi.boolean().default(true),
  CORS_MAX_AGE: Joi.number().min(0).default(86400),

  // 🛡️ Rate Limiting
  RATE_LIMIT_WINDOW_MS: Joi.number().min(1000).default(900000), // Min 1 second
  RATE_LIMIT_MAX_REQUESTS: Joi.number().min(1).default(100),
  RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS: Joi.boolean().default(false),
  RATE_LIMIT_SKIP_FAILED_REQUESTS: Joi.boolean().default(false),

  // 🤖 AI Service Configuration
  AI_SERVICE_URL: Joi.string().uri().default('http://localhost:8000'),
  AI_SERVICE_TIMEOUT: Joi.number().min(1000).default(30000),
  AI_SERVICE_RETRIES: Joi.number().min(0).default(3),
  AI_SERVICE_RETRY_DELAY: Joi.number().min(100).default(1000),
  AI_SERVICE_HEALTH_CHECK: Joi.string().default('/health'),
  AI_SERVICE_API_PREFIX: Joi.string().default('/api/v1'),

  // 📊 Monitoring & Observability
  PROMETHEUS_ENABLED: Joi.boolean().default(true),
  PROMETHEUS_PORT: Joi.number().port().default(9090),
  PROMETHEUS_ENDPOINT: Joi.string().default('/metrics'),
  METRICS_PREFIX: Joi.string().default('api_gateway_'),
  HEALTH_CHECK_ENABLED: Joi.boolean().default(true),
  HEALTH_CHECK_ENDPOINT: Joi.string().default('/health'),
  HEALTH_CHECK_TIMEOUT: Joi.number().min(1000).default(5000),
  HEALTH_CHECK_INTERVAL: Joi.number().min(5000).default(30000),
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug', 'verbose')
    .default('info'),
  LOG_FORMAT: Joi.string().valid('json', 'simple', 'combined').default('json'),
  LOG_TIMESTAMP: Joi.boolean().default(true),
  LOG_COLORIZE: Joi.boolean().default(false),
  LOG_FILE_ENABLED: Joi.boolean().default(false),
  LOG_FILE_PATH: Joi.string().default('./logs/api-gateway.log'),
  LOG_FILE_MAX_SIZE: Joi.string().default('10m'),
  LOG_FILE_MAX_FILES: Joi.number().min(1).default(5),
  TRACING_ENABLED: Joi.boolean().default(true),
  JAEGER_ENDPOINT: Joi.string().uri().optional(),
  JAEGER_AGENT_HOST: Joi.string().default('localhost'),
  JAEGER_AGENT_PORT: Joi.number().port().default(6832),

  // 🗜️ Performance Configuration
  COMPRESSION_ENABLED: Joi.boolean().default(true),
  COMPRESSION_LEVEL: Joi.number().min(1).max(9).default(6),
  COMPRESSION_THRESHOLD: Joi.number().min(0).default(1024),
  CACHE_ENABLED: Joi.boolean().default(true),
  CACHE_TTL: Joi.number().min(1).default(300),
  CACHE_MAX_SIZE: Joi.number().min(1).default(100),
  CACHE_CHECK_PERIOD: Joi.number().min(60).default(600),
  KEEP_ALIVE_TIMEOUT: Joi.number().min(1000).default(5000),
  HEADERS_TIMEOUT: Joi.number().min(1000).default(60000),

  // 🔒 Security Configuration
  HELMET_ENABLED: Joi.boolean().default(true),
  CSP_ENABLED: Joi.boolean().default(true),
  HSTS_ENABLED: Joi.boolean().default(true),
  HSTS_MAX_AGE: Joi.number().min(0).default(31536000),
  HSTS_INCLUDE_SUBDOMAINS: Joi.boolean().default(true),
  SSL_ENABLED: Joi.boolean().default(false),
  SSL_CERT_PATH: Joi.string().default('./ssl/cert.pem'),
  SSL_KEY_PATH: Joi.string().default('./ssl/key.pem'),
  SSL_CA_PATH: Joi.string().default('./ssl/ca.pem'),
  VALIDATION_ENABLED: Joi.boolean().default(true),
  VALIDATION_WHITELIST: Joi.boolean().default(true),
  VALIDATION_FORBID_NON_WHITELISTED: Joi.boolean().default(true),
  VALIDATION_SKIP_MISSING_PROPERTIES: Joi.boolean().default(false),
  VALIDATION_TRANSFORM: Joi.boolean().default(true),

  // 🧪 Development & Testing
  HOT_RELOAD: Joi.boolean().default(true),
  WATCH_FILES: Joi.boolean().default(true),
  SWAGGER_ENABLED: Joi.boolean().default(true),
  SWAGGER_PATH: Joi.string().default('/docs'),
  SWAGGER_JSON_PATH: Joi.string().default('/docs-json'),
  DEBUG_SQL: Joi.boolean().default(false),
  DEBUG_REDIS: Joi.boolean().default(false),
  DEBUG_HTTP: Joi.boolean().default(false),
  DEBUG_ROUTES: Joi.boolean().default(false),
  TEST_DATABASE_URL: Joi.string().uri().optional(),
  TEST_REDIS_URL: Joi.string().uri().optional(),
  TEST_PORT: Joi.number().port().default(3001),

  // 🌐 Service Discovery
  SERVICE_REGISTRY_ENABLED: Joi.boolean().default(true),
  SERVICE_REGISTRY_TTL: Joi.number().min(5000).default(30000),
  SERVICE_REGISTRY_HEARTBEAT: Joi.number().min(1000).default(10000),

  // 🔄 Circuit Breaker
  CIRCUIT_BREAKER_ENABLED: Joi.boolean().default(true),
  CIRCUIT_BREAKER_THRESHOLD: Joi.number().min(1).default(5),
  CIRCUIT_BREAKER_TIMEOUT: Joi.number().min(1000).default(60000),
  CIRCUIT_BREAKER_RESET_TIMEOUT: Joi.number().min(1000).default(30000),

  // 📧 External Services (Optional)
  SMTP_HOST: Joi.string().optional(),
  SMTP_PORT: Joi.number().port().optional(),
  SMTP_SECURE: Joi.boolean().default(false),
  SMTP_USER: Joi.string().email().optional(),
  SMTP_PASSWORD: Joi.string().optional(),
  EMAIL_FROM: Joi.string().email().optional(),

  // 📁 File Storage (Optional)
  STORAGE_TYPE: Joi.string().valid('local', 's3', 'gcs').default('local'),
  STORAGE_PATH: Joi.string().default('./uploads'),
  AWS_S3_BUCKET: Joi.string().optional(),
  AWS_ACCESS_KEY_ID: Joi.string().optional(),
  AWS_SECRET_ACCESS_KEY: Joi.string().optional(),
  AWS_REGION: Joi.string().default('us-east-1'),

  // 🚀 Production Settings
  CLUSTER_MODE: Joi.boolean().default(false),
  WORKER_PROCESSES: Joi.alternatives()
    .try(Joi.number().min(1), Joi.string().valid('auto'))
    .default('auto'),
  BODY_LIMIT: Joi.string().default('10mb'),
  GRACEFUL_SHUTDOWN_TIMEOUT: Joi.number().min(1000).default(10000),
});
