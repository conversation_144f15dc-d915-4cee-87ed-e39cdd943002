/**
 * 🤖 AI Service - AI/ML Integration Service
 * 
 * Implements AI service communication with:
 * - HTTP client with retry logic
 * - Circuit breaker pattern
 * - Request/response transformation
 * - Error handling and logging
 */

import {
  Injectable,
  Logger,
  BadRequestException,
  ServiceUnavailableException,
  InternalServerErrorException,
} from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, timeout, catchError } from 'rxjs';
import { AxiosError } from 'axios';

import { CircuitBreakerService } from './circuit-breaker.service';
import {
  EmbeddingRequestDto,
  ChatCompletionRequestDto,
  SimilaritySearchRequestDto,
  AlgorithmAnalysisRequestDto,
  EmbeddingResponseDto,
  ChatCompletionResponseDto,
  SimilaritySearchResponseDto,
  AlgorithmAnalysisResponseDto,
} from './dto';

@Injectable()
export class AiServiceService {
  private readonly logger = new Logger(AiServiceService.name);
  private readonly baseUrl: string;
  private readonly requestTimeout: number;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly circuitBreaker: CircuitBreakerService,
  ) {
    this.baseUrl = this.configService.get<string>('aiService.url', 'http://localhost:8000');
    this.requestTimeout = this.configService.get<number>('aiService.timeout', 30000);
  }

  /**
   * Generate text embeddings
   */
  async generateEmbeddings(
    request: EmbeddingRequestDto,
    userId: string,
  ): Promise<EmbeddingResponseDto> {
    const startTime = Date.now();
    
    try {
      this.logger.debug('Generating embeddings', {
        userId,
        textLength: request.text.length,
        model: request.model,
      });

      const response = await this.circuitBreaker.execute(async () => {
        return firstValueFrom(
          this.httpService
            .post('/api/v1/embeddings', {
              text: request.text,
              model: request.model,
              normalize: request.normalize,
            })
            .pipe(
              timeout(this.requestTimeout),
              catchError(this.handleHttpError.bind(this)),
            ),
        );
      });

      const duration = Date.now() - startTime;
      
      this.logger.log('Embeddings generated successfully', {
        userId,
        duration,
        dimensions: response.data.dimensions,
        tokens: response.data.usage?.totalTokens,
      });

      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.error('Failed to generate embeddings', {
        userId,
        duration,
        error: error.message,
        textLength: request.text.length,
      });

      throw this.transformError(error);
    }
  }

  /**
   * Generate chat completion
   */
  async chatCompletion(
    request: ChatCompletionRequestDto,
    userId: string,
  ): Promise<ChatCompletionResponseDto> {
    const startTime = Date.now();
    
    try {
      this.logger.debug('Generating chat completion', {
        userId,
        messageCount: request.messages.length,
        model: request.model,
        maxTokens: request.maxTokens,
      });

      const response = await this.circuitBreaker.execute(async () => {
        return firstValueFrom(
          this.httpService
            .post('/api/v1/chat/completions', {
              messages: request.messages,
              model: request.model,
              max_tokens: request.maxTokens,
              temperature: request.temperature,
              top_p: request.topP,
              stream: request.stream,
            })
            .pipe(
              timeout(this.requestTimeout),
              catchError(this.handleHttpError.bind(this)),
            ),
        );
      });

      const duration = Date.now() - startTime;
      
      this.logger.log('Chat completion generated successfully', {
        userId,
        duration,
        model: response.data.model,
        tokens: response.data.usage?.totalTokens,
        choices: response.data.choices?.length,
      });

      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.error('Failed to generate chat completion', {
        userId,
        duration,
        error: error.message,
        messageCount: request.messages.length,
      });

      throw this.transformError(error);
    }
  }

  /**
   * Perform similarity search
   */
  async similaritySearch(
    request: SimilaritySearchRequestDto,
    userId: string,
  ): Promise<SimilaritySearchResponseDto> {
    const startTime = Date.now();
    
    try {
      this.logger.debug('Performing similarity search', {
        userId,
        queryLength: request.query.length,
        topK: request.topK,
        threshold: request.threshold,
      });

      const response = await this.circuitBreaker.execute(async () => {
        return firstValueFrom(
          this.httpService
            .post('/api/v1/search/similarity', {
              query: request.query,
              top_k: request.topK,
              threshold: request.threshold,
              filters: request.filters,
            })
            .pipe(
              timeout(this.requestTimeout),
              catchError(this.handleHttpError.bind(this)),
            ),
        );
      });

      const duration = Date.now() - startTime;
      
      this.logger.log('Similarity search completed successfully', {
        userId,
        duration,
        resultsCount: response.data.results?.length,
        totalResults: response.data.totalResults,
      });

      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.error('Failed to perform similarity search', {
        userId,
        duration,
        error: error.message,
        queryLength: request.query.length,
      });

      throw this.transformError(error);
    }
  }

  /**
   * Analyze algorithm
   */
  async analyzeAlgorithm(
    request: AlgorithmAnalysisRequestDto,
    userId: string,
  ): Promise<AlgorithmAnalysisResponseDto> {
    const startTime = Date.now();
    
    try {
      this.logger.debug('Analyzing algorithm', {
        userId,
        codeLength: request.code.length,
        language: request.language,
        analysisType: request.analysisType,
      });

      const response = await this.circuitBreaker.execute(async () => {
        return firstValueFrom(
          this.httpService
            .post('/api/v1/analyze/algorithm', {
              code: request.code,
              language: request.language,
              analysis_type: request.analysisType,
            })
            .pipe(
              timeout(this.requestTimeout * 2), // Algorithm analysis may take longer
              catchError(this.handleHttpError.bind(this)),
            ),
        );
      });

      const duration = Date.now() - startTime;
      
      this.logger.log('Algorithm analysis completed successfully', {
        userId,
        duration,
        language: request.language,
        patternsFound: response.data.patterns?.length,
        optimizationsFound: response.data.optimizations?.length,
      });

      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.error('Failed to analyze algorithm', {
        userId,
        duration,
        error: error.message,
        language: request.language,
        codeLength: request.code.length,
      });

      throw this.transformError(error);
    }
  }

  /**
   * Get available models
   */
  async getAvailableModels(userId: string): Promise<any> {
    try {
      this.logger.debug('Fetching available models', { userId });

      const response = await this.circuitBreaker.execute(async () => {
        return firstValueFrom(
          this.httpService
            .get('/api/v1/models')
            .pipe(
              timeout(5000), // Shorter timeout for metadata
              catchError(this.handleHttpError.bind(this)),
            ),
        );
      });

      this.logger.log('Available models fetched successfully', {
        userId,
        modelCount: response.data.models?.length,
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to fetch available models', {
        userId,
        error: error.message,
      });

      throw this.transformError(error);
    }
  }

  /**
   * Get usage statistics
   */
  async getUsageStatistics(
    userId: string,
    period: 'day' | 'week' | 'month',
  ): Promise<any> {
    try {
      this.logger.debug('Fetching usage statistics', { userId, period });

      // For now, return mock data - in production, this would come from a database
      const mockUsage = {
        usage: {
          totalRequests: Math.floor(Math.random() * 1000),
          totalTokens: Math.floor(Math.random() * 100000),
          requestsByType: {
            embeddings: Math.floor(Math.random() * 300),
            chat: Math.floor(Math.random() * 500),
            analysis: Math.floor(Math.random() * 200),
          },
          period,
          startDate: new Date(Date.now() - this.getPeriodMs(period)),
          endDate: new Date(),
        },
      };

      return mockUsage;
    } catch (error) {
      this.logger.error('Failed to fetch usage statistics', {
        userId,
        period,
        error: error.message,
      });

      throw this.transformError(error);
    }
  }

  /**
   * Get service health
   */
  async getServiceHealth(userId: string): Promise<any> {
    try {
      this.logger.debug('Checking AI service health', { userId });

      const response = await firstValueFrom(
        this.httpService
          .get('/health')
          .pipe(
            timeout(5000),
            catchError(this.handleHttpError.bind(this)),
          ),
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to check AI service health', {
        userId,
        error: error.message,
      });

      // Return degraded status if health check fails
      return {
        status: 'degraded',
        error: 'AI service health check failed',
        lastCheck: new Date().toISOString(),
      };
    }
  }

  /**
   * Handle HTTP errors
   */
  private handleHttpError(error: AxiosError) {
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      throw new ServiceUnavailableException('AI service is unavailable');
    }

    if (error.response) {
      const status = error.response.status;
      const data = error.response.data as any;

      if (status >= 400 && status < 500) {
        throw new BadRequestException(data?.message || 'Invalid request to AI service');
      }

      if (status >= 500) {
        throw new ServiceUnavailableException('AI service internal error');
      }
    }

    throw error;
  }

  /**
   * Transform errors to appropriate HTTP exceptions
   */
  private transformError(error: any): Error {
    if (error instanceof BadRequestException || 
        error instanceof ServiceUnavailableException) {
      return error;
    }

    if (error.message?.includes('timeout')) {
      return new ServiceUnavailableException('AI service request timeout');
    }

    if (error.message?.includes('circuit breaker')) {
      return new ServiceUnavailableException('AI service temporarily unavailable');
    }

    return new InternalServerErrorException('AI service error');
  }

  /**
   * Get period in milliseconds
   */
  private getPeriodMs(period: 'day' | 'week' | 'month'): number {
    switch (period) {
      case 'day':
        return 24 * 60 * 60 * 1000;
      case 'week':
        return 7 * 24 * 60 * 60 * 1000;
      case 'month':
        return 30 * 24 * 60 * 60 * 1000;
      default:
        return 24 * 60 * 60 * 1000;
    }
  }
}
