/**
 * 🤖 AI Service Controller - AI/ML API Endpoints
 * 
 * Implements AI service endpoints:
 * - Text embeddings and similarity search
 * - Chat completions and conversations
 * - Algorithm analysis and optimization
 * - Code pattern detection
 */

import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  CacheInterceptor,
  CacheTTL,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiQuery,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';

import { AiServiceService } from './ai-service.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequirePermissions } from '../auth/guards/permissions.guard';
import {
  EmbeddingRequestDto,
  ChatCompletionRequestDto,
  SimilaritySearchRequestDto,
  AlgorithmAnalysisRequestDto,
  EmbeddingResponseDto,
  ChatCompletionResponseDto,
  SimilaritySearchResponseDto,
  AlgorithmAnalysisResponseDto,
} from './dto';

@ApiTags('AI Service')
@Controller('ai')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AiServiceController {
  constructor(private readonly aiService: AiServiceService) {}

  @Post('embeddings')
  @RequirePermissions('ai:embeddings')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300) // Cache for 5 minutes
  @ApiOperation({
    summary: 'Generate text embeddings',
    description: 'Generate vector embeddings for text input using AI models',
  })
  @ApiBody({ type: EmbeddingRequestDto })
  @ApiResponse({
    status: 200,
    description: 'Embeddings generated successfully',
    type: EmbeddingResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication required',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  async generateEmbeddings(
    @Body() embeddingRequest: EmbeddingRequestDto,
    @Request() req: any,
  ) {
    return this.aiService.generateEmbeddings(embeddingRequest, req.user.id);
  }

  @Post('chat/completions')
  @RequirePermissions('ai:chat')
  @ApiOperation({
    summary: 'Chat completion',
    description: 'Generate AI chat responses using language models',
  })
  @ApiBody({ type: ChatCompletionRequestDto })
  @ApiResponse({
    status: 200,
    description: 'Chat completion generated successfully',
    type: ChatCompletionResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication required',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  async chatCompletion(
    @Body() chatRequest: ChatCompletionRequestDto,
    @Request() req: any,
  ) {
    return this.aiService.chatCompletion(chatRequest, req.user.id);
  }

  @Post('search/similarity')
  @RequirePermissions('ai:search')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(180) // Cache for 3 minutes
  @ApiOperation({
    summary: 'Similarity search',
    description: 'Find similar content using vector similarity search',
  })
  @ApiBody({ type: SimilaritySearchRequestDto })
  @ApiResponse({
    status: 200,
    description: 'Similarity search completed successfully',
    type: SimilaritySearchResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication required',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  async similaritySearch(
    @Body() searchRequest: SimilaritySearchRequestDto,
    @Request() req: any,
  ) {
    return this.aiService.similaritySearch(searchRequest, req.user.id);
  }

  @Post('analyze/algorithm')
  @RequirePermissions('ai:analysis')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(600) // Cache for 10 minutes
  @ApiOperation({
    summary: 'Algorithm analysis',
    description: 'Analyze algorithm complexity, patterns, and optimization opportunities',
  })
  @ApiBody({ type: AlgorithmAnalysisRequestDto })
  @ApiResponse({
    status: 200,
    description: 'Algorithm analysis completed successfully',
    type: AlgorithmAnalysisResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication required',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  async analyzeAlgorithm(
    @Body() analysisRequest: AlgorithmAnalysisRequestDto,
    @Request() req: any,
  ) {
    return this.aiService.analyzeAlgorithm(analysisRequest, req.user.id);
  }

  @Get('models')
  @RequirePermissions('ai:read')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(3600) // Cache for 1 hour
  @ApiOperation({
    summary: 'List available models',
    description: 'Get list of available AI models and their capabilities',
  })
  @ApiResponse({
    status: 200,
    description: 'Models retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        models: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'gpt-3.5-turbo' },
              name: { type: 'string', example: 'GPT-3.5 Turbo' },
              type: { type: 'string', example: 'llm' },
              provider: { type: 'string', example: 'openai' },
              capabilities: {
                type: 'array',
                items: { type: 'string' },
                example: ['chat', 'completion', 'embeddings'],
              },
              maxTokens: { type: 'number', example: 4096 },
              isActive: { type: 'boolean', example: true },
            },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication required',
  })
  async getAvailableModels(@Request() req: any) {
    return this.aiService.getAvailableModels(req.user.id);
  }

  @Get('usage')
  @RequirePermissions('ai:usage')
  @ApiOperation({
    summary: 'Get AI usage statistics',
    description: 'Get current user AI service usage statistics',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: ['day', 'week', 'month'],
    description: 'Time period for usage statistics',
  })
  @ApiResponse({
    status: 200,
    description: 'Usage statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        usage: {
          type: 'object',
          properties: {
            totalRequests: { type: 'number', example: 150 },
            totalTokens: { type: 'number', example: 50000 },
            requestsByType: {
              type: 'object',
              properties: {
                embeddings: { type: 'number', example: 50 },
                chat: { type: 'number', example: 75 },
                analysis: { type: 'number', example: 25 },
              },
            },
            period: { type: 'string', example: 'month' },
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication required',
  })
  async getUsageStatistics(
    @Query('period') period: 'day' | 'week' | 'month' = 'month',
    @Request() req: any,
  ) {
    return this.aiService.getUsageStatistics(req.user.id, period);
  }

  @Get('health')
  @RequirePermissions('ai:health')
  @ApiOperation({
    summary: 'AI service health check',
    description: 'Check the health and availability of AI service',
  })
  @ApiResponse({
    status: 200,
    description: 'AI service health status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        models: {
          type: 'object',
          properties: {
            available: { type: 'number', example: 5 },
            active: { type: 'number', example: 4 },
          },
        },
        performance: {
          type: 'object',
          properties: {
            avgResponseTime: { type: 'number', example: 250 },
            successRate: { type: 'number', example: 99.5 },
          },
        },
        lastCheck: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication required',
  })
  async getAiServiceHealth(@Request() req: any) {
    return this.aiService.getServiceHealth(req.user.id);
  }
}
