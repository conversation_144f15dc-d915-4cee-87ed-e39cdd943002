/**
 * 🤖 AI Service DTOs - Request/Response Data Transfer Objects
 * 
 * Implements validation and serialization for:
 * - Embedding generation requests
 * - Chat completion requests
 * - Similarity search requests
 * - Algorithm analysis requests
 */

import {
  IsString,
  IsArray,
  IsNumber,
  IsBoolean,
  IsOptional,
  IsEnum,
  IsObject,
  Min,
  Max,
  <PERSON><PERSON>eng<PERSON>,
  <PERSON>Leng<PERSON>,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// ================================
// 🔤 Embedding DTOs
// ================================

export class EmbeddingRequestDto {
  @ApiProperty({
    description: 'Text or array of texts to generate embeddings for',
    example: 'This is a sample text for embedding generation',
  })
  @IsString()
  @MinLength(1, { message: 'Text cannot be empty' })
  @MaxLength(10000, { message: 'Text must not exceed 10,000 characters' })
  text: string;

  @ApiPropertyOptional({
    description: 'Model to use for embedding generation',
    example: 'text-embedding-ada-002',
    default: 'text-embedding-ada-002',
  })
  @IsOptional()
  @IsString()
  model?: string = 'text-embedding-ada-002';

  @ApiPropertyOptional({
    description: 'Whether to normalize the embeddings',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  normalize?: boolean = true;
}

export class EmbeddingResponseDto {
  @ApiProperty({
    description: 'Generated embeddings',
    type: 'array',
    items: { type: 'array', items: { type: 'number' } },
  })
  embeddings: number[][];

  @ApiProperty({
    description: 'Model used for generation',
    example: 'text-embedding-ada-002',
  })
  model: string;

  @ApiProperty({
    description: 'Embedding dimensions',
    example: 1536,
  })
  dimensions: number;

  @ApiProperty({
    description: 'Token usage information',
    type: 'object',
  })
  usage: {
    totalTokens: number;
    promptTokens: number;
  };
}

// ================================
// 💬 Chat Completion DTOs
// ================================

export class ChatMessageDto {
  @ApiProperty({
    description: 'Message role',
    enum: ['system', 'user', 'assistant'],
    example: 'user',
  })
  @IsEnum(['system', 'user', 'assistant'])
  role: 'system' | 'user' | 'assistant';

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how can I help you today?',
  })
  @IsString()
  @MinLength(1, { message: 'Message content cannot be empty' })
  @MaxLength(10000, { message: 'Message content must not exceed 10,000 characters' })
  content: string;

  @ApiPropertyOptional({
    description: 'Message name (optional)',
    example: 'assistant',
  })
  @IsOptional()
  @IsString()
  name?: string;
}

export class ChatCompletionRequestDto {
  @ApiProperty({
    description: 'Array of chat messages',
    type: [ChatMessageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatMessageDto)
  messages: ChatMessageDto[];

  @ApiPropertyOptional({
    description: 'Model to use for chat completion',
    example: 'gpt-3.5-turbo',
    default: 'gpt-3.5-turbo',
  })
  @IsOptional()
  @IsString()
  model?: string = 'gpt-3.5-turbo';

  @ApiPropertyOptional({
    description: 'Maximum number of tokens to generate',
    example: 2048,
    minimum: 1,
    maximum: 4096,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4096)
  maxTokens?: number = 2048;

  @ApiPropertyOptional({
    description: 'Temperature for randomness (0.0 to 2.0)',
    example: 0.7,
    minimum: 0,
    maximum: 2,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number = 0.7;

  @ApiPropertyOptional({
    description: 'Top-p sampling parameter',
    example: 1.0,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  topP?: number = 1.0;

  @ApiPropertyOptional({
    description: 'Whether to stream the response',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  stream?: boolean = false;
}

export class ChatCompletionResponseDto {
  @ApiProperty({
    description: 'Completion ID',
    example: 'chatcmpl-123',
  })
  id: string;

  @ApiProperty({
    description: 'Object type',
    example: 'chat.completion',
  })
  object: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: 1677652288,
  })
  created: number;

  @ApiProperty({
    description: 'Model used',
    example: 'gpt-3.5-turbo',
  })
  model: string;

  @ApiProperty({
    description: 'Chat completion choices',
    type: 'array',
  })
  choices: Array<{
    index: number;
    message: ChatMessageDto;
    finishReason: 'stop' | 'length' | 'content_filter' | null;
  }>;

  @ApiProperty({
    description: 'Token usage information',
    type: 'object',
  })
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

// ================================
// 🔍 Similarity Search DTOs
// ================================

export class SimilaritySearchRequestDto {
  @ApiProperty({
    description: 'Search query text',
    example: 'machine learning algorithms',
  })
  @IsString()
  @MinLength(1, { message: 'Query cannot be empty' })
  @MaxLength(1000, { message: 'Query must not exceed 1,000 characters' })
  query: string;

  @ApiPropertyOptional({
    description: 'Number of top results to return',
    example: 10,
    minimum: 1,
    maximum: 100,
    default: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  topK?: number = 10;

  @ApiPropertyOptional({
    description: 'Minimum similarity threshold (0.0 to 1.0)',
    example: 0.7,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  threshold?: number = 0.0;

  @ApiPropertyOptional({
    description: 'Additional filters for search',
    example: { category: 'algorithms', language: 'python' },
  })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;
}

export class SimilaritySearchResponseDto {
  @ApiProperty({
    description: 'Search results',
    type: 'array',
  })
  results: Array<{
    id: string;
    content: string;
    score: number;
    metadata?: Record<string, any>;
  }>;

  @ApiProperty({
    description: 'Original search query',
    example: 'machine learning algorithms',
  })
  query: string;

  @ApiProperty({
    description: 'Total number of results found',
    example: 25,
  })
  totalResults: number;

  @ApiProperty({
    description: 'Search execution time in milliseconds',
    example: 150,
  })
  searchTime: number;
}

// ================================
// 🧮 Algorithm Analysis DTOs
// ================================

export class AlgorithmAnalysisRequestDto {
  @ApiProperty({
    description: 'Source code to analyze',
    example: 'def bubble_sort(arr):\n    for i in range(len(arr)):\n        for j in range(0, len(arr)-i-1):\n            if arr[j] > arr[j+1]:\n                arr[j], arr[j+1] = arr[j+1], arr[j]\n    return arr',
  })
  @IsString()
  @MinLength(10, { message: 'Code must be at least 10 characters long' })
  @MaxLength(50000, { message: 'Code must not exceed 50,000 characters' })
  code: string;

  @ApiProperty({
    description: 'Programming language',
    enum: ['javascript', 'typescript', 'python', 'java', 'cpp'],
    example: 'python',
  })
  @IsEnum(['javascript', 'typescript', 'python', 'java', 'cpp'])
  language: 'javascript' | 'typescript' | 'python' | 'java' | 'cpp';

  @ApiProperty({
    description: 'Type of analysis to perform',
    enum: ['complexity', 'patterns', 'optimization', 'all'],
    example: 'all',
  })
  @IsEnum(['complexity', 'patterns', 'optimization', 'all'])
  analysisType: 'complexity' | 'patterns' | 'optimization' | 'all';
}

export class AlgorithmAnalysisResponseDto {
  @ApiProperty({
    description: 'Time complexity analysis',
    type: 'object',
  })
  timeComplexity: {
    bestCase: string;
    averageCase: string;
    worstCase: string;
    explanation: string;
    confidence: number;
  };

  @ApiProperty({
    description: 'Space complexity analysis',
    type: 'object',
  })
  spaceComplexity: {
    bestCase: string;
    averageCase: string;
    worstCase: string;
    explanation: string;
    confidence: number;
  };

  @ApiProperty({
    description: 'Detected patterns',
    type: 'array',
  })
  patterns: Array<{
    pattern: string;
    description: string;
    confidence: number;
    location: {
      startLine: number;
      endLine: number;
    };
  }>;

  @ApiProperty({
    description: 'Optimization suggestions',
    type: 'array',
  })
  optimizations: Array<{
    type: 'performance' | 'memory' | 'readability' | 'maintainability';
    description: string;
    impact: 'low' | 'medium' | 'high';
    effort: 'low' | 'medium' | 'high';
    example?: string;
  }>;

  @ApiProperty({
    description: 'Code quality metrics',
    type: 'object',
  })
  codeQuality: {
    readability: number;
    maintainability: number;
    testability: number;
    performance: number;
    overall: number;
  };
}
