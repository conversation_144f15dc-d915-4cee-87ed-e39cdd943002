/**
 * ⚡ Circuit Breaker Service - Fault Tolerance Pattern
 * 
 * Implements circuit breaker pattern for:
 * - Preventing cascade failures
 * - Fast failure detection
 * - Automatic recovery
 * - Service health monitoring
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Failing fast
  HALF_OPEN = 'HALF_OPEN' // Testing recovery
}

interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  expectedErrorRate: number;
}

interface CircuitMetrics {
  totalRequests: number;
  failedRequests: number;
  successfulRequests: number;
  lastFailureTime: number;
  lastSuccessTime: number;
  consecutiveFailures: number;
}

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private state: CircuitState = CircuitState.CLOSED;
  private metrics: CircuitMetrics = {
    totalRequests: 0,
    failedRequests: 0,
    successfulRequests: 0,
    lastFailureTime: 0,
    lastSuccessTime: 0,
    consecutiveFailures: 0,
  };
  private config: CircuitBreakerConfig;
  private stateChangeTime: number = Date.now();

  constructor(private readonly configService: ConfigService) {
    this.config = {
      failureThreshold: this.configService.get<number>('circuitBreaker.failureThreshold', 5),
      recoveryTimeout: this.configService.get<number>('circuitBreaker.recoveryTimeout', 60000), // 1 minute
      monitoringPeriod: this.configService.get<number>('circuitBreaker.monitoringPeriod', 300000), // 5 minutes
      expectedErrorRate: this.configService.get<number>('circuitBreaker.expectedErrorRate', 0.5), // 50%
    };

    this.logger.log('Circuit breaker initialized', {
      config: this.config,
      initialState: this.state,
    });
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    // Check if circuit should be opened
    this.updateState();

    if (this.state === CircuitState.OPEN) {
      const error = new Error('Circuit breaker is OPEN - failing fast');
      this.logger.warn('Circuit breaker preventing execution', {
        state: this.state,
        consecutiveFailures: this.metrics.consecutiveFailures,
        timeSinceLastFailure: Date.now() - this.metrics.lastFailureTime,
      });
      throw error;
    }

    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const result = await operation();
      
      // Record success
      this.recordSuccess(Date.now() - startTime);
      
      return result;
    } catch (error) {
      // Record failure
      this.recordFailure(Date.now() - startTime, error);
      
      throw error;
    }
  }

  /**
   * Get current circuit breaker status
   */
  getStatus(): {
    state: CircuitState;
    metrics: CircuitMetrics;
    config: CircuitBreakerConfig;
    stateChangeTime: number;
    errorRate: number;
  } {
    return {
      state: this.state,
      metrics: { ...this.metrics },
      config: { ...this.config },
      stateChangeTime: this.stateChangeTime,
      errorRate: this.calculateErrorRate(),
    };
  }

  /**
   * Manually reset circuit breaker
   */
  reset(): void {
    this.logger.log('Circuit breaker manually reset');
    
    this.state = CircuitState.CLOSED;
    this.metrics = {
      totalRequests: 0,
      failedRequests: 0,
      successfulRequests: 0,
      lastFailureTime: 0,
      lastSuccessTime: 0,
      consecutiveFailures: 0,
    };
    this.stateChangeTime = Date.now();
  }

  /**
   * Update circuit breaker state based on current metrics
   */
  private updateState(): void {
    const now = Date.now();
    const timeSinceStateChange = now - this.stateChangeTime;

    switch (this.state) {
      case CircuitState.CLOSED:
        // Check if we should open the circuit
        if (this.shouldOpenCircuit()) {
          this.openCircuit();
        }
        break;

      case CircuitState.OPEN:
        // Check if we should try half-open
        if (timeSinceStateChange >= this.config.recoveryTimeout) {
          this.halfOpenCircuit();
        }
        break;

      case CircuitState.HALF_OPEN:
        // In half-open state, we allow limited requests through
        // The next success/failure will determine if we close or open
        break;
    }
  }

  /**
   * Check if circuit should be opened
   */
  private shouldOpenCircuit(): boolean {
    // Need minimum number of requests to make a decision
    if (this.metrics.totalRequests < this.config.failureThreshold) {
      return false;
    }

    // Check consecutive failures
    if (this.metrics.consecutiveFailures >= this.config.failureThreshold) {
      return true;
    }

    // Check error rate over monitoring period
    const errorRate = this.calculateErrorRate();
    if (errorRate >= this.config.expectedErrorRate) {
      return true;
    }

    return false;
  }

  /**
   * Open the circuit
   */
  private openCircuit(): void {
    this.state = CircuitState.OPEN;
    this.stateChangeTime = Date.now();
    
    this.logger.warn('Circuit breaker opened', {
      consecutiveFailures: this.metrics.consecutiveFailures,
      errorRate: this.calculateErrorRate(),
      totalRequests: this.metrics.totalRequests,
    });
  }

  /**
   * Set circuit to half-open state
   */
  private halfOpenCircuit(): void {
    this.state = CircuitState.HALF_OPEN;
    this.stateChangeTime = Date.now();
    
    this.logger.log('Circuit breaker half-opened - testing recovery', {
      timeSinceOpen: Date.now() - this.stateChangeTime,
    });
  }

  /**
   * Close the circuit (normal operation)
   */
  private closeCircuit(): void {
    this.state = CircuitState.CLOSED;
    this.stateChangeTime = Date.now();
    this.metrics.consecutiveFailures = 0;
    
    this.logger.log('Circuit breaker closed - normal operation resumed');
  }

  /**
   * Record successful operation
   */
  private recordSuccess(duration: number): void {
    this.metrics.successfulRequests++;
    this.metrics.lastSuccessTime = Date.now();
    this.metrics.consecutiveFailures = 0;

    this.logger.debug('Circuit breaker - operation succeeded', {
      duration,
      state: this.state,
      successfulRequests: this.metrics.successfulRequests,
    });

    // If we're in half-open state and got a success, close the circuit
    if (this.state === CircuitState.HALF_OPEN) {
      this.closeCircuit();
    }
  }

  /**
   * Record failed operation
   */
  private recordFailure(duration: number, error: any): void {
    this.metrics.failedRequests++;
    this.metrics.lastFailureTime = Date.now();
    this.metrics.consecutiveFailures++;

    this.logger.warn('Circuit breaker - operation failed', {
      duration,
      state: this.state,
      error: error.message,
      consecutiveFailures: this.metrics.consecutiveFailures,
      errorRate: this.calculateErrorRate(),
    });

    // If we're in half-open state and got a failure, open the circuit again
    if (this.state === CircuitState.HALF_OPEN) {
      this.openCircuit();
    }
  }

  /**
   * Calculate current error rate
   */
  private calculateErrorRate(): number {
    if (this.metrics.totalRequests === 0) {
      return 0;
    }
    
    return this.metrics.failedRequests / this.metrics.totalRequests;
  }

  /**
   * Clean up old metrics (called periodically)
   */
  private cleanupMetrics(): void {
    const now = Date.now();
    const cutoffTime = now - this.config.monitoringPeriod;

    // In a production system, you'd want to maintain a sliding window
    // of metrics rather than just resetting everything
    if (this.metrics.lastFailureTime < cutoffTime && this.metrics.lastSuccessTime < cutoffTime) {
      this.logger.debug('Cleaning up old circuit breaker metrics');
      
      this.metrics.totalRequests = 0;
      this.metrics.failedRequests = 0;
      this.metrics.successfulRequests = 0;
    }
  }

  /**
   * Get health status for monitoring
   */
  getHealthStatus(): {
    isHealthy: boolean;
    state: CircuitState;
    errorRate: number;
    consecutiveFailures: number;
    uptime: number;
  } {
    const errorRate = this.calculateErrorRate();
    
    return {
      isHealthy: this.state !== CircuitState.OPEN && errorRate < this.config.expectedErrorRate,
      state: this.state,
      errorRate,
      consecutiveFailures: this.metrics.consecutiveFailures,
      uptime: Date.now() - this.stateChangeTime,
    };
  }
}
