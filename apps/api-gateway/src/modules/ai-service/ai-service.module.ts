/**
 * 🤖 AI Service Module - AI/ML Integration
 * 
 * Implements AI service integration with:
 * - HTTP client configuration
 * - Circuit breaker pattern
 * - Caching and rate limiting
 * - Error handling and retries
 */

import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CacheModule } from '@nestjs/cache-manager';

import { AiServiceController } from './ai-service.controller';
import { AiServiceService } from './ai-service.service';
import { CircuitBreakerService } from './circuit-breaker.service';

@Module({
  imports: [
    // HTTP Client Configuration
    HttpModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        baseURL: configService.get<string>('aiService.url', 'http://localhost:8000'),
        timeout: configService.get<number>('aiService.timeout', 30000),
        maxRedirects: 3,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Enterprise-API-Gateway/1.0.0',
        },
        // Retry configuration
        retries: configService.get<number>('aiService.retries', 3),
        retryDelay: (retryCount: number) => {
          const baseDelay = configService.get<number>('aiService.retryDelay', 1000);
          return Math.min(baseDelay * Math.pow(2, retryCount), 10000); // Exponential backoff with max 10s
        },
        retryCondition: (error: any) => {
          // Retry on network errors and 5xx responses
          return !error.response || (error.response.status >= 500 && error.response.status <= 599);
        },
      }),
    }),

    // Caching for AI responses
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get<number>('performance.cache.ttl', 300), // 5 minutes
        max: configService.get<number>('performance.cache.maxSize', 100),
        isGlobal: false,
      }),
    }),
  ],
  controllers: [AiServiceController],
  providers: [AiServiceService, CircuitBreakerService],
  exports: [AiServiceService],
})
export class AiServiceModule {}
