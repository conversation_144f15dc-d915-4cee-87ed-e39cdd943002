/**
 * 🔐 Authentication Service - Core Auth Business Logic
 * 
 * Implements authentication operations:
 * - User login and registration
 * - JWT token management
 * - Password validation and hashing
 * - Session management
 * - Security monitoring
 */

import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  Logger,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

import { User } from '../../infrastructure/database/entities/user.entity';
import { Role } from '../../infrastructure/database/entities/role.entity';
import { LoginDto, RegisterDto, RefreshTokenDto, ChangePasswordDto } from './dto';
import { JWTPayload, TokenPair } from '@shared-types';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly saltRounds = 12;

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Authenticate user with email and password
   */
  async login(loginDto: LoginDto, ipAddress?: string): Promise<{
    user: Partial<User>;
    tokens: TokenPair;
  }> {
    const { email, password, rememberMe } = loginDto;

    try {
      // Find user with roles and permissions
      const user = await this.userRepository.findOne({
        where: { email: email.toLowerCase() },
        relations: ['roles', 'roles.permissions'],
      });

      if (!user) {
        this.logger.warn(`Login attempt with non-existent email: ${email}`, { ipAddress });
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check if user can login
      if (!user.canLogin()) {
        this.logger.warn(`Login attempt for inactive/locked user: ${email}`, { 
          userId: user.id, 
          isActive: user.isActive, 
          isLocked: user.isLocked,
          ipAddress 
        });
        
        if (user.isLocked) {
          throw new ForbiddenException('Account is temporarily locked due to failed login attempts');
        }
        
        throw new UnauthorizedException('Account is not active');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      
      if (!isPasswordValid) {
        // Increment failed login attempts
        user.incrementFailedLoginAttempts();
        await this.userRepository.save(user);
        
        this.logger.warn(`Failed login attempt for user: ${email}`, { 
          userId: user.id, 
          failedAttempts: user.failedLoginAttempts,
          ipAddress 
        });
        
        throw new UnauthorizedException('Invalid credentials');
      }

      // Successful login - record it
      user.recordLogin(ipAddress);
      await this.userRepository.save(user);

      // Generate tokens
      const tokens = await this.generateTokenPair(user, rememberMe);

      this.logger.log(`Successful login for user: ${email}`, { 
        userId: user.id, 
        ipAddress 
      });

      return {
        user: user.toJSON(),
        tokens,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException || error instanceof ForbiddenException) {
        throw error;
      }
      
      this.logger.error('Login error', { error: error.message, email, ipAddress });
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Register new user
   */
  async register(registerDto: RegisterDto): Promise<{
    user: Partial<User>;
    tokens: TokenPair;
  }> {
    const { email, username, password, firstName, lastName } = registerDto;

    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findOne({
        where: [
          { email: email.toLowerCase() },
          { username: username.toLowerCase() },
        ],
      });

      if (existingUser) {
        if (existingUser.email === email.toLowerCase()) {
          throw new ConflictException('Email already registered');
        }
        throw new ConflictException('Username already taken');
      }

      // Validate password strength
      this.validatePasswordStrength(password);

      // Hash password
      const hashedPassword = await bcrypt.hash(password, this.saltRounds);

      // Get default user role
      const userRole = await this.roleRepository.findOne({
        where: { name: 'user' },
        relations: ['permissions'],
      });

      if (!userRole) {
        this.logger.error('Default user role not found');
        throw new BadRequestException('Registration temporarily unavailable');
      }

      // Create user
      const user = this.userRepository.create({
        email: email.toLowerCase(),
        username: username.toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        roles: [userRole],
        isActive: true,
        isVerified: false, // Email verification required
      });

      const savedUser = await this.userRepository.save(user);

      // Generate tokens
      const tokens = await this.generateTokenPair(savedUser);

      this.logger.log(`New user registered: ${email}`, { userId: savedUser.id });

      return {
        user: savedUser.toJSON(),
        tokens,
      };
    } catch (error) {
      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      
      this.logger.error('Registration error', { error: error.message, email, username });
      throw new BadRequestException('Registration failed');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<TokenPair> {
    const { refreshToken } = refreshTokenDto;

    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('auth.jwt.refreshSecret'),
      });

      if (payload.tokenType !== 'refresh') {
        throw new UnauthorizedException('Invalid token type');
      }

      // Find user
      const user = await this.userRepository.findOne({
        where: { id: payload.userId },
        relations: ['roles', 'roles.permissions'],
      });

      if (!user || !user.canLogin()) {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Generate new token pair
      return this.generateTokenPair(user);
    } catch (error) {
      this.logger.warn('Refresh token failed', { error: error.message });
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * Change user password
   */
  async changePassword(
    userId: string,
    changePasswordDto: ChangePasswordDto,
  ): Promise<void> {
    const { currentPassword, newPassword } = changePasswordDto;

    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      
      if (!isCurrentPasswordValid) {
        throw new UnauthorizedException('Current password is incorrect');
      }

      // Validate new password strength
      this.validatePasswordStrength(newPassword);

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, this.saltRounds);

      // Update password
      user.password = hashedNewPassword;
      user.passwordChangedAt = new Date();
      
      await this.userRepository.save(user);

      this.logger.log(`Password changed for user: ${user.email}`, { userId });
    } catch (error) {
      if (error instanceof UnauthorizedException || error instanceof BadRequestException) {
        throw error;
      }
      
      this.logger.error('Change password error', { error: error.message, userId });
      throw new BadRequestException('Password change failed');
    }
  }

  /**
   * Validate user by ID (for JWT strategy)
   */
  async validateUser(userId: string): Promise<User | null> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['roles', 'roles.permissions'],
      });

      if (!user || !user.canLogin()) {
        return null;
      }

      return user;
    } catch (error) {
      this.logger.error('User validation error', { error: error.message, userId });
      return null;
    }
  }

  /**
   * Generate JWT token pair
   */
  private async generateTokenPair(user: User, rememberMe = false): Promise<TokenPair> {
    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      roles: user.roleNames,
      permissions: user.permissions,
    };

    const accessTokenExpiry = this.configService.get<string>('auth.jwt.expiresIn', '15m');
    const refreshTokenExpiry = rememberMe ? '30d' : '7d';

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: accessTokenExpiry,
    });

    const refreshToken = this.jwtService.sign(
      { userId: user.id, tokenType: 'refresh' },
      {
        secret: this.configService.get<string>('auth.jwt.refreshSecret'),
        expiresIn: refreshTokenExpiry,
      },
    );

    // Calculate expiry time in seconds
    const decoded = this.jwtService.decode(accessToken) as any;
    const expiresIn = decoded.exp - decoded.iat;

    return {
      accessToken,
      refreshToken,
      expiresIn,
      tokenType: 'Bearer',
    };
  }

  /**
   * Validate password strength
   */
  private validatePasswordStrength(password: string): void {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const errors: string[] = [];

    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }
    if (!hasUpperCase) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!hasLowerCase) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!hasNumbers) {
      errors.push('Password must contain at least one number');
    }
    if (!hasSpecialChar) {
      errors.push('Password must contain at least one special character');
    }

    if (errors.length > 0) {
      throw new BadRequestException({
        message: 'Password validation failed',
        errors,
      });
    }
  }

  /**
   * Logout user (invalidate tokens - would require token blacklist in production)
   */
  async logout(userId: string): Promise<void> {
    // In a production system, you would add the tokens to a blacklist
    // For now, we just log the logout
    this.logger.log(`User logged out`, { userId });
  }

  /**
   * Get user profile
   */
  async getProfile(userId: string): Promise<Partial<User>> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles', 'roles.permissions'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    return user.toJSON();
  }
}
