/**
 * 🔐 Authentication Module - JWT and RBAC Implementation
 * 
 * Implements comprehensive authentication with:
 * - JWT token management
 * - Role-based access control
 * - Password security
 * - Session management
 * - Rate limiting
 */

import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import { User } from '../../infrastructure/database/entities/user.entity';
import { Role } from '../../infrastructure/database/entities/role.entity';
import { Permission } from '../../infrastructure/database/entities/permission.entity';

@Module({
  imports: [
    // JWT Configuration
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('auth.jwt.secret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.jwt.expiresIn', '15m'),
          issuer: configService.get<string>('auth.jwt.issuer', 'enterprise-platform'),
          audience: configService.get<string>('auth.jwt.audience', 'api-gateway'),
        },
      }),
    }),

    // Passport Configuration
    PassportModule.register({
      defaultStrategy: 'jwt',
      property: 'user',
      session: false,
    }),

    // Rate Limiting for Auth Endpoints
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ttl: 900, // 15 minutes
        limit: 5, // 5 attempts per 15 minutes for auth endpoints
      }),
    }),

    // Database Entities
    TypeOrmModule.forFeature([User, Role, Permission]),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    UsersService,
    JwtStrategy,
    LocalStrategy,
    JwtAuthGuard,
    RolesGuard,
    PermissionsGuard,
  ],
  exports: [
    AuthService,
    JwtAuthGuard,
    RolesGuard,
    PermissionsGuard,
    JwtModule,
    PassportModule,
  ],
})
export class AuthModule {}
