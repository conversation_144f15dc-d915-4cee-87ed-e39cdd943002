/**
 * 🔐 JWT Auth Guard - JWT Authentication Guard
 * 
 * Implements JWT authentication with:
 * - Token validation
 * - Optional authentication support
 * - Error handling
 * - Security logging
 */

import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';

export const IS_PUBLIC_KEY = 'isPublic';
export const IS_OPTIONAL_AUTH_KEY = 'isOptionalAuth';

/**
 * Decorator to mark endpoints as public (no authentication required)
 */
export const Public = () => {
  const { SetMetadata } = require('@nestjs/common');
  return SetMetadata(IS_PUBLIC_KEY, true);
};

/**
 * Decorator to mark endpoints as having optional authentication
 */
export const OptionalAuth = () => {
  const { SetMetadata } = require('@nestjs/common');
  return SetMetadata(IS_OPTIONAL_AUTH_KEY, true);
};

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(private readonly reflector: Reflector) {
    super();
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // Check if endpoint is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // Check if authentication is optional
    const isOptionalAuth = this.reflector.getAllAndOverride<boolean>(
      IS_OPTIONAL_AUTH_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (isOptionalAuth) {
      return this.handleOptionalAuth(context);
    }

    // Standard JWT authentication
    return super.canActivate(context);
  }

  /**
   * Handle optional authentication
   */
  private async handleOptionalAuth(context: ExecutionContext): Promise<boolean> {
    try {
      const result = await super.canActivate(context);
      return result as boolean;
    } catch (error) {
      // If authentication fails but it's optional, continue without user
      const request = context.switchToHttp().getRequest();
      request.user = null;
      
      this.logger.debug('Optional authentication failed, continuing without user', {
        error: error.message,
        correlationId: request.correlationId,
      });
      
      return true;
    }
  }

  /**
   * Handle authentication requests
   */
  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const correlationId = request.correlationId;

    // Log authentication attempt
    this.logger.debug('JWT authentication attempt', {
      hasUser: !!user,
      hasError: !!err,
      info: info?.message,
      correlationId,
    });

    if (err) {
      this.logger.error('JWT authentication error', {
        error: err.message,
        correlationId,
      });
      throw err;
    }

    if (!user) {
      const errorMessage = this.getAuthErrorMessage(info);
      
      this.logger.warn('JWT authentication failed', {
        reason: errorMessage,
        info: info?.message,
        correlationId,
      });
      
      throw new UnauthorizedException(errorMessage);
    }

    // Successful authentication
    this.logger.debug('JWT authentication successful', {
      userId: user.id,
      email: user.email,
      correlationId,
    });

    return user;
  }

  /**
   * Get user-friendly error message based on JWT info
   */
  private getAuthErrorMessage(info: any): string {
    if (!info) {
      return 'Authentication required';
    }

    switch (info.message) {
      case 'No auth token':
        return 'Authentication token is required';
      
      case 'jwt expired':
        return 'Authentication token has expired';
      
      case 'jwt malformed':
        return 'Invalid authentication token format';
      
      case 'jwt signature verification failed':
        return 'Invalid authentication token signature';
      
      case 'invalid token':
        return 'Invalid authentication token';
      
      case 'invalid signature':
        return 'Invalid token signature';
      
      case 'jwt audience invalid':
        return 'Token not valid for this service';
      
      case 'jwt issuer invalid':
        return 'Token from invalid issuer';
      
      case 'jwt not active':
        return 'Token not yet active';
      
      default:
        return 'Authentication failed';
    }
  }

  /**
   * Get token from request
   */
  getRequest(context: ExecutionContext) {
    return context.switchToHttp().getRequest();
  }
}
