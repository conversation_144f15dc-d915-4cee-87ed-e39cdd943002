/**
 * 🛡️ Roles Guard - Role-Based Access Control
 * 
 * Implements role-based authorization with:
 * - Role requirement checking
 * - Hierarchical role support
 * - Flexible role matching
 * - Security logging
 */

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { User } from '../../../infrastructure/database/entities/user.entity';

export const ROLES_KEY = 'roles';

/**
 * Decorator to specify required roles for an endpoint
 */
export const Roles = (...roles: string[]) => {
  const { SetMetadata } = require('@nestjs/common');
  return SetMetadata(ROLES_KEY, roles);
};

/**
 * Role matching strategies
 */
export enum RoleMatchStrategy {
  ANY = 'any', // User must have at least one of the required roles
  ALL = 'all', // User must have all required roles
  EXACT = 'exact', // User must have exactly the required roles
}

export const ROLE_STRATEGY_KEY = 'roleStrategy';

/**
 * Decorator to specify role matching strategy
 */
export const RoleStrategy = (strategy: RoleMatchStrategy) => {
  const { SetMetadata } = require('@nestjs/common');
  return SetMetadata(ROLE_STRATEGY_KEY, strategy);
};

@Injectable()
export class RolesGuard implements CanActivate {
  private readonly logger = new Logger(RolesGuard.name);

  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Get required roles from decorator
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // If no roles are required, allow access
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    // Get role matching strategy
    const strategy = this.reflector.getAllAndOverride<RoleMatchStrategy>(
      ROLE_STRATEGY_KEY,
      [context.getHandler(), context.getClass()],
    ) || RoleMatchStrategy.ANY;

    // Get user from request
    const request = context.switchToHttp().getRequest();
    const user: User = request.user;

    if (!user) {
      this.logger.warn('Roles guard called without authenticated user');
      throw new ForbiddenException('Authentication required');
    }

    // Get user roles
    const userRoles = user.roleNames || [];
    const correlationId = request.correlationId;

    // Check role requirements
    const hasAccess = this.checkRoleAccess(userRoles, requiredRoles, strategy);

    if (!hasAccess) {
      this.logger.warn('Access denied - insufficient roles', {
        userId: user.id,
        userRoles,
        requiredRoles,
        strategy,
        correlationId,
        endpoint: `${request.method} ${request.url}`,
      });

      throw new ForbiddenException({
        message: 'Insufficient permissions',
        required: requiredRoles,
        strategy,
        userRoles: userRoles,
      });
    }

    this.logger.debug('Role access granted', {
      userId: user.id,
      userRoles,
      requiredRoles,
      strategy,
      correlationId,
    });

    return true;
  }

  /**
   * Check if user roles meet the requirements based on strategy
   */
  private checkRoleAccess(
    userRoles: string[],
    requiredRoles: string[],
    strategy: RoleMatchStrategy,
  ): boolean {
    // Handle super admin role (bypasses all role checks)
    if (userRoles.includes('super_admin')) {
      return true;
    }

    switch (strategy) {
      case RoleMatchStrategy.ANY:
        return this.hasAnyRole(userRoles, requiredRoles);
      
      case RoleMatchStrategy.ALL:
        return this.hasAllRoles(userRoles, requiredRoles);
      
      case RoleMatchStrategy.EXACT:
        return this.hasExactRoles(userRoles, requiredRoles);
      
      default:
        this.logger.error(`Unknown role strategy: ${strategy}`);
        return false;
    }
  }

  /**
   * Check if user has at least one of the required roles
   */
  private hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {
    return requiredRoles.some(role => userRoles.includes(role));
  }

  /**
   * Check if user has all required roles
   */
  private hasAllRoles(userRoles: string[], requiredRoles: string[]): boolean {
    return requiredRoles.every(role => userRoles.includes(role));
  }

  /**
   * Check if user has exactly the required roles (no more, no less)
   */
  private hasExactRoles(userRoles: string[], requiredRoles: string[]): boolean {
    if (userRoles.length !== requiredRoles.length) {
      return false;
    }
    
    const sortedUserRoles = [...userRoles].sort();
    const sortedRequiredRoles = [...requiredRoles].sort();
    
    return sortedUserRoles.every((role, index) => role === sortedRequiredRoles[index]);
  }
}
