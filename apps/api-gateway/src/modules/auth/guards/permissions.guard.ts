/**
 * 🔐 Permissions Guard - Fine-grained Access Control
 * 
 * Implements permission-based authorization with:
 * - Resource-action permission checking
 * - Wildcard permission support
 * - Context-aware permissions
 * - Security logging
 */

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { User } from '../../../infrastructure/database/entities/user.entity';

export const PERMISSIONS_KEY = 'permissions';

/**
 * Permission requirement interface
 */
export interface PermissionRequirement {
  resource: string;
  action: string;
  context?: Record<string, any>;
}

/**
 * Decorator to specify required permissions for an endpoint
 */
export const RequirePermissions = (...permissions: (string | PermissionRequirement)[]) => {
  const { SetMetadata } = require('@nestjs/common');
  
  // Normalize permissions to PermissionRequirement format
  const normalizedPermissions = permissions.map(permission => {
    if (typeof permission === 'string') {
      const [resource, action] = permission.split(':');
      return { resource, action };
    }
    return permission;
  });
  
  return SetMetadata(PERMISSIONS_KEY, normalizedPermissions);
};

/**
 * Permission matching strategies
 */
export enum PermissionMatchStrategy {
  ANY = 'any', // User must have at least one of the required permissions
  ALL = 'all', // User must have all required permissions
}

export const PERMISSION_STRATEGY_KEY = 'permissionStrategy';

/**
 * Decorator to specify permission matching strategy
 */
export const PermissionStrategy = (strategy: PermissionMatchStrategy) => {
  const { SetMetadata } = require('@nestjs/common');
  return SetMetadata(PERMISSION_STRATEGY_KEY, strategy);
};

@Injectable()
export class PermissionsGuard implements CanActivate {
  private readonly logger = new Logger(PermissionsGuard.name);

  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Get required permissions from decorator
    const requiredPermissions = this.reflector.getAllAndOverride<PermissionRequirement[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    // If no permissions are required, allow access
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    // Get permission matching strategy
    const strategy = this.reflector.getAllAndOverride<PermissionMatchStrategy>(
      PERMISSION_STRATEGY_KEY,
      [context.getHandler(), context.getClass()],
    ) || PermissionMatchStrategy.ALL;

    // Get user from request
    const request = context.switchToHttp().getRequest();
    const user: User = request.user;

    if (!user) {
      this.logger.warn('Permissions guard called without authenticated user');
      throw new ForbiddenException('Authentication required');
    }

    // Get user permissions
    const userPermissions = user.permissions || [];
    const correlationId = request.correlationId;

    // Check permission requirements
    const hasAccess = this.checkPermissionAccess(
      userPermissions,
      requiredPermissions,
      strategy,
      request,
    );

    if (!hasAccess) {
      this.logger.warn('Access denied - insufficient permissions', {
        userId: user.id,
        userPermissions,
        requiredPermissions: requiredPermissions.map(p => `${p.resource}:${p.action}`),
        strategy,
        correlationId,
        endpoint: `${request.method} ${request.url}`,
      });

      throw new ForbiddenException({
        message: 'Insufficient permissions',
        required: requiredPermissions.map(p => `${p.resource}:${p.action}`),
        strategy,
        userPermissions,
      });
    }

    this.logger.debug('Permission access granted', {
      userId: user.id,
      requiredPermissions: requiredPermissions.map(p => `${p.resource}:${p.action}`),
      strategy,
      correlationId,
    });

    return true;
  }

  /**
   * Check if user permissions meet the requirements based on strategy
   */
  private checkPermissionAccess(
    userPermissions: string[],
    requiredPermissions: PermissionRequirement[],
    strategy: PermissionMatchStrategy,
    request: any,
  ): boolean {
    // Handle super admin permission (bypasses all permission checks)
    if (userPermissions.includes('*:*')) {
      return true;
    }

    switch (strategy) {
      case PermissionMatchStrategy.ANY:
        return requiredPermissions.some(permission =>
          this.hasPermission(userPermissions, permission, request),
        );
      
      case PermissionMatchStrategy.ALL:
        return requiredPermissions.every(permission =>
          this.hasPermission(userPermissions, permission, request),
        );
      
      default:
        this.logger.error(`Unknown permission strategy: ${strategy}`);
        return false;
    }
  }

  /**
   * Check if user has a specific permission
   */
  private hasPermission(
    userPermissions: string[],
    requiredPermission: PermissionRequirement,
    request: any,
  ): boolean {
    const { resource, action, context } = requiredPermission;

    // Check exact permission
    const exactPermission = `${resource}:${action}`;
    if (userPermissions.includes(exactPermission)) {
      return this.checkPermissionContext(context, request);
    }

    // Check wildcard permissions
    const resourceWildcard = `${resource}:*`;
    const actionWildcard = `*:${action}`;
    
    if (userPermissions.includes(resourceWildcard) || userPermissions.includes(actionWildcard)) {
      return this.checkPermissionContext(context, request);
    }

    // Check admin permissions for specific resources
    const adminPermission = `${resource}:admin`;
    if (userPermissions.includes(adminPermission)) {
      return this.checkPermissionContext(context, request);
    }

    return false;
  }

  /**
   * Check permission context constraints
   */
  private checkPermissionContext(
    context: Record<string, any> | undefined,
    request: any,
  ): boolean {
    if (!context) {
      return true; // No context constraints
    }

    // Example context checks (extend as needed)
    
    // Time-based restrictions
    if (context.timeRestrictions) {
      if (!this.checkTimeRestrictions(context.timeRestrictions)) {
        return false;
      }
    }

    // IP-based restrictions
    if (context.ipRestrictions) {
      const clientIp = this.getClientIp(request);
      if (!this.checkIpRestrictions(context.ipRestrictions, clientIp)) {
        return false;
      }
    }

    // Resource ownership (e.g., user can only access their own resources)
    if (context.ownershipRequired) {
      const user = request.user;
      const resourceOwnerId = request.params?.userId || request.body?.userId;
      
      if (resourceOwnerId && resourceOwnerId !== user.id) {
        // Check if user has admin permissions to override ownership
        if (!user.permissions.includes('*:*') && !user.roleNames.includes('admin')) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Check time-based restrictions
   */
  private checkTimeRestrictions(timeRestrictions: any): boolean {
    const now = new Date();
    
    // Check day of week restrictions
    if (timeRestrictions.daysOfWeek) {
      const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      if (!timeRestrictions.daysOfWeek.includes(currentDay)) {
        return false;
      }
    }

    // Check time range restrictions
    if (timeRestrictions.startTime && timeRestrictions.endTime) {
      const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
      if (currentTime < timeRestrictions.startTime || currentTime > timeRestrictions.endTime) {
        return false;
      }
    }

    return true;
  }

  /**
   * Check IP-based restrictions
   */
  private checkIpRestrictions(ipRestrictions: string[], clientIp: string): boolean {
    if (!clientIp) {
      return false;
    }

    return ipRestrictions.some(allowedIp => {
      // Simple IP matching - in production, you'd want CIDR support
      return allowedIp === clientIp || allowedIp === '*';
    });
  }

  /**
   * Extract client IP address from request
   */
  private getClientIp(request: any): string {
    return (
      request.headers['x-forwarded-for']?.split(',')[0] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.ip ||
      ''
    );
  }
}
