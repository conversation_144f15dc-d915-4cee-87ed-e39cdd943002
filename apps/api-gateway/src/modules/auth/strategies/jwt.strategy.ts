/**
 * 🔐 JWT Strategy - Passport JWT Authentication Strategy
 * 
 * Implements JWT token validation with:
 * - Token extraction from headers
 * - Payload validation
 * - User lookup and verification
 * - Security checks
 */

import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from '../auth.service';
import { JWTPayload } from '@shared-types';
import { User } from '../../../infrastructure/database/entities/user.entity';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('auth.jwt.secret'),
      issuer: configService.get<string>('auth.jwt.issuer', 'enterprise-platform'),
      audience: configService.get<string>('auth.jwt.audience', 'api-gateway'),
      algorithms: ['HS256'],
      passReqToCallback: true,
    });
  }

  /**
   * Validate JWT payload and return user
   */
  async validate(request: any, payload: JWTPayload): Promise<User> {
    try {
      // Extract correlation ID from request
      const correlationId = request.headers['x-correlation-id'] || request.correlationId;
      
      // Log token validation attempt
      this.logger.debug('Validating JWT token', {
        userId: payload.userId,
        correlationId,
        iat: payload.iat,
        exp: payload.exp,
      });

      // Validate payload structure
      if (!payload.userId || !payload.email) {
        this.logger.warn('Invalid JWT payload structure', {
          payload: { ...payload, userId: payload.userId ? '[REDACTED]' : undefined },
          correlationId,
        });
        throw new UnauthorizedException('Invalid token payload');
      }

      // Check token expiration (additional check)
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < now) {
        this.logger.warn('Expired JWT token', {
          userId: payload.userId,
          exp: payload.exp,
          now,
          correlationId,
        });
        throw new UnauthorizedException('Token expired');
      }

      // Validate user exists and is active
      const user = await this.authService.validateUser(payload.userId);
      
      if (!user) {
        this.logger.warn('JWT validation failed - user not found or inactive', {
          userId: payload.userId,
          correlationId,
        });
        throw new UnauthorizedException('User not found or inactive');
      }

      // Check if user's password was changed after token was issued
      if (user.passwordChangedAt && payload.iat) {
        const passwordChangedTimestamp = Math.floor(user.passwordChangedAt.getTime() / 1000);
        if (passwordChangedTimestamp > payload.iat) {
          this.logger.warn('JWT validation failed - password changed after token issued', {
            userId: payload.userId,
            passwordChangedAt: passwordChangedTimestamp,
            tokenIssuedAt: payload.iat,
            correlationId,
          });
          throw new UnauthorizedException('Token invalidated due to password change');
        }
      }

      // Attach additional context to user object
      (user as any).tokenPayload = payload;
      (user as any).correlationId = correlationId;

      this.logger.debug('JWT validation successful', {
        userId: user.id,
        email: user.email,
        roles: user.roleNames,
        correlationId,
      });

      return user;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error('JWT validation error', {
        error: error.message,
        stack: error.stack,
        payload: payload ? { ...payload, userId: '[REDACTED]' } : undefined,
      });

      throw new UnauthorizedException('Token validation failed');
    }
  }
}
