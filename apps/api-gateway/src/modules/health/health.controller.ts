/**
 * 🏥 Health Check Controller - Health Monitoring Endpoints
 * 
 * Provides comprehensive health check endpoints:
 * - Basic health status
 * - Detailed system health
 * - Database connectivity
 * - External service status
 */

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  HttpHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';
import { HealthService } from './health.service';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly healthService: HealthService,
    private readonly http: HttpHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
  ) {}

  @Get()
  @ApiOperation({ 
    summary: 'Basic health check',
    description: 'Returns basic application health status'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Application is healthy',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        info: { type: 'object' },
        error: { type: 'object' },
        details: { type: 'object' }
      }
    }
  })
  @HealthCheck()
  check() {
    return this.health.check([
      // Memory check - ensure we're not using too much memory
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024), // 150MB
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024), // 150MB
      
      // Disk space check
      () => this.disk.checkStorage('storage', { 
        path: '/', 
        thresholdPercent: 0.9 // 90% threshold
      }),
    ]);
  }

  @Get('detailed')
  @ApiOperation({ 
    summary: 'Detailed health check',
    description: 'Returns comprehensive system health including dependencies'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Detailed health information',
  })
  @HealthCheck()
  checkDetailed() {
    return this.health.check([
      // Memory checks
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
      
      // Disk space check
      () => this.disk.checkStorage('storage', { 
        path: '/', 
        thresholdPercent: 0.9 
      }),
      
      // AI Service health check
      () => this.http.pingCheck(
        'ai-service',
        process.env.AI_SERVICE_URL + '/health' || 'http://localhost:8000/health'
      ),
      
      // Custom health checks
      () => this.healthService.checkRedisConnection(),
      () => this.healthService.checkDatabaseConnection(),
    ]);
  }

  @Get('ready')
  @ApiOperation({ 
    summary: 'Readiness probe',
    description: 'Kubernetes readiness probe endpoint'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Application is ready to serve traffic',
  })
  @HealthCheck()
  checkReadiness() {
    return this.health.check([
      () => this.healthService.checkDatabaseConnection(),
      () => this.healthService.checkRedisConnection(),
    ]);
  }

  @Get('live')
  @ApiOperation({ 
    summary: 'Liveness probe',
    description: 'Kubernetes liveness probe endpoint'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Application is alive',
  })
  @HealthCheck()
  checkLiveness() {
    return this.health.check([
      () => this.memory.checkHeap('memory_heap', 200 * 1024 * 1024), // 200MB for liveness
    ]);
  }

  @Get('info')
  @ApiOperation({ 
    summary: 'Application information',
    description: 'Returns application metadata and version information'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Application information',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        version: { type: 'string' },
        environment: { type: 'string' },
        uptime: { type: 'number' },
        timestamp: { type: 'string' },
        nodeVersion: { type: 'string' },
        platform: { type: 'string' },
        architecture: { type: 'string' }
      }
    }
  })
  getInfo() {
    return this.healthService.getApplicationInfo();
  }
}
