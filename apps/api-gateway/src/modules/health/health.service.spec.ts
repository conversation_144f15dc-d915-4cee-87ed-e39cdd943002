/**
 * 🧪 Health Service Unit Tests
 * 
 * Comprehensive unit tests for health service:
 * - Redis connection testing
 * - Database connection testing
 * - Application info retrieval
 * - Error handling scenarios
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HealthService } from './health.service';
import { mockConfigService, MockRedisService } from '../../../test/setup';

describe('HealthService', () => {
  let service: HealthService;
  let configService: ConfigService;
  let mockRedis: MockRedisService;

  beforeEach(async () => {
    mockRedis = new MockRedisService();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HealthService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<HealthService>(HealthService);
    configService = module.get<ConfigService>(ConfigService);

    // Mock Redis client
    (service as any).redis = mockRedis;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('checkRedisConnection', () => {
    it('should return healthy status when Redis is available', async () => {
      // Arrange
      jest.spyOn(mockRedis, 'ping').mockResolvedValue('PONG');

      // Act
      const result = await service.checkRedisConnection();

      // Assert
      expect(result).toEqual({
        redis: {
          status: 'up',
          responseTime: expect.stringMatching(/\d+ms/),
          host: 'localhost',
          port: 6379,
          db: 0,
        },
      });
      expect(mockRedis.ping).toHaveBeenCalledTimes(1);
    });

    it('should return unhealthy status when Redis is unavailable', async () => {
      // Arrange
      const errorMessage = 'Connection refused';
      jest.spyOn(mockRedis, 'ping').mockRejectedValue(new Error(errorMessage));

      // Act
      const result = await service.checkRedisConnection();

      // Assert
      expect(result).toEqual({
        redis: {
          status: 'down',
          error: errorMessage,
          host: 'localhost',
          port: 6379,
        },
      });
    });

    it('should handle Redis client not initialized', async () => {
      // Arrange
      (service as any).redis = null;

      // Act
      const result = await service.checkRedisConnection();

      // Assert
      expect(result).toEqual({
        redis: {
          status: 'down',
          error: 'Redis client not initialized',
          host: 'localhost',
          port: 6379,
        },
      });
    });
  });

  describe('checkDatabaseConnection', () => {
    it('should return healthy status for database connection', async () => {
      // Act
      const result = await service.checkDatabaseConnection();

      // Assert
      expect(result).toEqual({
        database: {
          status: 'up',
          responseTime: expect.stringMatching(/\d+ms/),
          host: 'localhost',
          port: 5432,
          database: 'enterprise_db',
        },
      });
    });

    it('should handle database connection errors', async () => {
      // Arrange
      const originalMethod = service.checkDatabaseConnection;
      jest.spyOn(service, 'checkDatabaseConnection').mockImplementation(async () => {
        throw new Error('Database connection failed');
      });

      // Act & Assert
      await expect(service.checkDatabaseConnection()).rejects.toThrow('Database connection failed');
    });
  });

  describe('getApplicationInfo', () => {
    it('should return comprehensive application information', () => {
      // Arrange
      const mockPackageJson = {
        name: 'test-api-gateway',
        version: '1.0.0',
        description: 'Test API Gateway',
      };
      
      // Mock require for package.json
      jest.doMock('../../../../package.json', () => mockPackageJson, { virtual: true });

      // Act
      const result = service.getApplicationInfo();

      // Assert
      expect(result).toEqual({
        name: mockPackageJson.name,
        version: mockPackageJson.version,
        description: mockPackageJson.description,
        environment: 'test',
        uptime: expect.any(Number),
        timestamp: expect.any(String),
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch,
        memory: {
          used: expect.any(Number),
          total: expect.any(Number),
          external: expect.any(Number),
          rss: expect.any(Number),
        },
        cpu: {
          usage: expect.any(Object),
        },
        config: {
          port: 3000,
          apiPrefix: '/api/v1',
          cors: {
            origin: ['http://localhost:3000'],
          },
          rateLimit: {
            windowMs: 900000,
            maxRequests: 100,
          },
        },
        services: {
          aiService: {
            url: 'http://localhost:8000',
            timeout: 30000,
          },
        },
      });
    });

    it('should handle missing package.json gracefully', () => {
      // Arrange
      jest.doMock('../../../../package.json', () => {
        throw new Error('Module not found');
      }, { virtual: true });

      // Act
      const result = service.getApplicationInfo();

      // Assert
      expect(result.name).toBe('Enterprise API Gateway');
      expect(result.version).toBe('1.0.0');
    });
  });

  describe('onModuleDestroy', () => {
    it('should close Redis connection gracefully', async () => {
      // Arrange
      const quitSpy = jest.spyOn(mockRedis, 'quit' as any).mockResolvedValue(undefined);
      (service as any).redis = { ...mockRedis, quit: quitSpy };

      // Act
      await service.onModuleDestroy();

      // Assert
      expect(quitSpy).toHaveBeenCalledTimes(1);
    });

    it('should handle Redis connection close errors', async () => {
      // Arrange
      const quitSpy = jest.fn().mockRejectedValue(new Error('Close failed'));
      (service as any).redis = { quit: quitSpy };

      // Act & Assert
      await expect(service.onModuleDestroy()).resolves.not.toThrow();
      expect(quitSpy).toHaveBeenCalledTimes(1);
    });

    it('should handle missing Redis client', async () => {
      // Arrange
      (service as any).redis = null;

      // Act & Assert
      await expect(service.onModuleDestroy()).resolves.not.toThrow();
    });
  });

  describe('configuration integration', () => {
    it('should use configuration values correctly', () => {
      // Arrange
      mockConfigService.get.mockImplementation((key: string, defaultValue?: any) => {
        const config = {
          'redis.host': 'custom-redis-host',
          'redis.port': 6380,
          'database.host': 'custom-db-host',
          'database.port': 5433,
        };
        return config[key] || defaultValue;
      });

      // Act
      const info = service.getApplicationInfo();

      // Assert
      expect(mockConfigService.get).toHaveBeenCalledWith('redis.host', 'localhost');
      expect(mockConfigService.get).toHaveBeenCalledWith('redis.port', 6379);
      expect(mockConfigService.get).toHaveBeenCalledWith('database.host', 'localhost');
      expect(mockConfigService.get).toHaveBeenCalledWith('database.port', 5432);
    });
  });

  describe('error scenarios', () => {
    it('should handle unexpected errors gracefully', async () => {
      // Arrange
      jest.spyOn(service, 'checkRedisConnection').mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act & Assert
      await expect(service.checkRedisConnection()).rejects.toThrow('Unexpected error');
    });

    it('should validate response time measurements', async () => {
      // Arrange
      jest.spyOn(Date, 'now')
        .mockReturnValueOnce(1000) // Start time
        .mockReturnValueOnce(1050); // End time

      // Act
      const result = await service.checkRedisConnection();

      // Assert
      expect(result.redis.responseTime).toBe('50ms');
    });
  });

  describe('performance tests', () => {
    it('should complete health checks within acceptable time', async () => {
      // Arrange
      const startTime = Date.now();

      // Act
      await Promise.all([
        service.checkRedisConnection(),
        service.checkDatabaseConnection(),
      ]);

      // Assert
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle concurrent health checks', async () => {
      // Arrange
      const promises = Array.from({ length: 10 }, () => 
        service.checkRedisConnection()
      );

      // Act
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.redis.status).toBe('up');
      });
    });
  });
});
