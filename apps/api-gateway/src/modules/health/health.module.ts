/**
 * 🏥 Health Check Module - System Health Monitoring
 * 
 * Implements comprehensive health checking with:
 * - Database connectivity checks
 * - Redis connectivity checks
 * - External service health checks
 * - System resource monitoring
 */

import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';

@Module({
  imports: [
    TerminusModule,
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),
  ],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}
