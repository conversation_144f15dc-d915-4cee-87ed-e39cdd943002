{"name": "@enterprise/api-gateway", "version": "1.0.0", "description": "Enterprise API Gateway - NestJS Implementation", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "docker:build": "docker build -t api-gateway .", "docker:run": "docker run -p 3000:3000 api-gateway"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/graphql": "^12.0.0", "@nestjs/apollo": "^12.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/throttler": "^5.0.0", "@nestjs/cache-manager": "^2.0.0", "@apollo/server-express": "^4.0.0", "apollo-server-express": "^3.12.0", "graphql": "^16.6.0", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "bcrypt": "^5.1.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^6.7.0", "redis": "^4.6.0", "cache-manager": "^5.2.0", "cache-manager-redis-store": "^3.0.1", "winston": "^3.8.0", "nest-winston": "^1.9.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "uuid": "^9.0.0", "joi": "^17.9.0", "swagger-ui-express": "^4.6.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.0", "@types/node": "^20.3.0", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/bcrypt": "^5.0.0", "@types/uuid": "^9.0.0", "@types/compression": "^1.7.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "api-gateway", "microservices", "graphql", "rest", "enterprise", "typescript"], "author": "Enterprise Platform Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/enterprise-platform/api-gateway.git"}}