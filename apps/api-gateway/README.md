# 🌐 **API Gateway - Enterprise Entry Point**

> **NestJS-powered API Gateway with GraphQL Federation, Authentication, and Microservices Orchestration**

## 📋 **Overview**

The API Gateway serves as the **single entry point** for all client requests, providing unified access to our microservices ecosystem. Built with NestJS and GraphQL Federation, it offers enterprise-grade features including authentication, rate limiting, request transformation, and comprehensive monitoring.

## 🏗️ **Architecture**

```
api-gateway/
├── src/
│   ├── modules/              # Feature modules
│   │   ├── auth/            # Authentication module
│   │   ├── users/           # User management
│   │   ├── graphql/         # GraphQL federation
│   │   └── health/          # Health checks
│   ├── middleware/          # Custom middleware
│   │   ├── auth.middleware.ts
│   │   ├── rate-limit.middleware.ts
│   │   └── logging.middleware.ts
│   ├── guards/              # Security guards
│   │   ├── jwt-auth.guard.ts
│   │   ├── roles.guard.ts
│   │   └── throttle.guard.ts
│   ├── interceptors/        # Request/Response interceptors
│   ├── filters/             # Exception filters
│   ├── decorators/          # Custom decorators
│   ├── config/              # Configuration
│   └── main.ts              # Application bootstrap
├── test/                    # E2E tests
├── Dockerfile               # Container configuration
├── docker-compose.yml       # Local development
└── package.json             # Dependencies
```

## 🔧 **Key Features**

### **🔐 Authentication & Authorization**
- ✅ **JWT Authentication** - Stateless token-based auth
- ✅ **OAuth2 Integration** - Google, GitHub, Microsoft
- ✅ **Multi-layered Access Control (RBAC, ABAC, ReBAC)** - Granular permissions with dynamic evaluation
- ✅ **API Key Management** - Service-to-service authentication
- ✅ **Session Management** - Redis-backed sessions

### **📊 GraphQL Federation**
- ✅ **Schema Stitching** - Unified GraphQL schema
- ✅ **Service Discovery** - Automatic microservice detection
- ✅ **Query Optimization** - Efficient data fetching
- ✅ **Real-time Subscriptions** - WebSocket support
- ✅ **Schema Validation** - Type-safe operations

### **🛡️ Security & Protection**
- ✅ **Rate Limiting** - Configurable per endpoint/user
- ✅ **CORS Configuration** - Cross-origin request handling
- ✅ **Input Validation** - Request payload validation
- ✅ **SQL Injection Protection** - Parameterized queries
- ✅ **XSS Protection** - Content sanitization

### **🔄 Request Processing**
- ✅ **Circuit Breaker** - Fault tolerance pattern
- ✅ **Retry Logic** - Automatic retry with backoff
- ✅ **Request Transformation** - Data mapping and enrichment
- ✅ **Response Caching** - Redis-based caching
- ✅ **Compression** - Gzip response compression

## 🚀 **Quick Start**

### **📦 Installation**
```bash
cd apps/api-gateway
npm install
```

### **⚙️ Environment Setup**
```bash
# Copy environment template
cp .env.example .env

# Configure environment variables
# DATABASE_URL=postgresql://user:pass@localhost:5432/db
# REDIS_URL=redis://localhost:6379
# JWT_SECRET=your-secret-key
# GRAPHQL_PLAYGROUND=true
```

### **🏃‍♂️ Development**
```bash
# Start in development mode
npm run start:dev

# Start with file watching
npm run start:debug

# Access points:
# API Gateway: http://localhost:3000
# GraphQL Playground: http://localhost:3000/graphql
# API Documentation: http://localhost:3000/docs
# Health Check: http://localhost:3000/health
```

### **🐳 Docker Development**
```bash
# Start with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f api-gateway

# Stop services
docker-compose down
```

## 📊 **API Endpoints**

### **🔍 Core Endpoints**
```typescript
// Health Check
GET /health
Response: { status: 'ok', timestamp: '2024-01-01T00:00:00Z' }

// GraphQL Endpoint
POST /graphql
Content-Type: application/json
Body: { query: "query { users { id name email } }" }

// Authentication
POST /auth/login
Body: { email: string, password: string }
Response: { accessToken: string, refreshToken: string }

// API Documentation
GET /docs
Response: Swagger UI interface
```

### **🔐 Authentication Flow**
```typescript
// 1. Login
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

// 2. Response
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": "123",
    "email": "<EMAIL>",
    "roles": ["user"]
  }
}

// 3. Authenticated Requests
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

## 🏗️ **GraphQL Schema**

### **📋 Example Schema**
```graphql
type User {
  id: ID!
  email: String!
  name: String!
  roles: [String!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Query {
  users: [User!]!
  user(id: ID!): User
  me: User
}

type Mutation {
  createUser(input: CreateUserInput!): User!
  updateUser(id: ID!, input: UpdateUserInput!): User!
  deleteUser(id: ID!): Boolean!
}

type Subscription {
  userCreated: User!
  userUpdated: User!
}
```

### **🔄 Federation Example**
```typescript
// User Service Schema
@Directive('@key(fields: "id")')
@ObjectType()
export class User {
  @Field(() => ID)
  id: string;

  @Field()
  email: string;

  @Field()
  name: string;
}

// Task Service Schema (extends User)
@Directive('@extends')
@Directive('@key(fields: "id")')
@ObjectType()
export class User {
  @Field(() => ID)
  @Directive('@external')
  id: string;

  @Field(() => [Task])
  tasks: Task[];
}
```

## 🔧 **Configuration**

### **📋 Environment Variables**
```bash
# Server Configuration
PORT=3000
NODE_ENV=development

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/gateway_db
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# GraphQL
GRAPHQL_PLAYGROUND=true
GRAPHQL_INTROSPECTION=true

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Microservices
USER_SERVICE_URL=http://localhost:3001
TASK_SERVICE_URL=http://localhost:3002
AI_SERVICE_URL=http://localhost:8000
```

### **⚙️ Module Configuration**
```typescript
// app.module.ts
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        PORT: Joi.number().default(3000),
        DATABASE_URL: Joi.string().required(),
        JWT_SECRET: Joi.string().required(),
      }),
    }),
    GraphQLModule.forRoot<ApolloFederationDriverConfig>({
      driver: ApolloFederationDriver,
      autoSchemaFile: {
        federation: 2,
      },
      playground: process.env.NODE_ENV === 'development',
    }),
    ThrottlerModule.forRoot({
      ttl: 60,
      limit: 100,
    }),
    AuthModule,
    UsersModule,
    HealthModule,
  ],
})
export class AppModule {}
```

## 🧪 **Testing**

### **🔬 Test Structure**
```
test/
├── unit/                 # Unit tests
│   ├── auth/
│   ├── users/
│   └── graphql/
├── integration/          # Integration tests
│   ├── auth.e2e-spec.ts
│   └── graphql.e2e-spec.ts
└── fixtures/             # Test data
    ├── users.json
    └── auth-tokens.json
```

### **🚀 Running Tests**
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov

# Watch mode
npm run test:watch
```

### **📊 Test Example**
```typescript
describe('AuthController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/auth/login (POST)', () => {
    return request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      })
      .expect(201)
      .expect((res) => {
        expect(res.body.accessToken).toBeDefined();
        expect(res.body.user.email).toBe('<EMAIL>');
      });
  });
});
```

## 📊 **Monitoring & Observability**

### **📈 Metrics**
- ✅ **Request Metrics** - Response time, throughput, errors
- ✅ **GraphQL Metrics** - Query performance, resolver timing
- ✅ **Authentication Metrics** - Login success/failure rates
- ✅ **Rate Limiting Metrics** - Throttling statistics
- ✅ **Circuit Breaker Metrics** - Failure detection and recovery

### **📝 Logging**
```typescript
// Structured logging with Winston
logger.info('User authenticated', {
  userId: user.id,
  email: user.email,
  ip: request.ip,
  userAgent: request.headers['user-agent'],
});

logger.error('GraphQL query failed', {
  query: info.fieldName,
  variables: args,
  error: error.message,
  stack: error.stack,
});
```

## 🚀 **Deployment**

### **🐳 Docker Production**
```dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "start:prod"]
```

### **☸️ Kubernetes Deployment**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: api-gateway:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
```

## 🔗 **Related Documentation**

- [🏗️ Project Structure](../../docs/PROJECT_STRUCTURE.md)
- [⚡ Services Layer](../../services/README.md)
- [🔐 Authentication Guide](../../docs/07-knowledge-base/06-security/README.md)
- [📊 GraphQL Best Practices](../../docs/07-knowledge-base/02-software-design/README.md)
- [🧪 Testing Strategy](../../tests/README.md)

---

> **Next Steps**: Configure your environment variables and start the development server to begin building your API Gateway.
